# 多分辨率支持技术方案

## 1. 需求背景

相机设备需要支持720P和4K两种分辨率的视频播放，客户端根据网络状况自动选择合适的分辨率进行播放。

### 核心需求
- 支持720P和4K两种分辨率的视频存储
- 客户端根据网络状况自动切换分辨率播放
- 生成符合HLS标准的Master Playlist包含所有分辨率信息
- 客户端请求主m3u8时返回Master Playlist，自动选择子播放列表
- 保持对其他相机型号的兼容性
- 不在后端硬编码设备型号，通过设备端上报的video类型判断功能支持

## 2. 技术方案

### 2.1 设计思路
沿用AOV双画面的main_trace_id逻辑，将720P视频作为4K主视频的"附属视频"，通过main_trace_id进行关联。

### 2.2 核心设计原则
- **复用现有架构**：完全复用双画面的main_trace_id机制
- **向后兼容**：所有设备完全不受影响
- **数据一致性**：720P和4K视频通过main_trace_id强关联
- **扩展性**：未来支持更多分辨率时可以继续扩展
- **设备驱动**：通过设备端上报的video类型判断功能支持，不在后端硬编码设备型号
- **标准兼容**：生成符合HLS标准的Master Playlist，支持自适应播放

### 2.3 数据存储设计

#### 设备端上报逻辑
- **视频类型**：复用现有`videoType`字段，多分辨率设备使用新值3支持多分辨率
- **分辨率信息**：设备端上报`resolution`字段标识分辨率
- **关联信息**：复用现有`mainTraceId`字段，多分辨率场景下720P视频关联到4K主视频

#### 存储逻辑
- **4K主视频**：`video_type=1`或`video_type=3`，`main_trace_id=null`，`resolution="3840x2160"`
- **720P附属视频**：`video_type=3`，`main_trace_id=4K视频的traceId`，`resolution="1280x720"`

#### 查询逻辑
- 先查询主视频的video类型，判断是否为多分辨率场景
- 沿用双画面的查询方式，通过main_trace_id关联
- 多分辨率判断：查询该traceId下是否有多个分辨率（`video_type in (1,3)` 或 `main_trace_id=traceId and video_type=3`）
- 4K分辨率：直接查询主视频
- 720P分辨率：通过main_trace_id查询附属视频

## 3. 数据库表结构变更

### 3.1 新增字段

```sql
-- 为video_slice表添加resolution字段（主要存储表）
ALTER TABLE `video_slice`
ADD COLUMN `resolution` varchar(20) DEFAULT NULL COMMENT '视频分辨率，如1280x720、3840x2160' AFTER `codec`;

-- 为video_library表添加resolution字段（提升查询性能）
ALTER TABLE `video-library-db`.`video_library`
ADD COLUMN `resolution` VARCHAR(32) NULL COMMENT '视频分辨率，如1280x720、3840x2160'
AFTER `codec`;

-- 为library_status表添加resolution字段（提升查询性能）
ALTER TABLE `video-library-db`.`library_status`
ADD COLUMN `resolution` VARCHAR(32) NULL COMMENT '视频分辨率，如1280x720、3840x2160'
AFTER `service_name`;
```

### 3.2 设计说明

#### 数据冗余策略
- **video_slice表**：主要存储表，包含完整的分辨率信息
- **video_library表**：为提升查询性能，冗余存储resolution字段
- **library_status表**：为提升查询性能，冗余存储resolution字段

#### 数据一致性保证
- 在VideoStoreService中同时设置video_library和library_status的resolution字段
- 确保从VideoSliceDO → VideoCache → LibraryTb → 数据库表的完整数据流

### 3.3 索引优化

```sql
-- 为video_slice表添加复合索引，支持按分辨率查询
ALTER TABLE `video_slice`
ADD INDEX `idx_trace_resolution_order` (`trace_id`, `resolution`, `order`),
ADD INDEX `idx_main_trace_resolution_order` (`main_trace_id`, `resolution`, `order`),
ADD INDEX `idx_main_video_type_order` (`main_trace_id`, `video_type`, `order`);

-- 为video_library表添加索引，支持按分辨率查询
ALTER TABLE `video-library-db`.`video_library`
ADD INDEX `idx_resolution_video_type` (`resolution`, `video_type`),
ADD INDEX `idx_main_trace_resolution` (`main_trace_id`, `resolution`);

-- 为library_status表添加索引，支持按分辨率查询
ALTER TABLE `video-library-db`.`library_status`
ADD INDEX `idx_resolution_video_type` (`resolution`, `video_type`);
```

## 4. 核心代码实现

### 4.1 常量定义

#### VideoResolutionConstants.java
```java
public class VideoResolutionConstants {
    public static final String RESOLUTION_4K = "3840x2160";
    public static final String RESOLUTION_720P = "1280x720";
    
    public static final Map<String, String> RESOLUTION_DISPLAY_NAMES = ImmutableMap.of(
        RESOLUTION_4K, "4K",
        RESOLUTION_720P, "720P"
    );
}
```

#### VideoTypeEnum.java（扩展）
```java
public enum VideoTypeEnum {
    SNAPSHOT_RECORDING(0, "快照录像"),
    EVNET_RECORDING_MAIN_VIEW(1, "事件录像主画面"),      // 双画面主画面，也可以是多分辨率4K主视频
    EVNET_RECORDING_SUB_VIEW(2, "事件录像子画面"),       // 双画面子画面
    EVNET_RECORDING_MULTI_RESOLUTION(3, "事件录像多分辨率"), // 多分辨率视频（4K主视频或720P附属视频）

    // 多分辨率视频类型说明：
    // - 4K主视频：可以使用 video_type=1（兼容老设备）或 video_type=3（新设备）
    // - 720P附属视频：使用 video_type=3，通过main_trace_id关联到4K主视频
    // - 具体分辨率通过resolution字段区分
}
```

### 4.2 实体类扩展

#### VideoSliceDO.java
```java
public class VideoSliceDO extends LibraryExtension {
    // 现有字段...
    Integer videoType; // 复用现有字段，扩展新值3
    String mainTraceId; // 复用现有字段

    /* 多分辨率支持 begin */
    private String resolution; // 视频分辨率
    /* 多分辨率支持 end */
}
```

#### LibraryTb.java
```java
public class LibraryTb extends LibraryExtension {
    // 现有字段...
    Integer videoType; // 复用现有字段，扩展新值3
    String mainTraceId; // 复用现有字段

    /* 多分辨率支持 begin */
    private String resolution; // 视频分辨率，如"1280x720"、"3840x2160"
    /* 多分辨率支持 end */
}
```

#### LibraryStatusTb.java
```java
public class LibraryStatusTb {
    // 现有字段...
    Integer videoType; // 复用现有字段，扩展新值3
    String mainTraceId; // 复用现有字段
    String serviceName; // 存储服务

    /* 多分辨率支持 begin */
    private String resolution; // 视频分辨率，如"1280x720"、"3840x2160"
    /* 多分辨率支持 end */
}
```

#### DeviceLibraryViewDO.java
```java
public class DeviceLibraryViewDO extends LibraryExtension {
    // 现有字段...
    Integer videoType; // 复用现有字段，扩展新值3
    String mainTraceId; // 复用现有字段
    String serviceName; // 存储服务
    String codec; // 视频编码

    /* 多分辨率支持 begin */
    private String resolution; // 视频分辨率，如"1280x720"、"3840x2160"
    /* 多分辨率支持 end */
}
```

#### UserLibraryViewDO.java
```java
public class UserLibraryViewDO extends DeviceLibraryViewDO {
    // 现有字段...
    private List<UserLibraryViewDO> subVideos = new LinkedList<>(); // 双画面子视频列表（videoType=2）

    /* 多分辨率支持 begin */
    private List<UserLibraryViewDO> multiResolutionVideos = new LinkedList<>(); // 多分辨率视频列表（videoType=3）
    /* 多分辨率支持 end */
}
```

#### LibraryEventView.java
```java
public class LibraryEventView extends LibraryExtension {
    // 现有字段...
    @Builder.Default
    private Boolean hasSubVideos = false; // 是否有子画面。videoEvent下任意一个视频有子画面都是true

    /* 多分辨率支持 begin */
    @Builder.Default
    private Boolean hasMultiResolutionVideos = false; // 是否有多分辨率视频。videoEvent下任意一个视频有多分辨率都是true
    /* 多分辨率支持 end */
}
```

#### VideoCache.java
```java
public class VideoCache {
    // 现有字段...
    private Integer videoType;
    private String mainTraceId;

    /* 多分辨率支持 begin */
    private String resolution; // 视频分辨率，如"1280x720"、"3840x2160"
    /* 多分辨率支持 end */

    public void setResolution(String resolution) {
        if (StringUtils.isBlank(resolution)) return;
        if (StringUtils.isBlank(this.resolution)) {
            this.resolution = resolution;
        } else if (!this.resolution.equals(resolution)) {
            LogUtil.error(log, "同一个视频的用了多个resolution! {},resolution={},resolution2={}", toSimpleDesc(), this.resolution, resolution);
        }
    }

    public void addSlice(VideoSliceDO slice) {
        // 现有逻辑...
        setCodec(slice.getCodec());
        setVideoType(slice.getVideoType());
        setMainTraceId(slice.getMainTraceId());
        setServiceName(slice.getServiceName());
        setResolution(slice.getResolution()); // 🆕添加resolution设置
    }
}
```

#### VideoSliceReport.java
```java
public class VideoSliceReport {
    // 现有字段...
    Integer videoType; // 复用现有字段，多分辨率设备使用新值3
    String mainTraceId; // 复用现有字段：双画面子画面或多分辨率附属画面关联到主画面

    /* 多分辨率支持 begin */
    private String resolution; // 设备上报的分辨率信息
    /* 多分辨率支持 end */
}
```

### 4.3 DAO层修改

#### IShardingLibraryDAO.java
```java
@Mapper
public interface IShardingLibraryDAO {

    // 🆕更新SELECT_LIBRARY_COLUMNS常量，包含resolution字段
    String SELECT_LIBRARY_COLUMNS = " select 0 as `id`,date_format( from_unixtime( ( `timestamp` + 28800 ) ), '%Y%m%d' ) AS `date`," +
            "ifnull(`end_timestamp`,`timestamp`+ceil(`period`)) endTimestamp," +
            "timestamp,serial_number,image_url,video_url,type,received_all_slice,period,file_size,image_only," +
            "admin_id,share_user_ids,device_name,tags,ai_edge_tags,trace_id,main_trace_id,video_type,service_name,codec,resolution,event_info,ai_edge_event_info,doorbell_tags,doorbell_event_info,device_call_event_tag,keyshot,extension, summary_description " +
            " ";

    // 🆕插入视频记录时包含resolution字段
    @Insert("INSERT INTO `video-library-db`.`video_library` " +
            "(`user_id`, `serial_number`, `trace_id`, `timestamp`, `period`, `image_url`, `video_url`, " +
            "`type`, `received_all_slice`, `device_name`, `tags`, `ai_edge_tags`, `event_info`, " +
            "`ai_edge_event_info`, `doorbell_tags`, `doorbell_event_info`, `device_call_event_tag`, " +
            "`keyshot`, `extension`, `summary_description`, `video_type`, `main_trace_id`, `service_name`, `codec`, `resolution`) " +
            "VALUES (#{userId}, #{serialNumber}, #{traceId}, #{timestamp}, #{period}, #{imageUrl}, #{videoUrl}, " +
            "#{type}, #{receivedAllSlice}, #{deviceName}, #{tags}, #{aiEdgeTags}, #{eventInfo}, " +
            "#{aiEdgeEventInfo}, #{doorbellTags}, #{doorbellEventInfo}, #{deviceCallEventTag}, " +
            "#{keyshot}, #{extension}, #{summaryDescription}, #{videoType}, #{mainTraceId}, #{serviceName}, #{codec}, #{resolution})")
    int insertLibrary(InsertLibraryRequest request);

    // 🆕查询方法中包含resolution字段
    @Select("select 0 as `id`,trace_id,main_trace_id,video_type,service_name,codec,resolution,image_url,period,device_name,admin_id,video_url,type,received_all_slice,timestamp" +
            ",ifnull(`end_timestamp`,ceil(`timestamp`+`period`)) as `end_timestamp`" +
            ",doorbell_tags,doorbell_event_info,device_call_event_tag,keyshot,extension,summary_description " +
            "from `video-library-db`.`video_library` where user_id in ...")
    List<DeviceLibraryViewDO> selectLibraryByUserIdsAndMainTraceIds(...);
}
```

#### IShardingLibraryStatusDAO.java
```java
@Mapper
public interface IShardingLibraryStatusDAO {

    // 🆕插入library_status时包含resolution字段
    @Insert("<script>" +
            "INSERT INTO `video-library-db`.`library_status` " +
            "(`user_id`, `admin_id`, `library_id`, `trace_id`, `timestamp`, `serial_number`, `video_event`, " +
            "`tier_id`, `tier_group_id`, `ttl_timestamp`, `support_magic_pix`, `extension`" +
            "<if test=\"videoType!=null\">,`video_type`</if>" +
            "<if test=\"mainTraceId!=null\">,`main_trace_id`</if>" +
            "<if test=\"serviceName!=null\">,`service_name`</if>" +
            "<if test=\"resolution!=null\">,`resolution`</if>" + // 🆕添加resolution字段
            "<if test=\"tags!=null\">,`tags`</if>" +
            ") VALUES " +
            "(#{userId}, #{adminId}, #{libraryId}, #{traceId}, #{timestamp}, #{serialNumber}, #{eventKey}, " +
            "#{deviceUseCurrentTierId}, #{deviceUseCurrentTierGroupId}, #{ttlTimestamp}, #{supportMagicPix}, #{extension}" +
            "<if test=\"videoType!=null\">,#{videoType}</if>" +
            "<if test=\"mainTraceId!=null\">,#{mainTraceId}</if>" +
            "<if test=\"serviceName!=null\">,#{serviceName}</if>" +
            "<if test=\"resolution!=null\">,#{resolution}</if>" + // 🆕添加resolution值
            "<if test=\"tags!=null\">,#{tags}</if>" +
            ")" +
            "</script>")
    int insertLibraryStatusV2(LibraryStatusTb libraryStatusTb);
}
```

### 4.4 Service层修改

#### VideoStoreService.java
```java
@Service
public class VideoStoreService {

    public void storeVideo(VideoCache video) {
        // 创建video_library记录
        InsertLibraryRequest library = new InsertLibraryRequest();
        // ... 设置其他字段
        library.setVideoType(video.getVideoType());
        library.setMainTraceId(video.getMainTraceId());
        library.setServiceName(video.getServiceName());
        library.setCodec(video.getCodec());
        library.setResolution(video.getResolution()); // 🆕设置resolution

        // 创建library_status记录
        LibraryStatusTb libraryStatus = new LibraryStatusTb();
        // ... 设置其他字段
        libraryStatus.setVideoType(video.getVideoType());
        libraryStatus.setMainTraceId(video.getMainTraceId());
        libraryStatus.setServiceName(video.getServiceName());
        libraryStatus.setResolution(video.getResolution()); // 🆕设置resolution
    }
}
```

#### LibraryService.java
```java
@Service
public class LibraryService {

    public List<UserLibraryViewDO> selectlibrary(LibraryRequest request) {
        // 查询双画面子视频
        if (Optional.ofNullable(request.getNeedSubVideos()).orElse(true)) {
            subLibraryViewList = shardingLibraryDAO.selectLibraryByUserIdsAndMainTraceIds(
                adminIds, traceIdSet, Arrays.asList(VideoTypeEnum.EVNET_RECORDING_SUB_VIEW.getCode()), null);
        }

        // 🆕查询多分辨率子视频
        if (Optional.ofNullable(request.getNeedMultiResolutionVideos()).orElse(false)) {
            multiResolutionLibraryViewList = shardingLibraryDAO.selectLibraryByUserIdsAndMainTraceIds(
                adminIds, traceIdSet, Arrays.asList(VideoTypeEnum.EVNET_RECORDING_MULTI_RESOLUTION.getCode()), null);
        }

        // 设置子视频列表
        Map<String, List<UserLibraryViewDO>> traceId2SubLibraryList = subLibraryViewList.stream()
            .collect(Collectors.groupingBy(it -> it.getMainTraceId()));
        Map<String, List<UserLibraryViewDO>> traceId2MultiResolutionLibraryList = multiResolutionLibraryViewList.stream()
            .collect(Collectors.groupingBy(it -> it.getMainTraceId()));

        listUserLibraryViewDO = listUserLibraryViewDO.stream().map(userLibraryViewDO -> {
            userLibraryViewDO.setSubVideos(traceId2SubLibraryList.getOrDefault(userLibraryViewDO.getTraceId(), new ArrayList<>()));
            userLibraryViewDO.setMultiResolutionVideos(traceId2MultiResolutionLibraryList.getOrDefault(userLibraryViewDO.getTraceId(), new ArrayList<>())); // 🆕设置多分辨率视频
            return userLibraryViewDO;
        }).collect(Collectors.toList());
    }

    void getLibraryEventSummary(Integer userId, LibraryEventView libraryEventView, LibraryEventDO libraryEventDO,
                               Map<String, LibraryTb> traceIdToLibraryTb,
                               Map<String, List<LibraryTb>> traceId2SubLibraryList,
                               Map<String, List<LibraryTb>> traceId2MultiResolutionLibraryList) { // 🆕添加多分辨率参数
        // 设置是否有子画面
        libraryEventView.setHasSubVideos(libraryEventDO.getTraceIds().stream().anyMatch(traceId2SubLibraryList::containsKey));
        // 🆕设置是否有多分辨率视频
        libraryEventView.setHasMultiResolutionVideos(libraryEventDO.getTraceIds().stream().anyMatch(traceId2MultiResolutionLibraryList::containsKey));
    }
}
```

### 4.5 数据流完整性保证

#### 4.5.1 数据插入流程
```
设备端上报 → VideoSliceReport(包含resolution)
     ↓
VideoSliceDO(包含resolution)
     ↓
VideoCache.addSlice() → 设置resolution字段
     ↓
VideoStoreService.storeVideo()
     ├── LibraryTb.setResolution() → video_library表(包含resolution)
     └── LibraryStatusTb.setResolution() → library_status表(包含resolution)
```

#### 4.5.2 数据查询流程
```
客户端请求 → LibraryRequest(needSubVideos, needMultiResolutionVideos)
     ↓
LibraryService.selectlibrary()
     ├── 查询主视频(包含resolution字段)
     ├── 查询双画面子视频(videoType=2) → subVideos列表
     └── 查询多分辨率子视频(videoType=3) → multiResolutionVideos列表
     ↓
UserLibraryViewDO(包含resolution, subVideos, multiResolutionVideos)
     ↓
客户端获取完整的视频信息和分辨率数据
```

#### 4.5.3 关键修复点
1. **SELECT查询修复**：所有返回视频信息的查询都包含resolution字段
2. **INSERT操作修复**：video_library和library_status表的插入都包含resolution
3. **字段分离设计**：subVideos和multiResolutionVideos分离，避免APP混淆
4. **数据一致性**：从VideoSliceDO到数据库表的完整数据流保证

### 4.6 核心业务逻辑

#### M3u8MasterPlaylistGenerator.java（🆕新增工具类）
```java
public class M3u8MasterPlaylistGenerator {

    /**
     * 生成Master Playlist（带实际带宽计算）
     */
    public static void generateMasterPlaylistWithBandwidth(String traceId, List<ResolutionInfo> resolutions,
                                                          SlicesProvider slicesProvider,
                                                          int playListType, boolean fillVacancy, PrintWriter writer) {
        writer.println("#EXTM3U");
        writer.println("#EXT-X-VERSION:6");

        // 按分辨率从高到低排序
        resolutions.sort((r1, r2) -> {
            int bandwidth1 = getDefaultBandwidth(r1.getResolution());
            int bandwidth2 = getDefaultBandwidth(r2.getResolution());
            return Integer.compare(bandwidth2, bandwidth1);
        });

        // 为每个分辨率生成EXT-X-STREAM-INF条目
        for (ResolutionInfo resolution : resolutions) {
            String resolutionValue = resolution.getResolution();
            List<VideoSliceDO> slices = slicesProvider.getSlicesByResolution(resolutionValue);

            int bandwidth = calculateBandwidth(slices, resolutionValue);

            writer.println(String.format("#EXT-X-STREAM-INF:BANDWIDTH=%d,RESOLUTION=%s,CODECS=\"avc1.640028\"",
                          bandwidth, resolutionValue));
            writer.println(generateSubPlaylistUrl(traceId, resolutionValue, playListType, fillVacancy));
        }
    }
}
```

#### VideoStoreService.java
```java
public List<VideoSliceDO> querySliceByAdminUserIdAndTraceIdAndResolution(
    Integer adminUserId, String traceId, String resolution) {

    // 多分辨率场景的分辨率切换逻辑
    if (StringUtils.isBlank(resolution)) {
        // 未指定分辨率时，返回默认分辨率
        return querySliceByAdminUserIdAndTraceId(adminUserId, traceId);
    }

    // 先查询主视频的video类型
    boolean isMultiResolution = isMultiResolution(adminUserId, traceId);
    if (!isMultiResolution) {
        // 非多分辨率场景，继续使用原有逻辑
        return querySliceByAdminUserIdAndTraceId(adminUserId, traceId);
    }

    // 查询对应分辨率的视频
    return shardingVideoSliceDAO.querySliceByAdminUserIdAndMainTraceIdAndResolution(
            adminUserId, traceId, resolution);
}

/**
 * 判断是否是多分辨率视频
 */
private boolean isMultiResolution(Integer adminUserId, String traceId) {
    List<ResolutionInfo> slices = shardingVideoSliceDAO.queryAvailableResolutionsByTraceId(adminUserId, traceId);
    return !CollectionUtils.isEmpty(slices);
}
```

### 4.2 工具类设计

#### M3u8MasterPlaylistGenerator工具类

**设计目标**：
- 将Master Playlist生成逻辑从Service层抽取到工具类
- 提高代码复用性和可测试性
- 支持不同的带宽计算策略

**核心方法**：
```java
public class M3u8MasterPlaylistGenerator {

    /**
     * 生成Master Playlist（带实际带宽计算）
     */
    public static void generateMasterPlaylistWithBandwidth(String traceId,
                                                          List<ResolutionInfo> resolutions,
                                                          SlicesProvider slicesProvider,
                                                          int playListType,
                                                          boolean fillVacancy,
                                                          String token,
                                                          String baseUrl,
                                                          PrintWriter writer);

    /**
     * 计算带宽（根据实际切片数据）
     */
    public static int calculateBandwidth(List<VideoSliceDO> slices, String resolution);

    /**
     * 获取默认带宽（根据分辨率）
     */
    public static int getDefaultBandwidth(String resolution);

    /**
     * 生成子播放列表URL
     */
    public static String generateSubPlaylistUrl(String traceId, String resolution,
                                               int playListType, boolean fillVacancy,
                                               String token, String baseUrl);

    /**
     * 切片数据提供者接口
     */
    @FunctionalInterface
    public interface SlicesProvider {
        List<VideoSliceDO> getSlicesByResolution(String resolution);
    }
}
```

**使用示例**：
```java
// 在VideoService中使用工具类
String baseUrl = buildBaseUrl(request);
M3u8MasterPlaylistGenerator.generateMasterPlaylistWithBandwidth(
    traceId,
    availableResolutions,
    resolution -> videoStoreService.querySliceByAdminUserIdAndTraceIdAndResolution(userId, traceId, resolution), // Lambda表达式
    playListType,
    fillVacancy,
    token,
    baseUrl,
    writer
);
```

**优势**：
- **职责分离**：专门负责Master Playlist生成，与业务逻辑解耦
- **易于测试**：可以独立进行单元测试
- **灵活扩展**：支持函数式接口，便于不同数据源接入
- **代码复用**：其他模块也可以使用此工具类

## 5. API接口设计

### 5.1 设备端切片上报接口扩展

#### 接口路径
```
POST /video/sliceReport
```

#### 请求参数变更
```json
{
  // 现有字段保持不变
  "traceId": "trace_4k_123",
  "serialNumber": "SN123456",
  "videoPath": "path/to/video.ts",
  "period": 2000,
  "order": 1,
  "isLast": 0,
  "fileSize": 1024000,
  "timezone": "Asia/Shanghai",

  // 现有ss121字段保持不变
  "videoType": 1,
  "mainTraceId": "trace_main_123",  // 双画面子画面或多分辨率附属画面时必填
  "codec": "h264",

  // 🆕新增多分辨率支持字段
  "resolution": "3840x2160"         // 🆕视频分辨率，支持多分辨率的设备必填
}
```

#### 新增字段说明

**🆕 resolution（新增：视频分辨率）**
- **类型**：String
- **必填**：支持多分辨率的设备必填
- **取值**：`"3840x2160"`、`"1280x720"`等标准分辨率值
- **说明**：设备当前上报切片的分辨率

#### 兼容性说明
- 老设备不上报新字段时，服务端会根据现有字段自动推断video类型
- 新字段都有默认值或允许为空，不影响现有设备的正常工作
- 服务端会根据设备上报情况智能处理，无需设备端做特殊适配

### 5.2 APP端m3u8下载接口

#### 5.2.1 统一接口设计（兼容新老客户端）

```java
@GetMapping(path = PATH_DOWNLOAD_M3U8 + "/{traceId}" + POSTFIX_M3U8)
public void downloadM3u8(@PathVariable String traceId,
                         @RequestParam(value = "token", required = false) String token,
                         @RequestParam(value = "playListType", required = false, defaultValue = "0") int playListType,
                         @RequestParam(value = "fillVacancy", required = false, defaultValue = "false") boolean fillVacancy,
                         @RequestParam(value = "resolution", required = false) String resolution, // 🆕用于生成特定分辨率的子播放列表
                         @RequestParam(value = "supportMasterPlaylist", required = false, defaultValue = "false") boolean supportMasterPlaylist, // 🆕是否支持Master Playlist
                         HttpServletRequest request, HttpServletResponse response
                         ) {
    if (resolution != null) {
        // 如果指定了分辨率，生成特定分辨率的播放列表（用于Master Playlist的子播放列表）
        List<VideoSliceDO> sliceList = videoStoreService.querySliceByAdminUserIdAndTraceIdAndResolution(adminId, traceId, resolution);
        videoService.downloadVideoM3u8(sliceList, playListType, fillVacancy, safeRtcRootUrl, writer);
        return;
    }
    if (!supportMasterPlaylist) {
        // 老客户端：使用原有逻辑
        videoService.downloadVideoM3u8(adminId, traceId, playListType, fillVacancy, safeRtcRootUrl, writer);
        return;
    }
    // 新客户端：生成Master Playlist
    videoService.downloadMasterPlaylistM3u8(adminId, traceId, playListType, fillVacancy, safeRtcRootUrl, token, request, writer);
}
```

#### Master Playlist格式

当视频有多个分辨率时，返回Master Playlist：

```m3u8
#EXTM3U
#EXT-X-VERSION:6

#EXT-X-STREAM-INF:BANDWIDTH=8000000,RESOLUTION=3840x2160,CODECS="avc1.640028"
trace_example_123.m3u8?resolution=3840x2160

#EXT-X-STREAM-INF:BANDWIDTH=2000000,RESOLUTION=1280x720,CODECS="avc1.640028"
trace_example_123.m3u8?resolution=1280x720
```

#### 参数说明

**🆕 resolution（新增参数，仅在master接口中）**
- **类型**：String
- **必填**：否
- **取值**：`"3840x2160"`、`"1280x720"`等
- **说明**：仅在master接口中使用，用于生成特定分辨率的子播放列表，由Master Playlist自动调用

#### 兼容性设计

**老客户端兼容**：
- 老版本APP请求原有接口：`GET /video/download/m3u8/{traceId}.m3u8`（不传supportMasterPlaylist参数或传false）
- 服务端返回默认分辨率的播放列表（4K优先，如果没有则返回720P）
- 保持与现有APP的完全兼容

**新客户端支持**：
- 新版本APP请求同一接口：`GET /video/download/m3u8/{traceId}.m3u8?supportMasterPlaylist=true`
- 服务端返回Master Playlist包含所有可用分辨率
- 客户端根据网络状况自动选择合适的分辨率
- 客户端自动请求对应的子播放列表：`GET /video/download/m3u8/{traceId}.m3u8?resolution=xxx`

### 5.3 视频列表接口改造

#### 5.3.1 支持supportMasterPlaylist参数的接口列表

以下接口都新增了`supportMasterPlaylist`参数支持：

1. **POST /library/selectlibrary** - 获取用户所有设备的录像
2. **POST /library/newselectlibrary** - 获取用户所有设备的录像（新版）
3. **POST /library/selectTimeLineLibrary** - 获取时间线录像列表
4. **POST /library/selectsinglelibrary** - 获取单个录像信息
5. **POST /library/newselectsinglelibrary** - 获取单个录像信息（新版）
6. **POST /library/getLibraryByTraceId** - 根据traceId获取录像信息

#### 5.3.2 请求参数扩展

**LibraryRequest.java**：
```java
public class LibraryRequest {
    // 现有字段...
    @Schema(title = "返回结果的event视频中，是否需要包含subVideos字段(子画面视频列表)")
    private Boolean needSubVideos;

    /* 🆕多分辨率支持 begin */
    @Schema(title = "是否支持Master Playlist。true-返回新的master接口URL；false-返回原有接口URL。默认false兼容老APP")
    private Boolean supportMasterPlaylist = false;
    @Schema(title = "返回结果的event视频中，是否需要包含multiResolutionVideos字段(多分辨率视频列表)")
    private Boolean needMultiResolutionVideos;
    /* 多分辨率支持 end */
}
```

**UserLibraryViewRequest.java**：
```java
public class UserLibraryViewRequest {
    // 现有字段...

    @Schema(title = "是否支持Master Playlist。true-返回新的master接口URL；false-返回原有接口URL。默认false兼容老APP")
    private boolean supportMasterPlaylist = false;
}
```

**TraceLibraryRequest.java**：
```java
public class TraceLibraryRequest {
    private String traceId;

    @Schema(title = "是否支持Master Playlist。true-返回新的master接口URL；false-返回原有接口URL。默认false兼容老APP")
    private boolean supportMasterPlaylist = false;
}
```

#### 5.3.3 videoUrl生成逻辑
- `supportMasterPlaylist=false`（默认）：返回原有格式URL，兼容老APP
- `supportMasterPlaylist=true`：多分辨率视频返回Master Playlist URL，单分辨率视频返回原有URL

**🆕多分辨率视频列表设计**：
- 原有：`subVideos`字段只包含双画面子视频（videoType=2）
- 🆕新增：`multiResolutionVideos`字段专门包含多分辨率子视频（videoType=3）
- 🆕新增：`hasMultiResolutionVideos`字段表示事件是否包含多分辨率视频

**实际代码实现**：
```java
// 在LibraryService中分别查询双画面子视频和多分辨率子视频
// 查询双画面子视频
List<Integer> subVideoTypes = Arrays.asList(VideoTypeEnum.EVNET_RECORDING_SUB_VIEW.getCode());
List<LibraryTb> subViewLibraryList = shardingLibraryDAO.selectLibraryInfoByUserIdsAndBatchMainTraceId(
    adminIdSet, traceIdSet, subVideoTypes, null);

// 查询多分辨率子视频
List<Integer> multiResolutionVideoTypes = Arrays.asList(VideoTypeEnum.EVNET_RECORDING_MULTI_RESOLUTION.getCode());
List<LibraryTb> multiResolutionLibraryList = shardingLibraryDAO.selectLibraryInfoByUserIdsAndBatchMainTraceId(
    adminIdSet, traceIdSet, multiResolutionVideoTypes, null);
```

**响应示例**：
```json
[
  {
    "traceId": "ID_01_1734936195_e34a_1",
    "serviceName": "s3",
    "resolution": "3840x2160",
    "videoUrl": "https://iot-service/video/download/m3u8/v2.m3u8?supportMasterPlaylist=true", // 🆕新APP返回带supportMasterPlaylist参数的URL
    "imageUrl": "https://s3/device_video_slice/sn/v2/image.jpeg",
    "videoType": 1,
    "endTimestamp": 1739270998,
    "timestamp": 1739270982,
    "period": 10000,
    "tags": "person",
    "sliceList": [],
    "subVideos": [
      {
        "traceId": "ID_01_1734936195_e34a_2",
        "videoUrl": "https://iot-service/video/download/m3u8/v21.m3u8", // 双画面子视频
        "imageUrl": "https://s3/device_video_slice/sn/v21/image.jpeg",
        "videoType": 2,
        "timestamp": 1739270982,
        "endTimestamp": 1739270998,
        "period": 10000,
        "sliceList": []
      }
    ],
    "multiResolutionVideos": [
      {
        "traceId": "ID_01_1734936195_e34a_3",
        "videoUrl": "https://iot-service/video/download/m3u8/v22.m3u8?supportMasterPlaylist=true", // 🆕多分辨率子视频
        "imageUrl": "https://s3/device_video_slice/sn/v22/image.jpeg",
        "videoType": 3,
        "resolution": "1280x720", // 🆕分辨率信息
        "timestamp": 1739270982,
        "endTimestamp": 1739270998,
        "period": 10000,
        "sliceList": []
      }
    ]
  }
]
```

### 5.4 分辨率查询接口

```java
@GetMapping("/video/{traceId}/resolutions")
public Result<List<ResolutionInfo>> getAvailableResolutions(@PathVariable String traceId,
                                                            @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId) {
    Integer adminId = libraryStatusService.queryAdminIdByUserIdAndTraceId(userId, traceId);
    if (adminId == null) {
        return Result.Error(ResultCollection.NO_LIBRARY_ACCESS, "no library access");
    }

    List<ResolutionInfo> resolutions = videoStoreService.queryAvailableResolutionsByTraceId(adminId, traceId);
    resolutions.forEach((e) -> e.setDisplayName(VideoResolutionConstants.getDisplayName(e.getResolution())));

    return new Result<>(resolutions);
}
```

### 5.5 数据库DAO层实现

#### IShardingVideoSliceDAO.java
```java
// 多分辨率支持：按分辨率查询附属视频切片
@ShardingReadOnly
@Select("select " + SQL_SELECT_VIDEO_SLICE_COLUMNS + " from `video-library-db`.`video_slice`" +
        " where `user_id` = #{userId} and `main_trace_id`=#{mainTraceId} and `resolution`=#{resolution} order by `order` asc")
List<VideoSliceDO> querySliceByAdminUserIdAndMainTraceIdAndResolution(@Param("userId") Integer userId,
                                                                      @Param("mainTraceId") String mainTraceId,
                                                                      @Param("resolution") String resolution);

// 多分辨率支持：查询某个traceId下所有可用的分辨率
@ShardingReadOnly
@Select("<script>" +
        "select distinct `resolution`, `video_type` from `video-library-db`.`video_slice`" +
        " where `user_id` = #{userId} and (" +
        "   (`trace_id`=#{traceId} and `video_type` in (1,3)) or " +  // 主视频（video_type=1兼容老设备，video_type=3新设备）
        "   (`main_trace_id`=#{traceId} and `video_type`=3)" +    // 多分辨率附属视频（720P）
        " ) and `resolution` is not null" +
        "</script>")
List<ResolutionInfo> queryAvailableResolutionsByTraceId(@Param("userId") Integer userId, @Param("traceId") String traceId);
```

#### ResolutionInfo.java
```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResolutionInfo {

    /**
     * 分辨率值，如"1280x720"、"3840x2160"
     */
    private String resolution;

    /**
     * video类型
     */
    private Integer videoType;

    /**
     * 显示名称，如"720P"、"4K"
     */
    private String displayName;
}
```

#### 响应格式
```json
{
  "result": 1,
  "data": [
    {
      "resolution": "3840x2160",
      "displayName": "4K",
      "videoType": 1
    },
    {
      "resolution": "1280x720",
      "displayName": "720P",
      "videoType": 3
    }
  ]
}
```

### 5.6 supportMasterPlaylist参数详细说明

#### 5.6.1 参数作用
`supportMasterPlaylist`参数用于控制视频列表接口返回的videoUrl格式：
- `false`（默认）：返回传统的m3u8 URL，兼容老版本APP
- `true`：对于多分辨率视频，返回带有`supportMasterPlaylist=true`参数的URL，支持Master Playlist功能

#### 5.6.2 URL生成逻辑
```java
// 在S3Service或相关服务中的URL生成逻辑
public String generateVideoUrl(String traceId, boolean supportMasterPlaylist) {
    String baseUrl = "/video/download/m3u8/" + traceId + ".m3u8";

    if (supportMasterPlaylist) {
        // 新客户端：添加supportMasterPlaylist参数
        return baseUrl + "?supportMasterPlaylist=true";
    } else {
        // 老客户端：返回原有格式
        return baseUrl;
    }
}
```

#### 5.6.3 客户端使用流程
1. **老客户端**：不传`supportMasterPlaylist`参数或传`false`
   - 服务端返回传统videoUrl
   - 播放器直接播放默认分辨率视频

2. **新客户端**：传`supportMasterPlaylist=true`
   - 服务端返回带参数的videoUrl
   - 播放器请求时会获得Master Playlist
   - 播放器根据网络状况自动选择分辨率

## 6. 使用示例

### 6.1 设备端切片上报示例

#### 普通单画面设备（兼容老设备）
```json
POST /video/sliceReport
{
  "traceId": "trace_normal_123",
  "serialNumber": "SN123456",
  "videoPath": "path/to/video.ts",
  "period": 2000,
  "order": 1,
  "isLast": 0,
  "fileSize": 1024000,
  "timezone": "Asia/Shanghai"
}
```

#### 多分辨率设备4K主画面上报
```json
POST /video/sliceReport
{
  "traceId": "trace_4k_123",
  "serialNumber": "SN123456",
  "videoPath": "path/to/4k_video.ts",
  "period": 2000,
  "order": 1,
  "isLast": 0,
  "fileSize": 2048000,
  "timezone": "Asia/Shanghai",
  "resolution": "3840x2160",
  "videoType": 3  // 多分辨率视频
}
```

#### 多分辨率设备720P附属画面上报
```json
POST /video/sliceReport
{
  "traceId": "trace_720p_123",
  "serialNumber": "SN123456",
  "videoPath": "path/to/720p_video.ts",
  "period": 2000,
  "order": 1,
  "isLast": 0,
  "fileSize": 512000,
  "timezone": "Asia/Shanghai",
  "resolution": "1280x720",
  "videoType": 3,  // 多分辨率视频
  "mainTraceId": "trace_4k_123"  // 关联到4K主画面
}
```

#### 双画面设备（兼容现有逻辑）
```json
// 主画面
POST /video/sliceReport
{
  "traceId": "trace_main_123",
  "serialNumber": "SN123456",
  "videoPath": "path/to/main_video.ts",
  "videoType": 1  // 双画面主画面
}

// 子画面
POST /video/sliceReport
{
  "traceId": "trace_sub_123",
  "serialNumber": "SN123456",
  "videoPath": "path/to/sub_video.ts",
  "videoType": 2,  // 双画面子画面
  "mainTraceId": "trace_main_123"
}
```

### 6.2 APP端API调用示例

#### 6.2.1 老客户端（兼容模式）

```bash
# 老版本APP：获取播放列表（完全保持原有逻辑）
GET /video/download/m3u8/{traceId}.m3u8?token=xxx

# 查询录像列表（不传supportMasterPlaylist参数，默认false）
POST /library/newselectlibrary
{
  "deviceSn": "xxx",
  "startTime": 1739270982,
  "endTime": 1739270998
}

# 其他接口同样兼容（都不传supportMasterPlaylist参数）
POST /library/selectlibrary
POST /library/selectTimeLineLibrary
POST /library/selectsinglelibrary
POST /library/newselectsinglelibrary
POST /library/getLibraryByTraceId
```

#### 6.2.2 新客户端（自动切换模式）

```bash
# 🆕新版本APP：获取Master Playlist（包含所有分辨率，客户端自动切换）
GET /video/download/m3u8/{traceId}.m3u8?token=xxx&supportMasterPlaylist=true

# 获取特定分辨率的子播放列表（由Master Playlist自动调用，APP无需直接调用）
GET /video/download/m3u8/{traceId}.m3u8?token=xxx&resolution=3840x2160
GET /video/download/m3u8/{traceId}.m3u8?token=xxx&resolution=1280x720

# 查询录像列表（传supportMasterPlaylist=true）
POST /library/newselectlibrary
{
  "deviceSn": "xxx",
  "startTime": 1739270982,
  "endTime": 1739270998,
  "supportMasterPlaylist": true
}

# 其他接口同样支持supportMasterPlaylist参数
POST /library/selectlibrary
{
  "deviceSn": "xxx",
  "startTime": 1739270982,
  "endTime": 1739270998,
  "supportMasterPlaylist": true
}

POST /library/selectTimeLineLibrary
{
  "deviceSn": "xxx",
  "startTime": 1739270982,
  "endTime": 1739270998,
  "supportMasterPlaylist": true
}

POST /library/selectsinglelibrary
{
  "traceId": "trace_4k_123",
  "supportMasterPlaylist": true
}

POST /library/newselectsinglelibrary
{
  "traceId": "trace_4k_123",
  "supportMasterPlaylist": true
}

POST /library/getLibraryByTraceId
{
  "traceId": "trace_4k_123",
  "supportMasterPlaylist": true
}

# 查询视频支持的分辨率列表（可选，用于展示）
GET /video/{traceId}/resolutions
```

### 6.3 Master Playlist响应示例

```m3u8
#EXTM3U
#EXT-X-VERSION:6

#EXT-X-STREAM-INF:BANDWIDTH=8000000,RESOLUTION=3840x2160,CODECS="avc1.640028"
trace_4k_123.m3u8?token=xxx&resolution=3840x2160

#EXT-X-STREAM-INF:BANDWIDTH=2000000,RESOLUTION=1280x720,CODECS="avc1.640028"
trace_4k_123.m3u8?token=xxx&resolution=1280x720
```

### 6.4 分辨率查询响应示例

```json
{
  "result": 1,
  "data": [
    {
      "resolution": "3840x2160",
      "displayName": "4K",
      "videoType": 1
    },
    {
      "resolution": "1280x720",
      "displayName": "720P",
      "videoType": 3
    }
  ]
}
```

## 7. 兼容性保证

### 7.1 向后兼容
- 原有m3u8接口不传resolution参数时，继续使用原有逻辑
- 所有设备的切片存储和查询逻辑保持不变
- 老设备继续使用现有videoType值（0、1、2）
- resolution字段允许为NULL，不影响现有数据

### 7.2 渐进式升级
- 可以先部署服务端代码，支持resolution参数
- 设备端固件可以逐步升级，开始上报分辨率信息
- APP端可以逐步支持分辨率选择功能

### 7.3 数据迁移
- 现有设备的切片数据resolution字段为NULL
- 现有设备继续使用原有videoType值（0、1、2）
- 新的多分辨率设备：
  - 4K主视频：可以使用videoType=1（兼容老设备）或videoType=3（新设备）
  - 720P附属视频：使用videoType=3，通过main_trace_id关联到4K主视频
- 通过resolution字段和main_trace_id区分不同分辨率的视频
- 可以根据设备的recResolution配置推断历史数据的分辨率
- 新的切片上报会包含明确的分辨率信息

## 8. 测试验证

### 8.1 功能测试
- [ ] 多分辨率设备上报4K分辨率切片（videoType=3, resolution="3840x2160"）
- [ ] 多分辨率设备上报720P分辨率切片（videoType=3, resolution="1280x720"）
- [ ] 4K分辨率m3u8文件生成和下载
- [ ] 720P分辨率m3u8文件生成和下载
- [ ] 分辨率列表查询接口
- [ ] 其他设备型号兼容性测试

### 8.2 性能测试
- [ ] 多分辨率查询性能
- [ ] 索引效果验证
- [ ] 并发访问测试

## 9. 部署计划

### 9.1 数据库变更
1. 执行DDL语句添加字段和索引
2. 验证表结构变更成功

### 9.2 代码部署
1. 部署服务端代码
2. 验证API接口功能
3. 监控系统运行状态

### 9.3 设备端升级
1. 发布支持分辨率上报的固件
2. 逐步推送固件升级
3. 监控设备上报数据

## 10. 风险评估

### 10.1 技术风险
- **低风险**：完全复用现有双画面逻辑，技术方案成熟
- **数据一致性**：通过main_trace_id强关联保证数据一致性
- **性能影响**：新增索引优化查询性能，影响可控

### 10.2 兼容性风险
- **低风险**：新增字段允许NULL，不影响现有功能
- **渐进式升级**：支持逐步升级，降低风险

### 10.3 运维风险
- **监控完善**：需要监控新字段的数据质量
- **回滚方案**：可以通过配置开关快速回滚到原有逻辑

## 11. 重要修复总结

### 11.1 关键问题修复

#### 问题1：SELECT查询缺少resolution字段
**问题描述**：虽然数据库表添加了resolution字段，但SELECT查询中没有包含该字段，导致客户端无法获取分辨率信息。

**修复方案**：
- 更新`IShardingLibraryDAO.java`中的`SELECT_LIBRARY_COLUMNS`常量，包含resolution字段
- 修复`selectLibraryByUserIdsAndMainTraceIds`等查询方法，确保返回resolution信息
- 在`DeviceLibraryViewDO`和`UserLibraryViewDO`中添加resolution字段

#### 问题2：subVideos字段复用导致APP无法区分视频类型
**问题描述**：原设计将多分辨率视频也放入subVideos列表，但该列表原本用于双画面子视频，APP通过hasSubVideos字段判断显示逻辑，会导致无法区分双画面和多分辨率视频。

**修复方案**：
- 在`UserLibraryViewDO`中新增`multiResolutionVideos`字段，专门用于多分辨率视频
- 在`LibraryEventView`中新增`hasMultiResolutionVideos`字段
- 在`LibraryRequest`中新增`needMultiResolutionVideos`参数
- 修改`LibraryService`逻辑，分别查询和处理双画面子视频和多分辨率视频

### 11.2 数据完整性保证

#### 数据库层面
- **video_library表**：添加resolution字段，提升查询性能
- **library_status表**：添加resolution字段，保持数据一致性
- **video_slice表**：主要存储表，包含完整分辨率信息

#### 代码层面
- **VideoCache**：添加resolution字段和setResolution方法
- **VideoStoreService**：确保插入video_library和library_status时都设置resolution
- **LibraryTb/LibraryStatusTb**：实体类包含resolution字段
- **DAO层**：INSERT和SELECT操作都包含resolution字段

### 11.3 架构设计优化

#### 字段分离设计
```java
// 原有设计（有问题）
List<UserLibraryViewDO> subVideos; // 混合了双画面和多分辨率视频

// 新设计（修复后）
List<UserLibraryViewDO> subVideos; // 只包含双画面子视频（videoType=2）
List<UserLibraryViewDO> multiResolutionVideos; // 只包含多分辨率视频（videoType=3）
```

#### 数据流完整性
```
设备上报 → VideoSliceDO → VideoCache → VideoStoreService → 数据库表 → 查询接口 → 客户端
每个环节都正确处理resolution字段，确保数据流完整性
```

### 11.4 向后兼容性
- **老APP**：继续使用subVideos字段，不受影响
- **新APP**：可以使用multiResolutionVideos字段获取多分辨率视频
- **参数控制**：通过needMultiResolutionVideos参数控制是否查询多分辨率视频

### 11.5 修复验证要点
1. ✅ 所有SELECT查询都包含resolution字段
2. ✅ 所有INSERT操作都包含resolution字段
3. ✅ subVideos和multiResolutionVideos字段分离
4. ✅ 数据流从设备端到客户端完整传递resolution信息
5. ✅ APP可以正确区分双画面子视频和多分辨率视频
