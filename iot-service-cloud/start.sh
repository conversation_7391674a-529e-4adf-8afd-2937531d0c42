#!/bin/bash
#这里可替换为你自己的执行程序，其他代码无需更改
APP_NAME=iot-camera.jar
#使用说明，用来提示输入参数
print_help() {
    echo "samples:"
    echo "-------------------------------------------------------------------------"
    echo "sh run.sh status             :    查看项目当前是否运行"
    echo "sh run.sh start cn/us/test/local              :    启动项目 profile=prod-cn/prod-us/test/local"
    echo "sh run.sh stop xx              :    停止项目 + cn/us/test"
    echo "sh run.sh restart xx           :    重新启动项目 profile=prod + cn/us/test"
    echo "-------------------------------------------------------------------------"

    exit 1
}

#检查程序是否在运行
is_exist(){
  pid=`ps -ef|grep $APP_NAME|grep -v grep|awk '{print $2}' `
  #如果不存在返回1，存在返回0
  if [ -z "${pid}" ]; then
   return 1
  else
    return 0
  fi
}

#启动方法
start(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "${APP_NAME} is already running. pid=${pid} ."
  else
    echo "current profile>>>>> ${profile}"
    nohup java -jar $APP_NAME $profile >> launch.log 2>&1 &
    echo "---------------------------------"
    echo "${APP_NAME} 启动完成>>>>>"
    echo "---------------------------------"
  fi
}

#停止方法
stop(){
  is_exist
  if [ $? -eq "0" ]; then
    kill -9 $pid
    echo "Kill Process ${APP_NAME} !!!"
  else
    echo "${APP_NAME} is not running"
  fi
}

#输出运行状态
status(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "${APP_NAME} is running. Pid is ${pid}"
  else
    echo "${APP_NAME} is NOT running."
  fi
}

#重启
restart(){
  stop
  sleep 2s
  start
}



# 取profile
profile=$2
environment=""

#if [ ! -z $2 ]; then
#    environment=$2
#else
#    environment="prod"
#fi

if [ $2 = "test" ]; then
    environment="dev"
elif [ $2 = "local" ]; then
    environment="local"
else
    environment="prod"
fi

profile="--spring.profiles.active=${environment}"

#根据输入参数，选择执行对应方法，不输入则执行使用说明

case "$1" in
  "start")
    start
    ;;
  "stop")
    stop
    ;;
  "status")
    status
    ;;
  "restart")
    restart
    ;;
  *)
    print_help
    ;;
esac