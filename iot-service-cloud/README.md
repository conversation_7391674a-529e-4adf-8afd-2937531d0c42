# iot-camera
### 1.接口测试
1. 登录
```
 curl -X POST 'https://api-test.addx.live/account/login' -H 'Content-Type:application/json' -d '{"email":"<EMAIL>","password":"123456","app":{"tenantId":"vicoo"}}'
```
2. 查询视频列表
```
curl -X POST 'https://api-test.addx.live/library/selectsinglelibrary' -H 'Content-Type:application/json' -H 'Authorization:Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************.bFv4j7zYrEb_T4jAxz-pNniRDoF87Yqftj2ZXvZ0L2gezaPfkbY_argVS93EiNAzGN6KtU5P7PvCcucb7uAp6A' -d '{"id":"460057"}'
```
2. proto代码生成
```
mvn protobuf:compile;mvn protobuf:compile-custom;
```