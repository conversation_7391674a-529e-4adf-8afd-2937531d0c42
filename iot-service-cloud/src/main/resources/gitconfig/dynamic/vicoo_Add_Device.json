[{"subview": {"flexDirection": "column", "marginTop": 5, "subviews": [{"flexDirection": "column", "subviews": [{"paddingRight": 15, "position": "absolute", "paddingBottom": 20, "flexGrow": 1, "alignItems": "flexStart", "subviews": [{"percentWidth": 48, "radius": 11, "background_color": "#ffF5F6FA", "alignItems": "center", "contentMode": "scaleAspectFill", "subviews": [{"height": 75, "marginTop": 24, "contentMode": "scaleAspectFill", "image_url": {"cn": "https://vicoo.tech/images/add_device_cg0.png"}, "image": {"cn": "add_device_cg0"}, "width": 75, "type": "ImageView"}, {"textAlignment": "center", "textFont": 16, "marginTop": 19, "title": {"cn": "G0"}, "marginLeft": 10, "type": "Label", "marginRight": 10, "textColor": "#ff333333", "bold": true}, {"textAlignment": "center", "textFont": 13, "marginTop": 3, "title": {"en": "SmartBatteryCamera", "cn": "智能电池摄像机", "ja": "スマートバッテリーカメラ"}, "marginLeft": 10, "textLines": 0, "type": "Label", "textColor": "#ff333333", "marginRight": 10}], "type": "TextButton", "value": "CG0", "action": "pushAddDevice", "identery": "adddevice_cg0", "updateKey": "deviceType", "height": 185, "flexDirection": "column"}, {"percentWidth": 48, "radius": 11, "background_color": "#ffF5F6FA", "alignItems": "center", "contentMode": "scaleAspectFill", "subviews": [{"height": 75, "marginTop": 24, "contentMode": "scaleAspectFill", "image_url": {"cn": "https://vicoo.tech/images/add_device_cg1.png"}, "image": {"cn": "add_device_cg1"}, "width": 75, "type": "ImageView"}, {"textAlignment": "center", "textFont": 16, "marginTop": 19, "title": {"cn": "CG1"}, "marginLeft": 10, "type": "Label", "marginRight": 10, "textColor": "#ff333333", "bold": true}, {"textAlignment": "center", "textFont": 13, "marginTop": 3, "title": {"en": "SmartBatteryCamera", "cn": "智能电池摄像机", "ja": "スマートバッテリーカメラ"}, "marginLeft": 10, "textLines": 0, "type": "Label", "textColor": "#ff333333", "marginRight": 10}], "type": "TextButton", "value": "CG1", "action": "pushAddDevice", "identery": "adddevice_cg1", "updateKey": "deviceType", "height": 185, "flexDirection": "column"}, {"percentWidth": 48, "radius": 11, "background_color": "#ffF5F6FA", "alignItems": "center", "contentMode": "scaleAspectFill", "subviews": [{"height": 75, "marginTop": 24, "contentMode": "scaleAspectFill", "image_url": {"cn": "https://vicoo.tech/images/add_device_cg2.png"}, "image": {"cn": "add_device_cg2"}, "width": 75, "type": "ImageView"}, {"textAlignment": "center", "textFont": 16, "marginTop": 19, "title": {"cn": "CG2"}, "marginLeft": 10, "type": "Label", "marginRight": 10, "textColor": "#ff333333", "bold": true}, {"textAlignment": "center", "textFont": 13, "marginTop": 3, "title": {"en": "SmartBatteryCamera", "cn": "智能电池摄像机", "ja": "スマートバッテリーカメラ"}, "marginLeft": 10, "textLines": 0, "type": "Label", "textColor": "#ff333333", "marginRight": 10}], "marginTop": 10, "type": "TextButton", "value": "CG2", "updateKey": "deviceType", "action": "pushAddDevice", "identery": "adddevice_cg2", "height": 185, "flexDirection": "column"}, {"percentWidth": 48, "radius": 11, "background_color": "#ffF5F6FA", "alignItems": "center", "contentMode": "scaleAspectFill", "subviews": [{"height": 75, "marginTop": 24, "contentMode": "scaleAspectFill", "image_url": {"cn": "https://vicoo.tech/images/add_device_cb0.png"}, "image": {"cn": "add_device_cb0"}, "width": 75, "type": "ImageView"}, {"textAlignment": "center", "textFont": 16, "marginTop": 19, "title": {"cn": "CB0"}, "marginLeft": 10, "type": "Label", "marginRight": 10, "textColor": "#ff333333", "bold": true}, {"textAlignment": "center", "textFont": 13, "marginTop": 3, "title": {"en": "SmartPTZCamera", "cn": "智能云台摄像机", "ja": "スマート雲台カメラ"}, "marginLeft": 10, "textLines": 0, "type": "Label", "textColor": "#ff333333", "marginRight": 10}], "marginTop": 10, "type": "TextButton", "value": "CB0", "action": "pushAddDevice", "identery": "adddevice_cb0", "updateKey": "deviceType", "height": 185, "flexDirection": "column"}, {"percentWidth": 48, "radius": 11, "background_color": "#ffF5F6FA", "alignItems": "center", "contentMode": "scaleAspectFill", "subviews": [{"height": 75, "marginTop": 24, "contentMode": "scaleAspectFill", "image_url": {"cn": "https://vicoo.tech/images/add_device_cb2.png"}, "image": {"cn": "add_device_cb2"}, "width": 75, "type": "ImageView"}, {"textAlignment": "center", "textFont": 16, "marginTop": 19, "title": {"cn": "CB1"}, "marginLeft": 10, "type": "Label", "marginRight": 10, "textColor": "#ff333333", "bold": true}, {"textAlignment": "center", "textFont": 13, "marginTop": 3, "title": {"en": "SmartPTZCamera", "cn": "智能云台摄像机", "ja": "スマート雲台カメラ"}, "marginLeft": 10, "textLines": 0, "type": "Label", "textColor": "#ff333333", "marginRight": 10}], "marginTop": 10, "type": "TextButton", "value": "CB1", "action": "pushAddDevice", "identery": "adddevice_cb1", "updateKey": "deviceType", "height": 185, "flexDirection": "column"}, {"percentWidth": 48, "radius": 11, "background_color": "#ffF5F6FA", "alignItems": "center", "contentMode": "scaleAspectFill", "subviews": [{"height": 75, "marginTop": 24, "contentMode": "scaleAspectFill", "image_url": {"cn": "https://vicoo.tech/images/add_device_ck.png"}, "image": {"cn": "add_device_default"}, "width": 75, "type": "ImageView"}, {"textAlignment": "center", "textFont": 16, "marginTop": 19, "title": {"cn": "CK0"}, "marginLeft": 10, "type": "Label", "marginRight": 10, "textColor": "#ff333333", "bold": true}, {"textAlignment": "center", "textFont": 13, "marginTop": 3, "title": {"en": "SmartcardCamera", "cn": "智能卡片摄像机", "ja": "スマートカードカメラ"}, "marginLeft": 10, "textLines": 0, "type": "Label", "textColor": "#ff333333", "marginRight": 10}], "marginTop": 10, "type": "TextButton", "value": "CK0", "action": "pushAddDevice", "identery": "adddevice_ck0", "updateKey": "deviceType", "height": 185, "flexDirection": "column"}, {"percentWidth": 48, "radius": 11, "background_color": "#ffF5F6FA", "alignItems": "center", "contentMode": "scaleAspectFill", "subviews": [{"height": 75, "marginTop": 24, "contentMode": "scaleAspectFill", "image_url": {"cn": "https://vicoo.tech/images/add_device_cs0.png"}, "image": {"cn": "add_device_default"}, "width": 75, "type": "ImageView"}, {"textAlignment": "center", "textFont": 16, "marginTop": 19, "title": {"cn": "CS0"}, "marginLeft": 10, "type": "Label", "marginRight": 10, "textColor": "#ff333333", "bold": true}, {"textAlignment": "center", "textFont": 13, "marginTop": 3, "title": {"en": "SmartBoxCamera", "cn": "智能枪型摄像机", "ja": "スマートガンカメラ"}, "marginLeft": 10, "textLines": 0, "type": "Label", "textColor": "#ff333333", "marginRight": 10}], "marginTop": 10, "type": "TextButton", "value": "CS0", "action": "pushAddDevice", "identery": "adddevice_cs0", "updateKey": "deviceType", "height": 185, "flexDirection": "column"}, {"percentWidth": 48, "radius": 11, "background_color": "#ffF5F6FA", "alignItems": "center", "contentMode": "scaleAspectFill", "subviews": [{"width": 75, "contentMode": "scaleAspectFill", "marginTop": 24, "height": 75, "type": "ImageView", "image": {"en": "add_device_join", "ja": "add_device_join", "cn": "add_device_join"}}, {"textAlignment": "center", "textFont": 16, "marginTop": 19, "title": {"cn": "查看好友的相机", "en": "GetAccessToFriend'sCamera", "ja": "友達のカメラを見る"}, "marginLeft": 10, "textLines": 0, "type": "Label", "textColor": "#ff333333", "marginRight": 10}], "marginTop": 10, "type": "TextButton", "value": "join", "action": "joinDeviceAction", "identery": "adddevice_join", "updateKey": "deviceType", "height": 185, "flexDirection": "column"}], "flexDirection": "row", "justifyContent": "spaceBetween", "paddingLeft": 15, "flexWrap": "wrap"}], "flexGrow": 1, "type": "<PERSON><PERSON>"}], "flexGrow": 1}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "hiddenKey": "showBackPress", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "hiddenKey": "showBack", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"identery": "skipActionButton", "type": "TextButton", "padding": 5, "title": {"en": "<PERSON><PERSON>", "cn": "跳过", "ja": "スキップ"}, "action": "finishAction", "width": 60, "percentHeight": 100, "hiddenKey": "showSkip", "textColor": "#5f333333", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "Add_device_entry", "actions": [{"identery": "pushAddDevice", "type": "push", "to": "add_device_select_location", "actionType": "action_local_jump", "actionJumpTo": "add_device_select_location", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_SELECT_LOCATION;S.yoga_key_dynamic_page=add_device_select_location;end"}, {"identery": "finishAction", "type": "finish"}, {"identery": "joinDeviceAction", "type": "push"}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "双击电源键进入扫描", "en": "Double click the button to scan", "ja": "電源ボタンをダブルクリックしてスキャンを開始します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到提示音之后，即可进行下一步", "en": "When you hear the sound, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_reset_g2.gif", "ja": "https://vicoo.tech/images/bind/add_device_reset_g2.gif", "en": "https://vicoo.tech/images/bind/add_device_reset_g2.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "hidden": true, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360041953993", "action": "action_zendesk_jump"}, {"textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到声音", "en": "I heard  the sound.", "ja": "音声が聞こえました"}, "identery": "listen_voice", "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_qr_code"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "3/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_reset_g2", "actions": [{"identery": "pushAddDevice", "type": "push"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "push_to_qr_code", "actionType": "action_local_jump", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_QR_CODE;end"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360041953993"}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "长按电源键开机", "en": "<PERSON> press the button to power up", "ja": "電源ボタンを長押しして起動します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到提示音之后，即可进行下一步", "en": "When you hear the sound, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_charge_g1.gif", "ja": "https://vicoo.tech/images/bind/add_device_charge_g1.gif", "en": "https://vicoo.tech/images/bind/add_device_charge_g1.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360041953993", "action": "action_zendesk_jump"}, {"identery": "nextAction", "textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到声音", "en": "I heard  the sound.", "ja": "音声が聞こえました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_reset"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "2/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_setup_g1", "actions": [{"identery": "push_to_reset", "type": "push", "to": "add_device_power_reset_g1", "actionType": "action_dynamic_jump", "actionJumpTo": "add_device_power_reset_g1"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360041953993"}]}, {"subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "长按电源键开机", "en": "<PERSON> press the button to power up", "ja": "電源ボタンを長押しして起動します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到“开机”后，即可进行下一步", "en": "When you hear \"Power up\", you can proceed to the next step.", "ja": "「電源オン」と聞こえたら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "action": "add_device_power_setup_g0_next", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_charge_g0_zh.gif", "ja": "https://vicoo.tech/images/bind/add_device_charge_g0_ja.gif", "en": "https://vicoo.tech/images/bind/add_device_charge_g0_en.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360041953993", "action": "action_zendesk_jump"}, {"identery": "nextAction", "textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到“开机”", "en": "I heard \"Power Up\".", "ja": "「起動」と聞こえました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_reset"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "2/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_setup_g0", "actions": [{"identery": "push_to_reset", "type": "push", "to": "add_device_power_check_g0", "actionType": "action_dynamic_jump", "actionJumpTo": "add_device_power_check_g0"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360041953993"}], "background_color": "#ffffff"}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "长按Reset键进入扫描", "en": "Long press reset button to scan", "ja": "Resetキーを長押ししてスキャンします"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到提示音之后，即可进行下一步", "en": "When you hear the sound, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_reset_k0.gif", "ja": "https://vicoo.tech/images/bind/add_device_reset_k0.gif", "en": "https://vicoo.tech/images/bind/add_device_reset_k0.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "找不到Reset键？", "en": "Cannot find reset button?", "ja": "Resetキーが見つかりませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360044504253", "action": "action_zendesk_jump"}, {"textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到声音", "en": "I heard  the sound.", "ja": "音声が聞こえました"}, "identery": "listen_voice", "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_qr_code"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "3/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_reset_k0", "actions": [{"identery": "pushAddDevice", "type": "push"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "push_to_qr_code", "actionType": "action_local_jump", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_QR_CODE;end"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360044504253"}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "连接电源以开机", "en": "Plug in to power up", "ja": "電源に接続して起動します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到提示音之后，即可进行下一步", "en": "When you hear the sound, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_charge_b0.gif", "ja": "https://vicoo.tech/images/bind/add_device_charge_b0.gif", "en": "https://vicoo.tech/images/bind/add_device_charge_b0.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360043907234", "action": "action_zendesk_jump"}, {"identery": "nextAction", "textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "摄像机已完成转动", "en": "Camera finished rotating.", "ja": "カメラの回転が完了しました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_reset"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "2/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_setup_b0", "actions": [{"identery": "push_to_reset", "type": "push", "to": "add_device_power_reset_b0", "actionType": "action_dynamic_jump", "actionJumpTo": "add_device_power_reset_b0"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360043907234"}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "长按Reset键进入扫描", "en": "Long press reset button to scan", "ja": "Resetキーを長押ししてスキャンします"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到扫描二维码声音之后，即可进行下一步", "en": "When you hear scan QR code sound, you can proceed to the next step.", "ja": "QRコードスキャン音が聞こえたら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_reset_b2.gif", "ja": "https://vicoo.tech/images/bind/add_device_reset_b2.gif", "en": "https://vicoo.tech/images/bind/add_device_reset_b2.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "找不到Reset键？", "en": "Cannot find reset button?", "ja": "Resetキーが見つかりませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360043907834", "action": "action_zendesk_jump"}, {"textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到声音", "en": "I heard  the sound.", "ja": "音声が聞こえました"}, "identery": "push_to_qr_code", "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "3/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_reset_b2", "actions": [{"identery": "pushAddDevice", "type": "push"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "push_to_qr_code", "actionType": "action_local_jump", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_QR_CODE;end"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360043907834"}]}, {"bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "name": "选择位置", "identery": "add_device_select_location", "action": "add_device_select_location", "isNative": true, "actions": [{"identery": "select_wifi_page", "type": "push", "to": "add_device_select_wifi"}, {"identery": "finishAction", "type": "pop"}, {"identery": "joinDeviceAction", "type": "push"}, {"identery": "add_device_select_location", "actionType": "action_local_jump", "actionJumpTo": "add_device_select_wifi", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_SET_WIFI;S.yoga_key_dynamic_page=add_device_select_wifi;end"}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "bold": true, "textLines": 0, "title": {"cn": "连接电源以开机", "en": "Plug in to power up", "ja": "電源に接続して起動します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到提示音之后，即可进行下一步", "en": "When you hear the sound, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_charge_b1.gif", "ja": "https://vicoo.tech/images/bind/add_device_charge_b1.gif", "en": "https://vicoo.tech/images/bind/add_device_charge_b1.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360043907234", "action": "action_zendesk_jump"}, {"identery": "nextAction", "textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "摄像机已完成转动", "en": "Camera finished rotating.", "ja": "カメラの回転が完了しました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_reset"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "2/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_setup_b1", "actions": [{"identery": "push_to_reset", "type": "push", "to": "add_device_power_reset_b1", "actionType": "action_dynamic_jump", "actionJumpTo": "add_device_power_reset_b1"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360043907234"}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "长按Reset键进入扫描", "en": "Long press reset button to scan", "ja": "Resetキーを長押ししてスキャンします"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到扫描二维码声音之后，即可进行下一步", "en": "When you hear scan QR code sound, you can proceed to the next step.", "ja": "QRコードスキャン音が聞こえたら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_reset_s0.gif", "ja": "https://vicoo.tech/images/bind/add_device_reset_s0.gif", "en": "https://vicoo.tech/images/bind/add_device_reset_s0.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "找不到Reset键？", "en": "Cannot find reset button?", "ja": "Resetキーが見つかりませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360044504393", "action": "action_zendesk_jump"}, {"textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到声音", "en": "I heard  the sound.", "ja": "音声が聞こえました"}, "identery": "listen_voice", "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_qr_code"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "3/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_reset_s0", "actions": [{"identery": "pushAddDevice", "type": "push"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "push_to_qr_code", "actionType": "action_local_jump", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_QR_CODE;end"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360044504393"}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "连接电源以开机", "en": "Plug in to power up", "ja": "電源に接続して起動します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "摄像机完成转动之后，即可进行下一步", "en": "After the camera finishes rotating, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_charge_b1.gif", "ja": "https://vicoo.tech/images/bind/add_device_charge_b1.gif", "en": "https://vicoo.tech/images/bind/add_device_charge_b1.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360043907234", "action": "action_zendesk_jump"}, {"identery": "nextAction", "textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "摄像机已完成转动", "en": "Camera finished rotating.", "ja": "カメラの回転が完了しました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_reset"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "2/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_setup_b2", "actions": [{"identery": "push_to_reset", "type": "push", "to": "add_device_power_reset_b2", "actionType": "action_dynamic_jump", "actionJumpTo": "add_device_power_reset_b2"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360043907234"}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "长按Reset键进入扫描", "en": "Long press reset button to scan", "ja": "Resetキーを長押ししてスキャンします"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到提示音之后，即可进行下一步", "en": "When you hear the sound, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_reset_b1.gif", "ja": "https://vicoo.tech/images/bind/add_device_reset_b1.gif", "en": "https://vicoo.tech/images/bind/add_device_reset_b1.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "找不到Reset键？", "en": "Cannot find reset button?", "ja": "Resetキーが見つかりませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360043907834", "action": "action_zendesk_jump"}, {"textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到声音", "en": "I heard  the sound.", "ja": "音声が聞こえました"}, "identery": "listen_voice", "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_qr_code"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "3/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_reset_b1", "actions": [{"identery": "pushAddDevice", "type": "push"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "push_to_qr_code", "actionType": "action_local_jump", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_QR_CODE;end"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360043907834"}]}, {"subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "bold": true, "title": {"cn": "连接电源以开机", "en": "Plug in to power up", "ja": "電源に接続して起動します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 8, "title": {"cn": "当您听到开机声音之后，即可进行下一步", "en": "When you hear power up sound, you can proceed to the next step.", "ja": "起動が聞こえたら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_charge_s0.gif", "ja": "https://vicoo.tech/images/bind/add_device_charge_s0.gif", "en": "https://vicoo.tech/images/bind/add_device_charge_s0.gif"}, "image": {"cn": "add_device_cg0"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360044282374", "action": "action_zendesk_jump"}, {"identery": "nextAction", "textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已连通电源", "ja": "I plugged in the camera.", "en": "電源に接続されています"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_reset"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "2/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_setup_s0", "actions": [{"identery": "push_to_reset", "actionType": "action_dynamic_jump", "actionJumpTo": "add_device_power_reset_s0", "type": "push", "to": "add_device_power_reset_s0"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360044282374"}], "background_color": "#ffffff"}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "长按Reset键进入扫描", "en": "Long press reset button to scan", "ja": "Resetキーを長押ししてスキャンします"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到提示音之后，即可进行下一步", "en": "When you hear the sound, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_reset_b0.gif", "ja": "https://vicoo.tech/images/bind/add_device_reset_b0.gif", "en": "https://vicoo.tech/images/bind/add_device_reset_b0.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "找不到Reset键？", "en": "Cannot find reset button?", "ja": "Resetキーが見つかりませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360043907834", "action": "action_zendesk_jump"}, {"textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到声音", "en": "I heard  the sound.", "ja": "音声が聞こえました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "identery": "listen_voice", "action": "push_to_qr_code"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "3/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_reset_b0", "actions": [{"identery": "pushAddDevice", "type": "push"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "push_to_qr_code", "actionType": "action_local_jump", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_QR_CODE;end"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360043907834"}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "连接电源以开机", "en": "Plug in to power up", "ja": "電源に接続して起動します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "摄像机完成转动之后，即可进行下一步", "en": "After the camera finishes rotating, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_reset_g0.gif", "ja": "https://vicoo.tech/images/bind/add_device_reset_g0.gif", "en": "https://vicoo.tech/images/bind/add_device_reset_g0.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360041953993", "action": "action_zendesk_jump"}, {"textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "摄像机已完成转动", "en": "Camera finished rotating.", "ja": "カメラの回転が完了しました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "identery": "listen_voice", "action": "push_to_qr_code"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "2/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_reset_g0", "actions": [{"identery": "pushAddDevice", "type": "push"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "push_to_qr_code", "actionType": "action_local_jump", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_QR_CODE;end"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360041953993"}]}, {"bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "action": "add_device_select_wifi", "showSkip": "1", "showBack": "0"}, "name": "选择位置", "identery": "add_device_select_wifi", "action": "add_device_select_wifi", "isNative": true, "actions": [{"identery": "push_adddevice_power_up", "type": "push", "to": "add_device_power_setup_g0", "failAction": "push_adddevice_power_up_g1", "conditions": {"firstKey": "deviceType", "condition": "==", "secoundValue": "CG0"}}, {"identery": "push_adddevice_power_up_g1", "type": "push", "to": "add_device_power_setup_g1", "failAction": "push_adddevice_power_up_g2", "conditions": {"firstKey": "deviceType", "condition": "==", "secoundValue": "CG1"}}, {"identery": "push_adddevice_power_up_g2", "type": "push", "to": "add_device_power_setup_g2", "failAction": "push_adddevice_power_up_b0", "conditions": {"firstKey": "deviceType", "condition": "==", "secoundValue": "CG2"}}, {"identery": "push_adddevice_power_up_b0", "type": "push", "to": "add_device_power_setup_b0", "failAction": "push_adddevice_power_up_b1", "conditions": {"firstKey": "deviceType", "condition": "==", "secoundValue": "CB0"}}, {"identery": "push_adddevice_power_up_b1", "type": "push", "to": "add_device_power_setup_b1", "failAction": "push_adddevice_power_up_b2", "conditions": {"firstKey": "deviceType", "condition": "==", "secoundValue": "CB1"}}, {"identery": "push_adddevice_power_up_b2", "type": "push", "to": "add_device_power_setup_b2", "failAction": "push_adddevice_power_up_k0", "conditions": {"firstKey": "deviceType", "condition": "==", "secoundValue": "CB2"}}, {"identery": "push_adddevice_power_up_k0", "type": "push", "to": "add_device_power_setup_k0", "failAction": "push_adddevice_power_up_s0", "conditions": {"firstKey": "deviceType", "condition": "==", "secoundValue": "CK0"}}, {"identery": "push_adddevice_power_up_s0", "type": "push", "to": "add_device_power_setup_s0", "conditions": {"firstKey": "deviceType", "condition": "==", "secoundValue": "CS0"}}, {"identery": "finishAction", "type": "finish"}, {"identery": "joinDeviceAction", "type": "push"}, {"identery": "finishAction", "type": "pop"}, {"identery": "add_device_select_wifi", "actionType": "action_dynamic_jump", "actionJumpToByProp": {"deviceType": {"G0": "add_device_power_setup_g0", "CG0": "add_device_power_setup_g0", "CG1": "add_device_power_setup_g1", "CG2": "add_device_power_setup_g2", "CB0": "add_device_power_setup_b0", "CB1": "add_device_power_setup_b1", "CK0": "add_device_power_setup_k0", "CS0": "add_device_power_setup_s0"}}}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "是否听到“扫描二维码”", "en": "Did you hear \"Scan QR code\"?", "ja": "「QRコードをスキャン」と聞きましたか"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "justifyContent": "center", "percentWidth": 100, "flexDirection": "column", "subviews": [{"identery": "add_device_have_heard_qr", "textAlignment": "center", "percentWidth": 100, "minWidth": 46, "textFont": 16, "textLines": 0, "radius": 23, "title": {"cn": "已听到“扫描二维码”", "en": "I heard \"Scan QR code\".", "ja": "「QRコードをスキャン」と聞きました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 16, "action": "push_to_qr_code", "padding": 10}, {"identery": "add_device_have_not_heard_qr", "textAlignment": "center", "percentWidth": 100, "minWidth": 46, "textFont": 16, "textLines": 0, "radius": 23, "title": {"cn": "未听到“扫描二维码”", "en": "I did not hear \"Scan QR code\".", "ja": "「QRコードをスキャン」と聞こえなかった"}, "action": "push_to_reset", "type": "TextButton", "textColor": "#333333", "background_color": "#F5F6FA", "padding": 10}]}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "3/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_check_g0", "actions": [{"identery": "pushAddDevice", "type": "push"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "push_to_reset", "actionType": "action_dynamic_jump", "actionJumpTo": "add_device_power_reset_g0", "to": "add_device_power_reset_g0", "type": "push"}, {"identery": "push_to_qr_code", "actionType": "action_local_jump", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_QR_CODE;end"}]}, {"subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "bold": true, "title": {"cn": "连接电源以开机", "en": "Plug in to power up", "ja": "電源に接続して起動します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 8, "title": {"cn": "当您听到提示音之后，即可进行下一步", "en": "When you hear the sound, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_charge_k0.gif", "ja": "https://vicoo.tech/images/bind/add_device_charge_k0.gif", "en": "https://vicoo.tech/images/bind/add_device_charge_k0.gif"}, "image": {"cn": "add_device_cg0"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360043907234", "action": "action_zendesk_jump"}, {"identery": "nextAction", "textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到声音", "en": "I heard  the sound.", "ja": "音声が聞こえました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_reset"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "2/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_setup_k0", "actions": [{"identery": "push_to_reset", "type": "push", "to": "add_device_power_reset_k0", "actionType": "action_dynamic_jump", "actionJumpTo": "add_device_power_reset_k0"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360043907234"}], "background_color": "#ffffff"}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "长按电源键开机", "en": "<PERSON> press the button to power up", "ja": "電源ボタンを長押しして起動します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到提示音之后，即可进行下一步", "en": "When you hear the sound, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_charge_g2.gif", "ja": "https://vicoo.tech/images/bind/add_device_charge_g2.gif", "en": "https://vicoo.tech/images/bind/add_device_charge_g2.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360041953993", "action": "action_zendesk_jump"}, {"identery": "nextAction", "textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到声音", "en": "I heard  the sound.", "ja": "音声が聞こえました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "action": "push_to_reset"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName", "showSkip": "1", "showBack": "0"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "2/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_setup_g2", "actions": [{"identery": "push_to_reset", "type": "push", "actionType": "action_dynamic_jump", "actionJumpTo": "add_device_power_reset_g2", "to": "add_device_power_reset_g2"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360041953993"}]}, {"background_color": "#ffffff", "subview": {"paddingRight": 29, "flexGrow": 1, "alignItems": "center", "subviews": [{"textAlignment": "center", "textFont": 19, "marginTop": 8, "textLines": 0, "bold": true, "title": {"cn": "双击电源键进入扫描", "en": "Double click the button to scan", "ja": "電源ボタンをダブルクリックしてスキャンを開始します"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#000000"}, {"textAlignment": "center", "textFont": 12, "marginTop": 13, "textLines": 0, "title": {"cn": "当您听到提示音之后，即可进行下一步", "en": "When you hear the sound, you can proceed to the next step.", "ja": "カメラの回転が完了したら、次のステップに進むことができます"}, "paddingLeft": 20, "paddingRight": 20, "type": "Label", "textColor": "#FFB2B2B2"}, {"marginTop": 10, "marginBottom": 10, "flexGrow": 1, "contentMode": "scaleAspectFit", "image_url": {"cn": "https://vicoo.tech/images/bind/add_device_reset_g1.gif", "ja": "https://vicoo.tech/images/bind/add_device_reset_g1.gif", "en": "https://vicoo.tech/images/bind/add_device_reset_g1.gif"}, "percentWidth": 100, "type": "ImageView"}, {"textFont": 16, "hidden": true, "title": {"cn": "无法开机？", "en": "Cannot power up?", "ja": "起動することができませんか？"}, "type": "TextButton", "textColor": "#999999", "marginBottom": 20, "updateKey": "common_zendesk_id", "identery": "common_zendesk_id", "value": "360041953993", "action": "action_zendesk_jump"}, {"textAlignment": "center", "percentWidth": 100, "height": 46, "textFont": 16, "radius": 8, "title": {"cn": "已听到声音", "en": "I heard  the sound.", "ja": "音声が聞こえました"}, "type": "TextButton", "textColor": "#FFFFFF", "background_color": "#FF58C4A7", "marginBottom": 35, "identery": "listen_voice", "action": "push_to_qr_code"}], "flexDirection": "column", "justifyContent": "spaceBetween", "paddingLeft": 29, "flexWrap": "wrap"}, "bindData": {"deviceId": "modle&deviceid", "inputName": "view&inputnameV&text", "deviceName": "modle&deviceName"}, "flexDirection": "column", "name": "添加设备", "navtion": {"flexDirection": "row", "marginTop": 0, "subviews": [{"action": "finishAction", "textAlignment": "center", "textFont": 14, "width": 60, "image": {"en": "icon_back_gray", "ja": "icon_back_gray", "cn": "icon_back_gray"}, "identery": "finishActionButton", "type": "ImageButton", "padding": 5, "textColor": "#ff000000"}, {"type": "Label", "padding": 5, "flexGrow": 1, "textAlignment": "center", "textColor": "#ffff0000", "textFont": 14}, {"type": "TextButton", "padding": 5, "title": {"cn": "3/4"}, "marginRight": 15, "percentHeight": 100, "textColor": "#FF58C4A7", "textFont": 14}], "height": 56}, "desc": "添加设备页面", "identery": "add_device_power_reset_g1", "actions": [{"identery": "pushAddDevice", "type": "push"}, {"identery": "finishAction", "type": "finish"}, {"identery": "finishAction", "type": "pop"}, {"identery": "push_to_qr_code", "actionType": "action_local_jump", "actionPageIntent": "intent:#Intent;action=action.ADD_DEVICE_QR_CODE;end"}, {"identery": "action_zendesk_jump", "type": "action_zendesk_jump", "actionType": "action_zendesk_jump", "to": "action_zendesk_jump", "data": ["common_zendesk_id"], "actionJumpTo": "360041953993"}]}]