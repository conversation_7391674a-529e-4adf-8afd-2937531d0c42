spring:
  datasource:
    dynamic:
      primary: camera
      datasource:
        camera:
          url: '*********************************************************************************************************************************************************'
          username: testUser
          password: testUser@2019
          driver-class-name: com.mysql.jdbc.Driver
          hikari:
            minIdle: 0
        camera-readonly:
          url: '*********************************************************************************************************************************************************'
          username: testUser
          password: testUser@2019
          driver-class-name: com.mysql.jdbc.Driver
          hikari:
            minIdle: 0
        factory:
          url: '*******************************************************************************************************************************************************************************************************************'
          username: testUser
          password: testUser@2019
          driver-class-name: com.mysql.jdbc.Driver
          hikari:
            minIdle: 0

  kafka:
    bootstrap-servers: 10.0.3.2:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      properties:
        partitioner.class: com.addx.iotcamera.mq.MqProducePartitioner
    consumer:
      group-id: iot-service
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: true
      auto-commit-interval: 1000
      max-poll-records: 50
    topics:
      ai-video-segment: test_video_slice
      video-generate: test-cn-video-generate
      video-generate-by-user-id: test-cn-video-generate-by-user-id
      saas-ai-task: test-cn-saas-ai-task
      saas-ai-task-iot-result: test-cn-saas-ai-task-iot-result
      saas-ai-task-result: test-cn-saas-ai-task-result
      statemachine: cn-test-statemachine
      heartbeat-connected: cn-test-heartbeat-connected
      call-webhook: test-cn-call-webhook
      zendesk: cn-test_zendesk
      reid-data: test-cn-reid-data
      reid-update: test-cn-reid-update
      device-platform: cn-test_device_platform
      emailSend: cn-test-send-email
      device-wakeup: cn-test-device-wakeup
      video-library: cn-test-video-library
      user-tier-device: cn-test-user-tier-device
      user-tier-device-xxl: cn-test-user-tier-device-xxl
      device-sim: cn-test_device-sim
      sim-change: cn-test-sim-change
      tier-expire-notify: cn-test-tier-expire-notify
      user-ios-sub-order: cn-test-user-ios-sub-order
      exchange-code-mark: cn-test-exchange-code-mark
redisconfig:
  businessRedis:
    database: 0
    host: *********
    port: 6379
    #    password:
  businessReadOnlyRedis:
    database: 0
    host: *********
    port: 6379
  eventRedis:
    database: 1
    host: *********
    port: 6379
  businessRedis2:
    database: 0
    host: *********
    port: 6379
  businessRedisCluster:
    host: *********
    port: 6379
    maxRedirect: 6
    metaCommandTimeout: 1000
    defaultCommandTimeout: 250
    connectTimeout: 100
    shutdownTimeout: 10000
    #    password:
  maxActive: 8
  maxIdle: 2
  minIdle: 0
  maxWait: 3000
  timeout: 30000

mqtt:
  serverUri: tcp://vernemq-test-internal.addx.live:1883
  clientUri: tcp://vmq-test.addx.live:1883
#  ip: '0.0.0.0'

app:
  id: 2
apollo:
  meta: http://*********:9080
  bootstrap:
    enabled: true
    namespaces: application,firmware,app,dns,devicereport,template

wowza:
  elb: ''

s3config:
  bucket: 'addx-test'
  clientRegion: 'cn-north-1'
  s3AccessKey: '********************'
  s3SecretKey: 'SXycRbyidSaPSVuL3ORry98tKCx2dwXlcRo8lBlr'
  serverS3AccelerateEnable: false

s3:
  bucket: 'addx-test'
  clientRegion: 'cn-north-1'
  accessKey: '********************'
  secretKey: 'SXycRbyidSaPSVuL3ORry98tKCx2dwXlcRo8lBlr'
  accelerateEnable: false
  # 每个回看天数单独分配一个bucket
  defaultBucket: 'addx-test-vip-none'
  lookBackDays2Bucket:
    7:
      - bucket: 'addx-test-vip-none'
        region: 'cn-north-1'
    15:
      - bucket: 'addx-test-vip-basic'
        region: 'cn-north-1'
    30:
      - bucket: 'addx-test-vip-plus'
        region: 'cn-north-1'
    60:
      - bucket: 'addx-test-vip-pro'
        region: 'cn-north-1'

sts:
  clientRegion: 'cn-north-1'
  accessKey: '********************'
  secretKey: 'SXycRbyidSaPSVuL3ORry98tKCx2dwXlcRo8lBlr'

dynamodb:
  clientRegion: 'cn-north-1'
  accessKey: '********************'
  secretKey: 'SXycRbyidSaPSVuL3ORry98tKCx2dwXlcRo8lBlr'

cloudfront:
  config:
    firmware:
      distributionDomain: firmware-cn.addx.live
      privateKeyFilePath: classpath://secret-test/cloudfront_private_key.pem
      keyPairId: K3K6SFP4RMB77G
      expireTime: 600000
      sources:
        - bucket: addx-firmware-cn

zk:
  connect-string: *********:2181,*********:2181,********:2181
  kiss-node-path: /kiss/node/test
  kiss-safertc-node-path: /kiss-safertc/node/test
  coturn-node-path: /coturn/node/test

codebind:
  node: test
  node-matcher:
    endpoint: 'https://node-matcher-test.addx.live'
    accessKey: '2vi9ZxRSW9J2xkZBrBO8H1'
    secretKey: 'TNzniwrZSW2j+TzwU23Piw=='

servernode: CN
nodeEvn: test
release:
  evn: test

videoslice:
  rootPath: https://api-test.addx.live
  roleArn: arn:aws-cn:iam::437416304740:role/test_bucket_access_role
  durationSeconds: 43200
  awsCertEndpoint: sns.cn-north-1.amazonaws.com.cn
  deviceS3AccelerateEnable: false

momoapi:
  config:
    askari:
      apiUser: d10f4ad1-5914-41ed-99a7-64f2a4a839ec
      apiKey: 634402bb9a594817b4aebf704b9c3977
      subscriptionKey: f19c33e753cd4753a2e1276ca3b20875
      targetEnvironment: sandbox
      baseUrl: https://sandbox.momodeveloper.mtn.com
      callbackUrl: https://api-test.addx.live/payNotify/momoapi/notify
      currency: EUR

dynamo:
  videoSlice:
    tableName: test_cn_videoSlice2
    #  自动删除前存活秒数:3600*24*62
    survivalSeconds: 5356800
    oldTableName: test_cn_videoSlice
  videoSliceComplete:
    tableName: test_cn_videoCompleteReport2
    #  自动删除前存活秒数:3600*24*62
    survivalSeconds: 5356800
  videoLibrary:
    tableName: test_cn_video_library
    #  自动删除前存活秒数:3600*24*62
    survivalSeconds: 5356800
  videoEvent:
    tableName: test_cn_video_event
    #  自动删除前存活秒数:3600*24*62
    survivalSeconds: 5356800
  tenantAwsConfig:
    tableName: test_cn_tenantAwsConfig
  deviceConfig:
    tableName: test_cn_deviceConfig
  zendeskTicket:
    tableName: test_cn_zendesk_ticket
  userReId:
    tableName: test_cn_userReId
  paasAccessLog:
    tableName: test_cn_paasAccessLog
    #  自动删除前存活秒数:3600*24*62
    survivalSeconds: 5356800
  deviceAiChange:
    tableName: test_cn_deviceAiChange
    #  自动删除前存活秒数:3600*24*62
    survivalSeconds: 5356800

countly:
  app-type-to-tenant-id-to-params:
    iOS:
      default:
        countlyServer: https://countly.vicohome.io
        countlyKey: bf83767885e01ae5f1e09a2b2a270d4c02f2ce27
    Android:
      default:
        countlyServer: https://countly.vicohome.io
        countlyKey: 11af45a88650e97c9990ee81e3c7080708940d8e

open-api:
  node: test-cn
  auth-url:
    # test-cn环境连staging-cn的auth
    validate-request: http://81.70.126.101/auth/validate-request

server-node:
  root-url:
    CN: https://api-test.addx.live
    EU: https://api-test-eu.addx.live
    US: https://api-test-us.addx.live

zendesk:
  userconfig:
    vicoo:
      zone:
      jwtsecret:
      url: https://1117336245552676.us-east-1.fc.aliyuncs.com/2016-08-15/proxy/zendesk/zendesk_api_proxy
      username: <EMAIL>
      token: krHQMo2TJxsnKFKzl8Bk3b6AeGwMWEfvcNwGXh2x
    safemo:
      zone:
      jwtsecret:
      url: https://1117336245552676.us-east-1.fc.aliyuncs.com/2016-08-15/proxy/zendesk/zendesk_api_proxy
      username: <EMAIL>
      token: krHQMo2TJxsnKFKzl8Bk3b6AeGwMWEfvcNwGXh2x
    kiwibit:
      zone:
      jwtsecret:
      url: https://1117336245552676.us-east-1.fc.aliyuncs.com/2016-08-15/proxy/zendesk/zendesk_api_proxy
      username: <EMAIL>
      token: krHQMo2TJxsnKFKzl8Bk3b6AeGwMWEfvcNwGXh2x
  tenantId2requester:
    map: "{\"vicoo\": \"<EMAIL>\", \"dzees\":\"<EMAIL>\"}"
  sdkconfig:
    vicoo:
      CN:
        appId: 2dda898af493b68ba17aa1feb302d93a22ce3c67bf053d6f
        clientId: mobile_sdk_client_d7b9ce4f447bb57990ab
        host: "https://support.vicoo.tech"
      US:
        appId: 2dda898af493b68ba17aa1feb302d93a22ce3c67bf053d6f
        clientId: mobile_sdk_client_d7b9ce4f447bb57990ab
        host: "https://support.vicoo.tech"
      EU:
        appId: 2dda898af493b68ba17aa1feb302d93a22ce3c67bf053d6f
        clientId: mobile_sdk_client_d7b9ce4f447bb57990ab
        host: "https://support.vicoo.tech"
    safemo:
      CN:
        appId: d71306066e7cf5a81b065d088e2469d7f2e770b60e40c0fe
        clientId: mobile_sdk_client_0a532383172e206ffdd2
        host: "https://safemo.zendesk.com"
      US:
        appId: d71306066e7cf5a81b065d088e2469d7f2e770b60e40c0fe
        clientId: mobile_sdk_client_0a532383172e206ffdd2
        host: "https://safemo.zendesk.com"
      EU:
        appId: d71306066e7cf5a81b065d088e2469d7f2e770b60e40c0fe
        clientId: mobile_sdk_client_0a532383172e206ffdd2
        host: "https://safemo.zendesk.com"
    kiwibit:
      CN:
        appId: d71306066e7cf5a81b065d088e2469d7f2e770b60e40c0fe
        clientId: mobile_sdk_client_0a532383172e206ffdd2
        host: "https://kiwibit.zendesk.com"
      US:
        appId: d71306066e7cf5a81b065d088e2469d7f2e770b60e40c0fe
        clientId: mobile_sdk_client_0a532383172e206ffdd2
        host: "https://kiwibit.zendesk.com"
      EU:
        appId: d71306066e7cf5a81b065d088e2469d7f2e770b60e40c0fe
        clientId: mobile_sdk_client_0a532383172e206ffdd2
        host: "https://kiwibit.zendesk.com"
ai-task:
  timeout: 90000
  outStorage:
    serviceName: 'S3'
    bucket: 'addx-test'
    clientRegion: 'cn-north-1'
    keyPrefix: 'ai-saas-out-storage'
    expiredSeconds: 1800
    expiredConfig:
      objectImage: 86400
      eventCoverImage: 5184000
      eventSummary: 86400
  sendOldTaskPercent: 100
  sendSaasTaskPercent: 100
  useSaasResult: true

elastic-search:
  endpoints:
    - 'http://*********:9200'
  splitByDayIndexes: ['paas']
  indexes:
    paas: 'access-log-cn-staging-paas'
    birdName: 'bird_name'
    netTestCmd: 'net_test_cmd'
    netTestResult: 'net_test_result'

spring.shardingsphere.datasource:
  names: video-library-db-0,video-library-db-1,video-library-db-2,video-library-db-3,video-library-db-4,video-library-db-5,video-library-db-6,video-library-db-7,video-library-db-0-readonly,video-library-db-1-readonly,video-library-db-2-readonly,video-library-db-3-readonly,video-library-db-4-readonly,video-library-db-5-readonly,video-library-db-6-readonly,video-library-db-7-readonly
  video-library-db-0:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************&rewriteBatchedStatements=true&allowMultiQueries=true
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-1:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************&rewriteBatchedStatements=true&allowMultiQueries=true
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-2:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************&rewriteBatchedStatements=true&allowMultiQueries=true
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-3:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************&rewriteBatchedStatements=true&allowMultiQueries=true
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-4:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************&rewriteBatchedStatements=true&allowMultiQueries=true
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-5:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************&rewriteBatchedStatements=true&allowMultiQueries=true
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-6:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************&rewriteBatchedStatements=true&allowMultiQueries=true
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-7:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************&rewriteBatchedStatements=true&allowMultiQueries=true
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-0-readonly:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-1-readonly:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-2-readonly:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-3-readonly:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-4-readonly:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-5-readonly:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-6-readonly:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource
  video-library-db-7-readonly:
    driver-class-name: com.mysql.jdbc.Driver
    jdbc-url: ************************************************************************************************************************************************************************************************************************
    username: testUser
    password: testUser@2019
    minimumIdle: 5
    maximumPoolSize: 10
    connectionTimeout: 30000
    idleTimeout: 60000
    autoCommit: true
    leakDetectionThreshold: 60000
    data-source-properties:
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      cachePrepStmts: false
      useServerPrepStmts: false
      elideSetAutoCommits: true
    type: com.zaxxer.hikari.HikariDataSource

google-storage:
  enable: false
  serviceAccounts:
    storage: 'google/a4xpaas-000001-7cac2ea0258c.json'
    createToken: 'google/a4xpaas-000001-7cac2ea0258c.json'
  #    createToken: 'google/a4xpaas-000001-259859bd6400.json'
  bucket: 'test-a4x'
  # 每个回看天数单独分配一个bucket
  defaultBucket: ''
  lookBackDays2Bucket:
    3: ''
    7: ''
  whiteSns:
  whiteUserIdSet:
  whiteUserPercent: 0

iosPayNotifyRedirectUrl: "https://api-test.addx.live"

# 腾讯cos云存储配置
cos:
  params:
    secretId: "AKIDIMsLgBOPX5klW5QHcFbjbCrd9SUHrM4W"
    secretKey: "PwEF0VrqmUgdMi2A8xb3GDtfBNNFzWnn"
    region: "ap-beijing"
    storageClass: "STANDARD"
  bucket: 'addx-test-**********'
  lookBackDays2Bucket:
    3: 'addx-test-**********'
    7: 'test-vip-7d-**********'
  domainPattern: '%s.cos.ap-beijing.tencentcos.cn'
  enable: true

#甲骨文oci云存储配置
oci:
  accessKey: 'c93cd229cb8862c8332849277e6e70435c8e7873'
  secretKey: '1xHsZl3C3mdp5dpX/Y8Co7olP8kIkU+znEel0ijUgas='
  params:
    user: 'ocid1.user.oc1..aaaaaaaauly5p6thgk5bhcga4imzzckftbzsfonshlzubgcfct5wfxvs7nxa'
    fingerprint: '07:80:0c:a7:2a:30:81:2c:25:a8:6f:97:01:dd:0c:e8'
    tenancy: 'ocid1.tenancy.oc1..aaaaaaaarbccm4fvewf5vxteawrjg5ui2yap2nws3x2jsgty6l5hiuxqygka'
    region: 'us-ashburn-1'
    key_file: 'classpath:oci/for-us-staging-buckets_2023-02-23T02_06_44.897Z.pem'
  bucket: 'staging-us-vip-7d'
  lookBackDays2Buckets:
    3:
      - bucket: 'staging-us-vip-3d'
        region: 'us-ashburn-1'
        minRatio: 0
        maxRatio: 100
      - bucket: 'staging-us-vip-3d-us-chicago-1'
        region: 'us-chicago-1'
        minRatio: 100
        maxRatio: 100
      - bucket: 'staging-us-vip-3d-us-phoenix-1'
        region: 'us-phoenix-1'
        minRatio: 100
        maxRatio: 100
      - bucket: 'staging-us-vip-3d-us-sanjose-1'
        region: 'us-sanjose-1'
        minRatio: 100
        maxRatio: 100

    7:
      - bucket: 'staging-us-vip-7d'
        region: 'us-ashburn-1'
        minRatio: 0
        maxRatio: 100
      - bucket: 'staging-us-vip-7d-us-chicago-1'
        region: 'us-chicago-1'
        minRatio: 100
        maxRatio: 100
      - bucket: 'staging-us-vip-7d-us-phoenix-1'
        region: 'us-phoenix-1'
        minRatio: 100
        maxRatio: 100
      - bucket: 'staging-us-vip-7d-us-sanjose-1'
        region: 'us-sanjose-1'
        minRatio: 100
        maxRatio: 100
  enable: true

# kx日志bucket
kxLog:
  bucket: ""

#云存储分配比例
storage-allocate:
  vipRatios: # vip用户/设备中，各云存储的分配比例
    - { "min": 0, "max": 100, "serviceNames": [ "cos", "s3" ] }
    - { "min": 100, "max": 100, "serviceNames": [ "s3" ] }
  noVipRatios: # 非vip用户/设备中，各云存储的分配比例
    - { "min": 0, "max": 100, "serviceNames": [ "cos", "s3" ] }
  whiteUserIds: # 用户id白名单，白名单先于比例生效
    s3: [ ]
    gcs: [ ]
    cos: [ ]
    oci: [ ]
  saasAiSupportServiceNames: [ "s3" ]

# iot-service连接kiss的websocket
kiss-websocket:
  port: 18888

app-files-s3-config:
  bucket-prefix: app-files-cn
  clientRegion: cn-north-1
  s3AccessKey: ********************
  s3SecretKey: 833G6wNTbWTY6xzRXZALK1kQfU8XAyH//+Qprxvu

safe-push:
  enable: false
  whiteUserIds: ''
  rootUrl: ''
  secret: 'QYBa868oZ+bT2uwwWJiQV9ss3nYmeh+U/aH268HwEQE='
tracker-url: 'https://us-tracker-management.theunismart.com'
tracker-token:
  secret: 'LCaTglVU+DPuZJJNq4At8hqvhnetL9io3Y/pKIRsFy4='

device:
  iot:
    sim:
      config:
        domain: "https://sim-staging-us.vicohome.io"

marketing-service:
  domain: "https://marketing-staging-us.vicohome.io"

bird-tab:
  image-store-bucket: 'staging-cn-**********'
  enable-tenants: 'vicoo'

exchange:
  code:
    mark:
      url: https://revenus-sharing-backend-test.addx.live/xxl/markCode
      max-retries: 3
      retry-delay: 3000 # 重试间隔3秒