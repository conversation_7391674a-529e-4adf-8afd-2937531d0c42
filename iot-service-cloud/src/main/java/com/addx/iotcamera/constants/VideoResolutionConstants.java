package com.addx.iotcamera.constants;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;

import java.util.Map;
import java.util.Set;

/**
 * 视频分辨率相关常量
 * 
 * <AUTHOR>
 * @date 2024-12-XX
 */
public class VideoResolutionConstants {
    
    // 分辨率常量
    public static final String RESOLUTION_4K = "3840x2160";
    public static final String RESOLUTION_1080P = "1920x1080";
    public static final String RESOLUTION_720P = "1280x720";
    public static final String RESOLUTION_480P = "640x480";
    public static final String RESOLUTION_360P = "640x360";
    
    // 分辨率显示名称映射
    public static final Map<String, String> RESOLUTION_DISPLAY_NAMES = ImmutableMap.<String, String>builder()
            .put(RESOLUTION_4K, "4K")
            .put(RESOLUTION_1080P, "1080P")
            .put(RESOLUTION_720P, "720P")
            .put(RESOLUTION_480P, "480P")
            .put(RESOLUTION_360P, "360P")
            .build();
    
    // SS121支持的分辨率
    public static final Set<String> SS121_SUPPORTED_RESOLUTIONS = ImmutableSet.of(
            RESOLUTION_4K, RESOLUTION_720P
    );
    
    // 默认分辨率（4K优先）
    public static final String DEFAULT_RESOLUTION = RESOLUTION_4K;
    
    // 附属分辨率（720P）
    public static final String SECONDARY_RESOLUTION = RESOLUTION_720P;
    
    /**
     * 获取分辨率显示名称
     * @param resolution 分辨率值
     * @return 显示名称
     */
    public static String getDisplayName(String resolution) {
        return RESOLUTION_DISPLAY_NAMES.getOrDefault(resolution, resolution);
    }
    
    /**
     * 判断是否为4K分辨率
     * @param resolution 分辨率值
     * @return 是否为4K
     */
    public static boolean is4K(String resolution) {
        return RESOLUTION_4K.equals(resolution);
    }
    
    /**
     * 判断是否为720P分辨率
     * @param resolution 分辨率值
     * @return 是否为720P
     */
    public static boolean is720P(String resolution) {
        return RESOLUTION_720P.equals(resolution);
    }

}
