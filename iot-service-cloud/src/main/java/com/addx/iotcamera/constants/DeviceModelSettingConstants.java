package com.addx.iotcamera.constants;

import com.addx.iotcamera.enums.DoorbellPressNotifyType;

import java.util.Arrays;
import java.util.List;

public class DeviceModelSettingConstants {
    public final static String VOICE_VOLUME = "voiceVolume";
    public final static String ALARM_VOLUME = "alarmVolume";
    //扬声器音量
    public final static int VOICE_VOLUME_VALUE = 50;
    //警报音量
    public final static int ALARM_VOLUME_VALUE = 90;
    //运动检测开关-开
    public final static int NEED_MOTION_VALUE = 1;
    //运动检测灵敏度-中
    public final static int MOTION_SENSITIVITY_VALUE = 2;
    public final static int MOTION_SENSITIVITY_LOW_VALUE = 3;
    // 夜视模式-开
    public final static int NEED_NIGHT_VISION_VALUE = 1;
    //夜视模式-红外
    public final static int NIGHT_VISION_MODE_VALUE = 0;
    //夜视灵敏度-中
    public final static int NIGHT_VISION_SENSITIVITY_VALUE = 20;
    public final static int NIGHT_THRESHOLD_Level_VALUE = 2;
    //设备警铃-关
    public final static int NEED_ALARM_VALUE = 0;
    //警报时长
    public final static int ALARM_SECONDS_VALUE = 5;
    public final static String ALARM_DURATION_ENUM_VALUE = "5s";
    //录制视频开关-开
    public final static int NEED_VIDEO_VALUE = 1;
    //录制视频时长
    public final static int VIDEO_SECONDS_VALUE = 10;
    //运动追踪开关-关
    public final static int MOTION_TRACK_VALUE = 0;
    public final static int MOTION_TRACK_MODE_VALUE = 0;
    //录像指示灯-开启
    public final static int REC_LAMP_VALUE = 1;
    //音量开关-开
    public final static int VOICE_VOlUME_SWITCH_VALUE = 1;
    //哭声检测
    public final static int CRY_DETECT_VALUE = 0;
    public final static int CRY_DETECT_LEVEL_VALUE = 3;
    //设备人型检测，关
    public final static int DEVICE_PERSON_DETECT_VALUE = 0;
    //设备Log
    public final static int AUTO_LOG_VALUE = 0;
    //视频翻转
    public final static int MIRROR_FLIP_VALUE = 0;

    // 录制视频分辨率
    public final static String DEFAULT_VALUE_REC_RESOLUTION = "1280X720";
    // 直播收音默认值
    public static final boolean DEFAULT_VALUE_LIVE_AUDIO_TOGGLE_ON = true;
    // 录像直播收音默认值
    public static final boolean DEFAULT_VALUE_RECORDING_AUDIO_TOGGLE_ON = true;
    // 直播对讲音量默认值
    public static final int DEFAULT_VALUE_LIVE_SPEAKER_VOLUME = 100;
    // 是否打开拆除时报警开关默认值
    public static final String KEY_POWER_SOURCE = "powerSource";
    public static final String KEY_CHARGE_AUTO_POWER_ON_SWITCH = "chargeAutoPowerOnSwitch";
    public static final boolean DEFAULT_VALUE_ALARM_WHEN_REMOVE_TOGGLE_ON = false;
    public static final int DEFAULT_VALUE_MECHANICAL_DING_DONG_SWITCH = 1;
    public static final int DEFAULT_VALUE_MECHANICAL_DING_DONG_DURATION = 200; // 默认值：200ms
    public static final String DEFAULT_KEY_DEVICE_CALL_TOGGLE_ON = "chargeAutoPowerOnCapacity";
    public static final boolean DEFAULT_VALUE_DEVICE_CALL_TOGGLE_ON = true;

    // 1、允许充电自动开机开关，默认关，chargeAutoPowerOnSwitch
    public static final int DEFAULT_VALUE_CHARGE_AUTO_POWER_ON_SWITCH = 0;
    // 2、允许充电自动开机电量，默认25，chargeAutoPowerOnCapacity
    public static final int DEFAULT_VALUE_CHARGE_AUTO_POWER_ON_CAPACITY = 10;
    // 是否支持充电自动开机
    public static final int DEFAULT_VALUE_SUPPORT_CHARGE_AUTO_POWER_ON = 0;
    // 允许充电自动开机电量选项
//    public static final List<Integer> DEFAULT_VALUE_CHARGE_AUTO_POWER_ON_CAPACITY_OPTIONS = Arrays.asList(25, 50, 75, 100);
    public static final List<Integer> DEFAULT_VALUE_CHARGE_AUTO_POWER_ON_CAPACITY_OPTIONS = Arrays.asList();


    public static final String DEFAULT_KEY_OTA_AUTO_UPGRADE = "otaAutoUpgrade";

    public static final boolean DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_SWITCH = true;
    public static final String DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_TYPE = DoorbellPressNotifyType.phone.name();

    public static final String DEFAULT_KEY_WIFI_POWER_LEVEL = "wifiPowerLevel";
    public static final Integer DEFAULT_VALUE_WIFI_POWER_LEVEL = 0;

    public static final Integer DEFAULT_VALUE_SD_CARD_COOLDOWN_SWITCH = 0;

    //sd 卡拍摄模式
    public static final String DEFAULT_VALUE_SD_CARD_VIDEO_MODE = "eventual";
    public static final String DEFAULT_VALUE_SD_CARD_VIDEO_MODE_CONTINUAL = "continual";

    public static final String DEFAULT_VALUE_SD_CARD_COOLDOWN_TIME = "10s";

    public static final Integer DEFAULT_VALUE_MOTION_FLOOD_LIGHT_SWITCH = 0;

    public static final String DEFAULT_VALUE_FLOOD_LIGHT_MODE = "auto";

    public static final boolean DEFAULT_VALUE_SUPPORT_SNAPSHOT_RECORDING = false;
    public static final boolean DEFAULT_VALUE_SUPPORT_EVENT_RECORDING_DUALVIEW = false;
    public static final int DEFAULT_VALUE_DEVICE_DUAL_VIEW_TYPE = 0;

    public static final boolean DEFAULT_VALUE_SNAPSHOT_RECORDING_SWITCH = false;
    public static final String DEFAULT_VALUE_SNAPSHOT_CAPTURE_INTERVAL = "auto";
    public static final boolean DEFAULT_VALUE_EVENT_RECORDING_DUAL_VIEW_SWITCH = false;


}
