package com.addx.iotcamera.dynamo.util;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;

import java.lang.reflect.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 操作dynamoDB辅助类
 */
public class DynamoUtil {

    public static class ConvertConfig {
        private final LinkedHashSet<String> ignoreFieldNamePatterns = new LinkedHashSet<>();

        public ConvertConfig addIgnoreFieldNamePattern(String... patterns) {
            ignoreFieldNamePatterns.addAll(Arrays.asList(patterns));
            return this;
        }

        public boolean isIgnoreFieldName(String fieldPath) {
            String fieldName = fieldPath.substring(1);
            for (String ignoreFieldNamePattern : ignoreFieldNamePatterns) {
                if (fieldName.matches(ignoreFieldNamePattern)) return true;
            }
            return false;
        }
    }

    public static class UnconvertConfig {
        private final LinkedHashMap<String, Class> fieldNamePattern2Cls = new LinkedHashMap<>();

        public UnconvertConfig addFieldNamePattern2Cls(String fieldNamePattern, Class cls) {
            fieldNamePattern2Cls.put(fieldNamePattern, cls);
            return this;
        }

        public Class getFieldCls(String fieldName) {
            for (Map.Entry<String, Class> entry : fieldNamePattern2Cls.entrySet()) {
                if (fieldName.matches(entry.getKey())) return entry.getValue();
            }
            return null;
        }
    }

    public static AttributeValue convertAttributeValue(Object value, String... ignoreFieldNamePattern) {
        DynamoUtil.ConvertConfig convertConfig = new DynamoUtil.ConvertConfig()
                .addIgnoreFieldNamePattern(ignoreFieldNamePattern);
        return convertAttributeValue(value, "", convertConfig);
    }

    public static AttributeValue convertAttributeValue(Object value, ConvertConfig convertConfig) {
        return convertAttributeValue(value, "", convertConfig);
    }

    private static AttributeValue convertAttributeValue(Object value, String path, ConvertConfig convertConfig) {
        if (value == null) {
            return new AttributeValue().withNULL(true);
        } else if (value instanceof CharSequence) {
            return new AttributeValue().withS(value.toString());
        } else if (value instanceof Number) {
            return new AttributeValue().withN(value + "");
        } else if (value instanceof Boolean) {
            return new AttributeValue().withBOOL((Boolean) value);
        } else if (value instanceof Date) {
            return new AttributeValue().withS(convertDate((Date) value));
        }
//            throw new IllegalArgumentException("不支持的数据类型:value=" + value + ",type=" + value.getClass().getName());
//            Set<String> ignoreFieldNames = convertConfig.getIgnoreFieldNamesByPath(value.getClass());
        /* 处理嵌套结构 */
        String collectionType = (value instanceof Collection || value.getClass().isArray()) ? "L" : "M";
        Map<String, AttributeValue> fieldMap = new LinkedHashMap<>();
        BiConsumer<String, Supplier> loopHandler = (fieldName, fieldValueGetter) -> {
            String fieldPath = path + "." + fieldName;
            if (convertConfig.isIgnoreFieldName(fieldPath)) return;
            Object fieldValue = fieldValueGetter.get();
            AttributeValue fieldAttributeValue = convertAttributeValue(fieldValue, fieldPath, convertConfig);
            if (filterAttributeValue(fieldAttributeValue, collectionType)) return;
            fieldMap.put(fieldName, fieldAttributeValue);
        };
        if (value.getClass().isArray()) {
            for (int i = 0; i < Array.getLength(value); i++) {
                Object v = Array.get(value, i);
                loopHandler.accept(i + "", () -> v);
            }
            return new AttributeValue().withL(fieldMap.values());
        } else if (value instanceof Collection) {
            AtomicInteger index = new AtomicInteger(0);
            ((Collection<?>) value).forEach(v -> loopHandler.accept(index.getAndIncrement() + "", () -> v));
            return new AttributeValue().withL(fieldMap.values());
        } else if (value instanceof Map) {
            ((Map<String, ?>) value).forEach((k, v) -> loopHandler.accept(k, () -> v));
            return new AttributeValue().withM(fieldMap);
        } else {
            foreachFiledGetter(value, loopHandler);
            return new AttributeValue().withM(fieldMap);
        }
    }

    /**
     * 过滤掉不合法的属性值
     *
     * @param fieldAttributeValue
     * @param collectionType      M:Map,要求值不能为null; L:List,要求值不能为null
     * @return
     */
    private static boolean filterAttributeValue(AttributeValue fieldAttributeValue, String collectionType) {
        if ("M".equals(collectionType)) return fieldAttributeValue == null;
        else if ("L".equals("env")) return fieldAttributeValue == null;
        return false;
    }

    public static String convertDate(Date date) {
        return new SimpleDateFormat("yyyyMMddHHmmss").format(date);
    }

    public static <T extends Date> T unconvertDate(String str, Class<T> cls) {
        try {
            long time = new SimpleDateFormat("yyyyMMddHHmmss").parse(str).getTime();
            Constructor<T> constructor = cls.getDeclaredConstructor(long.class);
            return constructor.newInstance(time);
        } catch (Exception e) {
            throw new RuntimeException("unconvertDate发生异常:str=" + str + ",cls=" + cls.getName(), e);
        }
    }

    /**
     * 遍历javaBean的getter方法
     *
     * @param obj
     * @param loop fieldName -> (() -> fieldValue)
     */
    public static void foreachFiledGetter(Object obj, BiConsumer<String, Supplier> loop) {
        for (Method method : obj.getClass().getDeclaredMethods()) {
            if (!Modifier.isPublic(method.getModifiers()) || Modifier.isStatic(method.getModifiers())
                    || method.getReturnType() == null || method.getParameterCount() > 0
                    || !method.getName().startsWith("get") || method.getName().length() < 4) continue;
            String fieldName = method.getName().substring(3, 4).toLowerCase() + method.getName().substring(4);
            Supplier fieldValueGetter = () -> invokeMethod(obj, method);
            loop.accept(fieldName, fieldValueGetter);
        }
    }

    @FunctionalInterface
    public interface TriConsumer<T, U, K> {
        void accept(T t, U u, K k);
    }

    public static void foreachFiledSetter(Object obj, TriConsumer<String, Class, Consumer> loop) {
        for (Method method : obj.getClass().getDeclaredMethods()) {
            if (!Modifier.isPublic(method.getModifiers()) || Modifier.isStatic(method.getModifiers())
                    || method.getParameterCount() != 1
                    || !method.getName().startsWith("set") || method.getName().length() < 4) continue;
            String fieldName = method.getName().substring(3, 4).toLowerCase() + method.getName().substring(4);
            Consumer fieldValueSetter = (arg) -> invokeMethod(obj, method, arg);
            loop.accept(fieldName, method.getParameterTypes()[0], fieldValueSetter);
        }
    }

    /**
     * 反射调用getter方法
     *
     * @param obj
     * @param method
     * @return
     */
    public static Object invokeMethod(Object obj, Method method, Object... args) {
        try {
            if (!method.isAccessible()) method.setAccessible(true);
            return method.invoke(obj, args);
        } catch (Exception e) {
            throw new RuntimeException("调用get方法" + obj.getClass().getName() + "::" + method.getName() + "发生异常!", e);
        }
    }

    public static <T> T unconvertNumberType(String str, Class<T> cls) {
        T value;
        if (BigDecimal.class.isAssignableFrom(cls)) {
            value = (T) new BigDecimal(str);
        } else if (BigInteger.class.isAssignableFrom(cls)) {
            value = (T) new BigInteger(str);
        } else if (Boolean.class.isAssignableFrom(cls) || boolean.class == cls) {
            value = (T) Boolean.valueOf(str);
        } else if (Byte.class.isAssignableFrom(cls) || byte.class == cls) {
            value = (T) Byte.valueOf(str);
        } else if (Character.class.isAssignableFrom(cls) || char.class == cls) {
            value = (T) Character.valueOf(str.charAt(0));
        } else if (Short.class.isAssignableFrom(cls) || short.class == cls) {
            value = (T) Short.valueOf(str);
        } else if (Integer.class.isAssignableFrom(cls) || int.class == cls) {
            value = (T) Integer.valueOf(str);
        } else if (Long.class.isAssignableFrom(cls) || long.class == cls) {
            value = (T) Long.valueOf(str);
        } else if (Float.class.isAssignableFrom(cls) || float.class == cls) {
            value = (T) Float.valueOf(str);
        } else if (Double.class.isAssignableFrom(cls) || double.class == cls) {
            value = (T) Double.valueOf(str);
        } else {
            value = null;
        }
        return value;
    }

    public static Object unconvertAttributeValue(AttributeValue attributeValue, Type type) {
        if (attributeValue == null || type == null) return null;
        Class cls;
        Type[] typeArgs;
        if (type instanceof ParameterizedType) {
            cls = (Class) ((ParameterizedType) type).getRawType();
            typeArgs = ((ParameterizedType) type).getActualTypeArguments();
        } else {
            cls = (Class) type;
            typeArgs = new Type[0];
        }
        Object value;
        if (attributeValue.getS() != null) {
            if (CharSequence.class.isAssignableFrom(cls)) {
                value = attributeValue.getS();
            } else if (Date.class.isAssignableFrom(cls)) {
                value = unconvertDate(attributeValue.getS(), cls);
            } else {
                value = unconvertNumberType(attributeValue.getS(), cls);
                if (value == null) {
                    throw new RuntimeException("无法转换的数字类型:" + cls.getName());
                }
            }
        } else if (attributeValue.getN() != null) {
            value = unconvertNumberType(attributeValue.getN(), cls);
        } else if (attributeValue.getBOOL() != null) {
            value = unconvertNumberType(attributeValue.getBOOL() + "", cls);
        } else if (attributeValue.getB() != null) {
            if (cls == byte[].class) {
                return attributeValue.getB().array();
            } else {
                throw new RuntimeException("无法转换的字节集合类型:" + cls.getName());
            }
        } else if (attributeValue.getL() != null || attributeValue.getBS() != null
                || attributeValue.getSS() != null || attributeValue.getNS() != null) {
            Class fieldCls = null;
            if (typeArgs.length > 0) fieldCls = (Class) typeArgs[0];
            else if (attributeValue.getBS() != null) fieldCls = byte[].class;
            else if (attributeValue.getSS() != null) fieldCls = String.class;
            else if (attributeValue.getNS() != null) fieldCls = Number.class;
            if (cls.isArray()) {
                value = Array.newInstance(fieldCls, attributeValue.getL().size());
                AtomicInteger index = new AtomicInteger(0);
                for (AttributeValue fieldAttributeValue : attributeValue.getL()) {
                    Object fieldValue = unconvertAttributeValue(fieldAttributeValue, fieldCls);
                    Array.set(value, index.getAndIncrement(), fieldValue);
                }
            } else if (Collection.class.isAssignableFrom(cls)) {
                value = createObject(cls);
                for (AttributeValue fieldAttributeValue : attributeValue.getL()) {
                    Object fieldValue = unconvertAttributeValue(fieldAttributeValue, fieldCls);
                    ((Collection) value).add(fieldValue);
                }
            } else {
                throw new RuntimeException("无法转换的集合类型:" + cls.getName());
            }
        } else if (attributeValue.getM() != null) {
            value = createObject(cls);
            if (Map.class.isAssignableFrom(cls)) {
                Class fieldCls = null;
                for (Map.Entry<String, AttributeValue> entry : attributeValue.getM().entrySet()) {
                    Object fieldValue = unconvertAttributeValue(entry.getValue(), fieldCls);
                    ((Map<String, Object>) value).put(entry.getKey(), fieldValue);
                }
            } else {
                foreachFiledSetter(value, (fieldName, fieldCls, setter) -> {
                    Object fieldValue = unconvertAttributeValue(attributeValue.getM().get(fieldName), fieldCls);
                    setter.accept(fieldValue);
                });
            }
        } else {
            value = null;
        }
        return value;
    }

    public static <T> T createObject(Class<T> cls) {
        try {
            if (Modifier.isAbstract(cls.getModifiers()) || Modifier.isInterface(cls.getModifiers())) {
                if (List.class.isAssignableFrom(cls)) return (T) new LinkedList();
                else if (Set.class.isAssignableFrom(cls)) return (T) new LinkedHashSet();
                else if (Map.class.isAssignableFrom(cls)) return (T) new LinkedHashMap();
            }
            return cls.newInstance();
        } catch (Exception e) {
            throw new RuntimeException("创建对象发生异常:" + cls.getName(), e);
        }
    }
}
