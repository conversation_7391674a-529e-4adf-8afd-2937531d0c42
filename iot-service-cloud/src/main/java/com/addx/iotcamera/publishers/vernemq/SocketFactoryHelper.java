package com.addx.iotcamera.publishers.vernemq;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.Security;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

public class SocketFactoryHelper {
	public static SSLSocketFactory getSocketFactory(final String caCrtFile)throws Exception {
			Security.addProvider(new BouncyCastleProvider());
			// load CA certificate
			X509Certificate caCert = null;
			FileInputStream fis = new FileInputStream(caCrtFile);
			BufferedInputStream bis = new BufferedInputStream(fis);
			CertificateFactory cf = CertificateFactory.getInstance("X.509");
			while (bis.available() > 0) {
			caCert = (X509Certificate) cf.generateCertificate(bis);
			// System.out.println(caCert.toString());
			}

			// CA certificate is used to authenticate server
			KeyStore caKs = KeyStore.getInstance(KeyStore.getDefaultType());
			caKs.load(null, null);
			caKs.setCertificateEntry("ca-certificate", caCert);
			TrustManagerFactory tmf = TrustManagerFactory.getInstance("X509");
			tmf.init(caKs);

			// finally, create SSL socket factory
			SSLContext context = SSLContext.getInstance("TLSv1.2");
			context.init(null, tmf.getTrustManagers(), null);
			return context.getSocketFactory();
			}

	public static HostnameVerifier getHostnameVerifier() {
		 return (javax.net.ssl.HostnameVerifier) new org.apache.hc.client5.http.ssl.DefaultHostnameVerifier();
	}
}
