package com.addx.iotcamera.dao.openapi;

import com.addx.iotcamera.bean.openapi.PaasUserVipLog;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Repository
public interface PaasUserVipDAO {

    @Insert("insert into `camera`.`paas_user_vip_log`(" +
            "`tenant_id`," +
            "`third_user_id`," +
            "`product_id`," +
            "`vip_level`," +
            "`time`," +
            "`end_time`," +
            "`operator`" +
            ") values (" +
            "#{tenantId}," +
            "#{thirdUserId}," +
            "#{productId}," +
            "#{vipLevel}," +
            "#{time}," +
            "#{endTime}," +
            "#{operator}" +
            ")")
    int savePaasUserVip(PaasUserVipLog paasUserVipLog);

    // 根据时间参数，过滤出当前vip
    String FILTER_CURRENT_VIP_BY_TIME = "( `time` <= #{time} and `end_time` > #{time} )";
    String FILTER_CURRENT_VIP_BY_TIME_XML = "( `time` &lt;= #{time} and `end_time` &gt; #{time} )";

    // 查询当前生效的的vip
    @Select("select * from `camera`.`paas_user_vip_log`" +
            " where `tenant_id`=#{tenantId} and `third_user_id`=#{thirdUserId}" +
            " and " + FILTER_CURRENT_VIP_BY_TIME +
            " order by `time` asc limit 1")
    PaasUserVipLog queryLastPaasUserVip(@Param("tenantId") String tenantId, @Param("thirdUserId") String thirdUserId
            , @Param("time") Long time);

    @Select("select * from `camera`.`paas_user_vip_log`" +
            " where `tenant_id`=#{tenantId} and `third_user_id`=#{thirdUserId}" +
            " order by `time` asc")
    List<PaasUserVipLog> queryPaasUserVip(@Param("tenantId") String tenantId, @Param("thirdUserId") String thirdUserId);

    @Select("select * from `camera`.`paas_user_vip_log`" +
            " where `tenant_id`=#{tenantId} and `third_user_id`=#{thirdUserId}" +
            " order by `time` asc" + // 按开始时间排序
            " for update"  // 给同一thirdUserId下的所有vip记录加区间锁
    )
    List<PaasUserVipLog> queryPaasUserVipForUpdate(@Param("tenantId") String tenantId, @Param("thirdUserId") String thirdUserId);

    @Select("<script>select * " +
            " from `camera`.`paas_user_vip_log`" +
            " <where>" +
            " <if test='last.tenantId!=null'>and `tenant_id`=#{last.tenantId}</if>" +
            " <if test='last.thirdUserId!=null'>" +
            "   <choose>" +
            "    <when test='last.time!=null'> and `third_user_id`=#{last.thirdUserId} and `time`>#{last.time}</when>" +
            "    <otherwise> and `third_user_id`>#{last.thirdUserId}</otherwise>" +
            "   </choose>" +
            " </if>" +
            " </where>" +
            " order by `third_user_id` asc,`time` asc" +
            " <if test='limitNum!=null'>limit #{limitNum}</if>" +
            "</script>")
    List<PaasUserVipLog> queryByLastAndLimitNum(@Param("last") PaasUserVipLog last, @Param("limitNum") Integer limitNum);

    @Select("<script>select * from `camera`.`paas_user_vip_log`" +
            " where `tenant_id`=#{tenantId}" +
            " and `third_user_id` in" +
            " <foreach collection='ids' item='id' open='(' separator=',' close=')'>#{id}</foreach>" +
            " <if test='time!=null'>and " + FILTER_CURRENT_VIP_BY_TIME_XML + " </if>" +
            "</script>")
    List<PaasUserVipLog> queryByTenantIdAndThirdUserIdIn(@Param("tenantId") String tenantId
            , @Param("ids") Collection<String> ids, @Param("time") Long time);

    @Select("<script>select count(distinct `third_user_id`) userNum,count(1) logNum" +
            " from `camera`.`paas_user_vip_log`" +
            " <where>" +
            " <if test='tenantId!=null'>and `tenant_id`=#{tenantId}</if>" +
            " </where></script>")
    Map<String, Long> queryUserNumAndLogNum(@Param("tenantId") String tenantId);

    @Update("<script>" +
            "update `camera`.`paas_user_vip_log`" +
            " set `id`=`id`" +
            " <if test='tenantId!=null'>,`tenant_id`=#{tenantId}</if>" +
            " <if test='thirdUserId!=null'>,`third_user_id`=#{thirdUserId}</if>" +
            " <if test='productId!=null'>,`product_id`=#{productId}</if>" +
            " <if test='vipLevel!=null'>,`vip_level`=#{vipLevel}</if>" +
            " <if test='time!=null'>,`time`=#{time}</if>" +
            " <if test='endTime!=null'>,`end_time`=#{endTime}</if>" +
            " <if test='operator!=null'>,`operator`=#{operator}</if>" +
            " where `id`=#{id}" +
            "</script>")
    int updatePaasUserVipById(PaasUserVipLog paasUserVipLog);

    @Delete("delete from `camera`.`paas_user_vip_log` where `id`=#{id}")
    int deletePaasUserVipById(@Param("id") Long id);

    @Delete("<script>" +
            "delete from `camera`.`paas_user_vip_log` where `id` in " +
            " <foreach collection='ids' item='id' open='(' close=')' separator=','>#{id}</foreach>" +
            "</script>")
    int deletePaasUserVipByIds(@Param("ids") Collection<Long> ids);

    /** 这些方法只用于初始化脚本 begin **/

    @Select("select * from `camera`.`paas_user_vip_log`")
    List<PaasUserVipLog> queryAllPaasUserVipLog();

    /** 这些方法只用于初始化脚本 end **/

}
