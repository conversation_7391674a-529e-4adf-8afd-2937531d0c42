package com.addx.iotcamera.dao.device;

import com.addx.iotcamera.bean.domain.ShareApprovalDO;
import com.addx.iotcamera.bean.domain.ShareStatusDO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.StatementType;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShareApprovalLocalDAO {
    
    @Insert("INSERT INTO `camera`.`share_approval_local` " +
            "(`admin_id`, " +
            "`target_id`, " +
            "`target_email`, " +
            "`role_id`, " +
            "`insert_time`, " +
            "`handled`, " +
            "`status`," +
            "`share_id`,link_param,tenant_id,share_code,serial_number,device_type) " +
            "VALUES " +
            "(#{adminId}, " +
            " #{targetId}, " +
            " #{targetEmail}, " +
            " #{roleId}, " +
            " #{insertTime}, " +
            " 0, " +
            " 0," +
            "#{shareId},#{linkParam},#{tenantId},#{shareCode},#{serialNumber},#{deviceType}); ")
    @SelectKey(before = false, keyProperty = "id", resultType = Integer.class, statementType = StatementType.STATEMENT, statement = "SELECT LAST_INSERT_ID() AS id;")
    Integer insertApproval(ShareApprovalDO approvalDO);

    @Update("UPDATE `camera`.`share_approval_local` " +
            "SET " +
            "`handle_time` = UNIX_TIMESTAMP(), " +
            "`handled` = 1, " +
            "`target_id` = #{targetId}, " +
            "`status` = #{status} " +
            "WHERE `id` = #{id}; ")
    Integer setApprovalStatus(ShareApprovalDO approvalDO);

    @Update("UPDATE `camera`.`share_approval_local` " +
            "SET " +
            "`insert_time` = UNIX_TIMESTAMP() " +
            "WHERE `id` = #{id}; ")
    Integer initApprovalTime(@Param("id") Integer id);


    @Select("select * from camera.share_approval_local where share_id = #{shareId}")
    ShareApprovalDO queryByShareId(@Param("shareId") String shareId);

    @Select("select id,admin_id,target_id,target_email,share_id,serial_number,device_type " +
            "from camera.share_approval_local where target_email = #{targetEmail} " +
            " and tenant_id = #{tenantId} and status = 0 and insert_time > (unix_timestamp() - 24 * 60 * 60)")
    List<ShareApprovalDO> queryShareApprovalList(ShareApprovalDO approvalDO);


    @Select("select id,admin_id,target_id,target_email,share_id,serial_number,device_type from camera.share_approval_local where share_code = #{shareCode} " +
            "  and status = 0 and insert_time > (unix_timestamp() - 24 * 60 * 60)")
    ShareApprovalDO queryShareApprovalByShareCode(String shareCode);

    @Select("<script>SELECT   " +
            "    id,`target_id` as user_id, target_email as userEmail ,status ,share_id,serial_number,device_type  " +
            "FROM camera.`share_approval_local` " +
            "WHERE `handled` = 0 AND insert_time > (unix_timestamp() - 24 * 60 * 60) and device_type = #{deviceType} " +
            " <if test='serialNumber!=null and serialNumber != \"\"'> and `serial_number` = #{serialNumber} </if>" +
            " <if test='targetEmail!=null and targetEmail != \"\"'> and target_email =#{targetEmail}</if>" +
            "</script>")
    List<ShareStatusDO> queryDeviceShareingList(
            @Param("serialNumber") String serialNumber,
            @Param("targetEmail") String targetEmail,
            @Param("deviceType") Integer deviceType
    );
}
