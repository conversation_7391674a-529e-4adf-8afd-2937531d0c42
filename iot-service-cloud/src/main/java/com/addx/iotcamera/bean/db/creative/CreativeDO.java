package com.addx.iotcamera.bean.db.creative;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
@Accessors(chain = true)
public class CreativeDO implements Serializable {
    private Long id;
    private Integer ctype;
    private String name;
    private Integer width;
    private Integer height;
    private String links;
    private String language;
    private String description;
    private Long createTime;
    private Long updateTime;
}