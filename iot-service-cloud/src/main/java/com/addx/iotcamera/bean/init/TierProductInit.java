package com.addx.iotcamera.bean.init;

import com.addx.iotcamera.bean.app.vip.TierProduct;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.config.apollo.GlobalConfig;
import com.addx.iotcamera.config.apollo.ProductConfig;
import com.addx.iotcamera.config.apollo.ProductSubConfig;
import com.addx.iotcamera.config.apollo.ProductUpgradeConfig;
import com.addx.iotcamera.config.app.CustomerMadeTierNameConfig;
import com.addx.iotcamera.config.app.TierProductConfig;
import com.addx.iotcamera.config.app.TierProductSubConfig;
import com.addx.iotcamera.config.app.TierProductUpgradeConfig;
import com.addx.iotcamera.enums.TierLevelEnums;
import com.addx.iotcamera.service.ProductService;
import com.addx.iotcamera.service.vip.TierService;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.addx.iotcamera.constants.CopyWriteConstans.tier_message_subhead_pre;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;

@Configuration
@Slf4j
public class TierProductInit {
    @Resource
    private CopyWrite copyWrite;


    @Resource
    private ProductConfig productConfig;
    @Resource
    private ProductSubConfig productSubConfig;
    @Resource
    private ProductUpgradeConfig productUpgradeConfig;

    @Resource
    private GlobalConfig globalConfig;

    private final static String defaultMonthNum = "3";

    @Resource
    private ProductService productService;

    @Resource
    @Lazy
    private TierService tierService;

    @Resource
    private CustomerMadeTierNameConfig customerMadeTierNameConfig;

    /**
     * 初始化商品列表（升级、订阅）-- yml 只配置中文，需在加载copyWrite 后，初始化其他语言
     * @return
     */
    @Bean
    @Lazy
    public TierProductConfig initTierProductConfig(){
        TierProductConfig tierProductConfig = new TierProductConfig();
        initProduct(tierProductConfig);
        return tierProductConfig;
    }

    /**
     * 初始化商品列表（升级）-- yml 只配置中文，需在加载copyWrite 后，初始化其他语言
     * @return
     */
    @Bean
    public TierProductUpgradeConfig initTierProductUpgradeConfig(){
        TierProductUpgradeConfig tierProductUpgradeConfig = new TierProductUpgradeConfig();
        //initProductUpgrade(tierProductUpgradeConfig);
        return tierProductUpgradeConfig;
    }

    /**
     * 初始化商品列表（订阅）-- yml 只配置中文，需在加载copyWrite 后，初始化其他语言
     * @return
     */
    @Bean
    @Lazy
    public TierProductSubConfig initTierProductSubConfig(){
        TierProductSubConfig tierProductSubConfig = new TierProductSubConfig();
        initProductSub(tierProductSubConfig);
        return tierProductSubConfig;
    }

    /**
     * 初始化普通商品列表
     *
     * @param tierProductConfig
     */
    public void initProduct(TierProductConfig tierProductConfig) {
        List<String> languageList = globalConfig.getLanguage().get(TENANTID_VICOO);
        //tenantId -> package -> language -> tierId -> product List
        Map<String, Map<String, Map<String, Map<Integer, List<TierProduct>>>>> tierProductTenantMap = Maps.newHashMap();
        //循环公司
        for (String tenantId : productConfig.getProduct().keySet()) {
            //package -> language -> tierId -> product List
            Map<String, Map<String, Map<Integer, List<TierProduct>>>> tierProductTenantPackageMap = Maps.newHashMap();
            for (String appPackage : productConfig.getProduct().get(tenantId).keySet()) {
                //获取商品列表模板,初始化结构
                Map<Integer, List<TierProduct>> tierProductMap = productConfig.getProduct().get(tenantId).get(appPackage).get("zh");

                //language -> tierId -> product List
                Map<String, Map<Integer, List<TierProduct>>> tierProductTenantPackageLanguageMap = Maps.newHashMap();
                for (String language : languageList) {

                    Map<Integer, List<TierProduct>> languageTierProductMap = Maps.newHashMap();
                    //将商品月数根据语言替换
                    for (Integer tierId : tierProductMap.keySet()) {
                        List<TierProduct> productList = tierProductMap.get(tierId);
                        List<TierProduct> productListNew = initTierProductList(productList);
                        for (int i = 0; i < productListNew.size(); i++) {
                            String month = this.getProductMonth(productListNew.get(i).getProductId(),language,copyWrite);
                            if(StringUtils.isEmpty(month)){
                                continue;
                            }
                            productListNew.get(i).setMonth(month);
                        }
                        languageTierProductMap.put(tierId, productListNew);
                    }
                    tierProductTenantPackageLanguageMap.put(language,languageTierProductMap);
                    log.debug("productInit tenantId {},appPackage {},language {}",tenantId,appPackage,language);
                }
                tierProductTenantPackageMap.put(appPackage,tierProductTenantPackageLanguageMap);
            }
            tierProductTenantMap.put(tenantId,tierProductTenantPackageMap);
        }
        tierProductConfig.setProduct(tierProductTenantMap);
    }

    /**
     * 初始化订阅商品列表
     *
     * @param tierProductSubConfig
     */
    public void initProductSub(TierProductSubConfig tierProductSubConfig) {
        List<String> languageList = globalConfig.getLanguage().get(TENANTID_VICOO);
        //tenantId -> package -> language -> tierId -> product List
        Map<String, Map<String, Map<String, Map<String, List<TierProduct>>>>> tierProductTenantMap = Maps.newHashMap();
        //循环tenantId
        for (String tenantId : productSubConfig.getProduct().keySet()) {
            //package -> language -> tierId -> product List
            Map<String, Map<String, Map<String, List<TierProduct>>>> tierProductTenantPackageMap = Maps.newHashMap();
            for (String appPackage : productSubConfig.getProduct().get(tenantId).keySet()) {
                //获取商品列表模板,初始化结构,tierId-商品list
                Map<String, List<TierProduct>> tierProductMap = productSubConfig.getProduct().get(tenantId).get(appPackage).get("zh");

                //language -> tierId -> product List
                Map<String, Map<String, List<TierProduct>>> tierProductTenantPackageLanguageMap = Maps.newHashMap();
                for (String language : languageList) {
                    Map<String, List<TierProduct>> languageTierProductMap = Maps.newHashMap();
                    //将商品月数根据语言替换
                    for (String tierId : tierProductMap.keySet()) {
                        //根据模板，根据tenantId,language生成不同的商品列表
                        List<TierProduct> productList = tierProductMap.get(tierId);
                        List<TierProduct> productListNew = initTierProductList(productList);
                        for (int i = 0; i < productListNew.size(); i++) {
                            String key = "每年".equals(productListNew.get(i).getMonth()) ? "yearly" : "monthly";
                            String monthly = copyWrite.getConfig().get(key).get(language);
                            if(StringUtils.isEmpty(monthly)){
                                continue;
                            }
                            productListNew.get(i).setMonth(monthly);
                        }
                        languageTierProductMap.put(tierId, productListNew);
                    }

                    tierProductTenantPackageLanguageMap.put(language, languageTierProductMap);
                    log.debug("productSubInit tenantId {},appPackage {},language {}",tenantId,appPackage,language);
                }
                tierProductTenantPackageMap.put(appPackage,tierProductTenantPackageLanguageMap);
            }
            tierProductTenantMap.put(tenantId,tierProductTenantPackageMap);
        }
        tierProductSubConfig.setProduct(tierProductTenantMap);
    }

    /**
     * 初始化升级商品列表
     *
     * @param tierProductUpgradeConfig
     */
    public void initProductUpgrade(TierProductUpgradeConfig tierProductUpgradeConfig) {
        List<String> languageList = globalConfig.getLanguage().get(TENANTID_VICOO);
        //循环tenantId
        //tenantId -> package -> language -> tierId -> product List
        Map<String, Map<String, Map<String, Map<String, List<TierProduct>>>>> tierProductTenantMap = Maps.newHashMap();
        for (String tenantId : productUpgradeConfig.getProduct().keySet()) {
            //package -> language -> tierId -> product List
            Map<String, Map<String, Map<String, List<TierProduct>>>> tierProductTenantPackageMap = Maps.newHashMap();
            for (String appPackage : productUpgradeConfig.getProduct().get(tenantId).keySet()) {
                //获取商品列表模板,初始化结构,tierId-商品list
                Map<String, List<TierProduct>> tierProductMap = productUpgradeConfig.getProduct().get(tenantId).get(appPackage).get("zh");
                //language -> tierId -> product List
                Map<String, Map<String, List<TierProduct>>> tierProductTenantPackageLanguageMap = Maps.newHashMap();
                for (String language : languageList) {

                    Map<String, List<TierProduct>> languageTierProductMap = Maps.newHashMap();
                    //将商品月数根据语言替换
                    for (String tierId : tierProductMap.keySet()) {
                        //根据模板，根据tenantId,language生成不同的商品列表
                        List<TierProduct> productList = tierProductMap.get(tierId);
                        List<TierProduct> productListNew = initTierProductList(productList);
                        for (int i = 0; i < productListNew.size(); i++) {
                            String month = this.getProductMonth(productListNew.get(i).getProductId(),language,copyWrite);
                            if(StringUtils.isEmpty(month)){
                                continue;
                            }
                            productListNew.get(i).setMonth(month);
                        }
                        languageTierProductMap.put(tierId, productListNew);
                    }

                    tierProductTenantPackageLanguageMap.put(language, languageTierProductMap);
                    log.debug("productUpgradeInit tenantId {},appPackage {},language {}",tenantId,appPackage,language);
                }
                tierProductTenantPackageMap.put(appPackage,tierProductTenantPackageLanguageMap);
            }
            tierProductTenantMap.put(tenantId,tierProductTenantPackageMap);
        }
        tierProductUpgradeConfig.setProduct(tierProductTenantMap);
    }


    /**
     * 根据模本生成商品列表
     *
     * @param productList
     * @return
     */
    private List<TierProduct> initTierProductList(List<TierProduct> productList) {
        List<TierProduct> tierProducts = Lists.newArrayList();

        for (TierProduct tierProduct : productList) {
            TierProduct product = new TierProduct();
            BeanUtils.copyProperties(tierProduct, product);
            tierProducts.add(product);
        }
        return tierProducts;
    }

    /**
     * 获取套餐等级
     *
     * @param tierId
     * @param language
     * @param copyWrite
     * @return
     */
    public String getTierSubhead(String tierId, String language, CopyWrite copyWrite,String tenantId) {
        Tier tier = tierService.queryTierById(Integer.valueOf(tierId));
        if(tier == null || tier.getTierId() == 0){
            return "";
        }
        String tierLevel = TierLevelEnums.idOf(tier.getLevel()).getName();
        //获取指定等级套餐、指定语言 的subhead
        return copyWrite.getConfig().getOrDefault(this.initCopyWriteNameKey(tier_message_subhead_pre , tierLevel,tenantId), Collections.singletonMap(language, "TierSubhead")).get(language);
    }

    /**
     * 拼接文案key
     * @param copyWriteConfigPreKey
     * @param level
     * @param tenantId
     * @return
     */
    public String initCopyWriteNameKey(String copyWriteConfigPreKey,String level,String tenantId){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(copyWriteConfigPreKey);
        if(customerMadeTierNameConfig.supportCustomerMadeTierName(tenantId)){
            stringBuffer.append("_").append(tenantId);
        }
        if(StringUtils.hasLength(level)){
            stringBuffer.append("_").append(level);
        }

        return stringBuffer.toString();
    }

    /**
     * 商品月描述
     *
     * @return
     */
    public String getProductMonth(Integer productId,String language,CopyWrite copyWrite){
        ProductDO productDO = productService.queryProductById(productId);
        if(productDO == null){
            log.debug("initProduct product not exist {}",productId);
            return "";
        }
        String key = productDO.getMonth() == 1 ? "oneMonth" : "months";
        String month = copyWrite.getConfig().get(key).get(language);
        if(StringUtils.isEmpty(month)){
            return "";
        }
        return month.replace(defaultMonthNum, String.valueOf(productDO.getMonth()));
    }
}
