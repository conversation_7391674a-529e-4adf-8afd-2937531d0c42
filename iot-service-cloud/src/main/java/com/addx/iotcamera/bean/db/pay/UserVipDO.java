package com.addx.iotcamera.bean.db.pay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserVipDO {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * 会员Id
     */
    private Integer tierId;

    /**
     * 会员生效时间
     */
    private Integer effectiveTime;

    /**
     * 会员结束时间
     */
    private Integer endTime;

    /**
     * 创建时间
     */
    private Integer cdate;
    private Long orderId;
    private String tradeNo;

    private Boolean active;
    @Schema(title = "回看天数")
    private Integer rollingDay;

    @Schema(title = "免费试用")
    private Integer freeTrial;

    @Schema(title = "套餐服务类型")
    private Integer tierServiceType;
}
