package com.addx.iotcamera.bean.app.payment;

import lombok.Data;
import org.json.JSONArray;

/**
 * <AUTHOR>
 */
@Data
public class IosPublisher {
    /**
     * 通知的版本
     */
    private String auto_renew_adam_id;
    /**
     * 用户的订阅续订的自动更新订阅的产品标识符
     */
    private String auto_renew_product_id;
    /**
     * 当前续订状态
     */
    private String auto_renew_status;
    /**
     * 续订状态处于打开或关闭状态的时间
     */
    private String auto_renew_status_change_date;
    /**
     * 以UNIX纪元时间格式（以毫秒为单位）打开或关闭自动更新订阅的更新状态的时间
     */
    private String auto_renew_status_change_date_ms;


    /**
     * 自动续订的续订状态在太平洋时区中打开或关闭的时间
     */
    private String auto_renew_status_change_date_pst;
    /**
     * 收据生成的环境。
     */
    private String environment;
    /**
     * 订阅过期的原因
     */
    private Integer expiration_intent;
    /**
     * 最新的Base64编码的交易收据
     */
    private String latest_expired_receipt;
    /**
     * 最新的Base64编码的交易收据详情
     */
    private String latest_expired_receipt_info;


    /**
     * 交易收据
     */
    private String latest_receipt;
    /**
     * 交易收据详情
     */
    private LatestReceiptInfo latest_receipt_info;
    /**
     * 触发通知的订阅事件
     */
    private String notification_type;
    /**
     * 验证收据时password
     */
    private String password;
    /**
     * 包含有关应用程序最新应用内购买交易信息的对象
     */
    private String unified_receipt;
}
