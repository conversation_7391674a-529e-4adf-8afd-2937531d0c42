package com.addx.iotcamera.bean.reid;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;

@Accessors(chain = true)
@Data
public class ReIdLabelUpdateVO {
    private String userId;
    private String labelId;

    private String labelName; // 聚类名称
    private List<String> labelIds;

    public List<String> getAllLabelIds() {
        LinkedHashSet<String> labelIdSet = new LinkedHashSet<>();
        if (this.labelIds != null) {
            labelIdSet.addAll(this.labelIds);
        }
        if (this.labelId != null) {
            labelIdSet.add(this.labelId);
        }
        return new LinkedList<>(labelIdSet);
    }
}
