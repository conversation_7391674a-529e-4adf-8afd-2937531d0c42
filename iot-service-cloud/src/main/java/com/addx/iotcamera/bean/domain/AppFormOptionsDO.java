package com.addx.iotcamera.bean.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class AppFormOptionsDO {

    private Map<String, Collection<AppFormOptionDO>> deviceFormOptions;

    @Data
    @Builder
    public static class AppFormOptionDO {
        private Object value;
        @Builder.Default
        private boolean enabled = true;
    }


    @Getter
    @AllArgsConstructor
    public enum CooldownOptionValue {
        SECONDS_10("10s", 10, false, false),
        SECONDS_30("30s", 30, false, false),
        SECONDS_60("60s", 60, false, true),
        SECONDS_180("180s", 180, false, true),
        SECONDS_300("300s", 300, true, true),
        SECONDS_600("600s", 600, true, true),
        SECONDS_1200("1200s", 1200, true, true),
        SECONDS_1800("1800s", 1800, true, true),
        ;
        private final String enumName;
        private final int value;
        private final boolean isFreeOptionForNotStandby; // 是否是常电设备的免费选项。用于替代对auto档的判断。
        private final boolean isFreeOptionForStandby; // 是否是低功耗设备的免费选项。用于替代对auto档的判断。

        public static List<String> getAllEnumNames() {
            return Arrays.stream(values()).map(it -> it.getEnumName()).collect(Collectors.toList());
        }

        public static List<CooldownOptionValue> getFreeOptionsByCanStandby(boolean canStandby) {
            return Arrays.stream(values()).filter(it -> (!canStandby && it.isFreeOptionForNotStandby)
                    || (canStandby && it.isFreeOptionForStandby)).collect(Collectors.toList());
        }
    }

    @Getter
    @AllArgsConstructor
    public enum VideoSecondOptionValue {
        SECONDS_10("10s", 10, true,true),
        SECONDS_15("15s", 15, true,true),
        SECONDS_20("20s", 20, true,true),

        SECONDS_60("60s", 60, false,true), // 20240412 新增收费档位
        SECONDS_120("120s", 120, false,false), // 20240412 新增收费档位
        SECONDS_180("180s", 180, false,false), // 20240412 新增收费档位
        SECONDS_AUTO("auto", -1, false,false),
        ;
        private final String enumName;
        private final int value;
        private final boolean isFreeOption; // 是否是免费选项。用于替代对auto档的判断。
        private final boolean isPromotion;  //推广期
    }
}
