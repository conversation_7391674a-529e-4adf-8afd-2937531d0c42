package com.addx.iotcamera.config.app;

import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import com.google.api.client.util.Maps;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Data
@Component
@PropertySource(value = {"classpath:/app/app_copyright.yml"}, encoding = "utf-8", factory = MixPropertySourceFactory.class)
@ConfigurationProperties(prefix = "app.copyright")
public class AppCopyrightConfig {
    // tenantId,app-type

    Map<String, Map<String, String>> config;

    /**
     * 查询copyrite
     * @param tenantId
     * @param appType
     * @return
     */
    public String queryCopyrite(String tenantId,String appType){
        if(!config.containsKey(tenantId)){
            return "";
        }
        String copyrite = config.get(tenantId).get(appType);
        return copyrite.replace("{year}",DateUtils.dateToString(new Date(),DateUtils.YYYY));
    }
}
