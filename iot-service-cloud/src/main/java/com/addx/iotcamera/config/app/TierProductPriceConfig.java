package com.addx.iotcamera.config.app;

import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@PropertySource(value = {"classpath:/custom/tier_product_price.yml"}, encoding = "utf-8", factory = MixPropertySourceFactory.class)
@ConfigurationProperties(prefix = "tier.product.price")
public class TierProductPriceConfig {
    // country,currency,国家对应的货币
    Map<String, String> currency;
    //currency-productId-price
    Map<String,Map<Integer,String>> price;

    // 国家对应的货币符号
    Map<String, String> currencyCode;
}
