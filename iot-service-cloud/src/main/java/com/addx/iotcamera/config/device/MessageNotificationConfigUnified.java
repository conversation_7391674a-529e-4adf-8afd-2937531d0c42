package com.addx.iotcamera.config.device;


import com.addx.iotcamera.util.MixPropertySourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

@Slf4j
@Component
@PropertySource(value = {"classpath:/app/message_notification_unified.yml"}, encoding = "utf-8", factory = MixPropertySourceFactory.class)
@ConfigurationProperties(prefix = "unified-message-notification")
public class MessageNotificationConfigUnified {

    private Map<String, MessageNotificationConfig> tenants;

    public void setTenants(Map<String, MessageNotificationConfig> tenants) {
        this.tenants = tenants;
    }

    @PostConstruct
    public void init() {
        log.info("MessageNotificationConfigUnified init");
        for(MessageNotificationConfig config : tenants.values()){
            config.init();
        }
    }

    public MessageNotificationConfig getConfig(String tenantId) {
        if(tenants.containsKey(tenantId)){
            return tenants.get(tenantId);
        }
        return tenants.get("default");
    }

}
