package com.addx.iotcamera.config;

import com.addx.iotcamera.util.MixPropertySourceFactory;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Map;

import static com.addx.iotcamera.constants.ServerConstants.REDIS_CACHE_DEFAULT_EXPIRE_TIME;

/**
 * <AUTHOR>
 */
@Data
@Component
@PropertySource(value = {"classpath:/notification/redis_expired.yml"}, encoding = "utf-8",factory = MixPropertySourceFactory.class)
@ConfigurationProperties(prefix = "redisexpired")
public class RedisExpiredConfig implements Serializable {
    private Map<String,Integer> map;

    public Integer queryExpireTime(String keyPre){
        return map.getOrDefault(keyPre, REDIS_CACHE_DEFAULT_EXPIRE_TIME);
    }
}