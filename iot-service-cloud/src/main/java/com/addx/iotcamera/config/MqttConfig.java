package com.addx.iotcamera.config;

import com.addx.iotcamera.bean.device_msg.DeviceMsgSrc;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.mqtt.MqttConstants;
import com.addx.iotcamera.mqtt.MqttConsumer;
import com.addx.iotcamera.mqtt.enums.EInputTopicType;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.IDUtil;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.internal.ClientComms;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.eclipse.paho.client.mqttv3.util.Debug;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Configuration
@Slf4j
@Data
public class MqttConfig {
    private String username = "addx";
    private String password = "test"; // TODO 神奇的全局密码

    @Value("${mqtt.serverUri}")
    private String serverUri;
    private String idPrefix = "iotService-";

    @Resource
    private MqttConsumer mqttConsumer;

    private MqttClient client;

    private Executor executor1 = Executors.newSingleThreadExecutor();

    @Value("${iot.consumer:false}")
    private Boolean isIotConsumer = false;

    @PostConstruct
    private void init() {
        initClient();
    }

    public void disconnect() throws MqttException {
         if(client.isConnected()){
             log.info("mqttconfig ,client disconnect");
             client.disconnect();
        }
    }

    /**
     * MQTT连接参数
     * @return mqttConnectOptions
     */
    public MqttConnectOptions mqttConnectOptions(){
        MqttConnectOptions options = new MqttConnectOptions();
        options.setUserName(username);
        options.setPassword(password.toCharArray());
        options.setMaxInflight(3000);

        options.setCleanSession(true);
        options.setMqttVersion(MqttConnectOptions.MQTT_VERSION_3_1_1);

        options.setConnectionTimeout(30);     // 连接超时时间（秒）
        options.setKeepAliveInterval(60);     // 心跳间隔
        options.setAutomaticReconnect(true);  // 自动重连

        List<String> serverUriList = Arrays.asList(serverUri.trim().split(","));
        options.setServerURIs(serverUriList.toArray(new String[0]));
        return options;
    }

    /**
     * 生成新的mqttClient
     * @return
     */
    public void initClient() {

        String id = idPrefix + IDUtil.uuid();
        try {
            MqttClient tmpClient = new MqttClient(serverUri, id, new MemoryPersistence());
            tmpClient.setTimeToWait(10000);
            if(isIotConsumer) {
                tmpClient.setCallback(new MqttCallbackExtended() {
                    @Override
                    public void connectionLost(Throwable cause) {
                        com.addx.iotcamera.util.LogUtil.error(log, "<MQTT CONFIG> MQTT 连接断开 " + cause.getMessage(), cause);
                        dumpLog();
                    }

                    @Override
                    public void messageArrived(String topic, MqttMessage message) {
                        MDC.put(MDCKeys.REQUEST_ID, IDUtil.uuid());
                        final String value = new String(message.getPayload(), StandardCharsets.UTF_8);
                        try {
                            mqttConsumer.consume(topic, value, DeviceMsgSrc.IOT_MQTT);
                        } catch (Throwable e) {
                            com.addx.iotcamera.util.LogUtil.error(log, "<MQTT CONFIG> MQTT 消息处理异常 : {}", e.getMessage(), e);
                        }
                    }

                    @Override
                    public void deliveryComplete(IMqttDeliveryToken token) {}

                    @Override
                    public void connectComplete(boolean reconnect, String serverURI) {
                        log.info("<MQTT CONFIG> MQTT 建立连接 reconnect : {}", reconnect);
                        subscribeTopics();
                    }
                });
            }
            tmpClient.connect(mqttConnectOptions());

            //tmpClient初始化成功后再做替换
            if(client !=null && client.isConnected()){
                try {
                    client.disconnect();
                    client.close();
                } catch (Exception e) {
                    com.addx.iotcamera.util.LogUtil.error(log, "<MQTT disconnect> MQTT 连接断开失败 " + e.getMessage(), e);
                }
            }

            this.client = tmpClient;
            log.info("<MQTT CONFIG> 新建 MQTT client 成功 " + id);
        } catch (MqttException e) {
            com.addx.iotcamera.util.LogUtil.error(log, "<MQTT CONFIG> 新建 MQTT client 失败 " + id + " " + e.getMessage(), e);
        }
    }

    /**
     * 订阅主题
     */
    public void subscribeTopics() {
        executor1.execute(() -> {
            String[] topics = new String[EInputTopicType.values().length];
            Arrays.stream(EInputTopicType.values())
                    .map(e -> MqttConstants.inputTopicPrefix + e.getValue())
                    .collect(Collectors.toList())
                    .toArray(topics);

            try {
                log.info("<MQTT CONFIG> 订阅 MQTT topics 开始 {}", client.getClientId());
                client.subscribe(topics);
                log.info("<MQTT CONFIG> 订阅 MQTT topics 完成 {}", client.getClientId());
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.error(log, "<MQTT CONFIG> 订阅 MQTT topics 失败 " + client.getClientId() + " " + e.getMessage(), e);

                // 2秒后重新订阅
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException ignored) { }
                subscribeTopics(); // 重试订阅
            }
        });
    }

    private void dumpLog() {
        try {
            Debug debug = client.getDebug();
            Field field = debug.getClass().getDeclaredField("comms");
            field.setAccessible(true);

            Map<String, Object> map = new HashMap<>();
            ClientComms cc = (ClientComms) field.get(debug);

            map.put("dumpClientComms", cc.getDebug());
            map.put("dumpConOptions" , cc.getConOptions().getDebug());
            map.put("dumpClientState", cc.getClientState().getDebug());
            log.info("<MQTT CONFIG> dump : " + JSON.toJSONString(map));

        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, e.getMessage(), e);
        }

    }
}
