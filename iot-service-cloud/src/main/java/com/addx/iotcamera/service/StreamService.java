package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.P2PTicketResponse;
import com.addx.iotcamera.bean.app.PlayLocalVideoRequest;
import com.addx.iotcamera.bean.app.StartAudioRequest;
import com.addx.iotcamera.bean.app.WebsocketTicketVO;
import com.addx.iotcamera.bean.device.WebsocketTicketRequest;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.DeviceOperationDO;
import com.addx.iotcamera.bean.domain.StreamServerDO;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.config.TwilioConfig;
import com.addx.iotcamera.config.Wowconfig;
import com.addx.iotcamera.config.device.DeviceKeepAliveConfig;
import com.addx.iotcamera.config.device.KeepAliveParams;
import com.addx.iotcamera.constants.ReportLogConstants;
import com.addx.iotcamera.enums.EPeerRole;
import com.addx.iotcamera.helper.DeviceOperationHelper;
import com.addx.iotcamera.helper.kiss.AccessTokenHelper;
import com.addx.iotcamera.kiss.bean.IceServer;
import com.addx.iotcamera.kiss.bean.SignalServer;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.mqtt.enums.EOutputTopicType;
import com.addx.iotcamera.publishers.vernemq.MqttSendPackage;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.publishers.vernemq.requests.*;
import com.addx.iotcamera.service.device.DeviceAsyncService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.addx.iotcamera.service.device.DeviceWowConfigParamService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.live.TwilioService;
import com.addx.iotcamera.util.BeanUtil;
import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.LiveLogUtil;
import com.addx.iotcamera.util.MapUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableSet;
import groovy.lang.Lazy;
import lombok.Setter;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.IDUtil;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;
import static org.addx.iot.common.constant.AppConstants.APP_LIVE_AWAKE_DEVICE_EXPIRE;

@Component
public class StreamService {

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private DeviceOperationHelper deviceOperationHelper;

    @Autowired
    private WowzaService wowzaService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private MqttSender mqttSender;

    @Autowired
    private DeviceStatusService deviceStatusService;

    @Autowired
    private UserService userService;
    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private VernemqPublisher publisher;

    @Autowired
    @Lazy
    private ReportLogService reportLogService;

    @Resource
    private IKissService kissService;

    @Resource
    private DeviceManualService deviceManualService;

    @Autowired
    @Lazy
    private DeviceAsyncService deviceAsyncService;


    @Autowired
    private TwilioService twilioService;
    @Autowired
    private TwilioConfig twilioConfig;

    @Autowired
    private Wowconfig wowconfig;
    @Autowired
    private DeviceKeepAliveConfig deviceKeepAliveConfig;

    @Lazy
    @Autowired
    private UdpService udpService;

    @Setter
    @Value("${webrtc.ticket.validDurationMillis}")
    private Long webrtcTicketValidDurationMillis;

    @Autowired
    @Lazy
    private DeviceModelConfigService deviceModelConfigService;

    @Resource
    private DeviceWowConfigParamService deviceWowConfigParamService;

    public static final String DEFAULT_LIVE_RESOLUTION = "1280x720";

    private static final Logger LOGGER = LoggerFactory.getLogger(StreamService.class);
    private static final Set<String> ALLOWED_RESOLUTION = ImmutableSet.of("1920x1080", "1280x720", "480x270", "auto");

    private static final String websocketPath = "/iot-channel/connect";
    // 对于老版设备和APP，采用redis传递accessToken
    public static final String REDIS_KEY_WEBSOCKET_ACCESS_TOKEN = "websocket:accessToken:{group}:{id}";
    private static final int REDIS_TIMEOUT_WEBSOCKET_ACCESS_TOKEN = 3600 * 24; // 缓存一天
    private static final String REDIS_KEY_WEBSOCKET_WEBRTC_CONFIG = "websocket:webrtcConfig:{group}";
    private static final int REDIS_TIMEOUT_WEBSOCKET_WEBRTC_CONFIG = 3600 * 24; // 缓存一天
    private static final String REDIS_KEY_DEVICE_KEEP_ALIVE_PARAMS = "websocket:keepAliveParams:{sn}";

//    @Setter
//    private static final Set<String> compatDeviceSns = new HashSet<>(Arrays.asList(
//            "f98af5080147855b2fa336b3dc05e28b", "a5766dcfeb9963a49ec432b67745d625"
//    ));

    /**
     * 获取直播的ticket
     *
     * @param userId
     * @param serialNumber
     * @param viewerId                  直播观看端id，生成accessToken用。app的clientId=userId；alexa的clientId="alexa-"+userId
     * @param supportUnlimitedWebsocket 直播观看端是否支持websocket长链接
     * @return
     */
    @SentinelResource("getWebrtcTicket")
    public P2PTicketResponse getWebrtcTicket(Integer userId, String serialNumber, String originTraceId, String viewerId
            , Boolean supportUnlimitedWebsocket) {
        return getWebrtcTicket(userId, serialNumber, originTraceId, viewerId, supportUnlimitedWebsocket, true);
    }

    @SentinelResource("getWebrtcTicket")
    public P2PTicketResponse getWebrtcTicket(Integer userId, String serialNumber, String originTraceId, String viewerId
            , Boolean supportUnlimitedWebsocket, Boolean needWakeupDevice) {
        String groupId = serialNumber;
        String traceId = StringUtils.isEmpty(originTraceId) ? IDUtil.timeBasedRandomId16("webrtc") : originTraceId;

        LiveLogUtil.setLocalLiveInfo(traceId, serialNumber, userId);
        LiveLogUtil.log(traceId, "getWebrtcTicket generate traceId:{}", traceId);

        // 获取signalServer地址
        SignalServer signalServer = kissService.getSignalServerAddr(serialNumber, false);

        // 获取iceServer地址
        List<IceServer> masterIceServer;
        List<IceServer> viewerIceServer;
        if (twilioConfig.getWhiteSnSet().contains(serialNumber)
                && !twilioService.getIceServerList().isEmpty()) {
            masterIceServer = twilioService.getIceServerList();
            viewerIceServer = masterIceServer;
        } else {
            // 一次直播，为什么要选两次点？
            masterIceServer = kissService.chooseTurnServer(serialNumber, serialNumber);
            viewerIceServer = kissService.chooseTurnServer(serialNumber, userId + "");
        }

        if (needWakeupDevice) {
            //设备唤醒状态
            LiveLogUtil.log(traceId, "wakeUpDevice");
            deviceInfoService.wakeUpDevice(serialNumber, traceId);
        }

        // 组装成ticket
        P2PTicketResponse masterTicket = new P2PTicketResponse(traceId, groupId, EPeerRole.MASTER,
                serialNumber, masterIceServer, signalServer.getAddr(), signalServer.getIpAddress(), signalServer.getSecret()
                , webrtcTicketValidDurationMillis);
        P2PTicketResponse viewerTicket = new P2PTicketResponse(traceId, groupId, EPeerRole.VIEWER,
                String.valueOf(userId), viewerIceServer, signalServer.getAddr(), signalServer.getIpAddress(), signalServer.getSecret()
                , webrtcTicketValidDurationMillis);
        if (supportUnlimitedWebsocket) {
            viewerTicket.setWebsocketPath(websocketPath).setAccessToken(createViewerAccessToken(userId, viewerId));
        }
        final CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(serialNumber);
        {
            final String redisKey = REDIS_KEY_WEBSOCKET_ACCESS_TOKEN.replace("{group}", groupId).replace("{id}", groupId);
            // 直播是否使用ws长链接：设备支持ws长链接 或 设备当前使用兼容逻辑
            boolean useUnlimitedWebsocket = cloudDeviceSupport.getSupportUnlimitedWebsocket()
                    || supportUnlimitedWebsocket || redisService.get(redisKey) != null;
            // 对于新版设备，采用redis传递iceServer。在APP端JOIN_LIVE之后和给设备端发PEER_IN之前发送给设备
            if (useUnlimitedWebsocket) {
                final String masterAccessToken = AccessTokenHelper.createForCamera(groupId);
                redisService.set(redisKey, masterAccessToken, REDIS_TIMEOUT_WEBSOCKET_ACCESS_TOKEN);
                if (!supportUnlimitedWebsocket) {
                    viewerTicket.attachAccessTokenToSign(createViewerAccessToken(userId, viewerId));
                }
                if (!cloudDeviceSupport.getSupportUnlimitedWebsocket()) {
                    masterTicket.attachAccessTokenToSign("");
                }
                kissService.closeOldGroup(groupId); // 收到设备支持ws长链接，关闭app在老直播分组中的ws链接
                createWebrtcConfig(groupId, masterIceServer);
            }
        }
        // 给设备下发WebRTC直播指令、并且把ticket返回给app
        LiveLogUtil.log(traceId, "sendWebrtcCmd masterTicket:{}", LiveLogUtil.getNoQuotationStr(masterTicket));
        mqttSender.sendRetainedMessage(EOutputTopicType.WEBRTC, serialNumber, MqttSendPackage.of("webrtc", masterTicket));
        // 只有不支持ws保活的设备，才等待webrtc消息的响应
        if (!cloudDeviceSupport.getSupportUnlimitedWebsocket()) {
            String operationId = "webrtc" + serialNumber;
            // 设备是否相应标记
            redisService.set(operationId, "", APP_LIVE_AWAKE_DEVICE_EXPIRE);
            //电量统计
            LiveLogUtil.log(traceId, "markDeviceStartLive");

            if (needWakeupDevice) {
                // 异步确认设备是否唤醒
                LiveLogUtil.log(traceId, "checkDeviceAwake");
                deviceAsyncService.checkDeviceAwake(serialNumber, traceId, operationId, "webrtc");
            }
        }
        LiveLogUtil.log(traceId, "return app viewerTicket:{}", LiveLogUtil.getNoQuotationStr(viewerTicket));
        LiveLogUtil.cleanLocalInfo();
        return viewerTicket;
    }

    private String createViewerAccessToken(Integer userId, String viewerId) {
        final List<String> liveGroups = userRoleService.getSerialNumbersByUserId(userId);
        return AccessTokenHelper.createForApp(viewerId, liveGroups);
    }

    /**
     * 设备获取websocket长链接所需要的参数
     *
     * @param request
     * @return
     */
    public WebsocketTicketVO getWebsocketTicket(WebsocketTicketRequest request) {
        // 获取signalServer地址
        final SignalServer signalServer = kissService.getSignalServerAddr(request.getSn(), true);
        final String modelNo = deviceManualService.getModelNoBySerialNumber(request.getSn());
        // 组装成ticket
        final WebsocketTicketVO websocketTicket = new WebsocketTicketVO()
                .setSignalServer(signalServer.getAddr()).setSignalServerIpAddress(signalServer.getIpAddress())
                .setWebsocketPath(websocketPath).setAccessToken(AccessTokenHelper.createForCamera(request.getSn()))
                .setKeepAliveParams(getKeepAliveParams(request.getSn(), modelNo));
        return websocketTicket;
    }

    // 获取保活参数
    public KeepAliveParams getKeepAliveParams(String sn, String modelNo) {
        final KeepAliveParams modelParams = getKeepAliveParamsByModelNo(modelNo);
        KeepAliveParams keepAliveParams = new KeepAliveParams()
                .setInterval(modelParams.getInterval()) // 61s
                .setTimeout(modelParams.getTimeout()) // 120s
                .setMaxInterval(modelParams.getMaxInterval())
                .setMinInterval(modelParams.getMinInterval())
                .setDtim(udpService.calculateDynamicDTIM(sn, modelNo))
                .setRetryInterval(Optional.ofNullable(modelParams.getRetryInterval()).orElse(wowconfig.getRetryInterval()))
                .setRetryCount(Optional.ofNullable(modelParams.getRetryCount()).orElse(wowconfig.getRetryCount()))
                .setBan1Ref(Optional.ofNullable(modelParams.getBan1Ref()).orElse(wowconfig.getBan1Ref()))
                .setBan2Ref(Optional.ofNullable(modelParams.getBan2Ref()).orElse(wowconfig.getBan2Ref()))
                .setBan3Ref(Optional.ofNullable(modelParams.getBan3Ref()).orElse(wowconfig.getBan3Ref()));

        this.initDeviceWowconfigParam(sn, keepAliveParams);
        return keepAliveParams;
    }

    /**
     * Init wowconfig param
     *
     * @param serialNumber
     * @param keepAliveParams
     */
    public void initDeviceWowconfigParam(String serialNumber, KeepAliveParams keepAliveParams) {
        //先验证sn 有无设置wowconfig
        Set<String> supportWowConfigCache = deviceWowConfigParamService.querySupportWowConfigCache();
        if (CollectionUtils.isEmpty(supportWowConfigCache) || !supportWowConfigCache.contains(serialNumber)) {
            return;
        }
        Optional.ofNullable(deviceWowConfigParamService.queryWowConfigCache(serialNumber)).ifPresent(wowconfigCache -> {
            Optional.ofNullable(wowconfigCache.getRetryInterval()).ifPresent(keepAliveParams::setRetryInterval);
            Optional.ofNullable(wowconfigCache.getRetryCount()).ifPresent(keepAliveParams::setRetryCount);
            Optional.ofNullable(wowconfigCache.getDtim()).ifPresent(keepAliveParams::setDtim);
            Optional.ofNullable(wowconfigCache.getInterval()).ifPresent(keepAliveParams::setInterval);
            Optional.ofNullable(wowconfigCache.getTimeout()).ifPresent(keepAliveParams::setTimeout);
        });
    }

    public JSONObject getWebrtcConfig(String serialNumber) {
        // 获取iceServer地址
        final List<IceServer> masterIceServer;
        if (twilioConfig.getWhiteSnSet().contains(serialNumber) && !twilioService.getIceServerList().isEmpty()) {
            masterIceServer = twilioService.getIceServerList();
        } else {
            masterIceServer = kissService.chooseTurnServer(serialNumber, serialNumber);
        }
        return createWebrtcConfig(serialNumber, masterIceServer);
    }

    private JSONObject createWebrtcConfig(String groupId, List<IceServer> iceServer) {
        // {"method":"WEBRTC_CONFIG", "maxAllocationLimit":32,"iceServers":[] }
        final JSONObject webrtcConfig = new JSONObject().fluentPut("method", "WEBRTC_CONFIG")
                .fluentPut("iceServers", iceServer).fluentPut("maxAllocationLimit", 32);
        final String redisKey = REDIS_KEY_WEBSOCKET_WEBRTC_CONFIG.replace("{group}", groupId);
        redisService.set(redisKey, JSON.toJSONString(webrtcConfig), REDIS_TIMEOUT_WEBSOCKET_WEBRTC_CONFIG);
        return webrtcConfig;
    }

    // Keep this for backward-compatible
    @Deprecated
    public Result startLive(String serialNumber) throws MqttException, IdNotSetException {
        StreamServerDO streamServerDO = wowzaService.getLiveWowzaServerWithLeastLoad(serialNumber);
        String serverIP = streamServerDO.getIp();
        String appName = streamServerDO.getAppName();
        String liveId = CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID().substring(0, 8);
        String fix = String.format("%s_%s", liveId, PhosUtils.getUTCStamp());
        String live_instance = String.format("%s_%s", fix, "live");
        String audio_instance = String.format("%s_%s", fix, "audio");

        LiveLogUtil.setLocalLiveInfo(liveId, serialNumber);

        // 记录操作
        redisService.setDeviceOperationDOWithEmpty(liveId);

        StartStreamingRequest startStreamingRequest = StartStreamingRequest.builder()
                .id(PhosUtils.getUUID())
                .time(PhosUtils.getUTCStamp())
                .protocol(streamServerDO.getProtocol())
                .host(streamServerDO.getIp())
                .app(streamServerDO.getAppName())
                .instance(String.format("%s_%s", fix, "live"))
                .size(streamServerDO.getSize())
                .duration(0)
                .build();

        // 发送开始直播的请求到MQTT
        LiveLogUtil.log(liveId, "startStreaming startStreamingRequest:{}", LiveLogUtil.getNoQuotationStr(startStreamingRequest));
        boolean operationRes = VernemqPublisher.startStreaming(serialNumber, startStreamingRequest);
        if (!operationRes) {
            return Result.OperationResult(false);
        }

        // 查询操作状态
        Result result = deviceOperationHelper.waitOperation(liveId);

        // success flag
        if (result.getResult().equals(Result.successFlag)) {
            LOGGER.info(String.format("Start live request: %s for serial number: %s succeeded. LiveID: %s",
                    startStreamingRequest.getId(),
                    serialNumber,
                    liveId));
            HashMap<String, Object> res = new HashMap<>();
            res.put("url", String.format("%s://%s/%s/%s", streamServerDO.getProtocol(), serverIP, appName, live_instance));
            res.put("liveUrl", String.format("%s://%s/%s/%s", streamServerDO.getProtocol(), serverIP, appName, live_instance));
            res.put("audioUrl", String.format("%s://%s/%s/%s", streamServerDO.getProtocol(), serverIP, appName, audio_instance));

            LiveLogUtil.log(liveId, "return app start live res:{}", LiveLogUtil.getNoQuotationStr(res));
            LiveLogUtil.cleanLocalInfo();
            return new Result(res);
        } else {
            LOGGER.info(String.format("Start live request: %s with serial number: %s failed.", startStreamingRequest.getId(), serialNumber));
        }
        return result;
    }

    public Result newStartLive(Integer userId, String serialNumber, String liveResolution) {
        DeviceStatusDO deviceStatusDO = deviceStatusService.queryDeviceStatusBySerialNumber(serialNumber);
        Map<String, Object> deviceLastConnectionInfoMap = getDeviceLastConnectionInfoForLog(deviceStatusDO);

        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        // 准备参数 TODO 目前取uuid前8位在高并发场景下会重复
        String liveId = PhosUtils.getUUID().substring(0, 8);
        String fix = String.format("%s_%s_%s", liveId, PhosUtils.getUTCStamp(), serialNumber);
        String live_instance = String.format("%s_%s", fix, "live");
        String audio_instance = String.format("%s_%s", fix, "au"); // shorten the url because of the IOS audio stream name length limitation
        Map<String, Object> commonLogInfoMap = MapUtil.builder()
                .put("userId", userId)
                .put("serialNumber", serialNumber)
                .put("liveId", liveId)
                .put("modelNo", modelNo)
                .build();

        LiveLogUtil.setLocalLiveInfo(liveId, serialNumber);

        // 记录开始开启直播
        reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_BEGIN_START_LIVE,
                MapUtil.builder().putAll(commonLogInfoMap)
                        .putAll(deviceLastConnectionInfoMap)
                        .put("time", PhosUtils.getUTCStamp())
                        .put("userProfile", userService.getUserProfileById(userId).getValue())
                        .build());

        StreamServerDO streamServerDO = wowzaService.getLiveWowzaServerWithLeastLoad(serialNumber);
        String serverIP = streamServerDO.getIp();
        String appName = streamServerDO.getAppName();

        // 记录申请到的wowza服务地址
        reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_GET_WOWZA_SERVER,
                MapUtil.builder().putAll(commonLogInfoMap)
                        .put("serverIp", serverIP)
                        .put("time", PhosUtils.getUTCStamp())
                        .build());

        // 唤醒设备
        LiveLogUtil.log(liveId, "call kissService wakeupDevice");
        kissService.wakeupDevice(serialNumber, liveId);

        StartStreamingRequest startStreamingRequest = StartStreamingRequest.builder()
                .id(CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID())
                .time(PhosUtils.getUTCStamp())
                .protocol(streamServerDO.getProtocol())
                .host(streamServerDO.getIp())
                .app(streamServerDO.getAppName())
                .instance(String.format("%s_%s", fix, "live"))
                .size(liveResolution)
                .duration(0)
                .build();

        String startStreamingRequestId = startStreamingRequest.getId();
        redisService.setDeviceOperationDOWithEmpty(startStreamingRequestId);

        // 发送udp唤醒设备
        try {
            // 发送开始直播的请求到MQTT
            Thread thread = new Thread(() -> {
                try {
                    boolean ack = false;
                    // 最多发送10秒start stream
                    for (int i = 0; i < 10 * 10; i++) {
                        if (i % 5 == 0) { // 每0.5秒发送一次
                            LiveLogUtil.log(liveId, "startStreaming startStreamingRequest:{}", LiveLogUtil.getNoQuotationStr(startStreamingRequest));
                            VernemqPublisher.startStreaming(serialNumber, startStreamingRequest);
                            LOGGER.info("Send start stream message to device {}", serialNumber);

                            // 记录发送mqtt消息，开启直播
                            reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_START_LIVING_LOOP_SEND,
                                    MapUtil.builder()
                                            .putAll(commonLogInfoMap)
                                            .put("msg", "发送mqtt消息开启直播，等待回复")
                                            .put("time", PhosUtils.getUTCStamp())
                                            .build());
                        }
                        // 每0.1 秒检查 startStreamingRequestId
                        DeviceOperationDO storedOperation = redisService.getDeviceOperationDo(startStreamingRequestId);
                        if (storedOperation != null) {
                            LOGGER.info(String.format("=========== Device %s has received startStream request with ID : %s.", serialNumber, startStreamingRequestId));
                            // 记录收到回复
                            reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_START_LIVE_LOOP_GET_ANSWER,
                                    MapUtil.builder()
                                            .putAll(commonLogInfoMap)
                                            .put("msg", "收到回复")
                                            .put("time", PhosUtils.getUTCStamp())
                                            .build());
                            redisService.dropDeviceOperationDo(startStreamingRequestId);
                            ack = true;
                            break;
                        }
                        Thread.sleep(100L);
                    }

                    LiveLogUtil.log(liveId, "startStreaming startStreamingRequestId:{} ack:{}", startStreamingRequestId, ack);
                    if (!ack) {
                        // 记录没有回复
                        reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_START_LIVE_LOOP_NO_ANSWER,
                                MapUtil.builder()
                                        .putAll(commonLogInfoMap)
                                        .put("msg", "超过10秒没有检测到回复")
                                        .put("time", PhosUtils.getUTCStamp())
                                        .build());
                    }
                } catch (Exception ex) {
                    com.addx.iotcamera.util.LogUtil.error(LOGGER, "Didn't receive response from device {} for start stream message", serialNumber);

                    // 记录发生异常
                    reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_START_LIVE_LOOP_EXCEPTION,
                            MapUtil.builder()
                                    .putAll(commonLogInfoMap)
                                    .put("msg", "发生异常:" + ex.getMessage())
                                    .put("time", PhosUtils.getUTCStamp())
                                    .build());
                }
            });

            thread.start();

            LOGGER.info(String.format("Start live request: %s for serial number: %s succeeded. LiveID: %s",
                    startStreamingRequest.getId(),
                    serialNumber,
                    liveId));

            // 记录返回直播地址
            reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_START_LIVE_RETURN,
                    MapUtil.builder()
                            .putAll(commonLogInfoMap)
                            .put("msg", "返回直播地址")
                            .put("time", PhosUtils.getUTCStamp())
                            .build());

            HashMap<String, Object> res = new HashMap<>();
            res.put("liveId", liveId);
            res.put("url", String.format("%s://%s/%s/%s", streamServerDO.getProtocol(), serverIP, appName, live_instance));
            res.put("liveUrl", String.format("%s://%s/%s/%s", streamServerDO.getProtocol(), serverIP, appName, live_instance));
            res.put("audioUrl", String.format("%s://%s/%s/%s", streamServerDO.getProtocol(), serverIP, appName, audio_instance));
            LiveLogUtil.log(liveId, "return app start live res:{}", LiveLogUtil.getNoQuotationStr(res));
            LiveLogUtil.cleanLocalInfo();
            return new Result(res);

        } catch (Exception ex) {
            // 记录异常发生
            reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_START_LIVE_FAIL,
                    MapUtil.builder()
                            .putAll(commonLogInfoMap)
                            .put("msg", "开启直播失败")
                            .put("time", PhosUtils.getUTCStamp())
                            .build());
            return ResultCollection.DEVICE_NO_RESPONSE.getResult();
        }
    }

    public Result startAudio(StartAudioRequest request) throws MqttException, IdNotSetException {

        StartVoiceRequest startVoiceRequest = buildStartVoiceRequest(request.getAudioUrl());

        // 记录操作
        redisService.setDeviceOperationDOWithEmpty(startVoiceRequest.getId());

        return publishAudioRequest(request.getSerialNumber(), startVoiceRequest);
    }

    @SentinelResource("stoplive")
    public Result stopLive(Integer userId, String serialNumber) throws MqttException, IdNotSetException {
        StopStreamingRequest stopStreamingRequest = new StopStreamingRequest();
        stopStreamingRequest.setId(CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID());
        stopStreamingRequest.setTime(PhosUtils.getUTCStamp());
        redisService.setDeviceOperationDOWithEmpty(stopStreamingRequest.getId());

        // 记录关闭直播
        reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_STOP_LIVE,
                MapUtil.builder().put("userId", userId).put("serialNumber", serialNumber).put("msg", "关闭直播").build());

        boolean operationRes = VernemqPublisher.stopStreaming(serialNumber, stopStreamingRequest);
        if (!operationRes) {
            return Result.OperationResult(false);
        }
        return deviceOperationHelper.waitOperation(stopStreamingRequest.getId());

    }

    @SentinelResource("stopaudio")
    public Result stopAudio(String serialNumber) throws MqttException, IdNotSetException {
        StopVoiceRequest stopVoiceRequest = new StopVoiceRequest();
        stopVoiceRequest.setId(CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID());
        stopVoiceRequest.setTime(PhosUtils.getUTCStamp());
        redisService.setDeviceOperationDOWithEmpty(stopVoiceRequest.getId());
        return publishAudioRequest(serialNumber, stopVoiceRequest);
    }

    public boolean isValidResolution(String resolution) {
        return !StringUtils.isEmpty(resolution) && ALLOWED_RESOLUTION.contains(resolution);
    }

    public Result changeLiveResolution(String serialNumber, String liveResolution) {
        if (publisher.changeLiveResolution(serialNumber, new ChangeLiveResolutionRequest(liveResolution))) {
            return Result.Success();
        } else {
            return Result.Failure(String.format("Failed to send change live resolution MQTT message to %s.", serialNumber));
        }
    }

    public Result playLocalVideo(PlayLocalVideoRequest playLocalVideoRequest) {
        String streamUrl = buildUrlForLocalVideoStreaming(playLocalVideoRequest);
        if (publisher.playLocalVideo(new DevicePlayLocalVideoRequest(playLocalVideoRequest, streamUrl))) {
            return new Result(streamUrl);
        } else {
            return Result.Failure(String.format("Failed to send play local video MQTT message to %s.", playLocalVideoRequest.getSerialNumber()));
        }
    }

    private String buildUrlForLocalVideoStreaming(PlayLocalVideoRequest playLocalVideoRequest) {
        String serialNumber = playLocalVideoRequest.getSerialNumber();

        StreamServerDO streamServerDO = wowzaService.getLiveWowzaServerWithLeastLoad(serialNumber);
        return streamServerDO.toUrl() + playLocalVideoRequest.getSerialNumber()
                + "_" + playLocalVideoRequest.getStartTime()
                + "_" + playLocalVideoRequest.getEndTime();
    }

    private Result publishAudioRequest(String serialNumber, Object audioRequest) throws MqttException, IdNotSetException {
        String requestId;
        boolean operationRes;

        if (audioRequest instanceof StopVoiceRequest) {
            operationRes = VernemqPublisher.stopVoice(serialNumber, (StopVoiceRequest) audioRequest);
            requestId = ((StopVoiceRequest) audioRequest).getId();
        } else if (audioRequest instanceof StartVoiceRequest) {
            operationRes = VernemqPublisher.startVoice(serialNumber, (StartVoiceRequest) audioRequest);
            requestId = ((StartVoiceRequest) audioRequest).getId();
        } else {
            throw new IllegalArgumentException("Request should be either StartVoiceRequest or StopVoiceRequest");
        }

        if (!operationRes) {
            return Result.OperationResult(false);
        }

        Result result = deviceOperationHelper.waitOperation(requestId);

        if (0 == result.getResult()) {
            LOGGER.info(String.format("Audio request with requestID %s succeeded", requestId));
        } else {
            LOGGER.info(String.format("Audio request with requestID %s failed. Message: %s", requestId, result.getMsg()));
        }

        // success flag
        if (result.getResult() == 0) {
            return new Result(audioRequest);
        }
        return result;
    }

    private StartVoiceRequest buildStartVoiceRequest(String audioUrl) {
        // example: rtmp://x.x.x.x/rec/AIC
        List<String> urlSplits = new ArrayList(Arrays.asList(audioUrl.split("[/:]")));

        // remove blank string
        urlSplits.removeIf(String::isEmpty);

        StartVoiceRequest startVoiceRequest = new StartVoiceRequest();
        startVoiceRequest.setId(CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID());
        startVoiceRequest.setTime(PhosUtils.getUTCStamp());
        startVoiceRequest.setProtocol(urlSplits.get(0));
        startVoiceRequest.setHost(urlSplits.get(1));
        startVoiceRequest.setApp(urlSplits.get(2));
        startVoiceRequest.setInstance(urlSplits.get(3));

        return startVoiceRequest;
    }

    /**
     * 获取设备上一次上报连接信息
     *
     * @param deviceStatus
     * @return
     */
    private Map<String, Object> getDeviceLastConnectionInfoForLog(DeviceStatusDO deviceStatus) {
        // TODO 这儿的log需要kiss提供接口获取了
        String lastHeartbeatTime = "";
        String lastHeartbeatProtocol = "";
        String mqttLastAwakeTime = "";
        String deviceIp = "";

        if (deviceStatus != null) {
            if (deviceStatus.getLastAwake() != null && deviceStatus.getLastAwake() > 0) {
                mqttLastAwakeTime = DateUtils.timestampSecs2utcTime(deviceStatus.getLastAwake());
            }
        }

        return MapUtil.builder()
                .put("lastHeartbeatTime", lastHeartbeatTime)
                .put("lastHeartbeatProtocol", lastHeartbeatProtocol)
                .put("mqttLastAwakeTime", mqttLastAwakeTime)
                .put("deviceIp", deviceIp)
                .build();
    }


    /**
     * 从直播url中获取liveId
     *
     * @param liveUrl
     * @return
     */
    public String getLiveIdFromLiveUrl(String liveUrl) {
        try {
            int beginIndex = liveUrl.lastIndexOf("/") + 1;
            return liveUrl.substring(beginIndex, beginIndex + 8);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, e.getMessage(), e);
            return "";
        }
    }


    public int updateKeepAliveParamsIfChange(String serialNumber, String modelNo, boolean checkChange) {
        final CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(serialNumber);
        final KeepAliveParams params = getKeepAliveParams(serialNumber, modelNo);
        if (!cloudDeviceSupport.getSupportUnlimitedWebsocket()) return 1;
        final String redisKey = REDIS_KEY_DEVICE_KEEP_ALIVE_PARAMS.replace("{sn}", serialNumber);
        if (checkChange) {
            final KeepAliveParams oldParams = Optional.ofNullable(redisService.get(redisKey))
                    .map(it -> JSON.parseObject(it, KeepAliveParams.class)).orElse(null);
            if (params.equals(oldParams)) return 2; // 值不变，不用更新
        }
        if (!kissService.updateKeepAliveParams(serialNumber, params)) return 3;
        redisService.set(redisKey, JSON.toJSONString(params));
        return 0;
    }

    public KeepAliveParams getKeepAliveParamsByModelNo(String modelNo) {
        final KeepAliveParams rawKeepAliveParams = deviceKeepAliveConfig.getConfigByModelNo(modelNo);
        final KeepAliveParams keepAliveParams = BeanUtil.override(rawKeepAliveParams, new KeepAliveParams());
        final DeviceModel deviceModel = Optional.ofNullable(modelNo).filter(StringUtils::isNotBlank)
                .map(deviceModelConfigService::queryRowDeviceModelByModelNo).orElse(null);
        if (deviceModel != null) {
            if (deviceModel.getWsKeepAliveInterval() != null && deviceModel.getWsKeepAliveTimeout() != null) {
                keepAliveParams.setInterval(deviceModel.getWsKeepAliveInterval()).setTimeout(deviceModel.getWsKeepAliveTimeout());
            }
            if (DeviceModel.validateIntervalRange(deviceModel.getMinWsKeepAliveInterval(), deviceModel.getMaxWsKeepAliveInterval())) {
                keepAliveParams.setMinInterval(deviceModel.getMinWsKeepAliveInterval()).setMaxInterval(deviceModel.getMaxWsKeepAliveInterval());
            }
        }
        // 如果配置的interval范围不合法，则使用现有的interval值
        if (!DeviceModel.validateIntervalRange(keepAliveParams.getMinInterval(), keepAliveParams.getMaxInterval())) {
            keepAliveParams.setMinInterval(keepAliveParams.getInterval()).setMaxInterval(keepAliveParams.getInterval());
        }
        return keepAliveParams;
    }

}
