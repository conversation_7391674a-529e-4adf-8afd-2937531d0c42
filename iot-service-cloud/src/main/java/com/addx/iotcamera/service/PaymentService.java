package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.payment.ALiPaymentRequest;
import com.addx.iotcamera.bean.app.payment.ApplePaymentRequest;
import com.addx.iotcamera.bean.app.payment.GooglePaymentRequest;
import com.addx.iotcamera.bean.app.payment.PaymentRequest;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.UserTierDeviceDO;
import com.addx.iotcamera.bean.db.additional_tier.AdditionalUserTierDO;
import com.addx.iotcamera.bean.db.pay.*;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.pay.GoogleCancelOrderDO;
import com.addx.iotcamera.bean.domain.pay.OrderVerifyResultDO;
import com.addx.iotcamera.bean.domain.pay.PaymentConfig;
import com.addx.iotcamera.bean.domain.pay.SubscriptionPurchaseV2;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.response.user.UserDeviceCanChooseDO;
import com.addx.iotcamera.config.apollo.GlobalConfig;
import com.addx.iotcamera.config.apollo.MailConfig;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.config.app.EmailPayConfig;
import com.addx.iotcamera.config.pay.GooglePaySandboxConfig;
import com.addx.iotcamera.config.pay.PaymentCenterConfig;
import com.addx.iotcamera.config.serve.ServeConfig;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.dao.additional_tier.AdditionalUserTierDao;
import com.addx.iotcamera.dao.pay.*;
import com.addx.iotcamera.dao.redis.PayRedis;
import com.addx.iotcamera.enums.*;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.enums.pay.TierTypeEnums;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import com.addx.iotcamera.service.pay.*;
import com.addx.iotcamera.service.user.UserAppScoreService;
import com.addx.iotcamera.service.vip.TierGroupService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.*;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.StringUtils;
import com.alipay.api.response.AlipayDataDataserviceBillDownloadurlQueryResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.github.wxpay.sdk.WXPayUtil;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Sets;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.PayConstants.DEFAULT_ROOLING_DAY;
import static com.addx.iotcamera.constants.PayConstants.SUB_TIER_PRODUCT_SET;
import static com.addx.iotcamera.constants.ReportLogConstants.REPORT_TYPE_PAY_PARAM_EROOR;
import static com.addx.iotcamera.enums.user.UserAppScoreMomentEnum.USER_FIRST_PAYMENT;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.addx.iot.common.constant.AppConstants.TIER_DEVICE_LIMIT_APP;
import static org.addx.iot.common.enums.ResultCollection.*;
import static org.addx.iot.common.vo.Result.failureFlag;

@Service
@Slf4j
public class PaymentService {
    private final static Logger logger = LoggerFactory.getLogger(PaymentService.class);

    @Autowired
    private IPaymentFlowDAO iPaymentFlowDAO;

    @Autowired
    private PayRedis payRedis;

    @Autowired
    private ProductService productService;

    @Resource
    private ServeConfig serveConfig;

    @Autowired
    private IOrderDAO iOrderDAO;
    @Autowired
    private IOrderProductDAO iOrderProductDAO;

    @Autowired
    private IUserVipDAO iUserVipDAO;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private WxPayService wxPayService;
    @Autowired
    @Lazy
    private ApplePayService applePayService;
    @Autowired
    private AliPayService aliPayService;
    @Autowired
    private GooglePayService googlePayService;

    @Autowired
    @Lazy
    private NotificationService notificationService;
    private final static String key = "payment_user_{userId}";

    @Autowired
    private UserVipService userVipService;

    @Autowired
    private PaymentCenterConfig paymentCenterConfig;

    @Autowired
    private IUserDAO iUserDAO;

    @Autowired
    private MailConfig mailConfig;

    @Autowired
    private ReportLogService reportLogService;

    @Value("${codebind.node}")
    private String envNode;

    @Autowired
    private MomoPayService momoPayService;

    @Autowired
    private AppAccountConfig appAccountConfig;

    @Resource
    private EmailPayConfig emailPayConfig;

    @Resource
    private UserService userService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private DeviceConfigService deviceConfigService;

    @Autowired
    private IUserPaymentTimeSnDAO iUserPaymentTimeSnDAO;

    @Resource
    private UserVipActivateService userVipActivateService;

    @Autowired
    private TierService tierService;

    @Autowired
    private TierGroupService tierGroupService;

    @Autowired
    private AdditionalUserTierDao additionalUserTierDao;

    @Resource
    private UserAppScoreService userAppScoreService;

    @Resource
    private GlobalConfig globalConfig;

    @Resource
    private GooglePaySandboxConfig googlePaySandboxConfig;

    @Resource
    private UserTierDeviceService userTierDeviceService;

    @Resource
    private DeviceInfoService deviceInfoService;

    @Resource
    private DeviceManualService deviceManualService;

    @Resource
    private DeviceModelIconService deviceModelIconService;

    @Resource
    private AdditionalUserTierService additionalUserTierService;

    @Resource
    private IPurchaseSelectDeviceDAO iPurchaseSelectDeviceDAO;

    /**
     * 支付前的订单创建
     *
     * @param userId
     * @param paymentRequest
     * @param paymentType
     * @return
     */
    @Transactional
    public Result pay(Integer userId, PaymentRequest paymentRequest, PaymentTypeEnums paymentType) {
        Result result;
        try {
            ProductDO productDO = productService.queryProductById(paymentRequest.getProductId());
            if (productDO == null) {
                com.addx.iotcamera.util.LogUtil.error(logger, "paymentOrder 商品id错误, userId:{},productId:{}", userId, paymentRequest.getProductId());
                this.sysReportPaymentError(userId, paymentType.getCode(), PaymentFlagEnums.SUBMIT.getCode());
                return Result.Error(INVALID_PARAMS, "参数错误");
            }

            //校验商品信息
            if (!verifyPayment(userId, paymentRequest, productDO)) {
                this.sysReportPaymentError(userId, paymentType.getCode(), PaymentFlagEnums.SUBMIT.getCode());
                return Result.Error(INVALID_PARAMS, "参数验证错误");
            }

            if (paymentType.getCode().equals(PaymentTypeEnums.APPLE.getCode())
                    || paymentType.getCode().equals(PaymentTypeEnums.GOOGLE.getCode())) {
                //IOS 判断问题，暂时保留
                return new Result("success");
            }

            paymentRequest.setType(paymentType.getCode());

            //生成订单信息
            OrderDO orderDO = initOrder(userId, paymentRequest, productDO, false);
            if (orderDO == null) {
                this.sysReportPaymentError(userId, PaymentTypeEnums.APPLE.getCode(), PaymentFlagEnums.SUBMIT.getCode());
                return OPERTION_DB_ERROR.getResult();
            }

            String orderInfo = getOrderInfoSign(orderDO, productDO, paymentType);
            result = orderInfo == null ? OPERTION_DB_ERROR.getResult() : new Result(orderInfo);

        } catch (Exception e) {
            this.sysReportPaymentError(userId, paymentType.getCode(), PaymentFlagEnums.SUBMIT.getCode());
            com.addx.iotcamera.util.LogUtil.error(logger, "paymentOrder error,userId:{},{}", userId, e);
            throw new RuntimeException(String.format("写入订单信息异常:【%s】,【%s】,【%s】", userId, paymentRequest.toString(), paymentType.getCode()));
        }

        return result;
    }

    /**
     * 校验套餐购买条件
     *
     *
     * @param userId
     * @param paymentRequest
     * @param productDO
     * @return
     */
    private boolean verifyPayment(Integer userId, PaymentRequest paymentRequest, ProductDO productDO) {
        UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, paymentRequest.getLanguage(),
                paymentRequest.getApp().getTenantId());

        if (userVipTier.isVip() && productDO.getType().equals(1) && (productDO.getTierId() >= 0 && productDO.getTierId() <= userVipTier.getTierId())) {
            com.addx.iotcamera.util.LogUtil.error(logger, "paymentOrder 不是允许升级的商品:userId:{},productId:{}", userId, productDO.getId());
            return false;
        }
        return true;
    }

    /**
     * 生成订单信息
     *
     * @param userId
     * @param paymentRequest
     * @param productDO
     * @return
     * @throws Exception
     */

    public OrderDO initOrder(Integer userId, PaymentRequest paymentRequest, ProductDO productDO, boolean sandbox) throws Exception {

        String tenant = paymentRequest.getApp().getTenantId();
        int timeStart = (int) (System.currentTimeMillis() / 1000);
        //组装订单信息
        String orderSn = flowSn(tenant);
        OrderDO orderDO = OrderDO.builder()
                .orderSn(orderSn)
                .userId(userId)
                .orderType(paymentRequest.getType())
                .status(PaymentStatusEnum.TOBEPAID.getCode())
                .timeStart(timeStart)
                .timeEnd(timeStart)
                .cdate(timeStart)
                .mdate(timeStart)
                .tenantId(tenant)
                .sandbox(sandbox ? 1 : 0)
                .productMonth(productDO.getMonth())
                .guidanceSource(paymentRequest.getGuidanceSource())
                .orderInfo(paymentRequest.getOrderInfo())
                .orderInfoV2(paymentRequest.getOrderInfoV2())
                .build();

        //微信支付需要先 生成预支付订单
        if (paymentRequest.getType().equals(PaymentTypeEnums.WECHAT.getCode())) {
            orderDO = orderExtendInfo(orderDO, productDO, paymentRequest);
        }

        if (paymentRequest.getType().equals(PaymentTypeEnums.APPLE.getCode())
                || paymentRequest.getType().equals(PaymentTypeEnums.GOOGLE.getCode())) {
            orderDO.setExtend(paymentRequest.getExtend());
        }

        if (SUB_TIER_PRODUCT_SET.contains(productDO.getType())) {
            orderDO.setSubType(OrderSubTypeEnums.SUBSCRIBE.getCode());
        } else {
            orderDO.setSubType(OrderSubTypeEnums.NOMAL.getCode());
        }

        //写入订单信息
        iOrderDAO.insertOrder(orderDO);

        //组装订单商品信息
        OrderProductDo orderProductDo = OrderProductDo.builder()
                .orderId(orderDO.getId())
                .productId(productDO.getId())
                .subscriptionGroupId(paymentRequest.getSubscriptionGroupId())
                .body(productDO.getBody())
                .subject(productDO.getSubject())
                .cdate(timeStart)
                .num(1)
                .price(productDO.getPrice())
                .build();
        //写入订单商品表
        iOrderProductDAO.insertOrderProduct(orderProductDo);
        return orderDO;
    }

    /**
     * 微信订单获取pre订单ID
     *
     * @param orderDO
     * @param productDO
     * @param paymentRequest
     * @return
     * @throws Exception
     */
    private OrderDO orderExtendInfo(OrderDO orderDO, ProductDO productDO, PaymentRequest paymentRequest) throws Exception {
        String nonce_str = String.valueOf(System.currentTimeMillis());
        JSONObject obj = JSONObject.parseObject(paymentRequest.getExtend());
        obj.put("nonce_str", nonce_str);
        orderDO.setExtend(obj.toJSONString());

        String prePayId = wxPayService.prePayOrder(orderDO, productDO);
        if (prePayId == null) {
            throw new RuntimeException("wxpay pay.sql error");
        }
        obj.put("prepayid", prePayId);
        orderDO.setTradeNo(prePayId);
        orderDO.setExtend(obj.toJSONString());
        return orderDO;
    }


    /**
     * 初始化交易流水对象
     *
     * @param orderDO
     * @param orderProductDo
     * @return
     */

    public PaymentFlow initPayment(OrderDO orderDO, OrderProductDo orderProductDo, String returnInfo,OrderVerifyResultDO verifyResultDO) {
        return this.initPayment(orderDO,orderProductDo,returnInfo,verifyResultDO,false);
    }
    public PaymentFlow initPayment(OrderDO orderDO, OrderProductDo orderProductDo,
                                   String returnInfo,OrderVerifyResultDO verifyResultDO,
                                   Boolean autoRenew) {
        User user = userService.queryUserById(orderDO.getUserId());
        int time = (int) (System.currentTimeMillis() / 1000);
        PaymentFlow pay = PaymentFlow.builder()
                .outTradeNo(orderDO.getOrderSn())
                .tradeNo(orderDO.getTradeNo())
                .type(orderDO.getOrderType())
                .userId(orderDO.getUserId())
                .productId(orderProductDo.getProductId())
                .productSubject(orderProductDo.getSubject())
                .productBody(orderProductDo.getBody())
                .amount((verifyResultDO != null && (Boolean.TRUE.equals(verifyResultDO.getIsUpgradedOrder()) || Boolean.TRUE.equals(verifyResultDO.getIsTestOrder()))) ? 0 : orderProductDo.getPrice())
                .currency(0)
                .timeStart(orderDO.getTimeStart())
                .timeEnd(orderDO.getTimeEnd())
                .status(orderDO.getStatus())
                .tradeType(0)
                .extend(orderDO.getExtend())
                .returnInfo(returnInfo)
                .tenantId(orderDO.getTenantId())
                .cdate(time)
                .mdate(time)
                .appVersionName(user == null ? "" : user.getAppVersionName())
                .serveVersion(serveConfig.getServeReleaseTag())
                .freeTrial(orderDO.getFreeTrial() == null ? 0 : orderDO.getFreeTrial())
                .purchaseTime(verifyResultDO == null ? time : verifyResultDO.getPurchaseTime())
                .purchaseDatePst(verifyResultDO == null ? DateUtils.timeStamp2Date(time,DateUtils.YYYY_MM_DD_HH_MM_SS) : verifyResultDO.getPurchaseDatePst())
                .purchaseDate(verifyResultDO == null ? DateUtils.timeStamp2Date(time,DateUtils.YYYY_MM_DD_HH_MM_SS) : verifyResultDO.getPurchaseDate())
                .expireTime(verifyResultDO == null || verifyResultDO.getExpireTime() == null ? 0 : verifyResultDO.getExpireTime())
                .orderInfo(orderDO.getOrderInfo())
                .orderInfoV2(orderDO.getOrderInfoV2())
                .build();
        logger.info("PaymentFlow param:{}", pay.toString());
        iPaymentFlowDAO.insertPayment(pay);


        //支付时设备
        this.initPaymentTimePurchaseDevice(orderDO,pay,autoRenew);

        return pay;
    }

    /**
     * 计算支付时指定的设备、分成会使用
     * @param orderDO
     * @param paymentFlow
     */
    private void initPaymentTimePurchaseDevice(OrderDO orderDO,PaymentFlow paymentFlow,boolean autoRenew){
        ProductDO productDO = productService.queryProductById(paymentFlow.getProductId());
        Tier tier = tierService.queryTierById(productDO.getTierId());
        //用户当前绑定设备
        List<DeviceDO> deviceDOList = userTierDeviceService.queryUserAdminDevice(paymentFlow.getUserId(),paymentFlow.getTenantId());
        if(CollectionUtils.isEmpty(deviceDOList)){
            // 不存在admin所属设备
            return;
        }
        //支付时选中的设备,
        //连续订阅时默认当前套餐生效的设备,
        //购买时以选中的为准
        List<String> purchaseDeviceSn = autoRenew ?
                userTierDeviceService.queryUserTierDeviceDOListByUser(paymentFlow.getUserId(),paymentFlow.getTenantId()).stream()
                        .filter(userTierDeviceDO -> userTierDeviceDO.getTierId().equals(tier.getTierId()))
                        .map(UserTierDeviceDO::getSerialNumber)
                        .collect(Collectors.toList()) :
                userTierDeviceService.initUserTierDeviceList(orderDO,productDO.getTierId(),deviceDOList);
        if(!CollectionUtils.isEmpty(purchaseDeviceSn)){
            List<PurchaseSelectDeviceDO> purchaseSelectDeviceDOList = purchaseDeviceSn.stream().map(
                    sn ->   PurchaseSelectDeviceDO.builder()
                                .tradeNo(paymentFlow.getTradeNo())
                                .serialNumber(sn)
                                .build()
            ).collect(Collectors.toList());
            iPurchaseSelectDeviceDAO.insertPurchaseSelectDevice(purchaseSelectDeviceDOList);
        }else{
            logger.error("支付时选中设备为空");
        }

        // 记录时刻用户所有设备
        deviceDOList = userTierDeviceService.filterOutDeviceVip(paymentFlow.getTenantId(),  deviceDOList);
        List<UserpaymentTimeSnDO> userpaymentTimeSnDOList = deviceDOList.stream()
                .filter(deviceDO -> deviceDO.getAdminId().equals(deviceDO.getUserId()))
                .filter(deviceDO -> deviceInfoService.checkIfDeviceUse4G(deviceDO.getSerialNumber()) == TierServiceTypeEnums.TIER_4G.getCode().equals(tier.getTierServiceType()))
                .filter(deviceDO -> Optional.ofNullable(deviceDO.getSimThirdParty()).orElse(0).equals(0))
                .map(
                    deviceDO ->   UserpaymentTimeSnDO.builder()
                        .tradeNo(paymentFlow.getTradeNo())
                        .serialNumber(deviceDO.getSerialNumber())
                        .build()
        ).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(userpaymentTimeSnDOList)){
            iUserPaymentTimeSnDAO.insertUserPaymentTimeSn(userpaymentTimeSnDOList);
        }
    }


    /**
     * 生成订单信息并签名
     *
     * @param orderDO
     * @param productDO
     * @param paymentType
     * @return
     */
    private String getOrderInfoSign(OrderDO orderDO, ProductDO productDO, PaymentTypeEnums paymentType) throws Exception {
        String orderInfo = null;
        switch (paymentType) {
            case ALI:
                orderInfo = aliPayService.aliOrderInfo(orderDO, productDO);
                break;
            case WECHAT:
                orderInfo = wxPayService.wechatOrderInfo(orderDO);
                break;
            case APPLE:
            case GOOGLE:
                orderInfo = orderDO.getOrderSn();
                break;
            default:
                break;
        }
        return orderInfo;
    }

    /**
     * 生成支付流水号--tenantId前两个字符+时间戳+当天支付数量
     *
     * @param tenant
     * @return
     */
    private String flowSn(String tenant) {
        tenant = tenant.length() > 2 ? tenant.substring(0, 2) : tenant;
        Date date = new Date();
        tenant += DateUtils.dateToString(date, DateUtils.YYYYMMDDHHMMSS) +
                String.format("%05d", payRedis.incre(DateUtils.dateToString(date, DateUtils.YYYYMMDD)));
        return tenant;
    }


    /**
     * 异步通知
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean changePaymentStatus(Map<String, String> param) {
        boolean complete = false;
        String outTradeNo = param.get("out_trade_no");
        String redisKey = key.replace("{userId}", outTradeNo);
        long lock = redisUtil.tryLock(redisKey);
        try {
            if (lock == -1) {
                com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult redisLock notify error:{}", outTradeNo);
                complete = false;
            }

            OrderDO orderDO = iOrderDAO.queryByOrderSn(outTradeNo);
            if (orderDO == null) {
                com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult notify error,order null:{}", outTradeNo);
                return false;
            }
            if (orderDO.getStatus().equals(PaymentStatusEnum.PAYMENTED.getCode())) {
                com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult notify 订单状态为已支付:{}", outTradeNo);
                return true;
            }

            OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
            if (orderProductDo == null) {
                com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult notify error,orderProductDo null:{}", orderDO.getId());
                return false;
            }

            complete = checkPaymentFlowInfo(orderDO.getOrderSn(), param.get("total_amount"), param.get("app_id"), orderProductDo,
                    orderDO.getTenantId());
            if (complete) {
                orderDO.setTradeNo(param.get("trade_no"));
                initOrderComplete(orderDO, orderProductDo, orderDO.getTradeNo());
                complete = true;
            }

        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult支付异步通知error:{}", e);
        } finally {
            redisUtil.unlock(redisKey, lock);
        }
        return complete;
    }

    /**
     * 验证收到支付返回是否正确
     *
     * @param aLiPaymentRequest
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"userIsVip"}, key = "#userId")
    public boolean orderResult(ALiPaymentRequest aLiPaymentRequest, Integer userId) {
        boolean complete = false;
        String outTradeNo;
        String totalAmount;
        String appid;
        OrderDO orderDO;
        logger.info("aliPayParams:userId:{},{}", userId, aLiPaymentRequest.toString());
        if (!"9000".equals(aLiPaymentRequest.getResultStatus())) {
            return false;
        }
        JSONObject payResult = JSONObject.parseObject(aLiPaymentRequest.getResult());
        JSONObject para = payResult.getJSONObject("alipay_trade_app_pay_response");

        outTradeNo = para.getString("out_trade_no");
        if (StringUtils.isEmpty(outTradeNo)) {
            com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult error:out_trade_no订单不存在:{}", outTradeNo);
            return false;
        }

        String redisKey = key.replace("{userId}", outTradeNo);
        long lock = 0;
        try {
            lock = redisUtil.tryLock(redisKey);
            if (lock == -1) {
                com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult redisLock error:{}", outTradeNo);
                return false;
            }

            orderDO = iOrderDAO.queryByOrderSn(outTradeNo);
            if (orderDO == null) {
                com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult orderDO null error:{}", outTradeNo);
                return false;
            }

            if (orderDO.getStatus().equals(PaymentStatusEnum.PAYMENTED.getCode())) {
                com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult orderDO 已支付完成:{}", outTradeNo);
                return true;
            }

            //支付订单查询
            AlipayTradeQueryResponse response = aliPayService.queryAliOrderResult(orderDO.getOrderSn());
            if (response.isSuccess()) {
                if (response.getTradeStatus().equals("TRADE_SUCCESS")) {
                    orderDO.setTradeNo(response.getTradeNo());
                    complete = true;
                }
            }

            if (!complete) {
                logger.info("aliPayResult  订单验证不通过;{}", outTradeNo);
            }

            outTradeNo = para.getString("out_trade_no");
            totalAmount = para.getString("total_amount");
            appid = para.getString("app_id");

            OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
            if (orderProductDo != null) {
                //验证支付信息是否一致
                complete = checkPaymentFlowInfo(outTradeNo, totalAmount, appid, orderProductDo, orderDO.getTenantId());
            }
            if (complete) {
                //支付完成
                logger.info("aliPayResult  订单验证完毕，开始操作数据库;{}", outTradeNo);
                initOrderComplete(orderDO, orderProductDo, orderDO.getTradeNo());
            }

        } catch (Exception e) {
            complete = false;
            com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult error:{}", e);
            throw new RuntimeException("aliPayResult error:{}", e);
        } finally {
            redisUtil.unlock(redisKey, lock);
        }
        return complete;
    }


    /**
     * 验证支付宝返回与本地存储记录是否一致
     *
     * @param outTradeNo
     * @param totalAmount
     * @param appid
     * @return
     */
    private boolean checkPaymentFlowInfo(String outTradeNo, String totalAmount, String appid, OrderProductDo orderProductDo, String tenantId) {
        //支付金额相等
        BigDecimal totalAmountLongValue = new BigDecimal(totalAmount).multiply(new BigDecimal(100));
        if (!orderProductDo.getPrice().equals(totalAmountLongValue.intValue())) {
            com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult error,anount 不相等,outTradeNo:【{}】返回值:【{}】,数据库值:【{}】", outTradeNo,
                    totalAmountLongValue, orderProductDo.getPrice());
            return false;
        }

        //根据tenantId 获取各app各自的参数
        PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(tenantId);
        if (!paymentConfig.getAliAppId().equals(appid)) {
            com.addx.iotcamera.util.LogUtil.error(logger, "aliPayResult error,appid 不相等,返回appid:【{}】,配置值:【{}】", appid,
                    paymentConfig.getAliAppId());
            return false;
        }
        return true;
    }

    /**
     * 微信支付异步通知
     *
     * @param request
     * @param response
     */
    @Transactional(rollbackFor = Exception.class)
    public void wxPayNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String content = wxPayService.getWxNotifyContent(request);
        Map<String, String> notifyMap = WXPayUtil.xmlToMap(content);
        String outTradeNo = notifyMap.get("out_trade_no");
        String redisKey = key.replace("{userId}", outTradeNo);

        long lock = redisUtil.tryLock(redisKey);
        if (lock == -1) {
            this.sysReportPaymentError(null, PaymentTypeEnums.WECHAT.getCode(), PaymentFlagEnums.VERIFY.getCode());
            com.addx.iotcamera.util.LogUtil.error(logger, "wxpayNotify redisLock error:{}", outTradeNo);
            return;
        }

        try {
            OrderDO orderDO = iOrderDAO.queryByOrderSn(outTradeNo);

            boolean verify = wxPayService.wxPayResultVerify(notifyMap, orderDO);
            if (!verify) {
                this.sysReportPaymentError(orderDO.getUserId(), PaymentTypeEnums.WECHAT.getCode(), PaymentFlagEnums.VERIFY.getCode());
                com.addx.iotcamera.util.LogUtil.error(logger, "wxpayNotify 通知验证错误:{}", content);
                return;
            }
            logger.info("wxpayNotify 签名验证通过{}", outTradeNo);

            if (orderDO.getStatus().equals(PaymentStatusEnum.PAYMENTED.getCode())) {
                logger.info("wxpayNotify 订单已支付完成:{}", outTradeNo);
                wxPayService.wxPayResponse(response);
                return;
            }
            OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
            if (orderProductDo == null) {
                this.sysReportPaymentError(orderDO.getUserId(), PaymentTypeEnums.WECHAT.getCode(), PaymentFlagEnums.VERIFY.getCode());
                com.addx.iotcamera.util.LogUtil.error(logger, "wxpayNotify error orderProductDo null:{}", outTradeNo);
                return;
            }

            //将订单置位支付成功
            //orderDO.setTradeNo(notifyMap.get("transaction_id"));

            //初始化vip操作
            initOrderComplete(orderDO, orderProductDo, orderDO.getTradeNo());

            //wxPay应答
            wxPayService.wxPayResponse(response);
            logger.info("wxpayNotify_info success:{}", outTradeNo);
        } catch (Exception e) {
            this.sysReportPaymentError(null, PaymentTypeEnums.WECHAT.getCode(), PaymentFlagEnums.VERIFY.getCode());
            com.addx.iotcamera.util.LogUtil.error(logger, "wxpayNotify error:{}", e);
            throw new RuntimeException("wxpayNotify 异步通知异常");
        } finally {
            redisUtil.unlock(redisKey, lock);
        }
    }


    /**
     * 验证订单是否支付完成
     *
     * @param paymentRequest
     * @return
     */
    @CacheEvict(value = {"userIsVip"}, key = "#userId")
    public Result wxPayCheck(PaymentRequest paymentRequest, Integer userId) {
        boolean complete = false;
        String redisKey = key.replace("{userId}", paymentRequest.getOutTradeNo());
        long lock = 0;
        try {
            if (StringUtils.isEmpty(paymentRequest.getOutTradeNo())) {
                com.addx.iotcamera.util.LogUtil.error(logger, "wxpayPay error,订单号为空:{},userId:{}", paymentRequest.getOutTradeNo(), userId);
                return Result.Error(INVALID_PARAMS, "outTradeNo 不能为空");
            }

            lock = redisUtil.tryLock(redisKey);
            logger.info("wxpayPay 验证数据:{},userId:{}", paymentRequest.getOutTradeNo(), userId);
            complete = initWxPayVip(lock, paymentRequest.getOutTradeNo(), paymentRequest);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(logger, "wxpay orderInfo check error:{}", e);
            throw new RuntimeException(e.getMessage());
        } finally {
            redisUtil.unlock(redisKey, lock);
        }
        return new Result(complete);
    }


    /**
     * 校验是否可以执行初始化
     *
     * @param lock
     * @param outTradeNo
     * @return
     * @throws Exception
     */
    private boolean initWxPayVip(long lock, String outTradeNo, PaymentRequest paymentRequest) throws Exception {
        if (lock == -1) {
            com.addx.iotcamera.util.LogUtil.error(logger, "wxpayPay redisLock error:{}", outTradeNo);
            return false;
        }

        OrderDO orderDO = iOrderDAO.queryBytradeNo(outTradeNo);
        if (orderDO == null) {
            com.addx.iotcamera.util.LogUtil.error(logger, "wxpayPay order null:{}", outTradeNo);
            return false;
        }

        if (!orderDO.getStatus().equals(OrderStatusTypeEnums.CREAT.getCode())) {
            logger.info("wxpayPay 订单状态已支付:{}", orderDO.getStatus());
            return true;
        }

        OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
        if (orderProductDo == null) {
            com.addx.iotcamera.util.LogUtil.error(logger, "wxpayPay orderProductDo null:{}", outTradeNo);
            return false;
        }

        //校验微信订单是否结束
        boolean complete = wxPayService.wechatOrderCompleteQeury(orderDO);
        logger.info("wxpayPay 验证结果:{}", outTradeNo);
        if (complete) {
            //订单支付完成，初始化数据
            initOrderComplete(orderDO, orderProductDo, paymentRequest.getTradeNo());
        }
        return complete;
    }

    /**
     * 订单支付完成,初始化操作
     *
     * @param orderDO
     * @return
     */
    private void initOrderComplete(OrderDO orderDO, OrderProductDo orderProductDo, String originTradeNo,OrderVerifyResultDO verifyResultDO) throws Exception {
        orderDO.setStatus(PaymentStatusEnum.PAYMENTED.getCode());
        int time = (int) (System.currentTimeMillis() / 1000);
        orderDO.setMdate(time);

        //记录支付流水
        initPayment(orderDO, orderProductDo, "",verifyResultDO);

        //生成vip记录
        initUserVip(orderDO, orderProductDo,verifyResultDO);

        //初始化ai通知设置
        notificationService.initDeviceAiSettingAndNotifySetting(orderDO.getUserId());

        orderDO.setTradeNo(originTradeNo);
        //将订单置位支付成功
        iOrderDAO.updateOrderStatus(orderDO);

        this.verifyPaymentCount(orderDO.getUserId(),orderDO.getTradeNo());
    }

    private void initOrderComplete(OrderDO orderDO, OrderProductDo orderProductDo, String originTradeNo) throws Exception {
        this.initOrderComplete(orderDO,orderProductDo,originTradeNo,null);
    }

    /**
     * 只第一次购买需要验证
     * @param userId
     * @param tradeNo
     */
    public void verifyPaymentCount(Integer userId,String tradeNo){
        int paymentCount = iPaymentFlowDAO.queryPaymentFlowCountExclusion(userId,tradeNo);
        if(paymentCount > 0){
            return;
        }
        userAppScoreService.userAppScoreMoment(userId, USER_FIRST_PAYMENT);
    }

    public Result handlePayComplete(String orderSn) throws Exception {
        OrderDO orderDO = iOrderDAO.queryByOrderSn(orderSn);
        if (orderDO == null) {
            return Result.Error(INVALID_PARAMS, "orderSn不存在! orderSn=" + orderSn);
        }
        OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
        if (orderProductDo == null) {
            return Result.Error(INVALID_PARAMS, "orderProduct不存在! orderId=" + orderDO.getId());
        }
        initOrderComplete(orderDO, orderProductDo, orderDO.getTradeNo());
        return new Result(new JSONObject().fluentPut("orderSn", orderSn)
                .fluentPut("orderId", orderDO.getId()).fluentPut("productId", orderProductDo.getProductId()));
    }

    /**
     * 验证苹果支付
     *
     * @param receipt
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"userIsVip"}, key = "#userId")
    public boolean applePaymentVerify(Integer userId, ApplePaymentRequest receipt, Integer type) {
        boolean result = false;
        try {
            PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(receipt.getApp().getTenantId());

            String verifyResult = applePayService.compatibleSandboxOrder(receipt.getReceiptData(), paymentConfig);

            OrderVerifyResultDO verifyResultDO = applePayService.appleOrderVerify(verifyResult, receipt.getTransactionId(), paymentConfig);
            Integer productId = verifyResultDO.getProductId();
            if (productId == null) {
                this.sysReportPaymentError(userId, PaymentTypeEnums.APPLE.getCode(), PaymentFlagEnums.VERIFY.getCode());
                com.addx.iotcamera.util.LogUtil.error(logger, "applePay productId null,productId :{};userId:{}", productId, userId);
                return false;
            }

            logger.info("applePay 订单商品id:{}", productId);
            ProductDO productDO = productService.queryProductById(productId);

            // 验证次账单是否已验证
            PaymentFlow paymentFlow = this.queryPaymentFlow(receipt.getTransactionId());
            if (paymentFlow != null) {
                this.sysReportPaymentError(userId, PaymentTypeEnums.APPLE.getCode(), PaymentFlagEnums.VERIFY.getCode());
                logger.info("applePay 订单已经通知:{},userId:{}", receipt.getTransactionId(), userId);
                return true;
            }
            if (this.isRenewalOrder(userId, productId)) {
                logger.info("applePay 订单已经通知,续费订单续订");
                return true;
            }

            PaymentRequest paymentRequest = new PaymentRequest();
            paymentRequest.setType(type);
            paymentRequest.setProductId(productId);
            paymentRequest.setExtend(JSONObject.toJSONString(receipt));
            paymentRequest.getApp().setTenantId(receipt.getApp().getTenantId());

            OrderDO orderDO = initOrder(userId, paymentRequest, productDO, applePayService.isSandboxOrder(verifyResult));
            orderDO.setFreeTrial(verifyResultDO.getFreeTrial());

            //更新支付状态、返回字段
            OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
            if (orderProductDo != null) {
                orderDO.setTradeNo(receipt.getTransactionId());
                logger.info("applePay 验证通过;userId:{}", userId);
                initOrderComplete(orderDO, orderProductDo, verifyResultDO.getOriginTradeNo());
                result = true;
            }
        } catch (Exception e) {
            this.sysReportPaymentError(userId, PaymentTypeEnums.APPLE.getCode(), PaymentFlagEnums.VERIFY.getCode());
            com.addx.iotcamera.util.LogUtil.error(logger, "applePay error:{}", e);
            throw new RuntimeException(e.getMessage());
        }

        return result;
    }

    /**
     * 判断是否订阅订单续费订单验证
     *
     * @param userId
     * @param productId
     * @return
     */
    private boolean isRenewalOrder(Integer userId, Integer productId) {
        List<Long> userOrderId = userVipService.queryUserOrderIdAccess(userId);
        if (CollectionUtils.isEmpty(userOrderId)) {
            return false;
        }
        Long orderId = iOrderDAO.queryUserNewestSubOrder(userOrderId);
        if (orderId == null) {
            return false;
        }

        OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderId);
        if (orderProductDo == null) {
            return false;
        }

        return orderProductDo.getProductId().equals(productId);
    }

    /**
     * 验证苹果支付v1
     *
     * @param receipt
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result applePaymentVerifyV1(Integer userId, ApplePaymentRequest receipt, Integer type) throws Exception {
        PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(receipt.getApp().getTenantId());

        String verifyResult = applePayService.compatibleSandboxOrder(receipt.getReceiptData(), paymentConfig);

        OrderVerifyResultDO verifyResultDO = applePayService.appleOrderVerifyV1(verifyResult, receipt, paymentConfig);
        Integer resultCode = verifyResultDO.getProductId();
        if (resultCode < 0) {
            //各种错误返回码
            return Result.Error(resultCode, "订单验证错误");
        }else if(resultCode == 0){
            //恢复订单验证，
            return Result.Success();
        }
        logger.info("applePay 订单商品id:{}", resultCode);
        ProductDO productDO = productService.queryProductById(resultCode);

        // 验证次账单是否已验证
        PaymentFlow paymentFlow = this.queryPaymentFlow(receipt.getTransactionId());
        if (paymentFlow != null) {
            this.sysReportPaymentError(userId, PaymentTypeEnums.APPLE.getCode(), PaymentFlagEnums.VERIFY.getCode());
            logger.info("applePay 订单已经通知:{},userId:{}", receipt.getTransactionId(), userId);
            return Result.Success();
        }
        //防止选择的设备大于套餐限制数量
        receipt.setTierDeviceList(this.initPaymentTierDeviceList(receipt.getTierDeviceList(),productDO));

        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setType(type);
        paymentRequest.setProductId(resultCode);
        paymentRequest.setExtend(JSONObject.toJSONString(receipt));
        paymentRequest.getApp().setTenantId(receipt.getApp().getTenantId());
        paymentRequest.setGuidanceSource(receipt.getGuidanceSource());

        OrderDO orderDO = initOrder(userId, paymentRequest, productDO, applePayService.isSandboxOrder(verifyResult));
        orderDO.setFreeTrial(verifyResultDO.getFreeTrial());
        //更新支付状态、返回字段
        OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());

        orderDO.setTradeNo(receipt.getTransactionId());
        logger.info("applePay 验证通过;userId:{}", userId);

        //

        initOrderComplete(orderDO, orderProductDo, verifyResultDO.getOriginTradeNo(),verifyResultDO);
        return Result.Success();
    }



    /**
     * 谷歌支付
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"userIsVip"}, key = "#userId")
    public boolean googlePaymentVerify(GooglePaymentRequest request, Integer userId) {
        boolean result = false;
        try {
            OrderVerifyResultDO verifyResultDO = googlePayService.googleOrderVerify(request);
            log.info("Google OrderVerifyResultDO is: {}", verifyResultDO);
            if (!verifyResultDO.getVerify()) {
                this.sysReportPaymentError(userId, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.VERIFY.getCode());
                com.addx.iotcamera.util.LogUtil.warn(logger, "googlePay Verify false :{},userId:{}", request.toString(), userId);
                return false;
            }
            logger.info("谷歌验证订单是否相等 {}",request.getOutTradeNo().equals(verifyResultDO.getTradeNo()));
            ProductDO productDO = productService.queryProductById(request.getProductId());
            if (productDO == null) {
                this.sysReportPaymentError(userId, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.VERIFY.getCode());
                com.addx.iotcamera.util.LogUtil.error(logger, "googlePay productDO null :{},userId:{}", request.toString(), userId);
                return false;
            }
            //防止选择的设备大于套餐限制数量
            request.setTierDeviceList(this.initPaymentTierDeviceList(request.getTierDeviceList(),productDO));

            PaymentRequest paymentRequest = new PaymentRequest();
            paymentRequest.setType(PaymentTypeEnums.GOOGLE.getCode());
            paymentRequest.setProductId(request.getProductId());
            paymentRequest.setSubscriptionGroupId(request.getSubscriptionGroupId());
            paymentRequest.setExtend(JSONObject.toJSONString(request));
            paymentRequest.getApp().setTenantId(request.getApp().getTenantId());
            paymentRequest.setGuidanceSource(request.getGuidanceSource());
            paymentRequest.setOrderInfo(verifyResultDO.getOrderInfo());
            paymentRequest.setOrderInfoV2(verifyResultDO.getOrderInfoV2());
            OrderDO orderDOExit = iOrderDAO.queryBytradeNo(request.getOutTradeNo());
            if (orderDOExit != null) {
                this.sysReportPaymentError(userId, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.VERIFY.getCode());
                logger.info("googlePay 订单已经通知:{},userId:{}", request.getOutTradeNo(), userId);
                return true;
            }
            logger.info("购买时指定设备 {}",request.getTierDeviceList());

            OrderDO orderDO = initOrder(userId, paymentRequest, productDO, this.isSandboxOrder(userId));
            //更新支付状态、返回字段
            orderDO.setTradeNo(request.getOutTradeNo());
            orderDO.setFreeTrial(verifyResultDO.getFreeTrial());

            OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
            if (orderProductDo == null) {
                this.sysReportPaymentError(userId, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.VERIFY.getCode());

                com.addx.iotcamera.util.LogUtil.error(logger, "googlePay error orderProductDo null,userId:{},{}", userId, request.toString());
                return false;
            }

            logger.info("googlePay 支付成功,开通会员权限:userId:{},{}", userId, request.getOutTradeNo());


            initOrderComplete(orderDO, orderProductDo, request.getOutTradeNo(),verifyResultDO);
            if (verifyResultDO.getIsUpgradedOrder()) {
                Gson gson = new Gson();
                SubscriptionPurchaseV2 subscriptionPurchaseV2 = gson.fromJson(verifyResultDO.getLinkedOrderInfo(), SubscriptionPurchaseV2.class);
                if (subscriptionPurchaseV2.getCanceledStateContext() != null && subscriptionPurchaseV2.getCanceledStateContext().getReplacementCancellation() != null) {
                    PaymentFlow paymentFlow = this.queryPaymentFlow(subscriptionPurchaseV2.getLatestOrderId());
                    if (paymentFlow != null) {
                        userVipService.userVipRefundOrUpgrade(paymentFlow, paymentFlow.getUserId());
                    }
                }
            }
            result = true;
        } catch (Exception e) {
            this.sysReportPaymentError(userId, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.VERIFY.getCode());
            com.addx.iotcamera.util.LogUtil.error(logger, "googlePay error:{}", e);
            throw new RuntimeException(e.getMessage());
        }
        return result;
    }

    /**
     * 用户是否沙盒支付--仅针对谷歌支付订单
     * @param userId
     * @return
     */
    public boolean isSandboxOrder(Integer userId){
        User user = userService.queryUserById(userId);
        String tenantId = user.getTenantId();
        if(!googlePaySandboxConfig.getConfig().containsKey(tenantId)){
            return false;
        }
        return googlePaySandboxConfig.getConfig().get(tenantId).contains(user.getEmail());
    }

    /**
     * 验证未支付状态的订单
     */
    public void incompleteOrder() {
        int time = (int) (System.currentTimeMillis() / 1000);

        List<OrderDO> orderDOList = iOrderDAO.queryOrderUnCompleteList(time - 24 * 60 * 60);

        if (!CollectionUtils.isEmpty(orderDOList)) {
            orderDOList.forEach(item -> {
                if (item.getOrderType().equals(PaymentTypeEnums.ALI.getCode())) {
                    queryAliOrderComplete(item);
                } else {
                    queryWechatOrderComplete(item);
                }
            });
        }
    }


    /**
     * 验证支付宝订单是否完成
     *
     * @param item
     */
    @Transactional
    public void queryAliOrderComplete(OrderDO item) {
        try {
            AlipayTradeQueryResponse response = aliPayService.queryAliOrderResult(item.getOrderSn());
            boolean neddUpdate = false;
            logger.info("queryAliOrderComplete result:{}", response.toString());
            if (response.isSuccess()) {
                if (response.getTradeStatus().equals("TRADE_SUCCESS")) {
                    //订单在支付宝支付状态为已完成
                    item.setStatus(PaymentStatusEnum.PAYMENTED.getCode());
                    item.setTradeNo(response.getTradeNo());
                    neddUpdate = true;
                }
            }
            if (neddUpdate) {
                OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(item.getId());
                if (orderProductDo == null) {
                    com.addx.iotcamera.util.LogUtil.error(logger, "queryAliOrderComplete 订单商品部存在:{}", item.getId());
                    return;
                }
                initOrderComplete(item, orderProductDo, item.getTradeNo());
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(logger, "incompleteOrder ali error{}", e);
        }
    }


    /**
     * 验证微信订单支付是否完成
     *
     * @param item
     */
    @Transactional(rollbackFor = Exception.class)
    public void queryWechatOrderComplete(OrderDO item) {
        try {
            boolean complete = wxPayService.wechatOrderCompleteQeury(item);
            if (!complete) {
                com.addx.iotcamera.util.LogUtil.error(logger, "incompleteOrder 微信支付结果验证不通过:{}", item.getOrderSn());
                return;
            }

            OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(item.getId());
            if (orderProductDo == null) {
                com.addx.iotcamera.util.LogUtil.error(logger, "incompleteOrder orderProductDo null:{}", item.getOrderSn());
                return;
            }

            initOrderComplete(item, orderProductDo, item.getTradeNo());
            logger.info("incompleteOrder Complete,orderNo:{}", item.getOrderSn());
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(logger, "incompleteOrder wechat error:{}", e);
            throw new RuntimeException("更新微信订单异常");
        }
    }

    /**
     * 支付账单确认
     */
    public void aliPayBill() {
        try {
            Date date = new Date();
            date.setTime(date.getTime() - 24 * 60 * 60 * 1000);
            //获取昨日日期
            String yestoday = DateUtils.dateToString(date, DateUtils.YYYY_MM_DD);
            //创建API对应的request类
            //获取支付宝账单信息
            AlipayDataDataserviceBillDownloadurlQueryResponse response = aliPayService.queryAliBill(yestoday);
            logger.info("aliPayBill url:{}", response.getBillDownloadUrl());

            if (response.isSuccess()) {
                //根据tenantId 获取各app各自的参数
                PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get("vicoo");
                String dir = String.format("%s%s/", paymentConfig.getAliBillFilePath(), yestoday);
                String path = String.format("%s%s.zip", dir, yestoday);
                //下载账单文件
                FileUtil.downloadFile(response.getBillDownloadUrl(), dir, path);
                logger.info("aliPayBill download");
                //解压缩文件
                FileUtil.unZip(path, dir);

                //支付宝账单
                List<String[]> dataList = getAliBillInfo(dir);
                Map<String, Integer> aliDataMap = Maps.newHashMap();
                if (!CollectionUtils.isEmpty(dataList)) {
                    for (String[] arr : dataList) {
                        aliDataMap.put(arr[1].replace("\t", ""), BigDecimal.valueOf(Double.valueOf(arr[11].replace("\t", ""))).multiply(new BigDecimal(100)).intValue());
                    }
                }
                //获取本地流水表对账
                String timeStart = yestoday + " 00:00:00";
                String timeEnd = yestoday + " 23:59:59";
                List<PaymentFlow> payList = iPaymentFlowDAO.queryPayListOrderList(DateUtils.date2time(timeStart, null)
                        , DateUtils.date2time(timeEnd, null), PaymentTypeEnums.ALI.getCode());
                Map<String, Integer> payDataMap = Maps.newHashMap();
                if (!CollectionUtils.isEmpty(payList)) {
                    for (PaymentFlow flow : payList) {
                        payDataMap.put(flow.getOutTradeNo(), flow.getAmount());
                    }
                }
                //check账单数据
                checkPayOrder(yestoday, aliDataMap, payDataMap, PaymentTypeEnums.ALI.getCode());
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(logger, "aliPayBill error:{}", e);
        }
    }

    /**
     * 读取账单信息
     *
     * @param dir
     * @return
     * @throws IOException
     */
    private List<String[]> getAliBillInfo(String dir) throws IOException {
        //行文件中所有数据
        List<String[]> dataList = new ArrayList<>();
        // 遍历文件 获取需要的汇整csv
        File[] fs = new File(dir).listFiles();

        for (File file : fs) {
            if (file.getAbsolutePath().contains("明细") && !file.getAbsolutePath().contains("汇总")) {
                InputStreamReader inputStreamReader = null;
                InputStream fiStream = null;
                BufferedReader br = null;

                //暂时存放每一行的数据
                String rowRecord = "";
                try {
                    //文件流对象
                    fiStream = new FileInputStream(file);
                    inputStreamReader = new InputStreamReader(fiStream, Charset.forName("GBK"));
                    br = new BufferedReader(inputStreamReader);
                    int line = 0;
                    while ((rowRecord = br.readLine()) != null) {
                        if (rowRecord.contains("合计")) {
                            continue;
                        }
                        String[] lineList = rowRecord.split("\\,");
                        if (line > 4 && lineList.length > 4) {
                            dataList.add(lineList);
                        }
                        line++;
                    }
                } catch (Exception e) {
                    com.addx.iotcamera.util.LogUtil.error(logger, "读取支付宝账单异常:{}", e);
                } finally {
                    if (br != null) {
                        br.close();
                    }
                    if (fiStream != null) {
                        fiStream.close();
                    }
                    if (inputStreamReader != null) {
                        inputStreamReader.close();
                    }
                }
            }
        }
        // 插入成功， 删除csv 文件
        for (File file : fs) {
            file.delete();
        }

        return dataList;
    }

    /**
     * 统计本地流水与支付宝流水,并将数据放入payment_amount表
     *
     * @param day
     * @param aliDataMap
     * @param payDataMap
     */
    private void checkPayOrder(String day, Map<String, Integer> aliDataMap, Map<String, Integer> payDataMap, int type) {
        List<String> noInAliList = Lists.newArrayList();
        List<String> noInPayList = Lists.newArrayList();
        int amountAli = 0;
        int countAli = 0;

        int amount = 0;
        int count = 0;
        for (String key : aliDataMap.keySet()) {
            amountAli += aliDataMap.get(key);
            countAli++;
            if (!payDataMap.containsKey(key) || !aliDataMap.get(key).equals(payDataMap.get(key))) {
                noInPayList.add(key);
                continue;
            }
        }

        for (String key : payDataMap.keySet()) {
            amount += payDataMap.get(key);
            count++;
            if (!aliDataMap.containsKey(key) || !payDataMap.get(key).equals(aliDataMap.get(key))) {
                noInAliList.add(key);
                continue;
            }
        }
    }


    /**
     * 获取微信账单
     *
     * @throws Exception
     */
    public void wechatOrderBill() throws Exception {
        Date d = new Date();
        long time = d.getTime() - 24 * 60 * 60 * 1000;
        //昨天日期
        String date = DateUtils.dateToString(new Date(time), DateUtils.YYYYMMDD);

        String yestoday = DateUtils.dateToString(new Date(time), DateUtils.YYYYMMDD);

        Map<String, String> response = wxPayService.queryWxPayBill(date);
        String returnCode = response.get("return_code");
        //获取返回码
        //若返回码为SUCCESS，则会返回一个result_code,再对该result_code进行判断
        if (returnCode.equals("SUCCESS")) {
            //账单格式化
            List<Map<String, String>> list = getWechatOrderList(response.get("data"));
            if (!CollectionUtils.isEmpty(list)) {
                Map<String, Integer> wechatMap = Maps.newHashMap();
                list.forEach(item -> {
                    wechatMap.put(item.get("商户订单号"),
                            BigDecimal.valueOf(Double.valueOf(item.get("订单金额"))).multiply(new BigDecimal(100)).intValue());
                });

                //获取本地流水表记录
                String timeStart = yestoday + " 00:00:00";
                String timeEnd = yestoday + " 23:59:59";
                List<PaymentFlow> payList = iPaymentFlowDAO.queryPayListOrderList(DateUtils.date2time(timeStart, null)
                        , DateUtils.date2time(timeEnd, null), PaymentTypeEnums.ALI.getCode());
                Map<String, Integer> payDataMap = Maps.newHashMap();
                if (!CollectionUtils.isEmpty(payList)) {
                    for (PaymentFlow flow : payList) {
                        payDataMap.put(flow.getOutTradeNo(), flow.getAmount());
                    }
                }

                //check 流水
                checkPayOrder(date, wechatMap, payDataMap, PaymentTypeEnums.WECHAT.getCode());
            }
        }
    }

    /**
     * 格式化微信账单信息
     *
     * @param data
     * @return
     */
    private List<Map<String, String>> getWechatOrderList(String data) {
        List<Map<String, String>> list = Lists.newArrayList();
        String newStr = data.replaceAll(",", " "); // 去空格
        String[] tempStr = newStr.split("`"); // 数据分组
        String[] t = tempStr[0].split(" ");// 分组标题
        int k = 1; // 纪录数组下标
        int j = tempStr.length / t.length; // 计算循环次数
        for (int i = 0; i < j; i++) {
            Map<String, String> map = Maps.newHashMap();
            for (int l = 0; l < t.length; l++) {
                //如果是最后列且是最后一行数据时，去除数据里的汉字
                if ((i == j - 1) && (l == t.length - 1)) {
                    String reg = "[\u4e00-\u9fa5]";//汉字的正则表达式
                    Pattern pat = Pattern.compile(reg);
                    Matcher mat = pat.matcher(tempStr[l + k]);
                    String repickStr = mat.replaceAll("");
                    map.put(t[l], repickStr);
                } else {
                    map.put(t[l], tempStr[l + k]);
                }
            }
            k = k + t.length;
            list.add(map);
        }
        return list;
    }

    /**
     * 初始化用户vip信息
     *
     * @param orderDO
     * @param orderProductDo
     */

    public void initUserVip(OrderDO orderDO, OrderProductDo orderProductDo)  {
        this.initUserVip(orderDO,orderProductDo,null);
    }
    public void initUserVip(OrderDO orderDO, OrderProductDo orderProductDo,OrderVerifyResultDO verifyResultDO)  {
        ProductDO productDO = productService.queryProductById(orderProductDo.getProductId());

        if(!StringUtils.isEmpty(productDO.getAdditionalTierUid())) {
            return;
        }

        Integer tierId = productDO.getTierId();
        Integer userId = orderDO.getUserId();

        int time = (int) (System.currentTimeMillis() / 1000);

        User userParam = new User();
        userParam.setId(userId);
        User user = iUserDAO.getUserById(userParam);
        if (user == null) {
            logger.info("initUserVip 用户信息为空，可能是已注销:{}", userId);
            return;
        }

        Tier buyTier =  tierService.queryTierById(tierId);

        List<UserVipDO> currentUserVipDOList = iUserVipDAO.queryUserVipInfo(userId, time,buyTier.getTierServiceType());
        Integer currentTierId = CollectionUtils.isEmpty(currentUserVipDOList) ? 0 : currentUserVipDOList.get(0).getTierId();

        Map<Integer, List<UserVipDO>> tierGroupUserVipDOList = CollectionUtils.isEmpty(currentUserVipDOList) ? Collections.emptyMap() :
                currentUserVipDOList.stream().collect(Collectors.groupingBy(userVipDO -> {
                    Tier tier = tierService.queryTierById(userVipDO.getTierId());
                    return ObjectUtils.defaultIfNull(tier.getTierGroupId(), -1);
                }));

        List<UserVipDO> existBuyTierList = currentUserVipDOList.stream()
                .filter(userVipDO -> userVipDO.getTierId().equals(tierId))
                .collect(Collectors.toList());
        // 当前套餐最后一次的购买
        UserVipDO userVipLast = CollectionUtils.isEmpty(existBuyTierList) ? null : existBuyTierList.get(existBuyTierList.size()-1);
        //现在是非会员
        UserVipDO userVipDONew = UserVipDO.builder()
                .userId(orderDO.getUserId())
                .tierId(tierId)
                .cdate(time)
                .orderId(orderDO.getId())
                .tradeNo(orderDO.getTradeNo())
                .rollingDay(this.initUserVipRoolingDay(userVipLast,buyTier,user.getTenantId()))
                .freeTrial(verifyResultDO == null ? 0 : verifyResultDO.getFreeTrial())
                .tierServiceType(buyTier.getTierServiceType())
                .build();

        List<UserVipDO> userVipDOList = tierGroupService.addUserVipDO(tierGroupUserVipDOList.get(ObjectUtils.defaultIfNull(buyTier.getTierGroupId(), -1)), userVipDONew, productDO, userVipDONew.getFreeTrial().equals(0) ? 0 : verifyResultDO.getFreeTrialPeriod());
        for(UserVipDO userVipDO : userVipDOList) {
            if(userVipDO.getId() == null) {
                logger.info("initUserVip param:{}", userVipDO.toString());
                iUserVipDAO.insertUserVip(userVipDO);

                //更新用户是否购买或者领取标记
                userVipService.userVipReceiveTag(orderDO.getUserId(),true);
            }
        }

        if(ObjectUtils.notEqual(tierId, currentTierId)) {
            //刷新套餐所属设备,
            userVipActivateService.activateUserVip(userId, tierId);
        }
    }


    public int initUserVipRoolingDay(UserVipDO userVipDO,Tier tier,String tenantId){
        return userVipDO == null ?
                tier.getTierType().equals(TierTypeEnums.TIER_DEVICE_LIMIT.getCode()) && TIER_DEVICE_LIMIT_APP.contains(tenantId) ?
                        DEFAULT_ROOLING_DAY : tier.getRollingDays()
                : Optional.ofNullable(userVipDO.getRollingDay()).orElse(0);
    }

    /**
     * 添加用户叠加包
     * @param orderDO
     * @param productDO
     */
    public void initAdditionalUseTier(OrderDO orderDO, ProductDO productDO) {
        AdditionalUserTierDO additionalUserTierDO = new AdditionalUserTierDO();
        additionalUserTierDO.setUserId(orderDO.getUserId());
        additionalUserTierDO.setTierUid(productDO.getAdditionalTierUid());
        additionalUserTierDO.setOrderId(orderDO.getId());
        additionalUserTierDO.setTradeNo(orderDO.getTradeNo());
        additionalUserTierDO.setEffectiveTime((int) (System.currentTimeMillis() / 1000));

        additionalUserTierDO.setMonth(productDO.getMonth());
        Date nowDate = new Date();
        Date addMonthDate = org.apache.commons.lang3.time.DateUtils.addMonths(new Date(), productDO.getMonth());
        int addDays = Long.valueOf((addMonthDate.getTime() - nowDate.getTime() + 1000) / (24 * 60 * 60 * 1000)).intValue();
        additionalUserTierDO.setDay(addDays);

        additionalUserTierDO.setTenantId(productDO.getTenantId());
        additionalUserTierDao.insertUserTier(additionalUserTierDO);
    }

    /**
     * 注册时免费送一个月套餐
     *
     * @param userId
     * @param productId
     * @throws Exception
     */
    @Transactional(noRollbackFor = Exception.class)
    public void initUserVipRegister(Integer userId, Integer productId) throws Exception {
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setType(PaymentTypeEnums.REGISTER.getCode());
        //配置的套餐，固定写死
        paymentRequest.setProductId(productId);
        String tenantId = userService.queryTenantIdById(userId);
        paymentRequest.getApp().setTenantId(tenantId);
        ProductDO productDO = productService.queryProductById(productId);
        if (productDO != null) {
            OrderDO orderDO = initOrder(userId, paymentRequest, productDO, false);

            //更新支付状态、返回字段
            int time = (int) (System.currentTimeMillis() / 1000);
            orderDO.setStatus(PaymentStatusEnum.PAYMENTED.getCode());
            orderDO.setMdate(time);
            orderDO.setTradeNo(orderDO.getOrderSn());
            iOrderDAO.updateOrderStatus(orderDO);

            OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
            if (orderProductDo != null) {
                logger.info("新用户注册,开通会员权限:{}", userId);
                //生成vip记录
                initUserVip(orderDO, orderProductDo);

                userVipService.userVipReceiveTag(userId,true);
            }

            //初始化ai通知设置
            notificationService.initDeviceAiSettingAndNotifySetting(orderDO.getUserId());
        }
    }

    @Transactional(noRollbackFor = Exception.class)
    public Result initOrderMomo(Integer userId, Integer productId,String transactionId,String tenantId) throws Exception {
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setType(PaymentTypeEnums.MOMOPAY.getCode());
        paymentRequest.setProductId(productId);
        paymentRequest.getApp().setTenantId(tenantId);
        ProductDO productDO = productService.queryProductById(productId);
        if (productDO == null) {
            return Result.Error(INVALID_PARAMS,"productId does not exist");
        }

        OrderDO orderExist = iOrderDAO.queryBytradeNo(transactionId);
        if(orderExist != null){
            return Result.Success();
        }

        OrderDO orderDO = initOrder(userId, paymentRequest, productDO, false);

        //更新支付状态、返回字段
        int time = (int) (System.currentTimeMillis() / 1000);
        orderDO.setStatus(PaymentStatusEnum.PAYMENTED.getCode());
        orderDO.setMdate(time);
        orderDO.setTradeNo(transactionId);
        iOrderDAO.updateOrderStatus(orderDO);

        OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
        if (orderProductDo != null) {
            //生成vip记录
            initUserVip(orderDO, orderProductDo);
        }
        return Result.Success();
    }

    // 兑换码兑换vip
    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Exception.class)
    @CacheEvict(value = {"userIsVip"}, key = "#userId")
    public void exchangeVip(Integer userId, ProductDO productDO, PaymentTypeEnums paymentType, String tenantId) throws Exception {
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setType(paymentType.getCode());
        paymentRequest.setProductId(productDO.getId());
        AppInfo appInfo = new AppInfo();
        appInfo.setTenantId(tenantId);
        paymentRequest.setApp(appInfo);

        OrderDO orderDO = initOrder(userId, paymentRequest, productDO, false);

        //更新支付状态、返回字段
        int time = (int) (System.currentTimeMillis() / 1000);
        orderDO.setStatus(PaymentStatusEnum.PAYMENTED.getCode());
        orderDO.setMdate(time);
        orderDO.setTradeNo(orderDO.getOrderSn());
        iOrderDAO.updateOrderStatus(orderDO);

        OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
        //生成vip记录
        initUserVip(orderDO, orderProductDo);
        logger.info("兑换码兑换vip,开通会员权限:{}", userId);
        //初始化ai通知设置
        notificationService.initDeviceAiSettingAndNotifySetting(orderDO.getUserId());
    }

    public PaymentFlow queryPaymentFlow(String tradeNo) {
        return iPaymentFlowDAO.queryPaymentFlow(tradeNo);
    }


    public void verifySubOrderFlow() {
        List<OrderDO> orderDOList = applePayService.querySubOrderlist();
        if (CollectionUtils.isEmpty(orderDOList)) {
            return;
        }

        List<String[]> orderFlowCheckList = getErrorOrderList(orderDOList);

        if (CollectionUtils.isEmpty(orderFlowCheckList)) {
            logger.info("verifySubOrderFlow no error order");
            return;
        }

        String[] titleArray = new String[]{"用户id", "订单号", "应付账单数", "已付账单数"};

        EmailUtils.sendEmail(mailConfig.getTolist(), envNode + "订阅订单账单监控", EmailUtils.emailContent(titleArray, orderFlowCheckList),appAccountConfig.queryEmailAccount(TENANTID_VICOO));
    }

    private List<String[]> getErrorOrderList(List<OrderDO> orderDOList) {
        List<String[]> orderFlowCheckList = Lists.newArrayList();


        orderDOList.forEach(o -> {
            LocalDateTime localDateTimeStart = LocalDateTime.ofEpochSecond(o.getTimeStart(), 0, ZoneOffset.ofHours(8));


            LocalDateTime localDateTime = LocalDateTime.now();
            int countTimeEndCount = this.countTimeEndCount(localDateTime, localDateTimeStart);

            int paymentFlowCount = iPaymentFlowDAO.queryPaymentFlowCount(o.getOrderSn());

            if (countTimeEndCount != paymentFlowCount) {
                String[] contentArray = new String[4];
                contentArray[0] = String.valueOf(o.getUserId());
                contentArray[1] = o.getOrderSn();
                contentArray[2] = String.valueOf(countTimeEndCount);
                contentArray[3] = String.valueOf(paymentFlowCount);
                orderFlowCheckList.add(contentArray);
            }
        });

        return orderFlowCheckList;
    }

    /**
     * 根据timeStart计算支付次数
     *
     * @param localDateTime
     * @param localDateTimeStart
     * @return
     */
    private int countTimeEndCount(LocalDateTime localDateTime, LocalDateTime localDateTimeStart) {
        //所处于第几个账期,订阅订单第一次已经支付，所以从第一个开始
        int countTimeStart = 1;
        while (true) {
            localDateTimeStart = localDateTimeStart.plus(1, ChronoUnit.MONTHS);
            //说明本次账期未支付
            if (localDateTimeStart.isAfter(localDateTime)) {
                break;
            }

            countTimeStart++;
        }
        return countTimeStart;
    }


    public void queryOrderFlowList() {
        String[] titleArray = new String[]{"用户id", "订单号", "平台订单号", "付款日期", "套餐", "公司代码", "支付类型", "订单类型"};
        List<String[]> orderFlowList = this.queryOrderFlowArray();
        EmailUtils.sendEmail(mailConfig.getTolist(), envNode + "昨日订单流水", EmailUtils.emailContent(titleArray, orderFlowList),appAccountConfig.queryEmailAccount(TENANTID_VICOO));
    }

    private List<String[]> queryOrderFlowArray() {
        int todayTime = (int) (DateUtils.initDateByDay() / 1000);
        int yestodayTime = todayTime - 24 * 60 * 60;
        List<PaymentFlow> orderPaymentFlowList = iPaymentFlowDAO.queryPaymentFlowList(yestodayTime, todayTime);

        List<String[]> orderPaymentFlowArray = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orderPaymentFlowList)) {
            return orderPaymentFlowArray;
        }

        for (PaymentFlow flow : orderPaymentFlowList) {
            String[] contentArray = new String[8];
            contentArray[0] = String.valueOf(flow.getUserId());
            contentArray[1] = flow.getOutTradeNo();
            contentArray[2] = flow.getTradeNo();
            LocalDateTime localDateTimeStart = LocalDateTime.ofEpochSecond(flow.getTimeStart(), 0, ZoneOffset.ofHours(8));
            contentArray[3] = localDateTimeStart.toString();
            contentArray[4] = flow.getProductBody();
            contentArray[5] = flow.getTenantId();
            contentArray[6] = PaymentTypeEnums.queryByCode(flow.getType()).getMsg();
            OrderDO orderDO = iOrderDAO.queryByOrderSn(flow.getOutTradeNo());
            contentArray[7] = flow.getTradeNo().equals(orderDO.getTradeNo()) ? "原始订单" : "续订订单";

            orderPaymentFlowArray.add(contentArray);
        }
        return orderPaymentFlowArray;
    }


    /**
     * 用户支付异常Log
     *
     * @param userId
     * @param payType
     */
    public void sysReportPaymentError(Integer userId, int payType, int type) {
        reportLogService.sysReportUpdateUserConfig(REPORT_TYPE_PAY_PARAM_EROOR, MapUtil.builder()
                .put("userId", userId)
                .put("payType", payType)
                .build());
    }

    /**
     * 恢复订单验证
     *
     * @param userId
     * @throws Exception
     */
    public void iosOrderRecovery(Integer userId) throws Exception {
        // 恢复购买先假接口
//        List<OrderDO> orderDOList = iOrderDAO.queryUserIosSubOrderList(userId);
//        if (CollectionUtils.isEmpty(orderDOList)) {
//            return;
//        }
//        for (OrderDO orderDO : orderDOList) {
//            logger.info("ios订单恢复{}", orderDO.getOrderSn());
//            applePayService.appleOrderResult(orderDO);
//        }
    }


    @Transactional(rollbackFor = {Exception.class, BaseException.class})
    public Result requestToPayMomoPay(Integer userId, PaymentRequest request) throws Exception {
        ProductDO productDO = productService.queryProductById(request.getProductId());

        ResultCollection resultCollection = requestMomoPayVerify(userId, request, productDO);
        if (resultCollection.getCode() != SUCCESS.getCode()) {
            return resultCollection.getResult();
        }

        request.setType(PaymentTypeEnums.MOMOPAY.getCode());
        //生成订单信息
        OrderDO orderDO = initOrder(userId, request, productDO, false);
        if (orderDO == null) {
            return OPERTION_DB_ERROR.getResult();
        }

        boolean requestToPay = momoPayService.requestToPay(request, productDO, orderDO);
        if (!requestToPay) {
            throw new BaseException(failureFlag, "支付失败");
        }
        return Result.Success();
    }

    private ResultCollection requestMomoPayVerify(Integer userId, PaymentRequest request, ProductDO productDO) {
        if (productDO == null) {
            com.addx.iotcamera.util.LogUtil.error(logger, "requestToPay 商品id错误, userId:{},productId:{}", userId, request.getProductId());
            return INVALID_PARAMS;
        }
        //校验商品信息
        if (!verifyPayment(userId, request, productDO)) {
            return INVALID_PARAMS;
        }
        return SUCCESS;
    }


    /**
     * 定时验证未支付完成的订单
     */
    public void momoPayOrderVerify() {
        List<OrderDO> orderDOList = iOrderDAO.queryMomoOrderPending();
        if (CollectionUtils.isEmpty(orderDOList)) {
            return;
        }

        for (OrderDO orderDO : orderDOList) {
            if (momoPayService.verifyMomoPayStatus(orderDO)) {
                OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
                try {
                    initOrderComplete(orderDO, orderProductDo, orderDO.getTradeNo());
                } catch (Exception e) {
                    logger.info("套餐购买成功:{}", orderDO.getOrderSn());
                }
            }
        }
    }


    /**
     * 批量查询账单是否已存在
     * @param tradeNos
     * @return
     */
    public Set<String> queryExistPaymentBatch(Set<String> tradeNos){
        if(CollectionUtils.isEmpty(tradeNos)){
            return Sets.newHashSet();
        }
        return iPaymentFlowDAO.queryPaymentFlowExistBatch(tradeNos);
    }

    /**
     * 更新退款详情
     * @param paymentFlow
     */
    public void updateRefundInfo(PaymentFlow paymentFlow){
        User user = userService.queryUserById(paymentFlow.getUserId());
        paymentFlow.setRefundAppVersionName(user.getAppVersionName());
        paymentFlow.setRefundServeVersion(serveConfig.getServeReleaseTag());
        iPaymentFlowDAO.updateRefundOrderInfo(paymentFlow);
    }

    /**
     * 查询已取消的订单
     */
    public boolean queryGoogleCancelOrder(){
        List<String> tenantIdList = globalConfig.getTenant();
        if(CollectionUtils.isEmpty(tenantIdList)){
            logger.error("未配置tenantId");
            return false;
        }
        for(String tenantId : tenantIdList){
            List<GoogleCancelOrderDO.VoidedPurchaseDO> list = googlePayService.queryCancelOrderList(tenantId);
            if(CollectionUtils.isEmpty(list)){
                continue;
            }
            Map<String,GoogleCancelOrderDO.VoidedPurchaseDO> purchaseDOMap = list.stream().collect(Collectors.toMap(GoogleCancelOrderDO.VoidedPurchaseDO::getOrderId, Function.identity()));
            Set<String> tradeNos = list.stream().map(GoogleCancelOrderDO.VoidedPurchaseDO::getOrderId).collect(Collectors.toSet());
            List<PaymentFlow> paymentFlows = iPaymentFlowDAO.queryPaymentFlowNoRefundBatch(tradeNos);
            for(PaymentFlow paymentFlow : paymentFlows){
                GoogleCancelOrderDO.VoidedPurchaseDO purchaseDO = purchaseDOMap.get(paymentFlow.getTradeNo());
                paymentFlow.setRefund(1);
                paymentFlow.setRefundReason(purchaseDO.getVoidedReason());
                paymentFlow.setRefundTime((int)(purchaseDO.getVoidedTimeMillis()/1000));

                User user = userService.queryUserById(paymentFlow.getUserId());
                if(user != null){
                    paymentFlow.setRefundAppVersionName(user.getAppVersionName());
                }
                paymentFlow.setRefundServeVersion(serveConfig.getServeReleaseTag());
                iPaymentFlowDAO.updateRefundOrderInfo(paymentFlow);

                userVipService.userVipRefundOrUpgrade(paymentFlow,paymentFlow.getUserId());
            }
        }
        return true;
    }

    /**
     * 判断是否可购买指定套餐，并返回待指定设备列表
     * @param userId
     * @param productId
     * @param tenantId
     * @return
     */
    public Map<String,Object> verifyProductPurchase(Integer userId,Integer productId,String tenantId){
        return this.verifyProductPurchase(userId,productId,tenantId,false);
    }

    public Map<String,Object> verifyProductPurchase(Integer userId,Integer productId,String tenantId,boolean removeAvailable){
        // 是否还有生效的订单,判断是否可购买指定的套餐
        UserDeviceCanChooseDO.CurrentTierVerifyResult verifyResult = this.userEffectiveTrade(userId,productId,tenantId,removeAvailable);

        List<UserDeviceCanChooseDO> list = Lists.newArrayList();
        boolean allSimThirdParty = false;
        if(verifyResult.isCanPurchase()){
            // 用户绑定的设备
            List<DeviceDO> deviceDOList =  deviceInfoService.listDevicesByUserId(userId);
            //全是
            allSimThirdParty = deviceDOList.stream()
                    .filter(deviceDO -> deviceDO.getAdminId().equals(deviceDO.getUserId()))
                    .filter(deviceDO -> org.springframework.util.StringUtils.hasLength(deviceDO.getIccid()))
                    .allMatch(deviceDO ->  Optional.ofNullable(deviceDO.getSimThirdParty()).orElse(0).equals(1));
            deviceDOList =  deviceDOList
                    .stream()
                    .filter(deviceDO -> {
                        boolean device4G = deviceInfoService.checkIfDeviceUse4G(deviceDO.getSerialNumber());
                        if(device4G){
                            return TierServiceTypeEnums.TIER_4G.getCode().equals(verifyResult.getTierServiceType()) && (!StringUtils.isEmpty(deviceDO.getIccid()) && deviceDO.getSimThirdParty().equals(0));
                        }else{
                            return TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode().equals(verifyResult.getTierServiceType());
                        }
                    })
                    .collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(deviceDOList)){
                // 过滤设备vip
                deviceDOList = userTierDeviceService.filterOutDeviceVip(tenantId,  deviceDOList);
                Collections.reverse(deviceDOList);
                // 返回支持套餐购买的设备list, 区分是否已被其他套餐指定
                list = deviceDOList.stream()
                        .filter(deviceDO -> deviceDO.getAdminId().equals(deviceDO.getUserId()))
                        .map(
                                device -> UserDeviceCanChooseDO.builder()
                                        .serialNumber(device.getSerialNumber())
                                        //设备未被其他套餐指定
                                        .canChoose (verifyResult.isSwitchBillingCycle() || CollectionUtils.isEmpty(verifyResult.getTierDeviceSet()) || !verifyResult.getTierDeviceSet().contains(device.getSerialNumber()))
                                        .deviceName(device.getDeviceName())
                                        //设备icon图标
                                        .icon(device.getIcon())
                                        .build()
                        ).sorted(Comparator.comparing(UserDeviceCanChooseDO::getCanChoose))
                        .collect(Collectors.toList()
                        );
            }
        }

        Map<String,Object> result = Maps.newHashMap();
        //是否可购买,不判断是否可购买的设备
        result.put("availableForPurchase", verifyResult.isCanPurchase());
        //待指定设备列表
        result.put("deviceList", list);
        result.put("allSimThirdParty",allSimThirdParty);
        return result;
    }

    /**
     * 是否可购买此商品,并返回已指定的设备
     * @param userId
     * @param productId
     * @param tenantId
     * @return
     */
    public UserDeviceCanChooseDO.CurrentTierVerifyResult userEffectiveTrade(Integer userId, Integer productId,String tenantId,boolean removeAvailable){
        UserDeviceCanChooseDO.CurrentTierVerifyResult result = new UserDeviceCanChooseDO.CurrentTierVerifyResult();
        ProductDO productDO = productService.queryProductById(productId);
        if(productDO == null){
            //无此商品
            throw new BaseException(INVALID_PARAMS,INVALID_PARAMS.getMsg());
        }

        if(!ProductTypeEnums.NEW_PRODUCT_TYPE_SET.contains(productDO.getType())){
            //非新版套餐
            log.debug("非新版套餐");
            result.setCanPurchase(true);
            result.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            return result;
        }

        Tier buyTier = tierService.queryTierById(productDO.getTierId());
        result.setTierServiceType(buyTier.getTierServiceType());
        List<UserVipDO> userVipDOList = iUserVipDAO.queryUserVipInfo(userId, (int) Instant.now().getEpochSecond(),buyTier.getTierServiceType());
        if(CollectionUtils.isEmpty(userVipDOList)){
            //未购买过或者当前没有生效的
            log.debug("CollectionUtils.isEmpty(userVipDOList)");
            result.setCanPurchase(true);
            return result;
        }

        String tradeNo = userVipDOList.get(0).getTradeNo();
        PaymentFlow paymentFlow = iPaymentFlowDAO.queryPaymentFlow(tradeNo);
        if(paymentFlow == null){
            // 未购买过,现有vip是赠送的,可购买
            log.debug("paymentFlow == null");
            result.setCanPurchase(true);
            return result;
        }

        OrderDO orderDO = iOrderDAO.queryByOrderSn(paymentFlow.getOutTradeNo());
        if(orderDO == null){
            //无订单信息，可购买
            log.debug("orderDO == null");
            result.setCanPurchase(true);
            return result;
        }
        boolean hasEffectiveOrder = orderDO.getOrderCancel().equals(0);
        if(!hasEffectiveOrder){
            //已取消订阅，可购买
            log.debug("!hasEffectiveOrder");
            result.setCanPurchase(true);
            return result;
        }

        int buyTierDeviceNum = Optional.ofNullable(buyTier.getMaxDeviceNum()).orElse(0);

        //云服务套餐有此需求,
        if( buyTierDeviceNum != 1 && buyTier.getTierServiceType().equals(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())){
            //新购买的套餐只单设备需要判断
            log.debug("//云服务套餐有此需求");
            result.setCanPurchase(true);
            return result;
        }

        //当前生效的套餐的商品
        OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
        ProductDO currentProduct = productService.queryProductById(orderProductDo.getProductId());
        if(currentProduct == null){
            throw new BaseException(INVALID_PARAMS,INVALID_PARAMS.getMsg());
        }

        //当前生效的套餐
        Tier currentTier = tierService.queryTierById(currentProduct.getTierId());
        int currentDeviceNum = Optional.ofNullable(currentTier.getMaxDeviceNum()).orElse(0);

        // 套餐类型
        int buyTierType = Optional.ofNullable(buyTier.getTierType()).orElse(0);
        int currentTierType = Optional.ofNullable(currentTier.getTierType()).orElse(0);

        // 套餐续订区间，0月1年
        int buyTierPeriod = productDO.getMonth();
        int currentTierPeriod = currentProduct.getMonth();
        if(buyTierType != currentTierType){
            //不属于同类型套餐，可购买
            log.debug("buyTierType != currentTierType");
            result.setCanPurchase(true);
            return result;
        }

        int buyBillingCycleType = Optional.ofNullable(productDO.getSubscriptionPeriod()).orElse(-1);
        int buyBillingCycleDuration = Optional.of(productDO.getMonth()).orElse(-1);
        int currentBillingCycleType = Optional.ofNullable(currentProduct.getSubscriptionPeriod()).orElse(-1);
        int currentBillingCycleDuration = Optional.of(currentProduct.getMonth()).orElse(-1);

        log.debug("buyProduct is:{} and buyTier is: {}", productDO, buyTier );
        if (buyTierType == TierTypeEnums.TIER_DEVICE_LIMIT.getCode()){
            // V2套餐购买验证
            if(currentDeviceNum == 2 && buyTierPeriod == currentTierPeriod){
                //双设备购买单设备，相同套餐区间不可买
                //v2双设备包月不能买v2单设备包月
                //v2双设备包年不能买v2单设备包年
                log.debug("双设备购买单设备，相同套餐区间");
                if (removeAvailable) {
                    log.debug("removeAvailable=true, 允许购买且返回空设备集合");
                    result.setCanPurchase(true);
                    result.setTierDeviceSet(Lists.newArrayList());
                } else {
                    log.debug("removeAvailable=false, 保持原有逻辑不可购买");
                    result.setCanPurchase(false);
                }
                return result;
            }
            if(currentDeviceNum == 1 && buyTierPeriod == currentTierPeriod){
                //可买，当前套餐设备置灰不可改
                //v2单设备包月可买v2单设备包月
                //v2单设备包年可买v2单设备包年
                log.debug("可买，当前套餐设备置灰不可改");
                result.setCanPurchase(true);
                List<String> tierSerialNumberList = userTierDeviceService.queryUserTierDeviceDOListByUser(userId,tenantId)
                        .stream()
                        .filter(ud -> ud.getTierId().equals(currentTier.getTierId()))
                        .map(UserTierDeviceDO::getSerialNumber)
                        .collect(Collectors.toList());
                result.setTierDeviceSet(CollectionUtils.isEmpty(tierSerialNumberList) ? Lists.newArrayList() : Collections.singletonList(tierSerialNumberList.get(0)));
                // 查找指定的设备
                return result;
            }
            log.debug("buyTierType == TierTypeEnums.TIER_DEVICE_LIMIT");
            result.setCanPurchase(true);
            // todo 查找指定的设备
            return result;
        }else if(buyTierType == TierTypeEnums.TIER_LEVEL_DEVICE_LIMIT.getCode()){
            int buyTierTierLevel = buyTier.getLevel();
            int currentTierTierLevel = currentTier.getLevel();
            // V1套餐购买验证
            if(currentDeviceNum == 2 && buyTierPeriod == currentTierPeriod && buyTierTierLevel == currentTierTierLevel) {
                //双设备购买单设备，相同套餐区间不可买
                //v1基础版双设备包月不能买v1基础版单设备包月
                //v1基础版双设备包年不能买v1基础版单设备包年
                result.setCanPurchase(false);
                return result;
            }
            if(currentDeviceNum == 1 && buyTierPeriod == currentTierPeriod && buyTierTierLevel == currentTierTierLevel){
                //可买，当前套餐设备置灰不可改
                //v2单设备包月可买v2单设备包月
                //v2单设备包年可买v2单设备包年
                log.debug("可买，当前套餐设备置灰不可改");
                result.setCanPurchase(true);

                List<String> tierSerialNumberList = userTierDeviceService.queryUserTierDeviceDOListByUser(userId,tenantId)
                        .stream()
                        .filter(ud -> ud.getTierId().equals(currentTier.getTierId()))
                        .map(UserTierDeviceDO::getSerialNumber)
                        .collect(Collectors.toList());
                result.setTierDeviceSet(CollectionUtils.isEmpty(tierSerialNumberList) ? Lists.newArrayList() : Collections.singletonList(tierSerialNumberList.get(0)));
                return result;
            }
            log.debug("可买");
            result.setCanPurchase(true);
            // todu 查找指定的设备
            return result;
        } else if(buyTier.getTierServiceType().equals(TierServiceTypeEnums.TIER_4G.getCode())){
            int buyTierTierLevel = buyTier.getLevel();
            int currentTierTierLevel = currentTier.getLevel();
            log.debug("buyTierTierLevel is: {}, currentTierTierLevel is: {}, buyBillingCycleType is: {}, currentBillingCycleType is: {}, buyBillingCycleDuration is: {}, " +
                    "currentBillingCycleDuration is : {}", buyTierTierLevel, currentTierTierLevel, buyBillingCycleType, currentBillingCycleType, buyBillingCycleDuration, currentBillingCycleDuration);
            if(buyBillingCycleType == currentBillingCycleType && buyBillingCycleDuration == currentBillingCycleDuration &&
                    buyTierTierLevel <= currentTierTierLevel){
                //不能买低等级的套餐
                result.setCanPurchase(false);
                return result;
            }

            result.setCanPurchase(true);
            List<String> tierSerialNumberList = userTierDeviceService.queryUserTierDeviceDOListByUser(userId,tenantId)
                    .stream()
                    .filter(ud -> ud.getTierId().equals(currentTier.getTierId()))
                    .map(UserTierDeviceDO::getSerialNumber)
                    .collect(Collectors.toList());
            result.setTierDeviceSet(CollectionUtils.isEmpty(tierSerialNumberList) ? Lists.newArrayList() : tierSerialNumberList);
            boolean switchBillingCycle = !(buyBillingCycleType == currentBillingCycleType && buyBillingCycleDuration == currentBillingCycleDuration);
            result.setSwitchBillingCycle(switchBillingCycle);
            log.debug("tierSerialNumberList is:{} , switchBillingCycle is: {}", tierSerialNumberList, switchBillingCycle);
            return result;
        }else{
            log.debug("else result");
            result.setCanPurchase(true);
            return result;
        }
    }
    /**
     * 兜底选取的设备超过套餐数量限制
     * @param userTierDevice
     * @param productDO
     * @return
     */
    public List<String> initPaymentTierDeviceList(List<String> userTierDevice,ProductDO productDO){
        if(productDO.getType() < ProductTypeEnums.PRODUCT_DEVICE_NUM.getCode()){
            return Lists.newArrayList();
        }
        if(CollectionUtils.isEmpty(userTierDevice)){
            return Lists.newArrayList();
        }

        Tier tier = tierService.queryTierById(productDO.getTierId());
        if(tier.getMaxDeviceNum() == null){
            return userTierDevice;
        }

        return userTierDevice.size() > tier.getMaxDeviceNum() ? userTierDevice.subList(0,tier.getMaxDeviceNum()) : userTierDevice;
    }

    /**
     * 批次查询流水记录
     * @return
     */
    public List<PaymentFlow> queryPaymentBatch(List<String> tradeNos){
        if(CollectionUtils.isEmpty(tradeNos)){
            return Lists.newArrayList();
        }
        return iPaymentFlowDAO.queryPaymentFlowBatch(tradeNos);
    }
    /**
     * 更新账单过期时间
     * @param tradeNo
     * @param expireTime
     */
    public void updatePaymentFlowExpire(String tradeNo,Integer expireTime){
        iPaymentFlowDAO.updatePaymentFlow(tradeNo,expireTime);
    }
}
