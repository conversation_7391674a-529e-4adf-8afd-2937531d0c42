package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.RotationPointRequest;
import com.addx.iotcamera.bean.app.RotationPointSaveRequest;
import com.addx.iotcamera.bean.db.RotationPointDo;
import com.addx.iotcamera.dao.RotationPointDao;
import com.addx.iotcamera.helper.DeviceOperationHelper;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.requests.DeviceRotateToPointRequest;
import lombok.NonNull;
import org.addx.iot.common.vo.Result;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;

@Component
public class RotationPointService {

    private static final int MAX_ROTATION_COUNT = 5;
    @Autowired
    private S3Service s3Service;

    @Autowired
    private RotationPointDao rotationPointDao;

    @Autowired
    private VernemqPublisher vernemqPublisher;

    @Autowired
    private RedisService redisService;

    @Autowired
    private DeviceOperationHelper deviceOperationHelper;

    @Value("${s3.bucket}")
    private String ROTATION_THUMBNAIL_BUCKET;

    private static final String ROTATION_THUMBNAIL_PREFIX = "rotation_thumbnail/";

    private static final Logger LOGGER = LoggerFactory.getLogger(RotationPointService.class);

    public Result saveRotationPoint(@NonNull RotationPointSaveRequest rotationPointSaveRequest) {
        try {

            String serialNumber = rotationPointSaveRequest.getSerialNumber();

            //校验预设位置个数是否已超
            List<RotationPointDo> list = rotationPointDao.queryDeviceRotationPoints(serialNumber);
            if(CollectionUtils.isNotEmpty(list) && list.size()>=MAX_ROTATION_COUNT){
                return Result.Error(INVALID_PARAMS, "BEYOND_MAX_ROTATION_COUNT");
            }

            String thumbnailUrl = uploadRotationThumbnail(rotationPointSaveRequest);

            RotationPointDo rotationPointDo = RotationPointDo.builder()
                    .serialNumber(serialNumber)
                    .rotationPointName(rotationPointSaveRequest.getRotationPointName())
                    .thumbnailUrl(thumbnailUrl)
                    .coordinate(rotationPointSaveRequest.getCoordinate())
                    .build();
            rotationPointDao.insertRotationPoint(rotationPointDo);
            rotationPointDo.setThumbnailUrl(s3Service.preSignUrl(thumbnailUrl));
            return new Result(rotationPointDo);
        } catch (Exception ex) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "Failed to save rotation point for {}.", rotationPointSaveRequest.getSerialNumber(), ex);
        }
        return Result.OperationResult(false);
    }

    public List<RotationPointDo> getRotationPoints(String serialNumber) {
        List<RotationPointDo> rotationPoints = rotationPointDao.queryDeviceRotationPoints(serialNumber);

        rotationPoints.forEach(rotationPointDo -> rotationPointDo.setThumbnailUrl(s3Service.preSignUrl(rotationPointDo.getThumbnailUrl())));

        return rotationPoints;
    }

    public Result rotateToPoint(RotationPointRequest rotationPointRequest) {
        if (vernemqPublisher.rotateToPoint(new DeviceRotateToPointRequest(rotationPointRequest))) {
            return Result.Success();
        } else {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "Failed to send rotateToPoint MQTT message to device {}", rotationPointRequest.getSerialNumber());
            return Result.OperationResult(false);
        }
    }

    public Result deleteRotationPoint(RotationPointRequest rotationPointRequest) {
        //增加序列号，避免删除非本人的位置
        return Result.SqlOperationResult(rotationPointDao.deleteRotationPoint(rotationPointRequest.getPointId(),
                rotationPointRequest.getSerialNumber()));
    }

    private String uploadRotationThumbnail(RotationPointSaveRequest rotationPointSaveRequest) throws IOException {
        String fileName = ROTATION_THUMBNAIL_PREFIX + rotationPointSaveRequest.getSerialNumber()
                + "_" + rotationPointSaveRequest.getThumbnail().getOriginalFilename().hashCode();

        return s3Service.uploadObject(ROTATION_THUMBNAIL_BUCKET, fileName, rotationPointSaveRequest.getThumbnail());
    }

    /**
     * 根据设备序列号删除收藏点
     *
     * @param serialNumber
     */
    public void deleteRotationPointBySerialNumber(String serialNumber) {
        rotationPointDao.deleteRotationPointBySerialNumber(serialNumber);
    }
}
