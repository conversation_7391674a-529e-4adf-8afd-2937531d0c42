package com.addx.iotcamera.service.firmware;

import com.addx.iotcamera.bean.db.FirmwareBuildInfoTableDO;
import com.addx.iotcamera.bean.db.FirmwareTableDO;
import com.addx.iotcamera.bean.db.device.FirmwareSubVersionTableDO;
import com.addx.iotcamera.bean.device.DeviceCdCertRequest;
import com.addx.iotcamera.bean.device.DeviceCdCertResponse;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.DeviceOTADO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.manage.*;
import com.addx.iotcamera.bean.response.device.ModelFirmwareUpgradeCount;
import com.addx.iotcamera.config.CloudfrontConfig;
import com.addx.iotcamera.config.OtaConfig;
import com.addx.iotcamera.config.OtaStartConfig;
import com.addx.iotcamera.config.device.DeviceOTADebugConfig;
import com.addx.iotcamera.config.firmware.FirmwareBuildGroupConfig;
import com.addx.iotcamera.constants.DeviceInfoConstants;
import com.addx.iotcamera.dao.IFirmwareBuildDAO;
import com.addx.iotcamera.dao.IFirmwareDAO;
import com.addx.iotcamera.dao.IFirmwareSubVersionDAO;
import com.addx.iotcamera.dao.device.CdCertificationInfoDAO;
import com.addx.iotcamera.dao.device.IDeviceManualDAO;
import com.addx.iotcamera.enums.DeviceOtaStatusEnums;
import com.addx.iotcamera.enums.EErrorCode;
import com.addx.iotcamera.enums.FirmwareBuildEnums;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.helper.DeviceOperationHelper;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.requests.OTAStartRequest;
import com.addx.iotcamera.publishers.vernemq.requests.OTAValue;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceDormancyPlanService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.FirmwareSubVersionService;
import com.addx.iotcamera.service.device.model.DeviceModelTenantService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.util.HttpUtils;
import com.addx.iotcamera.util.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.PageResult;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.maven.artifact.versioning.DefaultArtifactVersion;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;
import static com.addx.iotcamera.constants.FirmwareConstants.*;
import static com.addx.iotcamera.constants.ReportLogConstants.*;
import static com.addx.iotcamera.enums.FirmwareUpgradeTypeEnums.NEGLIGIBLE;
import static org.addx.iot.common.enums.ResultCollection.DEVICE_DORMANCY_STATUS;
import static org.addx.iot.common.enums.ResultCollection.DEVICE_NO_RESPONSE;

@Component
@Slf4j
public class FirmwareService {

    //单位秒
    private static final Integer KX_OTA_TIMEOUT_VALUE = 30 * 60;
    private static final String TOKEN = "481ca84f84cf29c74b5ae93672f6b321";
    public static final int BX_FILE_EXPIRE_TIME_MILLISECOND = KX_OTA_TIMEOUT_VALUE * 1000;
    @Autowired
    private IFirmwareDAO firmwareDAO;

    @Autowired
    private DeviceOperationHelper deviceOperationHelper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private OtaConfig otaConfig;

    @Resource
    private DeviceManualService deviceManualService;

    @Resource
    private FirmwareCacheService firmwareCacheService;

    @Resource
    private UserRoleService userRoleService;

    @Value("${otaUnlimitedUsers:}")
    @Getter
    @Setter
    private String otaUnlimitedUsers; // 从Apollo中配置

    @Value("${otaUnlimitedDevices:}")
    @Getter
    @Setter
    private String otaUnlimitedDevices; // 从Apollo中配置

    @Autowired
    private OtaStartConfig otaStartConfig;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private DeviceDormancyPlanService deviceDormancyPlanService;

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private CloudfrontService cloudFrontService;

    @Autowired
    private ReportLogService reportLogService;

    @Autowired
    private OtaDefaultCopywriteConfig otaDefaultCopywriteConfig;

    @Autowired
    private DeviceOTADebugConfig deviceOTADebugConfig;

    @Autowired
    private FirmwareSubVersionService firmwareSubVersionService;

    @Autowired
    private IFirmwareBuildDAO firmwareBuildDAO;

    @Autowired
    private IFirmwareSubVersionDAO deviceSubVersionDAO;

    @Autowired
    private FactoryDataQueryService factoryDataQueryService;

    @Autowired
    private FirmwareBuildGroupConfig firmwareBuildGroupConfig;

    @Autowired
    private DeviceModelTenantService deviceModelTenantService;

    @Resource
    private IDeviceManualDAO deviceManualDAO;

    @Resource
    private CdCertificationInfoDAO cdCertificationInfoDAO;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    private static final String JENKINS_BUILD_URL = "http://10.0.6.46:8080/bxsoft_build";

    public static final int STATUS_BEHIND_LATEST_FIRMWARE = 1;
    private static final int STATUS_IGNORED = 2;
    public static final int STATUS_IN_PROGRESS = 4;
    // 强制升级标记位
    private static final int STATUS_HAS_TO_UPDATE = 8;
    // 建议升级标记位
    private static final int STATUS_RECOMMEND_UPGRADE = 16;
    public static final String OTA_IGNORE_FURTHER_VERSION_FORMAT = "devota:%s";
    private static final int OTA_IGNORE_FURTHER_VERSION_TIMEOUT = 7200;
    private static final long DEFAULT_OTA_PACKET_SIZE = 65536;

    private static final String OTA_TIMEOUT_KEY = "timeout";
    private static final int OTA_TIMEOUT_VALUE = 600;

    public List<OTADBValue> getAllOtaInfoForDevice(String serialNumber,String requestModelNo) {
        //兼容旧接口x
        final String DEFAULT_MODEL_NO = "G0";
        String modelNo = "";
        if (StringUtils.isEmpty(serialNumber)) {
            modelNo = DEFAULT_MODEL_NO;
        } else {
            modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        }

        //cx没有绑定到云端，兼容一下，可以根据modelno就能
        if(StringUtils.isEmpty(modelNo)){
            modelNo = requestModelNo;
        }

        boolean isOtaDebugWhiteSn = deviceOTADebugConfig.getOtaDebugWhiteSnSet().contains(serialNumber);
        boolean isOtaDebugBlackSn = deviceOTADebugConfig.getOtaDebugBlackSnSet().contains(serialNumber);
        Boolean useDebug = isOtaDebugWhiteSn ? null : false;
        List<OTADBValue> otadbValueList = firmwareDAO.selectOTAInfoByModelNo(modelNo, useDebug);
        if (!CollectionUtils.isEmpty(otadbValueList)) {
            otadbValueList.sort(Collections.reverseOrder());
        }

        if(isOtaDebugBlackSn) {
            // 不让isOtaDebugBlackSn的设备继续ota到更高版本，便于定位问题
            DeviceDO deviceInfo = deviceService.getAllDeviceInfo(serialNumber);
            if(deviceInfo != null && !StringUtils.isEmpty(deviceInfo.getFirmwareId())) {
                otadbValueList = otadbValueList.stream().filter(otadbValue -> PhosUtils.VersionCodeCompare(otadbValue.getFirmwareId(), deviceInfo.getFirmwareId()) <= 0).collect(Collectors.toList());
            }
        }

        //以前的固件只需要选版本，现在CX的下载地址必须是可用的
        if( !CollectionUtils.isEmpty(otadbValueList) ){
            for(OTADBValue item : otadbValueList) {
                //带限时和签名的下载地址
                String cloudfrontUrl = null;
                try {
                    cloudfrontUrl = cloudFrontService.createCloudfrontUrl(CloudfrontConfig.Keys.firmware.name(), item.getPath(), null);
                } catch (Exception e) {
                    com.addx.iotcamera.util.LogUtil.error(log, "startDeviceOTAWithVersion 生成cloudfrontUrl失败", e);
                }
                item.setPath(cloudfrontUrl);
                item.setSize(item.getSize());
                item.setTimeout(OTA_TIMEOUT_VALUE);
            }
        }

        return otadbValueList;
    }


    public List<OTADBValue> getAllBxOtaInfoForDevice(String version, String modelNo, String serialNumber,int pageNo, int pageSize) {
        // 计算起始索引
        int startIndex = (pageNo - 1) * pageSize;


        List<OTADBValue> otadbValueList = firmwareDAO.selectBxOTAInfoByModelNo(modelNo, null, startIndex, pageSize);
        if (!CollectionUtils.isEmpty(otadbValueList)) {
            otadbValueList.sort(Collections.reverseOrder());
        }

        otadbValueList.forEach(item->{
            List<ApplicationViewDo> apps = getBxApplications(modelNo, item.getFirmwareId());
            item.setApplications( apps );
            item.setTimeout(KX_OTA_TIMEOUT_VALUE);
        });


        return otadbValueList;
    }

    public FirmwareViewDO buildFirmwareView(String serialNumber, String language) {
        // 可升级的最新固件版本
        FirmwareTableDO latestFirmware = getProperFirmwareBySerialNumber(serialNumber);
        if (latestFirmware == null) {
            // 没有当前设备型号对应固件
            return null;
        }
        DeviceDO deviceInfo = deviceService.getAllDeviceInfo(serialNumber);

        OTAProgressDO deviceOtaInfo = firmwareDAO.getOtaProgress(serialNumber);

        int firmwareStatus = buildFirmwareStatus(latestFirmware, deviceInfo, deviceOtaInfo);

        int userId = userRoleService.getDeviceAdminUser(serialNumber);

        return FirmwareViewDO.builder()
                .serialNumber(serialNumber)
                .currentFirmware(deviceInfo.getFirmwareId())
                .targetFirmware(deviceOtaInfo.getTargetFirmware())
                .firmwareStatus(firmwareStatus)
                .size(latestFirmware.getSize())
                .md5(latestFirmware.getMd5())
                .modelNo(latestFirmware.getModelNo())
                .adminId(userId)
                .newestFirmwareId(latestFirmware.getFirmwareId())
                .releaseNote(queryOtaDetailCopywrite(latestFirmware.getFirmwareId(),latestFirmware.getModelNo(),language))
                .build();
    }

    /**
     * 获取固件发版描述文案
     * @param firmwareId
     * @param modelNo
     * @param language
     * @return
     */
    public List<String> queryOtaDetailCopywrite(String firmwareId,String modelNo,String language){
        List<String> releaseNote = firmwareDAO.firmwareDescriptions(firmwareId, language, modelNo);
        if(!CollectionUtils.isEmpty(releaseNote)){
            return releaseNote;
        }

        return otaDefaultCopywriteConfig.getConfig().get(language);
    }

    /**
     * 组装固件信息--此方法信息仅限于用户设备列表，如其他地方使用请确认信息是否需要增加
     *
     * @param deviceInfo
     * @param deviceOTADO
     * @return
     */
    public FirmwareViewDO buildFirmwareView(DeviceDO deviceInfo, DeviceOTADO deviceOTADO) {
        FirmwareTableDO latestFirmware = getProperFirmwareBySerialNumber(deviceInfo.getSerialNumber());
        if (latestFirmware == null) {
            return null;
        }
        OTAProgressDO deviceOtaInfo = getOtaProgress(deviceOTADO);
        int firmwareStatus = buildFirmwareStatus(latestFirmware, deviceInfo, deviceOtaInfo);
        return FirmwareViewDO.builder()
                .targetFirmware(latestFirmware.getFirmwareId())
                .firmwareStatus(firmwareStatus)
                .build();
    }

    /**
     * 是否需要提醒固件升级
     * @param currentVersion
     * @param latestFirmware
     * @return
     */
    public boolean firmwareUpgradeReminder(String currentVersion, FirmwareTableDO latestFirmware){
        if(latestFirmware.getUpgradeType().equals(NEGLIGIBLE.getCode())){
            //可忽略,不需要提醒
            return false;
        }
        // 当前固件版本小于或者登记 提醒版本
        return PhosUtils.VersionCodeCompare(currentVersion, latestFirmware.getRecommendUpgradeVersion()) <= 0;
    }

    public FirmwareTableDO getProperFirmwareBySerialNumber(String serialNumber) {
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        if (deviceDO == null) {
            com.addx.iotcamera.util.LogUtil.error(log, "获取最新可升级固件版本,设备不存在{}",serialNumber);
            throw new BaseException(ResultCollection.DEVICE_NO_ACCESS, "设备序列号找不到！serialNumber=" + serialNumber);
        }
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if(!StringUtils.hasLength(modelNo)){
            com.addx.iotcamera.util.LogUtil.error(log, "获取最新可升级固件版本,设备型号不存在{}",serialNumber);
            throw new BaseException(ResultCollection.DEVICE_NO_ACCESS, "设备型号找不到！serialNumber=" + serialNumber);
        }
        String currentVersion = deviceDO.getFirmwareId();

        // 先查询当前型号可用的最新固件
        FirmwareTableDO firmwareTableDO = getLatestActivatedFirmwareBySerialNumber(serialNumber,modelNo);
        if (firmwareTableDO == null) {
            //无此型号的固件
            return null;
        }
        // 校验是否可以升级到当前型号的最新版本固件
        if (PhosUtils.VersionCodeCompare(currentVersion, firmwareTableDO.getMinReachable()) >= 0) {
            return firmwareTableDO;
        }

        // 根据设备的当前固件版本，挑选一个合适的固件来升级
        return getLatestActivatedFirmwareByCurrentVersion(modelNo, serialNumber, currentVersion ,null, false);
    }

    /**
     * 获取最新的激活的可用的固件
     *
     * @param serialNumber
     * @return
     */
    public FirmwareTableDO getLatestActivatedFirmwareBySerialNumber(String serialNumber) {
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        return this.getLatestActivatedFirmwareBySerialNumber(serialNumber, modelNo);
    }

    public FirmwareTableDO getLatestActivatedFirmwareBySerialNumber(String serialNumber,String modelNo) {
        Integer adminId = userRoleService.getDeviceAdminUser(serialNumber);
        return getLatestActivatedFirmwareByModelNoAndAdminId(modelNo, adminId, serialNumber);
    }


    /**
     * 获取最新的激活的可用的固件
     *
     * @param modelNo
     * @param adminId
     * @return
     */
    public FirmwareTableDO getLatestActivatedFirmwareByModelNoAndAdminId(String modelNo, Integer adminId, String serialNumber) {
        List<FirmwareTableDO> firmwareList = getAllFirmwareForModel(modelNo, serialNumber);
        Collections.sort(firmwareList, Collections.reverseOrder());

        Optional<FirmwareTableDO> latestFirmware = firmwareList.stream()
                .filter(firmware -> firmware.getActivated() == 1 && firmware.getDeprecated() == 0)
                .filter(firmware -> BooleanUtils.isTrue(firmware.getUseDebug()) || allowOTA(adminId, firmware.getOtaLimit()))
                .findFirst();

        if (latestFirmware.isPresent()) {
            return latestFirmware.get();
        } else {
            // 未发现当前设备型号下可用的固件
            return null;
        }
    }

    /**
     * 获取最新已激活固件版本
     *
     * @param modelNo
     * @return
     */
    public FirmwareTableDO getLatestActivatedFirmwareByModelNo(String modelNo) {
        List<FirmwareTableDO> firmwareList = getAllFirmwareForModel(modelNo, null);
        Collections.sort(firmwareList, Collections.reverseOrder());

        Optional<FirmwareTableDO> latestFirmware = firmwareList.stream()
                .filter(firmware -> firmware.getActivated() == 1)
                .findFirst();
        if (latestFirmware.isPresent()) {
            return latestFirmware.get();
        } else {
            // 未发现当前设备型号下可用的固件
            return null;
        }
    }

    /**
     * 获取最新已激活固件版本-比指定版本小的
     *
     * @param modelNo
     * @return
     */
    public FirmwareTableDO getLatestActivatedFirmwareByModelNo(String modelNo,String firmwareId) {
        List<FirmwareTableDO> firmwareList = getAllFirmwareForModel(modelNo, null);
        Collections.sort(firmwareList, Collections.reverseOrder());

        Optional<FirmwareTableDO> latestFirmware = firmwareList.stream()
                .filter(firmware -> firmware.getActivated() == 1 && PhosUtils.VersionCodeCompare(firmware.getFirmwareId(),firmwareId) < 0)
                .findFirst();
        if (latestFirmware.isPresent()) {
            return latestFirmware.get();
        } else {
            // 未发现当前设备型号下可用的固件
            return null;
        }
    }


    /**
     * 根据设备型号和当前固件版本，寻找当前所能升级到的最高的版本
     *
     * @param modelNo
     * @param currentVersion
     * @return
     */
    private FirmwareTableDO getLatestActivatedFirmwareByCurrentVersion(String modelNo, String serialNumber, String currentVersion, String bxVersion, Boolean isBstationRelatedDevice) {
        List<FirmwareTableDO> firmwareList = getAllFirmwareForModel(modelNo, serialNumber);

        log.debug("gshen debug , firmwareList : " + JSON.toJSONString(firmwareList));
        Collections.sort(firmwareList, Collections.reverseOrder());

        if(StringUtils.hasLength(bxVersion)){
            firmwareList = firmwareList.stream().filter(item->  (PhosUtils.VersionCodeCompare(bxVersion, item.getMinBxVersion()) >= 0)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(firmwareList)){
                return null;
            }
        }

        Optional<FirmwareTableDO> latestFirmware = firmwareList.stream()
                .filter(firmware ->
                        firmware.getActivated() == 1 && firmware.getDeprecated() == 0 && (PhosUtils.VersionCodeCompare(currentVersion, firmware.getMinReachable()) >= 0)
                ).filter(firmware -> (!isBstationRelatedDevice ||  allowBstationDeviceOTA(0,firmware.getOtaLimit(), serialNumber)))
                .findFirst();

        if (latestFirmware.isPresent()) {
            return latestFirmware.get();
        } else {
            return null;
        }
    }

    public OTAProgressDO getOtaProgress(DeviceOTADO deviceOTADO) {
        OTAProgressDO otaProgressDO = new OTAProgressDO();
        otaProgressDO.setInProgress(deviceOTADO.getInProgress());
        otaProgressDO.setLastAct(deviceOTADO.getLastAct());
        otaProgressDO.setSerialNumber(deviceOTADO.getSerialNumber());
        otaProgressDO.setTargetFirmware(deviceOTADO.getTargetFirmware());
        // 超时两分钟以上，认为传输已经停止
        if (null == otaProgressDO || null == otaProgressDO.getLastAct() || PhosUtils.getUTCStamp() - otaProgressDO.getLastAct() > OTA_TIMEOUT_VALUE) {
            otaProgressDO.setInProgress(0);
        }
        otaProgressDO.setOtaStatus(deviceOTADO.getOtaStatus());
        otaProgressDO.setOtaStartTime(deviceOTADO.getOtaStartTime());
        return otaProgressDO;
    }

    public OTAProgressDO getOtaProgress(String serialNumber) {
        OTAProgressDO otaProgressDO = firmwareDAO.getOtaProgress(serialNumber);
        // 超时两分钟以上，认为传输已经停止
        if (null == otaProgressDO || null == otaProgressDO.getLastAct() || PhosUtils.getUTCStamp() - otaProgressDO.getLastAct() > OTA_TIMEOUT_VALUE) {
            otaProgressDO.setInProgress(0);
        }

        // 超时时间判断 (新逻辑根据ota开始时间判断)
        if(PhosUtils.getUTCStamp() - otaProgressDO.getOtaStartTime() > OTA_TIMEOUT_VALUE){
            otaProgressDO.setOtaStatus(DeviceOtaStatusEnums.OTA_TIME_OUT.getValue());
        }
        return otaProgressDO;
    }

    public Integer ignoreOTA(String serialNumber) {
        return firmwareDAO.ignoreOTA(serialNumber);
    }

    public Integer insertDeviceOTA(FirmwareViewDO firmwareViewDO) {
        return firmwareDAO.insertDeviceOTA(firmwareViewDO);
    }

    public Result startOtaFromApp(String serialNumber, FirmwareViewDO firmwareViewDO, boolean voiceReminder) {
        return startDeviceOTAWithVersion(null, serialNumber, firmwareViewDO.getNewestFirmwareId(), false, voiceReminder, false);
    }

    public Result startOtaFromDevice(String serialNumber, FirmwareViewDO firmwareViewDO, String otaId) {
        return startDeviceOTAWithVersion(otaId, serialNumber, firmwareViewDO.getNewestFirmwareId(), false, false, true);
    }

    /**
     * @param serialNumber         设备UID
     * @param firmwareId           所要升级到的固件的版本
     * @param ignoreFurtherVersion 为false时，如果有比传入的firmwareId更高版本的固件，则设备唤醒后需继续升级
     * @return
     */
    public Result startDeviceOTAWithVersion(String otaId, String serialNumber, String firmwareId, Boolean ignoreFurtherVersion, Boolean voiceReminder, Boolean silentOta) {
        Boolean deviceDormancyStatus = deviceDormancyPlanService.checkDeviceDormancyStatus(serialNumber);
        if (deviceDormancyStatus) {
            throw new BaseException(DEVICE_DORMANCY_STATUS, "DEVICE_DORMANCY_STATUS");
        }
        //调用kiss 唤醒设备
        deviceInfoService.wakeUpDevice(serialNumber, "");

        OTAStartRequest otaStartRequest = getOTAStartRequest(otaId, serialNumber, firmwareId, voiceReminder);

        redisService.setDeviceOperationDOWithEmpty(otaStartRequest.getId());

        sendDeviceOtaStart(serialNumber, otaStartRequest);

        // 轮询本次OTA操作状态
        log.info("waitOperation otaId:{} serialNumber:{}", otaStartRequest.getId(), serialNumber);
        Result result = deviceOperationHelper.waitOperation(otaStartRequest.getId());

        if (Result.successFlag.equals(result.getResult())) {
            DeviceOperationDO operationResult = (DeviceOperationDO) (result.getData());

            // 目前Consumer会返回 "success" 和 "fail"
            if (!"success".equals(operationResult.getMsg())) {
                com.addx.iotcamera.util.LogUtil.error(log, "Device {} refuse to start OTA with code {} otaId:{}", operationResult.getSerialNumber(), operationResult.getCode(), otaStartRequest.getId());
                //ota start log
                this.reportOtaStartLog(serialNumber,otaStartRequest.getId(), otaStartRequest.getValue().getFirmwareId(),false);
                return ResultCollection.OTA_START_REFUSED.getResult();
            }
            if (ignoreFurtherVersion) {
                redisService.set(String.format(OTA_IGNORE_FURTHER_VERSION_FORMAT, serialNumber), serialNumber, OTA_IGNORE_FURTHER_VERSION_TIMEOUT);
            }
            firmwareDAO.initOta(serialNumber, firmwareId, silentOta);
            OTAProgressDO otaProgress = firmwareDAO.getOtaProgress(serialNumber);
            //ota start log
            this.reportOtaStartLog(serialNumber,otaStartRequest.getId(), otaStartRequest.getValue().getFirmwareId(),true);
            return new Result(JSON.parseObject(JSON.toJSONString(otaProgress)).fluentPut(OTA_TIMEOUT_KEY, OTA_TIMEOUT_VALUE));
        }
        //ota start log
        this.reportOtaStartLog(serialNumber,otaStartRequest.getId(), otaStartRequest.getValue().getFirmwareId(),false);
        return result;
    }

    public OTAStartRequest getOTAStartRequest(String otaId, String serialNumber, String firmwareId, Boolean voiceReminder) {
        OTADBValue newestFirmware = queryOtaFirmware(serialNumber, firmwareId);
        log.info("Newest firmware for device {} is {} ", serialNumber, newestFirmware);
        JSONObject specifics = StringUtils.isEmpty(newestFirmware.getSpecificsText()) ? new JSONObject() : JSONObject.parseObject(newestFirmware.getSpecificsText());

        String cloudfrontUrl = null;
        try {
            cloudfrontUrl = cloudFrontService.createCloudfrontUrl(CloudfrontConfig.Keys.firmware.name(), newestFirmware.getPath(), null);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "startDeviceOTAWithVersion 生成cloudfrontUrl失败", e);
        }

        String requestId = CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID();
        OTAValue otaValue = OTAValue.builder()
                .otaId(StringUtils.isEmpty(otaId) ? requestId : otaId)
                .firmwareId(newestFirmware.getFirmwareId())
                .size(newestFirmware.getSize())
                .md5(newestFirmware.getMd5())
                .version(newestFirmware.getFirmwareId())
                .type(newestFirmware.getType())
                .specifics(specifics)
                .packetSize(getDynamicOtaPacketSize(serialNumber))
                .voiceReminder(voiceReminder)
                .signedUrl(cloudfrontUrl)
                .timeout(OTA_TIMEOUT_VALUE) // 默认600秒
                .checkLowBattery(otaStartConfig.getOtaStartCheckLowBatterWhiteSnSet().contains(serialNumber) ? 0 : 1)
                .isRetryStart(!StringUtils.isEmpty(otaId))
                .build();

        OTAStartRequest otaStartRequest = OTAStartRequest.builder()
                .id(requestId)
                .time(PhosUtils.getUTCStamp())
                .value(otaValue)
                .build();
        return otaStartRequest;
    }

    private void reportOtaStartLog(String serialNumber,String otaId,String targetFirmwareId,boolean otaStartResult){
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        Map<String, Object> map = MapUtil.builder()
                .put("sn",serialNumber)
                .put("otaId",otaId)
                .put("userId",userRoleService.getDeviceAdminUser(serialNumber))
                .put("modelNo",deviceManualService.getModelNoBySerialNumber(serialNumber))
                .put("timestamp",Instant.now().getEpochSecond())

                .put("firmware",deviceDO.getFirmwareId())
                .put("gitsha",deviceInfoService.getDeviceFirmwareBuilder(serialNumber))

                .put("firmwareNew",targetFirmwareId)
                .put("otaStartResult",otaStartResult)
                .build();

        reportLogService.reportLog(REPORT_TYPE_DEVICE_OTA_START,REPORT_GROUP_REPORT,REPORTER_APP,map);
    }

    private void sendDeviceOtaStart(String serialNumber, OTAStartRequest otaStartRequest) {
        log.info("sendDeviceOtaStart otaId:{} serialNumber:{} ", otaStartRequest.getId(), serialNumber);

        boolean deviceHasWakeUp = deviceInfoService.waitDeviceWakeUp(serialNumber);
        if (deviceHasWakeUp) {
            log.info("waitDeviceWakeUp otaId:{} serialNumber:{} ", otaStartRequest.getId(), serialNumber);

            // 设备是已唤醒状态,向消息队列推送消息，让设备开始OTA
            VernemqPublisher.startOTA(serialNumber, otaStartRequest);
            // 轮询本次OTA操作状态
            Result result = null;
            for (int i=0; i<2; i++) {
                result = deviceOperationHelper.waitOperation(otaStartRequest.getId(), DeviceInfoConstants.DEVICE_MQTT_RETRY_COUNT);
                if(Result.successFlag.equals(result.getResult())) {
                    break;
                }
            }
            if (result != null && !Result.successFlag.equals(result.getResult())) {
                log.info("{} {} sendDeviceOtaStart ota 无回复，重试一次", otaStartRequest.getId(), serialNumber);
                retrySendDeviceOtaStart(serialNumber, otaStartRequest);
            }
        } else {
            retrySendDeviceOtaStart(serialNumber, otaStartRequest);
        }
    }

    /**
     * 重试发送ota
     *
     * @param serialNumber
     * @param otaStartRequest
     */
    private void retrySendDeviceOtaStart(String serialNumber, OTAStartRequest otaStartRequest) {
        log.info("retrySendDeviceOtaStart otaId:{} serialNumber:{}", otaStartRequest.getId(), serialNumber);

        log.info("wakeUpDevice otaId:{} serialNumber:{}", otaStartRequest.getId(), serialNumber);
        deviceInfoService.wakeUpDevice(serialNumber, "");
        // 等待设备唤醒
        boolean deviceHasWakeUp = deviceInfoService.waitDeviceWakeUp(serialNumber);
        if (!deviceHasWakeUp) {
            log.info("retrySendDeviceOtaStart设备{}唤醒失败 otaId:{}", serialNumber, otaStartRequest.getId());
            throw new BaseException(DEVICE_NO_RESPONSE, "设备唤醒错误");
        }
        // 向消息队列推送消息，让设备开始OTA
        VernemqPublisher.startOTA(serialNumber, otaStartRequest);
    }


    private int buildFirmwareStatus(FirmwareTableDO latestFirmware, DeviceDO deviceInfo, OTAProgressDO
            otaProgressDO) {
        int status = 0;
        boolean isBehindLatestFirmware = isBehindLatestFirmware(latestFirmware, deviceInfo);
        boolean isIgnored = deviceInfo.getOtaIgnored() == 1;
        boolean isInProgress = otaProgressDO.getInProgress() == 1 && PhosUtils.getUTCStamp() - otaProgressDO.getOtaStartTime() < OTA_TIMEOUT_VALUE;
        boolean hasToUpdate = isBehindLatestFirmware && hasToUpdate(latestFirmware, deviceInfo);
        boolean recommendUpgrade = isBehindLatestFirmware && !hasToUpdate && firmwareUpgradeReminder(deviceInfo.getFirmwareId(),latestFirmware);
        if (isBehindLatestFirmware) {
            status += STATUS_BEHIND_LATEST_FIRMWARE;
        }
        if (isIgnored) {
            status += STATUS_IGNORED;
        }
        if (isInProgress) {
            status += STATUS_IN_PROGRESS;
        }
        if (hasToUpdate) {
            status += STATUS_HAS_TO_UPDATE;
        }
        if(recommendUpgrade){
            status += STATUS_RECOMMEND_UPGRADE;
        }
        return status;
    }

    private boolean isBehindLatestFirmware(FirmwareTableDO latestFirmware, DeviceDO deviceInfo) {
        DefaultArtifactVersion latestVersion = new DefaultArtifactVersion(latestFirmware.getFirmwareId());
        DefaultArtifactVersion currentVersion = new DefaultArtifactVersion(deviceInfo.getFirmwareId());

        return latestVersion.compareTo(currentVersion) > 0;
    }

    private boolean hasToUpdate(FirmwareTableDO latestFirmware, DeviceDO device) {
        DefaultArtifactVersion minSupportVersion = new DefaultArtifactVersion(latestFirmware.getMinSupport());
        DefaultArtifactVersion currentVersion = new DefaultArtifactVersion(device.getFirmwareId());

        // 先拿最小版本判断, 再拿特殊版本判断
        return minSupportVersion.compareTo(currentVersion) >= 0 ||
                firmwareCacheService.firmwareNeedForceUpgrade(latestFirmware.getModelNo(), device.getFirmwareId());
    }


    public OTADBValue queryOtaFirmware(String serialNumber, String firmwareId) {
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        return firmwareDAO.selectFirmwareByIdAndModelNo(firmwareId, modelNo);
    }

    // 当前只根据设备的型号而定，未来可以根据用户的网络情况进行动态调整
    private long getDynamicOtaPacketSize(String serialNumber) {
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if (otaConfig.getPacket().containsKey(modelNo)) {
            return otaConfig.getPacket().get(modelNo);
        }
        return DEFAULT_OTA_PACKET_SIZE;
    }


    // 如果是100%，就全部能升级 ；
    // 如果是灰度，先过设备白名单，在白名单里，允许升级
    // 最后用序列号前6位做hash 看是否命中 ，例如 0% 对应 10000000:1 ，需要 序列号前6位%10000000 ，看结果是否为1 ，取6位避免不均匀情况
    public boolean allowBstationDeviceOTA(Integer adminId, String otaLimit, String serialNumber) {

        try {
            if (StringUtils.isEmpty(otaLimit)) {
                return true;
            }

            // 看看在不在设备白名单中
            Set<String> whiteDevices = getOtaUnlimitedDevicesSet();
            if (whiteDevices.contains(serialNumber)) {
                return true;
            }

            String[] array = otaLimit.split(":");
            Integer count = Integer.valueOf(array[0]);

            Integer id = Integer.parseInt(serialNumber.substring(0,6),16);

            int shard = id % count;
            for (String allowedShard : array[1].split(",")) {
                if (Integer.parseInt(allowedShard) == shard) {
                    return true;
                }
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "检查otaLimit错误 " + e.getMessage(), e);
        }

        return false;
    }


    /**
     * 检查是否允许ota
     *
     * @param adminId
     * @param otaLimit 格式为 10:1,2,3 表示10个分片，hash值是1、2、3的允许 如果为空 表示不限制
     * @return
     */
    public boolean allowOTA(Integer adminId, String otaLimit) {
        try {
            if (StringUtils.isEmpty(otaLimit)) {
                return true;
            }

            // 看看在不在白名单中
            if (getOtaUnlimitedUsersSet().contains(adminId.toString())) {
                return true;
            }

            String[] array = otaLimit.split(":");
            Integer count = Integer.valueOf(array[0]);

            int shard = adminId % count;
            for (String allowedShard : array[1].split(",")) {
                if (Integer.parseInt(allowedShard) == shard) {
                    return true;
                }
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "检查otaLimit错误 " + e.getMessage(), e);
        }

        return false;
    }

    public Set<String> getOtaUnlimitedUsersSet() {
        try {
            return Arrays.stream(otaUnlimitedUsers.split(","))
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "otaUnlimitedUsers 不合法 : {}", otaUnlimitedUsers);
            return new HashSet<>();
        }
    }

    public Set<String> getOtaUnlimitedDevicesSet() {
        try {
            return Arrays.stream(otaUnlimitedDevices.split(","))
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "otaUnlimitedDevices 不合法 : {}", otaUnlimitedDevices);
            return new HashSet<>();
        }
    }

    public List<FirmwareTableDO> getAllFirmwareForModel(String modelNo, String serialNumber) {
        List<FirmwareTableDO> firmwareTableDOList = null;
        if(StringUtils.isEmpty(serialNumber)) {
            firmwareTableDOList = firmwareDAO.getAllFirmwareForModel(modelNo, null);
        } else {
            boolean isOtaDebugWhiteSn = deviceOTADebugConfig.getOtaDebugWhiteSnSet().contains(serialNumber);
            boolean isOtaDebugBlackSn = deviceOTADebugConfig.getOtaDebugBlackSnSet().contains(serialNumber);
            Boolean useDebug = isOtaDebugWhiteSn ? null : false;
            firmwareTableDOList = firmwareDAO.getAllFirmwareForModel(modelNo, useDebug);
            if (!CollectionUtils.isEmpty(firmwareTableDOList)) {
                firmwareTableDOList.sort(Collections.reverseOrder());
            }

            if(isOtaDebugBlackSn) {
                // 不让isOtaDebugBlackSn的设备继续ota到更高版本，便于定位问题
                DeviceDO deviceInfo = deviceService.getAllDeviceInfo(serialNumber);
                if(deviceInfo != null && !StringUtils.isEmpty(deviceInfo.getFirmwareId())) {
                    firmwareTableDOList = firmwareTableDOList.stream().filter(firmwareTableDO -> PhosUtils.VersionCodeCompare(firmwareTableDO.getFirmwareId(), deviceInfo.getFirmwareId()) <= 0).collect(Collectors.toList());
                }
            }
        }

        return firmwareTableDOList;
    }

    public OtaLatestVersionViewDO getLatestVersion(Integer userId, String serialNumber, String currentVersion, String modelNo, Integer deviceType, String bxVersion) {
        //设备可能没在云端注册，直接使用modelNo查询

        FirmwareTableDO latestFirmware = getLatestActivatedFirmwareByCurrentVersion(modelNo, serialNumber, currentVersion, bxVersion , true);

        if(latestFirmware == null){
            return  null;
        }
        DefaultArtifactVersion latestVersion = new DefaultArtifactVersion(latestFirmware.getFirmwareId());
        //对于基站设备，云端库里版本只是出厂版本，需要使用客户端请求里的版本号
        DefaultArtifactVersion nowVersion = new DefaultArtifactVersion(currentVersion);

        boolean isBehindLatestFirmware = latestVersion.compareTo(nowVersion) > 0;

        DefaultArtifactVersion minSupportVersion = new DefaultArtifactVersion(latestFirmware.getMinSupport());
        nowVersion = new DefaultArtifactVersion(currentVersion);

        // 先拿最小版本判断, 再拿特殊版本判断
        boolean hasToUpdate =  minSupportVersion.compareTo(nowVersion) >= 0 ||
                firmwareCacheService.firmwareNeedForceUpgrade(latestFirmware.getModelNo(), currentVersion);

        hasToUpdate = isBehindLatestFirmware && hasToUpdate;

        boolean recommendUpgrade = isBehindLatestFirmware && !hasToUpdate && firmwareUpgradeReminder(currentVersion,latestFirmware);

        log.debug(" gshen debug, latestFirmware {} , hasToUpdate={}, recommendUpgrade={}",JSON.toJSONString(latestFirmware), hasToUpdate, recommendUpgrade);

        if(hasToUpdate || recommendUpgrade){
            OtaLatestVersionViewDO otaLatestVersionViewDO = new OtaLatestVersionViewDO();
            otaLatestVersionViewDO.setVersion(latestFirmware.getVersion());
            //带限时和签名的下载地址
            String cloudfrontUrl = null;
            try {
                cloudfrontUrl = cloudFrontService.createCloudfrontUrl(CloudfrontConfig.Keys.firmware.name(), latestFirmware.getPath(), null);
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.error(log, "startDeviceOTAWithVersion 生成cloudfrontUrl失败", e);
            }
            otaLatestVersionViewDO.setPath(cloudfrontUrl);
            otaLatestVersionViewDO.setMd5(latestFirmware.getMd5());
            otaLatestVersionViewDO.setGitsha(latestFirmware.getGitsha());
            otaLatestVersionViewDO.setSize(latestFirmware.getSize());
            //0:非debug 1:debug固件
            otaLatestVersionViewDO.setUseDebug((latestFirmware.getUseDebug()!=null && latestFirmware.getUseDebug()) ? 1 : 0);
            otaLatestVersionViewDO.setUpgradeType(latestFirmware.getUpgradeType());
            otaLatestVersionViewDO.setTimeout(OTA_TIMEOUT_VALUE);

            if(deviceType != null && deviceType.equals( DeviceModelCategoryEnums.BX.getCode())){
                otaLatestVersionViewDO.setTimeout(KX_OTA_TIMEOUT_VALUE);
                List<ApplicationViewDo> apps = getBxApplications(modelNo, latestFirmware.getFirmwareId());
                otaLatestVersionViewDO.setApplications( apps );
            }

            return otaLatestVersionViewDO;
        }
        return null;
    }

    @NotNull
    public List<ApplicationViewDo> getBxApplications(String modelNo, String firmwareId) {
        String cloudfrontUrl;
        //获取子版本下载地址
        List<ApplicationViewDo> apps = new ArrayList<>();
        List<FirmwareSubVersionTableDO> subversions = firmwareSubVersionService.getSubversions(firmwareId, modelNo);
        log.info("gshen debug , subversions : " + JSON.toJSONString(subversions));

        if( !CollectionUtils.isEmpty(subversions) ){
            //数量不大，且不是用唯一id标识的，没法in，先循环查，实现基本功能，再优化
            for(FirmwareSubVersionTableDO item : subversions) {
                FirmwareTableDO firmwareTableDO = firmwareDAO.getFirmwareByPkey(modelNo, item.getFirmwareId(), item.getApplication(),null);
                if(firmwareTableDO == null){
                    log.info("getFirmwareByPkey is null : {},{},{}",modelNo, item.getFirmwareId(), item.getApplication());
                    continue;
                }
                ApplicationViewDo applicationViewDo = new ApplicationViewDo();
                applicationViewDo.setName(firmwareTableDO.getApplication());
                applicationViewDo.setVersion(firmwareTableDO.getVersion());
                //带限时和签名的下载地址
                cloudfrontUrl = null;
                try {
                    cloudfrontUrl = cloudFrontService.createCloudfrontUrl(CloudfrontConfig.Keys.firmware.name(), firmwareTableDO.getPath(), BX_FILE_EXPIRE_TIME_MILLISECOND);
                } catch (Exception e) {
                    com.addx.iotcamera.util.LogUtil.error(log, "startDeviceOTAWithVersion 生成cloudfrontUrl失败", e);
                }
                applicationViewDo.setPath(cloudfrontUrl);
                applicationViewDo.setSize(firmwareTableDO.getSize());
                applicationViewDo.setMd5(firmwareTableDO.getMd5());
                applicationViewDo.setGitsha(firmwareTableDO.getGitsha());
                apps.add(applicationViewDo);
            }
        }
        return apps;
    }

    public Boolean buildBxVersion(FirmwareLinkRequest request) {
        if(!checkMd5(request.getRequestTime(),request.getAuth())){
            log.error("buildBxVersion auth faild , {}", JSON.toJSONString(request));
            if(activeProfile !=null && activeProfile.contains("prod")){
                throw new BaseException(EErrorCode.NO_PERMISSION.getCode(), "无权限构建");
            }
        }

        if(StringUtils.isEmpty(request.getRootFirmwareId())||CollectionUtils.isEmpty(request.getAppFirmwareIdMap())){
            throw new BaseException(EErrorCode.BAD_REQUEST.getCode(), "版本不能为空");
        }

        //check是否已发布
        String platform = DEFAULT_BX_PLATFORM;
        if(!StringUtils.isEmpty(request.getPlatform())){
            platform = request.getPlatform();
        }
        FirmwareTableDO firmwareTableDO = firmwareDAO.getFirmwareByPkey(null, request.getRootFirmwareId(), APPLICATION_BXSOFT, FIRMWARE_ACTIVATED);
        if(firmwareTableDO !=null ){
            throw new BaseException(EErrorCode.BAD_REQUEST.getCode(), "固件版本已发布，请重新输入版本");
        }

        String firmwareId = request.getRootFirmwareId();
        //同时构建一个debug版本， 放在前边
        if(request.getUseDebug()>0) {
            Boolean useDebug = true;
            request.setRootFirmwareId(firmwareId+"-d");
            insertDbAndCallJenkins(request, platform, useDebug);

            //重新设置回来
            request.setRootFirmwareId(firmwareId);
        }


        insertDbAndCallJenkins(request, platform, false);

        return true;
    }

    private void insertDbAndCallJenkins(FirmwareLinkRequest request, String platform, Boolean useDebug) {
        //插入构建数据和版本数据
        FirmwareBuildInfoTableDO buildInfo = new FirmwareBuildInfoTableDO();
        buildInfo.setFirmwareId(request.getRootFirmwareId());
        buildInfo.setPlatform(platform);
        int time = (int) (System.currentTimeMillis() / 1000);
        buildInfo.setCreateTime(time);
        buildInfo.setStatus(FirmwareBuildEnums.INIT.getCode());
        buildInfo.setUpdateTime(time);
        firmwareBuildDAO.insertNewFirmwareBuildInfo(buildInfo);

        //插入版本关联信息
        insertSubVersions(request);

        //请求jenkins接口
        Map<String, Object> map = new HashMap<>(request.getAppFirmwareIdMap());
        map.put("version", request.getRootFirmwareId());
        map.put("id", buildInfo.getId());
        map.put("use_debug",String.valueOf(useDebug));

        //单元测试，不请求远程接口
        if (!"true".equals(System.getProperty("unit-test"))) {
            String result = HttpUtils.httpPostPojo(map, JENKINS_BUILD_URL);
            log.info("request jenkins build kxsoft , {}, result={}", map, result);
        }
    }

    //Auth = md5(token+str(requestTime))
    private boolean checkMd5(Long requestTime, String auth) {
        if(requestTime == null || auth == null){
            return false;
        }
        String expectAuth = getMD5(TOKEN + requestTime.toString());
        log.info("checkmd5 auth={} , requestTime = {} , expectAuth={}", auth, requestTime, expectAuth);
        return expectAuth.equals(auth);
    }

    public static String getMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());

            StringBuilder sb = new StringBuilder();
            for (byte b : messageDigest) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        return null;
    }

    private void insertSubVersions(FirmwareLinkRequest request) {
        try {
            String platform = DEFAULT_BX_PLATFORM;
            if(!StringUtils.isEmpty(request.getPlatform())){
                platform = request.getPlatform();
            }
            List<String> modelNoList = factoryDataQueryService.queryPlatformModelNo(platform);

            for (String modelNo : modelNoList) {
                for (Map.Entry<String, String> kv : request.getAppFirmwareIdMap().entrySet()) {
                    FirmwareSubVersionDO subVersion = new FirmwareSubVersionDO();
                    subVersion.setApplication(kv.getKey());
                    subVersion.setFirmwareId(kv.getValue());
                    subVersion.setModelNo(modelNo);
                    subVersion.setRootFirmwareId(request.getRootFirmwareId());
                    deviceSubVersionDAO.insertFirmwareSubVersion(subVersion);
                    log.info("insertDeviceSubVersion : " + JSON.toJSONString(subVersion));
                }
            }

        }catch (Exception e){
            log.error("linkFirmware error, " , e);
        }
    }

    public boolean updateBxBuildInfo(KernelFirmwareRequest request) {
        FirmwareBuildInfoTableDO firmwareBuildInfoTableDO = firmwareBuildDAO.getFirmware(request.getId());
        if(firmwareBuildInfoTableDO != null) {
            firmwareBuildInfoTableDO.setGitsha(request.getGitsha());
            firmwareBuildInfoTableDO.setMd5(request.getMd5());
            firmwareBuildInfoTableDO.setPath(request.getDownloadUrl());
            firmwareBuildInfoTableDO.setStatus(request.getBuildSuccess() ? FirmwareBuildEnums.SUCCESS.getCode() : FirmwareBuildEnums.FAILED.getCode());
            firmwareBuildInfoTableDO.setUpdateTime((int) (System.currentTimeMillis() / 1000));
            int flag = firmwareBuildDAO.updateFirmwareDO(firmwareBuildInfoTableDO);
            log.info("updateBxBuildInfo : {} ", JSON.toJSONString(firmwareBuildInfoTableDO));
            return flag > 0;
        }
        //not find build info
        return false;
    }


    public List<String>  queryBxBuildVersions() {
        String platform = DEFAULT_BX_PLATFORM;
        List<String>  ids =  firmwareBuildDAO.getFirmwareIds(platform);

        return ids;
    }

    public PageResult getBxBuildList(BXBuildListRequest request) {
        List<FirmwareBuildInfoTableDO> list = firmwareBuildDAO.queryBuildInfoList(request.getFirmwareId(), request.getStartTime(), request.getEndTime(), request.getStatus(), (request.getPage() - 1) * request.getPageSize(), request.getPageSize());
        Long total = firmwareBuildDAO.queryBuildInfoCount(request.getFirmwareId(), request.getStartTime(), request.getEndTime(), request.getStatus());
        PageResult pageResult = PageResult.builder()
                .list(list)
                .total(total)
                .build();
        return pageResult;
    }

    //只展示CX BXSoft
    public PageResult getPubList(PubListRequest request) {

        Map<String, Object> extMap = new HashMap<>();
        List<String> platFormModelNos = new ArrayList<>();
        //查询并去掉空平台
        List<String> platforms = factoryDataQueryService.queryAllPlatforms().
                stream().filter(item->!StringUtils.isEmpty(item)).collect(Collectors.toList());

        extMap.put("platforms", platforms) ;

        if(request.getPlatform() == null) {
            //取第一个平台
            platFormModelNos = factoryDataQueryService.queryPlatformModelNo(platforms.get(0));
            extMap.put("defaultPlatform",platforms.get(0));
        }else{
            platFormModelNos = factoryDataQueryService.queryPlatformModelNo(request.getPlatform() );
        }

        Set<String> groups = firmwareBuildGroupConfig.getConfig().keySet();
        List<String> allGroups = new ArrayList<>();
        allGroups.add("all");
        allGroups.add("other");
        allGroups.addAll(groups);
        extMap.put("groups", allGroups) ;


        if(request.getGroup() != null && !request.getGroup().equals("all")) {
            if (request.getGroup().equals("other")) {
                List<String> groupModelNos = new ArrayList<>();
                Collection<List<String>> items = firmwareBuildGroupConfig.getConfig().values();
                for (List<String> item : items) {
                    groupModelNos.addAll(item);
                }
                //只取不在group的modelno
                platFormModelNos = platFormModelNos.stream().filter(item -> !groupModelNos.contains(item)).collect(Collectors.toList());
            } else {
                //特定的group
                List<String> groupModelNos = firmwareBuildGroupConfig.getConfig().get(request.getGroup());
                //只取在group的modelno
                platFormModelNos = platFormModelNos.stream().filter(groupModelNos::contains).collect(Collectors.toList());
            }
        }

        //平台和分组决定了有哪些版本
        List<String> firmwareIds = firmwareDAO.getAllFirmwareIdsForModels(platFormModelNos, null, null);

        extMap.put("firmwareIds", firmwareIds) ;


        String firmwareId = request.getFirmwareId();
        if(firmwareId  == null && !CollectionUtils.isEmpty(firmwareIds)){
            firmwareId = firmwareIds.get(0);
            extMap.put("defaultFirmwareId",firmwareId);
        }

        if(firmwareId  == null){
            throw new BaseException("没有符合条件的版本");
        }

        List<FirmwareTableDO> firmwareDoList = firmwareDAO.getAllFirmwareForModels(platFormModelNos, firmwareId, (request.getPage()-1)* request.getPageSize(), request.getPageSize());

        Map<String, List<String>> modelTenantMap = deviceModelTenantService.queryDeviceModelTenantDOByModel(platFormModelNos);

        List<ModelFirmwareUpgradeCount> upgradeCountList = deviceManualDAO.queryModelFirmwareUpgradeCount(platFormModelNos, firmwareId);

        buildTenantAndUpgradeRate(firmwareDoList, modelTenantMap, upgradeCountList);



        Long total = firmwareDAO.getAllFirmwareCountForModels(platFormModelNos, firmwareId);
        return PageResult.builder()
                .list(firmwareDoList)
                .total(total)
                .extMap(extMap)
                .build();
    }

    private void buildTenantAndUpgradeRate(List<FirmwareTableDO> firmwareDoList, Map<String, List<String>> modelTenantMap, List<ModelFirmwareUpgradeCount> upgradeCountList) {

        Map<String, ModelFirmwareUpgradeCount> modelCountMap = upgradeCountList.stream().collect(Collectors.toMap(ModelFirmwareUpgradeCount::getModelNo, Function.identity()));

        firmwareDoList.forEach(item->{
            String maxFirmwareId = "";
            List<String> activatedFirmwareIds = firmwareDAO.getAllFirmwareIdsForModels(Collections.singletonList(item.getModelNo()), FIRMWARE_ACTIVATED, 1);
            if(!CollectionUtils.isEmpty(activatedFirmwareIds)) {
                maxFirmwareId = activatedFirmwareIds.get(0);
            }

            item.setLatestVersion(maxFirmwareId);
            item.setTenants(modelTenantMap.get(item.getModelNo()));
            item.setPubRate("0%");
            if(modelCountMap.containsKey(item.getModelNo())){
                ModelFirmwareUpgradeCount modelFirmwareUpgradeCount = modelCountMap.get(item.getModelNo());
                DecimalFormat df = new DecimalFormat("#.00");
                String result = df.format(modelFirmwareUpgradeCount.getCount()*100.0/modelFirmwareUpgradeCount.getTotal());
                //去掉 20.00 结尾的 .00
                result = result.replaceAll("\\.0*$","");
                item.setPubRate(result+"%");
            }
        });
    }

    public FirmwareBuildInfoTableDO getBxBuildInfo(Long id) {
        FirmwareBuildInfoTableDO info = firmwareBuildDAO.getFirmware(id);
        //暂时用默认platform查询
        List<String> modelNoList = factoryDataQueryService.queryPlatformModelNo(DEFAULT_BX_PLATFORM);
        log.info("getBxBuildInfo {}, {}", JSON.toJSONString(info), JSON.toJSON(modelNoList));
        List<ApplicationViewDo> apps = getBxApplications(modelNoList.get(0), info.getFirmwareId());
        info.setApplications(apps);
        return info;
    }

    public List<ApplicationViewDo> getBxSoftsInfo(String modelNo, String firmwareId) {
        return getBxApplications(modelNo, firmwareId);
    }

    public DeviceCdCertResponse getCdCertificationByPidVid(DeviceCdCertRequest request) {
        DeviceCdCertResponse response = new DeviceCdCertResponse();
        if (request.getPid()==null) {
            return response;
        }
        if (request.getVid()==null) {
            return response;
        }
        String pid = Integer.toHexString(request.getPid());
        String vid = Integer.toHexString(request.getVid());
        response.setCdCertification(cdCertificationInfoDAO.getCdCertificationByPidVid(pid, vid));
        return response;
    }

    public MinBxVersionViewDO queryBxSupportMinVersion(Integer userId, String serialNumber, String firmwareId, String modelNo) {
        if (modelNo == null) {
            modelNo = factoryDataQueryService.queryModelNoBySn(serialNumber);
        }

        if(modelNo == null){
            throw new org.addx.iot.common.exception.BaseException(ResultCollection.INVALID_PARAMS, "can not find modelNo");
        }

        OTADBValue info = firmwareDAO.selectFirmwareByIdAndModelNo(firmwareId, modelNo);
        MinBxVersionViewDO minBxVersionViewDO = new MinBxVersionViewDO();
        if(info == null){
            minBxVersionViewDO.setMinBxVersion("");
            return minBxVersionViewDO;
        }
        minBxVersionViewDO.setMinBxVersion(info.getMinBxVersion()==null?"":info.getMinBxVersion());
        return minBxVersionViewDO;
    }
}
