package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.apollo.ProductExplan;
import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.config.apollo.ProductExplainConfig;
import com.addx.iotcamera.dao.IProductDAO;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
public class ProductService {
    private final static Logger logger = LoggerFactory.getLogger(ProductService.class);

    @Autowired
    private IProductDAO iProductDAO;

    @Autowired
    private ProductExplainConfig productExplainConfig;

    @Cacheable(value = "productInfoV1", key = "#id", unless = "#result==null")
    public ProductDO queryProductById(Integer id) {
        return iProductDAO.selectById(id);
    }

    /**
     * 获取商品描述
     *
     * @param request
     * @return
     */
    public ProductExplan queryProductExplain(AppRequestBase request) {
        Map<String, Map<String, Map<String,ProductExplan>>> message = productExplainConfig.getMessage();
        if (!message.containsKey(request.getApp().getTenantId())) {
            logger.info("ProductExplan no {}", request.getApp().getTenantId());
            return null;
        }
        if (!message.get(request.getApp().getTenantId()).containsKey(request.getLanguage())) {
            logger.info("ProductExplan no {}", request.getLanguage());
            return null;
        }

        ProductExplan productExplan = message.get(request.getApp().getTenantId()).get(request.getLanguage()).get(queryAppType(request.getApp().getAppType()));
        return productExplan;
    }

    private String queryAppType(String appType){
        appType = appType.toLowerCase();
        return appType.equals("ios")? "ios" :"android";
    }

    /**
     * 查询指定类型商品List
     * @param productTypeList
     * @param tenantId
     * @return
     */

    public List<ProductDO> queryProductListByType(List<Integer> productTypeList,String tenantId, boolean notFreeTrial, Integer freeTrialDuration){
        return iProductDAO.queryProductByType(productTypeList,tenantId, notFreeTrial ? 1 : 0, freeTrialDuration);
    }

    /**
     * 获取套餐对应商品类型
     * @param tierId
     * @return
     */
    @Cacheable(value = "tierProductType", key = "#tierId", unless = "#result==null")
    public int queryTierProductType(Integer tierId){
        return Optional.ofNullable(iProductDAO.queryProductTypeByTier(tierId)).orElse(0);
    }

    /**
     * 查询当前app设置的免费领取的鸟类套餐
     * @param tenantId
     * @return
     */
    @Cacheable(value = "additionalProductFree", key = "#tenantId", unless = "#result==null")
    public ProductDO queryAdditionalProductFree(String tenantId){
        return iProductDAO.queryAdditionalProductFree(tenantId);
    }

    public List<ProductDO> queryAdditionalProductFreeList() {
        return iProductDAO.queryAdditionalProductFreeList();
    }
}
