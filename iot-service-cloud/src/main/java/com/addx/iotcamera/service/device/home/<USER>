package com.addx.iotcamera.service.device.home;

import com.addx.iotcamera.bean.app.device.home.DeviceHomeModeRequest;
import com.addx.iotcamera.bean.app.device.home.DeviceHomeModeSettingRequest;
import com.addx.iotcamera.bean.db.device.DeviceHomeDO;
import com.addx.iotcamera.bean.db.device.DeviceHomeModeDO;
import com.addx.iotcamera.bean.db.device.DeviceHomeModeSettingDO;
import com.addx.iotcamera.bean.device.attributes.*;
import com.addx.iotcamera.bean.device.model.DeviceModelIconDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.DeviceHomeModeSettingResponseDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.response.device.home.DeviceHomeModeResponse;
import com.addx.iotcamera.bean.response.device.home.DeviceHomeModeSettingResponse;
import com.addx.iotcamera.config.device.DeviceHomeModePresetConfig;
import com.addx.iotcamera.constants.DeviceAttributeName;
import com.addx.iotcamera.dao.device.home.DeviceHomeModeDAO;
import com.addx.iotcamera.enums.DeviceOnlineStatusEnums;
import com.addx.iotcamera.enums.DeviceStateMachineEnums;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.enums.device.DeviceHomeModeEnums;
import com.addx.iotcamera.enums.device.DeviceMessageDelayEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.*;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import com.google.api.client.util.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.addx.iotcamera.bean.msg.MsgType.PIR_ALARM_CANCEL_MODE;
import static com.addx.iotcamera.bean.msg.MsgType.PIR_ALARM_CANCEL_USER;
import static com.addx.iotcamera.constants.DeviceAttributeName.*;
import static com.addx.iotcamera.constants.DeviceInfoConstants.*;
import static com.addx.iotcamera.constants.PayConstants.MONTH_TIME_SECEND;
import static com.addx.iotcamera.constants.VideoConstants.VIDEO_HOME_ALARM;
import static com.addx.iotcamera.enums.device.DeviceHomeModeEnums.DISARMED;
import static org.addx.iot.common.constant.AppConstants.SUPPORT_HOME_MODE;
import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.addx.iot.domain.extension.ai.enums.AiObjectEnum.BIRD;

@Service
@Slf4j
public class DeviceHomeModeService {
    @Resource
    private DeviceHomeModeDAO deviceHomeModeDAO;

    @Resource
    @Lazy
    private DeviceHomeService deviceHomeService;

    @Resource
    private DeviceHomeModePresetConfig deviceHomeModePresetConfig;

    @Resource
    private DeviceHomeModeSettingService deviceHomeModeSettingService;

    @Resource
    @Lazy
    private DeviceService deviceService;

    @Resource
    private DeviceModelIconService deviceModelIconService;

    @Resource
    private DeviceManualService deviceManualService;

    @Resource
    private StateMachineService stateMachineService;

    @Resource
    @Lazy
    private DeviceInfoService deviceInfoService;

    @Resource
    @Lazy
    private DeviceAttributeService deviceAttributeService;

    @Resource
    @Lazy
    private LocationInfoService locationInfoService;

    @Resource
    private RedisService redisService;

    @Resource
    private DeviceMessageDelayService deviceMessageDelayService;

    @Resource
    @Lazy
    private DeviceSettingService deviceSettingService;

    @Resource
    @Lazy
    private UserRoleService userRoleService;

    @Resource
    private DeviceModelEventService deviceModelEventService;

    @Resource
    @Lazy
    private VipService vipService;

    @Resource
    private DeviceSupportService deviceSupportService;

    @Resource
    @Lazy
    private NotificationService notificationService;

    @Resource
    @Lazy
    private Device4GService device4GService;

    @Resource
    @Lazy
    private DeviceCallService deviceCallService;

    @Resource
    private UserVipService userVipService;

    @Resource
    @Lazy
    private DeviceStatusService deviceStatusService;

    /**
     * 查询用户名下Home-mode
     * @param userId
     * @param request
     * @return
     */
    public List<DeviceHomeModeResponse> queryDeviceHomeMode(Integer userId,DeviceHomeModeRequest request){
        boolean vipUser = userVipService.hasCloudVipDeviceUser(userId,request.getApp().getTenantId());

        // 非喂鸟器的设备
        List<String> userDeviceList = userRoleService.getUserRoleByUserId(userId, UserRoleEnums.ADMIN.getCode())
                .stream()
                .map(UserRoleDO::getSerialNumber)
                .filter(sn -> {
                    if(device4GService.queryDevice4GSimDO(sn) != null){
                        return false;
                    }
                    String modelNo = deviceManualService.getModelNoBySerialNumber(sn);
                    if(!StringUtils.hasLength(modelNo)){
                        return false;
                    }
                    return !deviceModelEventService.queryRowDeviceModelEvent(modelNo).contains(BIRD.getName());
                })
                .collect(Collectors.toList());

        // 只有同时满足VIP用户且有非喂鸟器设备时，才支持home mode
        if(!vipUser || CollectionUtils.isEmpty(userDeviceList)){
            log.debug("不支持home mode, vipUser: {}, hasNonBirdDevice: {}", vipUser, !CollectionUtils.isEmpty(userDeviceList));
            this.noSupportHomeMode(userId);
            return Lists.newArrayList();
        }

        Long homeId = request.getHomeId();
        List<DeviceHomeDO> list;

        boolean supporHomeMode = this.querySupportHomeMode(userId);
        if(!supporHomeMode){
            // 老版本app 升级 新版本app,初始化home mode
            this.markSupportHomeMode(userId);
            this.initDeviceHomeMode(userId,true); // 这里传入true因为已经确认是vipUser

            list = deviceHomeService.queryDeviceHomeListMaster(userId);
        }else{
            list = deviceHomeService.queryDeviceHomeList(userId);
        }

        list = list
                .stream()
                .filter(deviceHomeDO -> (homeId == null || deviceHomeDO.getId().equals(homeId)))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            log.debug("homeId error");
            return Lists.newArrayList();
        }

        Map<Long,List<DeviceHomeModeDO>> deviceHomeModeMap = deviceHomeModeDAO.queryDeviceHomeModeList(userId,homeId,null)
                .stream()
                .collect(Collectors.groupingBy(DeviceHomeModeDO::getHomeId));

        return list.stream()
                .map(deviceHomeDO ->
                             deviceHomeModeMap.containsKey(deviceHomeDO.getId()) ? DeviceHomeModeResponse.builder()
                                .homeId(deviceHomeDO.getId())
                                     .delayTime(this.homeModeDelayTime(deviceHomeDO))
                                .homeName(deviceHomeDO.getHomeName())
                                .selectedModeId(deviceHomeDO.getSelectedMode())
                                     .securityModes(DeviceHomeModeEnums.homeModeArray)
                                .build() : null
                        )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 查询mode leaving 生效时间
     * @param deviceHomeDO
     * @return
     */
    private Integer homeModeDelayTime(DeviceHomeDO deviceHomeDO){
        String key = DEVICE_HOME_MODE_DELAY_KEY.replace("{homeId}",String.valueOf(deviceHomeDO.getId()))
                .replace("{modeId}",String.valueOf(deviceHomeDO.getSelectedMode()));
        String oldCacheValue = redisService.get(key);
        log.debug("get mode deviceHomeDelayKey {} value {}",key,oldCacheValue);

        return StringUtils.hasLength(oldCacheValue) ? Integer.parseInt(oldCacheValue) : 0;
    }

    /**
     * 清除mode leaving 生效时间
     * @param homeId
     * @param modeId
     * @return
     */
    private void deleteModeDelayTime(Long homeId,Integer modeId){
        String key = DEVICE_HOME_MODE_DELAY_KEY.replace("{homeId}",String.valueOf(homeId))
                .replace("{modeId}",String.valueOf(modeId));
        redisService.delete(key);
    }

    public DeviceHomeModeSettingResponse getSecurityModeSettings(Integer userId, DeviceHomeModeRequest request){
        List<DeviceHomeModeDO> list = deviceHomeModeDAO.queryDeviceHomeModeList(userId,request.getHomeId(),request.getSelectedModeId());
        if(CollectionUtils.isEmpty(list)){
            log.warn("no home mode record homeId {} modeId {}",request.getHomeId(),request.getSelectedModeId());
            throw new BaseException(INVALID_PARAMS);
        }

        return initDeviceHomeModeSettingResponse(list.get(0),request.getIsPreset());
    }

    public List<DeviceHomeModeDO> queryDeviceHomeModeBatch(Integer userId,Long homeId){
        return deviceHomeModeDAO.queryDeviceHomeModeList(userId,homeId,null);
    }

    /**
     * init home mode 参数设置
     * @param deviceHomeModeDO
     * @param isPreset
     * @return
     */
    private DeviceHomeModeSettingResponse initDeviceHomeModeSettingResponse(DeviceHomeModeDO deviceHomeModeDO,boolean isPreset){
        // homeMode预设参数
        DeviceHomeModePresetConfig.DeviceHomeModePresetDO deviceHomeModePresetDO = deviceHomeModePresetConfig.queryDeviceHomeModePreset(deviceHomeModeDO.getMode());

        DeviceHomeModeEnums deviceHomeModeEnums = DeviceHomeModeEnums.queryByCode(deviceHomeModeDO.getMode());
        if(deviceHomeModeEnums == null){
            return DeviceHomeModeSettingResponse.builder()
                    .modifiableAttributes(Lists.newArrayList())
                    .cameraList(Lists.newArrayList())
                    .build();
        }

        List<DeviceHomeModeSettingDO> deviceHomeModeSettingDOS = deviceHomeModeSettingService.queryDeviceHomeModeSettingList(deviceHomeModeDO.getId(),null);
        List<DeviceModifiableAttribute> attrList = Lists.newArrayList();

        if(!deviceHomeModeDO.getMode().equals(DISARMED.getId())){
            // 设备全部支持alarmDelay 时才显示当前选项
            boolean deviceAllSupportAlarmDelay = deviceHomeModeSettingDOS
                    .stream()
                    .allMatch(deviceHomeModeSettingDO -> deviceSupportService.supportAlarmDelay(deviceSupportService.queryDeviceSupportBySn(deviceHomeModeSettingDO.getSerialNumber())));

            if(deviceAllSupportAlarmDelay){
                attrList.add(new DeviceEnumAttributes().setName(alarmDelay.name())
                        .setValue(String.valueOf(isPreset ? deviceHomeModePresetDO.getAlarmDelay() : deviceHomeModeDO.getAlarmDelay()))
                        .setOptions(deviceHomeModePresetConfig.getAlarmDelayOptions()));
            }
            attrList.add(new DeviceEnumAttributes().setName(leavingDelay.name())
                    .setValue(String.valueOf(isPreset ? deviceHomeModePresetDO.getLeavingDelay() : deviceHomeModeDO.getLeavingDelay()))
                    .setOptions(deviceHomeModePresetConfig.getLeavingDelayOptions()));
        }


        List<DeviceHomeModeSettingResponse.DeviceHomeModeDevice> cameraList = deviceHomeModeSettingDOS.stream().map(
                deviceHomeModeSettingDO -> {
                    DeviceHomeModeSettingResponse.DeviceHomeModeDevice deviceInfo = new DeviceHomeModeSettingResponse.DeviceHomeModeDevice();
                    DeviceDO deviceDO = deviceService.getAllDeviceInfo(deviceHomeModeSettingDO.getSerialNumber());
                    deviceInfo.setDeviceName(deviceDO.getDeviceName());
                    deviceInfo.setSerialNumber(deviceDO.getSerialNumber());

                    //标记设备类型，云存还是4G
                    Device4GSimDO device4GSimDO = device4GService.queryDevice4GSimDO(deviceDO.getSerialNumber());
                    deviceInfo.setDeviceStorageType(device4GSimDO == null ? TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode() : TierServiceTypeEnums.TIER_4G.getCode());
                    deviceInfo.setSimThirdParty(device4GSimDO == null ? 0 :device4GSimDO.getSimThirdParty());
                    deviceInfo.setDeviceInVip(vipService.isVipDevice(deviceHomeModeDO.getUserId(),deviceDO.getSerialNumber()));

                    String modelNo = deviceManualService.getModelNoBySerialNumber(deviceHomeModeSettingDO.getSerialNumber());
                    DeviceModelIconDO deviceModelIconDO = deviceModelIconService.queryDeviceModelIcon(modelNo);
                    deviceInfo.setIcon(deviceModelIconDO == null ? "" : deviceModelIconDO.getIconUrl());

                    DeviceStateDO deviceStateDO = stateMachineService.getDeviceState(deviceHomeModeSettingDO.getSerialNumber());
                    int online = deviceStateDO.inUnstable() ? DeviceOnlineStatusEnums.OFFLINE.getCode() : DeviceStateMachineEnums.verifyDeviceOnline(deviceStateDO.getStateId()) ?
                            DeviceOnlineStatusEnums.ONLINE.getCode() : DeviceOnlineStatusEnums.OFFLINE.getCode();
                    deviceInfo.setOnline(online);
                    final DeviceDormancyInfo dormancyInfo = deviceInfoService.getDeviceDormancyInfo(deviceDO.getSerialNumber(), deviceHomeModeDO.getUserId(), online);
                    deviceInfo.setDeviceStatus(dormancyInfo.getDeviceStatus());


                    List<DeviceModifiableAttribute> cameraAttributes = Lists.newArrayList();
                    cameraAttributes.add(new DeviceSwitchAttributes().setName(pirSwitch.name())
                            .setValue((isPreset ? deviceHomeModePresetDO.getPirSwitch() : deviceHomeModeSettingDO.getPir()) > 0));
                    cameraAttributes.add(new DeviceSwitchAttributes().setName(pushIgnoredSwitch.name())
                            .setValue((isPreset ? deviceHomeModePresetDO.getPushIgnored() : deviceHomeModeSettingDO.getPushIgnored()) > 0));


                    DeviceAttributeService.DeviceAttributeSource src = deviceAttributeService.getAttributeSource(deviceHomeModeSettingDO.getSerialNumber());
                    if (src.getSupport().getDeviceSupportAlarm()) {
                        cameraAttributes.add(new DeviceSwitchAttributes().setName(motionAlertSwitch.name())
                                .setValue((isPreset ? deviceHomeModePresetDO.getMotionAlertSwitch() : deviceHomeModeSettingDO.getNeedAlarm()) > 0));

                        cameraAttributes.add(new DeviceEnumAttributes().setName(alarmDuration.name())
                                .setValue(String.valueOf(isPreset ? deviceHomeModePresetDO.getAlarmDuration() : deviceHomeModeSettingDO.getAlarmDuration()))
                                .setOptions(src.getSupport().getAlarmDurationOptions()));
                    }
                    if (src.getSupport().getSupportAlarmFlashLight()) {
                        cameraAttributes.add(new DeviceSwitchAttributes().setName(alarmFlashLightSwitch.name())
                                .setValue(isPreset ? deviceHomeModePresetDO.getAlarmFlashLightSwitch() > 0 : deviceHomeModeSettingDO.getWhiteLightScintillation() > 0));
                    }

                    deviceInfo.setModifiableAttributes(cameraAttributes);
                    return deviceInfo;
                }
        ).filter(Objects::nonNull).collect(Collectors.toList());

        return DeviceHomeModeSettingResponse.builder()
                .modifiableAttributes(attrList)
                .cameraList(cameraList)
                .build();
    }



    public void modifySecurityModeSettings(Integer userId,DeviceHomeModeSettingRequest request){
        List<DeviceHomeModeDO> deviceHomeModeDOS = deviceHomeModeDAO.queryDeviceHomeModeList(userId,request.getHomeId(), request.getSecurityModeId());
        if(CollectionUtils.isEmpty(deviceHomeModeDOS)){
            throw new BaseException(INVALID_PARAMS);
        }

        DeviceHomeDO deviceHomeDO = deviceHomeService.queryDeviceHomeDO(request.getHomeId());

        for(DeviceHomeModeDO deviceHomeModeDO : deviceHomeModeDOS){
            DeviceHomeModeSettingResponseDO cacheValue = DeviceHomeModeSettingResponseDO.builder()
                    .alarmDelay(deviceHomeModeDO.getAlarmDelay())
                    .leavingDelay(deviceHomeModeDO.getLeavingDelay())
                    .build();
            //延时参数设置
            this.saveDeviceHomeModeSettingDelay(deviceHomeModeDO,request.getModifiableAttributes());

            //设备alarm 参数设置
            this.saveDeviceHomeModeSettingAlarm(userId,deviceHomeModeDO,request.getCameraList(),cacheValue,deviceHomeDO);
        }
    }

    /**
     * deviceHomeMode 延时参数
     * @param deviceHomeModeDO
     * @param modifiableAttributes
     */
    private void saveDeviceHomeModeSettingDelay(DeviceHomeModeDO deviceHomeModeDO,List<DeviceModifiableAttribute> modifiableAttributes){
        if(CollectionUtils.isEmpty(modifiableAttributes)){
            return;
        }

        for(DeviceModifiableAttribute deviceModifiableAttribute : modifiableAttributes){
            final DeviceAttributeName attrName = nameOf(deviceModifiableAttribute.getName());
            Object value = deviceModifiableAttribute.getValue();
            switch (attrName) {
                /* 1.运动检测-总开关*/
                case alarmDelay:
                    deviceHomeModeDO.setAlarmDelay(Integer.valueOf((String)value));
                    break;
                case leavingDelay:
                    deviceHomeModeDO.setLeavingDelay(Integer.valueOf((String)value));
                    break;
                default:
                    break;
            }
        }
        if(deviceHomeModeDO.getLeavingDelay().equals(0)){
            this.deleteModeDelayTime(deviceHomeModeDO.getHomeId(),deviceHomeModeDO.getMode());
        }
        deviceHomeModeDAO.updateDeviceHome(deviceHomeModeDO);
    }

    private void saveDeviceHomeModeSettingAlarm(Integer userId,DeviceHomeModeDO deviceHomeModeDO,
                                                List<DeviceHomeModeSettingResponse.DeviceHomeModeDevice> cameraList,
                                                DeviceHomeModeSettingResponseDO cacheValue,
                                                DeviceHomeDO deviceHomeDO){
        if(CollectionUtils.isEmpty(cameraList)){
            log.info("no device alarm setting");
            return;
        }
        List<DeviceHomeModeSettingDO> deviceHomeModeSettingDOList = deviceHomeModeSettingService.queryDeviceHomeModeSettingList(deviceHomeModeDO.getId(),null);

        Map<String,DeviceHomeModeSettingDO> settingDOMap = deviceHomeModeSettingDOList
                .stream()
                .collect(Collectors.toMap(DeviceHomeModeSettingDO::getSerialNumber,Function.identity()));
        for(DeviceHomeModeSettingResponse.DeviceHomeModeDevice deviceHomeModeDevice : cameraList){
            DeviceHomeModeSettingDO deviceHomeModeSettingDO = settingDOMap.getOrDefault(deviceHomeModeDevice.getSerialNumber(),null);
            if(deviceHomeModeSettingDO == null){
                continue;
            }

            String key = DEVICE_HOME_MODE_SETTING_KEY.replace("{homeId}",String.valueOf(deviceHomeModeDO.getHomeId()))
                    .replace("{modeId}",String.valueOf(deviceHomeModeDO.getMode()))
                    .replace("{sn}",deviceHomeModeSettingDO.getSerialNumber());

            // 缓存当前值, leavingDelay 到期前继续使用原有值
            String oldCacheValue = redisService.get(key);
            cacheValue.setPir(deviceHomeModeSettingDO.getPir());
            cacheValue.setNeedAlarm(deviceHomeModeSettingDO.getNeedAlarm());
            cacheValue.setAlarmDuration(deviceHomeModeSettingDO.getAlarmDuration());
            cacheValue.setPushIgnored(deviceHomeModeSettingDO.getPushIgnored());
            cacheValue.setWhiteLightScintillation(deviceHomeModeSettingDO.getWhiteLightScintillation());

            for(DeviceModifiableAttribute deviceModifiableAttribute : deviceHomeModeDevice.getModifiableAttributes()){
                final DeviceAttributeName attrName = nameOf(deviceModifiableAttribute.getName());
                Object value = deviceModifiableAttribute.getValue();
                switch (attrName) {
                    /* 1.运动检测-总开关*/
                    case pirSwitch:
                        deviceHomeModeSettingDO.setPir(((Boolean) value) ? 1 : 0);
                        break;
                    case motionAlertSwitch:
                        deviceHomeModeSettingDO.setNeedAlarm(((Boolean) value) ? 1 : 0);
                        break;
                    case alarmDuration:
                        deviceHomeModeSettingDO.setAlarmDuration(Integer.valueOf((String)value));
                        break;
                    case alarmFlashLightSwitch:
                        deviceHomeModeSettingDO.setWhiteLightScintillation(((Boolean) value) ? 1 : 0);
                        break;
                    case pushIgnoredSwitch:
                        deviceHomeModeSettingDO.setPushIgnored(((Boolean) value) ? 1 : 0);
                        break;
                    default:
                        break;
                }
            }

            deviceHomeModeSettingService.updateDeviceHomeModeSetting(deviceHomeModeSettingDO);
            deviceService.clearDeviceCache(deviceHomeModeSettingDO.getSerialNumber());

            // 向设备下发配置
            deviceSettingService.pushSettingMsg(deviceHomeModeSettingDO.getSerialNumber());

            if(deviceHomeModeSettingDO.getNeedAlarm().equals(0) && deviceHomeDO.getSelectedMode().equals(deviceHomeModeDO.getMode())){
                try {
                    String keyAlarm = VIDEO_HOME_ALARM.replace("{sn}",deviceHomeModeSettingDO.getSerialNumber());
                    if(!StringUtils.hasLength(redisService.get(keyAlarm))){
                        //无将要响铃或者正在醒来标记
                        continue;
                    }

                    //是否在线
                    DeviceStateDO deviceStateDO = stateMachineService.getDeviceState(deviceHomeModeSettingDO.getSerialNumber());
                    DeviceOnlineInfo onlineInfo = deviceStatusService.getDeviceOnlineInfo(deviceHomeModeSettingDO.getSerialNumber(), (int)Instant.now().getEpochSecond(), null, deviceStateDO);
                    if (onlineInfo.getOnline().equals(0)) {
                        //离线设备
                        continue;
                    }

                    boolean cancelAlarm = deviceCallService.deviceCancelAlarm(deviceHomeModeSettingDO.getSerialNumber());
                    if(cancelAlarm){
                        notificationService.deviceAlarmMessagePush(deviceHomeModeSettingDO.getSerialNumber(),"",PIR_ALARM_CANCEL_USER,0,deviceHomeModeSettingDO.getAlarmDuration(),(int)Instant.now().getEpochSecond(),deviceHomeService.queryDeviceHomeDO(deviceHomeModeDO.getHomeId()),userId);
                    }
                }catch (Exception e){
                    log.error("cancel alarm error",e);
                }
            }
        }

        // 更新缓存
        this.initHomeModeCacheValue(deviceHomeDO,deviceHomeModeDO,deviceHomeModeSettingDOList);
    }


    public void changeDeviceHomeMode(Integer userId,DeviceHomeDO deviceHomeDO,Integer oldModeId){
        List<DeviceHomeModeDO> deviceHomeModeDOS = deviceHomeModeDAO.queryDeviceHomeModeList(deviceHomeDO.getUserId(),deviceHomeDO.getId(), null);
        if(CollectionUtils.isEmpty(deviceHomeModeDOS)){
            log.debug("changeDeviceHomeMode deviceHomeModeDOS empty homeId {}",deviceHomeDO.getId());
            return;
        }
        DeviceHomeModeDO newHomeMode = deviceHomeModeDOS.stream().filter(deviceHomeModeDO -> deviceHomeModeDO.getMode().equals(deviceHomeDO.getSelectedMode())).findFirst().get();
        DeviceHomeModeDO oldHomeMode = deviceHomeModeDOS.stream().filter(deviceHomeModeDO -> deviceHomeModeDO.getMode().equals(oldModeId)).findFirst().get();

        Integer currentTime = (int)Instant.now().getEpochSecond();

        List<DeviceHomeModeSettingDO> newDeviceHomeModeSettingDOList = deviceHomeModeSettingService.queryDeviceHomeModeSettingList(newHomeMode.getId(),null);
        if(CollectionUtils.isEmpty(newDeviceHomeModeSettingDOList)){
            log.debug("changeDeviceHomeMode newDeviceHomeModeSettingDOList empty homeModeId {}",newHomeMode.getId());
            return;
        }


        for(DeviceHomeModeSettingDO modeSettingDO : newDeviceHomeModeSettingDOList){
            if(modeSettingDO.getNeedAlarm().equals(0)) {
                try {
                    String key = VIDEO_HOME_ALARM.replace("{sn}",modeSettingDO.getSerialNumber());
                    if(!StringUtils.hasLength(redisService.get(key))){
                        //无将要响铃或者正在醒来标记
                        continue;
                    }

                    //是否在线
                    DeviceStateDO deviceStateDO = stateMachineService.getDeviceState(modeSettingDO.getSerialNumber());
                    DeviceOnlineInfo onlineInfo = deviceStatusService.getDeviceOnlineInfo(modeSettingDO.getSerialNumber(), (int)Instant.now().getEpochSecond(), null, deviceStateDO);
                    if (onlineInfo.getOnline().equals(0)) {
                        //离线设备
                        continue;
                    }

                    boolean cancelAlarm = deviceCallService.deviceCancelAlarm(modeSettingDO.getSerialNumber());
                    if(cancelAlarm){
                        notificationService.deviceAlarmMessagePush(modeSettingDO.getSerialNumber(), "",PIR_ALARM_CANCEL_MODE,0,modeSettingDO.getAlarmDuration(),(int)Instant.now().getEpochSecond(),deviceHomeDO,userId);
                    }
                }catch (Exception e){
                    log.error("取消alarm error " ,e);
                }
            }
        }

        if(newHomeMode.getLeavingDelay().equals(0)){
            this.deleteDeviceHomeActiveMode(newHomeMode.getHomeId());
            for(DeviceHomeModeSettingDO modeSettingDO : newDeviceHomeModeSettingDOList){
                String key = DEVICE_HOME_MODE_SETTING_KEY.replace("{homeId}",String.valueOf(newHomeMode.getHomeId()))
                        .replace("{modeId}",String.valueOf(newHomeMode.getMode()))
                        .replace("{sn}",modeSettingDO.getSerialNumber());
                log.debug("changeDeviceHomeMode modeSettingDO key {}",key);

                redisService.delete(key);
                deviceMessageDelayService.deleteDeviceSettingDelay(modeSettingDO.getSerialNumber(),DeviceMessageDelayEnums.DEVICE_SETTING);

                //调用kiss 唤醒设备
                deviceInfoService.wakeUpDevice(modeSettingDO.getSerialNumber(), "");
                deviceSettingService.pushSettingMsg(modeSettingDO.getSerialNumber());
            }
        }else{
            Map<String,DeviceHomeModeSettingDO> oldDeviceHomeModeSettingMap = deviceHomeModeSettingService.queryDeviceHomeModeSettingList(oldHomeMode.getId(),null)
                    .stream().collect(Collectors.toMap(DeviceHomeModeSettingDO::getSerialNumber,Function.identity()));

            //计算生效时间
            this.initDeviceHomeActiveMode(newHomeMode,currentTime,oldModeId);

            Gson gson = new Gson();

            for(DeviceHomeModeSettingDO modeSettingDO : newDeviceHomeModeSettingDOList){
                // 新mode 是否等于 当前正在生效的Mode，如不是，需要记录缓存 正在生效mode的设备参数
                if(!queryDeviceHomeActiveMod(deviceHomeDO.getId()).equals(deviceHomeDO.getSelectedMode())){
                    // 新mode缓存参数key
                    String newModeKey = DEVICE_HOME_MODE_SETTING_KEY.replace("{homeId}",String.valueOf(newHomeMode.getHomeId()))
                            .replace("{modeId}",String.valueOf(newHomeMode.getMode()))
                            .replace("{sn}",modeSettingDO.getSerialNumber());

                    // 旧mode缓存参数key
                    String oldModeKey = DEVICE_HOME_MODE_SETTING_KEY.replace("{homeId}",String.valueOf(oldHomeMode.getHomeId()))
                            .replace("{modeId}",String.valueOf(oldHomeMode.getMode()))
                            .replace("{sn}",modeSettingDO.getSerialNumber());

                    String oldHomeCacheValue = redisService.get(oldModeKey);

                    DeviceHomeModeSettingDO oldModeSettingDO = oldDeviceHomeModeSettingMap.get(modeSettingDO.getSerialNumber());
                    if(oldModeSettingDO == null){
                        continue;
                    }

                    DeviceHomeModeSettingResponseDO cacheValue = DeviceHomeModeSettingResponseDO.builder()
                            .alarmDelay(oldHomeMode.getAlarmDelay())
                            .leavingDelay(oldHomeMode.getLeavingDelay())
                            .pir(oldModeSettingDO.getPir())
                            .pushIgnored(oldModeSettingDO.getPushIgnored())
                            .needAlarm(oldModeSettingDO.getNeedAlarm())
                            .alarmDuration(oldModeSettingDO.getAlarmDuration())
                            .whiteLightScintillation(oldModeSettingDO.getWhiteLightScintillation())
                            .build();

                    // 如果旧的Mode 缓存未过期，表示旧mode 还未生效就被切换到其他mode,正在生效的mode为旧mode之前的mode 的参数,新mode 需要 继承旧mode 的缓存
                    redisService.set(newModeKey, StringUtils.hasLength(oldHomeCacheValue) ? oldHomeCacheValue :  gson.toJson(cacheValue), newHomeMode.getLeavingDelay());
                    log.debug("changeDeviceHomeMode key {} oldHomeCacheValue {} cacheValue {}",newModeKey,oldHomeCacheValue,cacheValue);
                }

                // 延时下发setting
                deviceMessageDelayService.saveDeviceSettingDelay(modeSettingDO.getSerialNumber(),((int) Instant.now().getEpochSecond()) + newHomeMode.getLeavingDelay(), DeviceMessageDelayEnums.DEVICE_SETTING);
            }
        }

        for(DeviceHomeModeSettingDO modeSettingDO : newDeviceHomeModeSettingDOList){
            // push 开关缓存
            deviceService.clearDeviceCache(modeSettingDO.getSerialNumber());
        }
    }

    private void initDeviceHomeActiveMode(DeviceHomeModeDO deviceHomeModeDO,Integer currentTime,Integer oldHomeMode){
        String key = DEVICE_HOME_ACTIVE_MODE_KEY.replace("{homeId}",String.valueOf(deviceHomeModeDO.getHomeId()));
        String activeModeValue = redisService.get(key);
        if(StringUtils.hasLength(activeModeValue)){
            Integer activeModeId = Integer.parseInt(activeModeValue);
            if(activeModeId.equals(deviceHomeModeDO.getMode())){
                // 切回正在生效的Mode，需要更新缓存
                return;
            }
        }else{
            //记录正在生效的mode
            redisService.set(key,String.valueOf(oldHomeMode),deviceHomeModeDO.getLeavingDelay());
        }
        // 剩余leaving time 计算
        this.initModeLeavingRemainingTime(deviceHomeModeDO,currentTime);
    }

    public Integer queryDeviceHomeActiveMod(Long homeId){
        String key = DEVICE_HOME_ACTIVE_MODE_KEY.replace("{homeId}",String.valueOf(homeId));
        String activeModeValue = redisService.get(key);
        return StringUtils.hasLength(activeModeValue) ? Integer.parseInt(activeModeValue) : 0;
    }

    private void deleteDeviceHomeActiveMode(Long homeId){
        String key = DEVICE_HOME_ACTIVE_MODE_KEY.replace("{homeId}",String.valueOf(homeId));
        redisService.delete(key);
    }

    /**
     * 剩余leavingTime 计算
     * @param newHomeMode
     * @param currentTime
     */
    private void initModeLeavingRemainingTime(DeviceHomeModeDO newHomeMode,Integer currentTime){
        // 计算生效时间
        String deviceHomeDelayKey = DEVICE_HOME_MODE_DELAY_KEY.replace("{homeId}",String.valueOf(newHomeMode.getHomeId()))
                .replace("{modeId}",String.valueOf(newHomeMode.getMode()));
        log.debug("changeDeviceHomeMode deviceHomeDelayKey {}",deviceHomeDelayKey);

        redisService.set(deviceHomeDelayKey,String.valueOf(currentTime + newHomeMode.getLeavingDelay()),newHomeMode.getLeavingDelay());
    }

    @Transactional
    public void deviceSwitchHome(String serialNumber,Integer userId,Integer locationId){
        LocationDO locationDO = locationInfoService.selectSingleLocation(locationId);
        if(locationDO == null){
            log.error("deviceSwitchHome locationId error {}",locationId);
            throw new BaseException(INVALID_PARAMS);
        }

        Long originalHomeId = deviceHomeService.deviceSwitchHome(serialNumber,locationDO);
        if(originalHomeId.equals(locationDO.getHomeId())){
            log.info("deviceSwitchHome 相同homeId，后续设置无需变动");
            return;
        }

        // 1. 获取原始Home的mode列表
        List<DeviceHomeModeDO> originalDeviceHomeModeList = deviceHomeModeDAO.queryDeviceHomeModeList(userId,originalHomeId, null);
        Map<Long,DeviceHomeModeDO> originalDeviceHomeModeMap = originalDeviceHomeModeList
                .stream().collect(Collectors.toMap(DeviceHomeModeDO::getId,Function.identity()));

        // 2. 获取新Home的mode列表
        Map<Integer,DeviceHomeModeDO> newDeviceHomeModeMap = deviceHomeModeDAO.queryDeviceHomeModeList(userId,locationDO.getHomeId(), null)
                .stream().collect(Collectors.toMap(DeviceHomeModeDO::getMode,Function.identity()));

        // 3. 检查并创建缺失的mode
        for (DeviceHomeModeDO modeDO : originalDeviceHomeModeList) {
            Integer mode = modeDO.getMode();
            if (!newDeviceHomeModeMap.containsKey(mode)) {
                // 创建缺失的mode
                DeviceHomeModeDO newMode = DeviceHomeModeDO.builder()
                        .userId(userId)
                        .homeId(locationDO.getHomeId())
                        .mode(mode)
                        .alarmDelay(0)
                        .leavingDelay(0)
                        .build();
                deviceHomeModeDAO.insertDeviceHomeMode(newMode);
                newDeviceHomeModeMap.put(mode, newMode);
                log.debug("deviceSwitchHome: created missing mode {} for homeId {}", mode, locationDO.getHomeId());
            }
        }

        // 4. 获取原始Home的mode setting
        List<DeviceHomeModeSettingDO> deviceHomeModeSettingDOList = deviceHomeModeSettingService.queryDeviceHomeModeSettingByHomeModeId(originalDeviceHomeModeList.stream().map(DeviceHomeModeDO::getId).collect(Collectors.toList()),true)
                .stream()
                .filter(deviceHomeModeSettingDO -> deviceHomeModeSettingDO.getSerialNumber().equals(serialNumber))
                .collect(Collectors.toList());

        // 5. 更新mode setting的homeModeId
        for (DeviceHomeModeSettingDO modeSettingDO : deviceHomeModeSettingDOList) {

            // 找到对应的mode
            DeviceHomeModeDO originalMode = originalDeviceHomeModeMap.get(modeSettingDO.getHomeModeId());
            
            if (originalMode == null) {
                continue;
            }

            DeviceHomeModeDO newMode = newDeviceHomeModeMap.get(originalMode.getMode());
            if (newMode != null) {
                // 更新每个setting的homeModeId
                modeSettingDO.setHomeModeId(newMode.getId());
                modeSettingDO.setMode(newMode.getMode());
                deviceHomeModeSettingService.updateDeviceHomeModeSetting(modeSettingDO);
            }
        }
        deviceService.clearDeviceCache(serialNumber);
        // 7. 下发setting
        deviceSettingService.pushSettingMsg(serialNumber);
    }

    public DeviceHomeModeSettingResponseDO initDeviceSettingHomeModeParam(String serialNumber,Integer adminId){
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        if(deviceDO == null || Optional.ofNullable(deviceDO.getHomeId()).orElse(0L).equals(0L)){
            log.debug("initDeviceSettingHomeModeParam deviceDO null");
            return null;
        }

        if(adminId == null){
            adminId = userRoleService.getDeviceAdminUser(serialNumber);
        }
        if(adminId <= 0){
            log.debug("initDeviceSettingHomeModeParam 当前设备未绑定");
            return null;
        }


        boolean supportHomeMode = this.querySupportHomeMode(adminId);
        if(!supportHomeMode){
            log.debug("initDeviceSettingHomeModeParam 不支持homeMode {}",adminId);
            return null;
        }

        DeviceHomeDO deviceHomeDO = deviceHomeService.queryDeviceHomeDO(deviceDO.getHomeId());
        if(deviceHomeDO == null || deviceHomeDO.getSelectedMode().equals(0)){
            log.info("initDeviceSettingHomeModeParam device no home info");
            return null;
        }

        DeviceHomeModeDO deviceHomeModeDO = deviceHomeModeDAO.queryDeviceHomeModeByHomeIdAndMode(deviceDO.getHomeId(), deviceHomeDO.getSelectedMode());
        if(deviceHomeModeDO == null){
            log.info("initDeviceSettingHomeModeParam device no home info");
            return null;
        }

        String key = DEVICE_HOME_MODE_SETTING_KEY.replace("{homeId}",String.valueOf(deviceHomeModeDO.getHomeId()))
                .replace("{modeId}",String.valueOf(deviceHomeModeDO.getMode()))
                .replace("{sn}",serialNumber);
        String settingValue = redisService.get(key);

        log.info("initDeviceSettingHomeModeParam settingValue key {} settingValue {}",key,settingValue);

        Gson gson = new Gson();
        if(StringUtils.hasLength(settingValue)){
            return gson.fromJson(settingValue,DeviceHomeModeSettingResponseDO.class);
        }

        DeviceHomeModeSettingDO deviceHomeModeSettingDO = deviceHomeModeSettingService.queryDeviceHomeModeSetting(deviceHomeModeDO.getId(),serialNumber);
        if(deviceHomeModeSettingDO == null){
            log.info("initDeviceSettingHomeModeParam deviceHomeModeSettingDO null");
            return null;
        }

        return DeviceHomeModeSettingResponseDO.builder()
                .alarmDelay(deviceHomeModeDO.getAlarmDelay())
                .leavingDelay(deviceHomeModeDO.getLeavingDelay())
                .pir(deviceHomeModeSettingDO.getPir())
                .needAlarm(deviceHomeModeSettingDO.getNeedAlarm())
                .alarmDuration(deviceHomeModeSettingDO.getAlarmDuration())
                .whiteLightScintillation(deviceHomeModeSettingDO.getWhiteLightScintillation())
                .pushIgnored(deviceHomeModeSettingDO.getPushIgnored())
                .build();
    }


    /**
     * 初始化用户
     * @param userId
     */
    public void initDeviceHomeMode(Integer userId, boolean userVip){
        this.initDeviceHomeMode(userId,userVip,true);
    }
    public void initDeviceHomeMode(Integer userId, boolean userVip,boolean settingReadOnly){
        log.debug("initDeviceHomeMode start {}",userId);
        if(!this.querySupportHomeMode(userId)){
            return;
        }
        if(!userVip){
            log.debug("initDeviceHomeMode userVip {}",userVip);
            return;
        }

        List<DeviceHomeDO> deviceHomeDOList = deviceHomeService.queryDeviceHomeList(userId);
        if(CollectionUtils.isEmpty(deviceHomeDOList)){
            log.debug("initDeviceHomeMode deviceHomeDOList empty");
            return;
        }
        List<Long> homeIds = deviceHomeDOList.stream().map(DeviceHomeDO::getId).collect(Collectors.toList());

        // 批量获取homeMode(如不存在需要创建)
        List<DeviceHomeModeDO> deviceHomeModeDOList = deviceHomeModeDAO.queryDeviceHomeModeByHomeIdsBatch(homeIds);
        List<Long> deviceHomeModeIdList = deviceHomeModeDOList.stream().map(DeviceHomeModeDO::getId).collect(Collectors.toList());
        Map<Long,List<DeviceHomeModeDO>> deviceHomeModeMap = deviceHomeModeDOList
                .stream()
                .collect(Collectors.groupingBy(DeviceHomeModeDO::getHomeId));

        // 非喂鸟器的设备
        List<String> userDeviceList = userRoleService.getUserRoleByUserId(userId, UserRoleEnums.ADMIN.getCode())
                .stream()
                .map(UserRoleDO::getSerialNumber)
                .collect(Collectors.toList());

        log.debug("initDeviceHomeMode userDeviceList {}}",userDeviceList.size());
        if(CollectionUtils.isEmpty(userDeviceList)){
            log.debug("initDeviceHomeMode userVip {} userDeviceList {}",userVip,userDeviceList);
            return;
        }
        // 批量查询 homeModeSetting,如不存在需要创建
        Map<Long,List<DeviceHomeModeSettingDO>> modeSettingDOMap = deviceHomeModeSettingService.queryDeviceHomeModeSettingByHomeModeId(deviceHomeModeIdList,settingReadOnly)
                .stream().collect(Collectors.groupingBy(DeviceHomeModeSettingDO::getHomeModeId));


        for(DeviceHomeDO deviceHomeDO : deviceHomeDOList){
            Map<Integer,DeviceHomeModeDO> modeDOmap = deviceHomeModeMap.getOrDefault(deviceHomeDO.getId(),Lists.newArrayList())
                    .stream()
                    .collect(Collectors.toMap(DeviceHomeModeDO::getMode,Function.identity()));

            // vip 的情况下 ,home mode未创建时需要创建
            for(DeviceHomeModeEnums enums : DeviceHomeModeEnums.values()){
                DeviceHomeModeDO deviceHomeModeDO = modeDOmap.getOrDefault(enums.getId(),null);
                if(deviceHomeModeDO == null){
                    deviceHomeModeDO = DeviceHomeModeDO.builder()
                            .userId(userId)
                            .homeId(deviceHomeDO.getId())
                            .mode(enums.getId())
                            .alarmDelay(0)
                            .leavingDelay(0)
                            .build();
                    deviceHomeModeDAO.insertDeviceHomeMode(deviceHomeModeDO);
                }

                log.debug("initDeviceHomeMode userVip deviceHomeModeDO {}",deviceHomeModeDO);

                Set<String> deviceHomeModeSettingDeviceSet = modeSettingDOMap.getOrDefault(deviceHomeModeDO.getId(),Lists.newArrayList())
                        .stream().map(DeviceHomeModeSettingDO::getSerialNumber).collect(Collectors.toSet());
                //device setting

                DeviceHomeModeDO finalDeviceHomeModeDO = deviceHomeModeDO;
                userDeviceList.forEach(
                        serialNumber -> {
                            DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
                            if(deviceDO == null){
                                return;
                            }
                            if(!deviceDO.getHomeId().equals(deviceHomeDO.getId())){
                                log.debug("initDeviceHomeMode deivece homeId {} device homeId ",deviceHomeDO.getId(),deviceDO.getHomeId());
                                return;
                            }

                            if (deviceHomeModeSettingDeviceSet.contains(serialNumber)){
                                return;
                            }

                            DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(deviceDO.getSerialNumber());
                            DeviceHomeModeSettingDO deviceHomeModeSettingDO = DeviceHomeModeSettingDO.builder()
                                    .homeModeId(finalDeviceHomeModeDO.getId())
                                    .mode(finalDeviceHomeModeDO.getMode())
                                    .serialNumber(deviceDO.getSerialNumber())
                                    .pir(deviceAttributeService.getPir(deviceHomeDO.getUserId(),deviceSettingsDO,null))
                                    .pushIgnored(deviceDO.getPushIgnored() ? 1 : 0)
                                    .needAlarm(deviceSettingsDO == null ? 0 : deviceSettingsDO.getNeedAlarm())
                                    .alarmDuration(deviceSettingsDO == null ? 0 : deviceSettingsDO.getPirSirenDuration())
                                    .whiteLightScintillation(deviceSettingsDO == null ? 0 : deviceSettingsDO.getWhiteLightScintillation())
                                    .build();
                            deviceHomeModeSettingService.initDeviceHomeModeSetting(deviceHomeModeSettingDO);
                        }
                );
            }
            if(deviceHomeDO.getSelectedMode().equals(0)){
                //home 设置默认mode
                this.deviceMessageDeviceHomeMode(deviceHomeDO,deviceHomeModeMap,modeSettingDOMap, DISARMED.getId());
            }
        }
    }

    /**
     * 更新Home mode ，同时下发device setting
     *
     */
    public void deviceMessageDeviceHomeMode(DeviceHomeDO deviceHomeDO,Map<Long,List<DeviceHomeModeDO>> deviceHomeModeMap,Map<Long,List<DeviceHomeModeSettingDO>> modeSettingDOMap,Integer selectModeId) {
        List<DeviceHomeModeDO> deviceHomeModeDOList = deviceHomeModeMap.getOrDefault(deviceHomeDO.getId(), Lists.newArrayList())
                .stream()
                .filter(deviceHomeModeDO -> deviceHomeModeDO.getMode().equals(deviceHomeDO.getSelectedMode())).collect(Collectors.toList());

        deviceHomeDO.setSelectedMode(selectModeId);
        deviceHomeService.updateDeviceHone(deviceHomeDO);


        if (!CollectionUtils.isEmpty(deviceHomeModeDOList)) {
            DeviceHomeModeDO currentMode = deviceHomeModeDOList.get(0);
            List<DeviceHomeModeSettingDO> deviceHomeModeSettingDOList = modeSettingDOMap.getOrDefault(currentMode.getId(), Lists.newArrayList());
            for (DeviceHomeModeSettingDO modeSettingDO : deviceHomeModeSettingDOList) {
                deviceSettingService.pushSettingMsg(modeSettingDO.getSerialNumber());
            }
        }
    }


    public boolean devicePushIgnored(DeviceDO deviceDO){
        if(deviceDO == null){
            return true;
        }
        int adminId = userRoleService.getDeviceAdminUser(deviceDO.getSerialNumber());
        if(adminId <= 0){
            return deviceDO.getPushIgnored();
        }
        boolean supportHomeMode = this.querySupportHomeMode(adminId);
        if(!supportHomeMode){
            return deviceDO.getPushIgnored();
        }

        DeviceHomeDO deviceHomeDO = deviceHomeService.queryDeviceHomeDO(deviceDO.getHomeId());
        if(deviceHomeDO == null){
            return deviceDO.getPushIgnored();
        }

        DeviceHomeModeDO deviceHomeModeDO = deviceHomeModeDAO.queryDeviceHomeModeByHomeIdAndMode(deviceHomeDO.getId(), deviceHomeDO.getSelectedMode());
        if(deviceHomeModeDO == null){
            return deviceDO.getPushIgnored();
        }

        // 优先使用Redis缓存中的设置
        String key = DEVICE_HOME_MODE_SETTING_KEY.replace("{homeId}",String.valueOf(deviceHomeDO.getId()))
                .replace("{modeId}",String.valueOf(deviceHomeModeDO.getMode()))
                .replace("{sn}",deviceDO.getSerialNumber());
        String cacheValue = redisService.get(key);
        if(StringUtils.hasLength(cacheValue)){
            Gson gson = new Gson();
            DeviceHomeModeSettingResponseDO settingResponseDO = gson.fromJson(cacheValue, DeviceHomeModeSettingResponseDO.class);
            return settingResponseDO.getPushIgnored().equals(1);
        }

        DeviceHomeModeSettingDO deviceHomeModeSettingDO = deviceHomeModeSettingService.queryDeviceHomeModeSetting(deviceHomeModeDO.getId(),deviceDO.getSerialNumber());
        return deviceHomeModeSettingDO == null ? deviceDO.getPushIgnored() : deviceHomeModeSettingDO.getPushIgnored().equals(1);
    }


    public void markSupportHomeMode(Integer userId){
        String key = SUPPORT_HOME_MODE.replace("{userId}",String.valueOf(userId));
        redisService.set(key,"",MONTH_TIME_SECEND);
    }

    public boolean querySupportHomeMode(Integer userId){
        String key = SUPPORT_HOME_MODE.replace("{userId}",String.valueOf(userId));
        return redisService.hasDeviceOperationDo(key);
    }

    public boolean noSupportHomeMode(Integer userId){
        String key = SUPPORT_HOME_MODE.replace("{userId}",String.valueOf(userId));
        return redisService.delete(key);
    }

    /**
     * 将设备所属Home 下所有mode  alarmDelay设置为不支持
     * @param serialNumber
     * @param supportAlarmDelay
     */
    public void initDeviceHomeModeAlarmDelay(String serialNumber,boolean supportAlarmDelay){
        if(supportAlarmDelay){
            return;
        }
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        if(deviceDO == null){
            return;
        }
        Integer adminId = userRoleService.getDeviceAdminUser(serialNumber);
        if(adminId <= 0){
            return;
        }

        List<DeviceHomeModeDO> deviceHomeModeDOS = deviceHomeModeDAO.queryDeviceHomeModeList(adminId, deviceDO.getHomeId(), null)
                .stream()
                .filter(deviceHomeModeDO -> deviceHomeModeDO.getAlarmDelay() > 0)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(deviceHomeModeDOS)){
            return;
        }

        //设备是老固件，需要将同home 下所有alarmDelay 的设置初始化为0，同时app不展示alarmDelay
        for(DeviceHomeModeDO deviceHomeModeDO : deviceHomeModeDOS){
            deviceHomeModeDO.setAlarmDelay(0);
            deviceHomeModeDAO.updateDeviceHome(deviceHomeModeDO);
        }
    }


    public void deactivateOwnDevice(Integer userId,String serialNumber){
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        if(deviceDO == null){
            return;
        }

        List<DeviceHomeModeDO> deviceHomeModeList = deviceHomeModeDAO.queryDeviceHomeModeList(userId,deviceDO.getHomeId(),null);
        if(CollectionUtils.isEmpty(deviceHomeModeList)){
            return;
        }
        deviceHomeModeList.forEach(
                deviceHomeModeDO -> deviceHomeModeSettingService.deleteDeviceHomeModeSetting(deviceHomeModeDO.getId(),serialNumber)
        );
    }


    public void initHomeModeCacheValue(DeviceHomeDO deviceHomeDO,DeviceHomeModeDO deviceHomeModeDO,List<DeviceHomeModeSettingDO> deviceHomeModeSettingDOS){
        // 获取当前正在生效的Mode
        Integer activeMode = queryDeviceHomeActiveMod(deviceHomeModeDO.getHomeId());

        // 检查条件:
        // 与deviceHome的selectedMode不一致
        if(activeMode.equals(deviceHomeDO.getSelectedMode())) {
            log.debug("initHomeModeCacheValue activeMode {}",activeMode);
            return;
        }


        // 获取selectedMode对应的deviceHomeMode(将要生效的)
        DeviceHomeModeDO selectedModeDO = deviceHomeModeDAO.queryDeviceHomeModeByHomeIdAndMode(
                deviceHomeDO.getId(), deviceHomeDO.getSelectedMode());
        if(selectedModeDO == null || selectedModeDO.getLeavingDelay().equals(0)) {
            log.debug("initHomeModeCacheValue selectedModeDO {}",selectedModeDO);

            return;
        }


        Gson gson = new Gson();
        for(DeviceHomeModeSettingDO settingDO : deviceHomeModeSettingDOS) {
            // 构建缓存key
            String key = DEVICE_HOME_MODE_SETTING_KEY.replace("{homeId}",String.valueOf(deviceHomeDO.getId()))
                    .replace("{modeId}",String.valueOf(deviceHomeDO.getSelectedMode()))
                    .replace("{sn}",settingDO.getSerialNumber());
            boolean hasExist = redisService.hasDeviceOperationDo(key);
            if(!hasExist){
                // 无cache
                log.debug("initHomeModeCacheValue 无未生效的Mode");
                continue;
            }
            // 构建缓存value
            DeviceHomeModeSettingResponseDO cacheValue = DeviceHomeModeSettingResponseDO.builder()
                    .alarmDelay(selectedModeDO.getAlarmDelay())
                    .leavingDelay(selectedModeDO.getLeavingDelay())
                    .pir(settingDO.getPir())
                    .pushIgnored(settingDO.getPushIgnored())
                    .needAlarm(settingDO.getNeedAlarm())
                    .alarmDuration(settingDO.getAlarmDuration())
                    .whiteLightScintillation(settingDO.getWhiteLightScintillation())
                    .build();

            // 重新设置缓存,保持原有过期时间
            Long expireTime = Optional.ofNullable(redisService.ttl(key)).orElse(0L);
            redisService.set(key, gson.toJson(cacheValue), expireTime > 0 ? expireTime.intValue() : selectedModeDO.getLeavingDelay());
            log.debug("initHomeModeCacheValue key {} cacheValue {}", key, cacheValue);
        }
    }
}
