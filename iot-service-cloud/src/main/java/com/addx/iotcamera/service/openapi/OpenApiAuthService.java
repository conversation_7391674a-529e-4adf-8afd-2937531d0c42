package com.addx.iotcamera.service.openapi;

import com.addx.iotcamera.bean.openapi.OpenApiAccount;
import com.addx.iotcamera.bean.openapi.OpenApiResult;
import com.addx.iotcamera.util.HttpUtils;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.regex.Pattern;

@Slf4j
@Component
public class OpenApiAuthService {

    @Setter
    @Value("${open-api.auth-url.validate-request}")
    private String authUrlForValidateRequest;

    // 鉴权必需的参数
    public static final String[] AUTH_PARAMS = {"signature", "timestamp", "accessKey"};
    private static final int INDEX_SIGNATURE = 0, INDEX_TIMESTAMP = 1, INDEX_ACCESSKEY = 2;

    private static final Pattern PATTERN_TIMESTAMP = Pattern.compile("^[1-9][0-9]{9}$");
    private static final int TIMEOUT_TIMESTAMP = 60 * 10; // 秒
    // 返回码与httpCode的映射关系
    private static final ImmutableMap<Integer, Integer> resultCode2HttpCode = ImmutableMap.<Integer, Integer>builder()
            // 200	OK
            .put(0, 200)
            // 400	INVALID_ARGUMENT
            .put(101, 400).put(102, 400).put(103, 400).put(104, 400)
            .put(301, 400).put(302, 400)
            // 401	UNAUTHENTICATED
            .put(201, 401)
            .put(-1, 500)
            .build();

    /**
     * @param rowRequestUrl  除了查询 eg: https://host/path
     * @param rowQueryString 查询字符串，不包含'?'号 eg: a=1&b=2
     * @return
     */
    public Result<OpenApiAccount> validateSign(final String rowRequestUrl, final String rowQueryString) {
        log.info("validateSign rowRequestUrl: {} rowQueryString: {}", rowRequestUrl, rowQueryString);
        if (StringUtils.isBlank(rowQueryString)) {
            return new Result(102, "missing authentication parameters!", null);
        }
        // getRequestURL 返回的是完整的url，包括Http协议，端口号，servlet名字和映射路径，但它不包含请求参数。
        // eg: http://api-staging-us.vicohome.io/open-api/auth/token
        // 我们的https是通过Nginx实现的，后端接口本身只是http
        final String requestUrl = rowRequestUrl.replace("http://", "https://");
        final String queryString = rowQueryString.startsWith("?") ? rowQueryString.substring(1) : rowQueryString;
        String[] params = queryString.split("&");
        int i = params.length - 1, j = 0;
        String[] values = new String[AUTH_PARAMS.length];
        for (String param : AUTH_PARAMS) {
            if (i < 0 || !params[i].startsWith(param + "=")) {
                return new Result(102, "missing authentication parameter " + param + " !", null);
            }
            values[j++] = params[i--].substring(param.length() + 1);
        }
//        log.info("validateSign authParamValues: {}", Arrays.toString(values));
        if (!PATTERN_TIMESTAMP.matcher(values[INDEX_TIMESTAMP]).matches()) {
            return new Result(102, "timestamp parameter is invalid!", null);
        }
        Integer timestamp = Integer.valueOf(values[INDEX_TIMESTAMP]);
        if (Math.abs(PhosUtils.getUTCStamp() - timestamp) > TIMEOUT_TIMESTAMP) {
            return new Result(102, "timestamp is timeout!", null);
        }
        // 待签名url
        int signatureParamLength = params[params.length - 1].length() + 1;
        String unsignedUrl = requestUrl + "?" + queryString.substring(0, queryString.length() - signatureParamLength);
        JSONObject reqBody = new JSONObject().fluentPut("unsignedUrl", unsignedUrl);
        for (int k = 0; k < AUTH_PARAMS.length; k++) reqBody.put(AUTH_PARAMS[k], values[k]);
        return validateRequest(reqBody);
    }

    private Result<OpenApiAccount> validateRequest(JSONObject reqBody) {
        String respBody = HttpUtils.httpPostPojo(reqBody, authUrlForValidateRequest);
        if (StringUtils.isBlank(respBody)) return Result.Failure("access auth service failed!");
//        return JSON.parseObject(respBody, new TypeReference<Result<OpenApiAccount>>() {}.getType());
        JSONObject resp = JSON.parseObject(respBody);
        if (resp == null || resp.getIntValue("result") != 0) {
            return Result.Error(resp.getInteger("result"), resp.getString("message"));
        }
        return new Result<>(resp.getJSONObject("data").toJavaObject(OpenApiAccount.class));
    }

    public static void writeResponse(HttpServletResponse httpResponse, OpenApiResult result) {
        Integer httpCode = resultCode2HttpCode.getOrDefault(result.getCode(), 200);
        httpResponse.setStatus(httpCode);
        try {
            PrintWriter writer = httpResponse.getWriter();
            JSON.writeJSONString(writer, result);
            writer.flush();
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "返回openApi响应失败", e);
        }
    }

    // 创建签名url
    public static String createSignedUrl(String rowUrl, String accessKey, String accessSecret) {
        String unsignedUrl = OpenApiUtil.buildUnsignedUrl(rowUrl, accessKey, PhosUtils.getUTCStamp() + "");
        log.info("unsignedUrl: {}", unsignedUrl);
        try {
            String signature = OpenApiUtil.sign(unsignedUrl, accessSecret);
            String signedUrl = unsignedUrl + "&" + AUTH_PARAMS[INDEX_SIGNATURE] + "=" + signature;
            return signedUrl;
        } catch (Exception e) {
            throw new RuntimeException("open-api createSignedUrl", e);
        }
    }

}
