package com.addx.iotcamera.service.pay;

import com.addx.iotcamera.bean.app.payment.ApplePaymentRequest;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.domain.pay.OrderVerifyResultDO;
import com.addx.iotcamera.bean.domain.pay.PaymentConfig;
import com.addx.iotcamera.config.pay.PaymentCenterConfig;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.pay.IOrderProductDAO;
import com.addx.iotcamera.enums.OrderStatusTypeEnums;
import com.addx.iotcamera.enums.pay.OrderMessageTypeEnum;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.PaymentService;
import com.addx.iotcamera.service.ProductService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.HttpUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;

import static com.addx.iotcamera.constants.UserConstants.*;
import static org.addx.iot.common.enums.ResultCollection.*;

@Service
public class ApplePayService {
    private final static Logger logger = LoggerFactory.getLogger(WxPayService.class);

    @Autowired
    private IOrderDAO iOrderDAO;

    @Autowired
    private IOrderProductDAO iOrderProductDAO;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private ProductService productService;

    @Autowired
    private PaymentCenterConfig paymentCenterConfig;

    @Resource
    @Lazy
    private OrderService orderService;

    @Resource
    @Lazy
    private UserVipService userVipService;

    @Resource
    @Lazy
    private RedisService redisService;

    @Resource
    private MqSender mqSender;

    @Value("${spring.kafka.topics.user-ios-sub-order}")
    private String userIosOrderTopic;

    /**
     * 验证支付结果是否正确
     *
     * @param verifyResult
     * @return
     */
    public OrderVerifyResultDO appleOrderVerify(String verifyResult, String transactionId, PaymentConfig paymentConfig) {
        OrderVerifyResultDO resultDO = new OrderVerifyResultDO();
        if (verifyResult == null) {
            com.addx.iotcamera.util.LogUtil.error(logger, "apple pay 获取苹果结果信息为空");
            return resultDO;
        }

        logger.info("apple pay 获取苹果结果信息:{}", verifyResult);
        // 苹果验证有返回结果
        JSONObject job = JSONObject.parseObject(verifyResult);
        int states = job.getIntValue("status");
        // 前端所提供的收据是有效的    验证成功
        if (states != 0) {
            com.addx.iotcamera.util.LogUtil.error(logger, "apple pay 获取苹果结果信息result:{}", states);
            return resultDO;
        }

        Integer productId = null;
        Integer freeTrail = 0;
        JSONObject r = job.getJSONObject("receipt");
        String bundleId = r.getString("bundle_id");

        JSONArray inApp = r.getJSONArray("in_app");
        if (inApp != null && inApp.size() > 0) {
            for (int i = 0; i < inApp.size(); i++) {
                JSONObject o = inApp.getJSONObject(i);
                if (transactionId.equals(o.getString("transaction_id"))) {
                    productId = o.getIntValue("product_id");
                    freeTrail = o.getBoolean("is_trial_period") ? 1 : 0;
                    break;
                }
            }
        }
        if (productId != null) {
            productId = paymentConfig.getAppleBundleId().equals(bundleId) ? productId : null;
        }

        resultDO.setProductId(productId);
        resultDO.setFreeTrial(freeTrail);
        return resultDO;
    }

    /**
     * 验证支付结果是否正确
     *
     * @param verifyResult
     * @return
     */
    public OrderVerifyResultDO appleOrderVerifyV1(String verifyResult, ApplePaymentRequest receipt, PaymentConfig paymentConfig) {
        Integer productId = null;
        if (verifyResult == null) {
            com.addx.iotcamera.util.LogUtil.error(logger, "apple pay 获取苹果结果信息为空");
            return OrderVerifyResultDO.builder()
                    .productId(IOS_ORDER_EMPTY.getCode())
                    .build();
        }

        logger.info("apple pay 获取苹果结果信息:{}", verifyResult);
        // 苹果验证有返回结果
        JSONObject job = JSONObject.parseObject(verifyResult);
        int states = job.getIntValue("status");
        // 前端所提供的收据是有效的    验证成功
        if (states != 0) {
            com.addx.iotcamera.util.LogUtil.error(logger, "apple pay 获取苹果结果信息result:{}", states);
            return OrderVerifyResultDO.builder()
                    .productId(IOS_ORDER_STATUS_ERROR.getCode())
                    .build();
        }

        JSONObject r = job.getJSONObject("receipt");
        String bundleId = r.getString("bundle_id");
        if (!paymentConfig.getAppleBundleId().equals(bundleId)) {
            com.addx.iotcamera.util.LogUtil.error(logger, "apple pay 获取苹果结果信息,包名不同,bundleId:{},package:{}", bundleId, paymentConfig.getAppleBundleId());
            return OrderVerifyResultDO.builder()
                    .productId(IOS_ORDER_PACKAGE_ERROR.getCode())
                    .build();

        }

        JSONArray inApp = r.getJSONArray("in_app");
        if (inApp == null || inApp.size() <= 0) {
            com.addx.iotcamera.util.LogUtil.error(logger, "apple pay 获取苹果结果信息 IN_APP 为空");
            return OrderVerifyResultDO.builder()
                    .productId(IOS_ORDER_EMPTY.getCode())
                    .build();
        }

        Integer freeTrail = 0;
        String tradeNo = "";
        Integer purchaseTime = 0;
        String purchaseDatePst = null;
        String purchaseDate = null;
        Integer freeTrialPeriod = 0;
        Integer expireTime = null;
        //验证是否此次购买的账单
        for (int i = 0; i < inApp.size(); i++) {
            JSONObject o = inApp.getJSONObject(i);
            if (receipt.getTransactionId().equals(o.getString("transaction_id"))) {
                productId = o.getIntValue("product_id");
                freeTrail = o.getBoolean("is_trial_period") ? 1 : 0;
                if(freeTrail.equals(1)){
                    freeTrialPeriod = DateUtils.getTimePeriodDay(o.getLong("purchase_date_ms"),o.getLong("expires_date_ms"));
                }
                tradeNo = o.getString("original_transaction_id");
                purchaseTime = (int)(o.getLongValue("purchase_date_ms")/1000);
                purchaseDatePst = o.getString("purchase_date_pst").substring(0,19);
                purchaseDate = o.getString("purchase_date").substring(0,19);
                expireTime = (int)(o.getLongValue("expires_date_ms")/1000);
                break;
            }
        }

        if(productId == null){
            //此次传递的transactionId不正确，验证恢复购买账单
            logger.info("订单transactionId 不存在");
            return this.queryPurchasedProductId(inApp,receipt);
        }

        return OrderVerifyResultDO.builder()
                .verify(productId != null)
                .productId(productId == null ? 0 : productId)
                .freeTrial(freeTrail)
                .freeTrialPeriod(freeTrialPeriod)
                .originTradeNo(tradeNo)
                .purchaseTime(purchaseTime)
                .purchaseDate(purchaseDate)
                .purchaseDatePst(purchaseDatePst)
                .expireTime(expireTime)
                .build();
    }

    /**
     * 恢复购买逻辑，查询已购买的账单中，不存在于数据库中的
     * @param inApp
     * @param receipt
     * @return
     */
    public OrderVerifyResultDO queryPurchasedProductId(JSONArray inApp,ApplePaymentRequest receipt){

        Integer freeTrail = 0;
        String originalTradeNo = "";
        String tradeNo = "";
        Integer purchaseTime = 0;
        String purchaseDatePst = null;
        String purchaseDate = null;
        Integer productId = null;
        Integer expireTime = null;
        Integer freeTrialPeriod = 0;
        // 批量查询已验证的账单
        Map<String,Integer> tradeNo2index = Maps.newHashMap();
        for(int i = 0; i < inApp.size();i++){
            JSONObject obj = inApp.getJSONObject(i);
            tradeNo2index.put(obj.getString("transaction_id"),i);
        }
        long currentTime = System.currentTimeMillis();
        //支付流水表中已记录的账单号
        Set<String> tradeNoExistSet = paymentService.queryExistPaymentBatch(tradeNo2index.keySet());
        for(int i = 0; i < inApp.size();i++){
            JSONObject obj = inApp.getJSONObject(i);
            String transactionId = obj.getString("transaction_id");

            if(!tradeNoExistSet.contains(transactionId)){
                //账单号未记录到数据库中且未过期的订单
                if(obj.containsKey("expires_date_ms") && obj.getLongValue("expires_date_ms") > currentTime){
                    receipt.setTransactionId(transactionId);
                    tradeNo = transactionId;
                    productId = obj.getIntValue("product_id");
                    freeTrail = obj.getBoolean("is_trial_period") ? 1 : 0;
                    if(freeTrail.equals(1)){
                        freeTrialPeriod = DateUtils.getTimePeriodDay(obj.getLong("purchase_date_ms"),obj.getLong("expires_date_ms"));
                    }
                    originalTradeNo = obj.getString("original_transaction_id");
                    purchaseTime = (int)(obj.getLongValue("purchase_date_ms")/1000);
                    purchaseDatePst = obj.getString("purchase_date_pst").substring(0,19);
                    purchaseDate = obj.getString("purchase_date").substring(0,19);
                    expireTime = (int)(obj.getLongValue("expires_date_ms")/1000);
                    break;
                }
            }
        }

        return OrderVerifyResultDO.builder()
                .verify(productId != null)
                .productId(productId == null ? 0 : productId)
                .freeTrial(freeTrail)
                .freeTrialPeriod(freeTrialPeriod)
                .originTradeNo(originalTradeNo)
                .tradeNo(tradeNo)
                .purchaseTime(purchaseTime)
                .purchaseDatePst(purchaseDatePst)
                .purchaseDate(purchaseDate)
                .expireTime(expireTime)
                .build();
    }

    public List<OrderDO> querySublist(Long startOrderId) {
        return iOrderDAO.queryIosSubOrderList(startOrderId);
    }


    /**
     * 批量查询需要处理的order info
     */
    public void iosSubOrder(){
        String currentDayKey = USER_IOS_SUBORDER_START_KEY.replace("{date}",DateUtils.dateToString(new Date(),DateUtils.YYYYMMDD));
        String lastQueryId = redisService.get(currentDayKey);
        //上次处理id
        long startUserId = org.springframework.util.StringUtils.hasLength(lastQueryId) ? Long.parseLong(lastQueryId) : 0;
        List<OrderDO> orderDOList = this.querySublist(startUserId);
        logger.info("iosSubOrder startId {} sise {}",startUserId,orderDOList.size());
        if (CollectionUtils.isEmpty(orderDOList)) {
            return;
        }

        for (OrderDO orderDO : orderDOList) {
            try{
                Map<String,Object> dataMap = Maps.newHashMap();
                dataMap.put("orderId",orderDO.getId());
                dataMap.put(SHARE_MSG_TYPE_KEY, OrderMessageTypeEnum.SUB_ORDER_XXL.getMessage());
                mqSender.send(userIosOrderTopic,String.valueOf(orderDO.getId()),dataMap);
            }catch (Exception e){
                logger.error("executeUserVipActivateAndDeactivate 需要计算的用户"+ orderDO.getId(),e);
            }
        }
        startUserId = orderDOList.get(orderDOList.size() - 1).getId();
        redisService.set(currentDayKey,String.valueOf(startUserId),USER_VIP_ACTIVE_EXPIRE_TIME);
    }




    @Transactional
    public void appleOrderResult(OrderDO orderDO,String notificationType) throws Exception {
        try {
            JSONObject receipt = JSONObject.parseObject(orderDO.getExtend());
            Integer productId = receipt.getInteger("productId");
            if (productId == null) {
                com.addx.iotcamera.util.LogUtil.error(logger, "appleOrderResult 存储的数据不包含商品id,{}", orderDO.getOrderSn());
                return;
            }
            ProductDO productDO = productService.queryProductById(productId);
            if (productDO == null) {
                com.addx.iotcamera.util.LogUtil.error(logger, "appleOrderResult error,productDO null,{}", orderDO.getOrderSn());
                return;
            }
            PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(orderDO.getTenantId());

            String verifyResult = compatibleSandboxOrder(receipt.getString("receiptData"), paymentConfig);
            //logger.info("appleOrderResult orderSn:{},verifyResult:{}", orderDO.getOrderSn(), verifyResult);
            subOrder(verifyResult, productDO, orderDO,notificationType);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(logger, "sub order verify orderSn {}", orderDO.getOrderSn());
        }
    }

    /**
     * 订阅订单处理
     *
     * @param verifyResult
     * @param productDO
     * @param orderDO
     * @throws Exception
     */
    public boolean subOrder(String verifyResult, ProductDO productDO, OrderDO orderDO,String notificationType) throws Exception {
        if (StringUtils.isEmpty(verifyResult)) {
            com.addx.iotcamera.util.LogUtil.error(logger, "apple pay 获取苹果结果信息为空");
            return false;
        }
        JSONObject orderInfo = JSONObject.parseObject(verifyResult);
        int status = orderInfo.getIntValue("status");
        logger.debug("appleOrderResult orderSn:{},status:{}", orderDO.getOrderSn(), status);
        if (status != 0) {
            logger.info("appleOrderResult 订单状态不是付款状态:{}", orderInfo);
            return false;
        }
        // ios 订单历史账单记录
        JSONArray orderList = orderInfo.getJSONArray("latest_receipt_info");
        if (orderList.size() == 0) {
            logger.info("appleOrderResult 无订单记录");
            return false;
        }

        logger.debug("appleOrderResult orderSn:{},sises:{}", orderDO.getOrderSn(), orderList.size());
        int currentTime = (int)Instant.now().getEpochSecond();

        for (int i = orderList.size() - 1; i >= 0; i--) {
            JSONObject order = orderList.getJSONObject(i);
            int product_id = order.getInteger("product_id");
            int expires_date_ms = (int) (order.getLongValue("expires_date_ms") / 1000);

            //订阅订单可能升级，商品ID不一定相同
            if (productDO.getId().equals(product_id) && expires_date_ms > currentTime) {
                // 是否是退款订单
                boolean refundOrder = order.containsKey("cancellation_date_ms");
                String transaction_id = order.getString("transaction_id");
                logger.debug("appleOrderResult orderSn:{},transaction_id:{},productId:{},expires_date_ms:{}", orderDO.getOrderSn(), transaction_id, product_id, expires_date_ms);

                // 订单是否已升级
                boolean isUpgradedOrder = order.containsKey("is_upgraded") && "true".equals(order.get("is_upgraded"));
                PaymentFlow paymentFlow = paymentService.queryPaymentFlow(transaction_id);
                if (paymentFlow != null) {
                    if (isUpgradedOrder) {
                        userVipService.userVipRefundOrUpgrade(paymentFlow, paymentFlow.getUserId());
                    }
                    if(!refundOrder){
                        //正常购买的账单，如果paymentFlow 记录已存在，表示账单已经验证过，
                        logger.debug("appleOrderResult orderSn:{} paymentFlow 已经存在:{}", orderDO.getOrderSn(), paymentFlow.getTradeNo());
                        continue;
                    }
                }
                orderDO.setTimeEnd(expires_date_ms);
                orderDO.setStatus(refundOrder ? OrderStatusTypeEnums.CANCEL.getCode() : orderDO.getStatus());
                //更新订单过期时间
                iOrderDAO.updateOrderStatus(orderDO);
                if(refundOrder){
                    // 退款、取消账单
                    paymentFlow.setRefund(1);
                    paymentFlow.setRefundTime((int)(order.getLong("cancellation_date_ms")/1000));
                    paymentFlow.setRefundReason(order.getInteger("cancellation_reason"));
                    paymentService.updateRefundInfo(paymentFlow);

                    userVipService.userVipRefundOrUpgrade(paymentFlow,paymentFlow.getUserId());
                }else{
                    //正常支付账单
                    OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
                    logger.debug("appleOrderResult orderSn:{},initUserVip", orderDO.getOrderSn());

                    // 记录每一期vip跟账单的关联
                    orderDO.setTradeNo(transaction_id);
                    paymentService.initUserVip(orderDO, orderProductDo);
                    logger.debug("appleOrderResult orderSn:{},initPayment", orderDO.getOrderSn());

                    int time_start = (int) (order.getLongValue("purchase_date_ms") / 1000);
                    orderDO.setTimeStart(time_start);

                    OrderVerifyResultDO verifyResultDO =  OrderVerifyResultDO.builder()
                            .tradeNo(transaction_id)
                            .purchaseTime((int)(order.getLongValue("purchase_date_ms")/1000))
                            .purchaseDate(order.getString("purchase_date").substring(0,19))
                            .purchaseDatePst(order.getString("purchase_date_pst").substring(0,19))
                            .build();
                    paymentService.initPayment(orderDO, orderProductDo, order.toJSONString(),verifyResultDO,true);
                    logger.debug("appleOrderResult");

                    if(orderDO.getOrderCancel().equals(1)){
                        // 回复订阅
                        orderDO.setOrderCancel(0);
                        orderDO.setOrderCancelReason(0);
                        orderDO.setCancelEventKey("");
                        orderDO.setOrderCancelTime(0);
                        iOrderDAO.updateOrderCancelTime(orderDO);
                    }

                    return true;
                }
            }
        }

        this.orderRenewStatus(orderInfo,orderDO,notificationType);
        return true;
    }


    /**
     * 兼容审核时沙盒测试
     *
     * @param receiptData
     * @return
     */
    public String compatibleSandboxOrder(String receiptData, PaymentConfig paymentConfig) {
        JSONObject obj = new JSONObject();
        obj.put("receipt-data", receiptData);
        obj.put("password", paymentConfig.getApplePassword());

        String verifyResult = HttpUtils.httpsPost(obj.toJSONString(), paymentConfig.getAppleVerifyUrl());
        logger.debug("applePay 第一次IOS result:{}", verifyResult);
        JSONObject job = JSONObject.parseObject(verifyResult);
        Optional<Integer> status = Optional.ofNullable(job).map(it -> it.getInteger("status"));
        if (status.isPresent() && status.get() == 21007) {
            //是否沙盒环境
            String url = "https://sandbox.itunes.apple.com/verifyReceipt";
            verifyResult = HttpUtils.httpsPost(obj.toJSONString(), url);
        }
        return verifyResult;
    }

    /**
     * 校验是否沙盒订单
     *
     * @return
     */
    public boolean isSandboxOrder(String orderInfo) {
        JSONObject isoOrder = JSONObject.parseObject(orderInfo);
        return isoOrder.containsKey("environment") && "Sandbox".equals(isoOrder.getString("environment"));
    }


    public List<OrderDO> querySubOrderlist() {
        return iOrderDAO.querySubOrderList();
    }

    /**
     * 批量查询ios 订单信息
     * @param startOrderId
     * @return
     */
    public List<OrderDO> queryIosOrderBatch(Long startOrderId){
        return iOrderDAO.queryIosOrderBatch(startOrderId);
    }


    public boolean appleOrderResultHistory(OrderDO orderDO,String cancelEventKey) {
        try {
            if(StringUtils.isEmpty(orderDO.getExtend())){
                logger.info("appleOrderResultHistory extend empty");
                return false;
            }
            JSONObject receipt = JSONObject.parseObject(orderDO.getExtend());

            PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(orderDO.getTenantId());

            String verifyResult = compatibleSandboxOrder(receipt.getString("receiptData"), paymentConfig);
            logger.info("appleOrderResultHistory orderSn:{},verifyResult:{}", orderDO.getOrderSn(), verifyResult);
            iosOrderHistory(verifyResult, orderDO,cancelEventKey);
            return true;
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(logger, "appleOrderResultHistory error orderSn " + orderDO.getOrderSn(), e);
            return false;
        }
    }


    /**
     * 订阅订单处理
     *
     * @param verifyResult
     * @param orderDO
     * @throws Exception
     */
    public boolean iosOrderHistory(String verifyResult, OrderDO orderDO,String cancelEventKey)  {
        if (StringUtils.isEmpty(verifyResult)) {
            com.addx.iotcamera.util.LogUtil.error(logger, "iosOrderHistory apple pay 获取苹果结果信息为空");
            return false;
        }
        JSONObject orderInfo = JSONObject.parseObject(verifyResult);
        int status = orderInfo.getIntValue("status");
        if (status != 0) {
            logger.info("iosOrderHistory appleOrderResult 订单状态不是付款状态 {} {}",orderDO.getOrderSn(), orderInfo);
            return false;
        }

        // ios 订单历史账单记录
        JSONArray orderList = orderInfo.getJSONArray("latest_receipt_info");
        if (orderList.size() > 0) {
            for (int i = orderList.size() - 1; i >= 0; i--) {
                JSONObject order = orderList.getJSONObject(i);
                String transaction_id = order.getString("transaction_id");
                PaymentFlow paymentFlow = paymentService.queryPaymentFlow(transaction_id);
                if (paymentFlow == null) {
                    continue;
                }

                // 退款订单
                boolean refundOrder = order.containsKey("cancellation_date_ms");
                if (refundOrder) {
                    // 退款、取消账单
                    paymentFlow.setRefund(1);
                    paymentFlow.setRefundTime((int)(order.getLong("cancellation_date_ms") / 1000));
                    paymentFlow.setRefundReason(order.getInteger("cancellation_reason"));
                    logger.info("iosOrderHistory refund {}",paymentFlow.getTradeNo());
                    paymentService.updateRefundInfo(paymentFlow);
                }
            }
        }

        this.orderRenewStatus(orderInfo,orderDO, cancelEventKey);
        return true;
    }
    /**
     * 更新订单取消
     * @param originalTransactionId
     * @param cancelStatus
     */
    public void updateOrderCancelStatus(String originalTransactionId,Integer cancelStatus){
        OrderDO orderDO = orderService.querySubOrder(originalTransactionId);
        if(orderDO == null){
            return;
        }
        orderDO.setOrderCancel(cancelStatus);
        orderDO.setOrderCancelTime((int)Instant.now().getEpochSecond());
        orderService.updateOrderCancel(orderDO);
    }


    public void orderRenewStatus(JSONObject orderInfo,OrderDO orderDO,String cancelEventKey){
        if(orderDO.getOrderCancel().equals(1) && Optional.ofNullable(orderDO.getOrderCancelReason()).orElse(0) > 0){
            return;
        }
        if(orderDO.getSubType().equals(0)){
            //非订阅订单
            return;
        }
        OrderProductDo orderProductDo = orderService.queryOrderProductDO(orderDO.getId());
        if(orderProductDo == null){
            return;
        }

        if(orderInfo.containsKey("pending_renewal_info")){
            /**
             * 是否有当前套餐商品的订阅记录
             */
            boolean hasProduct = false;
            boolean hasOrder = false;
            JSONArray pendingRenewalInfoList = orderInfo.getJSONArray("pending_renewal_info");
            for(int i = 0 ; i< pendingRenewalInfoList.size() ; i++){
                JSONObject obj = pendingRenewalInfoList.getJSONObject(i);
                Integer productId = obj.getIntValue("product_id");
                if(!orderProductDo.getProductId().equals(productId)){
                    logger.debug("订单续订状态判断 orderSn {} productId 不匹配 {}",orderDO.getOrderSn(),orderProductDo.getProductId());
                    continue;
                }

                if(!hasProduct){
                    hasProduct = true;
                }

                String original_transaction_id = obj.getString("original_transaction_id");
                if(orderDO.getTradeNo().equals(original_transaction_id)){
                    if(!hasOrder){
                        hasOrder = true;
                    }
                    Integer auto_renew_status = obj.getIntValue("auto_renew_status");
                    if(auto_renew_status == 0 ){
                        //取消续订

                        if(orderDO.getOrderCancel().equals(0)){
                            orderDO.setOrderCancel(1);
                            orderDO.setOrderCancelReason(obj.containsKey("expiration_intent") ? obj.getInteger("expiration_intent") : 0);
                            //未标记取消订阅的订单，才需要设置取消日期, 防止影响历史取消的记录
                            orderDO.setCancelEventKey(cancelEventKey);
                            orderDO.setOrderCancelTime((int)(System.currentTimeMillis()/1000));
                            iOrderDAO.updateOrderCancelTime(orderDO);
                        }else if(orderDO.getOrderCancel().equals(1) && orderDO.getOrderCancelReason().equals(0) && obj.containsKey("expiration_intent")){
                            //更新超时原因
                            orderDO.setOrderCancelReason(obj.getInteger("expiration_intent"));
                            iOrderDAO.updateOrderCancelTime(orderDO);
                        }
                    }
                }
            }
            /**
             * 已经不在订阅list中的订阅，已经购买了新套餐
             */
            logger.debug("订单续订状态判断 orderSn {} hasProduct {} hasOrder {}",orderDO.getOrderSn(),hasProduct,hasOrder);
            if(orderDO.getOrderCancel().equals(0) &&  (!hasProduct || !hasOrder)){
                //未标记取消订阅的订单,且不再续订订单List
                Integer currentTime = (int) Instant.now().getEpochSecond();
                orderDO.setOrderCancelTime(currentTime);
                orderDO.setOrderCancel(1);
                orderDO.setOrderCancelReason(100);
                orderDO.setCancelEventKey(cancelEventKey);
                iOrderDAO.updateOrderCancelTime(orderDO);
            }
        }
    }

    /**
     * 获取支付时套餐指定设备list
     * @param extend
     * @return
     */
    public List<String> queryUserTierDeviceRequest(String extend){
        if(!org.springframework.util.StringUtils.hasLength(extend)){
            return Lists.newArrayList();
        }

        ApplePaymentRequest request = JSONObject.parseObject(extend,ApplePaymentRequest.class);
        return request.getTierDeviceList();
    }

    /**
     * 查询账单过期时间
     * @param receipt
     * @param tradeNo
     * @return
     */
    public Integer queryAppleOrderExpireTime(ApplePaymentRequest receipt,String tradeNo){
        PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(receipt.getApp().getTenantId());

        String verifyResult = this.compatibleSandboxOrder(receipt.getReceiptData(), paymentConfig);

        OrderVerifyResultDO verifyResultDO = this.appleOrderVerifyV1(verifyResult, receipt, paymentConfig);
        return verifyResultDO.getExpireTime();
    }
}
