package com.addx.iotcamera.service.security_mode.domain.settings.device;

import com.addx.iotcamera.dao.security_mode.DeviceDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

/**
 * description:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2024/5/24 20:37
 */
@Service
@Slf4j
public class DeviceInfoSubDomain {
    @Autowired
    private DeviceDao deviceDao;

    @CacheEvict(value = "deviceInfoV1", key = "#serialNumber")
    public Boolean setDeviceSecurityMode(String serialNumber, Integer modeId){
        return deviceDao.updateModeId(serialNumber, modeId)>0;
    }
}
