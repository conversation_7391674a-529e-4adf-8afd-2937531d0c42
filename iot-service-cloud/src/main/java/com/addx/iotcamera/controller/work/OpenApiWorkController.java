package com.addx.iotcamera.controller.work;

import com.addx.iotcamera.bean.app.vip.TierProduct;
import com.addx.iotcamera.bean.db.ProductExchangeCodeTb;
import com.addx.iotcamera.bean.device.DeviceCoturnDO;
import com.addx.iotcamera.bean.device.DeviceWebrtcParamDO;
import com.addx.iotcamera.bean.device.DeviceWhiteListDO;
import com.addx.iotcamera.bean.device.attributes.DeviceAttributes;
import com.addx.iotcamera.bean.device.attributes.DeviceAttributesJson;
import com.addx.iotcamera.bean.device.attributes.DeviceAttributesQuery;
import com.addx.iotcamera.bean.device.attributes.DeviceFixedAttributes;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.bean.exchangeCode.ExchangeCodeCreate;
import com.addx.iotcamera.bean.exchangeCode.ExchangeCodeRequest;
import com.addx.iotcamera.bean.openapi.OpenApiAccount;
import com.addx.iotcamera.bean.openapi.OpenApiResult;
import com.addx.iotcamera.config.NetTestConfig;
import com.addx.iotcamera.config.StorageAllocateConfig;
import com.addx.iotcamera.config.apollo.ProductConfig;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.dao.ProductExchangeCodeDAO;
import com.addx.iotcamera.helper.DeviceOperationHelper;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.kiss.DeviceWebrtcParamService;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceAttributeService;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.addx.iotcamera.service.device.DeviceWhiteListService;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.util.FuncUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.core5.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.service.openapi.OpenApiConfigService.PAAS_OWNED_TENANT_ID;
import static org.addx.iot.common.enums.ResultCollection.DEVICE_NO_RESPONSE;

@Slf4j
@RestController
@RequestMapping("/work")
public class OpenApiWorkController {

    @Autowired
    private ProductExchangeCodeService productExchangeCodeService;
    @Autowired
    private QuestionBackService questionBackService;
    @Autowired
    private JwtHelper jwtHelper;
    @Autowired
    private NetTestService netTestService;
    @Autowired
    private DeviceInfoService deviceInfoService;
    @Autowired
    private DeviceStatusService deviceStatusService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private NetTestConfig netTestConfig;
    @Autowired
    private DeviceWebrtcParamService deviceWebrtcParamService;
    @Autowired
    private DeviceWhiteListService deviceWhiteListService;
    @Autowired
    private OpenApiConfigService openApiConfigService;
    @Autowired
    private ProductConfig productConfig;
    @Autowired
    private DeviceAttributeService deviceAttributeService;
    @Autowired
    private StatisticsService statisticsService;
    @Autowired
    private DeviceOperationHelper deviceOperationHelper;
    @Autowired
    private StorageAllocateConfig storageAllocateConfig;
    @Autowired
    private ProductService productService;
    @Autowired
    @Lazy
    private KissWsService kissWsService;
    @Autowired
    private ProductExchangeCodeDAO exchangeCodeDAO;

    /**
     * 生成兑换码页面-查询改后端节点的所有商品配置
     * https://test-cn-a4x-console-web.addx.live/develop/exchange-code
     */
    @LogRequestAndResponse
    @GetMapping(value = "/queryProductConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult queryProductConfig(@RequestParam(value = "language", required = false, defaultValue = "zh") String language
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        final long t1 = System.currentTimeMillis();
        List<JSONObject> list = new LinkedList<>();
        FuncUtil.foreachRecursiveMapOrCollection(productConfig.getProduct(), (paths, obj) -> {
            if (paths.size() < 4 || !(obj instanceof TierProduct)) return;
            // paths层级依次：tenantId、包名、language、套餐ID
            JSONObject product = (JSONObject) JSON.toJSON(obj);
            product.put("tenantId", paths.get(0));
            product.put("packageName", paths.get(1));
            product.put("language", paths.get(2));
            product.put("tierId", paths.get(3));
            list.add(product);
        });
        final long t2 = System.currentTimeMillis();
        final Map<String, List<JSONObject>> tenantId2AdditionalProductList = productService.queryAdditionalProductFreeList()
                .stream().collect(Collectors.groupingBy(it -> it.getTenantId(), Collectors.mapping(it -> {
                    JSONObject item = new JSONObject().fluentPut("id", it.getId()).fluentPut("body", it.getBody());
                    return item;
                }, Collectors.toList())));
        for (JSONObject item : list) {
            item.put("additionalProductList", tenantId2AdditionalProductList.getOrDefault(item.getString("tenantId"), new LinkedList<>()));
        }
        final long t3 = System.currentTimeMillis();
        log.info("queryProductConfig end! getProductCostTime={},queryAdditionalProductFreeListCostTime={},productNum={}", t2 - t1, t3 - t2, list.size());
        JSONObject data = new JSONObject().fluentPut("list", list);
        return OpenApiResult.builder().code(0).data(data).build();
    }

    /**
     * 生成兑换码页面-创建兑换码
     * https://test-cn-a4x-console-web.addx.live/develop/exchange-code
     */
    @RequestMapping("/productExchangeCode/create")
    @LogRequestAndResponse
    public OpenApiResult createExchangeCode(HttpServletRequest request, HttpServletResponse response
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
            , @RequestBody ExchangeCodeCreate body) throws Exception {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        Result<List<String>> result = productExchangeCodeService.createExchangeCode(body.getProductId(), body.getAdditionalProductId(), body.getExpireSeconds(), body.getNum(), body.getRemark());
        return OpenApiResult.from(result);
    }

    @RequestMapping("/productExchangeCode/create/v2")
    @LogRequestAndResponse
    public OpenApiResult createExchangeCodeV2(HttpServletRequest request, HttpServletResponse response
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
            , @RequestBody ExchangeCodeCreate body) throws Exception {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        Result<List<String>> result = productExchangeCodeService.createExchangeCode(body.getProductId(), body.getAdditionalProductId(),
                body.getExpireSeconds(), body.getNum(), body.getRemark(), body.getDeviceCategory(), body.getModelNo());
        return OpenApiResult.from(result);
    }

    @RequestMapping("/productExchangeCode/display/list")
    @LogRequestAndResponse
    public OpenApiResult listExchangeCodeDisplay(HttpServletRequest request, HttpServletResponse response
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
            , @RequestBody ExchangeCodeRequest exchangeCodeRequest) throws Exception {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }

        return OpenApiResult.from(new Result<>(productExchangeCodeService.listExchangeCodeDisplay(exchangeCodeRequest)));
    }

    @RequestMapping("/productExchangeCode/exchangeCodes")
    @LogRequestAndResponse
    public OpenApiResult listExchangeCodes(HttpServletRequest request, HttpServletResponse response
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
            , @RequestBody ExchangeCodeRequest exchangeCodeRequest) throws Exception {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }

        return OpenApiResult.from(new Result<>(productExchangeCodeService.listExchangeCodesByDisplayId(exchangeCodeRequest)));
    }

    @RequestMapping("/productExchangeCode/updateModelNo")
    @LogRequestAndResponse
    public OpenApiResult updateModelNo(HttpServletRequest request, HttpServletResponse response
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
            , @RequestBody ExchangeCodeRequest exchangeCodeRequest) throws Exception {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        productExchangeCodeService.updateModelNo(exchangeCodeRequest.getDisplayId(), exchangeCodeRequest.getModelNo());
        return OpenApiResult.from(Result.Success());
    }

    @RequestMapping("/productExchangeCode/batchDisable")
    @LogRequestAndResponse
    public OpenApiResult batchDisable(HttpServletRequest request, HttpServletResponse response
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
            , @RequestBody ExchangeCodeRequest exchangeCodeRequest) throws Exception {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        productExchangeCodeService.batchDisable(exchangeCodeRequest.getCodeList());
        return OpenApiResult.from(Result.Success());
    }

    @RequestMapping("/productExchangeCode/download")
    @LogRequestAndResponse
    public List<ProductExchangeCodeTb> download(HttpServletRequest request, HttpServletResponse response
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
            , @RequestBody ExchangeCodeRequest exchangeCodeRequest) throws Exception {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return null;
        }
        return exchangeCodeDAO.queryAllCodeByDisplayId(exchangeCodeRequest.getDisplayId());
    }

    /**
     * 批量创建兑换码
     */
    @Deprecated
    @SneakyThrows
    @GetMapping("/productExchangeCode/batchCreate")
    public void batchCreateExchangeCode(HttpServletRequest request, HttpServletResponse response
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
            , @RequestParam("expireSeconds") int expireSeconds, @RequestParam("num") int num) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            response.setStatus(HttpStatus.SC_BAD_REQUEST);
            response.getWriter().println("tenantId参数错误:" + account.getTenantIds());
            return;
        }
        JSONArray array = productExchangeCodeService.batchCreateExchangeCode(expireSeconds, num);
        response.setContentType("text/json");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        try (PrintWriter writer = response.getWriter()) {
            writer.write(JSON.toJSONString(array, true));
        }
    }

    /**
     * 查询用户的问题反馈视频
     * https://test-cn-a4x-console-web.addx.live/develop/question-back
     */
    @LogRequestAndResponse
    @PostMapping(value = "/queryQuestionBackVideos", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult queryQuestionBackVideos(@RequestBody JSONObject input, HttpServletRequest request
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        List<JSONObject> list = questionBackService.queryQuestionBackVideos(input.getInteger("adminId"));
        final String rootPath = "https://" + URI.create(request.getRequestURL().toString()).getHost();
        for (JSONObject item : list) {
            JSONObject video = item.getJSONObject("video");
            if (video == null) continue;
            LinkedHashMap<String, Object> claims = new LinkedHashMap<>();
            final String keyPrefix = questionBackService.buildBackupVideoJsonKey(video.getString("serialNumber"), video.getString("traceId"), "");
            claims.put("keyPrefix", keyPrefix);
            String token = jwtHelper.createToken(claims, 3600 * 24); // 1天有效
            item.put("downloadUrl", rootPath + "/workTemp/downloadQuestionBackVideo?token=" + token);
            item.put("m3u8Url", rootPath + "/workTemp/getQuestionBackVideoM3u8?token=" + token);
        }
        return OpenApiResult.builder().code(0).data(list).build();
    }

    /**
     * 网络测试 https://a4x-paas.feishu.cn/wiki/wikcn4xYKxlwugIJCYtOxYywA9f
     */
    @LogRequestAndResponse
    @PostMapping(value = "/netTest", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult netTest(@RequestBody JSONObject input, HttpServletRequest request
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        if (input.getString("arguments") == null) {
            input.put("arguments", NetTestService.buildIperf3Arguments(input));
        }
        final String serialNumber = input.getString("serialNumber");
        CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(serialNumber);
        Boolean supportNetTest = Optional.ofNullable(cloudDeviceSupport).map(it -> it.getSupportNetTest()).orElse(netTestConfig.getDefaultSupportNetTest());
        if (!supportNetTest) {
            return OpenApiResult.builder().code(102).message("设备不支持网络测试!").build();
        }
        UserRoleDO adminRole = userRoleService.getDeviceAdminUserRole(serialNumber);
        if (adminRole == null) {
            return OpenApiResult.builder().code(102).message("设备未绑定!").build();
        }
        input.put("adminId", adminRole.getAdminId());
        final Boolean verifyLastAck = Optional.ofNullable(input.getBoolean("verifyLastAck")).orElse(false);
        final Integer lastAckTime = Optional.ofNullable(input.getInteger("lastAckTime")).orElse(10);
        final Boolean waitDeviceWake = Optional.ofNullable(input.getBoolean("waitDeviceWake")).orElse(true);
        if (verifyLastAck) {
            final DeviceStatusDO deviceStatusDO = deviceStatusService.queryDeviceStatusBySerialNumber(serialNumber);
            final Integer lastAck = Optional.ofNullable(deviceStatusDO).map(it -> it.getLastAct()).orElse(0);
            if (PhosUtils.getUTCStamp() - lastAck < lastAckTime) {
                log.info("netTest 设备lastAck在{}s之内！", lastAckTime);
                return OpenApiResult.from(netTestService.sendNetTestCommand(input));
            }
        }
        if (waitDeviceWake) {
            deviceInfoService.wakeUpDevice(serialNumber, "");
            Boolean deviceWakeup = deviceOperationHelper.waitDeviceWake(serialNumber);
            if (!deviceWakeup) {
                return OpenApiResult.from(Result.Error(DEVICE_NO_RESPONSE, "设备唤醒失败!"));
            }
            log.info("netTest 设备唤醒成功！");
        }
        return OpenApiResult.from(netTestService.sendNetTestCommand(input));
    }

    /**
     * 网络测试页面-根据id查询单个网络测试结果
     * https://test-cn-a4x-console-web.addx.live/develop/device-net-test
     */
    @LogRequestAndResponse
    @GetMapping(value = "/queryNetTestResult", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult queryNetTestResult(@RequestParam("id") String id
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        return OpenApiResult.from(netTestService.queryNetTestResult(id));
    }

    /**
     * 网络测试页面-根据sn查询历史网络测试结果
     * https://test-cn-a4x-console-web.addx.live/develop/device-net-test
     */
    @LogRequestAndResponse
    @GetMapping(value = "/queryNetTestResultByDeviceSn", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult queryNetTestResultByDeviceSn(@RequestParam("deviceSn") String deviceSn
            , @RequestParam(value = "beginTime", required = false) Long beginTime
            , @RequestParam(value = "endTime", required = false) Long endTime
            , @RequestParam(value = "limitNum", required = false) Integer limitNum
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        List<JSONObject> list = netTestService.queryNetTestCmdWithResultByDeviceSn(deviceSn, beginTime, endTime, limitNum);
        return OpenApiResult.builder().code(0).data(list).message("Success").build();
    }

    /**
     * 网络测试页面-查询网络测试目标服务器
     * https://test-cn-a4x-console-web.addx.live/develop/device-net-test
     * 查找该后端节点的所有coturn服务器
     * https://test-cn-a4x-console-web.addx.live/develop/coturn-config
     */
    @LogRequestAndResponse
    @GetMapping(value = "/queryNetTestTargetServers", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult queryNetTestTargetServers(@RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        return OpenApiResult.from(netTestService.queryNetTestTargetServers());
    }

    /**
     * 查找设备的coturn服务器配置
     * https://test-cn-a4x-console-web.addx.live/develop/coturn-config
     */
    @LogRequestAndResponse
    @GetMapping(value = "/queryCoturnConfigByDeviceSn", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult queryCoturnConfigByDeviceSn(@RequestParam("deviceSn") String deviceSn
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        UserRoleDO adminRole = userRoleService.getDeviceAdminUserRole(deviceSn);
        if (adminRole == null) {
            return OpenApiResult.builder().code(102).message("设备未绑定!").build();
        }
        List<DeviceCoturnDO> deviceCoturnList = deviceWebrtcParamService.queryDeviceCoturnBySn(deviceSn);
        JSONObject webrtcParam = Optional.ofNullable(deviceWebrtcParamService.queryDeviceWebrtcParamBySn(deviceSn))
                .map(DeviceWebrtcParamDO::getContent).filter(JSON::isValidObject).map(JSON::parseObject).orElse(null);
        JSONObject data = new JSONObject().fluentPut("deviceCoturnList", deviceCoturnList).fluentPut("webrtcParam", webrtcParam);
        return OpenApiResult.builder().code(0).data(data).build();
    }

    /**
     * 修改设备的coturn服务器配置
     * https://test-cn-a4x-console-web.addx.live/develop/coturn-config
     */
    @LogRequestAndResponse
    @PostMapping(value = "/updateCoturnConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult updateCoturnConfig(@RequestBody JSONObject input
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        String deviceSn = input.getString("deviceSn");
        if (StringUtils.isBlank(deviceSn)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("deviceSn参数错误:" + deviceSn).build();
        }
        JSONArray array = input.getJSONArray("list");
        if (CollectionUtils.isEmpty(array)) {
            JSONObject data = new JSONObject().fluentPut("updateNum", 0).fluentPut("insertNum", 0);
            return OpenApiResult.builder().code(0).data(data).build();
        }
        List<DeviceCoturnDO> list = array.toJavaList(DeviceCoturnDO.class);
        int updateNum = 0;
        List<DeviceCoturnDO> insertList = new LinkedList<>();
        for (DeviceCoturnDO item : list) {
            if (item.getId() != null) {
                item.setSerialNumber(null);
                updateNum += deviceWebrtcParamService.updateDeviceCoturnById(item);
            } else {
                item.setSerialNumber(deviceSn);
                insertList.add(item);
            }
        }
        int insertNum = insertList.size() > 0 ? deviceWebrtcParamService.insertDeviceCoturn(insertList) : 0;

        JSONObject webrtcParam = input.getJSONObject("webrtcParam");
        int webrtcParamSaveNum = 0;
        if (webrtcParam != null) {
            webrtcParamSaveNum = deviceWebrtcParamService.saveDeviceWebrtcParam(Arrays.asList(new DeviceWebrtcParamDO()
                    .setSerialNumber(deviceSn).setContent(JSON.toJSONString(webrtcParam))));
        }
        JSONObject data = new JSONObject().fluentPut("updateNum", updateNum).fluentPut("insertNum", insertNum)
                .fluentPut("webrtcParamSaveNum", webrtcParamSaveNum);
        return OpenApiResult.builder().code(0).data(data).build();
    }

    /**
     * 查找设备的白名单配置
     */
    @LogRequestAndResponse
    @PostMapping(value = "/queryDeviceWhiteList", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult queryDeviceWhiteList(@RequestBody JSONObject input
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        final DeviceWhiteListDO condition = input.toJavaObject(DeviceWhiteListDO.class);
        final long count = deviceWhiteListService.countByCondition(condition);
        final List<DeviceWhiteListDO> list = count > 0 ? deviceWhiteListService.queryByCondition(condition,
                input.getInteger("offset"), input.getInteger("size")) : Collections.emptyList();
        final JSONObject data = new JSONObject().fluentPut("condition", input).fluentPut("list", list).fluentPut("count", count);
        return OpenApiResult.builder().code(0).data(data).build();
    }

    @LogRequestAndResponse
    @PostMapping(value = "/queryDeviceWhiteListBySns", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult queryDeviceWhiteListBySns(@RequestBody JSONObject input
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        final List<String> sns = input.getJSONArray("serialNumbers").toJavaList(String.class);
        final List<DeviceWhiteListDO> list = deviceWhiteListService.queryBySns(sns);
        final JSONObject data = new JSONObject().fluentPut("condition", input).fluentPut("list", list).fluentPut("count", list.size());
        return OpenApiResult.builder().code(0).data(data).build();
    }

    /**
     * 更新设备的白名单配置
     */
    @LogRequestAndResponse
    @PostMapping(value = "/updateDeviceWhiteList", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult updateDeviceWhiteList(@RequestBody JSONObject input, @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        final List<DeviceWhiteListDO> list = input.getJSONArray("list").toJavaList(DeviceWhiteListDO.class);
        int updateNum = 0, insertNum = 0;
        for (final DeviceWhiteListDO item : list) {
            if (item.getSerialNumber() == null) continue;
            final DeviceWhiteListDO oldItem = deviceWhiteListService.queryBySn(item.getSerialNumber());
            if (oldItem != null) {
                if (oldItem.getPirVideoStorageService().equals(item.getPirVideoStorageService())) continue;
                updateNum += deviceWhiteListService.update(item);
            } else {
                insertNum += deviceWhiteListService.insert((item));
            }
            openApiConfigService.publishDeviceConfig(item.getSerialNumber(), null); // 重发config
        }
        JSONObject data = new JSONObject().fluentPut("updateNum", updateNum).fluentPut("insertNum", insertNum);
        return OpenApiResult.builder().code(0).data(data).build();
    }

    @LogRequestAndResponse
    @PostMapping(value = "/getDeviceAttributes", produces = MediaType.APPLICATION_JSON_VALUE)
    public OpenApiResult getDeviceAttributes(@RequestBody DeviceAttributesQuery input
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }

        Result<DeviceAttributes> result = deviceAttributeService.getDeviceAttributes(input);
        if (result.getData() != null && result.getData().getFixedAttributes() != null && !org.springframework.util.CollectionUtils.isEmpty(result.getData().getFixedAttributes().getSupportJson()))  {
            DeviceAttributes data = result.getData();
            DeviceAttributesJson deviceAttributesJson = JSONObject.parseObject(JSONObject.toJSONString(data), DeviceAttributesJson.class);
            deviceAttributesJson.setModifiableAttributes(result.getData().getModifiableAttributes());
            deviceAttributesJson.setRealTimeAttributes(result.getData().getRealTimeAttributes());
            JSONObject supportJson = result.getData().getFixedAttributes().getSupportJson();
            if (deviceAttributesJson != null && deviceAttributesJson.getFixedAttributes() != null && !org.springframework.util.CollectionUtils.isEmpty(supportJson)) {
                deviceAttributesJson.getFixedAttributes().putAll(supportJson);
                JSONObject fixedAttributes = deviceAttributesJson.getFixedAttributes();
                for (Map.Entry<String, Object> entry : fixedAttributes.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    DeviceAttributes data1 = result.getData();
                    DeviceFixedAttributes fixedAttributes1 = data1.getFixedAttributes();
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(fixedAttributes1), JSONObject.class);
                    Object o = jsonObject.get(key);
                    if (!value.equals(o)) {
                        log.info("key = {} equals = {} jsonValue = {} oldValue = {}", key, value.equals(o), JSONObject.toJSONString(value), JSONObject.toJSONString(o));
                    }
                }
                return OpenApiResult.from(new Result(result.getResult(), result.getMsg(), deviceAttributesJson));
            }
        }
        return OpenApiResult.from(result);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/sliceUploadSuccessRate", produces = MediaType.APPLICATION_JSON_VALUE)
    public OpenApiResult sliceUploadSuccessRate(@RequestBody JSONObject input
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        final List<String> indexNames = Optional.ofNullable(input.getJSONArray("indexNames"))
                .map(it -> it.toJavaList(String.class)).orElseGet(Collections::emptyList);
        if (indexNames.isEmpty()) {
            return OpenApiResult.builder().code(102).message("indexNames不能为空!").build();
        }
        final List<String> sns = Optional.ofNullable(input.getJSONArray("sns"))
                .map(it -> it.toJavaList(String.class)).orElse(null);
        final JSONObject data = statisticsService.sliceUploadSuccessRate(indexNames, sns);
        return OpenApiResult.builder().code(0).data(data).build();
    }

    @LogRequestAndResponse
    @PostMapping(value = "/refreshStorageAllocateConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    public OpenApiResult refreshStorageAllocateConfig(@RequestBody JSONObject input
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        try {
            storageAllocateConfig.init();
            return OpenApiResult.builder().code(0).build();
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "refreshStorageAllocateConfig error!", e);
            return OpenApiResult.builder().code(-1).message(e.getMessage()).build();
        }
    }

    @LogRequestAndResponse
    @PostMapping(value = "/initDeviceSupport", produces = MediaType.APPLICATION_JSON_VALUE)
    public OpenApiResult initDeviceSupport(@RequestBody DeviceAttributesQuery input
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        return OpenApiResult.from(deviceInfoService.initDeviceSupport());
    }

    @LogRequestAndResponse
    @PostMapping(value = "/broadcast", produces = MediaType.APPLICATION_JSON_VALUE)
    OpenApiResult broadcast(@RequestBody JSONObject input, HttpServletRequest request
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        return OpenApiResult.from(kissWsService.broadcast(input));
    }

}
