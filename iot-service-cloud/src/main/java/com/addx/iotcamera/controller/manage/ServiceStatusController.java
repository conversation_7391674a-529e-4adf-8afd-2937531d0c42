package com.addx.iotcamera.controller.manage;

import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/management")
public class ServiceStatusController {
    @LogRequestAndResponse
    @GetMapping(value = "/health")
    public Result getHealth(HttpServletRequest httpServletRequest) {
        log.info("Custom health check succeeded.");
        return Result.Success();
    }
}
