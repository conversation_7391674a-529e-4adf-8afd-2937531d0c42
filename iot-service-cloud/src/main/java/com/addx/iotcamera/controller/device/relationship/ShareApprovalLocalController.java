package com.addx.iotcamera.controller.device.relationship;


import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.HandleApprovalRequest;
import com.addx.iotcamera.bean.app.ShareQueryRequest;
import com.addx.iotcamera.bean.app.user.CkApprovalRequest;
import com.addx.iotcamera.bean.domain.ShareApprovalDO;
import com.addx.iotcamera.bean.domain.ShareQueryResponseDO;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.enums.device.DeviceShareTypeEnum;
import com.addx.iotcamera.service.DeviceAuthService;
import com.addx.iotcamera.service.DeviceRelationshipService;
import com.addx.iotcamera.service.DeviceShareService;
import io.micrometer.core.annotation.Timed;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;


@RestController
@RequestMapping("/device/share")
public class ShareApprovalLocalController {
    private static Logger LOGGER = LoggerFactory.getLogger(ShareApprovalLocalController.class);


    @Autowired
    private DeviceRelationshipService deviceRelationshipService;

    @Autowired
    private DeviceShareService deviceShareService;

    @Resource
    private DeviceAuthService deviceAuthService;



    /**
     * 基站-发起分享请求，发送分享邀请邮件
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/invite/email", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result shareInviteEmail(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                   @RequestBody CkApprovalRequest request,
                                   HttpServletRequest httpServletRequest) {
        //验证参数
        deviceShareService.veryfyParam(request);

        if(request.getShareDeviceType().equals(DeviceShareTypeEnum.CLOUD.getCode())){
            Integer activatedCheck = deviceAuthService.checkActivatedAccess(userId, request.getSerialNumber());
            if (ResultCollection.SUCCESS.getCode() != activatedCheck) {
                return ResultCollection.getResult(activatedCheck);
            }
        }

        return deviceShareService.shareInviteEmail(userId,request);
    }


    /**
     * 验证分享码
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/verify/code", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result verifyDeviceShareCode(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                   @RequestBody CkApprovalRequest request,
                                   HttpServletRequest httpServletRequest) {
        return deviceShareService.verifyShareCode(userId,request.getShareCode());
    }

    /**
     * 处理分享请求--基站
     *
     * @param userId
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/handleapproval", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result HandleApproval(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                 @RequestBody HandleApprovalRequest request,
                                 HttpServletRequest httpServletRequest) {

        return deviceShareService.deviceShareHandLocal(userId, request);
    }
    


    /**
     * 未处理的分享请求--基站
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/recentapprovals/local", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result recentApprovalsLocal(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                       @RequestBody AppRequestBase request,
                                  HttpServletRequest httpServletRequest) {
        ShareApprovalDO shareApprovalDO = ShareApprovalDO.builder()
                .targetId(userId)
                .tenantId(request.getApp().getTenantId())
                .build();

        List<ShareQueryResponseDO> list = deviceRelationshipService.queryRecentApprovalsLocal(shareApprovalDO);
        return new Result(list);
    }


    /**
     * 取消分享-基站
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/cancel", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result deviceShareCancelLocal(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                       @RequestBody CkApprovalRequest request,
                                       HttpServletRequest httpServletRequest) {
        deviceShareService.stationDeviceShareCancel(userId,request.getShareIdList());
        return Result.Success();
    }

    /**
     * 获取已分享的用户list
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/user/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result deviceShareUserList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                         @Valid @RequestBody ShareQueryRequest request,
                                         HttpServletRequest httpServletRequest) {
        request.setAdminId(userId);
        return new Result<>(deviceRelationshipService.getDeviceShareUserList(request));
    }

    /**
     * 分享用户详情
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/user/info", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result deviceShareUserInfo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                         @Valid @RequestBody ShareQueryRequest request,
                                         HttpServletRequest httpServletRequest) {
        request.setAdminId(userId);
        return new Result<>(deviceRelationshipService.queryShareUserInfo(request));
    }
}

