package org.apache.shardingsphere.underlying.rewrite.engine;

import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.sql.parser.sql.constant.QuoteCharacter;
import org.apache.shardingsphere.underlying.common.rule.DataNode;
import org.apache.shardingsphere.underlying.rewrite.context.SQLRewriteContext;
import org.apache.shardingsphere.underlying.rewrite.parameter.builder.ParameterBuilder;
import org.apache.shardingsphere.underlying.rewrite.parameter.builder.impl.GroupedParameterBuilder;
import org.apache.shardingsphere.underlying.rewrite.parameter.builder.impl.StandardParameterBuilder;
import org.apache.shardingsphere.underlying.rewrite.sql.impl.RouteSQLBuilder;
import org.apache.shardingsphere.underlying.route.context.RouteResult;
import org.apache.shardingsphere.underlying.route.context.RouteUnit;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public final class SQLRouteRewriteEngine {

    private Map<String, Pattern> actualTableNamePatternMap;

    public SQLRouteRewriteEngine() {
        this.actualTableNamePatternMap = new HashMap<>();
    }

    public Map<RouteUnit, SQLRewriteResult> rewrite(SQLRewriteContext sqlRewriteContext, RouteResult routeResult) {
        Map<RouteUnit, SQLRewriteResult> result = new LinkedHashMap(routeResult.getRouteUnits().size(), 1.0F);
        Iterator var4 = routeResult.getRouteUnits().iterator();

        while (var4.hasNext()) {
            RouteUnit each = (RouteUnit) var4.next();

            AtomicReference<String> sqlRef = new AtomicReference<>((new RouteSQLBuilder(sqlRewriteContext, each)).toSQL());
            if (!sqlRef.get().contains(each.getDataSourceMapper().getActualName())) {
                each.getLogicTableNames().forEach(logicTableName -> {
                    if (CollectionUtils.isEmpty(each.getActualTableNames(logicTableName))) {
                        return;
                    }

                    each.getActualTableNames(logicTableName).forEach(actualTableName -> {
                        if (!actualTableNamePatternMap.containsKey(actualTableName)) {
                            actualTableNamePatternMap.put(actualTableName, Pattern.compile(QuoteCharacter.BACK_QUOTE.getStartDelimiter() + "*" + actualTableName + QuoteCharacter.BACK_QUOTE.getStartDelimiter() + "*"));
                        }

                        String quoteActualTableName = String.join("", QuoteCharacter.BACK_QUOTE.getStartDelimiter(), actualTableName, QuoteCharacter.BACK_QUOTE.getEndDelimiter());
                        String fullTableName = String.join("", QuoteCharacter.BACK_QUOTE.getStartDelimiter(), each.getDataSourceMapper().getActualName(), QuoteCharacter.BACK_QUOTE.getEndDelimiter(), ".", quoteActualTableName);
                        sqlRef.getAndUpdate(sql -> {
                            Matcher matcher = actualTableNamePatternMap.get(actualTableName).matcher(sql);
                            return matcher.replaceAll(fullTableName);
                        });
                    });
                });
            }

            log.debug("route rewrite sql {}", sqlRef.get());
            result.put(each, new SQLRewriteResult(sqlRef.get(), this.getParameters(sqlRewriteContext.getParameterBuilder(), routeResult, each)));
        }

        return result;
    }

    private List<Object> getParameters(ParameterBuilder parameterBuilder, RouteResult routeResult, RouteUnit routeUnit) {
        if (!(parameterBuilder instanceof StandardParameterBuilder) && !routeResult.getOriginalDataNodes().isEmpty() && !parameterBuilder.getParameters().isEmpty()) {
            List<Object> result = new LinkedList();
            int count = 0;

            for (Iterator var6 = routeResult.getOriginalDataNodes().iterator(); var6.hasNext(); ++count) {
                Collection<DataNode> each = (Collection) var6.next();
                if (this.isInSameDataNode(each, routeUnit)) {
                    result.addAll(((GroupedParameterBuilder) parameterBuilder).getParameters(count));
                }
            }

            return result;
        } else {
            return parameterBuilder.getParameters();
        }
    }

    private boolean isInSameDataNode(Collection<DataNode> dataNodes, RouteUnit routeUnit) {
        if (dataNodes.isEmpty()) {
            return true;
        } else {
            Iterator var3 = dataNodes.iterator();

            DataNode each;
            do {
                if (!var3.hasNext()) {
                    return false;
                }

                each = (DataNode) var3.next();
            } while (!routeUnit.findTableMapper(each.getDataSourceName(), each.getTableName()).isPresent());

            return true;
        }
    }
}

