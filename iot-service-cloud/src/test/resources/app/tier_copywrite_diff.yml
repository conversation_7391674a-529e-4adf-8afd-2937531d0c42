tier:
  copywrite:
    diff:
      config:
        vicoo:
          lite:
            - title: "vip_feature_cloud_storage"
              type: "text"
              value: "vip_feature_cloud_storage_day_count"
              vipValue: "vip_feature_cloud_storage_day_count"
            - title: "vip_feature_record_length"
              type: "text"
              value: "vip_feature_record_length_20s"
              vipValue: "vip_feature_record_length_3min"
              exclusive: ["vicoo","guard"]
            - title: "vip_feature_motion_detection"
              type: "bool"
              value: "true"
              vipValue: "true"
              exclusive: ["shenmou"]
            - title: "vip_feature_motion_alerts"
              type: "bool"
              value: "true"
              vipValue: "true"
            - title: "vip_feature_smart_push"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_activity_zone"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_person_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_pet_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_vehicle_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_package_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_cooldown_time"
              type: "bool"
              value: "false"
              vipValue: "true"
          noPlan:
            - title: "vip_feature_cloud_storage"
              type: "text"
              value: "no_plan_none_video_history"
              vipValue: "vip_feature_cloud_storage_day_count"
            - title: "vip_feature_record_length"
              type: "text"
              value: "thumbnails_only"
              vipValue: "vip_feature_record_length_3min"
              exclusive: ["vicoo","guard"]
            - title: "vip_feature_motion_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
              exclusive: ["shenmou"]
            - title: "vip_feature_motion_alerts"
              type: "bool"
              value: "true"
              vipValue: "true"
            - title: "vip_feature_smart_push"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_activity_zone"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_person_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_pet_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_vehicle_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_package_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "vip_feature_cooldown_time"
              type: "bool"
              value: "false"
              vipValue: "true"
        kiwibit:
          noPlan:
            - title: "b_rolling_history"
              type: "text"
              value: "b_motion_recording_desc1"
              vipValue: "b_vip_feature_cloud_storage_day_count"
            - title: "b_event_notification"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "b_smart_notification"
              type: "bool"
              value: "true"
              vipValue: "true"
            - title: "b_detection_area"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "b_person_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "b_pet_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "b_vehicle_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "b_package_detection"
              type: "bool"
              value: "false"
              vipValue: "true"
            - title: "b_zero_recording_gap"
              type: "bool"
              value: "false"
              vipValue: "true"