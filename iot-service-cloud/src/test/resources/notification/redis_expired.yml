redisexpired:
  map:
    productInfo: 6000
    firmware: 1800
    roleDefinition: 6000
    userVipInfo: 60
    tierProductSet: 600
    deviceModelNo: 86400
    deviceOriginalModelNo: 86400
    messageNotificationSettings: 600
    messageNotificationSettingsV1: 600
    messageNotificationSetting: 600
    deviceSupportLanguage: 600
    pushInfo: 600
    queryUserTenantId: 600
    countryList: 600
    firmwareNeedForceUpgrade: 1800
    devicePirNotifyFactorV2: 60
    deviceFirmware: 600
    deviceMcuVersion: 600
    deviceInfo: 600
    deviceAdminRole: 600
    deviceAdmin: 600
    deviceRole: 600
    activityZone: 600
    orderSub: 600
    deviceAdminCountryNo: 600
    userInfo: 600
    deviceSetting: 600
    userIsVip: 600
    userDeviceRole: 600
    tenantAwsConfig: 600
    userSetting: 600
    LibraryIdByTraceId: 600
    deviceModelConfig: 600
    deviceModelEventSet: 600
    rowDeviceModelEventSet: 600
    deviceModelIcon: 600
    deviceModelTenant: 600
    deviceModelVoice: 600
    deviceModelZendesk: 600
    deviceAiSettings: 600
    tierInfoV1: 600
    lastDeviceVip: 600
    deviceModelInstallBoot: 600
    videoSearchOption: 600