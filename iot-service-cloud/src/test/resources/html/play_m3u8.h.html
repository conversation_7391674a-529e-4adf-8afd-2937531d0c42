<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="https://imgcache.qq.com/open/qcloud/video/vcplayer/TcPlayer-2.2.0.js" charset="utf-8"></script>
</head>
<body>
<div id="id_video" style="width:100%; height:auto;"></div>
<script>
    var player = new TcPlayer('id_video', {
        "m3u8": "https://api-test.addx.live/video/download/m3u8/460057.m3u8?token=b421cc81607444e1a334af76138f651c", //请替换成实际可用的播放地址
        "autoplay": true,   //iOS 下 safari 浏览器，以及大部分移动端浏览器是不开放视频自动播放这个能力的
        "width": '480',//视频的显示宽度，请尽量使用视频分辨率宽度
        "height": '320'//视频的显示高度，请尽量使用视频分辨率高度
    });
</script>
</body>
</html>