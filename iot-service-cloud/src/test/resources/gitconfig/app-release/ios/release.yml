ios:
  minSupper:
    dzees:
      build: 1000
      version: 0.1
    vicoo:
      build: 1002
      version: '0.2'
  prod:
    dzees:
      build: 14062
    guard:
      build: 14017
    jxja:
      build: 14007
    soliom:
      build: 14005
    vicoo:
      build: 13076
  release:
    dzees:
      build: 14004
      minBuild: 1000
      minVersion: 0.1
      packageSize: 85
      version: 1.1.0
    vicoo:
      build: 13018
      minBuild: 1002
      minVersion: '0.2'
      packageSize: 85
      version: 2.1.1
  release_list:
    dzees:
      1.1.0: 14004
    vicoo:
      '2.0': 13009
      2.1.1: 13018
  release_notes:
    dzees:
      1.1.0:
        cn:
          - 中文描述
        de:
          - de description
        en:
          - English
          - node s
          - test
        ja:
          - 日本語記述
      '2.2':
        cn:
          - Обновите процесс привязки камеры, чтобы оптимизировать взаимодействие с
            пользователем
        de:
          - Обновите процесс привязки камеры, чтобы оптимизировать взаимодействие с
            пользователем
        en:
          - Обновите процесс привязки камеры, чтобы оптимизировать взаимодействие с
            пользователем
        fr:
          - Обновите процесс привязки камеры, чтобы оптимизировать взаимодействие с
            пользователем
        ja:
          - Обновите процесс привязки камеры, чтобы оптимизировать взаимодействие с
            пользователем
        ru:
          - Обновите процесс привязки камеры, чтобы оптимизировать взаимодействие с
            пользователем
    guard:
      '1.01':
        cn:
          - 修复bug，优化使用体验
        de:
          - Beheben Sie Fehler und optimieren Sie die Benutzererfahrung.
        en:
          - Fix bugs and optimize users' experience.
        fr:
          - Corrigez les bugs et optimisez l'expérience utilisateur
        ja:
          - バグを修正し、ユーザーエクスペリエンスを最適化する
        ru:
          - Исправляйте ошибки и оптимизируйте взаимодействие с пользователем
      '1.1':
        cn:
          - 添加推送免打扰功能，用户可根据需要暂时关闭消息通知
          - 优化运动追踪功能，提高使用体验
          - 优化绑定流程，有助摄像机绑定
          - 修复bug
        de:
          - Wenn Sie die Funktion Push nicht stören hinzufügen, können Benutzer Nachrichtenbenachrichtigungen
            bei Bedarf vorübergehend deaktivieren
          - Optimieren Sie die Bewegungsverfolgungsfunktion, um die Benutzererfahrung
            zu verbessern
          - Optimieren Sie den Bindungsprozess, um die Kamerabindung zu unterstützen
          - Fehler behoben
        en:
          - Add push do not disturb function, users can temporarily turn off message
            notifications as needed
          - Optimize motion tracking function to improve user experience
          - Optimize the binding process to help camera binding
          - Fix bug
        fr:
          - Ajouter la fonction push ne pas déranger, les utilisateurs peuvent désactiver
            temporairement les notifications de message au besoin
          - Optimiser la fonction de suivi de mouvement pour améliorer l'expérience
            utilisateur
          - Optimiser le processus de liaison pour faciliter la liaison de la caméra
          - Réparer le bug
        ja:
          - 追加プッシュは機能を妨げません、ユーザーは必要に応じてメッセージ通知を一時的にオフにすることができます
          - モーショントラッキング機能を最適化して、ユーザーエクスペリエンスを向上させます
          - バインディングプロセスを最適化して、カメラのバインディングを支援します
          - バグを修正
        ru:
          - Добавьте функцию не беспокоить, пользователи могут временно отключить уведомления
            о сообщениях по мере необходимости
          - Оптимизируйте функцию отслеживания движения, чтобы улучшить взаимодействие
            с пользователем
          - Оптимизируйте процесс привязки, чтобы облегчить привязку камеры
          - Исправить ошибку
      1.2.0:
        cn:
          - 功能升级，提高用户体验
        de:
          - Funktionserweiterung zur Verbesserung der Benutzererfahrung
        en:
          - Function upgrades to improve user experience
        ja:
          - ユーザーエクスペリエンスを向上させるための機能のアップグレード
    jxja:
      '1.2':
        cn:
          - 优化直播声音的使用体验
        de:
          - Optimieren Sie die Erfahrung mit Live-Sound
        en:
          - Optimize the experience of using live sound
        ja:
          - ライブサウンドの使用体験を最適化する
    soliom:
      1.2.0:
        cn:
          - 功能升级，提高用户体验
        de:
          - Funktionserweiterung zur Verbesserung der Benutzererfahrung
        en:
          - Function upgrades to improve user experience
        ja:
          - ユーザーエクスペリエンスを向上させるための機能のアップグレード
    vicoo:
      '2.1':
        cn:
          - 1.支持新设备接入
          - 2.修复已知bug，优化使用体验
        de:
          - 1. Unterstützen Sie neue Geräte
          - 2. Beheben Sie bekannte Fehler und optimieren Sie die Benutzererfahrung
        en:
          - 1. Support new devices
          - 2. Fix known bugs and optimize user experience
        ja:
          - 1.新しいデバイスをサポートする
          - 2.既知のバグを修正し、ユーザーエクスペリエンスを最適化します
      2.1.1:
        cn:
          - 1.修复bug，优化使用体验
          - 2.支持法语和俄语
        de:
          - 1.Beheben Sie Fehler und optimieren Sie die Benutzererfahrung.
          - 2.Unterstützt Französisch und Russisch
        en:
          - 1.Fix bugs and optimize users' experience.
          - 2.Support French and Russian
        ja:
          - 1.バグを修正し、ユーザーエクスペリエンスを最適化する
          - 2.フランス語とロシア語をサポート
      2.1.3:
        cn:
          - 修复bug，优化使用体验
        de:
          - Beheben Sie Fehler und optimieren Sie die Benutzererfahrung.
        en:
          - Fix bugs and optimize users' experience.
        ja:
          - バグを修正し、ユーザーエクスペリエンスを最適化する
      '2.2':
        cn:
          - 更新摄像机绑定流程，优化使用体验
        de:
          - Aktualisieren Sie den Kamerabindungsprozess, um die Benutzererfahrung zu
            optimieren
        en:
          - Update camera binding process to optimize user experience
        fr:
          - Mettre à jour le processus de liaison de la caméra pour optimiser l'expérience
            utilisateur
        ja:
          - カメラバインディングプロセスを更新して、ユーザーエクスペリエンスを最適化します
        ru:
          - Обновите процесс привязки камеры, чтобы оптимизировать взаимодействие с
            пользователем
      2.2.1:
        cn:
          - 修复bug，优化使用体验
        de:
          - Beheben Sie Fehler und optimieren Sie die Benutzererfahrung.
        en:
          - Fix bugs and optimize users' experience.
        fr:
          - Corrigez les bugs et optimisez l'expérience utilisateur
        ja:
          - バグを修正し、ユーザーエクスペリエンスを最適化する
        ru:
          - Исправляйте ошибки и оптимизируйте взаимодействие с пользователем
      '2.3':
        cn:
          - 添加推送免打扰功能，用户可根据需要暂时关闭消息通知
          - 优化运动追踪功能，提高使用体验
          - 优化绑定流程，有助摄像机绑定
          - 修复bug
        de:
          - Wenn Sie die Funktion Push nicht stören hinzufügen, können Benutzer Nachrichtenbenachrichtigungen
            bei Bedarf vorübergehend deaktivieren
          - Optimieren Sie die Bewegungsverfolgungsfunktion, um die Benutzererfahrung
            zu verbessern
          - Optimieren Sie den Bindungsprozess, um die Kamerabindung zu unterstützen
          - Fehler behoben
        en:
          - Add push do not disturb function, users can temporarily turn off message
            notifications as needed
          - Optimize motion tracking function to improve user experience
          - Optimize the binding process to help camera binding
        fr:
          - Ajouter la fonction push ne pas déranger, les utilisateurs peuvent désactiver
            temporairement les notifications de message au besoin
          - Optimiser la fonction de suivi de mouvement pour améliorer l'expérience
            utilisateur
          - Optimiser le processus de liaison pour faciliter la liaison de la caméra
          - Réparer le bug
        ja:
          - 追加プッシュは機能を妨げません、ユーザーは必要に応じてメッセージ通知を一時的にオフにすることができます
          - モーショントラッキング機能を最適化して、ユーザーエクスペリエンスを向上させます
          - バインディングプロセスを最適化して、カメラのバインディングを支援します
          - バグを修正
        ru:
          - Добавьте функцию не беспокоить, пользователи могут временно отключить уведомления
            о сообщениях по мере необходимости
          - Оптимизируйте функцию отслеживания движения, чтобы улучшить взаимодействие
            с пользователем
          - Оптимизируйте процесс привязки, чтобы облегчить привязку камеры
          - Исправить ошибку
    vicoo_staging:
      2.1.3:
        cn:
          - 修复bug，优化使用体验
        de:
          - Beheben Sie Fehler und optimieren Sie die Benutzererfahrung.
        en:
          - Fix bugs and optimize users' experience.
        ja:
          - バグを修正し、ユーザーエクスペリエンスを最適化する
  feature_notes:
    dzees:
      14008:
        cn:
          - 修复bug，优化使用体验
        de:
          - Beheben Sie Fehler und optimieren Sie die Benutzererfahrung.
        en:
          - Fix bugs and optimize users' experience.
        ja:
          - バグを修正し、ユーザーエクスペリエンスを最適化する
    guard:
      14025:
        cn:
          - 修复bug，优化使用体验
        de:
          - Beheben Sie Fehler und optimieren Sie die Benutzererfahrung.
        en:
          - Fix bugs and optimize users' experience.
        ja:
          - バグを修正し、ユーザーエクスペリエンスを最適化する
    jxja:
      14002:
        cn:
          - 修复bug，优化使用体验
        de:
          - Beheben Sie Fehler und optimieren Sie die Benutzererfahrung.
        en:
          - Fix bugs and optimize users' experience.
        ja:
          - バグを修正し、ユーザーエクスペリエンスを最適化する
    soliom:
      14004:
        cn:
          - 修复bug，优化使用体验
        de:
          - Beheben Sie Fehler und optimieren Sie die Benutzererfahrung.
        en:
          - Fix bugs and optimize users' experience.
        ja:
          - バグを修正し、ユーザーエクスペリエンスを最適化する
    vicoo:
      13018:
        cn:
          - 修复bug，优化使用体验
        de:
          - Beheben Sie Fehler und optimieren Sie die Benutzererfahrung.
        en:
          - Fix bugs and optimize users' experience.
        ja:
          - バグを修正し、ユーザーエクスペリエンスを最適化する
  staging:
    dzees:
      build: 14008
      minBuild: 1002
      minVersion: '0.2'
      packageSize: 85
      version: 2.4
    guard:
      build: 13197
      minBuild: 1002
      minVersion: '0.2'
      packageSize: 85
      version: 2.4
    jxja:
      build: 14002
      minBuild: 1002
      minVersion: '0.2'
      packageSize: 85
      version: 2.4
    soliom:
      build: 14004
      minBuild: 1002
      minVersion: '0.2'
      packageSize: 85
      version: 2.4
    vicoo:
      build: 13197
      minBuild: 1002
      minVersion: '0.2'
      packageSize: 85
      version: 2.4
  submit:
    dzees:
      build: 14007
      version: '2.2'
    guard:
      build: 14017
      version: 1.2.0
    jxja:
      build: 14006
      version: '1.2'
    soliom:
      build: 14005
      version: 1.2.0
    vicoo:
      build: 13076
      version: '2.3'
  test:
    dzees:
      build: 14013
      minBuild: 1002
      minVersion: '0.2'
      packageSize: 85
      version: 2.4
    vicoo:
      build: 13029
      minBuild: 1002
      minVersion: '0.2'
      packageSize: 85
      version: 2.4