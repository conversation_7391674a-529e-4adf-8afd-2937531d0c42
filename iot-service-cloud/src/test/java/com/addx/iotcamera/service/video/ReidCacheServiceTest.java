package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.reid.UserReId;
import com.addx.iotcamera.dynamo.dao.UserReIdDAO;
import com.google.api.client.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ReidCacheServiceTest {
    @InjectMocks
    private ReidCacheService reidCacheService;

    @Mock
    private UserReIdDAO userReIdDAO;

    @Test
    @DisplayName("查询userReid list")
    public void test_queryReId(){
        when(userReIdDAO.queryReId(any())).thenReturn(Lists.newArrayList());
        String userId = "userId";
        List<UserReId> expectedResult = Lists.newArrayList();
        List<UserReId> actualResult = reidCacheService.queryReId(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("查询未删除userReid")
    public void test_queryNotDeletedReId(){
        when(userReIdDAO.queryNotDeletedReId(any())).thenReturn(Lists.newArrayList());
        String userId = "userId";
        List<UserReId> expectedResult = Lists.newArrayList();
        List<UserReId> actualResult = reidCacheService.queryNotDeletedReId(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("查询未推送的userReid")
    public void test_queryNotPushedReId(){
        when(userReIdDAO.queryNotPushedReId(any(),any())).thenReturn(Lists.newArrayList());
        String userId = "userId";
        List<UserReId> expectedResult = Lists.newArrayList();
        List<UserReId> actualResult = reidCacheService.queryNotPushedReId(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("更新userReid")
    public void test_updateReIdPush(){
        when(userReIdDAO.updateReId(any())).thenReturn(1);

        int expectedResult = 1;
        int actualResult = reidCacheService.updateReIdPush(new UserReId());
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("查询标记的userReid")
    public void test_queryMarkedReId(){
        when(userReIdDAO.query(any(),any())).thenReturn(Lists.newArrayList());
        String userId = "userId";
        List<UserReId> expectedResult = Lists.newArrayList();
        List<UserReId> actualResult = reidCacheService.queryMarkedReId(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("查询用户下有效的单个聚类")
    public void test_queryValidUserReId(){
        when(userReIdDAO.query(any(),any())).thenReturn(Lists.newArrayList());
        String userId = "userId";
        String labelId = "";
        UserReId expectedResult = null;
        UserReId actualResult = reidCacheService.queryValidUserReId(userId,labelId);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("更新聚类")
    public void test_updateReIdLabel(){
        when(userReIdDAO.updateReId(any())).thenReturn(1);
        int expectedResult = 1;
        int actualResult = reidCacheService.updateReIdLabel(new UserReId());
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    public void test_cleanCacheBatch(){
        reidCacheService.cleanCacheBatch("");
        Assert.assertTrue(true);
    }

    @Test
    public void test_cleanCacheSingle(){
        reidCacheService.cleanCacheSingle(new UserReId());
        Assert.assertTrue(true);
    }
}
