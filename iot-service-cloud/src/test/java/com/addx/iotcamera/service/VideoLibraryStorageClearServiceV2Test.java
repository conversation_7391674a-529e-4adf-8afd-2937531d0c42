package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.library.TierFreeUserExpireDO;
import com.addx.iotcamera.bean.response.user.ABTestResult;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.config.device.FreeUserVipTier2Config;
import com.addx.iotcamera.service.vip.TierService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.addx.iotcamera.constants.CopyWriteConstans.LIBRARY_BANNER_STOREGE;
import static com.addx.iotcamera.constants.CopyWriteConstans.LIBRARY_BANNER_UPGRADE_FREE;
import static org.addx.iot.common.constant.AppConstants.APP_LANGUAGE_ZH;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
//@Ignore
public class VideoLibraryStorageClearServiceV2Test {

    @InjectMocks
    @Spy
    private VideoLibraryStorageClearService videoLibraryStorageClearService;

    @Mock
    private TierService tierService;

    @Mock
    private RedisService redisService;

    private FreeUserVipTier2Config freeUserVipTier2ConfigUnderTest;

    @Mock
    private CenterNotifyConfig centerNotifyConfig;

    @Before
    public void setUp() throws Exception {
        freeUserVipTier2ConfigUnderTest = new FreeUserVipTier2Config();
        freeUserVipTier2ConfigUnderTest.afterPropertiesSet();

        freeUserVipTier2ConfigUnderTest.setRegisterConfigList(Arrays.asList(new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(110);
            setRegisterStart(Integer.MAX_VALUE - 1);
            setRegisterEnd(Integer.MAX_VALUE);
            setCooldownValue(Collections.singletonMap("cg1", "60"));
        }}, new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(100);
            setRegisterStart(0);
            setRegisterEnd(Integer.MAX_VALUE);
            setCooldownValue(Collections.singletonMap("cg1", "60"));
        }}, new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(100);
            setRegisterStart(0);
            setRegisterEnd(1);
            setCooldownValue(Collections.singletonMap("cg1", "60"));
        }}, new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(100);
            setRegisterStart(Integer.MAX_VALUE - 1);
            setRegisterEnd(Integer.MAX_VALUE);
            setCooldownValue(Collections.singletonMap("cg1", "60"));
        }}));
    }


    @Test
    @DisplayName("套餐信息不存在")
    public void test_freeUserStorageNotify_noTier(){
        Integer tierId = 1;
        User user = new User();
        user.setId(1);

        when(tierService.queryTierById(any())).thenReturn(null);
        TierFreeUserExpireDO expectResult = TierFreeUserExpireDO.builder()
                .notify(false)
                .build();
        ABTestResult awarenessFreeTrailDayAbResult = ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build();
        TierFreeUserExpireDO actualResult = videoLibraryStorageClearService.freeUserStorageNotify(user,tierId, awarenessFreeTrailDayAbResult);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("未达报警线")
    public void test_freeUserStorageNotify_noNotify(){
        Integer tierId = 100;
        User user = new User();
        user.setId(1);
        user.setRegistTime((int) Instant.now().getEpochSecond());
        user.setTenantId(TENANTID_VICOO);
        user.setLanguage(APP_LANGUAGE_ZH);
        Tier tier = new Tier();
        tier.setTierId(tierId);
        when(tierService.queryTierById(any())).thenReturn(tier);

        when(redisService.hashGetString(any(),any())).thenReturn(null);

        //tenantId / language /key
        Map<String, Map<String, Map<String, String>>> message = this.initNotifyMessage();

        when(centerNotifyConfig.getMessage()).thenReturn(message);

        TierFreeUserExpireDO expectResult = TierFreeUserExpireDO.builder()
                .notify(false)
                .notifyType(1)
                .storege(0.0D)
                .notifyMessage("")
                .upgradeFreeBtn("")
                .build();
        ABTestResult awarenessFreeTrailDayAbResult = ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build();
        TierFreeUserExpireDO actualResult = videoLibraryStorageClearService.freeUserStorageNotify(user,tierId, awarenessFreeTrailDayAbResult);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("未达报警线")
    public void test_freeUserStorageNotify_noNeed_Notify(){
        Integer tierId = 100;
        User user = new User();
        user.setId(1);
        user.setRegistTime((int) Instant.now().getEpochSecond());
        user.setTenantId(TENANTID_VICOO);
        user.setLanguage(APP_LANGUAGE_ZH);
        Tier tier = new Tier();
        tier.setTierId(tierId);
        when(tierService.queryTierById(any())).thenReturn(tier);

        when(redisService.hashGetString(any(),any())).thenReturn(null);

        //tenantId / language /key
        Map<String, Map<String, Map<String, String>>> message = this.initNotifyMessage();

        when(centerNotifyConfig.getMessage()).thenReturn(message);

        TierFreeUserExpireDO expectResult = TierFreeUserExpireDO.builder()
                .notify(false)
                .notifyType(1)
                .storege(0.0D)
                .notifyMessage("")
                .upgradeFreeBtn("")
                .build();
        ABTestResult awarenessFreeTrailDayAbResult = ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build();
        TierFreeUserExpireDO actualResult = videoLibraryStorageClearService.freeUserStorageNotify(user,tierId, awarenessFreeTrailDayAbResult);
        Assert.assertEquals(expectResult,actualResult);
    }



    @Test
    @DisplayName("未达报警线")
    public void test_freeUserStorageNotify(){
        {
            Integer tierId = 100;
            User user = new User();
            user.setId(1);
            user.setRegistTime((int) Instant.now().getEpochSecond());
            user.setTenantId(TENANTID_VICOO);
            user.setLanguage(APP_LANGUAGE_ZH);
            Tier tier = new Tier();
            tier.setTierId(tierId);
            when(tierService.queryTierById(any())).thenReturn(tier);

            when(redisService.hashGetString(any(),any())).thenReturn("1,2,2,3,4,6,7,8,9,10");

            //tenantId / language /key
            Map<String, Map<String, Map<String, String>>> message = this.initNotifyMessage();

            when(centerNotifyConfig.getMessage()).thenReturn(message);

            TierFreeUserExpireDO expectResult = TierFreeUserExpireDO.builder()
                    .notify(true)
                    .notifyType(1)
                    .storege(0.0D)
                    .notifyMessage("test storege")
                    .upgradeFreeBtn("免费试用")
                    .build();
            ABTestResult awarenessFreeTrailDayAbResult = ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build();
            TierFreeUserExpireDO actualResult = videoLibraryStorageClearService.freeUserStorageNotify(user,tierId, awarenessFreeTrailDayAbResult);
            Assert.assertEquals(expectResult,actualResult);
        }

        {
            Integer tierId = 100;
            User user = new User();
            user.setId(1);
            user.setRegistTime((int) Instant.now().getEpochSecond());
            user.setTenantId(TENANTID_VICOO);
            user.setLanguage(APP_LANGUAGE_ZH);
            Tier tier = new Tier();
            tier.setTierId(tierId);
            when(tierService.queryTierById(any())).thenReturn(tier);

            when(redisService.hashGetString(any(),any())).thenReturn("1,2,2,3,4,6,7,8,9,10");

            //tenantId / language /key
            Map<String, Map<String, Map<String, String>>> message = this.initNotifyMessage();

            when(centerNotifyConfig.getMessage()).thenReturn(message);

            TierFreeUserExpireDO expectResult = TierFreeUserExpireDO.builder()
                    .notify(true)
                    .notifyType(1)
                    .storege(0.0D)
                    .notifyMessage("test storege")
                    .build();
            ABTestResult awarenessFreeTrailDayAbResult = ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build();
            TierFreeUserExpireDO actualResult = videoLibraryStorageClearService.freeUserStorageNotify(user,tierId, awarenessFreeTrailDayAbResult);
            Assert.assertEquals(expectResult,actualResult);
        }
    }


    private Map<String, Map<String, Map<String, String>>> initNotifyMessage(){
        Map<String, Map<String, Map<String, String>>> message = new HashMap<>();
        Map<String, Map<String, String>> languageMassage = Maps.newHashMap();
        Map<String, String> keyMassage = Maps.newHashMap();
        keyMassage.put(LIBRARY_BANNER_STOREGE,"test storege");
        keyMassage.put(LIBRARY_BANNER_UPGRADE_FREE,"免费试用");
        languageMassage.put(APP_LANGUAGE_ZH,keyMassage);
        message.put(TENANTID_VICOO,languageMassage);
        return message;
    }
}
