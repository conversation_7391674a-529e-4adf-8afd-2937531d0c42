package com.addx.iotcamera.service;

import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.thread.TaskExecutePool;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class TaskRunMonitorTest {

    private ForkJoinPool pool = new ForkJoinPool(2);

    private String mdcValue = OpenApiUtil.shortUUID();
    @Test
    @SneakyThrows
    public void test_TaskRunMonitor() {
        testTaskRunMonitor(0);
        testTaskRunMonitor(1);
        testTaskRunMonitor(10);
        testTaskRunMonitor(11);
        testTaskRunMonitor(100);
        MDC.put(MDCKeys.REQUEST_ID, mdcValue);
        MDC.put(MDCKeys.SERIAL_NUMBER, mdcValue);
        MDC.put(MDCKeys.MODEL_NO, mdcValue);
        MDC.put(MDCKeys.FIRMWARE_ID, mdcValue);
        testTaskRunMonitor(101);
        testTaskRunMonitor(1000);
        testTaskRunMonitor(1001);
    }

    @SneakyThrows
    public void testTaskRunMonitor(int expectTaskNum) {
        TaskExecutePool taskExecutePool = new TaskExecutePool();
        AtomicInteger logNum = new AtomicInteger(0);
        taskExecutePool.setReportLogService(new ReportLogService() {
            @Override
            public void sysReportEvent(String reportType, Map<String, Object> map) {
                super.sysReportEvent(reportType, map);
                logNum.incrementAndGet();
            }
        });

        final TaskExecutePool.TaskRunMonitor monitor1 = taskExecutePool.new TaskRunMonitor("pool1");
        final List<Integer> tasks = IntStream.range(0, expectTaskNum).mapToObj(it -> it).collect(Collectors.toList());
        AtomicInteger taskNum = new AtomicInteger(0);
        final Integer result = pool.submit(() -> {
            return tasks.parallelStream().map(it -> {
                final Runnable runnable = monitor1.decorate(() -> {
//                    log.info("task:{}", it);
                    taskNum.incrementAndGet();
                });
                runnable.run();
                return it;
            }).reduce(Integer::sum).orElse(0);
        }).get();
        final Integer expectResult = tasks.stream().reduce(Integer::sum).orElse(0);
        Assert.assertEquals(expectResult, result);
        Assert.assertEquals(tasks.size(), taskNum.get());
        Assert.assertTrue(Math.abs((tasks.size() / 100) - logNum.get()) <= 2);
    }

}
