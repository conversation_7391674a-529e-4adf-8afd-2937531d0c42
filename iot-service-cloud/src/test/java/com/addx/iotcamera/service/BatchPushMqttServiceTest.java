package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.AdminAndSettingDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.service.device.DeviceDormancyPlanService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.stream.IntStream;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class BatchPushMqttServiceTest {

    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private DeviceDormancyPlanService deviceDormancyPlanService;
    @Mock
    private UserRoleService userRoleService;
    @InjectMocks
    private BatchPushMqttService batchPushMqttService;

    @Before
    public void before() {
        when(userRoleService.queryAllAdminAndSettingIterator(anyString(), anyInt())).thenAnswer(it -> {
            final int batchSize = it.getArgument(1);
            return IntStream.range(0, batchSize * 2 + 1).mapToObj(i -> new AdminAndSettingDO() {{
                setSerialNumber("sn" + i);
                setUserId(i);
            }}).iterator();
        });
        when(userRoleService.queryAllAdminUserRoleIterator(anyString(), anyInt())).thenAnswer(it -> {
            final int batchSize = it.getArgument(1);
            return IntStream.range(0, batchSize * 2 + 1).mapToObj(i -> new UserRoleDO() {{
                setSerialNumber("sn" + i);
                setUserId(i);
            }}).iterator();
        });
    }

    @Test
    public void test_pushAllDeviceDormancyPlan() {
        batchPushMqttService.pushAllDeviceDormancyPlan(1000);
    }

    @Test
    public void test_pushAllDeviceSetting() {
        batchPushMqttService.pushAllDeviceSetting(1000);
    }

}
