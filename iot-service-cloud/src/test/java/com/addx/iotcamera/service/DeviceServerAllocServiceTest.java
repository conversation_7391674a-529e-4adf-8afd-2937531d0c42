package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.device.DeviceServerAllocDO;
import com.addx.iotcamera.dao.device.DeviceServerAllocDAO;
import com.addx.iotcamera.service.device.DeviceServerAllocService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceServerAllocServiceTest {

    @InjectMocks
    private DeviceServerAllocService deviceServerAllocService;
    @Mock
    private DeviceServerAllocDAO deviceServerAllocDAO;

    @Test
    public void test_deviceServerAllocService_queryBySn() {
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceServerAllocDAO.queryBySn(sn)).thenReturn(null);
            Assert.assertEquals(new DeviceServerAllocDO().setSn(sn), deviceServerAllocService.queryBySn(sn));
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceServerAllocDAO.queryBySn(sn)).thenThrow(new RuntimeException("mock"));
            Assert.assertEquals(new DeviceServerAllocDO().setSn(sn), deviceServerAllocService.queryBySn(sn));
        }
        {
            String sn = OpenApiUtil.shortUUID();
            DeviceServerAllocDO model = new DeviceServerAllocDO().setSn(sn).setKissPurpose("tcpdump").setCoturnPurpose("tcpdump");
            when(deviceServerAllocDAO.queryBySn(sn)).thenReturn(model);
            Assert.assertEquals(model, deviceServerAllocService.queryBySn(sn));
            Assert.assertEquals(model, JSON.parseObject(model.toString(), DeviceServerAllocDO.class));
        }
    }

    @Test
    public void test_deviceServerAllocService_save() {
        {
            DeviceServerAllocDO model = null;
            when(deviceServerAllocDAO.save(model)).thenReturn(0);
            Assert.assertEquals(0, deviceServerAllocService.save(model));
        }
        {
            DeviceServerAllocDO model = new DeviceServerAllocDO().setSn(null).setKissPurpose("tcpdump").setCoturnPurpose("tcpdump");
            when(deviceServerAllocDAO.save(model)).thenReturn(0);
            Assert.assertEquals(0, deviceServerAllocService.save(model));
        }
        {
            String sn = OpenApiUtil.shortUUID();
            DeviceServerAllocDO model = new DeviceServerAllocDO().setSn(sn).setKissPurpose("tcpdump").setCoturnPurpose("tcpdump");
            when(deviceServerAllocDAO.save(model)).thenReturn(1);
            Assert.assertEquals(1, deviceServerAllocService.save(model));
        }
    }

    @Test
    public void test_DeviceServerAllocDAO() {
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        DeviceServerAllocDAO dao = testHelper.getMapper(DeviceServerAllocDAO.class);
        {
            String sn = "test_" + OpenApiUtil.shortUUID();
            DeviceServerAllocDO model1 = new DeviceServerAllocDO().setSn(sn);
            Assert.assertEquals(1, dao.save(model1));
            DeviceServerAllocDO model2 = dao.queryBySn(sn);
            DeviceServerAllocDO model3 = new DeviceServerAllocDO().setSn(sn)
                    .setKissPurpose("normal").setCoturnPurpose("normal");
            Assert.assertEquals(model3, model2);

            model1.setKissPurpose("tcpdump").setCoturnPurpose("tcpdump");
            Assert.assertEquals(2, dao.save(model1));
            DeviceServerAllocDO model4 = dao.queryBySn(sn);
            DeviceServerAllocDO model5 = new DeviceServerAllocDO().setSn(sn)
                    .setKissPurpose("tcpdump").setCoturnPurpose("tcpdump");
            Assert.assertEquals(model5, model4);
        }
        {
            String sn = "test_" + OpenApiUtil.shortUUID();
            DeviceServerAllocDO model1 = new DeviceServerAllocDO().setSn(sn)
                    .setKissPurpose("tcpdump");
            int insertNum = dao.save(model1);
            Assert.assertEquals(1, insertNum);
            DeviceServerAllocDO model2 = dao.queryBySn(sn);
            DeviceServerAllocDO model3 = new DeviceServerAllocDO().setSn(sn)
                    .setKissPurpose("tcpdump").setCoturnPurpose("normal");
            Assert.assertEquals(model3, model2);

            model1.setCoturnPurpose("tcpdump");
            Assert.assertEquals(2, dao.save(model1));
            DeviceServerAllocDO model4 = dao.queryBySn(sn);
            DeviceServerAllocDO model5 = new DeviceServerAllocDO().setSn(sn)
                    .setKissPurpose("tcpdump").setCoturnPurpose("tcpdump");
            Assert.assertEquals(model5, model4);
        }
        {
            String sn = "test_" + OpenApiUtil.shortUUID();
            DeviceServerAllocDO model1 = new DeviceServerAllocDO().setSn(sn)
                    .setCoturnPurpose("tcpdump");
            int insertNum = dao.save(model1);
            Assert.assertEquals(1, insertNum);
            DeviceServerAllocDO model2 = dao.queryBySn(sn);
            DeviceServerAllocDO model3 = new DeviceServerAllocDO().setSn(sn)
                    .setCoturnPurpose("tcpdump");
            Assert.assertEquals(model3, model2);

            model1.setKissPurpose("tcpdump");
            Assert.assertEquals(2, dao.save(model1));
            DeviceServerAllocDO model4 = dao.queryBySn(sn);
            DeviceServerAllocDO model5 = new DeviceServerAllocDO().setSn(sn)
                    .setKissPurpose("tcpdump").setCoturnPurpose("tcpdump");
            Assert.assertEquals(model5, model4);
        }
    }

}
