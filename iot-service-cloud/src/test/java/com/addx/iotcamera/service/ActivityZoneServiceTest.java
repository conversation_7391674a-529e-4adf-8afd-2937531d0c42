package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.ActivityZoneRequest;
import com.addx.iotcamera.bean.db.ActivityZoneDO;
import com.addx.iotcamera.dao.ActivityZoneDAO;
import com.google.common.collect.ImmutableList;
import org.addx.iot.common.vo.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.addx.iot.common.enums.ResultCollection.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ActivityZoneServiceTest {

    @Mock
    VideoSearchService videoSearchService;

    @Mock
    ActivityZoneDAO activityZoneDAO;

    @Mock
    DeviceAuthService deviceAuthService;

    @Mock
    ActivityZoneRequest activityZoneRequest;

    @InjectMocks
    private ActivityZoneService activityZoneService;

    @Before
    public void setup() {

    }

    @Test
    public void testGetActivityZonesWithValidPermission() {

        when(deviceAuthService.checkDeviceCommonAccess(any(), any())).thenReturn(SUCCESS.getCode());

        ActivityZoneDO expectedActivityZoneDO = ActivityZoneDO.builder()
                .serialNumber("mockSerialNumber")
                .build();

        when(activityZoneDAO.getActivityZonesBySerailNumber(any())).thenReturn(ImmutableList.of(expectedActivityZoneDO));

        Result actualResult = activityZoneService.getActivityZones(activityZoneRequest, 0);

        assertEquals(Result.ListResult(ImmutableList.of(expectedActivityZoneDO)), actualResult);
    }

    @Test
    public void testGetActivityZonesWithInvalidPermission() {
        when(deviceAuthService.checkDeviceCommonAccess(any(), any())).thenReturn(DEVICE_NO_ROLE.getCode());

        Result expectedResult = DEVICE_NO_ROLE.getResult();

        Result actualResult = activityZoneService.getActivityZones(activityZoneRequest, 0);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testUpdateActivityZoneWithValidPermission() {
        when(deviceAuthService.checkAdminAccess(any(), any())).thenReturn(SUCCESS.getCode());

        when(activityZoneDAO.updateActivityZone(any())).thenReturn(1);

        Result actualResult = activityZoneService.updateActivityZone(activityZoneRequest, 1);

        assertEquals(Result.Success(), actualResult);
    }

    @Test
    public void testUpdateActivityZoneWithInValidPermission() {
        when(deviceAuthService.checkAdminAccess(any(), any())).thenReturn(DEVICE_AUTH_LIMITATION.getCode());

        Result expectedResult = DEVICE_AUTH_LIMITATION.getResult();

        Result actualResult = activityZoneService.updateActivityZone(activityZoneRequest, 1);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testInsertActivityZoneWithValidPermission() {
        when(deviceAuthService.checkAdminAccess(any(), any())).thenReturn(SUCCESS.getCode());

        when(activityZoneDAO.insertActivityZone(any())).thenReturn(1);

        //mock了dao层，但实际未执行，所以返回的id为null
        Result expectedResult = Result.KVResult("id", null);

        Result actualResult = activityZoneService.insertActivityZone(activityZoneRequest, 1);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testDeleteActivityZoneWithValidPermission() {
        when(deviceAuthService.checkAdminAccess(any(), any())).thenReturn(SUCCESS.getCode());

        when(activityZoneDAO.deleteActivityZone(any())).thenReturn(1);

        Result actualResult = activityZoneService.deleteActivityZone(activityZoneRequest, 1);

        assertEquals(Result.Success(), actualResult);
    }

    @Test
    public void testDeleteActivityZonesWithInvalidPermission() {
        when(deviceAuthService.checkDeviceCommonAccess(any(), any())).thenReturn(DEVICE_NO_ROLE.getCode());

        Result expectedResult = DEVICE_NO_ROLE.getResult();

        Result actualResult = activityZoneService.getActivityZones(activityZoneRequest, 1);

        assertEquals(expectedResult, actualResult);
    }

}
