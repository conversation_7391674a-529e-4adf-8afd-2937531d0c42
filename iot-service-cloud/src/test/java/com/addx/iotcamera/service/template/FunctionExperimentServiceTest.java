package com.addx.iotcamera.service.template;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.domain.library.TierFreeUserExpireDO;
import com.addx.iotcamera.bean.domain.library.TierFreeUserVipDO;
import com.addx.iotcamera.config.template.GrayscaleTemplateConfig;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.service.creative.CreativeService;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class FunctionExperimentServiceTest {
    @InjectMocks
    private FunctionExperimentService functionExperimentService = new TierFreeUserNotifyService();

    @Mock
    GrayscaleTemplateConfig grayscaleTemplateConfig;

    @Mock
    private UserVipService userVipService;

    @Test
    @DisplayName("未配置白名单")
    public void test_verifyWhiteConfig_white_null(){
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(Sets.newHashSet());
        boolean expectedResult = false;
        boolean actualResult = functionExperimentService.verifyWhiteConfig(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("在白名单内")
    public void test_verifyWhiteConfig_white_user_In(){
        Integer userId = 1;
        Set<Integer> set = Sets.newHashSet();
        set.add(1);
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(set);
        boolean expectedResult = true;
        boolean actualResult = functionExperimentService.verifyWhiteConfig(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("未配置灰度规则")
    public void test_Grayscale_null(){
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(Sets.newHashSet());

        when(grayscaleTemplateConfig.queryGrayscaleTemplate(any())).thenReturn(null);

        boolean expectedResult = true;
        boolean actualResult = functionExperimentService.verifyGrayscale(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("未配置灰度规则")
    public void test_Grayscale_total_null(){
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(Sets.newHashSet());

        GrayscaleTemplateConfig.GrayscaleTemplate template = new GrayscaleTemplateConfig.GrayscaleTemplate();
        template.setGrayscaleTotal(null);
        template.setGrayscaleScale(1);
        when(grayscaleTemplateConfig.queryGrayscaleTemplate(any())).thenReturn(template);

        boolean expectedResult = true;
        boolean actualResult = functionExperimentService.verifyGrayscale(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("配置灰度规则-不符合")
    public void test_Grayscale_Scale_null(){
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(Sets.newHashSet());

        GrayscaleTemplateConfig.GrayscaleTemplate template = new GrayscaleTemplateConfig.GrayscaleTemplate();
        template.setGrayscaleTotal(1);
        template.setGrayscaleScale(null);
        when(grayscaleTemplateConfig.queryGrayscaleTemplate(any())).thenReturn(template);

        boolean expectedResult = true;
        boolean actualResult = functionExperimentService.verifyGrayscale(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }




    @Test
    public void test_verify(){
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(Sets.newHashSet());

        GrayscaleTemplateConfig.GrayscaleTemplate template = new GrayscaleTemplateConfig.GrayscaleTemplate();
        template.setGrayscaleTotal(10);
        template.setGrayscaleScale(1);
         when(grayscaleTemplateConfig.queryGrayscaleTemplate(any())).thenReturn(template);

        when(grayscaleTemplateConfig.queryOperateProtect(any())).thenReturn(null);

        when(userVipService.initFreeUserVip(any(),any())).thenReturn(TierFreeUserVipDO.builder()
                .freeVip(false)
                .build());

        TierFreeUserExpireDO expectedResult = TierFreeUserExpireDO.builder()
                .notify(false)
                .notifyCount(1)
                .slotName(CreativeService.GUIDE_AFTER_CLOUD_BANNER)
                .build();

        TierFreeUserExpireDO actualResult = functionExperimentService.verify(userId,new AppRequestBase());

        Assert.assertEquals(expectedResult,actualResult);
    }



    @Test
    @DisplayName("未配置白名单")
    public void test_verifyRepeatedMinder_white_null(){
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(new HashSet(Arrays.asList(userId)));
        boolean expectedResult = true;
        boolean actualResult = functionExperimentService.verifyRepeatedMinder(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("未配置灰度规则")
    public void test_verifyRepeatedMinder_Grayscale_null(){
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(Sets.newHashSet());

        when(grayscaleTemplateConfig.queryGrayscaleRepeatedReminder(any())).thenReturn(null);

        boolean expectedResult = true;
        boolean actualResult = functionExperimentService.verifyRepeatedMinder(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("未配置灰度规则")
    public void test_verifyRepeatedMinder_total_null(){
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(Sets.newHashSet());

        GrayscaleTemplateConfig.GrayscaleTemplate template = new GrayscaleTemplateConfig.GrayscaleTemplate();
        template.setGrayscaleTotal(null);
        template.setGrayscaleScale(1);
        when(grayscaleTemplateConfig.queryGrayscaleRepeatedReminder(any())).thenReturn(template);

        boolean expectedResult = true;
        boolean actualResult = functionExperimentService.verifyRepeatedMinder(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("配置灰度规则-不符合")
    public void test_verifyRepeatedMinder(){
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(Sets.newHashSet());

        GrayscaleTemplateConfig.GrayscaleTemplate template = new GrayscaleTemplateConfig.GrayscaleTemplate();
        template.setGrayscaleTotal(10);
        template.setGrayscaleScale(2);
        when(grayscaleTemplateConfig.queryGrayscaleRepeatedReminder(any())).thenReturn(template);

        boolean expectedResult = true;
        boolean actualResult = functionExperimentService.verifyRepeatedMinder(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }

}
