package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.bird.BirdName;
import com.addx.iotcamera.bean.bird.BirdNameDict;
import com.addx.iotcamera.bean.bird.BirdNameFeedback;
import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.domain.BirdNameFeedbackDO;
import com.addx.iotcamera.config.EsConfig;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class BirdLoversServiceTest {

    @Mock
    private DeviceAiSettingsService deviceAiSettingsService;
    @Mock
    private MessageNotificationSettingsService messageNotificationSettingsService;
    @Mock
    private DeviceAuthService deviceAuthService;
    @Mock
    private LibraryService libraryService;
    @Mock
    private LibraryStatusService libraryStatusService;
    @Mock
    private S3Service s3Service;

    @InjectMocks
    private BirdLoversService birdLoversService;

//    private String esEndpoint;
//    private BirdNameDict dict;

    @Before
    @SneakyThrows
    public void init() {
        EsConfig esConfig = new EsConfig();
        esConfig.setEndpoints(Arrays.asList("https://elasticsearch-cn.addx.live"));
        esConfig.setIndexes(ImmutableMap.of("birdName", "bird_name"));
//        esConfig.setEndpoints(Arrays.asList("https://elasticsearch-eu.addx.live"));
//        esConfig.setIndexes(ImmutableMap.of("birdName", "bird_name"));
//        esConfig.setEndpoints(Arrays.asList("https://elasticsearch-us.addx.live"));
//        esConfig.setIndexes(ImmutableMap.of("birdName", "bird_name"));
        birdLoversService.setEsConfig(esConfig);
        birdLoversService.init();
        when(s3Service.preSignUrl(any())).thenAnswer(it -> it.getArgument(0) + "?signed");
    }

//    @Test
    public void test_readBirdNameDictFromFile() {
        BirdNameDict dict = birdLoversService.readBirdNameDictFromFile();
        Assert.assertEquals(11088, dict.getContent().size());
    }

//    @Test
    public void test_createBirdNameIndex() {
        String esIndexName = "bird_name_test";
        birdLoversService.deleteEsIndex(esIndexName);
        String birdNamesText = "" +
                "{\"enName\":\"Common Ostrich\",\"family\":\"Struthionidae\",\"ioc_12_1\":\"Struthio camelus\",\"lang2Name\":{\"en\":\"Common Ostrich\",\"ca\":\"estruç comú\",\"zh\":\"非洲鸵鸟\",\"zh-Hant\":\"鴕鳥\",\"hr\":\"noj\",\"cs\":\"pštros dvouprstý\",\"da\":\"Struds\",\"nl\":\"Struisvogel\",\"fi\":\"strutsi\",\"fr\":\"Autruche d’Afrique\",\"de\":\"Strauß\",\"it\":\"Struzzo\",\"ja\":\"ダチョウ\",\"lt\":\"strutis\",\"no\":\"slettestruts\",\"pl\":\"struś czerwonoskóry\",\"pt\":\"avestruz-comum\",\"ru\":\"Африканский страус\",\"sr\":\"Noj\",\"sk\":\"pštros dvojprstý\",\"es\":\"Avestruz común\",\"sv\":\"struts\",\"tr\":\"Devekuşu\",\"uk\":\"страус африканський\",\"af\":\"Volstruis\",\"et\":\"jaanalind\",\"hu\":\"strucc\",\"is\":\"Strútur\",\"lv\":\"Āfrikas strauss\",\"smi\":\"Struhcca\",\"sl\":\"noj\"},\"order\":\"STRUTHIONIFORMES\",\"seq\":\"1\",\"zhName\":\"非洲鸵鸟\"}\n" +
                "{\"enName\":\"Somali Ostrich\",\"family\":\"Struthionidae\",\"ioc_12_1\":\"Struthio molybdophanes\",\"lang2Name\":{\"en\":\"Somali Ostrich\",\"ca\":\"estruç de Somàlia\",\"zh\":\"灰颈鸵鸟\",\"zh-Hant\":\"索馬利亞鴕鳥\",\"hr\":\"somalijski noj\",\"cs\":\"pštros somálský\",\"da\":\"Somalistruds\",\"nl\":\"Somalische Struisvogel\",\"fi\":\"somalianstrutsi\",\"fr\":\"Autruche de Somalie\",\"de\":\"Somalistrauß\",\"it\":\"Struzzo somalo\",\"ja\":\"ソマリアダチョウ\",\"no\":\"somalistruts\",\"pl\":\"struś szaroskóry\",\"pt\":\"avestruz-somali\",\"ru\":\"Сомалийский страус\",\"sr\":\"Somalijski noj\",\"sk\":\"pštros sivonohý\",\"es\":\"Avestruz somalí\",\"sv\":\"somaliastruts\",\"tr\":\"Somali Devekuşu\",\"uk\":\"страус сомалійський\",\"et\":\"somaali jaanalind\",\"hu\":\"szomáliai strucc\"},\"order\":\"STRUTHIONIFORMES\",\"seq\":\"2\",\"zhName\":\"灰颈鸵鸟\"}\n" +
                "{\"enName\":\"Greater Rhea\",\"family\":\"Rheidae\",\"ioc_12_1\":\"Rhea americana\",\"lang2Name\":{\"en\":\"Greater Rhea\",\"ca\":\"nyandú comú\",\"zh\":\"大美洲鸵\",\"zh-Hant\":\"大美洲鴕\",\"hr\":\"veliki nandu\",\"cs\":\"nandu pampový\",\"da\":\"Nandu\",\"nl\":\"Nandoe\",\"fi\":\"nandu\",\"fr\":\"Nandou d’Amérique\",\"de\":\"Nandu\",\"it\":\"Nandù\",\"ja\":\"レア\",\"lt\":\"amerikinis nandu\",\"no\":\"stornandu\",\"pl\":\"nandu szare\",\"pt\":\"nandu-grande\",\"ru\":\"Обыкновенный нанду\",\"sr\":\"Veliki nandu\",\"sk\":\"nandu pampový\",\"es\":\"Ñandú común\",\"sv\":\"större nandu\",\"tr\":\"Büyük Rea\",\"uk\":\"нанду великий\",\"et\":\"nandu\",\"hu\":\"nandu\"},\"order\":\"RHEIFORMES\",\"seq\":\"3\",\"zhName\":\"大美洲鸵\"}\n";
        {
            ByteArrayInputStream is = new ByteArrayInputStream(birdNamesText.getBytes(StandardCharsets.UTF_8));
            Result<JSONObject> result = birdLoversService.createBirdNameIndex(esIndexName, is);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        {
            ByteArrayInputStream is = new ByteArrayInputStream(birdNamesText.getBytes(StandardCharsets.UTF_8));
            Result<JSONObject> result = birdLoversService.createBirdNameIndex(esIndexName, is);
            Assert.assertEquals(Result.failureFlag, result.getResult());
        }
    }

    /**
     * 建立鸟类名称es中，把json文件中的鸟类名称存入es索引中
     * 仅仅用于本地操作
     */
//    @Test
    @SneakyThrows
    public void put_birdName_to_es() {

    }

    /**
     * 把excel中的鸟类名称读取到项目的json文件中
     * 仅仅用于本地操作
     */
//    @Test
    @SneakyThrows
    public void write_birdNames_to_json() {
        InputStream is = ConfigHelper.loadConfig("classpath:bird/birdNames.json");
        birdLoversService.createBirdNameIndex("bird_name", is);
    }

    /**
     * 对比 11088种鸟的excel 和 404种鸟的算法模型
     * 仅仅用于本地操作
     */
//    @Test
    @SneakyThrows
    public void compare_11088_and_404() {
//        InputStream is404 = ConfigHelper.loadConfig("classpath:bird/bird_class_nabird404.txt");
        InputStream is404 = ConfigHelper.loadConfig("classpath:bird/bird_class_nabird404_IOC.txt");
        List<String> birdNames404 = new BufferedReader(new InputStreamReader(is404))
                .lines().filter(StringUtils::isNotBlank).collect(Collectors.toList());

        InputStream is11088 = ConfigHelper.loadConfig("classpath:bird/birdNames.json");
        List<BirdName> birdNames = new BufferedReader(new InputStreamReader(is11088))
                .lines().filter(StringUtils::isNotBlank).map(it -> JSON.parseObject(it, BirdName.class))
                .collect(Collectors.toList());
        Map<String, BirdName> stdNameMap = birdNames.stream().collect(Collectors.toMap(it -> it.getIoc_12_1(), it -> it));

        List<String> notFondNames = FuncUtil.subtractToList(birdNames404, stdNameMap.keySet());
        log.info("notFondNames={},{}", notFondNames.size(), JSON.toJSONString(notFondNames, true));
        List<String> requiredLangs = Arrays.asList("zh", "zh-Hant", "en", "ja", "de", "fr", "ru", "it", "es", "fi", "he", "iw", "ar", "vi", "pt", "pl", "tr");
        Map<String, List<String>> notFoundMap = new LinkedHashMap<>();
        for (String stdName : birdNames404) {
            BirdName birdName = stdNameMap.get(stdName);
            List<String> notFoundLangs;
            if (birdName != null) {
                notFoundLangs = FuncUtil.subtractToList(requiredLangs, birdName.getLang2Name().keySet());
            } else {
                notFoundLangs = new ArrayList<>(requiredLangs);
            }
            if (!notFoundLangs.isEmpty()) {
                notFoundMap.put(stdName, notFoundLangs);
            } else {
                log.info("fond: {}", birdName);
            }
        }
        int i = 0;
        for (Map.Entry<String, List<String>> entry : notFoundMap.entrySet()) {
            System.out.printf("%s : %s : %s\n", i++, entry.getKey(), JSON.toJSONString(entry.getValue()));
        }
    }

//    @Test
//    public void test_searchBirdName() {
//        BirdNameSearch input = new BirdNameSearch().setLanguage("zh").setInputName("哈").setLimitNum(10);
//        List<BirdNameVO> birdNames = birdLoversService.searchBirdName(input);
//        for (BirdNameVO birdName : birdNames) {
//            log.info("searchBirdName result:zh={},en={}", birdName.getZhName(), birdName.getEnName());
//        }
//        Assert.assertEquals(10, birdNames.size());
//        BirdNameSearch input2 = new BirdNameSearch().setLanguage("zh").setInputName("哈氏").setLimitNum(10);
//        List<BirdNameVO> birdNames2 = birdLoversService.searchBirdName(input2);
//        for (BirdNameVO birdName : birdNames2) {
//            log.info("searchBirdName result:zh={},en={}", birdName.getZhName(), birdName.getEnName());
//        }
//    }

//    @Test
//    public void test_feedbackBirdName() {
//        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
//        BirdNameFeedbackDAO birdNameFeedbackDAO = testHelper.getMapper(BirdNameFeedbackDAO.class);
//        birdLoversService.setBirdNameFeedbackDAO(birdNameFeedbackDAO);
//
//        final String traceId = OpenApiUtil.shortUUID();
//        final Integer userId = 123;
//        // 同种语言只保存一次
//        {
//            BirdNameSearch search = new BirdNameSearch().setLanguage("zh").setInputName("牛").setLimitNum(10);
//            List<BirdNameVO> matchList = birdLoversService.searchBirdName(search);
//            if (matchList.size() < 2) return;
//            for (int i = 0; i < 2; i++) {
//                BirdNameVO birdName = matchList.get(i);
//                BirdNameFeedback feedback = new BirdNameFeedback()
//                        .setTraceId(traceId).setUserId(userId).setLanguage(search.getLanguage())
//                        .setInputName(birdName.getMatchName())
//                        .setSelectStdName(birdName.getStdName()).setPossibleStdName("1");
//                Result result = birdLoversService.feedbackBirdName(feedback);
//                log.info("feedbackBirdName i={},result={}", i, JSON.toJSONString(result));
//                Assert.assertEquals(i == 0 ? 1 : 2, ((JSONObject) result.getData()).getIntValue("saveNum"));
//                assertBirdNameFeedbackSaveSuccess(feedback);
//            }
//        }
//        {
//            BirdNameSearch search = new BirdNameSearch().setLanguage("en").setInputName("red").setLimitNum(10);
//            List<BirdNameVO> matchList = birdLoversService.searchBirdName(search);
//            if (matchList.size() < 1) return;
//            BirdNameVO birdName = matchList.get(0);
//            BirdNameFeedback feedback = new BirdNameFeedback()
//                    .setTraceId(traceId).setUserId(userId).setLanguage(search.getLanguage())
//                    .setInputName(birdName.getMatchName())
//                    .setSelectStdName(birdName.getStdName()).setPossibleStdName("1");
//            Result result = birdLoversService.feedbackBirdName(feedback);
//            Assert.assertEquals(1, ((JSONObject) result.getData()).getIntValue("saveNum"));
//            assertBirdNameFeedbackSaveSuccess(feedback);
//        }
//        testHelper.commitAndClose();
//    }

    private void assertBirdNameFeedbackSaveSuccess(BirdNameFeedback feedback) {
        List<BirdNameFeedbackDO> list = birdLoversService.queryBirdNameFeedback(feedback);
        BirdNameFeedbackDO queryFeedback = list.stream().filter(it -> it.getLanguage().equals(feedback.getLanguage())).findFirst().orElse(null);
        Assert.assertNotNull(queryFeedback);
        Assert.assertEquals(feedback.getInputName(), queryFeedback.getInputName());
        Assert.assertEquals(feedback.getSelectStdName(), queryFeedback.getSelectStdName());
        Assert.assertEquals(feedback.getPossibleStdName(), queryFeedback.getPossibleStdName());
    }

//    @Test
//    public void test_handleAiEventPossibleSubcategory() {
//        {
//            List<PossibleSubcategory> list = birdLoversService.handleAiEventPossibleSubcategory(null);
//            Assert.assertEquals(0, list.size());
//        }
//        {
//            List<PossibleSubcategory> list = birdLoversService.handleAiEventPossibleSubcategory(Arrays.asList());
//            Assert.assertEquals(0, list.size());
//        }
//        {
//            PossibleSubcategory subcategory = new PossibleSubcategory().setName("Aphanapteryx bonasia").setConfidence(new BigDecimal("0.89"));
//            List<PossibleSubcategory> list = birdLoversService.handleAiEventPossibleSubcategory(Arrays.asList(subcategory));
//            Assert.assertEquals(1, list.size());
//            Assert.assertEquals(subcategory.getName(), list.get(0).getName());
//        }
//        {
//            PossibleSubcategory subcategory = new PossibleSubcategory().setName("Red").setConfidence(new BigDecimal("0.89"));
//            List<PossibleSubcategory> list = birdLoversService.handleAiEventPossibleSubcategory(Arrays.asList(subcategory));
//            Assert.assertEquals(0, list.size());
//        }
//    }

    @Test
    public void test_getBirdName() {
        BirdName birdName = birdLoversService.getBirdName("1");
        Assert.assertNotNull(birdName);
        Assert.assertEquals("1", birdName.getSeq());
    }

    @Test
    public void test_queryBirdAiSetting() {
        String sn = OpenApiUtil.shortUUID();
        Integer userId = 395304583;
        {
            when(deviceAuthService.checkActivatedAccess(any(), any())).thenReturn(Result.failureFlag);
            Result<JSONObject> result = birdLoversService.queryBirdAiSetting(userId, sn);
            Assert.assertNotEquals(Result.successFlag, result.getResult());
        }
        when(deviceAuthService.checkActivatedAccess(any(), any())).thenReturn(Result.successFlag);
        {
            Set<AiObjectEnum> oldAiAnalyzeEventObjects = new HashSet<>(Arrays.asList(AiObjectEnum.PERSON));
            when(deviceAiSettingsService.queryEnableEventObjects(userId, sn)).thenReturn(oldAiAnalyzeEventObjects);
            MessageNotificationSetting setting = MessageNotificationSetting.builder().serialNumber(sn).userId(userId)
                    .eventObjects(AiObjectEnum.PERSON.getObjectName()).packageEventType("").build();
            when(messageNotificationSettingsService.queryMessageNotificationSetting(sn, userId)).thenReturn(setting);
            Result<JSONObject> result = birdLoversService.queryBirdAiSetting(userId, sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(false, result.getData().getBoolean("aiAnalyzeSwitch"));
            Assert.assertEquals(false, result.getData().getBoolean("aiNotifySwitch"));
        }
        {
            Set<AiObjectEnum> oldAiAnalyzeEventObjects = new HashSet<>(Arrays.asList(AiObjectEnum.PERSON, AiObjectEnum.BIRD));
            when(deviceAiSettingsService.queryEnableEventObjects(userId, sn)).thenReturn(oldAiAnalyzeEventObjects);
            MessageNotificationSetting setting = MessageNotificationSetting.builder().serialNumber(sn).userId(userId)
                    .eventObjects(AiObjectEnum.PERSON.getObjectName() + "," + AiObjectEnum.BIRD.getObjectName()).packageEventType("").build();
            when(messageNotificationSettingsService.queryMessageNotificationSetting(sn, userId)).thenReturn(setting);
            Result<JSONObject> result = birdLoversService.queryBirdAiSetting(userId, sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(true, result.getData().getBoolean("aiAnalyzeSwitch"));
            Assert.assertEquals(true, result.getData().getBoolean("aiNotifySwitch"));
        }
        {
            Set<AiObjectEnum> oldAiAnalyzeEventObjects = new HashSet<>(Arrays.asList(AiObjectEnum.BIRD));
            when(deviceAiSettingsService.queryEnableEventObjects(userId, sn)).thenReturn(oldAiAnalyzeEventObjects);
            when(messageNotificationSettingsService.queryMessageNotificationSetting(sn, userId)).thenReturn(null);
            Result<JSONObject> result = birdLoversService.queryBirdAiSetting(userId, sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(true, result.getData().getBoolean("aiAnalyzeSwitch"));
            Assert.assertEquals(true, result.getData().getBoolean("aiNotifySwitch"));
        }
    }

    @Test
    public void test_updateBirdAiSetting() {
        String sn = OpenApiUtil.shortUUID();
        Integer userId = 395304583;
        {
            when(deviceAuthService.checkActivatedAccess(any(), any())).thenReturn(Result.failureFlag);
            Result result = birdLoversService.updateBirdAiSetting(userId, sn, new JSONObject());
            Assert.assertNotEquals(Result.successFlag, result.getResult());
        }
        when(deviceAuthService.checkActivatedAccess(any(), any())).thenReturn(Result.successFlag);
        final AtomicReference<Boolean> expectAiAnalyzeSwitch = new AtomicReference<>(null);
        when(deviceAiSettingsService.updateEnableEventObjectsPartly(any(), any(), any(), any())).thenAnswer(it -> {
            Collection<AiObjectEnum> enableList = it.getArgument(2);
            Collection<AiObjectEnum> disableList = it.getArgument(3);
            Boolean expectValue = expectAiAnalyzeSwitch.get();
            if (expectValue == null) {
                Assert.assertFalse(true);
            } else if (expectValue) {
                Assert.assertEquals(Arrays.asList(AiObjectEnum.BIRD), enableList);
                Assert.assertEquals(Arrays.asList(), disableList);
            } else {
                Assert.assertEquals(Arrays.asList(), enableList);
                Assert.assertEquals(Arrays.asList(AiObjectEnum.BIRD), disableList);
            }
            return 1;
        });
        final AtomicReference<Boolean> expectAiNotifySwitch = new AtomicReference<>(null);
        when(messageNotificationSettingsService.updateMessageNotificationSettings(any())).thenAnswer(it -> {
            MessageNotificationSetting model = it.getArgument(0);
            Boolean expectValue = expectAiNotifySwitch.get();
            if (expectValue == null) {
                Assert.assertFalse(true);
            } else if (expectValue) {
                Assert.assertTrue(model.getEventObjects().contains("bird"));
            } else {
                Assert.assertFalse(model.getEventObjects().contains("bird"));
            }
            return 1;
        });
        {
            expectAiAnalyzeSwitch.set(null);
            expectAiNotifySwitch.set(null);
            Result result = birdLoversService.updateBirdAiSetting(userId, sn, new JSONObject());
            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals(null, result.getData().getInteger("aiAnalyzeSwitchUpdate"));
//            Assert.assertEquals(null, result.getData().getInteger("aiNotifySwitchUpdate"));
        }
        {
            expectAiAnalyzeSwitch.set(true);
            expectAiNotifySwitch.set(null);
            Result result = birdLoversService.updateBirdAiSetting(userId, sn, new JSONObject().fluentPut("aiAnalyzeSwitch", true));
            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals((Integer) 1, result.getData().getInteger("aiAnalyzeSwitchUpdate"));
//            Assert.assertEquals(null, result.getData().getInteger("aiNotifySwitchUpdate"));
        }
        {
            expectAiAnalyzeSwitch.set(false);
            expectAiNotifySwitch.set(null);
            Result result = birdLoversService.updateBirdAiSetting(userId, sn, new JSONObject().fluentPut("aiAnalyzeSwitch", false));
            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals((Integer) 1, result.getData().getInteger("aiAnalyzeSwitchUpdate"));
//            Assert.assertEquals(null, result.getData().getInteger("aiNotifySwitchUpdate"));
        }
        {
            expectAiAnalyzeSwitch.set(null);
            expectAiNotifySwitch.set(true);
            MessageNotificationSetting setting = MessageNotificationSetting.builder().serialNumber(sn).userId(userId)
                    .eventObjects(AiObjectEnum.PERSON.getObjectName()).packageEventType("").build();
            when(messageNotificationSettingsService.queryMessageNotificationSetting(sn, userId)).thenReturn(setting);
            Result result = birdLoversService.updateBirdAiSetting(userId, sn, new JSONObject().fluentPut("aiNotifySwitch", true));
            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals(null, result.getData().getInteger("aiAnalyzeSwitchUpdate"));
//            Assert.assertEquals((Integer) 1, result.getData().getInteger("aiNotifySwitchUpdate"));
        }
        {
            expectAiAnalyzeSwitch.set(null);
            expectAiNotifySwitch.set(false);
            MessageNotificationSetting setting = MessageNotificationSetting.builder().serialNumber(sn).userId(userId)
                    .eventObjects(AiObjectEnum.PERSON.getObjectName() + "," + AiObjectEnum.BIRD.getObjectName()).packageEventType("").build();
            when(messageNotificationSettingsService.queryMessageNotificationSetting(sn, userId)).thenReturn(setting);
            Result result = birdLoversService.updateBirdAiSetting(userId, sn, new JSONObject().fluentPut("aiNotifySwitch", false));
            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals(null, result.getData().getInteger("aiAnalyzeSwitchUpdate"));
//            Assert.assertEquals((Integer) 1, result.getData().getInteger("aiNotifySwitchUpdate"));
        }
        {
            expectAiAnalyzeSwitch.set(null);
            expectAiNotifySwitch.set(null);
            when(messageNotificationSettingsService.queryMessageNotificationSetting(sn, userId)).thenReturn(null);
            MessageNotificationSetting setting = MessageNotificationSetting.builder().serialNumber(sn).userId(userId)
                    .eventObjects(AiObjectEnum.PERSON.getObjectName() + "," + AiObjectEnum.BIRD.getObjectName()).packageEventType("").build();
            when(messageNotificationSettingsService.buildMessageNotificationSetting(userId, sn)).thenReturn(setting);
            Result result = birdLoversService.updateBirdAiSetting(userId, sn, new JSONObject().fluentPut("aiNotifySwitch", true));
            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals(null, result.getData().getInteger("aiAnalyzeSwitchUpdate"));
//            Assert.assertEquals(null, result.getData().getInteger("aiNotifySwitchUpdate"));
        }

    }

//    @Test
//    public void test_queryVideoPossibleSubcategory() {
//        String traceId = OpenApiUtil.shortUUID();
//        Integer userId = 357043457;
//        {
//            when(libraryStatusService.selectLibraryStatusByTraceIdAndUserId(traceId, userId)).thenReturn(null);
//            Result<List<PossibleSubcategoryVO>> result = birdLoversService.queryVideoPossibleSubcategory(userId, traceId, "zh");
//            Assert.assertEquals((Integer) ResultCollection.NO_LIBRARY_ACCESS.getCode(), result.getResult());
//        }
//        when(libraryStatusService.selectLibraryStatusByTraceIdAndUserId(traceId, userId)).thenReturn(new LibraryStatusTb());
//        {
//            DeviceLibraryViewDO view = new DeviceLibraryViewDO();
//            view.setEventInfo(null);
//            when(libraryService.selectLibraryViewByTraceId(userId, traceId)).thenReturn(view);
//            Result<List<PossibleSubcategoryVO>> result = birdLoversService.queryVideoPossibleSubcategory(userId, traceId, "zh");
//            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals(0, result.getData().size());
//        }
//        {
//            DeviceLibraryViewDO view = new DeviceLibraryViewDO();
//            view.setEventInfo("");
//            when(libraryService.selectLibraryViewByTraceId(userId, traceId)).thenReturn(view);
//            Result<List<PossibleSubcategoryVO>> result = birdLoversService.queryVideoPossibleSubcategory(userId, traceId, "zh");
//            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals(0, result.getData().size());
//        }
//        {
//            DeviceLibraryViewDO view = new DeviceLibraryViewDO();
//            view.setEventInfo("[]");
//            when(libraryService.selectLibraryViewByTraceId(userId, traceId)).thenReturn(view);
//            Result<List<PossibleSubcategoryVO>> result = birdLoversService.queryVideoPossibleSubcategory(userId, traceId, "zh");
//            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals(0, result.getData().size());
//        }
//        Event event1 = new Event().setEventObject(EventObject.PERSON).setEventType(EventType.EXIST);
//        Event event2 = new Event().setEventObject(EventObject.BIRD).setEventType(EventType.EXIST)
//                .setPossibleSubcategory(Arrays.asList(
//                        new PossibleSubcategory().setName("Rissa tridactyla").setConfidence(new BigDecimal("0.92")), // 能找到
//                        new PossibleSubcategory().setName("Zenaida macroura").setConfidence(new BigDecimal("0.91")), // 能找到
//                        new PossibleSubcategory().setName("Melopyrrha grandis").setConfidence(new BigDecimal("0.87")), // 在语言es中找不到
//                        new PossibleSubcategory().setName("Progne none").setConfidence(new BigDecimal("0.34")), // 学名不存在
//                        new PossibleSubcategory().setName("Progne subis").setConfidence(new BigDecimal("0.87")) // 能找到
//
//                ));
//        DeviceLibraryViewDO view = new DeviceLibraryViewDO();
//        view.setEventInfo(JSON.toJSONString(Arrays.asList(event1, event2)));
//        when(libraryService.selectLibraryViewByTraceId(userId, traceId)).thenReturn(view);
//        {
//            Result<List<PossibleSubcategoryVO>> result = birdLoversService.queryVideoPossibleSubcategory(userId, traceId, "es");
//            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals(3, result.getData().size());
//            Assert.assertEquals("Gaviota tridáctila", result.getData().get(0).getMatchName());
//            Assert.assertEquals("Zenaida huilota", result.getData().get(1).getMatchName());
//            Assert.assertEquals("Golondrina purpúrea", result.getData().get(2).getMatchName());
//        }
//    }

}
