package com.addx.iotcamera.service;


import com.addx.iotcamera.service.device.DeviceCallService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceCallServiceTest {

    @InjectMocks
    @Spy
    private DeviceCallService deviceCallService;

    @Mock
    private NotificationService notificationService;

    @Mock
    private RedisService redisService;

    @Mock
    private LibraryService libraryService;
    @Mock
    private LibraryUpdateReceiveAllService libraryUpdateReceiveAllService;
    @Mock
    private VideoService videoService;
    @Mock
    private DeviceSettingService deviceSettingService;

    @Before
    public void beforeTest() throws NoSuchFieldException, IllegalAccessException {
        Field gsonField = NotificationService.class.getDeclaredField("gson");
        gsonField.setAccessible(true);
        gsonField.set(notificationService, new Gson());

        doNothing().when(libraryUpdateReceiveAllService).updateReceiveAllSlice(any());
        when(videoService.updateVideoReportEvents(anyString(),anyString(),any())).thenReturn(null);
    }

    @Test
    public void testCall_noLibrary() {
        // when(libraryService.getLibraryTrace(anyString())).thenReturn(null);

        when(redisService.hashGetString(anyString(), anyString())).thenReturn(null);
        deviceCallService.call("trace_01", "sn_01");

        when(redisService.hashGetString(anyString(), anyString())).thenReturn("[{\"event\":20}]");
        deviceCallService.call("trace_01", "sn_01");
    }

    @Test
    public void testCall_hasLibrary() {
        // when(libraryService.getLibraryTrace(anyString())).thenReturn(new LibraryTraceDO());

        when(redisService.hashGetString(anyString(), anyString())).thenReturn(null);
        deviceCallService.call("trace_01", "sn_01");

        when(redisService.hashGetString(anyString(), anyString())).thenReturn("[{\"event\":20}]");
        deviceCallService.call("trace_01", "sn_01");
    }

}
