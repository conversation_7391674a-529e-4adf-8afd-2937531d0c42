package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.device.DeviceBackendConfigDO;
import com.addx.iotcamera.dao.device.DeviceBackendConfigDAO;
import com.addx.iotcamera.service.device.DeviceBackendConfigService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceBackendConfigServiceTest {

    @InjectMocks
    private DeviceBackendConfigService deviceBackendConfigService;
    @Mock
    private DeviceBackendConfigDAO deviceBackendConfigDAO;

    @Test
    public void test_deviceBackendConfigService_queryBySn() {
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceBackendConfigDAO.queryBySn(sn)).thenReturn(null);
            Assert.assertEquals(new DeviceBackendConfigDO().setSn(sn), deviceBackendConfigService.queryBySn(sn));
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceBackendConfigDAO.queryBySn(sn)).thenThrow(new RuntimeException("mock"));
            Assert.assertEquals(new DeviceBackendConfigDO().setSn(sn), deviceBackendConfigService.queryBySn(sn));
        }
        {
            String sn = OpenApiUtil.shortUUID();
            DeviceBackendConfigDO model = new DeviceBackendConfigDO().setSn(sn).setLogUpload(true);
            when(deviceBackendConfigDAO.queryBySn(sn)).thenReturn(model);
            Assert.assertEquals(model, deviceBackendConfigService.queryBySn(sn));
            Assert.assertEquals(model, JSON.parseObject(model.toString(), DeviceBackendConfigDO.class));
        }
    }

    @Test
    public void test_deviceBackendConfigService_save() {
        {
            DeviceBackendConfigDO model = null;
            when(deviceBackendConfigDAO.save(model)).thenReturn(0);
            Assert.assertEquals(0, deviceBackendConfigService.save(model));
        }
        {
            DeviceBackendConfigDO model = new DeviceBackendConfigDO().setSn(null).setLogUpload(true);
            when(deviceBackendConfigDAO.save(model)).thenReturn(0);
            Assert.assertEquals(0, deviceBackendConfigService.save(model));
        }
        {
            String sn = OpenApiUtil.shortUUID();
            DeviceBackendConfigDO model = new DeviceBackendConfigDO().setSn(sn).setLogUpload(true);
            when(deviceBackendConfigDAO.save(model)).thenReturn(1);
            Assert.assertEquals(1, deviceBackendConfigService.save(model));
        }
    }

    @Test
    public void test_DeviceBackendConfigDAO() {
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        DeviceBackendConfigDAO dao = testHelper.getMapper(DeviceBackendConfigDAO.class);
        {
            String sn = "test_" + OpenApiUtil.shortUUID();
            DeviceBackendConfigDO model1 = new DeviceBackendConfigDO().setSn(sn);
            Assert.assertEquals(1, dao.save(model1));
            DeviceBackendConfigDO model2 = dao.queryBySn(sn);
            DeviceBackendConfigDO model3 = new DeviceBackendConfigDO().setSn(sn)
                    .setLogUpload(false);
            Assert.assertEquals(model3, model2);

            model1.setLogUpload(true);
            Assert.assertEquals(2, dao.save(model1));
            DeviceBackendConfigDO model4 = dao.queryBySn(sn);
            DeviceBackendConfigDO model5 = new DeviceBackendConfigDO().setSn(sn)
                    .setLogUpload(true);
            Assert.assertEquals(model5, model4);
        }
        {
            String sn = "test_" + OpenApiUtil.shortUUID();
            DeviceBackendConfigDO model1 = new DeviceBackendConfigDO().setSn(sn)
                    .setLogUpload(true);
            int insertNum = dao.save(model1);
            Assert.assertEquals(1, insertNum);
            DeviceBackendConfigDO model2 = dao.queryBySn(sn);
            DeviceBackendConfigDO model3 = new DeviceBackendConfigDO().setSn(sn)
                    .setLogUpload(true);
            Assert.assertEquals(model3, model2);

//            Assert.assertEquals(2, dao.save(model1));
//            DeviceBackendConfigDO model4 = dao.queryBySn(sn);
//            DeviceBackendConfigDO model5 = new DeviceBackendConfigDO().setSn(sn)
//                    .setLogUpload(true);
//            Assert.assertEquals(model5, model4);
        }
        {
            String sn = "test_" + OpenApiUtil.shortUUID();
            DeviceBackendConfigDO model1 = new DeviceBackendConfigDO().setSn(sn)
                    ;
            int insertNum = dao.save(model1);
            Assert.assertEquals(1, insertNum);
            DeviceBackendConfigDO model2 = dao.queryBySn(sn);
            DeviceBackendConfigDO model3 = new DeviceBackendConfigDO().setSn(sn)
                    ;
            Assert.assertEquals(model3, model2);

            model1.setLogUpload(true);
            Assert.assertEquals(2, dao.save(model1));
            DeviceBackendConfigDO model4 = dao.queryBySn(sn);
            DeviceBackendConfigDO model5 = new DeviceBackendConfigDO().setSn(sn)
                    .setLogUpload(true);
            Assert.assertEquals(model5, model4);
        }
    }

}
