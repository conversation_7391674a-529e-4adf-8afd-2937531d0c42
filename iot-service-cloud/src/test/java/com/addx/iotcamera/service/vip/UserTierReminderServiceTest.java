package com.addx.iotcamera.service.vip;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.db.UserTierDeviceDO;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.Device4GSimDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.domain.library.TierFreeUserExpireDO;
import com.addx.iotcamera.config.TierReminderConfig;
import com.addx.iotcamera.config.template.GrayscaleTemplateConfig;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.template.UserTierReminderService;
import com.addx.iotcamera.service.user.UserSettingService;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.PreDestroy;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class UserTierReminderServiceTest {
    @InjectMocks
    private UserTierReminderService userTierReminderService;

    @Mock
    private UserVipService userVipService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private TierReminderConfig tierReminderConfig;

    @Mock
    private GrayscaleTemplateConfig grayscaleTemplateConfig;

    @Mock
    private TierService tierService;
    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private UserTierDeviceService userTierDeviceService;

    @Mock
    private IDeviceDAO deviceDAO;

    @Mock
    private UserSettingService userSettingService;

    @Mock
    private VipService vipService;
    @Mock
    private Device4GService device4GService;

    @PreDestroy
    public void init() {
        when(device4GService.queryDevice4GSimDO(any())).thenAnswer(it -> new Device4GSimDO());
        when(userRoleService.getUserSerialNumberByUserId(any())).thenAnswer(it -> new ArrayList<>());
    }

    @Test
    @DisplayName("校验弹出条件是否满足-不弹出-已领取套餐")
    public void testEligible_withReceivedVip_shouldReturnFalseNotify() {
        // Arrange
        Integer userId = 1;
        AppRequestBase request = new AppRequestBase();
        request.setApp(new AppInfo());
        request.getApp().setTenantId("tenant1");
        when(userVipService.isTierReceived(userId, "tenant1")).thenReturn(true);
        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setSerialNumber("device1");
        bindOperationTb.setOperationId("1001"); // 需要一个 operationId
        when(deviceDAO.queryBindHistoryCompleted(userId)).thenReturn(bindOperationTb);
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());

        // Act
        TierFreeUserExpireDO result = userTierReminderService.eligible(userId, request);

        // Assert
        assertFalse(result.isNotify());
    }

    @Test
    @DisplayName("校验领取前提条件 - 未绑定设备")
    public void testEligible_withUnreceivedVipAndNoDeviceBound_shouldReturnFalseNotify() {
        // Arrange
        Integer userId = 1;
        AppRequestBase request = new AppRequestBase();
        request.setApp(new AppInfo());
        request.getApp().setTenantId("tenant1");
        when(userVipService.isTierReceived(userId, "tenant1")).thenReturn(false);
        when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(new ArrayList<>());
        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setSerialNumber("device1");
        bindOperationTb.setOperationId("1001"); // 需要一个 operationId
        when(deviceDAO.queryBindHistoryCompleted(userId)).thenReturn(bindOperationTb);
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());

        // Act
        TierFreeUserExpireDO result = userTierReminderService.eligible(userId, request);

        // Assert
        assertFalse(result.isNotify());
    }

    @Test
    public void testEligible_withUnreceivedVipAndDeviceBound_shouldReturnTrueNotify() {
        // Arrange
        Integer userId = 1;
        AppRequestBase request = new AppRequestBase();
        request.setApp(new AppInfo());
        request.getApp().setTenantId("tenant1");
        when(userVipService.isTierReceived(userId, "tenant1")).thenReturn(false);
        when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(Arrays.asList("serial1", "serial2"));
        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setSerialNumber("device1");
        bindOperationTb.setOperationId("1001"); // 需要一个 operationId
        when(deviceDAO.queryBindHistoryCompleted(userId)).thenReturn(bindOperationTb);
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
        when(vipService.isVipDevice(userId, "serial1")).thenReturn(true);
        when(vipService.isVipDevice(userId, "serial2")).thenReturn(true);
        // Act
        TierFreeUserExpireDO result = userTierReminderService.eligible(userId, request);

        // Assert
        assertTrue(result.isNotify());
    }


    @Test
    public void testInitNotifyConfig_SatisfyRule() {
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(new HashSet<>());
        when(grayscaleTemplateConfig.queryGrayscaleTemplate(any())).thenReturn(null);

        Map<Integer, Integer> expectedConfig = tierReminderConfig.getConfigV1();
        Map<Integer, Integer> actualConfig = userTierReminderService.initNotifyConfig(userId);
        assertEquals(expectedConfig, actualConfig);
    }

    @Test
    public void testInitNotifyConfig_NotSatisfyRule() {
        Integer userId = 5;
        when(grayscaleTemplateConfig.queryWhiteList(any())).thenReturn(new HashSet<>());

        GrayscaleTemplateConfig.GrayscaleTemplate grayscaleTemplate = new GrayscaleTemplateConfig.GrayscaleTemplate();
        grayscaleTemplate.setGrayscaleTotal(10);
        grayscaleTemplate.setGrayscaleScale(1);
        when(grayscaleTemplateConfig.queryGrayscaleTemplate(any())).thenReturn(grayscaleTemplate);

        Map<Integer, Integer> expectedConfig = tierReminderConfig.getConfig();
        Map<Integer, Integer> actualConfig = userTierReminderService.initNotifyConfig(userId);
        assertEquals(expectedConfig, actualConfig);
    }

    @Test
    public void testEligible_with4GDeviceNotInTierList() {
        // Arrange
        Integer userId = 1;
        AppRequestBase request = new AppRequestBase();
        request.setApp(new AppInfo());
        request.getApp().setTenantId("tenant1");

        when(userTierDeviceService.queryUserTierDeviceDOListByUser(userId, "tenant1")).thenReturn(Arrays.asList(
                UserTierDeviceDO.builder()
                        .userId(userId)
                        .tierId(10)
                        .tierUid("tierUid1")
                        .serialNumber("serial2")
                        .tenantId("tenant1")
                        .build()
        ));
        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setSerialNumber("device1");
        bindOperationTb.setOperationId("1001"); // 需要一个 operationId
        when(deviceDAO.queryBindHistoryCompleted(userId)).thenReturn(bindOperationTb);
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());

        // Act
        TierFreeUserExpireDO result = userTierReminderService.eligible(userId, request);

        // Assert
        assertTrue(!result.isNotify());
        assertEquals(Integer.valueOf(0), result.getRecommendProductType());
    }

    @Test
    public void testEligibleWith4GDevicesAndBoundToTier() {
        Integer userId = 1;
        AppInfo appInfo = AppInfo.builder()
                .tenantId("tenantId1")
                .version(1)
                .appName("TestApp")
                .channelId("testChannel")
                .appBuild("1.0.0")
                .bundle("com.test.bundle")
                .appType("IOT")
                .versionName("1.0.0")
                .env("production")
                .build();
        AppRequestBase request = new AppRequestBase();
        request.setApp(appInfo);

        Set<Integer> tierId4GSet = new HashSet<>(Arrays.asList(10));

        when(userRoleService.getUserRoleByUserId(userId, UserRoleEnums.ADMIN.getCode()))
                .thenReturn(Arrays.asList(new UserRoleDO(userId, "device1", "admin", 1, 0), new UserRoleDO(userId, "device2", "admin", 1, 0)));
        when(deviceInfoService.checkIfDeviceUse4G("device1")).thenReturn(true);
        when(deviceInfoService.checkIfDeviceUse4G("device2")).thenReturn(true);
        when(tierService.getTierIdByTierServiceType(appInfo.getTenantId(), TierServiceTypeEnums.TIER_4G.getCode()))
                .thenReturn(tierId4GSet);
        when(userTierDeviceService.queryUserTierDeviceDOListByUser(userId, appInfo.getTenantId()))
                .thenReturn(Arrays.asList(new UserTierDeviceDO(1L, userId, 10, "uid1", "device1", appInfo.getTenantId()), new UserTierDeviceDO(2L, userId, 10, "uid2", "device2", appInfo.getTenantId())));

        TierFreeUserExpireDO expectResult = TierFreeUserExpireDO.builder()
                .notify(false)
                .hasDevice4G(true)
                .operationId("1001")
                .recommendProductType(1)
                .build();
        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setSerialNumber("device1");
        bindOperationTb.setOperationId("1001"); // 需要一个 operationId
        when(deviceDAO.queryBindHistoryCompleted(userId)).thenReturn(bindOperationTb);
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());

        TierFreeUserExpireDO actualResult = userTierReminderService.eligible(userId, request);

        assertEquals(expectResult, actualResult);
    }
}
