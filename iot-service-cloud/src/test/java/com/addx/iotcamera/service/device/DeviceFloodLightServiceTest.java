package com.addx.iotcamera.service.device;

import com.addx.iotcamera.controller.device.response.MqttResponseController;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.publishers.vernemq.MqttReceivePackage;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.device.model.DeviceFloodLightMsgModel;
import com.addx.iotcamera.util.JsonUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.config.model.DeviceAttributeIntRange;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.collections.MapUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceFloodLightServiceTest {

    @InjectMocks
    private DeviceFloodLightService deviceFloodLightService;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private RedisService redisService;

    @Mock
    private MqttResponseController controller;

    @Mock
    private MqttSender mqttSender;


    @Test
    public void test_handleFloodlightMsg() throws Exception {
        MqttReceivePackage mqttReceivePackage = new MqttReceivePackage();

        try {

            // case 1
            deviceFloodLightService.handleFloodlightMsg(mqttReceivePackage);
        } catch (Exception e) {

        }

        JSONObject jsonObject = new JSONObject();
        mqttReceivePackage.setSerialNumber("1");
        mqttReceivePackage.setValue(jsonObject);
        mqttReceivePackage.setTime(1);
        // case 2
        try {
            deviceFloodLightService.handleFloodlightMsg(mqttReceivePackage);
        } catch (Exception e) {

        }

        CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(null);

        try {
            deviceFloodLightService.handleFloodlightMsg(mqttReceivePackage);
        } catch (Exception e) {

        }
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(cloudDeviceSupport);
        DeviceAttributeIntRange deviceAttributeIntRange = new DeviceAttributeIntRange();
        deviceAttributeIntRange.setInterval(1);
        deviceAttributeIntRange.setMax(100);
        deviceAttributeIntRange.setMin(1);
        cloudDeviceSupport.setFloodlightLuminanceRange(deviceAttributeIntRange);
        try {
            deviceFloodLightService.handleFloodlightMsg(mqttReceivePackage);
        } catch (Exception e) {
        }

        jsonObject.put("luminance", 2);
        deviceFloodLightService.handleFloodlightMsg(mqttReceivePackage);

    }

    @Test
    public void test_updateFloodlightSwitch() {
        String sn = "";
        boolean switchOn = true;
        deviceFloodLightService.updateFloodlightSwitch(sn, switchOn);
//        assertArrayEquals(result, Result.Failure("serialNumber is null"));
        deviceFloodLightService.updateFloodlightLuminance(sn, 1);

        sn = "1";
        deviceFloodLightService.updateFloodlightSwitch(sn, switchOn);
        deviceFloodLightService.updateFloodlightLuminance(sn, 1);

    }

    @Test
    public void test_getFloodLightMsg() {
        DeviceFloodLightMsgModel floodLightMsg = deviceFloodLightService.getFloodLightMsg("");
        assertNull(floodLightMsg);

        when(redisService.get(any())).thenReturn("");
        floodLightMsg = deviceFloodLightService.getFloodLightMsg("1");
        assertNull(floodLightMsg);

        when(redisService.get(any())).thenReturn("1");
        floodLightMsg = deviceFloodLightService.getFloodLightMsg("1");
        assertNull(floodLightMsg);


        when(redisService.get(any())).thenReturn(JsonUtil.toJson(new DeviceFloodLightMsgModel()));
        floodLightMsg = deviceFloodLightService.getFloodLightMsg("1");
        assertNotNull(floodLightMsg);

    }

    @Test
    public void test_getFloodLightMsgMap() {
        Map<String, DeviceFloodLightMsgModel> floodLightMsgMap = deviceFloodLightService.getFloodLightMsgMap(null);
        assertEquals(MapUtils.EMPTY_MAP, floodLightMsgMap);
        deviceFloodLightService.getFloodLightMsgMap(Arrays.asList("1"));
        deviceFloodLightService.getFloodLightMsgMap(Arrays.asList());
    }



}
