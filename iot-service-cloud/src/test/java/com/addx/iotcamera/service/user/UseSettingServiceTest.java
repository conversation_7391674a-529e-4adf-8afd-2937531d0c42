package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.dao.user.IUserSettingsDAO;
import com.addx.iotcamera.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class UseSettingServiceTest {
    @InjectMocks
    private UserSettingService userSettingService;

    @Mock
    private RedisService redisService;

    @Mock
    private IUserSettingsDAO iUserSettingsDAO;

    @Test
    public void queryUserSetting_ShouldReturnUserSettings() {
        // 准备测试数据
        Integer userId = 1;
        UserSettingsDO expectedSettings = new UserSettingsDO();
        expectedSettings.setUserId(userId);

        when(iUserSettingsDAO.queryUserSettings(userId)).thenReturn(expectedSettings);

        // 执行测试
        UserSettingsDO result = userSettingService.queryUserSetting(userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(userId, result.getUserId());
        verify(iUserSettingsDAO).queryUserSettings(userId);
    }

    @Test
    public void updateUserSetting_ShouldReturnTrue_WhenUpdateSuccessful() {
        // 准备测试数据
        UserSettingsDO settings = new UserSettingsDO();
        settings.setUserId(1);

        when(iUserSettingsDAO.updateUserSetting(settings)).thenReturn(1);

        // 执行测试
        boolean result = userSettingService.updateUserSetting(settings);

        // 验证结果
        assertTrue(result);
        verify(iUserSettingsDAO).updateUserSetting(settings);
    }

    @Test
    public void queryUserMark_ShouldThrowException_WhenInvalidKey() {
        // 准备测试数据
        Integer userId = 1;
        String invalidKey = "INVALID_KEY";

        // 执行测试并验证异常
        assertThrows(BaseException.class, () ->
                userSettingService.queryUserMark(userId, invalidKey)
        );
    }


    @Test
    public void buildTableNameFromEnv_ShouldReturnCorrectTableName() {
        // 测试不同环境下的表名生成
        assertEquals("test_user", userSettingService.buildTableNameFromEnv("prod"));
        assertEquals("test_user_staging", userSettingService.buildTableNameFromEnv("staging"));
        assertEquals("test_user_pre", userSettingService.buildTableNameFromEnv("pre"));
    }

    @Test
    public void buildStationValueFromEnv_ShouldReturnCorrectStation() {
        // 测试不同环境下的站点值生成
        assertEquals("cn", userSettingService.buildStationValueFromEnv("prod"));
        assertEquals("us", userSettingService.buildStationValueFromEnv("prod-us"));
        assertEquals("eu", userSettingService.buildStationValueFromEnv("staging-eu"));
    }
}
