package com.addx.iotcamera.service.pay;

import com.addx.iotcamera.bean.app.vip.TierInfo;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.db.TierGroupDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.uservip.TierGroupVipInfo;
import com.addx.iotcamera.bean.domain.uservip.UserTierExpire;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.config.app.AppColorConfig;
import com.addx.iotcamera.config.app.TierInfoConfig;
import com.addx.iotcamera.config.device.FreeUserVipTier2Config;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.enums.OrderSubTypeEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.service.vip.TierGroupService;
import com.addx.iotcamera.service.vip.TierService;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.time.Instant;
import java.util.*;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class UserVipServiceTest {

    @InjectMocks
    private UserVipService userVipService;
    @Mock
    private IUserVipDAO iUserVipDAO;
    @Mock
    private TierService tierService;
    @Mock
    private RedisService redisService;
    @Mock
    private CenterNotifyConfig centerNotifyConfig;
    @Mock
    private TierInfoConfig tierInfoConfig;
    @Mock
    private IOrderDAO iOrderDAO;
    @Mock
    private AppColorConfig appColorConfig;
    @Mock
    private UserTierDeviceService userTierDeviceService;

    @Mock
    private TierGroupService tierGroupService;

    @Mock
    private OrderService orderService;

    @Mock
    private UserService userService;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Before
    public void init() {
        when(centerNotifyConfig.getMessage()).thenReturn(Collections.singletonMap("vicoco", Collections.singletonMap("en", new HashMap<String, String>(){{
            put("freeMessage", "free");
            put("noUserTierMessage", "noUserTier");
            put("expirationReminderPopupMessage", "aaa");
            put("changeRemindePopupMessage", "aaa");
            put("protectionMessageToday", "aaaa");
            put("protectionMessage", "aaaa");
        }})));
        when(userTierDeviceService.hasActiveFreeLicenceDeviceVip(any(),any())).thenReturn(true);
    }

    /**
     * 用户没有vip时视频固定保存7天
     */
    @Test
    public void test_queryCurrentTierTime_noVip() {
        when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenReturn(Collections.EMPTY_LIST);
        when(iUserVipDAO.queryUserVipInfoBefor(any(), any())).thenReturn(null);

        Calendar cal = Calendar.getInstance();
        long curTime = cal.getTimeInMillis();
        cal.add(Calendar.DATE, -7);
        int expTierTime = getMaxInsertTime(cal);
        // 用户下保存时间小于tierTime的视频，需要删除
        int tierTime = userVipService.queryCurrentTierTime(0, curTime);
        Assert.assertEquals(expTierTime, tierTime);
    }

    // 计算用户视频的最大保存时间
    private static int getMaxInsertTime(Calendar cal) {
        cal.set(Calendar.HOUR, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        int expTierTime = (int) (cal.getTimeInMillis() / 1000);
        return expTierTime;
    }

    /**
     * 用户当前有有效vip且没有过期vip时，保存当前vip等级所设置的天数
     */
    @Test
    public void test_queryCurrentTierTime_haveValidVip() {
        int rollingDays = new Random().nextInt(50) + 10;

        Calendar cal = Calendar.getInstance();
        long curTime = cal.getTimeInMillis();
        cal.add(Calendar.DATE, -rollingDays);
        int expTierTime = getMaxInsertTime(cal);

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setEndTime((int) (curTime / 1000) + 1);
        userVipDO.setTierId(0);

        Tier tier = new Tier();
        tier.setTierId(2);
        tier.setRollingDays(rollingDays);

        when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenReturn(Arrays.asList(userVipDO));
        when(iUserVipDAO.queryUserVipInfoBefor(any(), any())).thenReturn(null);
        when(tierService.queryTierById(anyInt())).thenReturn(tier);

        // 用户下保存时间小于tierTime的视频，需要删除
        int tierTime = userVipService.queryCurrentTierTime(0, curTime);
        Assert.assertEquals(expTierTime, tierTime);
    }

    private static int getExpiratione() {
        try {
            Field field = UserVipService.class.getDeclaredField("expiratione");
            field.setAccessible(true);
            return field.getInt(UserVipService.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 用户当前只有过期vip时，如果最大的过期vip结束时间还在保护期内，
     */
    @Test
    public void test_queryCurrentTierTime_haveUnValidVip_gtExpiration() {
        int rollingDays = new Random().nextInt(50) + 10;
        // 过期且已过保护保护期
        test_queryCurrentTierTime_haveUnValidVip(-(getExpiratione() + 1), rollingDays, 7);
    }

    /**
     * @param diffDays       vip与当前时间相差的天数
     * @param rollingDays    vip天数
     * @param expRollingDays 期望计算出来的vip天数
     */
    public void test_queryCurrentTierTime_haveUnValidVip(int diffDays, int rollingDays, int expRollingDays) {
        Calendar cal = Calendar.getInstance();
        long curTime = cal.getTimeInMillis();
        cal.add(Calendar.DATE, -expRollingDays);
        int expTierTime = getMaxInsertTime(cal);

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setEndTime((int) (curTime / 1000) + diffDays);
        userVipDO.setTierId(0);

        Tier tier = new Tier();
        tier.setRollingDays(rollingDays);

        when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenReturn(Collections.EMPTY_LIST);
        when(iUserVipDAO.queryUserVipInfoBefor(any(), any())).thenReturn(userVipDO);
        when(tierService.queryTierById(anyInt())).thenReturn(tier);

        // 用户下保存时间小于tierTime的视频，需要删除
        int tierTime = userVipService.queryCurrentTierTime(0, curTime);
        Assert.assertEquals(expTierTime, tierTime);
    }

    /**
     * 高级vip变低级vip,且在保护期
     */
    @Test
    public void test_queryCurrentTierTime_tier_low2heigh_ltExpiration() {
        int rollingDays = new Random().nextInt(50) + 10;
        int oldRollingDays = rollingDays - 5;
        test_queryCurrentTierTime_tier_change(-(getExpiratione() - 1), oldRollingDays, rollingDays, rollingDays);
    }

    /**
     * 高级vip变低级vip,且不在保护期
     */
    @Test
    public void test_queryCurrentTierTime_tier_high2low_gtExpiration() {
        int rollingDays = new Random().nextInt(50) + 10;
        int oldRollingDays = rollingDays + 5;
        test_queryCurrentTierTime_tier_change(-(getExpiratione() + 1), oldRollingDays, rollingDays, rollingDays);
    }

    /**
     * 高级vip变低级vip,且不在保护期
     */
    @Test
    public void test_queryCurrentTierTime_tier_low2high_gtExpiration() {
        int rollingDays = new Random().nextInt(50) + 10;
        int oldRollingDays = rollingDays - 5;
        test_queryCurrentTierTime_tier_change(-(getExpiratione() + 1), oldRollingDays, rollingDays, rollingDays);
    }

    /**
     * @param diffDays       vip与当前时间相差的天数
     * @param oldRollingDays 最后一个已过期的vip的天数
     * @param rollingDays    vip天数
     * @param expRollingDays 期望计算出来的vip天数
     */
    public void test_queryCurrentTierTime_tier_change(int diffDays, int oldRollingDays, int rollingDays, int expRollingDays) {
        Calendar cal = Calendar.getInstance();
        long curTime = cal.getTimeInMillis();
        cal.add(Calendar.DATE, -expRollingDays);
        int expTierTime = getMaxInsertTime(cal);

        UserVipDO oldUserVipDO = new UserVipDO();
        oldUserVipDO.setEndTime((int) (curTime / 1000) + diffDays);
        oldUserVipDO.setTierId(2);

        Tier oldTier = new Tier();
        oldTier.setTierId(2);
        oldTier.setRollingDays(oldRollingDays);

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setEndTime((int) (curTime / 1000) + 10);
        userVipDO.setTierId(1);

        Tier tier = new Tier();
        tier.setTierId(1);
        tier.setRollingDays(rollingDays);

        when(iUserVipDAO.queryUserVipInfoBefor(any(), any())).thenReturn(oldUserVipDO);
        when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenReturn(Arrays.asList(userVipDO));
        when(tierService.queryTierById(oldUserVipDO.getTierId())).thenReturn(oldTier);
        when(tierService.queryTierById(userVipDO.getTierId())).thenReturn(tier);

        // 用户下保存时间小于tierTime的视频，需要删除
        int tierTime = userVipService.queryCurrentTierTime(0, curTime);
        Assert.assertEquals(expTierTime, tierTime);
    }

    @Test
    public void test_queryUserVipInfo() throws Exception {
        Field openReceiveVipField = UserVipService.class.getDeclaredField("openReceiveVip");
        openReceiveVipField.setAccessible(true);
        openReceiveVipField.set(userVipService, true);

        List<UserVipDO> userVipDOList = Arrays.asList(
                UserVipDO.builder()
                        .tierId(1)
                        .effectiveTime((int)(System.currentTimeMillis()/1000) - 10)
                        .endTime((int)(System.currentTimeMillis()/1000) + 10)
                        .tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())
                        .build(),
                UserVipDO.builder()
                        .tierId(2)
                        .effectiveTime((int)(System.currentTimeMillis()/1000) - 100)
                        .endTime((int)(System.currentTimeMillis()/1000) - 99)
                        .tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())
                        .build()
        );
        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt(),any())).thenReturn(
                Arrays.asList(
                        UserVipDO.builder()
                                .tierId(1)
                                .effectiveTime((int)(System.currentTimeMillis()/1000) - 10)
                                .endTime((int)(System.currentTimeMillis()/1000) + 10)
                                .tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())

                                .build(),
                        UserVipDO.builder()
                                .tierId(2)
                                .effectiveTime((int)(System.currentTimeMillis()/1000) - 100)
                                .endTime((int)(System.currentTimeMillis()/1000) - 99)
                                .tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())

                                .build()
                )
        );
        when(tierInfoConfig.getTierInfos(anyString(), anyString())).thenReturn(Collections.singletonList(new TierInfo(){{
            setId(1);
            setName("vip1");
        }}));

        Tier tier1 = new Tier();
        tier1.setTierId(1);
        tier1.setTierGroupId(1);
        Tier tier2 = new Tier();
        tier2.setTierId(2);
        tier2.setTierGroupId(1);
        when(tierService.queryTierById(1)).thenReturn(tier1);
        when(tierService.queryTierById(2)).thenReturn(tier2);

        when(tierGroupService.sortUserVipDO(userVipDOList)).thenReturn(userVipDOList);
        when(orderService.verifyOrderSub(any())).thenReturn(false);

        when(deviceInfoService.hasServiceTypeDevice(any(),any())).thenReturn(false);

        UserVipTier userVipTier = userVipService.queryUserVipInfo(1, "en", "vicoco");
        Assert.assertTrue(userVipTier.isVip());
        Assert.assertTrue(userVipTier.getTierId().intValue() == 1);

        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt(),any())).thenReturn(Collections.emptyList());
        when(iUserVipDAO.queryLastUserVipInfo(anyInt(),any())).thenReturn(UserVipDO.builder().tierId(1).endTime((int)(System.currentTimeMillis()/1000)).orderId(0L).build());
        when(tierInfoConfig.getTierInfos(anyString(), anyString())).thenReturn(Collections.singletonList(new TierInfo(){{
            setId(1);
            setName("vip1");
        }}));
        when(iOrderDAO.queryByOrderId(anyLong())).thenReturn(OrderDO.builder().subType(OrderSubTypeEnums.SUBSCRIBE.getCode()).build());
        when(appColorConfig.queryAppColor(anyString())).thenReturn("blue");
        when(tierService.queryTierById(anyInt())).thenReturn(new Tier() {{setRollingDays(1);setSize(1024);setLevel(1);}});

        userVipTier = userVipService.queryUserVipInfo(1, "en", "vicoco");
        Assert.assertTrue(userVipTier.isVip());
        Assert.assertTrue(userVipTier.getTierId().intValue() == 1);

        when(userService.queryUserById(any())).thenReturn(new User(){{
            setRegistTime((int)(System.currentTimeMillis()/ 1000));
        }});
        FreeUserVipTier2Config freeUserVipTier2ConfigUnderTest = new FreeUserVipTier2Config();
        freeUserVipTier2ConfigUnderTest.afterPropertiesSet();

        freeUserVipTier2ConfigUnderTest.setRegisterConfigList(Arrays.asList(new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(100);
            setCapacityGB(0.5);
            setLookBackDay(3);
            setRegisterStart(0);
            setRegisterEnd(Integer.MAX_VALUE);
            setCooldownValue(Collections.singletonMap("cg1", "60"));
        }}));
        userVipTier = userVipService.queryUserVipInfo(1, "en", "vicoco");
        Assert.assertTrue(userVipTier.isVip());
        Assert.assertTrue(userVipTier.getTierId().intValue() == 1);
    }

    @Test
    public void test_queryUserVipInfo_all() throws Exception {
        String tenantId = "vicoco";
        Field openReceiveVipField = UserVipService.class.getDeclaredField("openReceiveVip");
        openReceiveVipField.setAccessible(true);
        openReceiveVipField.set(userVipService, true);

        List<UserVipDO> userVipDOList = Arrays.asList(
                UserVipDO.builder()
                        .tierId(1)
                        .effectiveTime((int)(System.currentTimeMillis()/1000) - 10)
                        .endTime((int)(System.currentTimeMillis()/1000) + 10)
                        .tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())
                        .build(),
                UserVipDO.builder()
                        .tierId(2)
                        .effectiveTime((int)(System.currentTimeMillis()/1000) - 100)
                        .endTime((int)(System.currentTimeMillis()/1000) - 99)
                        .tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())

                        .build()
        );
        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt(),any())).thenReturn(
                Arrays.asList(
                        UserVipDO.builder()
                                .tierId(1)
                                .effectiveTime((int)(System.currentTimeMillis()/1000) - 10)
                                .endTime((int)(System.currentTimeMillis()/1000) + 10)
                                .tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())

                                .build(),
                        UserVipDO.builder()
                                .tierId(2)
                                .effectiveTime((int)(System.currentTimeMillis()/1000) - 100)
                                .endTime((int)(System.currentTimeMillis()/1000) - 99)
                                .tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())

                                .build()
                )
        );
        when(tierInfoConfig.getTierInfos(anyString(), anyString())).thenReturn(Collections.singletonList(new TierInfo(){{
            setId(1);
            setName("vip1");
        }}));

        when(deviceInfoService.hasServiceTypeDevice(any(),any())).thenReturn(false);


        Tier tier1 = new Tier();
        tier1.setTierId(1);
        tier1.setTierGroupId(1);
        Tier tier2 = new Tier();
        tier2.setTierId(2);
        tier2.setTierGroupId(1);
        when(tierService.queryTierById(1)).thenReturn(tier1);
        when(tierService.queryTierById(2)).thenReturn(tier2);

        TierGroupDO tierGroupDO = new TierGroupDO();
        tierGroupDO.setId(1);
        tierGroupDO.setWeight(1);
        when(tierGroupService.getById(any(),any())).thenReturn(tierGroupDO);

        when(tierGroupService.sortUserVipDO(userVipDOList)).thenReturn(userVipDOList);
        when(orderService.verifyOrderSub(any())).thenReturn(false);
        UserVipTier userVipTier = userVipService.queryUserVipInfo(1, "en", "vicoco");
        Assert.assertTrue(userVipTier.isVip());
        Assert.assertTrue(userVipTier.getTierId().intValue() == 1);
    }


    @Test
    public void test_markNoUserTier() {
        userVipService.markNoUserTier(1);
        userVipService.unmarkNoUserTier(1);
        boolean isNoUserTier = userVipService.isNoUserTier(1);
        Assert.assertTrue(!isNoUserTier);
    }

    @Test
    public void initUserVipTierGroup_test(){
        List<UserVipDO> userVipDOList = Lists.newArrayList();
        UserVipDO userVip1 = UserVipDO.builder()
                .tierId(1)
                .build();
        userVipDOList.add(userVip1);
        UserVipDO userVip10 = UserVipDO.builder()
                .tierId(10)
                .build();
        userVipDOList.add(userVip10);

        Tier tier1 = new Tier();
        tier1.setTierId(1);
        tier1.setTierGroupId(1);
        when(tierService.queryTierById(1)).thenReturn(tier1);

        Tier tier10 = new Tier();
        tier10.setTierId(10);
        tier10.setTierGroupId(2);
        when(tierService.queryTierById(10)).thenReturn(tier10);

        List<UserVipDO> userVipDOList1 = Lists.newArrayList();
        userVipDOList1.add(userVip1);
        when(tierGroupService.sortUserVipDO(userVipDOList1)).thenReturn(userVipDOList1);
        List<UserVipDO> userVipDOList10 = Lists.newArrayList();
        userVipDOList10.add(userVip10);
        when(tierGroupService.sortUserVipDO(userVipDOList10)).thenReturn(userVipDOList10);

        Map<Integer,List<UserVipDO>> expectedResult = Maps.newHashMap();
        expectedResult.put(1,userVipDOList1);
        expectedResult.put(2,userVipDOList10);

        Map<Integer,List<UserVipDO>> actualResult = userVipService.initUserVipTierGroup(userVipDOList);

        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("过期提醒list - 不需要提醒")
    public void initUserTierExpire_noNeed_notify(){
        Integer currentTime = (int)Instant.now().getEpochSecond();

        Map<Integer,List<UserVipDO>> groupTierUserVipList = Maps.newHashMap();
        groupTierUserVipList.put(1,
                    Arrays.asList(UserVipDO.builder()
                                    .tierId(1)
                                    .endTime(currentTime + 15*24*60*60)
                    .build()
                )
        );

        List<UserTierExpire> expectedResult = Lists.newArrayList();
        List<UserTierExpire> actualResult = userVipService.initUserTierExpire(Arrays.asList(1),groupTierUserVipList,currentTime,1,"zh","vicoco",Lists.newArrayList());
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("初始化套餐组套餐信息-empty")
    public void initTierGroupVipInfo_test_empty(){
        Map<Integer, List<UserVipDO>> userVipDOGroupMap = Maps.newHashMap();
        TierGroupVipInfo expectedResult = TierGroupVipInfo.builder()
                .maxWeightTierGroupIdUserVipList(Lists.newArrayList())
                .tierIdList(Lists.newArrayList())
                .tierGroupIdSortedList(Lists.newArrayList())
                .build();
        TierGroupVipInfo actualResult = userVipService.initTierGroupVipInfo(userVipDOGroupMap);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("初始化套餐组套餐信息-empty")
    public void initTierGroupVipInfo_test(){
        Map<Integer, List<UserVipDO>> userVipDOGroupMap = Maps.newHashMap();
        userVipDOGroupMap.put(2,
                Arrays.asList(
                        UserVipDO.builder()
                                .tierId(10)
                                .build()
                )
        );
        userVipDOGroupMap.put(1,
                Arrays.asList(
                        UserVipDO.builder()
                                .tierId(1)
                                .build()
                )
        );

        TierGroupDO tierGroupDO1 = TierGroupDO.builder()
                .weight(2)
                .build();
        when(tierGroupService.getById(null,1)).thenReturn(tierGroupDO1);
        TierGroupDO tierGroupDO2 = TierGroupDO.builder()
                .weight(1)
                .build();
        when(tierGroupService.getById(null,2)).thenReturn(tierGroupDO2);

        TierGroupVipInfo expectedResult = TierGroupVipInfo.builder()
                .maxWeightTierGroupIdUserVipList(userVipDOGroupMap.get(1))
                .tierIdList(Arrays.asList(1,10))
                .tierGroupIdSortedList(Arrays.asList(1,2))
                .build();
        TierGroupVipInfo actualResult = userVipService.initTierGroupVipInfo(userVipDOGroupMap);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("free message")
    public void getNextTierName_freeMessage(){
        TierInfo tierInfo = null;
        Integer tierId = 1;
        Map<String, String> notifyMessageMap = Maps.newHashMap();
        notifyMessageMap.put("freeMessage","freeMessage");
        String expectResult = "freeMessage";
        String actualResult = userVipService.getNextTierName(tierInfo,tierId,notifyMessageMap);
        Assert.assertEquals(expectResult,actualResult);

        tierId = 0;
        expectResult = "freeMessage";
        actualResult = userVipService.getNextTierName(tierInfo,tierId,notifyMessageMap);
        Assert.assertEquals(expectResult,actualResult);

        tierId = 1;
        notifyMessageMap.put("noUserTierMessage","noUserTierMessage");
        expectResult = "freeMessage";
        actualResult = userVipService.getNextTierName(tierInfo,tierId,notifyMessageMap);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("noUserTierMessage")
    public void getNextTierName_noUserTierMessage(){
        TierInfo tierInfo = null;
        Integer tierId = 0;
        Map<String, String> notifyMessageMap = Maps.newHashMap();
        notifyMessageMap.put("noUserTierMessage","noUserTierMessage");
        String expectResult = "noUserTierMessage";
        String actualResult = userVipService.getNextTierName(tierInfo,tierId,notifyMessageMap);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("nextTierName")
    public void getNextTierName_Test(){
        TierInfo tierInfo = new TierInfo();
        tierInfo.setName("tierName");

        Integer tierId = 0;
        Map<String, String> notifyMessageMap = Maps.newHashMap();
        String expectResult = "tierName";
        String actualResult = userVipService.getNextTierName(tierInfo,tierId,notifyMessageMap);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    public void initUserVipTier(){
        UserVipTier userVipTier = UserVipTier.builder()
                .tierId(1)
                .tierName("tierName")
                .build();

        UserVipTier actualResult = userVipService.initUserVipTier(userVipTier,Lists.newArrayList());
        Assert.assertEquals(userVipTier,actualResult);

        UserTierExpire userTierExpire = UserTierExpire.builder()
                .tierId(1)
                .nextTierId(1)
                .popupMessage("popupMessage")
                .notifyTime(11)
                .notifyType(1)
                .notifyDay(1)
                .build();
        actualResult = userVipService.initUserVipTier(userVipTier,Arrays.asList(userTierExpire));
        Assert.assertEquals(userVipTier,actualResult);
    }

    @Test
    public void test_queryUserVipByActiveTierId(){
        when(iUserVipDAO.queryUserVipByActiveTierId(any(),any())).thenReturn(new UserVipDO());
        UserVipDO expectResult = new UserVipDO();
        UserVipDO actualResult = userVipService.queryUserVipByActiveTierId(1,1);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    public void test_verifyNeedNotify(){
        Integer userId = 1;
        UserVipTier userVipTier = new UserVipTier();
        {
            userVipTier.setNotify(false);
            userVipService.verifyNeedNotify(userId,userVipTier);
            Assert.assertFalse(userVipTier.isNotify());
        }
        {
            userVipTier.setNotify(true);
            userVipTier.setTierId(-1);
            userVipService.verifyNeedNotify(userId,userVipTier);
            Assert.assertTrue(userVipTier.isNotify());
        }
        {
            userVipTier.setNotify(true);
            userVipTier.setTierId(1);
            when(redisService.containsKey(any())).thenReturn(true);
            userVipService.verifyNeedNotify(userId,userVipTier);
            Assert.assertFalse(userVipTier.isNotify());
        }
        {
            userVipTier.setNotify(true);
            userVipTier.setTierId(1);
            when(redisService.containsKey(any())).thenReturn(false);
            userVipService.verifyNeedNotify(userId,userVipTier);
            Assert.assertTrue(userVipTier.isNotify());
        }
    }
}
