package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.BindDeviceResult;
import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.domain.DeviceManualDO;
import com.addx.iotcamera.bean.domain.IpInfo;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.domain.device.DeviceBindStep;
import com.addx.iotcamera.bean.mqtt.Request.MqttDeviceBindRequest;
import com.addx.iotcamera.bean.openapi.PaasTenantInfo;
import com.addx.iotcamera.config.DeviceAntiflickerConfig;
import com.addx.iotcamera.config.PaasTenantConfigTest;
import com.addx.iotcamera.config.apollo.DeviceLanguageConfig;
import com.addx.iotcamera.config.device.DeviceCameraNameConfig;
import com.addx.iotcamera.config.device.DeviceNameConfig;
import com.addx.iotcamera.config.device.DeviceSettingConfig;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.enums.BindContentSrc;
import com.addx.iotcamera.enums.DeviceBindStatusEnums;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.service.device.*;
import com.addx.iotcamera.service.device.model.DeviceModelTenantService;
import com.addx.iotcamera.service.device.model.DeviceModelVoiceService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.openapi.NodeMatcherAgency;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.addx.iotcamera.service.user.UserAppScoreService;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.RandomUtils;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.addx.iot.common.vo.Result.failureFlag;
import static org.addx.iot.common.vo.Result.successFlag;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class BindService2Test {

    @InjectMocks
    private BindService bindService;
    @Mock
    private PaasTenantConfig paasTenantConfig;
    @Mock
    private DeviceAttributeService deviceAttributeService;

    @Mock
    private VideoSearchService videoSearchService;

    @Mock
    private IDeviceDAO iDeviceDAO;

    @Mock
    private FirmwareService firmwareService;

    @Mock
    private LibraryService libraryService;

    @Mock
    private FactoryDataQueryService factoryDataQueryService;

    @Mock
    private NotificationService notificationService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private VipService paasVipService;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private ReportLogService reportLogService;

    @Mock
    private RedisService redisService;

    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private OpenApiWebhookService openApiWebhookService;
    @Mock
    private OpenApiConfigService openApiConfigService;

    @Mock
    private RotationPointService rotationPointService;

    @Mock
    private GeoIpService geoIpService;

    //    @Autowired
//    @Spy
    @Mock
    private DeviceAntiflickerConfig deviceAntiflickerConfig;

    @Mock
    private UserService userService;

    //    @Autowired
//    @Spy
    @Mock
    private DeviceLanguageConfig deviceLanguageConfig;

    @Mock
    private DeviceService deviceService;
    @Mock
    private DeviceStatusService deviceStatusService;

    @Mock
    private DeviceModelTenantService deviceModelTenantService;

    @Mock
    private DeviceAiSettingsService deviceAiSettingsService;

    @Mock
    private NodeMatcherAgency nodeMatcherAgency;

    @Mock
    private DeviceCameraNameConfig deviceCameraNameConfig;

    @Mock
    private DeviceModelService deviceModelService;

    @Mock
    private DeviceNameConfig deviceNameConfig;

    @Mock
    private DeviceModelVoiceService deviceModelVoiceService;

    @Mock
    private DeviceSettingConfig deviceSettingConfig;

    @Mock
    private UserVipService userVipService;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private UserAppScoreService userAppScoreService;

    @Mock
    private UserTierDeviceService userTierDeviceService;

    @Mock ActivityZoneService activityZoneService;

    @Mock
    private DeviceFloodLightService deviceFloodLightService;

    @Before
    public void init(){
        PaasTenantConfigTest.mockPaasTenantConfig(this.paasTenantConfig);
    }

    @Test
    public void test_checkBindOperationStep() {
        final int userId = RandomUtils.nextInt(1000_0000, 10000_0000);
        final String sn1 = OpenApiUtil.shortUUID();
        final String sn2 = OpenApiUtil.shortUUID();
        final String opId1 = OpenApiUtil.shortUUID();
        final String opId2 = OpenApiUtil.shortUUID();
        final String opIds = opId1 + "," + opId2;
        final String redisKey1 = "deviceMqttConnect:" + opId1;
        final String redisKey2 = "deviceMqttConnect:" + opId2;

        AssertUtil.assertThrowException(() -> bindService.checkBindOperationStep("", userId, new IpInfo()));
        {
            when(redisService.get(redisKey1)).thenReturn(null);
            when(redisService.get(redisKey2)).thenReturn(null);
            final Result<DeviceBindStep> result = bindService.checkBindOperationStep(opIds, userId, new IpInfo());
            Assert.assertEquals(successFlag, result.getResult());
            Assert.assertEquals(DeviceBindStatusEnums.PREPARE.getCode(), result.getData().getDeviceBindStep());
            Assert.assertEquals("", result.getData().getSerialNumber());
        }
        {
            when(redisService.get(redisKey1)).thenReturn(null);
            when(redisService.get(redisKey2)).thenReturn(DeviceBindStatusEnums.BIND.getCode() + "");
            final Result<DeviceBindStep> result = bindService.checkBindOperationStep(opIds, userId, new IpInfo());
            Assert.assertEquals(successFlag, result.getResult());
            Assert.assertEquals(DeviceBindStatusEnums.BIND.getCode(), result.getData().getDeviceBindStep());
            Assert.assertEquals("", result.getData().getSerialNumber());
        }
        {
            when(redisService.get(redisKey1)).thenReturn(DeviceBindStatusEnums.BIND.getCode() + "");
            when(redisService.get(redisKey2)).thenReturn(DeviceBindStatusEnums.INIT.getCode() + "");
            when(iDeviceDAO.selectOperation(opId2)).thenReturn(new BindOperationTb() {{
                setOperationId(opId2);
                setSerialNumber(sn2);
            }});
            final Result<DeviceBindStep> result = bindService.checkBindOperationStep(opIds, userId, new IpInfo());
            Assert.assertEquals(successFlag, result.getResult());
            Assert.assertEquals(DeviceBindStatusEnums.INIT.getCode(), result.getData().getDeviceBindStep());
            Assert.assertEquals(sn2, result.getData().getSerialNumber());
        }
        {
            when(redisService.get(redisKey1)).thenReturn(DeviceBindStatusEnums.INIT.getCode() + "");
            when(iDeviceDAO.selectOperation(opId1)).thenReturn(new BindOperationTb() {{
                setOperationId(opId1);
                setSerialNumber(sn1);
            }});
            when(redisService.get(redisKey2)).thenReturn(DeviceBindStatusEnums.INIT.getCode() + "");
            when(iDeviceDAO.selectOperation(opId2)).thenReturn(new BindOperationTb() {{
                setOperationId(opId2);
                setSerialNumber(sn2);
            }});
            final Result<DeviceBindStep> result = bindService.checkBindOperationStep(opIds, userId, new IpInfo());
            Assert.assertEquals(successFlag, result.getResult());
            Assert.assertEquals(DeviceBindStatusEnums.INIT.getCode(), result.getData().getDeviceBindStep());
            Assert.assertEquals(sn1, result.getData().getSerialNumber());
        }
        {
            when(redisService.get(redisKey1)).thenReturn(DeviceBindStatusEnums.BIND.getCode() + "");
            when(redisService.get(redisKey2)).thenReturn(DeviceBindStatusEnums.REFUSE_OVERWRITE_BINDING.getCode() + "");
            final Result<DeviceBindStep> result = bindService.checkBindOperationStep(opIds, userId, new IpInfo());
            Assert.assertEquals(successFlag, result.getResult());
            Assert.assertEquals(DeviceBindStatusEnums.REFUSE_OVERWRITE_BINDING.getCode(), result.getData().getDeviceBindStep());
            Assert.assertEquals("", result.getData().getSerialNumber());
        }
    }

    @Test
    public void test_bindDeviceFromCamera_enableRefuseOverwriteBinding() throws MqttException, IdNotSetException {
        final int userId1 = RandomUtils.nextInt(1000_0000, 10000_0000);
        final int userId2 = RandomUtils.nextInt(1000_0000, 10000_0000);
        final String sn = OpenApiUtil.shortUUID();
        final String userSn = OpenApiUtil.shortUUID();
        final String tenantId1 = OpenApiUtil.shortUUID();
        final String rid = OpenApiUtil.shortUUID();
        final MqttDeviceBindRequest request = new MqttDeviceBindRequest() {{
            setSerialNumber(sn);
            setId(0);
            setTime(System.currentTimeMillis());
            setValue(new MqttDeviceBindRequest.MqttDeviceBindRequestValue() {{
                setRid(rid);
                setUserSn(userSn);
                setSerialNumber(sn);
                setModelNo("modelNo");
                setFirmwareId("1.3.1");
            }});
        }};
        when(iDeviceDAO.selectOperation(rid)).thenReturn(new BindOperationTb() {{
            setOperationId(rid);
            setUserId(userId2);
            setTenantId(tenantId1);
            setRequestTime(PhosUtils.getUTCStamp());
            setBindContentSrc(BindContentSrc.QRCODE.getCode());
        }});

        when(userService.queryUserById(userId1)).thenReturn(new User() {{
            setTenantId(tenantId1);
        }});
        {
            when(userRoleService.getDeviceAdminUserRole(sn)).thenReturn(new UserRoleDO() {{
                setUserId(userId1);
                setAdminId(userId1);
                setRoleId("1");
                setSerialNumber(sn);
            }});
            when(paasTenantConfig.getPaasTenantInfo(tenantId1)).thenReturn(new PaasTenantInfo().setEnableRefuseOverwriteBinding(true));
            final Result<BindDeviceResult> result = bindService.bindDeviceFromCamera(request);
            Assert.assertEquals(new Integer(ResultCollection.REFUSE_OVERWRITE_BINDING.getCode()), result.getResult());
        }
        when(userRoleService.queryUserRolesBySn(sn, false)).thenReturn(new UserRoleService.UserRoles(userId1, Arrays.asList()));
        when(userRoleService.queryUserRolesBySn(sn)).thenReturn(new UserRoleService.UserRoles(userId1, Arrays.asList()));
        when(factoryDataQueryService.queryDeviceManufactureBySn(sn)).thenReturn(new DeviceManufactureTableDO(){{
            setUserSn(userSn);
            setSerialNumber(sn);
        }});
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(userSn)).thenReturn(new DeviceManufactureTableDO(){{
            setUserSn(userSn);
            setSerialNumber(sn);
        }});
        {
            when(userRoleService.getDeviceAdminUserRole(sn)).thenReturn(new UserRoleDO() {{
                setUserId(userId2);
                setAdminId(userId2);
                setRoleId("1");
                setSerialNumber(sn);
            }});

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("");
            final Result<BindDeviceResult> result = bindService.bindDeviceFromCamera(request);
            Assert.assertEquals(failureFlag, result.getResult());
        }
        {
            when(userRoleService.getDeviceAdminUserRole(sn)).thenReturn(new UserRoleDO() {{
                setUserId(userId2);
                setAdminId(userId2);
                setRoleId("1");
                setSerialNumber(sn);
            }});

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("modelNo");
            when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(Sets.newHashSet());
            final Result<BindDeviceResult> result = bindService.bindDeviceFromCamera(request);
            Assert.assertEquals(failureFlag, result.getResult());
        }

        when(userRoleService.getDeviceAdminUserRole(sn)).thenReturn(new UserRoleDO() {{
            setUserId(userId1);
            setAdminId(userId1);
            setRoleId("1");
            setSerialNumber(sn);
        }});
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId1)).thenReturn(new PaasTenantInfo().setEnableRefuseOverwriteBinding(false));

            DeviceManualDO deviceManualDO = new DeviceManualDO();
            deviceManualDO.setUserSn(userSn);
            deviceManualDO.setSerialNumber(sn);
            deviceManualDO.setMacAddress("macAddress");
            request.getValue().setMacAddress("macAddress" + "test");
            when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(deviceManualDO);

            final Result<BindDeviceResult> result = bindService.bindDeviceFromCamera(request);
            Assert.assertEquals(failureFlag, result.getResult());
        }
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId1)).thenReturn(new PaasTenantInfo().setEnableRefuseOverwriteBinding(false));

            DeviceManualDO deviceManualDO = new DeviceManualDO();
            deviceManualDO.setUserSn(userSn);
            deviceManualDO.setSerialNumber(sn);
            deviceManualDO.setMacAddress("macAddress");
            request.getValue().setMacAddress("macAddress");
            when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(deviceManualDO);

            final Result<BindDeviceResult> result = bindService.bindDeviceFromCamera(request);
            Assert.assertEquals(failureFlag, result.getResult());
        }
    }

}
