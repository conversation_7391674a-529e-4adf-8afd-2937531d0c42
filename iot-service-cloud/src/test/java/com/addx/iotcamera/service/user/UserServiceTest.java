package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.apollo.MailSend;
import com.addx.iotcamera.bean.app.AppFormOptionsRequest;
import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.UpdatePasswordRequest;
import com.addx.iotcamera.bean.app.UserRequest;
import com.addx.iotcamera.bean.app.user.SendMailRequest;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.config.PaasVipConfig;
import com.addx.iotcamera.config.apollo.MailSendConfig;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.config.device.DeviceSettingConfig;
import com.addx.iotcamera.config.device.FreeUserVipTier2Config;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.enums.pay.TierTypeEnums;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.helper.TrackerTokenHelper;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.TierIdUtil;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Sets;
import lombok.SneakyThrows;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.*;

import static com.addx.iotcamera.constants.PayConstants.FREE_TIER_2;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.addx.iot.common.enums.ResultCollection.SHARE_TO_SELF;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class UserServiceTest {

    @InjectMocks
    private UserService userService;
    @Mock
    private DeviceModelConfigService deviceModelConfigService;
    @Mock
    private IUserDAO userDAO;
    @Mock
    private UpdatePasswordRequest request;
    @Mock
    private TokenService tokenService;
    @Mock
    private TrackerTokenHelper trackerTokenHelper;
    @Mock
    private JwtHelper jwtHelper;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private UserVipService userVipService;

    @Mock
    private DeviceSettingConfig deviceSettingConfig;
    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private MailConfirmService mailConfirmService;
    @Mock
    private AppAccountConfig appAccountConfig;

    @Mock
    private UserTierDeviceService userTierDeviceService;

    FreeUserVipTier2Config freeUserVipTier2Config;

    @Mock
    private TierService tierService;

    @Mock
    private PaasVipConfig paasVipConfig;
    @Mock
    private MailSendConfig mailSendConfig;

    @Before
    @SneakyThrows
    public void init() {
        freeUserVipTier2Config = new FreeUserVipTier2Config();
        try {
            freeUserVipTier2Config.afterPropertiesSet();
        } catch (Exception e) {
        }
        freeUserVipTier2Config.setRegisterConfigList(Collections.singletonList(new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(100);
            setRegisterStart(0);
            setRegisterEnd(Integer.MAX_VALUE);
            setCooldownValue(Collections.singletonMap("cg1", "60"));
        }}));
        when(userTierDeviceService.getIsNoVipOrFreeTier2(any(), any())).thenAnswer(it -> {
            Integer currentTierId = userTierDeviceService.getDeviceCurrentTier(it.getArgument(0), it.getArgument(1));
            return TierIdUtil.getTierLevelFromTierId(currentTierId) < 0 || TierIdUtil.isFreeTier2(currentTierId);
        });

        when(deviceInfoService.getDeviceSupport(anyString())).thenReturn(new CloudDeviceSupport(){{
            setSupportCanStandby(0);
        }});
        Field deviceSupportServiceField = FreeUserVipTier2Config.class.getDeclaredField("deviceInfoService");
        deviceSupportServiceField.setAccessible(true);
        deviceSupportServiceField.set(freeUserVipTier2Config, deviceInfoService);

        Field deviceModelCOnfigServiceField = FreeUserVipTier2Config.class.getDeclaredField("deviceModelConfigService");
        deviceModelCOnfigServiceField.setAccessible(true);
        deviceModelCOnfigServiceField.set(freeUserVipTier2Config, deviceModelConfigService);

        Field tierServiceField = FreeUserVipTier2Config.class.getDeclaredField("tierService");
        tierServiceField.setAccessible(true);
        tierServiceField.set(freeUserVipTier2Config, tierService);
    }

    @Test
    public void testUpdatePasswordWithUserNotExist() {
        when(userDAO.getUserById(any())).thenReturn(null);

        Result expectedResult = Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");

        Result actualResult = userService.updatePassword(1, new UpdatePasswordRequest());

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testUpdatePasswordWithWrongPassword() {

        User stored = new User();
        stored.setHashedPassword("wrongPassword");

        when(userDAO.getUserById(any())).thenReturn(stored);

        Result expectedResult = Result.Error(-1021, "WRONG_PASSWORD");

        UpdatePasswordRequest updatePasswordRequest = new UpdatePasswordRequest();
        updatePasswordRequest.setOldPassword("!qwe123Q");
        Result actualResult = userService.updatePassword(1, updatePasswordRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testUpdatePasswordWithInvalidNewPassword() {

        User stored = new User();
        String oldPassword = PhosUtils.CalcSalt("oldPassword", "salt");
        stored.setHashedPassword(oldPassword);
        stored.setSalt("salt");
        when(userDAO.getUserById(any())).thenReturn(stored);

        when(request.getOldPassword()).thenReturn("oldPassword");

        when(request.getNewPassword()).thenReturn("I");

        Result expectedResult = Result.Error(-1012, "INVALID_PASSWORD");

        Result actualResult = userService.updatePassword(1, request);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testUpdatePasswordWithUpdateSQLFailure() {

        User stored = new User();
        String oldPassword = PhosUtils.CalcSalt("oldPassword", "salt");
        stored.setHashedPassword(oldPassword);
        stored.setSalt("salt");
        when(userDAO.getUserById(any())).thenReturn(stored);

        when(request.getOldPassword()).thenReturn("oldPassword");

        when(request.getNewPassword()).thenReturn("!QWEqwe123");

        when(userDAO.updateUserPassword(any())).thenReturn(0);

        AppInfo appInfo = new AppInfo();
        when(request.getApp()).thenReturn(appInfo);

        Result expectedResult = Result.Failure("No data matches the operation.");

        Result actualResult = userService.updatePassword(1, request);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testUpdatePasswordSuccess() {

        User stored = new User();
        String oldPassword = PhosUtils.CalcSalt("oldPassword", "salt");
        stored.setHashedPassword(oldPassword);
        stored.setSalt("salt");
        when(userDAO.getUserById(any())).thenReturn(stored);

        when(request.getOldPassword()).thenReturn("oldPassword");

        when(request.getNewPassword()).thenReturn("!QWEqwe123");

        when(userDAO.updateUserPassword(any())).thenReturn(1);

        doNothing().when(tokenService).setUserToken(any());

        AppInfo appInfo = new AppInfo();
        when(request.getApp()).thenReturn(appInfo);

        when(jwtHelper.generateToken(any())).thenReturn(new HttpTokenDO());
        when(trackerTokenHelper.generateTrackerToken(any())).thenReturn("");

        Result actualResult = userService.updatePassword(1, request);

        assertEquals(LoginResponseDO.class, actualResult.getData().getClass());
    }

    @Test
    public void test_getAppFormOptions() throws Exception {
        when(userRoleService.getUserRoleDOByUserIdAndSerialNumber(anyInt(), anyString())).thenReturn(UserRoleDO.builder().build());

        when(userDAO.getUserById(any())).thenReturn(new User() {{
            setRegistTime((int) (System.currentTimeMillis() / 1000));
        }});

        when(deviceManualService.getModelNoBySerialNumber(anyString())).thenReturn("cg1");
        freeUserVipTier2Config.getRegisterConfigList().get(0).setCooldownValue(Collections.singletonMap("cg1", "60"));

        // no user tier
        when(userTierDeviceService.getDeviceCurrentTier(any(), any())).thenReturn(FREE_TIER_2);

        DeviceModel deviceModel = new DeviceModel();
        deviceModel.setCanStandby(true);
        when(deviceModelConfigService.queryDeviceModelConfig(any())).thenReturn(deviceModel);

        when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierType(TierTypeEnums.TIER_LEVEL.getCode()).build());

        AppFormOptionsDO appFormOptionsDO = userService.getAppFormOptions(1, new AppFormOptionsRequest() {{
            setSerialNumber("sn_01");
        }});
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("cooldown_in_s").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_300.getValue()).build())));
        Assert.assertTrue(!appFormOptionsDO.getDeviceFormOptions().get("cooldownUserEnable").isEmpty());
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("videoSeconds").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_10.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_15.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_20.getValue()).build())));

        // free tier 1
        when(userTierDeviceService.getDeviceCurrentTier(any(), any())).thenReturn(10);
        appFormOptionsDO = userService.getAppFormOptions(1, new AppFormOptionsRequest() {{setSerialNumber("sn_01");}});
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("cooldown_in_s").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_10.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_30.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_60.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_180.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_300.getValue()).build())));
        Assert.assertTrue(!appFormOptionsDO.getDeviceFormOptions().get("cooldownUserEnable").isEmpty());
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("videoSeconds").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_10.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_15.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_20.getValue()).build())));

        // free tier 2
        when(userTierDeviceService.getDeviceCurrentTier(any(), any())).thenReturn(100);
        appFormOptionsDO = userService.getAppFormOptions(1, new AppFormOptionsRequest() {{setSerialNumber("sn_01");}});
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("cooldown_in_s").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_60.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_180.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_300.getValue()).build())));
        Assert.assertTrue(!appFormOptionsDO.getDeviceFormOptions().get("cooldownUserEnable").isEmpty());
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("videoSeconds").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_10.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_15.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_20.getValue()).build())));

        // vip tier
        when(userTierDeviceService.getDeviceCurrentTier(any(), any())).thenReturn(1);
        appFormOptionsDO = userService.getAppFormOptions(1, new AppFormOptionsRequest() {{setSerialNumber("sn_01");}});
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("cooldown_in_s").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_10.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_30.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_60.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_180.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_300.getValue()).build())));
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("cooldownUserEnable").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(true).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(false).build())));
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("videoSeconds").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_10.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_15.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_20.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_AUTO.getValue()).build())));


        when(deviceManualService.getModelNoBySerialNumber(anyString())).thenReturn("cb1");
        freeUserVipTier2Config.getRegisterConfigList().get(0).setCooldownValue(Collections.singletonMap("cb1", "300"));
        when(userTierDeviceService.getDeviceCurrentTier(any(), any())).thenReturn(100);
        appFormOptionsDO = userService.getAppFormOptions(1, new AppFormOptionsRequest() {{setSerialNumber("sn_01");}});
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("cooldown_in_s").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.CooldownOptionValue.SECONDS_300.getValue()).build())));
        Assert.assertTrue(!appFormOptionsDO.getDeviceFormOptions().get("cooldownUserEnable").isEmpty());
        Assert.assertTrue(appFormOptionsDO.getDeviceFormOptions().get("videoSeconds").containsAll(Arrays.asList(AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_10.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_15.getValue()).build(), AppFormOptionsDO.AppFormOptionDO.builder().value(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_20.getValue()).build())));
    }


    @Test
    @DisplayName("用户appVersion-用户信息未变化")
    public void test_updateUserAppInfo_userequeal() {
        String appType = "iOS";
        Integer appVersion = 1;
        String appVersionName = "appVersionName";

        User user = new User();
        user.setId(1);
        user.setAppType(appType);
        user.setAppVersion(1);
        user.setAppVersionName(appVersionName);
        //when(userDAO.updateUserById(user)).thenReturn(1);

        userService.updateUserAppInfo(user, appType, appVersion, appVersionName);
        verify(userDAO, times(0)).updateUserById(user);
    }

    @Test
    @DisplayName("用户appVersion-用户版本变化")
    public void test_updateUserAppInfo_userNull() {
        String appType = "iOS";
        Integer appVersion = 1;
        String appVersionName = "";

        User user = new User();
        user.setId(1);
        user.setAppType(appType);
        user.setAppVersion(2);
        user.setAppVersionName(appVersionName);

        when(userDAO.updateUserById(user)).thenReturn(1);

        userService.updateUserAppInfo(user, appType, appVersion, appVersionName);
        verify(userDAO, times(1)).updateUserById(user);
    }

    @Test
    @DisplayName("用户appVersion")
    public void test_updateUserAppInfo() {
        String appType = "iOS";
        Integer appVersion = 1;
        String appVersionName = "";

        User user = new User();
        user.setId(1);
        user.setAppType(appType);
        user.setAppVersion(appVersion);
        user.setAppVersionName("appVersionName");

        when(userDAO.updateUserById(user)).thenReturn(1);

        userService.updateUserAppInfo(user, appType, appVersion, appVersionName);
        verify(userDAO, times(1)).updateUserById(user);
    }

    @Test(expected = BaseException.class)
    @DisplayName("删除用户-用户不存在")
    public void test_cancellationUser_noUser() {
        UserRequest request = new UserRequest();
        request.setId(1);

        when(userDAO.getUserById(any())).thenReturn(null);
        userService.cancellationUser(request);
    }


    @Test
    @DisplayName("删除用户")
    public void test_cancellationUser() {
        UserRequest request = new UserRequest();
        request.setId(1);

        when(userDAO.getUserById(any())).thenReturn(new User());
        doNothing().when(userRoleService).deleteUserRole(any());

        when(userDAO.deleteUser(any())).thenReturn(1);
        doNothing().when(mailConfirmService).deleteMailConfirm(any());
        doNothing().when(tokenService).deleteUserToken(any());

        userService.cancellationUser(request);

        verify(tokenService, times(1)).deleteUserToken(any());
    }

    @Test
    public void testGetUserBasicInfoPositive() {
        User storedUser = new User();
        storedUser.setId(1);
        storedUser.setName("John");
        storedUser.setLastName("Doe");
        storedUser.setEmail("<EMAIL>");
        storedUser.setPhone("1234567890");
        storedUser.setHashedPassword("a");
        storedUser.setCountryNo("US");
        when(userDAO.getUserById(any())).thenReturn(storedUser);

        LoginResponseDO expected = new LoginResponseDO();
        expected.setId(1);
        expected.setName("John");
        expected.setLastName("Doe");
        expected.setEmail("<EMAIL>");
        expected.setPhone("1234567890");
        expected.setHasPassword(true);
        expected.setCountryNo("US");

        LoginResponseDO actualResult = userService.getUserBasicInfo(1);
        assertEquals(expected, actualResult);
    }

    @Test
    public void test_buildLoginResponseDO() {
        User user = new User();
        user.setId(RandomUtils.nextInt(100_0000, 1000_0000));
        HttpTokenDO httpTokenDO = new HttpTokenDO();
        LoginResponseDO loginResponseDO = userService.buildLoginResponseDO(user, httpTokenDO);
        Assert.assertEquals(httpTokenDO, loginResponseDO.getToken());
        Assert.assertEquals(user.getId(), loginResponseDO.getId());
        Assert.assertEquals(user.getName(), loginResponseDO.getName());
        Assert.assertEquals(user.getEmail(), loginResponseDO.getEmail());
        Assert.assertEquals(user.getPhone(), loginResponseDO.getPhone());
    }

    @Test
    @DisplayName("判断用户是否已存在")
    public void test_verifyUserExist(){
        User user = new User();
        String adminEmail = "adminEmail";
        Integer userId = 1;

        Result exceptedResult;
        Result actualResult;
        {
            //不能分享给自己
            user.setEmail(adminEmail);
            when(userDAO.getUserById(any())).thenReturn(user);
            exceptedResult = Result.Error(SHARE_TO_SELF);
            actualResult = userService.verifyUserExist(userId,adminEmail, TENANTID_VICOO);
            Assert.assertEquals(actualResult,exceptedResult);
        }
        {
            user.setEmail("email");
            when(userDAO.getUserById(any())).thenReturn(user);

            when(userDAO.getUserByEmailAndTenantId(adminEmail,null, TENANTID_VICOO)).thenReturn(new User());
            actualResult = userService.verifyUserExist(userId,adminEmail, TENANTID_VICOO);
            Assert.assertEquals(actualResult.getResult().longValue(),0);
        }
    }


    @Test
    @DisplayName("批了查询可用用户List")
    public void test_selectAllValidUser(){
        Integer startUserId = 0;
        when(paasVipConfig.getExcludeTenantIds()).thenReturn(Sets.newHashSet());
        when(userDAO.selectAllBatch(any(),any(),any(),any(),any())).thenReturn(Lists.newArrayList());
        List<User> exceptedResult = Lists.newArrayList();
        List<User> actualResult = userService.selectAllValidUser(startUserId);
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    public void test_sendMail4Kb(){
        Integer userId = null;
        SendMailRequest request1 = new SendMailRequest();

//        request1.setApp();
        AppInfo appInfo = new AppInfo();
        appInfo.setTenantId("Vicoo");
        request1.setLanguage("en");
        request1.setApp(appInfo);
        userService.sendMail4Kb(userId, request1);
        userId = 1;
        userService.sendMail4Kb(userId, request1);
        User u = new User();
        when(userDAO.getUserById(any())).thenReturn(u);
        userService.sendMail4Kb(userId, request1);

        u.setEmail("123");
//        userService.sendMail4Kb(userId, request1);
        Map<String, Map<String, MailSend>> map = new HashMap<>();
        Map<String, MailSend> subMap = new HashMap<>();
        map.put("Vicoo", subMap);
        when(mailSendConfig.getConfig()).thenReturn(map);
        when(mailConfirmService.getSendEmailTitle(any(), any())).thenReturn("title");
        when(mailConfirmService.getSendEmailBody(anyString(), anyString(), any(), any(), any(), any())).thenReturn("body");
        when(mailConfirmService.queryTenantName(anyString(), anyString())).thenReturn("asdf");
        EmailAccount emailAccount = new EmailAccount();
        emailAccount.setAccount("1");
        emailAccount.setPassword("2");
        when(appAccountConfig.queryEmailAccount(anyString())).thenReturn(emailAccount);


        Result result = userService.sendMail4Kb(userId, request1);

        Assert.assertNotNull(result);
    }


}