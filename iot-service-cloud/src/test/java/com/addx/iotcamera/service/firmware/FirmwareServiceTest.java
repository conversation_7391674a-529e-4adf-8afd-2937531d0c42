package com.addx.iotcamera.service.firmware;

import com.addx.iotcamera.bean.device.DeviceCdCertRequest;
import com.addx.iotcamera.bean.device.DeviceCdCertResponse;
import com.addx.iotcamera.dao.device.CdCertificationInfoDAO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.Silent.class)
public class FirmwareServiceTest {

    @InjectMocks
    private FirmwareService firmwareService;

    @Mock
    private CdCertificationInfoDAO cdCertificationInfoDAO;

    @Test
    public void getCdCertificationByPidVid_non_pid() {
        DeviceCdCertRequest deviceCdCertRequest = new DeviceCdCertRequest();
        deviceCdCertRequest.setVid(2222);
        DeviceCdCertResponse response = firmwareService.getCdCertificationByPidVid(deviceCdCertRequest);
        Assert.assertNull(response.getCdCertification());
    }
    @Test
    public void getCdCertificationByPidVid_non_vid() {
        DeviceCdCertRequest deviceCdCertRequest = new DeviceCdCertRequest();
        deviceCdCertRequest.setPid(1111);
        DeviceCdCertResponse response = firmwareService.getCdCertificationByPidVid(deviceCdCertRequest);
        Assert.assertNull(response.getCdCertification());
    }
    @Test
    public void getCdCertificationByPidVid() {
        String cdCert = "cdCert";
        DeviceCdCertRequest deviceCdCertRequest = new DeviceCdCertRequest();
        Integer pid = 1111;
        Integer vid = 2222;
        deviceCdCertRequest.setPid(pid);
        deviceCdCertRequest.setVid(vid);
        String hexPid = Integer.toHexString(pid);
        String hexVid = Integer.toHexString(vid);
        Mockito.when(cdCertificationInfoDAO.getCdCertificationByPidVid(hexPid, hexVid)).thenReturn(cdCert);
        DeviceCdCertResponse response = firmwareService.getCdCertificationByPidVid(deviceCdCertRequest);
        Assert.assertEquals(response.getCdCertification(), cdCert);
    }
}