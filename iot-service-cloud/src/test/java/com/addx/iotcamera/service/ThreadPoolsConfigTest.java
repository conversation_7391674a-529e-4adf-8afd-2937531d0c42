package com.addx.iotcamera.service;

import com.addx.iotcamera.config.ThreadPoolsConfig;
import com.addx.iotcamera.helper.ConfigHelper;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

@Slf4j
@RunWith(JUnit4.class)
public class ThreadPoolsConfigTest {

    @Test
    public void test() throws Exception {
        JSONObject configJson = ConfigHelper.loadYmlConfig("classpath:application.yml").getJSONObject("thread-pools");
        ThreadPoolsConfig config = configJson.toJavaObject(ThreadPoolsConfig.class);
        log.info("");
    }
}
