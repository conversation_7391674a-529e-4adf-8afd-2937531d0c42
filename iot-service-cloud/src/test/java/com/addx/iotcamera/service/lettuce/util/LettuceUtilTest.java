package com.addx.iotcamera.service.lettuce.util;

import io.jsonwebtoken.lang.Assert;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.mockito.Mockito.when;


@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LettuceUtilTest {

    @Mock
    RedisTemplate<String, String> redisTemplate;
    @Mock
    RedisAdvancedClusterCommands<String, String> lettuceCommand;

    @Test
    public void convertMapObjToGenerify() {
        Map<String, String> inputMap = new HashMap<>();
        inputMap.put("a", "a");
        Map<String, String> stringStringMap = wantGetMapStr(LettuceUtil.convertMapObjToGenerify(inputMap));
        Assert.notEmpty(stringStringMap);
    }

    @Test
    public void convertSetToGenerify() {
        Set<String> hashSet = new HashSet<>();
        hashSet.add("a");
        Set<String> strings = wantGetSetStr(LettuceUtil.convertSetToGenerify(hashSet));
        Assert.notEmpty(strings);
    }

    @Test
    public void convertMapObjToStr() {
        // case1
        Map<String, String> inputMap = new HashMap<>();
        inputMap.put("a", "a");
        Map<String, String> stringStringMap = LettuceUtil.convertMapObjToStr(inputMap);
        Assert.notEmpty(stringStringMap);

        // case2
        Map<String, Integer > inputMap2 = new HashMap<>();
        inputMap2.put("a", 1);
        Map<String, String> stringStringMap2 = LettuceUtil.convertMapObjToStr(inputMap2);
        Assert.notEmpty(stringStringMap2);
    }

    @Test
    public void convertMapAnyToObj() {
        // case1
        Map<String, String> inputMap = new HashMap<>();
        inputMap.put("a", "a");
        Map<Object, Object> objectObjectMap = LettuceUtil.convertMapAnyToObj(inputMap);
        Assert.notEmpty(objectObjectMap);

        // case2
        Map<String, Integer> inputMap2 = new HashMap<>();
        inputMap2.put("a", 1);
        Map<Object, Object> objectObjectMap2 = LettuceUtil.convertMapAnyToObj(inputMap2);
        Assert.notEmpty(objectObjectMap2);
    }

    @Test
    public void importRedisTTLToLettuce() {
        when(redisTemplate.getExpire("a")).thenReturn(1L);
        LettuceUtil.importRedisTTLToLettuce("a", redisTemplate, lettuceCommand);
    }

    @Test
    public void importRedisTTLToRedis() {
        when(redisTemplate.getExpire("a")).thenReturn(1L);
        LettuceUtil.importRedisTTLToRedis("a", redisTemplate, redisTemplate);
    }

    @Test
    public void getDataTransfer() {
        LettuceUtil.getDataTransfer(
                () -> "a",
                () -> false,
                (value) -> {
                }, () -> {
                }
        );
        LettuceUtil.getDataTransfer(
                () -> "a",
                () -> false,
                (value) -> {
                }, () -> {
                }
        );
    }

    public Map<String, String> wantGetMapStr(Map<String, String> inputMap) {
        return inputMap;
    }

    public Set<String> wantGetSetStr(Set<String> set) {
        return set;
    }

}