package com.addx.iotcamera.service;

import com.addx.iotcamera.dao.device.DeviceSupportDAO;
import com.addx.iotcamera.service.device.DeviceEnumMappingService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceSupportService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.config.entity.CloudDeviceRemoteDO;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.collections4.SetUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.io.ClassPathResource;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.dao.device.DeviceSupportDAOHelper.deviceSupportToMap;
import static com.addx.iotcamera.util.TextUtil.toUnderline;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceSupportServiceTest {

    @Mock
    private DeviceEnumMappingService deviceEnumMappingService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private RedisService redisService;
    @Mock
    private DeviceSupportDAO deviceSupportDAO;
    @InjectMocks
    private DeviceSupportService deviceSupportService;

    @Before
    public void before() {
        when(deviceEnumMappingService.getDefaultEnumMapping()).thenReturn(IDeviceEnumMappingServiceTest.getDefaultEnumMapping());
    }

    @Test
    @SneakyThrows
    public void test_deviceSupportDAO() {
        String requestDeviceSupportValue = "{\"deviceSupportResolution\":\"1080P,360P\",\"deviceSupportAlarm\":1,\"deviceSupportMirrorFlip\":true,\"supportWebrtc\":1,\"supportRecLamp\":1,\"supportVoiceVolume\":1,\"supportAlarmVolume\":1,\"supportLiveAudioToggle\":1,\"supportRecordingAudioToggle\":1,\"supportLiveSpeakerVolume\":1,\"supportAlarmWhenRemoveToggle\":0,\"postMotionDetectResult\":0,\"killKeepAlive\":1,\"supportCryDetect\":0,\"deviceDormancySupport\":1,\"p2pConnMgtStrategy\":1,\"supportAlexaWebrtc\":1,\"supportChangeCodec\":0,\"supportPirCooldown\":1,\"resetVolSupport\":0,\"streamProtocol\":\"webrtc\",\"canStandby\":0,\"keepAliveProtocol\":\"tcp\",\"audioCodectype\":\"aac\",\"devicePersonDetect\":0,\"supportDeviceCall\":0,\"supportChargeAutoPowerOn\":0,\"deviceSupportLanguage\":\"cn,en,ja,de,ru,fr,it,es,pt\",\"doorBellRingKey\":0,\"supportDoorBellRingKey\":0,\"supportPirSliceReport\":1,\"batteryCode\":\"\",\"supportMechanicalDingDong\":0}";
        CloudDeviceRemoteDO remoteDO = JSON.parseObject(requestDeviceSupportValue, CloudDeviceRemoteDO.class);
        {
            remoteDO.setDeviceSupportAlarm(1);
            remoteDO.setKillKeepAlive(1);
            remoteDO.setResetVolSupport(1);
            remoteDO.setSupportMechanicalDingDong(1);
            remoteDO.setCanRotate(1);
            remoteDO.setSupportMotionTrack(1);
            remoteDO.setSupportFrequency(1);
            remoteDO.setAntiDisassemblyAlarm(1);
            remoteDO.setSupportGoogleStorage(1);
            CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.from(remoteDO);
            deviceSupportDAO(cloudDeviceSupport);
        }
        {
            remoteDO.setDeviceSupportAlarm(0);
            remoteDO.setKillKeepAlive(0);
            remoteDO.setResetVolSupport(0);
            remoteDO.setSupportMechanicalDingDong(0);
            remoteDO.setCanRotate(0);
            remoteDO.setSupportMotionTrack(0);
            remoteDO.setSupportFrequency(0);
            remoteDO.setAntiDisassemblyAlarm(0);
            remoteDO.setSupportGoogleStorage(0);
            CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.from(remoteDO);
            deviceSupportDAO(cloudDeviceSupport);
        }
    }

    public void deviceSupportDAO(CloudDeviceSupport cloudDeviceSupport) {
//        deviceSupport.setDefaultEnumOptions(deviceEnumMappingService.getDefaultEnumMapping());
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        deviceSupportService.setDeviceSupportDAO(testHelper.getMapper(DeviceSupportDAO.class));
        try {
            String sn = "test_" + OpenApiUtil.shortUUID();
            {
                int saveNum = deviceSupportService.insertDeviceSupport(sn, null);
                Assert.assertEquals(0, saveNum);
            }
            {
                int saveNum = deviceSupportService.insertDeviceSupport(sn, cloudDeviceSupport);
                Assert.assertEquals(1, saveNum);
                CloudDeviceSupport cloudDeviceSupport2 = deviceSupportService.queryDeviceSupportBySn(sn);
                Assert.assertNotNull(cloudDeviceSupport2);
                Assert.assertEquals(cloudDeviceSupport, cloudDeviceSupport2);
            }
            {
                int updateNum = deviceSupportService.updateDeviceSupport(sn, null);
                Assert.assertEquals(0, updateNum);
            }
            JSONObject obj = (JSONObject) JSON.toJSON(cloudDeviceSupport);
            for (String key : obj.keySet()) {
                if ("serialNumber".equals(key)) continue;
                Object val = obj.get(key);
                if (val != null) {
                    if (val instanceof Boolean) {
                        obj.put(key, !(Boolean) val);
                    } else if (val instanceof Integer) {
                        obj.put(key, 1 - (Integer) val);
                    } else if (val instanceof Collection) {
                        obj.put(key, new ArrayList<>());
                    }
                    CloudDeviceSupport cloudDeviceSupport3 = obj.toJavaObject(CloudDeviceSupport.class);
                    int updateNum = deviceSupportService.updateDeviceSupport(sn, cloudDeviceSupport3);
                    Assert.assertEquals("key=" + key + ",val=" + val, 1, updateNum);
                    CloudDeviceSupport cloudDeviceSupport4 = deviceSupportService.queryDeviceSupportBySn(sn);
                    Assert.assertNotNull("key=" + key + ",val=" + val, cloudDeviceSupport4);
                    Assert.assertEquals("key=" + key + ",val=" + val, cloudDeviceSupport3, cloudDeviceSupport4);
                    obj.put(key, val);
                }
            }
        } finally {
            testHelper.commitAndClose();
        }
    }

    //    @Test
    @SneakyThrows
    public void test_buildSql() {
        String path = "example/deviceSupportComment.txt";
        List<String> lines = null;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new ClassPathResource(path).getInputStream()))) {
            lines = reader.lines().collect(Collectors.toList());
        }
        Map<String, String> name2Comment = new LinkedHashMap<>();
        for (int i = 0; i < lines.size(); i += 3) {
            name2Comment.put(lines.get(i), lines.get(i + 2));
        }

//        Field[] fields = DeviceSupport.class.getDeclaredFields();
        Field[] fields = CloudDeviceSupport.class.getDeclaredFields();
        int i = 0;
        List<String[]> namePairs = new LinkedList<>();
        for (Field field : fields) {
            if ("serialNumber".equals(field.getName())) continue;
            log.info("{} : {} {} {}", i++, field.getName(), toUnderline(field.getName()), field.getType().getName());
            namePairs.add(new String[]{field.getName(), toUnderline(field.getName()), field.getType().getSimpleName()});
        }
        {
            String insertSql = new StringBuilder("insert into `camera`.`device_support`( \n")
                    .append("`serial_number`, \n")
                    .append(namePairs.stream().map(it -> "`" + it[1] + "`").collect(Collectors.joining(", \n")))
                    .append(" \n) values ( \n")
                    .append("#{serialNumber}, \n")
                    .append(namePairs.stream().map(it -> "#{" + it[0] + "}").collect(Collectors.joining(", \n")))
                    .append(");").toString();
            log.info("insertSql:\n{}", insertSql);
        }
        {
            // <if test='supportDeviceCall!=null'>,`support_device_call`=#{supportDeviceCall}\n</if>
            String updateSql = new StringBuilder("update `camera`.`device_support` set `serial_number`=#{serialNumber} \n")
                    .append(namePairs.stream().map(it -> "<if test='" + it[0] + "!=null'>,`" + it[1] + "`=#{" + it[0] + "}</if>").collect(Collectors.joining(" \n")))
                    .append(") \n")
                    .append("where `serial_number`=#{serialNumber};\n").toString();
            log.info("updateSql:\n{}", updateSql);
        }
        {
            // `serial_number` varchar(40) not null comment ''
            String createTableSql = new StringBuilder("create table `camera`.`device_support` ( \n")
                    .append("`serial_number` varchar(40) not null comment '设备sn', \n")
                    .append(Arrays.stream(fields).map(it -> {
                        String sqlType;
                        if (Collection.class.isAssignableFrom(it.getType())) {
                            sqlType = "varchar(100)";
                        } else if (String.class == it.getType()) {
                            sqlType = "varchar(100)";
                        } else if (Boolean.class == it.getType() || boolean.class == it.getType()) {
                            sqlType = "tinyint(4)";
                        } else {
                            sqlType = "tinyint(4)";
                        }
                        String comment = name2Comment.get(it.getName());
                        return "`" + TextUtil.toUnderline(it.getName()) + "` " + sqlType + " not null comment '" + comment + "'";
                    }).collect(Collectors.joining(", \n", "", ", \n")))
                    .append("`cdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', \n")
                    .append("`mdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n")
                    .append(") DEFAULT CHARSET=utf8mb4 COMMENT '设备支持项表';").toString();
            log.info("createTableSql:\n{}", createTableSql);
        }
        {
            String allField = Arrays.stream(fields)
                    .map(it -> {
                        if (Collection.class.isAssignableFrom(it.getType())) {
                            return ".put(\"" + it.getName() + "\",  it -> collection2String((Collection<?>) it))";
                        } else if (Boolean.class == it.getType() || boolean.class == it.getType()) {
                            return ".put(\"" + it.getName() + "\",  it -> boolean2Integer((Boolean) it))";
                        } else {
                            return ".put(\"" + it.getName() + "\",  Function.identity())";
                        }
                    })
                    .collect(Collectors.joining("\n"));
            log.info("\n" + allField);
        }
    }

    @Test
    public void test_saveAllDeviceSupport() {
        {
            when(deviceManualService.batchQueryDeviceManualDO(any(), any())).thenReturn(Arrays.asList());
            JSONObject result = deviceSupportService.saveAllDeviceSupport();
            Assert.assertEquals(new Integer(0), result.getInteger("saveNum"));
        }
    }

    //    @Test
    public void test_insertDeviceSupport(){
        String json="{\"supportCryDetect\":0,\"supportAudioCodectype\":\"aac\",\"supportLocalVideoLookBack\":false,\"batteryCode\":\"BTW5200\",\"supportDoorBellRingKey\":[],\"supportWebrtc\":1,\"supportOtaAutoUpgrade\":false,\"supportChargeAutoPowerOn\":1,\"supportPirCooldown\":1,\"pirCooldownTimeOptions\":[],\"liveStreamCodecOptions\":[],\"deviceDormancySupport\":1,\"nightVisionModeOptions\":[],\"localVideoStorageType\":0,\"supportMechanicalDingDong\":false,\"deviceSupportResolution\":[\"1920x1080\",\"1280x720\"],\"supportRotateCalibration\":false,\"pirRecordTimeOptions\":[],\"supportAlexaWebrtc\":1,\"supportGoogleStorage\":true,\"supportAlarmVolume\":1,\"supportVoiceVolume\":1,\"postMotionDetectResult\":0,\"alarmDurationOptions\":[],\"supportRecordingAudioToggle\":1,\"supportNetTest\":true,\"supportOci\":true,\"videoResolutionOptions\":[],\"supportPirSliceReport\":1,\"supportMotionTrack\":0,\"canRotate\":0,\"killKeepAlive\":true,\"supportLiveSpeakerVolume\":0,\"supportDevicePersonDetect\":0,\"deviceSupportMirrorFlip\":true,\"supportChangeCodec\":0,\"supportCos\":true,\"quantityCharge\":1,\"nightVisionSensitivityOptions\":[],\"p2pConnMgtStrategy\":1,\"deviceSupportLanguage\":[\"cn\",\"en\",\"ja\",\"de\",\"ru\",\"fr\",\"it\",\"es\"],\"pirSensitivityOptions\":[],\"supportAlarmWhenRemoveToggle\":0,\"supportResetVoltameter\":false,\"supportDeviceCall\":0,\"voltameterType\":1,\"isShield\":true,\"antiflickerSupport\":1,\"supportRecLamp\":1,\"supportSdCardFormat\":false,\"supportStreamProtocol\":\"webrtc\",\"supportKeepAliveProtocol\":\"tcp\",\"supportWifiPowerLevel\":true,\"videoAntiFlickerFrequencyOptions\":[],\"supportLiveAudioToggle\":1,\"supportCanStandby\":1,\"deviceSupportAlarm\":true}";
        final CloudDeviceSupport cloudDeviceSupport = JSON.parseObject(json, CloudDeviceSupport.class);
        final String sn = "16ab60034b191aef9c752c62af460f79";

        final TestHelper testHelper = TestHelper.getInstanceByEnv("staging");
        final DeviceSupportService deviceSupportService = new DeviceSupportService();
        deviceSupportService.setDeviceSupportDAO(testHelper.getMapper(DeviceSupportDAO.class));

        final int insertNum = deviceSupportService.insertDeviceSupport(sn, cloudDeviceSupport);
        Assert.assertEquals(1, insertNum);
    }

    //    @Test
    public void test_insertDeviceSupport2() {
        final String json0 = "{\"name\":\"deviceSupport\",\"time\":1677755317,\"id\":0,\"value\":{\"deviceSupportResolution\":\"1920x1080,1280x720\",\"deviceSupportAlarm\":1,\"deviceSupportMirrorFlip\":true,\"supportWebrtc\":1,\"supportRecLamp\":1,\"supportVoiceVolume\":1,\"supportAlarmVolume\":1,\"supportLiveAudioToggle\":1,\"supportRecordingAudioToggle\":1,\"supportLiveSpeakerVolume\":0,\"supportAlarmWhenRemoveToggle\":0,\"postMotionDetectResult\":0,\"killKeepAlive\":1,\"supportCryDetect\":0,\"deviceDormancySupport\":1,\"p2pConnMgtStrategy\":1,\"supportAlexaWebrtc\":1,\"supportChangeCodec\":1,\"supportPirCooldown\":1,\"resetVolSupport\":0,\"streamProtocol\":\"webrtc\",\"canStandby\":1,\"keepAliveProtocol\":\"tcp\",\"audioCodectype\":\"aac\",\"devicePersonDetect\":0,\"supportDeviceCall\":0,\"supportChargeAutoPowerOn\":1,\"deviceSupportLanguage\":\"cn,en,ja,de,ru,fr,it,es\",\"supportDoorBellRingKey\":\"\",\"supportPirSliceReport\":1,\"supportMechanicalDingDong\":0,\"supportOtaAutoUpgrade\":0,\"batteryCode\":\"HK5000\",\"voltameterType\":1,\"antiflickerSupport\":1,\"quantityCharge\":1,\"supportMotionTrack\":0,\"canRotate\":0,\"supportGoogleStorage\":1,\"isShield\":false,\"supportWifiPowerLevel\":true,\"supportNetTest\":true,\"isFilter\":false,\"supportOci\":1,\"supportCos\":1,\"iccid\":\"iccid\"}}";
        final CloudDeviceRemoteDO remote = JSON.parseObject(json0, CloudDeviceRemoteDO.class);
        final CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.from(remote);
        final Map<String, Object> map0 = deviceSupportToMap(cloudDeviceSupport, true);

//        String json="{\"name\":\"deviceSupport\",\"time\":1677755317,\"id\":0,\"value\":{\"deviceSupportResolution\":\"1920x1080,1280x720\",\"deviceSupportAlarm\":1,\"deviceSupportMirrorFlip\":true,\"supportWebrtc\":1,\"supportRecLamp\":1,\"supportVoiceVolume\":1,\"supportAlarmVolume\":1,\"supportLiveAudioToggle\":1,\"supportRecordingAudioToggle\":1,\"supportLiveSpeakerVolume\":0,\"supportAlarmWhenRemoveToggle\":0,\"postMotionDetectResult\":0,\"killKeepAlive\":1,\"supportCryDetect\":0,\"deviceDormancySupport\":1,\"p2pConnMgtStrategy\":1,\"supportAlexaWebrtc\":1,\"supportChangeCodec\":1,\"supportPirCooldown\":1,\"resetVolSupport\":0,\"streamProtocol\":\"webrtc\",\"canStandby\":1,\"keepAliveProtocol\":\"tcp\",\"audioCodectype\":\"aac\",\"devicePersonDetect\":0,\"supportDeviceCall\":0,\"supportChargeAutoPowerOn\":1,\"deviceSupportLanguage\":\"cn,en,ja,de,ru,fr,it,es\",\"supportDoorBellRingKey\":\"\",\"supportPirSliceReport\":1,\"supportMechanicalDingDong\":0,\"supportOtaAutoUpgrade\":0,\"batteryCode\":\"HK5000\",\"voltameterType\":1,\"antiflickerSupport\":1,\"quantityCharge\":1,\"supportMotionTrack\":0,\"canRotate\":0,\"supportGoogleStorage\":1,\"isShield\":false,\"supportWifiPowerLevel\":true,\"supportNetTest\":true,\"isFilter\":false,\"supportOci\":1,\"supportCos\":1}}";
        String json = "{\"support_cry_detect\":0,\"support_audio_codectype\":\"aac\",\"support_local_video_look_back\":0,\"battery_code\":\"HK5000\",\"support_door_bell_ring_key\":\"\",\"support_webrtc\":1,\"support_ota_auto_upgrade\":0,\"support_charge_auto_power_on\":1,\"support_pir_cooldown\":1,\"pir_cooldown_time_options\":\"\",\"live_stream_codec_options\":\"\",\"device_dormancy_support\":1,\"night_vision_mode_options\":\"\",\"local_video_storage_type\":0,\"support_mechanical_ding_dong\":0,\"device_support_resolution\":\"1920x1080,1280x720\",\"support_rotate_calibration\":0,\"pir_record_time_options\":\"\",\"support_alexa_webrtc\":1,\"support_google_storage\":1,\"support_alarm_volume\":1,\"support_voice_volume\":1,\"post_motion_detect_result\":0,\"alarm_duration_options\":[],\"support_recording_audio_toggle\":1,\"support_net_test\":1,\"support_oci\":1,\"video_resolution_options\":\"\",\"support_pir_slice_report\":1,\"support_motion_track\":0,\"can_rotate\":0,\"kill_keep_alive\":1,\"support_live_speaker_volume\":0,\"support_device_person_detect\":0,\"device_support_mirror_flip\":1,\"support_change_codec\":1,\"support_cos\":1,\"quantity_charge\":1,\"night_vision_sensitivity_options\":\"\",\"p2p_conn_mgt_strategy\":1,\"device_support_language\":\"cn,en,ja,de,ru,fr,it,es\",\"pir_sensitivity_options\":\"\",\"support_alarm_when_remove_toggle\":0,\"support_reset_voltameter\":0,\"support_device_call\":0,\"voltameter_type\":1,\"is_shield\":0,\"antiflicker_support\":1,\"support_rec_lamp\":1,\"support_sd_card_format\":0,\"support_stream_protocol\":\"webrtc\",\"support_keep_alive_protocol\":\"tcp\",\"support_wifi_power_level\":1,\"video_anti_flicker_frequency_options\":\"\",\"support_live_audio_toggle\":1,\"support_can_standby\":1,\"device_support_alarm\":1}";
        final Map<String, Object> map = JSON.parseObject(json, Map.class);
        final String sn = "16ab60034b191aef9c752c62af460f79";

        final SetUtils.SetView<String> keys = SetUtils.union(map0.keySet(), map.keySet());
        for (final String key : keys) {
            if (!Objects.equals(map0.get(key), map.get(key))) {
//            if (map0.get(key) == null || map.get(key) == null) {
                log.info("{} : {} , {} {}", key, map0.get(key), map.get(key), Optional.ofNullable(map.get(key)).map(it -> it.getClass()).orElse(null));
            }
        }

        final TestHelper testHelper = TestHelper.getInstanceByEnv("staging-us");
        final DeviceSupportDAO dao = testHelper.getMapper(DeviceSupportDAO.class);
        final int insertNum = dao.insert(sn, map);
        Assert.assertEquals(1, insertNum);
    }

    @Test
    public void test_initIccid() {
        final String json0 = "{\"iccid\":\"iccid\"}";
        final CloudDeviceRemoteDO remote = JSON.parseObject(json0, CloudDeviceRemoteDO.class);
        final CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.from(remote);
        Assert.assertEquals(cloudDeviceSupport.getIccid(),"iccid");
    }

}
