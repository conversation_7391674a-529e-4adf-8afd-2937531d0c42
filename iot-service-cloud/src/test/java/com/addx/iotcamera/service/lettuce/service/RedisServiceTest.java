package com.addx.iotcamera.service.lettuce.service;

import com.addx.iotcamera.bean.domain.DeviceOperationDO;
import com.addx.iotcamera.bean.domain.ShareCacheDO;
import com.addx.iotcamera.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.SpringContextUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest(SpringContextUtil.class)
public class RedisServiceTest {

    @InjectMocks
    private RedisService redisService;
    @Mock
    private StringRedisTemplate businessRedisTemplateClusterClient;
    @Mock
    LettuceDataTransClient lettuceDataTransClient;
    @Mock
    ValueOperations<String, String> valueOperations;
    @Mock
    ListOperations<String, String> listOperations;
    @Mock
    SetOperations<String, String> setOperations;
    @Mock
    ZSetOperations<String, String> zSetOperations;
    @Mock
    HashOperations<String, Object, Object> hashOperations;

    String key = "a";

    String field = "a";

    String value = "a";

    int timeout = 1;

    @Before
    public void before() {
        // mock数据
        List<String> resultList = Collections.singletonList("a");
        Set<String> resultSet = Collections.singleton("a");
        Set<ZSetOperations.TypedTuple<String>> typedTuples = Collections.singleton(new DefaultTypedTuple<>("a", 1d));
        Map<Object, Object> resultMap = new HashMap<>();
        resultMap.put("a", "a");
        Map<String, String> resultLettuceMap = new HashMap<>();

        when(businessRedisTemplateClusterClient.opsForValue()).thenReturn(valueOperations);
        when(businessRedisTemplateClusterClient.opsForList()).thenReturn(listOperations);
        when(businessRedisTemplateClusterClient.opsForSet()).thenReturn(setOperations);
        when(businessRedisTemplateClusterClient.opsForZSet()).thenReturn(zSetOperations);
        when(businessRedisTemplateClusterClient.opsForHash()).thenReturn(hashOperations);

        when(businessRedisTemplateClusterClient.opsForValue().get(key)).thenReturn("a");
        when(businessRedisTemplateClusterClient.opsForList().range(key, 0, -1)).thenReturn(resultList);
        when(businessRedisTemplateClusterClient.opsForSet().members(key)).thenReturn(resultSet);
        when(businessRedisTemplateClusterClient.opsForZSet().rangeWithScores(key, 0, -1)).thenReturn(typedTuples);
        when(businessRedisTemplateClusterClient.opsForHash().entries(key)).thenReturn(resultMap);
        when(businessRedisTemplateClusterClient.getExpire(key)).thenReturn(1L);
    }


    @Test
    public void getDeviceOperationDo() {
        redisService.getDeviceOperationDo(key);
    }

    @Test
    public void setDeviceOperationDOWithEmpty() {
        redisService.setDeviceOperationDOWithEmpty(key);
    }

    @Test
    public void hasDeviceOperationDo() {
        redisService.hasDeviceOperationDo(key);
    }

    @Test
    public void dropDeviceOperationDo() {
        redisService.dropDeviceOperationDo(key);
    }

    @Test
    public void setDeviceOperationDo() {
        redisService.setDeviceOperationDo(key, new DeviceOperationDO());
    }

    @Test
    public void getShareCacheDO() {
        redisService.getShareCacheDO(key);
    }

    @Test
    public void setShareCacheDO() {
        redisService.setShareCacheDO(key, new ShareCacheDO(), timeout);
    }


    @Test
    public void set() {
        redisService.set(key, value);
        redisService.set(key, value, timeout);
    }

    @Test
    public void get() {
        redisService.get(key);
        redisService.get(key, field);
    }

    @Test
    public void containsKey() {
        redisService.containsKey(key);
    }

    @Test
    public void getFromSlave() {
        redisService.getFromSlave(key);
    }

    @Test
    public void delete() {
        redisService.delete(key);
    }

    @Test
    public void setExpired() {
        redisService.setExpired(key, timeout);

    }

    @Test
    public void increNum() {
        redisService.increNum(key);
    }

    @Test
    public void incrBy() {
        redisService.incrBy(key, 1);
    }

    @Test
    public void setHashFieldValue() {
        redisService.setHashFieldValue(key, field, value);
    }

    @Test
    public void setHashFieldValueAll() {
        redisService.setHashFieldValueAll(key, new HashMap<>());
    }

    @Test
    public void setHashFieldValueMap() {
        redisService.setHashFieldValueMap(key, new HashMap<>(), 1L);
    }

    @Test
    public void getHashFieldValue() {
        redisService.getHashFieldValue(key, field);
    }

    @Test
    public void getHashEntries() {
        redisService.getHashEntries(key);
    }

    @Test
    public void deleteHashField() {
        redisService.deleteHashField(key, field);
    }

    @Test
    public void deleteHashFields() {
        redisService.deleteHashFields(key, Collections.singletonList(field));
    }

    @Test
    public void hasFieldKey() {
        redisService.hasFieldKey(key, field);
    }

    @Test
    public void getDeviceBattery() {
        redisService.getDeviceBattery(key);
    }

    @Test
    public void hashIncrementBigDecimal() {
        redisService.hashIncrementBigDecimal(key, field, new BigDecimal(1));
    }

    @Test
    public void hashIncrementDouble() {
        redisService.hashIncrementDouble(key, field, 1d, 1L);
    }

    @Test
    public void hashGetInt() {
        redisService.hashGetInt(key, field, 1);
    }

    @Test
    public void hashGetString() {
        redisService.hashGetString(key, field);
    }

    @Test
    public void hashMultiGet() {
        when(businessRedisTemplateClusterClient.opsForHash()).thenReturn(hashOperations);
        when(hashOperations.multiGet(key, Collections.singletonList(field))).thenReturn(Collections.singletonList("a"));
        redisService.hashMultiGet(key, Collections.singletonList(field));
    }

    @Test
    public void hashGetAll() {
        redisService.hashGetAll(key);
    }

    @Test
    public void hashEntries() {
        redisService.hashEntries(key);
    }

    @Test
    public void hashPut() {
        redisService.hashPut(key, field, value);
    }

    @Test
    public void hashPutAll() {
        redisService.hashPutAll(key, new HashMap<>());
    }

    @Test
    public void hashIncrementInt() {
        redisService.hashIncrementInt(key, field, 1);
        redisService.hashIncrementInt(key, field, 1, 1);
    }

    @Test
    public void hashMembersCount() {
        redisService.hashMembersCount(key);
    }

    @Test
    public void listRightPushAll() {
        redisService.listRightPushAll(key, Collections.singletonList(value), 1);
    }


    @Test
    public void zadd() {
        redisService.zadd(key, value, 1);
    }

    @Test
    public void zremove() {
        redisService.zremove(key, value);
    }

    @Test
    public void zrangeByScore() {
        redisService.zrangeByScore(key, 1, 1, 1, 1);
    }

    @Test
    public void zremoveByScore() {
        redisService.zremoveByScore(key, 1, 1);
    }

    @Test
    public void zCard() {
        redisService.zCard(key);
    }

    @Test
    public void zScore() {
        redisService.zScore(key, value);
    }

    @Test
    public void addSetValue() {
        redisService.addSetValue(key, Collections.singletonList(key).toArray(new String[0]));
    }

    @Test
    public void getSetValue() {
        redisService.getSetValue(key);
    }

    @Test
    public void setIfAbsent() {
        redisService.setIfAbsent(key, value, 1L, TimeUnit.SECONDS);
    }

    @Test
    public void getAndSet() {
        redisService.getAndSet(key, value);
    }

    @Test
    public void multiGet() {
        PowerMockito.mockStatic(SpringContextUtil.class);
        when(SpringContextUtil.getBean(RedisService.class)).thenReturn(redisService);
        redisService.multiGet(null);
        redisService.multiGet(Collections.singletonList(key));
    }

    @Test
    public void rangeList() {
        redisService.rangeList(key);
    }

    @Test
    public void saveList() {
        redisService.saveList(key, value, 1L);
    }

    @Test
    public void deleteListValue() {
        redisService.deleteListValue(key, value);
    }

    @Test
    public void clearRedisCache() {
        redisService.clearRedisCache(Collections.singletonList(key));
    }

    @Test
    public void setMembers() {
        redisService.setMembers(key);
    }

    @Test
    public void setAdd() {
        redisService.setAdd(key, Collections.singleton(value));
    }

    @Test
    public void ttl() {
        redisService.ttl(key);
    }

    @Test
    public void generateLibraryId() {
        redisService.generateLibraryId(1);
    }

    @Test
    public void setScan() {
        redisService.setScan(key, "::*", 1);
    }

    @Test
    public void setRemove() {
        redisService.setRemove(key, Collections.singletonList(value));
    }

}
