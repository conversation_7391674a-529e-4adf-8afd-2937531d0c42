package com.addx.iotcamera.service.vip;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.TierActiveConditionDO;
import com.addx.iotcamera.bean.db.TierGroupDO;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.dao.TierGroupDao;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.util.DateUtils;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;

import com.google.api.client.util.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.threeten.bp.Instant;

@RunWith(MockitoJUnitRunner.Silent.class)
public class TierGroupServiceTest {

    @Mock
    private TierActiveConditionService mockTierActiveConditionService;
    @Mock
    private TierService tierService;
    @Mock
    private TierGroupDao tierGroupDao;
    @Mock
    private IUserVipDAO iUserVipDAO;

    @InjectMocks
    private TierGroupService tierGroupService;



    @Test
    public void testGetById() {
        // Setup
        final TierGroupDO expectedResult = new TierGroupDO(0,  "name", 0, 0, "desc", "tenantId");

        // Configure TierGroupDao.getById(...).
        final TierGroupDO tierGroupDO = new TierGroupDO(0, "name", 0, 0, "desc", "tenantId");
        when(tierGroupDao.getById("tenantId", 0)).thenReturn(tierGroupDO);

        // Run the test
        final TierGroupDO result = tierGroupService.getById("tenantId", 0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAddUserVipDO() {
        Integer buyTierId = 2;
         UserVipDO addUserVipDO = UserVipDO.builder()
                .id(0L)
                .userId(0)
                .tierId(buyTierId)
                .effectiveTime(1000)
                .endTime(0)
                .orderId(0L)
                .tradeNo("tradeNo")
                .active(false)
                .freeTrial(1)
                .build();
        {
            //当前user_vip 为空
            List<UserVipDO> actualResult = tierGroupService.addUserVipDO(Lists.newArrayList(),addUserVipDO,new ProductDO(),0);
            assertEquals(1, actualResult.size());
        }

        addUserVipDO.setFreeTrial(0);
        // Setup
        Integer currentTierId = 1;
        final List<UserVipDO> currentUserVipDOList = Arrays.asList(UserVipDO.builder()
                        .id(0L)
                        .userId(0)
                        .tierId(currentTierId)
                        .effectiveTime(100)
                        .endTime(1000)
                        .orderId(0L)
                        .tradeNo("tradeNo")
                        .active(true)
                        .freeTrial(0)
                        .build());

        final ProductDO productDO = new ProductDO();
        productDO.setId(0);
        productDO.setType(0);
        productDO.setKeyId(0);
        productDO.setBody("body");
        productDO.setPrice(0);
        productDO.setCurrency(0);
        productDO.setSubject("subject");
        productDO.setStatus(0);
        productDO.setMonth(1);
        productDO.setTierId(buyTierId);
        {
            // 当前套餐与要购买的套餐等级相同
            addUserVipDO.setTierId(currentTierId);
            final List<UserVipDO> expectedResult = Arrays.asList(
                    UserVipDO.builder()
                            .id(0L)
                            .userId(0)
                            .tierId(currentTierId)
                            .effectiveTime(1000)
                            .endTime(DateUtils.computeTime(productDO, 0, 1000))
                            .orderId(0L)
                            .tradeNo("tradeNo")
                            .active(false)
                            .freeTrial(0)
                            .build()
            );
            List<UserVipDO> actualResult = tierGroupService.addUserVipDO(currentUserVipDOList,addUserVipDO,productDO,0);
            Assert.assertEquals(expectedResult,actualResult);
        }

        {
            // 当前套餐优先级等级高
            addUserVipDO.setTierId(buyTierId);
            addUserVipDO.setFreeTrial(1);
            Tier currentTier = new Tier();
            currentTier.setTierType(2);
            when(tierService.queryTierById(currentTierId)).thenReturn(currentTier);
            Tier buyTier = new Tier();
            buyTier.setTierType(1);
            when(tierService.queryTierById(buyTierId)).thenReturn(buyTier);
            final List<UserVipDO> expectedResult = Arrays.asList(
                    UserVipDO.builder()
                            .id(0L)
                            .userId(0)
                            .tierId(buyTierId)
                            .effectiveTime(1000)
                            .endTime(DateUtils.computeTime(null, 1, 1000))
                            .orderId(0L)
                            .tradeNo("tradeNo")
                            .active(false)
                            .freeTrial(1)
                            .build()
            );
            List<UserVipDO> actualResult = tierGroupService.addUserVipDO(currentUserVipDOList,addUserVipDO,productDO,1);
            Assert.assertEquals(expectedResult,actualResult);
        }

        addUserVipDO.setFreeTrial(0);
        {
            // 当前套餐优先级等级高
            addUserVipDO.setTierId(buyTierId);

            Tier currentTier = new Tier();
            currentTier.setTierType(2);
            when(tierService.queryTierById(currentTierId)).thenReturn(currentTier);
            Tier buyTier = new Tier();
            buyTier.setTierType(1);
            when(tierService.queryTierById(buyTierId)).thenReturn(buyTier);
            final List<UserVipDO> expectedResult = Arrays.asList(
                    UserVipDO.builder()
                            .id(0L)
                            .userId(0)
                            .tierId(buyTierId)
                            .effectiveTime(1000)
                            .endTime(DateUtils.computeTime(productDO, 0, 1000))
                            .orderId(0L)
                            .tradeNo("tradeNo")
                            .active(false)
                            .freeTrial(0)
                            .build()
            );
            List<UserVipDO> actualResult = tierGroupService.addUserVipDO(currentUserVipDOList,addUserVipDO,productDO,0);
            Assert.assertEquals(expectedResult,actualResult);
        }
        {
            addUserVipDO.setTierId(buyTierId);
            // 新购买的套餐等级优先级高
            Tier currentTier = new Tier();
            currentTier.setTierType(1);
            when(tierService.queryTierById(currentTierId)).thenReturn(currentTier);
            Tier buyTier = new Tier();
            buyTier.setTierType(2);
            when(tierService.queryTierById(buyTierId)).thenReturn(buyTier);


            Integer currentTime = (int)Instant.now().getEpochSecond();
            final List<UserVipDO> expectedResult = Arrays.asList(
                    UserVipDO.builder()
                            .id(0L)
                            .userId(0)
                            .tierId(buyTierId)
                            .effectiveTime(currentTime)
                            .endTime(DateUtils.computeTime(productDO, 1, currentTime))
                            .orderId(0L)
                            .tradeNo("tradeNo")
                            .active(false)
                            .freeTrial(0)
                            .build()
            );
            List<UserVipDO> actualResult = tierGroupService.addUserVipDO(currentUserVipDOList,addUserVipDO,productDO,0);
            Assert.assertTrue(actualResult.get(0).getEffectiveTime() - expectedResult.get(0).getEffectiveTime() <10);
        }
    }

    @Test
    public void testAddUserVipDO_IUserVipDAOQueryUserVipInfoReturnsNoItems() {
        UserVipDO userVipDO = UserVipDO.builder()
                .id(0L)
                .userId(0)
                .tierId(0)
                .effectiveTime(0)
                .endTime(0)
                .orderId(0L)
                .tradeNo("tradeNo")
                .active(false)
                .freeTrial(0)
                .build();
        // Setup
        final List<UserVipDO> currentUserVipDOList = Arrays.asList(userVipDO);
        final UserVipDO addUserVipDO = userVipDO;
        final ProductDO productDO = new ProductDO();
        productDO.setId(0);
        productDO.setType(0);
        productDO.setKeyId(0);
        productDO.setBody("body");
        productDO.setPrice(0);
        productDO.setCurrency(0);
        productDO.setSubject("subject");
        productDO.setStatus(0);
        productDO.setMonth(1);
        productDO.setCdate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        productDO.setMdate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(iUserVipDAO.queryUserVipInfo(0, 0,TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Collections.emptyList());
        when(iUserVipDAO.updateUserVipTime(userVipDO)).thenReturn(0);

        // Configure TierService.queryTierById(...).
        final Tier tier = new Tier();
        tier.setTierId(0);
        tier.setRollingDays(0);
        tier.setStorage(0L);
        tier.setMaxDeviceNum(0);
        tier.setStatus(0);
        tier.setSize(0);
        tier.setLevel(0);
        tier.setTierGroupId(0);
        when(tierService.queryTierById(0)).thenReturn(tier);

        // Configure TierGroupDao.getById(...).
        final TierGroupDO tierGroupDO = new TierGroupDO(0,"name", 0, 0, "desc", "tenantId");
        when(tierGroupDao.getById("tenantId", 0)).thenReturn(tierGroupDO);

        // Configure TierActiveConditionService.getById(...).
        final TierActiveConditionDO tierActiveConditionDO = new TierActiveConditionDO(0, "tierUid", false, 0, null, "supportModelNo", "tenantId");
        when(mockTierActiveConditionService.getById("tenantId", 0, "tierUid")).thenReturn(tierActiveConditionDO);

        // Run the test
        final List<UserVipDO> result = tierGroupService.addUserVipDO(currentUserVipDOList, addUserVipDO, productDO,0);

        // Verify the results
        assertFalse(CollectionUtils.size(result) == 2);
        
    }

    @Test
    public void testAddUserVipDO_IUserVipDAOQueryUserVipInfoBeforListReturnsNoItems() {
        UserVipDO userVipDO = UserVipDO.builder()
                .id(0L)
                .userId(0)
                .tierId(0)
                .effectiveTime(0)
                .endTime(0)
                .orderId(0L)
                .tradeNo("tradeNo")
                .active(false)
                .freeTrial(0)
                .build();
        // Setup
        final List<UserVipDO> currentUserVipDOList = Arrays.asList(userVipDO);
        final UserVipDO addUserVipDO = userVipDO;
        final ProductDO productDO = new ProductDO();
        productDO.setId(0);
        productDO.setType(0);
        productDO.setKeyId(0);
        productDO.setBody("body");
        productDO.setPrice(0);
        productDO.setCurrency(0);
        productDO.setSubject("subject");
        productDO.setStatus(0);
        productDO.setMonth(1);
        productDO.setCdate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        productDO.setMdate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final List<UserVipDO> expectedResult = Arrays.asList(userVipDO);

        // Configure IUserVipDAO.queryUserVipInfo(...).
        final List<UserVipDO> userVipDOList = Arrays.asList(userVipDO);
        when(iUserVipDAO.queryUserVipInfo(0, 0, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(userVipDOList);

        when(iUserVipDAO.updateUserVipTime(userVipDO)).thenReturn(0);

        // Configure TierService.queryTierById(...).
        final Tier tier = new Tier();
        tier.setTierId(0);
        tier.setRollingDays(0);
        tier.setStorage(0L);
        tier.setMaxDeviceNum(0);
        tier.setStatus(0);
        tier.setSize(0);
        tier.setLevel(0);
        tier.setTierGroupId(0);
        when(tierService.queryTierById(0)).thenReturn(tier);

        // Configure TierGroupDao.getById(...).
        final TierGroupDO tierGroupDO = new TierGroupDO(0,"name", 0, 0, "desc", "tenantId");
        when(tierGroupDao.getById("tenantId", 0)).thenReturn(tierGroupDO);

        // Configure TierActiveConditionService.getById(...).
        final TierActiveConditionDO tierActiveConditionDO = new TierActiveConditionDO(0, "tierUid", false, 0, null, "supportModelNo", "tenantId");
        when(mockTierActiveConditionService.getById("tenantId", 0, "tierUid")).thenReturn(tierActiveConditionDO);

        // Run the test
        final List<UserVipDO> result = tierGroupService.addUserVipDO(currentUserVipDOList, addUserVipDO, productDO,0);

        // Verify the results
        assertFalse(CollectionUtils.size(result) == 2);
    }

    @Test
    public void testSortUserVipDO() {
        // Setup
//        List<UserVipDO> currentUserVipDOList = Arrays.asList(new UserVipDO(0L, 0, 0, (int)(Instant.now().getEpochSecond())+100, (int)(Instant.now().getEpochSecond())+1000, 0, 0L, "tradeNo", false),
//                new UserVipDO(0L, 0, 1, (int)(Instant.now().getEpochSecond())-100, (int)(Instant.now().getEpochSecond())+200, 0, 0L, "tradeNo", false),
//                new UserVipDO(0L, 0, 0, (int)(Instant.now().getEpochSecond())+300, (int)(Instant.now().getEpochSecond())+1000, 0, 0L, "tradeNo", false),
//                new UserVipDO(0L, 0, 2, (int)(Instant.now().getEpochSecond())+300, (int)(Instant.now().getEpochSecond())+1000, 0, 0L, "tradeNo", false));

        List<UserVipDO> currentUserVipDOList = Arrays.asList(
                UserVipDO.builder()
                .id(0L)
                .userId(0)
                .tierId(0)
                .effectiveTime((int)(Instant.now().getEpochSecond())+100)
                .endTime((int)(Instant.now().getEpochSecond())+1000)
                .orderId(0L)
                .tradeNo("tradeNo")
                .active(false)
                .build(),
        UserVipDO.builder()
                .id(0L)
                .userId(0)
                .tierId(1)
                .effectiveTime((int)(Instant.now().getEpochSecond())-100)
                .endTime((int)(Instant.now().getEpochSecond())+200)
                .orderId(0L)
                .tradeNo("tradeNo")
                .active(false)
                .build(),
                UserVipDO.builder()
                        .id(0L)
                        .userId(0)
                        .tierId(0)
                        .effectiveTime((int)(Instant.now().getEpochSecond())+300)
                        .endTime((int)(Instant.now().getEpochSecond())+1000)
                        .orderId(0L)
                        .tradeNo("tradeNo")
                        .active(false)
                        .build(),
                UserVipDO.builder()
                        .id(0L)
                        .userId(0)
                        .tierId(2)
                        .effectiveTime((int)(Instant.now().getEpochSecond())+300)
                        .endTime((int)(Instant.now().getEpochSecond())+1000)
                        .orderId(0L)
                        .tradeNo("tradeNo")
                        .active(false)
                        .build());



        // Configure TierService.queryTierById(...).
        final Tier tier = new Tier();
        tier.setTierId(0);
        tier.setRollingDays(0);
        tier.setStorage(0L);
        tier.setMaxDeviceNum(0);
        tier.setStatus(0);
        tier.setSize(0);
        tier.setLevel(0);
        tier.setTierGroupId(0);
        when(tierService.queryTierById(0)).thenReturn(tier);

        // Configure TierGroupDao.getById(...).
        TierGroupDO tierGroupDO = new TierGroupDO(0, "name", 0, 3, "desc", "tenantId");
        when(tierGroupDao.getById(any(), eq(0))).thenReturn(tierGroupDO);

        // Configure TierActiveConditionService.getById(...).
        TierActiveConditionDO tierActiveConditionDO = new TierActiveConditionDO(0, "tierUid", false, 1, null, "supportModelNo", "tenantId");
        when(mockTierActiveConditionService.getById(any(), eq(0), any())).thenReturn(tierActiveConditionDO);
        when(mockTierActiveConditionService.getById(any(), eq(1), any())).thenReturn(null);
        when(mockTierActiveConditionService.getById(any(), eq(2), any())).thenReturn(new TierActiveConditionDO(){{
            setTierId(2);
            setActiveType(2);
        }});

        // Run the test
        List<UserVipDO> result = tierGroupService.sortUserVipDO(currentUserVipDOList);
        // Verify the results
        assertEquals(result.get(0).getTierId(), Integer.valueOf(2));

        // Configure TierGroupDao.getById(...).
        currentUserVipDOList = Arrays.asList(UserVipDO.builder()
                .id(0L)
                .userId(0)
                .tierId(0)
                .effectiveTime((int)(Instant.now().getEpochSecond())+100)
                .endTime((int)(Instant.now().getEpochSecond())+1000)
                .orderId(0L)
                .tradeNo("tradeNo")
                .active(false)
                .build(),
                UserVipDO.builder()
                        .id(0L)
                        .userId(0)
                        .tierId(1)
                        .effectiveTime((int)(Instant.now().getEpochSecond())-100)
                        .endTime((int)(Instant.now().getEpochSecond())+200)
                        .orderId(0L)
                        .tradeNo("tradeNo")
                        .active(false)
                        .build());
        tierGroupDO.setTierActiveOrder(2);
        // Run the test
       result = tierGroupService.sortUserVipDO(currentUserVipDOList);
        // Verify the results
        assertEquals(result.get(0).getTierId(), Integer.valueOf(1));

        // Configure TierGroupDao.getById(...).
        currentUserVipDOList = Arrays.asList(UserVipDO.builder()
                        .id(0L)
                        .userId(0)
                        .tierId(0)
                        .effectiveTime((int)(Instant.now().getEpochSecond())+100)
                        .endTime((int)(Instant.now().getEpochSecond())+1000)
                        .orderId(0L)
                        .tradeNo("tradeNo")
                        .active(false)
                        .build(),
                UserVipDO.builder()
                        .id(0L)
                        .userId(0)
                        .tierId(1)
                        .effectiveTime((int)(Instant.now().getEpochSecond())-100)
                        .endTime((int)(Instant.now().getEpochSecond())+200)
                        .orderId(0L)
                        .tradeNo("tradeNo")
                        .active(false)
                        .build());
        tierGroupDO.setTierActiveOrder(1);
        // Run the test
        result = tierGroupService.sortUserVipDO(currentUserVipDOList);
        // Verify the results
        assertEquals(result.get(0).getTierId(), Integer.valueOf(1));

        result = tierGroupService.sortUserVipDO(null);
        // Verify the results
        assertTrue(CollectionUtils.isEmpty(result));
    }



    @Test(expected = BaseException.class)
    @DisplayName("要购买的套餐不存在")
    public void testVerifyTierGroupPriority_WithBuyTierNull() {
        // Arrange
        Integer tierId = 1;
        Integer currentTierId = 2;

        when(tierService.queryTierById(tierId)).thenReturn(null);
        when(tierService.queryTierById(currentTierId)).thenReturn(new Tier());

        tierGroupService.verifyTierGroupPriority(tierId, currentTierId);
    }

    @Test(expected = BaseException.class)
    @DisplayName("当前生效的套餐不存在")
    public void testVerifyTierGroupPriority_WithTierNull() {
        // Arrange
        Integer tierId = 1;
        Integer currentTierId = 2;

        when(tierService.queryTierById(tierId)).thenReturn(new Tier());
        when(tierService.queryTierById(currentTierId)).thenReturn(null);

        tierGroupService.verifyTierGroupPriority(tierId, currentTierId);
    }

    @Test
    @DisplayName("验证要购买的套餐跟当前生效套餐之间的优先级")
    public void testVerifyTierGroupPriority_WithHigherWeight_ReturnsTrue() {
        // Arrange
        Integer tierId = 1;
        Integer currentTierId = 2;

        Tier buyTier = new Tier();
        buyTier.setTierGroupId(1);

        Tier currentTier = new Tier();
        currentTier.setTierGroupId(2);

        when(tierService.queryTierById(tierId)).thenReturn(buyTier);
        when(tierService.queryTierById(currentTierId)).thenReturn(currentTier);


        TierGroupDO buyTierGroup = new TierGroupDO();
        TierGroupDO currentTierGroup = new TierGroupDO();

        when(tierGroupService.getById(null,buyTier.getTierGroupId())).thenReturn(buyTierGroup);
        when(tierGroupService.getById(null,currentTier.getTierGroupId())).thenReturn(currentTierGroup);
        {
            buyTier.setTierType(2);
            currentTier.setTierType(1);
            // Act
            boolean actualResult = tierGroupService.verifyTierGroupPriority(tierId, currentTierId);
            // Assert
            assertTrue(actualResult);
        }
        {
            buyTier.setTierType(1);
            currentTier.setTierType(2);
            // Act
            boolean actualResult = tierGroupService.verifyTierGroupPriority(tierId, currentTierId);
            // Assert
            assertFalse(actualResult);
        }
        {
            buyTier.setTierType(2);
            currentTier.setTierType(2);

            buyTierGroup.setWeight(2);
            currentTierGroup.setWeight(1);
            // Act
            boolean actualResult = tierGroupService.verifyTierGroupPriority(tierId, currentTierId);
            // Assert
            assertTrue(actualResult);
        }

        {
            buyTier.setTierType(2);
            currentTier.setTierType(2);

            buyTierGroup.setWeight(1);
            currentTierGroup.setWeight(2);
            // Act
            boolean actualResult = tierGroupService.verifyTierGroupPriority(tierId, currentTierId);
            // Assert
            assertFalse(actualResult);
        }

        {
            buyTier.setTierType(2);
            currentTier.setTierType(2);

            buyTierGroup.setWeight(2);
            currentTierGroup.setWeight(2);

            buyTier.setLevel(2);
            currentTier.setLevel(1);
            // Act
            boolean actualResult = tierGroupService.verifyTierGroupPriority(tierId, currentTierId);
            // Assert
            assertTrue(actualResult);
        }
    }
}
