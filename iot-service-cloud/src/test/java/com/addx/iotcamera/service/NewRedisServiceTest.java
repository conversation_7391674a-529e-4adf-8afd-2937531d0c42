package com.addx.iotcamera.service;

import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.*;

import java.time.Duration;
import java.util.LinkedHashMap;

import static org.mockito.Mockito.*;


@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class NewRedisServiceTest {

    @InjectMocks
    private NewRedisService cacheRedisService;
    @Mock
    private StringRedisTemplate oldRedisTemplate;
    @Mock
    private StringRedisTemplate businessRedisTemplateClusterClient;
    @Mock
    private ValueOperations oldValueOperations;
    @Mock
    private ValueOperations valueOperations;

    private LinkedHashMap<Object, Object> oldMap = new LinkedHashMap<>();
    private LinkedHashMap<Object, Object> map = new LinkedHashMap<>();

    @Before
    public void before() {
        when(oldRedisTemplate.opsForValue()).thenReturn(oldValueOperations);
        when(businessRedisTemplateClusterClient.opsForValue()).thenReturn(valueOperations);

        doAnswer(it -> {
            map.put(it.getArgument(0), it.getArgument(1));
            return null;
        }).when(valueOperations).set(any(), any());
        doAnswer(it -> {
            map.put(it.getArgument(0), it.getArgument(1));
            return null;
        }).when(valueOperations).set(any(), any(), any());
        when(valueOperations.get(any())).thenAnswer(it -> map.get(it.getArgument(0)));
        when(oldValueOperations.get(any())).thenAnswer(it -> oldMap.get(it.getArgument(0)));
    }

    @Test
    public void test_set() {
        String key = OpenApiUtil.shortUUID();
        String value = OpenApiUtil.shortUUID();
        map.put(key, value);
        Assert.assertEquals(value, map.get(key));

        when(businessRedisTemplateClusterClient.opsForValue()).thenReturn(valueOperations);
        cacheRedisService.set(key, value, Duration.ofMillis(1));
    }

    @Test
    public void test_get() {
        {
            String key = OpenApiUtil.shortUUID();
            String value = OpenApiUtil.shortUUID();
            map.put(key, value);
            String result = cacheRedisService.get(key);
            Assert.assertEquals(value, result);
        }

    }

}
