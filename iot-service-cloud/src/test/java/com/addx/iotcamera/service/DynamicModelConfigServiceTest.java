package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.domain.device.DeviceApplicationDO;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelChanged;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelChangedV2;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelConfig;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelConfigV2;
import com.addx.iotcamera.bean.openapi.PaasTenantInfo;
import com.addx.iotcamera.config.apollo.DeviceDebugConfig;
import com.addx.iotcamera.config.device.DeviceApplicationConfig;
import com.addx.iotcamera.controller.work.DynamicModelConfigController;
import com.addx.iotcamera.dao.DynamicModelConfigDAO;
import com.addx.iotcamera.dao.DynamicModelConfigV2DAO;
import com.addx.iotcamera.dao.device.IDeviceSettingDAO;
import com.addx.iotcamera.dao.factory.DeviceManufactureDao;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.mqtt.enums.EOutputTopicType;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.requests.SetRetainParamRequest;
import com.addx.iotcamera.service.device.DeviceAttributeService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.openapi.PaasTenantConfigService;
import com.addx.iotcamera.testutil.TestHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.context.ApplicationContext;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DynamicModelConfigServiceTest {

    @InjectMocks
    private DynamicModelConfigService dynamicModelConfigServiceInject;
    @InjectMocks
    private DynamicModelConfigV2Service dynamicModelConfigV2ServiceInject;
    @Mock
    private DynamicModelConfigDAO dynamicModelConfigDAO;
    @Mock
    private DynamicModelConfigV2DAO dynamicModelConfigV2DAO;
    @Mock
    private MultipartHttpServletRequest httpRequest;
    @Mock
    private Part uploadPart;
    @Mock
    private HttpServletResponse httpResponse;
    @Mock
    private FactoryDataQueryService factoryDataQueryService;
    @InjectMocks
    private FactoryDataQueryService factoryDataQueryServiceInject;
    @Mock
    private DeviceManufactureDao deviceManufactureDao;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private UserRoleService userRoleService;
    @InjectMocks
    private DeviceSettingService deviceSettingServiceInject;
    @Mock
    private DeviceApplicationConfig deviceApplicationConfig;
    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private IDeviceSettingDAO iDeviceSettingDAO;

    @Mock
    private RedisService redisService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private DynamicModelConfigService dynamicModelConfigService;
    @Mock
    private DynamicModelConfigV2Service dynamicModelConfigV2Service;
    @Mock
    private MqttSender mqttSender;
    @Mock
    private DeviceDebugConfig deviceDebugConfig;
    @Mock
    private ApplicationContext applicationContext;
    @InjectMocks
    private DeviceAttributeService deviceAttributeService;
    @Mock
    private UserService userService;
    @Mock
    private VipService vipService;
    @Mock
    private DeviceModelConfigService deviceModelConfigService;
    @Mock
    private PaasTenantConfigService paasTenantConfigService;

    private DynamicModelConfigController dynamicModelConfigController;

    private TestHelper testHelper, factoryTestHelper;

    private void delegateToRealDAO() {
        testHelper = TestHelper.getInstanceByEnv("test");
        DynamicModelConfigDAO dynamicModelConfigDAO = testHelper.getMapper(DynamicModelConfigDAO.class);
        when(this.dynamicModelConfigDAO.save(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigDAO));
        when(this.dynamicModelConfigDAO.queryByRootKey(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigDAO));
        when(this.dynamicModelConfigDAO.queryByOriginalModelNo(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigDAO));
        when(this.dynamicModelConfigDAO.updateContentByRootKeyAndOriginalModelNo(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigDAO));
        when(this.dynamicModelConfigDAO.updateForceUpdateByRootKeyAndOriginalModelNo(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigDAO));
        when(this.dynamicModelConfigDAO.deleteByRootKeyAndOriginalModelNos(any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigDAO));
        when(this.dynamicModelConfigDAO.deleteByRootKey(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigDAO));
        DynamicModelConfigV2DAO dynamicModelConfigV2DAO = testHelper.getMapper(DynamicModelConfigV2DAO.class);
        when(this.dynamicModelConfigV2DAO.save(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigV2DAO));
        when(this.dynamicModelConfigV2DAO.queryByRootKey(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigV2DAO));
        when(this.dynamicModelConfigV2DAO.queryByModelNo(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigV2DAO));
        when(this.dynamicModelConfigV2DAO.updateContentByRootKeyAndModelNo(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigV2DAO));
        when(this.dynamicModelConfigV2DAO.updateForceUpdateByRootKeyAndModelNo(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigV2DAO));
        when(this.dynamicModelConfigV2DAO.deleteByRootKeyAndModelNos(any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigV2DAO));
        when(this.dynamicModelConfigV2DAO.deleteByRootKey(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigV2DAO));

        when(this.iDeviceSettingDAO.getDeviceSettingsBySerialNumber(any())).thenAnswer(it -> {
            IDeviceSettingDAO realDao = testHelper.getMapper(IDeviceSettingDAO.class);
            return realDao.getDeviceSettingsBySerialNumber(it.getArgument(0));
        });
        TestHelper.HelperConfig factoryTestHelperConfig = TestHelper.HelperConfig.builder().dbConfigName("factory").build();
        factoryTestHelper = TestHelper.getInstanceByEnv("test", factoryTestHelperConfig);

        when(paasTenantConfigService.getPaasTenantInfoBySn(any())).thenReturn(Optional.of(new PaasTenantInfo().setEnableSettingOnlyOldFiled(false)));
    }

    @SneakyThrows
    @Before
    public void before() {
        delegateToRealDAO();

        String settingsJson = "{\"alarmDuration\":5,\"alarmVolume\":100,\"alarmWhenRemoveToggleOn\":false,\"deviceCallToggleOn\":true,\"antiflicker\":50,\"antiflickerSwitch\":1,\"autoLog\":0,\"cooldownInS\":10,\"cooldownUserEnable\":false,\"cryDetect\":0,\"cryDetectLevel\":3,\"devicePersonDetect\":0,\"deviceSupportLanguage\":\"cn,en,ja,de,ru,fr,it,es\",\"doorBellRingKey\":0,\"irThreshold\":1,\"language\":\"cn\",\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"mirrorFlip\":0,\"motionSensitivity\":1,\"motionTrack\":0,\"motionTrackMode\":0,\"needAlarm\":0,\"needVideo\":1,\"nightThresholdLevel\":2,\"nightVisionMode\":0,\"nightVisionSensitivity\":20,\"pir\":1,\"pirSirenDuration\":5,\"recLamp\":1,\"recLen\":10,\"recordingAudioToggleOn\":true,\"serialNumber\":\"02fc15964e3e99f3272fa70f4862455b\",\"supportDoorBellRingKey\":\"\",\"timeZone\":\"Asia/Shanghai\",\"voiceVolume\":75,\"voiceVolumeSwitch\":1,\"whiteLightScintillation\":0}";
        DeviceSettingsDO deviceSettingsDO = JSON.parseObject(settingsJson, DeviceSettingsDO.class);
        when(deviceSettingService.getDeviceSettingsBySerialNumber(any())).thenReturn(deviceSettingsDO);
        when(deviceSettingService.initSetParameterRequest(any(), any(), any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(deviceSettingServiceInject));
        doNothing().doAnswer(AdditionalAnswers.delegatesTo(deviceSettingServiceInject)).when(deviceSettingService).pushVermemqMessage(any(), any(), any());
        DeviceApplicationDO deviceApplicationDO = new DeviceApplicationDO();
        deviceApplicationDO.setDeviceStatusInterval(9);
        when(deviceApplicationConfig.getConfig()).thenReturn(deviceApplicationDO);
        when(deviceDebugConfig.getMtu()).thenReturn(new LinkedHashMap<>());
        when(deviceDebugConfig.getDebugReport()).thenReturn(new LinkedHashMap<>());

        dynamicModelConfigController = new DynamicModelConfigController();
        dynamicModelConfigController.setDynamicModelConfigService(dynamicModelConfigServiceInject);
        dynamicModelConfigController.setDynamicModelConfigV2Service(dynamicModelConfigV2ServiceInject);
        when(httpRequest.getParts()).thenReturn(Arrays.asList(uploadPart));
//        when(uploadPart.getName()).thenReturn("dynamic-model-setting.yml");
        when(uploadPart.getSubmittedFileName()).thenReturn("unit_test_file");
        when(uploadPart.getSize()).thenReturn(108L);
        when(deviceManualService.getOriginalModelNoBySerialNumber(any())).thenReturn("CG122");

        final int userId = 1000_0000;
        String[] modelNos = {"CG6", "CG7", "CB1", "CG122", "MOCK_MODEL_NO"};
        List<UserRoleDO> userRoles = new LinkedList<>();
        for (int i = 0; i < 20; i++) {
            UserRoleDO userRole = UserRoleDO.builder().userId(userId + i).serialNumber("sn" + (userId + i)).build();
            userRoles.add(userRole);
            when(deviceManualService.getOriginalModelNoBySerialNumber(userRole.getSerialNumber()))
                    .thenReturn(modelNos[i % modelNos.length]); // 2/5 不在源型号范围内
            when(deviceSettingService.getDeviceSettingsBySerialNumber(userRole.getSerialNumber()))
                    .thenReturn(i % 4 == 0 ? null : deviceSettingsDO); // 1/4 找不到setting
        }
        when(userRoleService.queryAllAdminUserRoleNum()).thenReturn(userRoles.size());
        when(userRoleService.queryAllAdminUserRoleIterator(any(), anyInt())).thenAnswer(it -> userRoles.iterator());

        doNothing().when(redisService).setDeviceOperationDOWithEmpty(any());
        when(dynamicModelConfigService.queryDynamicSettingFields(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigServiceInject));
        when(dynamicModelConfigV2Service.queryDynamicSettingFields(any())).thenAnswer(AdditionalAnswers.delegatesTo(dynamicModelConfigV2ServiceInject));
        doNothing().when(deviceInfoService).sysReportUpdateUserConfigEnd(any(), anyBoolean(), any());

        Field staticMqttSenderField = VernemqPublisher.class.getDeclaredField("staticMqttSender");
        staticMqttSenderField.setAccessible(true);
        staticMqttSenderField.set(VernemqPublisher.class, mqttSender);

        Answer answer = args -> {
            EOutputTopicType topicType = args.getArgument(0);
            Object arg = args.getArgument(2);
            String str;
            if (arg instanceof byte[]) {
                str = new String((byte[]) arg, "UTF-8");
            } else {
                str = (String) arg;
            }
            JSONObject obj = JSON.parseObject(str);
            if (topicType == EOutputTopicType.SETTING) {
                JSONObject value = obj.getJSONObject("value");
                Assert.assertTrue(value.size() > 1);
                Assert.assertTrue(value.containsKey("pirSensor"));
                Assert.assertNotNull(value.get("pirSensor"));
            } else {
                List<JSONObject> value = obj.getJSONArray("value").toJavaList(JSONObject.class);
                Assert.assertTrue(value.size() > 1);
                JSONObject pirSensor = value.stream().filter(it -> it.getString("name").equals("pirSensor")).findFirst().orElse(null);
                Assert.assertNotNull(pirSensor);
                Assert.assertNotNull(pirSensor.get("value"));
            }
            Assert.assertTrue(str.contains("\"pirSensor\""));
            return null;
        };
        doNothing().doAnswer(answer).when(mqttSender).sendMessage(any(), any(), (Object) any());
        doNothing().doAnswer(answer).when(mqttSender).sendMessage(any(), any(), (Byte[]) any());
        doNothing().doAnswer(answer).when(mqttSender).sendRetainedMessage(any(), any(), (Object) any());
        doNothing().doAnswer(answer).when(mqttSender).sendRetainedMessage(any(), any(), (Byte[]) any());
    }

    @After
    public void after() {
        if (testHelper != null) {
            testHelper.commitAndClose();
        }
        if (factoryTestHelper != null) {
            factoryTestHelper.commitAndClose();
        }
    }

    @DisplayName("上传配置文件成功：增、改、删")
    @SneakyThrows
    @Test
    public void test_updateDynamicModelConfig() {
        // 清空测试数据
        this.dynamicModelConfigDAO.deleteByRootKey("unitTestPirSensor");
        testHelper.commit();

        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/test_case_1.yml");
            return is;
        });
        when(uploadPart.getName()).thenReturn("dynamic-model-setting.yml");
        Result<DynamicModelChanged> result = dynamicModelConfigController.updateDynamicModelConfig(httpRequest, httpResponse);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        Assert.assertEquals(3, result.getData().getSaveNum());
        Assert.assertEquals(0, result.getData().getUpdateNum());
        Assert.assertEquals(0, result.getData().getDeleteNum());
        testHelper.commit();

        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/test_case_2.yml");
            return is;
        });
        when(uploadPart.getName()).thenReturn("dynamic-model-setting.yml");
        Result<DynamicModelChanged> result2 = dynamicModelConfigController.updateDynamicModelConfig(httpRequest, httpResponse);
        Assert.assertEquals(result2.getMsg(), new Integer(0), result2.getResult());
        Assert.assertEquals(1, result2.getData().getSaveNum());
        Assert.assertEquals(1, result2.getData().getUpdateNum());
        Assert.assertEquals(1, result2.getData().getDeleteNum());
        testHelper.commit();

        // 设置为强制更新
        DynamicModelConfig update = new DynamicModelConfig().setForceUpdate(1).setRootKey("unitTestPirSensor").setOriginalModelNo("CG6");
        this.dynamicModelConfigDAO.updateForceUpdateByRootKeyAndOriginalModelNo(update);
        Result<DynamicModelChanged> result3 = dynamicModelConfigController.updateDynamicModelConfig(httpRequest, httpResponse);
        Assert.assertEquals(result3.getMsg(), new Integer(0), result3.getResult());
        Assert.assertEquals(0, result3.getData().getSaveNum());
        Assert.assertEquals(1, result3.getData().getUpdateNum());
        Assert.assertEquals(0, result3.getData().getDeleteNum());
        testHelper.commit();

        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/test_case_empty.yml");
            return is;
        });
        when(uploadPart.getName()).thenReturn("dynamic-model-setting.yml");
        Result<DynamicModelChanged> result4 = dynamicModelConfigController.updateDynamicModelConfig(httpRequest, httpResponse);
        Assert.assertEquals(result4.getMsg(), new Integer(0), result4.getResult());
        Assert.assertEquals(0, result4.getData().getSaveNum());
        Assert.assertEquals(0, result4.getData().getUpdateNum());
        Assert.assertEquals(3, result4.getData().getDeleteNum());
        testHelper.commit();
    }

    @DisplayName("上传配置文件成功V2：增、改、删")
    @SneakyThrows
    @Test
    public void test_updateDynamicModelConfigV2() {
        // 清空测试数据
        this.dynamicModelConfigV2DAO.deleteByRootKey("unitTestPirSensor");
        testHelper.commit();

        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/test_case_1.yml");
            return is;
        });
        when(uploadPart.getName()).thenReturn("other.yml");
        Result<DynamicModelChangedV2> result = dynamicModelConfigController.updateDynamicModelConfigV2(httpRequest, httpResponse);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        Assert.assertEquals(3, result.getData().getSaveNum());
        Assert.assertEquals(0, result.getData().getUpdateNum());
        Assert.assertEquals(0, result.getData().getDeleteNum());
        testHelper.commit();

        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/test_case_2.yml");
            return is;
        });
        when(uploadPart.getName()).thenReturn("other.yml");
        Result<DynamicModelChangedV2> result2 = dynamicModelConfigController.updateDynamicModelConfigV2(httpRequest, httpResponse);
        Assert.assertEquals(result2.getMsg(), new Integer(0), result2.getResult());
        Assert.assertEquals(1, result2.getData().getSaveNum());
        Assert.assertEquals(1, result2.getData().getUpdateNum());
        Assert.assertEquals(1, result2.getData().getDeleteNum());
        testHelper.commit();

        // 设置为强制更新
        DynamicModelConfigV2 update = new DynamicModelConfigV2().setForceUpdate(1).setRootKey("unitTestPirSensor").setModelNo("CG6");
        this.dynamicModelConfigV2DAO.updateForceUpdateByRootKeyAndModelNo(update);
        Result<DynamicModelChangedV2> result3 = dynamicModelConfigController.updateDynamicModelConfigV2(httpRequest, httpResponse);
        Assert.assertEquals(result3.getMsg(), new Integer(0), result3.getResult());
        Assert.assertEquals(0, result3.getData().getSaveNum());
        Assert.assertEquals(1, result3.getData().getUpdateNum());
        Assert.assertEquals(0, result3.getData().getDeleteNum());
        testHelper.commit();

        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/test_case_empty.yml");
            return is;
        });
        when(uploadPart.getName()).thenReturn("other.yml");
        Result<DynamicModelChangedV2> result4 = dynamicModelConfigController.updateDynamicModelConfigV2(httpRequest, httpResponse);
        Assert.assertEquals(result4.getMsg(), new Integer(0), result4.getResult());
        Assert.assertEquals(0, result4.getData().getSaveNum());
        Assert.assertEquals(0, result4.getData().getUpdateNum());
        Assert.assertEquals(3, result4.getData().getDeleteNum());
        testHelper.commit();
    }

    @DisplayName("上传配置文件格式错误")
    @SneakyThrows
    @Test
    public void test_updateDynamicModelConfig_format_error() {
        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/test_case_err.yml");
            return is;
        });
        when(uploadPart.getName()).thenReturn("dynamic-model-setting.yml");
        Result result = dynamicModelConfigController.updateDynamicModelConfig(httpRequest, httpResponse);
        Assert.assertEquals(result.getMsg(), new Integer(10), result.getResult());
        testHelper.commit();
    }

    @DisplayName("上传配置文件格式错误V2")
    @SneakyThrows
    @Test
    public void test_updateDynamicModelConfigV2_format_error() {
        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/test_case_err.yml");
            return is;
        });
        when(uploadPart.getName()).thenReturn("other.yml");
        Result result = dynamicModelConfigController.updateDynamicModelConfigV2(httpRequest, httpResponse);
        Assert.assertEquals(result.getMsg(), new Integer(10), result.getResult());
        testHelper.commit();
    }

    @DisplayName("上传配置文件失败")
    @SneakyThrows
    @Test
    public void test_updateDynamicModelConfig_stream_error() {
        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/test_case_1.yml");
            is.close(); // 故意关闭流，触发报错
            return is;
        });
        when(uploadPart.getName()).thenReturn("dynamic-model-setting.yml");
        Result result = dynamicModelConfigController.updateDynamicModelConfig(httpRequest, httpResponse);
        Assert.assertEquals(result.getMsg(), new Integer(-1), result.getResult());
        testHelper.commit();
    }

    @DisplayName("上传配置文件失败V2")
    @SneakyThrows
    @Test
    public void test_updateDynamicModelConfigV2_stream_error() {
        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/test_case_1.yml");
            is.close(); // 故意关闭流，触发报错
            return is;
        });
        when(uploadPart.getName()).thenReturn("other.yml");
        Result result = dynamicModelConfigController.updateDynamicModelConfigV2(httpRequest, httpResponse);
        Assert.assertEquals(result.getMsg(), new Integer(-1), result.getResult());
        testHelper.commit();
    }

    @DisplayName("根据sn查询DynamicModelConfig")
    @SneakyThrows
    @Test
    public void test_queryDynamicSettingFields() {
        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/example.yml");
            return is;
        });
        when(uploadPart.getName()).thenReturn("dynamic-model-setting.yml");
        Result result = dynamicModelConfigController.updateDynamicModelConfig(httpRequest, httpResponse);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        testHelper.commit();

//        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG122");
        when(deviceManualService.getOriginalModelNoBySerialNumber(any())).thenReturn("CG122");
        // 查询成功
        Map<String, Object> expectMap = dynamicModelConfigDAO.queryByOriginalModelNo("CG122")
                .stream().collect(Collectors.toMap(it -> it.getRootKey(), it -> JSON.parse(it.getContent())));
        Map<String, Object> map = dynamicModelConfigServiceInject.queryDynamicSettingFields("sn_test1");
        Assert.assertEquals(expectMap, map);

        // 查询失败
        when(dynamicModelConfigDAO.queryByOriginalModelNo("CG122")).thenThrow(new RuntimeException("模拟异常,测试catch分支"));
        Map<String, Object> map2 = dynamicModelConfigServiceInject.queryDynamicSettingFields("sn_test1");
        Assert.assertEquals(Collections.emptyMap(), map2);
//        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG122");
        when(deviceManualService.getOriginalModelNoBySerialNumber(any())).thenReturn("CG122");

        Map<String, Object> map3 = dynamicModelConfigServiceInject.queryDynamicSettingFields("");
        Assert.assertEquals(Collections.emptyMap(), map3);
        Map<String, Object> map4 = dynamicModelConfigServiceInject.queryDynamicSettingFields(null);
        Assert.assertEquals(Collections.emptyMap(), map4);
    }

    @DisplayName("根据sn查询DynamicModelConfigV2")
    @SneakyThrows
    @Test
    public void test_queryDynamicSettingFieldsV2() {
        when(uploadPart.getInputStream()).thenAnswer(it -> {
            InputStream is = ConfigHelper.loadConfig("classpath:dynamic_model_config/example.yml");
            return is;
        });
        when(uploadPart.getName()).thenReturn("other.yml");
        Result result = dynamicModelConfigController.updateDynamicModelConfigV2(httpRequest, httpResponse);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        testHelper.commit();

        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG122");
        // 查询成功
        Map<String, Object> expectMap = dynamicModelConfigV2DAO.queryByModelNo("CG122")
                .stream().collect(Collectors.toMap(it -> it.getRootKey(), it -> JSON.parse(it.getContent())));
        Map<String, Object> map = dynamicModelConfigV2ServiceInject.queryDynamicSettingFields("sn_test1");
        Assert.assertEquals(expectMap, map);

        // 查询失败
        when(dynamicModelConfigV2DAO.queryByModelNo("CG122")).thenThrow(new RuntimeException("模拟异常,测试catch分支"));
        Map<String, Object> map2 = dynamicModelConfigV2ServiceInject.queryDynamicSettingFields("sn_test1");
        Assert.assertEquals(Collections.emptyMap(), map2);
//        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG122");
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG122");

        Map<String, Object> map3 = dynamicModelConfigV2ServiceInject.queryDynamicSettingFields("");
        Assert.assertEquals(Collections.emptyMap(), map3);
        Map<String, Object> map4 = dynamicModelConfigV2ServiceInject.queryDynamicSettingFields(null);
        Assert.assertEquals(Collections.emptyMap(), map4);
    }


    @Test
    public void test_PushSettingMsg() {
        when(applicationContext.getBean(DeviceSettingService.class)).thenReturn(deviceSettingService);
        when(deviceSettingService.initSetParameterRequest(any(), any(), any())).thenReturn(new SetRetainParamRequest());
        when(deviceModelConfigService.queryRowDeviceModelByModelNo(any())).thenReturn(null);

        deviceSettingServiceInject.pushSettingMsg("123");
        deviceSettingServiceInject.pushSettingMsg("");


    }

    @Test
    public void test_getPir() {
        int pir = deviceAttributeService.getPir(0, null,null);
        Assert.assertEquals(pir, 0);

        when(deviceSettingService.initSetParameterRequest(any(), any(), any())).thenReturn(new SetRetainParamRequest());
        when(deviceInfoService.checkIf4GDeviceHasOfficialSimCard(any())).thenReturn(true);

        int pir1 = deviceAttributeService.getPir(0, new DeviceSettingsDO(),null);
        Assert.assertEquals(pir1, 0);



    }

    @Test
    public void test_pirSwitch() {
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(1);
        when(deviceInfoService.checkIf4GDeviceHasOfficialSimCard(any())).thenReturn(true);
        when(vipService.isVipDevice(anyInt(), anyString())).thenReturn(false);

        deviceSettingServiceInject.pirSwitch(null, new DeviceSettingsDO(),null);

        deviceSettingServiceInject.pirSwitch(new DeviceSettingsDO().setPir(1), new DeviceSettingsDO(),null);


    }
}
