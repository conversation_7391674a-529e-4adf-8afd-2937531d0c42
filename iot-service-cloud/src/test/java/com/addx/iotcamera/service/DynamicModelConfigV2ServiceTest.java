package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.device.SingleDeviceCodecDo;
import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelChangedV2;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelConfigV2;
import com.addx.iotcamera.dao.DynamicModelConfigV2DAO;
import com.addx.iotcamera.dao.device.DeviceCodecDAO;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DynamicModelConfigV2ServiceTest {

    public static final String SN_1 = "sn_1";
    @Mock
    private DynamicModelConfigV2DAO dynamicModelConfigV2DAO;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private DeviceCodecDAO deviceCodecDAO;

    @InjectMocks
    private DynamicModelConfigV2Service dynamicModelConfigV2Service;

    @Test
    public void test_publishDynamicSetting() {
        when(userRoleService.queryAllAdminUserRoleIterator(anyString(), anyInt())).thenAnswer(it -> {
            int batchSize = it.getArgument(1);
            List<UserRoleDO> list = new LinkedList<>();
            for (int i = 0; i < batchSize; i++) {
                UserRoleDO userRole = new UserRoleDO();
                userRole.setUserId(1000 + i);
                userRole.setAdminId(userRole.getUserId());
                userRole.setSerialNumber("sn_" + i);
            }
            return list.iterator();
        });
        DynamicModelChangedV2 changed = new DynamicModelChangedV2();
        changed.setModelNos(new HashSet<>(Arrays.asList("sn_1", "sn_3", "sn_5", "sn_7")));
        when(deviceSettingService.getDeviceSettingsBySerialNumber("sn_1")).thenReturn(new DeviceSettingsDO());
        when(deviceSettingService.getDeviceSettingsBySerialNumber("sn_5")).thenReturn(new DeviceSettingsDO());
        dynamicModelConfigV2Service.publishDynamicSetting(changed, 10);

    }

    @Test
    public void test_sendSettingIncludeCodec() {
        when(deviceSettingService.getDeviceSettingsBySerialNumber(SN_1)).thenReturn(buildDeviceSettingsDO());
        //queryByModelNo is null ,进入exception
        dynamicModelConfigV2Service.sendSettingIncludeCodec(SN_1,new HashMap<String,Object>(){{
            put("abc","abc");
        }});

        when(deviceManualService.getModelNoBySerialNumber(SN_1)).thenReturn("CG1");

        List<DynamicModelConfigV2> list = buildDynamicModelConfigV2List();

        when( dynamicModelConfigV2DAO.queryByModelNo("CG1")).thenReturn(list);
        dynamicModelConfigV2Service.sendSettingIncludeCodec(SN_1,new HashMap<String,Object>(){{
            put("abc","abc");
        }});

    }

    @NotNull
    private static List<DynamicModelConfigV2> buildDynamicModelConfigV2List() {
        List<DynamicModelConfigV2> list = new ArrayList<>();
        DynamicModelConfigV2 dynamicModelConfigV2 = new DynamicModelConfigV2();
        dynamicModelConfigV2.setRootKey("codecProfile");
        dynamicModelConfigV2.setContent("{}");
        list.add(dynamicModelConfigV2);
        return list;
    }

    private DeviceSettingsDO buildDeviceSettingsDO() {
        DeviceSettingsDO deviceSettingsDO = new DeviceSettingsDO();
        deviceSettingsDO.setPir(1);
        deviceSettingsDO.setIrThreshold(1);
        deviceSettingsDO.setNeedAlarm(1);
        deviceSettingsDO.setNeedVideo(1);
        return deviceSettingsDO;
    }

    @Test
    public void test_resetSingleDeviceCodec() {
        Map<String, Object> fields = new HashMap<>();
        List<SingleDeviceCodecDo> list = new ArrayList<>();
        SingleDeviceCodecDo singleDeviceCodecDo = new SingleDeviceCodecDo();
        singleDeviceCodecDo.setRes("auto");
        list.add(singleDeviceCodecDo);

        when(deviceCodecDAO.queryDeviceCodec(SN_1)).thenReturn(list);
        dynamicModelConfigV2Service.resetSingleDeviceCodec(SN_1,fields,DynamicModelConfigV2Service.CODEC_PROFILE);

    }

    @Test
    public void test_dealWithCodecProfile() {
        when(deviceCodecDAO.queryListByModelNo("CG1")).thenReturn(new HashSet<>());
        Map<String, Set<String>>  map = new HashMap<>();
        dynamicModelConfigV2Service.dealWithCodecProfile(map, SN_1,"CG1",
                new HashMap<>(), DynamicModelConfigV2Service.CODEC_PROFILE);
        //will not set  modelSerialNumbersMap again
        dynamicModelConfigV2Service.dealWithCodecProfile(map, SN_1,"CG1",
                new HashMap<>(), DynamicModelConfigV2Service.CODEC_PROFILE);

        //contains SN test
        when(deviceCodecDAO.queryListByModelNo("CG1")).thenReturn(new HashSet<String>(){{
            add(SN_1);
        }});
        dynamicModelConfigV2Service.dealWithCodecProfile( new HashMap<>(), SN_1,"CG1",
                new HashMap<>(), DynamicModelConfigV2Service.CODEC_PROFILE);
    }

}
