package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.LibraryDonateRequest;
import com.addx.iotcamera.bean.app.LibraryRequest;
import com.addx.iotcamera.bean.app.RemoveLibraryRequest;
import com.addx.iotcamera.bean.db.*;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.library.LibraryCountDay;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.init.CopyWriteInit;
import com.addx.iotcamera.bean.response.library.LibraryEventView;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.bean.tuple.Tuples;
import com.addx.iotcamera.bean.video.VideoTagSummary;
import com.addx.iotcamera.config.LibraryDonateConfig;
import com.addx.iotcamera.config.S3;
import com.addx.iotcamera.config.device.ModelAiEventConfig;
import com.addx.iotcamera.dao.IShareDAO;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.dao.library.LibraryDonateDAO;
import com.addx.iotcamera.dao.library.QuestionBackDAO;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.enums.VideoQuestion;
import com.addx.iotcamera.enums.VideoTagQuestionEnums;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryStatusDAO;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingVideoSliceDAO;
import com.addx.iotcamera.testutil.BizHelper;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.JsonUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.addx.iotcamera.util.ShardingJdbcUtil;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.enums.VideoTagEnums;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.apache.commons.lang3.StringUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LibraryServiceTest {

    private TestHelper testHelper;
    @InjectMocks
    LibraryService libraryService;
    @Mock
    QuestionBackDAO questionBackDAO;
    @Mock
    StringUtils stringUtils;
    @Mock
    LibraryDonateDAO libraryDonateDAO;
    @Mock
    LibraryDonateConfig libraryDonateConfig;
    @Mock
    UserRoleService userRoleService;
    @Mock
    IShardingLibraryDAO shardingLibraryDAO;
    @Mock
    IShardingVideoSliceDAO shardingVideoSliceDAO;
    @Mock
    IShardingLibraryStatusDAO shardingLibraryStatusDAO;
    @Mock
    IShareDAO shareDAO;
    @Mock
    DeviceModelService deviceModelService;
    @Mock
    DeviceService deviceService;
    @Mock
    S3Service s3Service;
    @Mock
    ModelAiEventConfig modelAiEventConfig;
    @Mock
    IUserDAO userDAO;
    @Mock
    NotificationService notificationService;
    @Mock
    UserService userService;
    @Mock
    VideoService videoService;
    @Mock
    S3 s3;
    @Mock
    LibraryStatusService libraryStatusService;
    @Mock
    DeviceInfoService deviceInfoService;
    @Mock
    DeviceConfigService deviceConfigService;
    @Mock
    AIService aiService;
    @Mock
    RedisService redisService;
    @Mock
    private DeviceModelEventService deviceModelEventService;
    @Mock
    private DeviceManualService deviceManuService;
    @Mock
    private UserTierDeviceService userTierDeviceService;
    @Mock
    private VipService paasVipService;
    @Mock
    private VideoSearchService videoSearchService;

    @Mock
    FactoryDataQueryService factoryDataQueryService;

    @Mock
    private DeviceManualService deviceManualService;

    private static int idCount = 0;
    private static final int LIBRARY_ID_NO_TAG = ++idCount;
    private static final int LIBRARY_ID_HAS_PERSON_TAG = ++idCount;
    private static final int LIBRARY_ID_HAS_PACKAGE_EXIST_TAG = ++idCount;
    private static final int LIBRARY_ID_HAS_PERSON_AND_PACKAGE_TAG = ++idCount;
    private static final int LIBRARY_ID_ALL_TAG = ++idCount;

    private static final Integer userId = 32;

    private Map<Integer, DeviceLibraryViewDO> libraryMap = new LinkedHashMap<>();
    private Map<Integer, Set<Integer>> libraryId2HasTagCodes = new LinkedHashMap<>();
    private boolean isMock;

    @Before
    public void init() throws Exception {
        when(deviceConfigService.getVideoRollingDaysOrNoPlan(any(), any(), any()))
                .thenReturn(Tuples.createTuple(30, System.currentTimeMillis() / 1000 + 365 * 24 * 60 * 60));
        when(redisService.generateLibraryId(any())).thenReturn(123);
        when(deviceManuService.getModelNoBySerialNumber(any())).thenReturn("model1");
        when(deviceModelEventService.queryDeviceModelEvent(any())).thenReturn(ImmutableSet.of("person", "pet", "vehicle", "package", "bird"));

        when(s3Service.deleteObject(any())).thenAnswer(it -> it.getArgument(0) == null ? 0 : ((Collection) it.getArgument(0)).size());
        libraryService.setIsRealDeleteSlice(true);
        DeviceLibraryViewDO library;

        library = new DeviceLibraryViewDO();
        library.setId(LIBRARY_ID_NO_TAG);
        library.setTraceId(UUID.randomUUID().toString());
        library.setTags("");
        libraryMap.put(library.getId(), library);
        libraryId2HasTagCodes.put(LIBRARY_ID_NO_TAG, ImmutableSet.of());

        library = new DeviceLibraryViewDO();
        library.setId(LIBRARY_ID_HAS_PERSON_TAG);
        library.setTraceId(UUID.randomUUID().toString());
        library.setTags(VideoTagEnums.PERSON.getName());
        libraryMap.put(library.getId(), library);
        libraryId2HasTagCodes.put(LIBRARY_ID_HAS_PERSON_TAG, ImmutableSet.of(
                VideoTagQuestionEnums.PERSON_RECOGNITION_ERROR.getCode()));

        library = new DeviceLibraryViewDO();
        library.setId(LIBRARY_ID_HAS_PACKAGE_EXIST_TAG);
        library.setTraceId(UUID.randomUUID().toString());
        library.setTags(VideoTagEnums.PACKAGE_EXIST.getName());
        libraryMap.put(library.getId(), library);
        libraryId2HasTagCodes.put(LIBRARY_ID_HAS_PERSON_AND_PACKAGE_TAG, ImmutableSet.of(
                VideoTagQuestionEnums.PERSON_RECOGNITION_ERROR.getCode(),
//                VideoTagQuestionEnums.PACKAGE_PICK_UP_RECOGNITION_ERROR.getCode()));
                VideoTagQuestionEnums.PACKAGE_RECOGNITION_ERROR.getCode()));

        library = new DeviceLibraryViewDO();
        library.setId(LIBRARY_ID_HAS_PERSON_AND_PACKAGE_TAG);
        library.setTraceId(UUID.randomUUID().toString());
        library.setTags(VideoTagEnums.PERSON.getName() + "," + VideoTagEnums.PACKAGE_PICK_UP.getName());
        libraryMap.put(library.getId(), library);
        libraryId2HasTagCodes.put(LIBRARY_ID_HAS_PACKAGE_EXIST_TAG, ImmutableSet.of(
//                VideoTagQuestionEnums.PACKAGE_EXIST_RECOGNITION_ERROR.getCode()));
                VideoTagQuestionEnums.PACKAGE_RECOGNITION_ERROR.getCode()));

        library = new DeviceLibraryViewDO();
        library.setId(LIBRARY_ID_ALL_TAG);
        library.setTraceId(UUID.randomUUID().toString());
        library.setTags(VideoTagEnums.PERSON.getName()
                        + "," + VideoTagEnums.PACKAGE_DROP_OFF.getName()
                        + "," + VideoTagEnums.PET.getName()
                        + "," + VideoTagEnums.VEHICLE.getName()
//                + "," + VideoTagEnums.CRY.getName()
        );
        libraryMap.put(library.getId(), library);
        libraryId2HasTagCodes.put(LIBRARY_ID_ALL_TAG, ImmutableSet.of(
                VideoTagQuestionEnums.PERSON_RECOGNITION_ERROR.getCode(),
//                VideoTagQuestionEnums.PACKAGE_DROP_OFF_RECOGNITION_ERROR.getCode(),
                VideoTagQuestionEnums.PACKAGE_RECOGNITION_ERROR.getCode(),
                VideoTagQuestionEnums.PET_RECOGNITION_ERROR.getCode(),
                VideoTagQuestionEnums.VEHICLE_RECOGNITION_ERROR.getCode()));

        for (DeviceLibraryViewDO libraryDo : libraryMap.values()) {
            when(shardingLibraryDAO.selectLibraryViewByUserIdsAndTraceId(Collections.singleton(1), libraryDo.getTraceId())).thenReturn(libraryDo);
        }
        when(userRoleService.getUserRoleDOByUserIdAndSerialNumber(any(), any())).thenReturn(new UserRoleDO());

        ShardingJdbcUtil shardingJdbcUtil = new ShardingJdbcUtil();
        shardingJdbcUtil.setRedisService(redisService);
        shardingJdbcUtil.setProfile("staging-eu");
        shardingJdbcUtil.setShardingLibraryStatusDAO(shardingLibraryStatusDAO);
        shardingJdbcUtil.afterPropertiesSet();

        testHelper = TestHelper.getInstanceByLocal();
        mockDao(true);

        CopyWrite copyWrite = new CopyWrite();
        CopyWriteInit.readStreamCsv("gitconfig/copywrite/iot.csv", copyWrite);
        List<String> titleKeys = Stream.of(VideoTagQuestionEnums.values()).map(it -> it.getTitleKey()).collect(Collectors.toList());
        BizHelper.mockMultiLanguageConfig(copyWrite.getConfig(), titleKeys);
        VideoQuestion.initQuestionBackText(copyWrite);
        log.info("");
    }

    private void mockDao(boolean flag) {
        if ((this.isMock = flag)) {
            when(questionBackDAO.insertQuestionBack(any())).thenReturn(1);
        } else {
            /* 代理给真实对象 */
            QuestionBackDAO questionBackDAO0 = testHelper.getMapper(QuestionBackDAO.class);
            when(questionBackDAO.insertQuestionBack(any())).thenAnswer(AdditionalAnswers.delegatesTo(questionBackDAO0));
            when(questionBackDAO.updateQuestionBackById(any())).thenAnswer(AdditionalAnswers.delegatesTo(questionBackDAO0));
            when(questionBackDAO.queryQuestionBackByLibraryIdAndUserId(any(), any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(questionBackDAO0));
        }
    }

    @After
    public void after() {
        if (!isMock) {
            this.testHelper.commitAndClose();
        }
    }

    @Test
    public void test_getOtherQueryLibraryTagNameList() {
        when(deviceService.queryBindHistory(anyInt())).thenReturn(Collections.singletonList(BindOperationTb.builder().serialNumber("sn_01").build()));
        when(shareDAO.getDeviceShareeSuccessHistory(anyInt())).thenReturn(Collections.singletonList(new ShareStatusDO() {{
            setSerialNumber("sn_01");
        }}));

        when(deviceModelService.queryDeviceModelCategoryBySerialNumber(anyString())).thenReturn(2);

        when(deviceInfoService.getDeviceSupport(anyString())).thenReturn(CloudDeviceSupport.builder().supportDeviceCall(1).build());

        Set<String> otherQueryLibraryTagNameList = libraryService.getOtherQueryLibraryTagNameList(1);
        Assert.assertTrue(otherQueryLibraryTagNameList.size() != 0);
    }

    @Test
    public void test_getOtherQueryLibraryTagNameList2() {
        Integer userId = new Random().nextInt(10000_0000);
        List<String> sns = Arrays.asList(OpenApiUtil.shortUUID(), OpenApiUtil.shortUUID());
        {
            when(deviceService.queryBindHistory(userId)).thenReturn(Arrays.asList());
            when(shareDAO.getDeviceShareeSuccessHistory(userId)).thenReturn(Arrays.asList());
            Set<String> set = libraryService.getOtherQueryLibraryTagNameList(userId);
            Assert.assertEquals(Collections.emptySet(), set);
        }
        when(deviceService.queryBindHistory(userId)).thenReturn(sns.stream()
                .map(sn -> BindOperationTb.builder().serialNumber(sn).build()).collect(Collectors.toList()));
        when(shareDAO.getDeviceShareeSuccessHistory(userId)).thenReturn(sns.stream().map(sn -> new ShareStatusDO() {{
            setSerialNumber(sn);
        }}).collect(Collectors.toList()));
        {
            when(deviceInfoService.getDeviceSupport(anyString())).thenReturn(CloudDeviceSupport.builder().supportDeviceCall(null).build());
            when(deviceModelService.queryDeviceModelCategoryBySerialNumber(anyString())).thenReturn(null);
            Set<String> set = libraryService.getOtherQueryLibraryTagNameList(userId);
            Assert.assertEquals(Collections.emptySet(), set);
        }
        when(deviceInfoService.getDeviceSupport(anyString())).thenReturn(CloudDeviceSupport.builder().supportDeviceCall(1).build());
        when(deviceModelService.queryDeviceModelCategoryBySerialNumber(anyString())).thenReturn(DeviceModelCategoryEnums.DOORBELL.getCode());
        Set<String> set = libraryService.getOtherQueryLibraryTagNameList(userId);
        Assert.assertEquals(ImmutableSet.of("DEVICE_CALL", "DOORBELL_PRESS", "DOORBELL_REMOVE"), set);
    }

    @Test
    public void test_getLibraryEventSummary() {
        modelAiEventConfig.eventSummary = new HashMap();
        when(modelAiEventConfig.getParentEvent()).thenReturn(new HashMap<String, List<String>>() {{
            put("PERSON", new LinkedList<>());
            put(EReportEvent.DOORBELL_PRESS.name(), new LinkedList<>());
            put(EReportEvent.DOORBELL_REMOVE.name(), new LinkedList<>());
            put(EReportEvent.DEVICE_CALL.name(), new LinkedList<>());
        }});

        LibraryEventView libraryEventView = new LibraryEventView();

        LibraryEventDO libraryEventDO = new LibraryEventDO();
        libraryEventDO.setLibraryIds("1");
        libraryEventDO.setTraceIds(new HashSet<>(Arrays.asList("trace_01")));

        Map<String, LibraryTb> traceIdToLibraryTb = new HashMap<>();

        libraryService.getLibraryEventSummary(userId, libraryEventView, libraryEventDO, traceIdToLibraryTb, new LinkedHashMap<>());

        libraryEventDO.setTags("PERSON,VEHICLE,");
        libraryService.getLibraryEventSummary(userId, libraryEventView, libraryEventDO, traceIdToLibraryTb, new LinkedHashMap<>());

        libraryEventDO.setDoorbellTags(EReportEvent.DOORBELL_PRESS.name() + "," + EReportEvent.DOORBELL_REMOVE.name());
        libraryEventDO.setDeviceCallEventTags(EReportEvent.DEVICE_CALL.name());
        traceIdToLibraryTb.put("trace_01", LibraryTb.builder().period(BigDecimal.valueOf(1.0)).type(1).timestamp(0).build());
        libraryService.getLibraryEventSummary(userId, libraryEventView, libraryEventDO, traceIdToLibraryTb, new LinkedHashMap<>());

        traceIdToLibraryTb.put("trace_01", LibraryTb.builder().period(BigDecimal.valueOf(1.0)).type(1).timestamp(Integer.MAX_VALUE).receivedAllSlice(true).build());
        libraryService.getLibraryEventSummary(userId, libraryEventView, libraryEventDO, traceIdToLibraryTb, new LinkedHashMap<>());

    }

    @Test
    public void test_initListUserLibraryViewDO() {
        List<UserLibraryViewDO> listUserLibraryViewDO = new LinkedList<>();
        UserLibraryViewDO userLibraryViewDO = new UserLibraryViewDO();
        userLibraryViewDO.setId(1);
        userLibraryViewDO.setTraceId("trace_01");
        userLibraryViewDO.setUserId(1);
        userLibraryViewDO.setAdminId(1);
        listUserLibraryViewDO.add(userLibraryViewDO);

        Map<String, LibraryStatusTb> traceIdToStatusMap = new HashMap<>();
        traceIdToStatusMap.put("trace_01", LibraryStatusTb.builder().userId(1).marked(1).missing(1).doorbellTags(EReportEvent.DOORBELL_PRESS.name() + "," + EReportEvent.DOORBELL_REMOVE.name()).build());

        when(userDAO.selectUserByUserIds(any())).thenReturn(Collections.singletonList(new User()));
        when(userService.queryUserById(any())).thenReturn(new User());

        libraryService.initListUserLibraryViewDO(listUserLibraryViewDO, traceIdToStatusMap);
        Assert.assertTrue(!CollectionUtils.isEmpty(listUserLibraryViewDO));

        userLibraryViewDO.setTags("PERSON");
        libraryService.initListUserLibraryViewDO(listUserLibraryViewDO, traceIdToStatusMap);
        Assert.assertTrue(!CollectionUtils.isEmpty(listUserLibraryViewDO));

        userLibraryViewDO.setTags(null);
        userLibraryViewDO.setDoorbellTags(EReportEvent.DOORBELL_PRESS.name() + "," + EReportEvent.DOORBELL_REMOVE.name());
        libraryService.initListUserLibraryViewDO(listUserLibraryViewDO, traceIdToStatusMap);
        Assert.assertTrue(!CollectionUtils.isEmpty(listUserLibraryViewDO));

        when(notificationService.getVideoReportEventMsgContent(any(), any())).thenReturn("aaa");
        userLibraryViewDO.setTags(null);
        userLibraryViewDO.setDoorbellTags(EReportEvent.DOORBELL_PRESS.name() + "," + EReportEvent.DOORBELL_REMOVE.name());
        libraryService.initListUserLibraryViewDO(listUserLibraryViewDO, traceIdToStatusMap);
        Assert.assertTrue(!CollectionUtils.isEmpty(listUserLibraryViewDO));
        Assert.assertTrue(!CollectionUtils.isEmpty(listUserLibraryViewDO.get(0).getEventInfoList()));

        userLibraryViewDO.setTags(null);
        userLibraryViewDO.setDoorbellTags(null);
        userLibraryViewDO.setDeviceCallEventTag(EReportEvent.DEVICE_CALL.name());
        libraryService.initListUserLibraryViewDO(listUserLibraryViewDO, traceIdToStatusMap);
        Assert.assertTrue(!CollectionUtils.isEmpty(listUserLibraryViewDO));

        when(notificationService.getVideoReportEventMsgContent(any(), any())).thenReturn("aaa");
        userLibraryViewDO.setTags(null);
        userLibraryViewDO.setDoorbellTags(null);
        userLibraryViewDO.setDeviceCallEventTag(EReportEvent.DEVICE_CALL.name());
        libraryService.initListUserLibraryViewDO(listUserLibraryViewDO, traceIdToStatusMap);
        Assert.assertTrue(!CollectionUtils.isEmpty(listUserLibraryViewDO));
        Assert.assertTrue(!CollectionUtils.isEmpty(listUserLibraryViewDO.get(0).getEventInfoList()));

    }

    @Test
    public void test_updateLibraryEvent() {
        boolean updateSuccess = libraryService.updateLibraryEvent(null, null);
        Assert.assertTrue(updateSuccess == false);

        when(shardingLibraryDAO.updateLibraryInfoByUserIdAndTraceId(any(), any())).thenReturn(1);
        updateSuccess = libraryService.updateLibraryEvent(null, null);
        Assert.assertTrue(updateSuccess == true);
    }

    @Test
    public void test_selectLibrary() {
        when(libraryStatusService.queryLibraryStatusList(any())).thenReturn(Collections.singletonList(LibraryStatusTb.builder().libraryId(1).traceId("traceId_01").build()));

        LibraryRequest libraryRequest = new LibraryRequest();
        libraryRequest.setUserId(1);
        libraryService.selectLibrary(libraryRequest);

        libraryRequest.setStartTimestamp(1L);
        libraryService.selectLibrary(libraryRequest);

        libraryRequest.setStartTimestamp(0L);
        libraryService.selectLibrary(libraryRequest);

        libraryRequest.setStartTimestamp(null);
        libraryRequest.setEndTimestamp(1L);
        libraryService.selectLibrary(libraryRequest);

        libraryRequest.setStartTimestamp(null);
        libraryRequest.setEndTimestamp(0L);
        libraryService.selectLibrary(libraryRequest);


        libraryRequest.setStartTimestamp(1L);
        libraryRequest.setEndTimestamp(1L);
        libraryService.selectLibrary(libraryRequest);

        libraryRequest.setStartTimestamp(0L);
        libraryRequest.setEndTimestamp(1L);
        libraryService.selectLibrary(libraryRequest);

        libraryRequest.setStartTimestamp(1L);
        libraryRequest.setEndTimestamp(0L);
        libraryService.selectLibrary(libraryRequest);

        libraryRequest.setStartTimestamp(0L);
        libraryRequest.setEndTimestamp(0L);
        libraryService.selectLibrary(libraryRequest);

        libraryRequest.setVideoEvent(System.currentTimeMillis());
        libraryService.selectLibrary(libraryRequest);
        libraryService.selectLibrary(libraryRequest);
        Assert.assertTrue(libraryRequest.getStartTimestamp() > 0 && libraryRequest.getEndTimestamp() > 0);
    }

    @Test
    public void test_selectLibraryEvent() {
        List<LibraryEventDO> mockLibraryEventDOList = new LinkedList<>();
        mockLibraryEventDOList.add(new LibraryEventDO() {{
            setVideoEvent(1L);
            setLibraryIds("1");
            setTraceIds(new HashSet<>(Arrays.asList("traceId_01")));
            setAdminIds(Collections.singleton(1));
        }});
        when(libraryStatusService.queryLibraryEventList(any())).thenReturn(mockLibraryEventDOList);

        mockLibraryEventDOList.get(0).setStartTime(1);
        mockLibraryEventDOList.get(0).setEndTime(1);
        libraryService.selectLibraryEvent(new LibraryRequest() {{
            setUserId(1);
        }});

        mockLibraryEventDOList.get(0).setStartTime(0);
        mockLibraryEventDOList.get(0).setEndTime(0);
        libraryService.selectLibraryEvent(new LibraryRequest() {{
            setUserId(1);
        }});

        mockLibraryEventDOList.get(0).setStartTime(1);
        mockLibraryEventDOList.get(0).setEndTime(0);
        libraryService.selectLibraryEvent(new LibraryRequest() {{
            setUserId(1);
        }});

        mockLibraryEventDOList.get(0).setStartTime(0);
        mockLibraryEventDOList.get(0).setEndTime(1);
        libraryService.selectLibraryEvent(new LibraryRequest() {{
            setUserId(1);
        }});
    }

    @Test
    public void test_deleteLibraries() throws Exception {
        RemoveLibraryRequest removeLibraryRequest = new RemoveLibraryRequest();

        removeLibraryRequest.setLibraryList(Collections.singletonList(1));
        libraryService.deleteLibraries(1, removeLibraryRequest);

        when(shardingLibraryDAO.selectSingleLibraryByUserIdAndTraceId(anyInt(), any())).thenReturn(new UserLibraryViewDO() {{
            setId(11);
            setTraceId("trace_01");
            setShareUserIds("2");
            setAdminId(1);
        }});
        when(shardingLibraryStatusDAO.selectLibraryStatus(any())).thenReturn(LibraryStatusTb.builder().userId(0).libraryId(1).traceId("trace_01").build());

        when(shardingLibraryDAO.deleteLibraryByUserIdAndTraceId(anyInt(), any())).thenReturn(0);
        libraryService.deleteLibraries(1, removeLibraryRequest);

        when(shardingLibraryDAO.deleteLibraryByUserIdAndTraceId(anyInt(), any())).thenReturn(0);
        libraryService.deleteLibraries(1, removeLibraryRequest);

        when(shardingLibraryDAO.deleteLibraryByUserIdAndTraceId(anyInt(), any())).thenReturn(1);
        libraryService.deleteLibraries(1, removeLibraryRequest);

        when(shardingLibraryDAO.deleteLibraryByUserIdAndTraceId(anyInt(), any())).thenReturn(1);
        libraryService.deleteLibraries(1, removeLibraryRequest);

        removeLibraryRequest.setTraceIdList(Collections.singletonList("trace_01"));
        libraryService.deleteLibrariesByTraceId(1, removeLibraryRequest);

        libraryService.deleteLibrariesByTraceId(1, removeLibraryRequest);

        when(shardingLibraryDAO.selectSingleLibraryByUserIdAndTraceId(1, "trace_01")).thenReturn(new UserLibraryViewDO() {{
            setTraceId("trace_01");
            setShareUserIds("1");
            setAdminId(1);
        }});
        libraryService.deleteLibrariesByTraceId(1, removeLibraryRequest);

        when(shardingLibraryDAO.selectSingleLibraryByUserIdAndTraceId(1, "trace_01")).thenReturn(new UserLibraryViewDO() {{
            setTraceId("trace_01");
            setShareUserIds("2");
            setAdminId(2);
        }});
        libraryService.deleteLibrariesByTraceId(1, removeLibraryRequest);

        when(shardingLibraryDAO.deleteLibraryByUserIdAndTraceId(anyInt(), any())).thenReturn(0);
        libraryService.deleteLibrariesByTraceId(1, removeLibraryRequest);

        when(shardingLibraryDAO.deleteLibraryByUserIdAndTraceId(anyInt(), any())).thenReturn(0);
        libraryService.deleteLibrariesByTraceId(1, removeLibraryRequest);

        when(shardingLibraryDAO.deleteLibraryByUserIdAndTraceId(anyInt(), any())).thenReturn(1);
        libraryService.deleteLibrariesByTraceId(1, removeLibraryRequest);

        when(shardingLibraryDAO.deleteLibraryByUserIdAndTraceId(anyInt(), any())).thenReturn(1);
        libraryService.deleteLibrariesByTraceId(1, removeLibraryRequest);
    }

    @Test
    public void test_useQueryResult() throws Exception {
        when(libraryStatusService.selectLibraryStatusByTraceIdAndUserId(any(), anyInt())).thenReturn(LibraryStatusTb.builder().userId(0).libraryId(1).traceId("trace_01").build());
        when(shardingLibraryStatusDAO.selectLibraryStatus(any())).thenReturn(LibraryStatusTb.builder().userId(0).libraryId(1).traceId("trace_01").build());
        when(shardingLibraryDAO.selectSingleLibraryByUserIdAndTraceId(anyInt(), any())).thenReturn(new UserLibraryViewDO() {{
            setTraceId("trace_01");
            setShareUserIds("2");
            setAdminId(2);
            setEventInfo(JsonUtil.toJson(Arrays.asList(new AiEvent() {{
                setEventObject(AiObjectEnum.PERSON);
                setEventType(AiObjectActionEnum.EXIST);
            }})));
            setAiEdgeEventInfo(JsonUtil.toJson(Arrays.asList(new AiEvent() {{
                setEventObject(AiObjectEnum.PET);
                setEventType(AiObjectActionEnum.EXIST);
            }})));
        }});

        libraryService.selectLibraryViewByTraceId(1, "trace_01");

        libraryService.selectSingleLibrary(1, null, 1);
        libraryService.selectSingleLibrary(1, "trace_01", 1);

        test_selectLibrary();

        test_selectLibraryEvent();
    }

    @Test
    public void test_insert() throws Exception {
        when(deviceConfigService.getVideoRollingDays(any(), any(), any())).thenReturn(new Tuple2(1, System.currentTimeMillis() / 1000 + 1000));

        AtomicInteger redisId = new AtomicInteger();
        when(redisService.setIfAbsent(anyString(), anyString(), anyLong(), any())).then(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                redisId.set(Integer.valueOf(invocation.getArgument(1)));
                return true;
            }
        });
        when(redisService.increNum(anyString())).then(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                return Long.valueOf(redisId.incrementAndGet());
            }
        });

        ShardingJdbcUtil shardingJdbcUtil = new ShardingJdbcUtil();
        shardingJdbcUtil.setRedisService(redisService);
        shardingJdbcUtil.setProfile("staging-eu");
        shardingJdbcUtil.afterPropertiesSet();

        boolean insertSuccess = libraryService.insertLibrary(new InsertLibraryRequest(), null, null);
        Assert.assertTrue(insertSuccess);
    }

    @Test
    public void test_deleteSliceVideoUrl() {
        List<String> urls = Arrays.asList("https://newBucket1.google/video", "https://bucket1.aws/video", "");
        when(videoService.queryVideoUrlByAdminUserIdAndTraceIds(anyInt(), any())).thenAnswer(it -> new LinkedList<>(urls));
        Assert.assertEquals(3, libraryService.deleteSliceVideoUrl(1, Arrays.asList("traceId1")));
        when(s3.getLookBackBuckets()).thenReturn(new HashSet<>(Arrays.asList("newBucket1")));
        Assert.assertEquals(2, libraryService.deleteSliceVideoUrl(1, Arrays.asList("traceId1")));
    }

    @Test
    public void test_queryAllSnsAndTagsByUserId() throws Exception {
        when(shardingLibraryStatusDAO.queryVideoTagSummaryByUserId(anyInt())).thenReturn(Arrays.asList(new VideoTagSummary() {{
            setSn("sn_01");
        }}, new VideoTagSummary() {{
            setSn("sn_02");
        }}));
        LibraryStatusService libraryStatusService = new LibraryStatusService();
        Field shardingLibraryStatusDAOField = LibraryStatusService.class.getDeclaredField("shardingLibraryStatusDAO");
        shardingLibraryStatusDAOField.setAccessible(true);
        shardingLibraryStatusDAOField.set(libraryStatusService, shardingLibraryStatusDAO);
        final Map<String, VideoTagSummary> sn2TagSummary = libraryStatusService.querySn2VideoTagSummaryByUserId(0);
        Assert.assertTrue(sn2TagSummary != null);
    }

    @Test
    public void test_queryLibraryCountDay() {
        // Mock library status service response
        when(libraryStatusService.queryLibraryCount(any())).thenReturn(1);

        // Test case 1: Without timezone
        LibraryRequest request = new LibraryRequest();
        request.setUserId(1);
        request.setStartTimestamp(1625097600L); // 2021-07-01 00:00:00
        request.setEndTimestamp(1625270400L); // 2021-07-03 00:00:00
        
        List<LibraryCountDay> result = libraryService.queryLibraryCountDay(request);
        Assert.assertEquals(3, result.size()); // Should return 3 days
        Assert.assertEquals(1625097600L, (long)result.get(0).getDateTimestamp());
        Assert.assertEquals(1625184000L, (long)result.get(1).getDateTimestamp()); // Next day
        Assert.assertEquals(1625270400L, (long)result.get(2).getDateTimestamp()); // Third day
        
        // Test case 2: With timezone - Test DST transition
        LibraryRequest requestWithTz = new LibraryRequest();
        requestWithTz.setUserId(1);
        requestWithTz.setApp(new com.addx.iotcamera.bean.app.AppInfo());
        requestWithTz.getApp().setTimeZone("America/New_York");
        
        // March 14, 2021 - DST starts (spring forward)
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("America/New_York"));
        calendar.set(2021, Calendar.MARCH, 14, 0, 0, 0);
        long start = calendar.getTimeInMillis() / 1000L;
        requestWithTz.setStartTimestamp(start); // 2021-03-14 00:00:00 EST
        calendar.set(2021, Calendar.MARCH, 15, 0, 0, 0);
        long end = calendar.getTimeInMillis() / 1000L;
        requestWithTz.setEndTimestamp(end); // 2021-03-15 00:00:00 EDT        
        List<LibraryCountDay> resultWithDst = libraryService.queryLibraryCountDay(requestWithTz);
        Assert.assertEquals(2, resultWithDst.size());
        // Verify timestamps align with timezone boundaries
        Assert.assertEquals(start, (long)resultWithDst.get(0).getDateTimestamp()); // Start of March 14 EST
        Assert.assertEquals((long)end, (long)resultWithDst.get(1).getDateTimestamp()); // Start of March 15 EDT
        
        // November 7, 2021 - DST ends (fall back) 
        calendar.set(2021, Calendar.NOVEMBER, 7, 0, 0, 0);
        start = calendar.getTimeInMillis() / 1000L;
        requestWithTz.setStartTimestamp(start); // 2021-11-07 00:00:00 EDT
        calendar.set(2021, Calendar.NOVEMBER, 8, 0, 0, 0);
        end = calendar.getTimeInMillis() / 1000L;
        requestWithTz.setEndTimestamp(end); // 2021-11-08 00:00:00 EST
        
        List<LibraryCountDay> resultWithDstEnd = libraryService.queryLibraryCountDay(requestWithTz);
        Assert.assertEquals(2, resultWithDstEnd.size());
        Assert.assertEquals(start, (long)resultWithDstEnd.get(0).getDateTimestamp()); // Start of Nov 7 EDT
        Assert.assertEquals(end, (long)resultWithDstEnd.get(1).getDateTimestamp()); // Start of Nov 8 EST
    }

    @Test
    public void test_queryUserLibraryViewByIdOrTraceId() {
        Set<Integer> userIds = new HashSet<>();
        userIds.add(1);
        when(shardingLibraryStatusDAO.selectLibraryStatus(any())).thenReturn(LibraryStatusTb.builder().traceId("trace_01").build());
        when(shardingLibraryDAO.selectLibraryViewByUserIdsAndTraceId(Collections.singleton(anyInt()), any())).thenReturn(new DeviceLibraryViewDO());
        when(shardingLibraryStatusDAO.selectAdminsByUserId(1)).thenReturn(userIds);
        when(shardingLibraryDAO.selectLibraryViewByUserIdsAndTraceId(Collections.singleton(1), "trace_01")).thenReturn(new DeviceLibraryViewDO());

        DeviceLibraryViewDO deviceLibraryViewDO = libraryService.queryUserLibraryViewByIdOrTraceId(1, null, 1);
        Assert.assertTrue(deviceLibraryViewDO != null);

        deviceLibraryViewDO = libraryService.queryUserLibraryViewByIdOrTraceId(1, "trace_01", 1);
        Assert.assertTrue(deviceLibraryViewDO != null);
    }

    @Test
    public void test_libraryStatus() throws Exception {
        LibraryStatusService libraryStatusService = new LibraryStatusService();
        libraryStatusService.setRedisService(redisService);
        libraryStatusService.setDeviceManualService(deviceManualService);
        libraryStatusService.setFactoryDataQueryService(factoryDataQueryService);

        Field shardingLibraryStatusDAOField = LibraryStatusService.class.getDeclaredField("shardingLibraryStatusDAO");
        shardingLibraryStatusDAOField.setAccessible(true);
        shardingLibraryStatusDAOField.set(libraryStatusService, shardingLibraryStatusDAO);
        Field videoSearchServiceField = LibraryStatusService.class.getDeclaredField("videoSearchService");
        videoSearchServiceField.setAccessible(true);
        videoSearchServiceField.set(libraryStatusService, videoSearchService);

        when(shardingLibraryStatusDAO.deleteLibraryStatus(anyInt(), any())).thenReturn(1);
        int res = libraryStatusService.deleteLibraryStatus(0, null, Collections.singletonList("trace_01"));
        Assert.assertTrue(res > 0);

        when(shardingLibraryStatusDAO.insertLibraryStatus(anyInt(), anyInt(), anyInt(), any(), any(), anyInt(), any(), anyInt(), anyInt(), anyInt(), any(), anyString())).thenReturn(1);
        res = libraryStatusService.insertLiraryStatus(0, Collections.singletonList(0), "trace_01", null, null, null, null, null, null, null);
        Assert.assertTrue(res > 0);

        libraryStatusService.deleteLibraryStatus(1);
        verify(shardingLibraryStatusDAO).deleteLibraryStatus(eq(1), any());
    }

    @Test
    public void test_saveLibraryDonate() {
        LibraryDonateRequest request = new LibraryDonateRequest();
        request.setId(1L);
        request.setTraceId("11");
        request.setCode(Collections.singleton(1));
        when(shardingLibraryDAO.selectLibraryViewByUserIdsAndTraceId(anySet(), anyString())).thenReturn(new DeviceLibraryViewDO());
        libraryService.saveLibraryDonate(request, 1);
    }

    @Test
    public void test_queryLibraryDonateList() {
        List<LibraryDonateDO> libraryDonateDOList = Collections.singletonList(new LibraryDonateDO());
        Map<String, List<LibraryDonateDO>> map = new HashMap<>();
        map.put("1", libraryDonateDOList);
        when(libraryDonateConfig.getReason()).thenReturn(map);
        libraryService.queryLibraryDonateList(1, "1", 1L);
    }

    @Test
    public void test_library_not_exist() {
        when(libraryStatusService.selectLibraryStatusByTraceIdAndUserId("1", 1)).thenReturn(new LibraryStatusTb());
        when(libraryStatusService.queryAdminIdByUserIdAndTraceId(1, "1")).thenReturn(null);
        try {
            libraryService.selectSingleLibrary(1, "1", 1);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof BaseException);
        }
    }

    @Test
    public void test_inflateSliceList() {
        UserLibraryViewDO library = new UserLibraryViewDO();
        library.setTraceId(OpenApiUtil.shortUUID());
        List<UserLibraryViewDO> libraryList = Arrays.asList(library);

        Map<String, List<VideoSliceDO>> traceId2SliceList = new LinkedHashMap<>();
        VideoSliceDO videoSlice = new VideoSliceDO();
        videoSlice.setVideoUrl("videoUrl");
        videoSlice.setOrder(0);
        videoSlice.setIsLast(false);
        videoSlice.setS3EventTime(System.currentTimeMillis());
        videoSlice.setFileSize(10000);
        videoSlice.setPeriod(new BigDecimal("2000"));
        traceId2SliceList.put(library.getTraceId(), Arrays.asList(videoSlice));

        libraryService.inflateSliceList(libraryList, traceId2SliceList);
        Assert.assertEquals(1, library.getSliceList().size());
    }

}
