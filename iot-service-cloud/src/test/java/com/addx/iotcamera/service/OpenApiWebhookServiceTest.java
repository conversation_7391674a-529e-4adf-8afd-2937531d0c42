package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.DeviceManualDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.openapi.OpenApiDeviceConfig;
import com.addx.iotcamera.bean.openapi.PaasTenantInfo;
import com.addx.iotcamera.bean.video.VideoMsgType;
import com.addx.iotcamera.config.PaasTenantConfigTest;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.enums.UserType;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.openapi.OpenApiUserService;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class OpenApiWebhookServiceTest {

    @InjectMocks
    private OpenApiWebhookService openApiWebhookService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private S3Service s3Service;
    @Mock
    private PaasTenantConfig paasTenantConfig;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private UserService userService;
    @Mock
    private MqSender mqSender;
    @Mock
    private OpenApiUserService openApiUserService;
    @Mock
    private OpenApiConfigService openApiConfigService;

    @Before
    public void before() {
        PaasTenantConfigTest.mockPaasTenantConfig(this.paasTenantConfig);
        when(openApiUserService.queryThirdUserIds(anyList())).thenAnswer(it -> {
            List<Integer> userIds = it.getArgument(0);
            return userIds.stream().map(id -> "third_" + id).collect(Collectors.toList());
        });
    }

    @Test
    public void test_callWebhook() {
//        TestHelper testHelper = TestHelper.getInstanceByEnv("staging");
        Integer userId = 799;
        String sn = "****************";

        User user = new User();
        user.setId(799);
        user.setTenantId("netvue");
        user.setAccountId("testAccount_18613");
        user.setThirdUserId("8a1e01db06e048f7");
        user.setType(UserType.THIRD.getCode());
        when(userService.queryUserById(anyInt())).thenAnswer((Answer<User>) invocation -> user);
        when(openApiUserService.queryThirdUserIds(any())).thenAnswer(it -> {
            List<String> thirdUserIds = it.getArgument(0);
            return thirdUserIds.stream().map(it2 -> it2.hashCode()).collect(Collectors.toList());
        });
        when(openApiConfigService.queryBySnAndTenantId(anyString(), anyString())).thenReturn(new OpenApiDeviceConfig());

        doNothing().when(mqSender).send(anyString(), anyString(), any());
        openApiWebhookService.callWebhookForDeviceBind(userId, sn, "Asia/Shanghai", new DeviceManualDO());
        openApiWebhookService.callWebhookForDeviceUnbind(userId, sn, Collections.emptyList());
        openApiWebhookService.callWebhookForDevicePir(user, sn, OpenApiUtil.shortUUID(), Collections.emptyList(), 0L, OpenApiDeviceConfig.MotionType.video.name());
    }

    public void testCallWebhook(String sn, List<Integer> userIds, Supplier<Boolean> callFunction) {
        Integer adminId = userIds.get(0);
        {
            when(userRoleService.getAdminUserBySn(eq(sn))).thenThrow(new RuntimeException(""));
            Assert.assertEquals(false, callFunction.get());
        }
        {
            when(userRoleService.getAdminUserBySn(eq(sn))).thenReturn(Result.Failure(""));
            Assert.assertEquals(false, callFunction.get());
        }
        {
            when(userRoleService.getAdminUserBySn(eq(sn))).thenReturn(new Result<>(new User() {{
                setId(adminId);
                setType(UserType.REGISTER.getCode());
                setTenantId("guard");
            }}));
            Assert.assertEquals(false, callFunction.get());
        }
        {
            when(userRoleService.getAdminUserBySn(eq(sn))).thenReturn(new Result<>(new User() {{
                setId(adminId);
                setType(UserType.THIRD.getCode());
                setTenantId("longse");
            }}));
            Assert.assertEquals(false, callFunction.get());
        }
        when(userRoleService.getAdminUserBySn(eq(sn))).thenReturn(new Result<>(new User() {{
            setId(adminId);
            setType(UserType.THIRD.getCode());
            setTenantId("netvue");
        }}));
        when(userRoleService.findAllUsersForDevice(eq(sn))).thenReturn(new ArrayList<>(userIds));
        Assert.assertEquals(true, callFunction.get());
    }

    @Test
    public void test_callWebhookForDeviceLowBattery() {
        Integer adminId = new Random().nextInt(10000_0000);
        Integer userId = new Random().nextInt(10000_0000);
        String sn = OpenApiUtil.shortUUID();
        testCallWebhook(sn, Arrays.asList(adminId, userId), () -> openApiWebhookService.callWebhookForDeviceLowBattery(sn));
    }


    @Test
    public void test_callWebhookForSaasAiResult() {
        final VideoMsgType videoMsgType = VideoMsgType.AI_EVENT_RESULT;
        final String tenantId = OpenApiUtil.shortUUID();
        final String deviceSn = OpenApiUtil.shortUUID();
        final String traceId = OpenApiUtil.shortUUID();
        final String thirdUserId = OpenApiUtil.shortUUID();
        final Integer ownerId = RandomUtils.nextInt(1000_0000, 9000_0000);
        final JSONObject data = new JSONObject();

        data.fluentPut("deviceSn", null).fluentPut("ownerId", ownerId);
        Assert.assertFalse(openApiWebhookService.callWebhookForSaasAiResult(videoMsgType, traceId, data));
        data.fluentPut("deviceSn", deviceSn).fluentPut("ownerId", null);
        Assert.assertFalse(openApiWebhookService.callWebhookForSaasAiResult(videoMsgType, traceId, data));
        data.fluentPut("deviceSn", null).fluentPut("ownerId", null);
        Assert.assertFalse(openApiWebhookService.callWebhookForSaasAiResult(videoMsgType, traceId, data));
        data.fluentPut("deviceSn", deviceSn).fluentPut("ownerId", ownerId);

        when(userService.queryUserById(ownerId)).thenReturn(null);
        Assert.assertFalse(openApiWebhookService.callWebhookForSaasAiResult(videoMsgType, traceId, data));
        when(userService.queryUserById(ownerId)).thenReturn(new User() {{
            setTenantId(tenantId);
            setThirdUserId(thirdUserId);
        }});
        when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableAiResultWebhook(false));
        Assert.assertFalse(openApiWebhookService.callWebhookForSaasAiResult(videoMsgType, traceId, data));

        when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableAiResultWebhook(true));
        Assert.assertTrue(openApiWebhookService.callWebhookForSaasAiResult(videoMsgType, traceId, data));
    }

    @Test
    public void test_callWebhookForDeviceCall() {
        final String tenantId = OpenApiUtil.shortUUID();
        final String sn = OpenApiUtil.shortUUID();
        final String traceId = OpenApiUtil.shortUUID();
        final Integer userId = RandomUtils.nextInt(1000_0000, 9000_0000);

        when(userService.queryUserById(userId)).thenReturn(new User() {{
            setId(userId);
            setThirdUserId("third:" + userId);
            setTenantId(tenantId);
        }});

        when(userRoleService.queryUserRolesBySn(sn)).thenReturn(null);
        Assert.assertFalse(openApiWebhookService.callWebhookForDeviceCall(sn, traceId));
        when(userRoleService.queryUserRolesBySn(sn)).thenReturn(new UserRoleService.UserRoles(null, Arrays.asList()));
        Assert.assertFalse(openApiWebhookService.callWebhookForDeviceCall(sn, traceId));
        when(userRoleService.queryUserRolesBySn(sn)).thenReturn(new UserRoleService.UserRoles(userId, Arrays.asList()));

        when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableWebhook(false));
        Assert.assertFalse(openApiWebhookService.callWebhookForDeviceCall(sn, traceId));
        when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableWebhook(true));

        when(userRoleService.queryUserRolesBySn(sn, false)).thenReturn(new UserRoleService.UserRoles(userId, Arrays.asList()));
        Assert.assertTrue(openApiWebhookService.callWebhookForDeviceCall(sn, traceId));
    }

    @Test
    public void test_callWebhookForDeviceBind() {
        final String tenantId = OpenApiUtil.shortUUID();
        final String sn = OpenApiUtil.shortUUID();
        final Integer userId = RandomUtils.nextInt(1000_0000, 9000_0000);
        String timeZone = "Asia/Shanghai";
        when(userService.queryUserById(userId)).thenReturn(new User() {{
            setId(userId);
            setTenantId(tenantId);
        }});
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableWebhook(false));
            Assert.assertFalse(openApiWebhookService.callWebhookForDeviceBind(userId, sn, timeZone, new DeviceManualDO()));
        }
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableWebhook(true));
            Assert.assertTrue(openApiWebhookService.callWebhookForDeviceBind(userId, sn, timeZone, new DeviceManualDO()));
        }
        {
            when(deviceManualService.getDeviceManualBySerialNumber(sn)).thenReturn(null);
            Assert.assertTrue(openApiWebhookService.callWebhookForDeviceBind(userId, sn, timeZone, null));
        }
        {
            when(deviceManualService.getDeviceManualBySerialNumber(sn)).thenReturn(new DeviceManualDO() {{
                setUserSn("userSn");
                setModelNo("modelNo");
                setDisplayModelNo("displayModelNo");
            }});
            Assert.assertTrue(openApiWebhookService.callWebhookForDeviceBind(userId, sn, timeZone, null));
        }
    }

    @Test
    public void test_callWebhookForDeviceUnbind() {
        final String tenantId = OpenApiUtil.shortUUID();
        final String sn = OpenApiUtil.shortUUID();
        final Integer userId = RandomUtils.nextInt(1000_0000, 9000_0000);
        when(userService.queryUserById(userId)).thenReturn(new User() {{
            setId(userId);
            setTenantId(tenantId);
        }});
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableWebhook(false));
            Assert.assertFalse(openApiWebhookService.callWebhookForDeviceUnbind(userId, sn, Arrays.asList()));
        }
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableWebhook(true));
            Assert.assertTrue(openApiWebhookService.callWebhookForDeviceUnbind(userId, sn, Arrays.asList()));
        }
    }

    @Test
    public void test_callWebhookForDevicePir() {
        final String tenantId = OpenApiUtil.shortUUID();
        final String traceId = OpenApiUtil.shortUUID();
        final String sn = OpenApiUtil.shortUUID();
        final Integer userId = RandomUtils.nextInt(1000_0000, 9000_0000);
        final User user = new User() {{
            setId(userId);
            setTenantId(tenantId);
        }};
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableWebhook(false));
            Assert.assertFalse(openApiWebhookService.callWebhookForDevicePir(user, sn, traceId, Arrays.asList(), 0, ""));
        }
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableWebhook(true));
            Assert.assertTrue(openApiWebhookService.callWebhookForDevicePir(user, sn, traceId, Arrays.asList(), 0, ""));
        }
    }

}
