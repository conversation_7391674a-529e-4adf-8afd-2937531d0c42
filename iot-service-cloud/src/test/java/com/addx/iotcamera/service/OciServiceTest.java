package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.param.OciCredentials;
import com.addx.iotcamera.bean.param.OciPar;
import com.addx.iotcamera.bean.param.OciParams;
import com.addx.iotcamera.bean.video.StoreBucket;
import com.addx.iotcamera.config.OciConfig;
import com.addx.iotcamera.constants.VideoConstants;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.amazonaws.services.s3.AmazonS3;
import com.oracle.bmc.objectstorage.ObjectStorageClient;
import com.oracle.bmc.objectstorage.model.ListObjects;
import com.oracle.bmc.objectstorage.model.PreauthenticatedRequest;
import com.oracle.bmc.objectstorage.responses.CreatePreauthenticatedRequestResponse;
import com.oracle.bmc.objectstorage.responses.GetNamespaceResponse;
import com.oracle.bmc.objectstorage.responses.GetPreauthenticatedRequestResponse;
import com.oracle.bmc.objectstorage.responses.ListObjectsResponse;
import io.reactivex.Observable;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({PreauthenticatedRequest.class, ListObjects.class, ObjectStorageClient.class,OciService.class})
public class OciServiceTest {

    @Mock
    private Executor executor;
    @Mock
    private TimeTicker timeTicker;

    @Before
    public void init() {
        doNothing().doAnswer(it -> {
            ((Runnable) it.getArgument(0)).run();
            return null;
        }).when(executor).execute(any());
        when(timeTicker.readMillis()).thenAnswer(it -> System.currentTimeMillis());

        MockitoAnnotations.initMocks(this);
    }

    @DisplayName("local环境的oci参数和api都测一遍")  // 平常不需要运行，注释掉
    //@Test
    public void testOciApiByLocal() {
        testOciApiByEnv("local");
    }

    @DisplayName("所有环境的oci参数和api都测一遍")  // 平常不需要运行，注释掉
//    @Test
    public void testAllEnv() {
        for (final String env : Arrays.asList("staging-eu", "staging-us", "pre-eu", "pre-us", "prod-eu", "prod-us")) {
            testOciApiByEnv(env);
        }
    }

    @DisplayName("staging-us环境的oci参数和api都测一遍")  // 平常不需要运行，注释掉
//    @Test
    public void testStagingUsEnv() {
        testOciApiByEnv("staging-us");
    }

    private static class MockRedisService extends RedisService {
        @Getter
        private Map<String, Set<String>> redisMap = new LinkedHashMap<>();


        @Override
        public Long setAdd(String key, Collection<String> values) {
            redisMap.computeIfAbsent(key, k -> new LinkedHashSet<>()).addAll(values);
            return Long.valueOf(values.size());
        }

        @Override
        public Long setRemove(String key, Collection<String> values) {
            redisMap.computeIfAbsent(key, k -> new LinkedHashSet<>()).removeAll(values);
            return Long.valueOf(values.size());
        }

        @Override
        public Observable<List<String>> setScan(String key, String match, int count) {
            final Set<String> values = redisMap.computeIfAbsent(key, k -> new LinkedHashSet<>());
            return Observable.fromIterable(values).buffer(count);
        }

        @Override
        public void set(String key, String value, Integer timeOut) {
            redisMap.put(key, new HashSet<>(Arrays.asList(value)));
        }

        @Override
        public String get(String key) {
            final Set<String> set = redisMap.get(key);
            return set != null && !set.isEmpty() ? set.stream().findFirst().get() : null;
        }

        @Override
        public Set<String> setMembers(String key) {
            return redisMap.computeIfAbsent(key, k -> new LinkedHashSet<>());
        }
    }

    public OciService createOciServiceByEnv(String env) {
        TestHelper testHelper = TestHelper.getInstanceByEnv(env);
        final OciConfig ociConfig = testHelper.getConfig().getObject("oci", OciConfig.class);
        ociConfig.setEnableCreateCredential(true);
        OciService ociService = new OciService().setOciConfig(ociConfig).setListObjectsBatchSize(2);
        ociService.setExecutor(executor);
        ociService.setTimeTicker(timeTicker);
        ociService.setRedisService(new MockRedisService());
        ociService.init();
        return ociService;
    }

    public void testOciApiByEnv(String env) {
        final OciService ociService = createOciServiceByEnv(env);
        for (final List<StoreBucket> storeBuckets : ociService.getOciConfig().getLookBackDays2Buckets().values()) {
            for(StoreBucket bucket : storeBuckets) {
                test_oci_api(ociService, bucket);
            }
        }
        test_createTempCredential(ociService);
        test_deleteParByRedis(ociService);
        ociService.close();
    }

    public static void test_oci_api(OciService ociService, StoreBucket bucket) {
        String keyPrefix = "test_oci_api/" + OpenApiUtil.shortUUID();
        final List<String> keys = IntStream.range(0, 3).mapToObj(it -> keyPrefix + it).collect(Collectors.toList());
        String content = UUID.randomUUID().toString();
        // putObject
        for (final String key : keys) {
            ociService.putObject(bucket, key, content);
        }
        // listObject
        final List<String> keys2 = ociService.listObjects(bucket, keyPrefix);
        Assert.assertTrue(keys.containsAll(keys2));
        // getObject
        for (final String key : keys) {
            final String content2 = ociService.getObject(bucket, key);
            Assert.assertEquals(content, content2);
            // copyObject
            final String desKey = VideoConstants.VIDEO_BACKUP_PREFIX + "/" + key;
            ociService.copyObject(bucket, key, new StoreBucket().setBucket(ociService.getOciConfig().getBucket()).setRegion(ociService.getDefaultRegion()), desKey);
        }
        // generatePresignedUrl
        for (final String key : keys) {
            final URL url = ociService.getObjectUrl(bucket, key);
            /*
            final URL presignedUrl = ociService.preSignUrlByPar(url.toString(), TimeUnit.MINUTES.toMillis(2880));
            Assert.assertNotNull(presignedUrl);
            try {
                final String content2 = IOUtils.toString(presignedUrl, StandardCharsets.UTF_8.displayName());
                Assert.assertEquals(content, content2);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            */
            // url解析
            test_parseOciUrlStatic(ociService, bucket, key, url.toString());
            // 测试:使用oci兼容s3客户端生成的预签名url
            final URL presignUrl2 = ociService.preSignUrl(url.toString(), TimeUnit.MINUTES.toMillis(2880));
            Assert.assertNotNull(presignUrl2);
            try {
                final String content2 = IOUtils.toString(presignUrl2, StandardCharsets.UTF_8.displayName());
                Assert.assertEquals(content, content2);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public static void test_parseOciUrlStatic(OciService ociService, StoreBucket bucket, String key, String ociUrl) {
        final OciService.OciUrlParts parts = OciService.parseOciUrl(ociUrl);
        Assert.assertNotNull(parts);
        Assert.assertEquals(bucket.getRegion(), parts.getRegion());
        Assert.assertEquals(ociService.getNamespace(bucket.getRegion()), parts.getNamespace());
        Assert.assertEquals(bucket.getBucket(), parts.getBucket());
        Assert.assertEquals(key, parts.getKey());
        Assert.assertEquals(ociUrl, OciService.getObjectUrl(parts).toString());
    }

//    @Test
//    public void test_getRootUrl(){
//        Assert.assertEquals("https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/test/o/", ociService.getRootUrl(getAnyBucket()));
//    }

    @DisplayName("生成预签名上传url，下载")
    //@Test
    public void test_createTempCredential() {
        final OciService ociService = createOciServiceByEnv("local");
        test_createTempCredential(ociService);
    }

    public void test_createTempCredential(OciService ociService) {
        final long startTime = System.currentTimeMillis();
        when(timeTicker.readMillis()).thenReturn(startTime);
        final OciCredentials ociCredentials = ociService.createTempCredential("sn_test_upload", 1);
        for (final Map.Entry<Integer, List<StoreBucket>> entry : ociService.getOciConfig().getLookBackDays2Buckets().entrySet()) {
            final List<StoreBucket> buckets = entry.getValue();
            final String accessUri = ociCredentials.getAccessUri().stream()
                    .filter(it -> it.endsWith("/b/" + buckets.get(0).getBucket() + "/o/")).findFirst().orElse(null);
            Assert.assertNotNull("找不到bucket[" + buckets.get(0) + "]的accessUri", accessUri);
//            if (i == 0) Assert.assertTrue(accessUri.endsWith("/b/test/o/"));
//            if (i == 1) Assert.assertTrue(accessUri.endsWith("/b/test-vip-7d/o/"));
            // https://objectstorage.us-ashburn-1.oraclecloud.com
            // /p/qaeCXlC5TeXwR-lqggKWA9TER1r-4Tt6iYgdpalISjX262dLy8TeQZNV4aykKRZq/n/idkrj1ijdi7s/b/test/o/
            // test_dir/pre_auth_obj_2023-25-21_1676982335167_0
            final String key;
            if (entry.getKey().equals(-1)) {
                key = "ai_edge/sn_test_upload/" + OpenApiUtil.shortUUID();
            } else {
                key = "device_video_slice/sn_test_upload/" + OpenApiUtil.shortUUID();
            }
            final String uploadUrl = ociService.getUploadUrl(accessUri, key);
            final String content = UUID.randomUUID().toString();
            final boolean result = OciService.putObjectByPar(uploadUrl, content);
            Assert.assertTrue("uploadUrl=" + uploadUrl, result);
            // getObject
            final String content2 = ociService.getObject(buckets.get(0), key);
            Assert.assertEquals(content, content2);
        }
        {
            when(timeTicker.readMillis()).thenReturn(startTime + 3600_000);
            final OciCredentials ociCredentials2 = ociService.createTempCredential("sn_test_upload", 1);
            Assert.assertEquals(ociCredentials, ociCredentials2);
        }
        {
            when(timeTicker.readMillis()).thenReturn(startTime + 3600_000 * 5);
            final OciCredentials ociCredentials2 = ociService.createTempCredential("sn_test_upload", 1);
            Assert.assertEquals(ociCredentials, ociCredentials2);
        }
        {
            when(timeTicker.readMillis()).thenReturn(startTime + 3600_000 * 6);
            final OciCredentials ociCredentials2 = ociService.createTempCredential("sn_test_upload",1);
            Assert.assertNotEquals(ociCredentials, ociCredentials2);
        }
        {
            when(timeTicker.readMillis()).thenReturn(startTime + 3600_000 * 12);
            final OciCredentials ociCredentials2 = ociService.createTempCredential("sn_test_upload",1);
            Assert.assertNotEquals(ociCredentials, ociCredentials2);
        }
        when(timeTicker.readMillis()).thenAnswer(it -> System.currentTimeMillis());
    }

    //    @Test
    public void test_init_close(){
        final OciService ociService = new OciService();
        ociService.setOciConfig(new OciConfig().setEnable(false));
        ociService.init();
        ociService.close();
    }

    //@Test
    public void test_cosService_error() {
        final OciService ociService = createOciServiceByEnv("local");
        AssertUtil.assertNotException(() -> ociService.getObject(new StoreBucket().setBucket("bucket1").setRegion(ociService.getDefaultRegion()), "key1"));
        AssertUtil.assertNotException(() -> ociService.putObject(new StoreBucket().setBucket("bucket1").setRegion(ociService.getDefaultRegion()), "key1", "value1"));
        AssertUtil.assertNotException(() -> ociService.createPar(new StoreBucket().setBucket("bucket1").setRegion(ociService.getDefaultRegion()), "key1"));
        AssertUtil.assertNotException(() -> ociService.putObjectByPar("bucket1", "value1"));
        AssertUtil.assertNotException(() -> ociService.getObjectUrl(null));
    }

    //@Test
    public void test_preSignUrl_null() {
        final OciService ociService = createOciServiceByEnv("local");
        final URL url2 = ociService.preSignUrl("https://a.b.c/xyz", 10000L);
        Assert.assertNull(url2);
    }

    @Test
    public void test_parseOciUrl(){
        {
            final String url = "";
            Assert.assertNull(OciService.parseOciUrl(url));
        }
        {
            final String url = "https://objectstorage/n/idkrj1ijdi7s/b/test/o/test_oci_api%2F0";
            Assert.assertNull(OciService.parseOciUrl(url));
        }
        {
            final String url = "https://error/n/idkrj1ijdi7s/b/test/o/test_oci_api%2F0";
            Assert.assertNull(OciService.parseOciUrl(url));
        }
        {
            final String url = "https://objectstorage.error/n/idkrj1ijdi7s/b/test/o/test_oci_api%2F0";
            Assert.assertNull(OciService.parseOciUrl(url));
        }
        {
            final String url = "https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/test/o";
            Assert.assertNull(OciService.parseOciUrl(url));
        }
        {
            final String url = "https://objectstorage.us-ashburn-1.oraclecloud.com/x/idkrj1ijdi7s/b/test/o/test_oci_api%2F0";
            Assert.assertNull(OciService.parseOciUrl(url));
        }
        {
            final String url = "https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/test/y/test_oci_api%2F0";
            Assert.assertNull(OciService.parseOciUrl(url));
        }
        {
            final String url = "https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/test/o/test_oci_api%2F0";
            Assert.assertNotNull(OciService.parseOciUrl(url));
        }

    }

    public void test_deleteParByRedis(OciService ociService) {
        final MockRedisService redisService = (MockRedisService) ociService.getRedisService();
        for (final String key : redisService.getRedisMap().keySet()) {
            final List<OciPar> parList = redisService.getRedisMap().get(key).stream()
                    .map(it -> JSON.parseObject(it, OciPar.class)).collect(Collectors.toList());
            ociService.deleteExpiredPar(key, parList);
        }
    }

    @Test
    public void test_getBucket2AllowPrefix() {
        Map<String, String> ociPrefixMap = OciService.getBucket2AllowPrefix(new HashMap<Integer, List<StoreBucket>>() {{

            put(-1, new ArrayList<StoreBucket>(){{ add(new StoreBucket().setBucket("a")); }});
            put(1, new ArrayList<StoreBucket>(){{ add(new StoreBucket().setBucket("b")); }});
        }}, "");
        Assert.assertTrue(ociPrefixMap.get("a").startsWith(VideoConstants.UPLOAD_AI_EDGE_HEAD));
        Assert.assertTrue(ociPrefixMap.get("b").startsWith(VideoConstants.UPLOAD_KEY_HEAD));

    }

    //    @Test
    @SneakyThrows
    public void test_par() {
        final String lines = "" +
                " 1) \"{\\\"accessUri\\\":\\\"/p/b-iK6OXqyyRFblNdP_YMskPBADQwf9fKQGusef-NJswBFFYCOF5bxXZ_DV3pRwA3/n/idrw0j0fjn4v/b/prod-eu-vip-30d/o/\\\",\\\"bucket\\\":\\\"prod-eu-vip-30d\\\",\\\"cacheInvalidTime\\\":1685108010456,\\\"createTime\\\":1685064810482,\\\"expiredTime\\\":1685151210431,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"hsD3aTXZ10+tKStxPz8JTloubxqpfX2CdKthoP8oxUlLrV9IuCmhmHUTqbS8CX/y\\\"}\"\n" +
                " 2) \"{\\\"accessUri\\\":\\\"/p/Lfd4Xu7DTc7jDL88OroJ-YM4hqEYPjAwkouXbGDtEkuzYBfpSZorr-CmWqS6o49I/n/idrw0j0fjn4v/b/prod-eu-vip-7d/o/\\\",\\\"bucket\\\":\\\"prod-eu-vip-7d\\\",\\\"cacheInvalidTime\\\":1685108010302,\\\"createTime\\\":1685064810342,\\\"expiredTime\\\":1685151210262,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"wdp5npfL51PeCBZ9c43Rv3mxD6RjgtugbmJMXjbEnojw9DqBGNy15g/wmJogBroh\\\"}\"\n" +
                " 3) \"{\\\"accessUri\\\":\\\"/p/cXonCEMNh0GxMFgB_kpdNzmS0C6N4cA9yS6UJ5lK4h0pOhwrNbz7gYCShQ_XgJam/n/idrw0j0fjn4v/b/prod-eu-vip-15d/o/\\\",\\\"bucket\\\":\\\"prod-eu-vip-15d\\\",\\\"cacheInvalidTime\\\":1685108010386,\\\"createTime\\\":1685064810431,\\\"expiredTime\\\":1685151210342,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"D52lWLZCs4InzoGDFZ8c/91WnrhGkCv4Z3I26ta0jZQxL489H78O7kOPuWnbc6lC\\\"}\"\n" +
                " 4) \"{\\\"accessUri\\\":\\\"/p/dC7iLHJdx57_xozt5H3w1YsVpbXLyr8AKG5M-Re01UKbvhARDxbZWpvPrtpAF_aL/n/idrw0j0fjn4v/b/prod-eu-vip-3d/o/\\\",\\\"bucket\\\":\\\"prod-eu-vip-3d\\\",\\\"cacheInvalidTime\\\":1685055547106,\\\"createTime\\\":1685012347186,\\\"expiredTime\\\":1685098747026,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"SfzSWZS6DRDjXfMWA0NY4FTQTJJtOBmtXRnhJfvx+5GdCmhS9dMC/BcXhdCgqFIZ\\\"}\"\n" +
                " 5) \"{\\\"accessUri\\\":\\\"/p/pcB088nkb0CviI2ruQtncWZ9lq3ZgGBCT2VYZPvAXlLpZvmqjUjnHvAf10awP40c/n/idrw0j0fjn4v/b/prod-eu-vip-60d/o/\\\",\\\"bucket\\\":\\\"prod-eu-vip-60d\\\",\\\"cacheInvalidTime\\\":1685055547460,\\\"createTime\\\":1685012347510,\\\"expiredTime\\\":1685098747411,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"TM6LLGDf4lF/eAoAXynpcAtGi8SJW4mGp/myLzAQvEyIAy7DM5F8hOYX9k+ht27x\\\"}\"\n" +
                " 6) \"{\\\"accessUri\\\":\\\"/p/KB35staWYfokz4m8JZ9ix7g3WuSj4JZG84iVE7EOT4X3Az0BcO0tYF1crjNwR43g/n/idrw0j0fjn4v/b/prod-eu-vip-30d/o/\\\",\\\"bucket\\\":\\\"prod-eu-vip-30d\\\",\\\"cacheInvalidTime\\\":1685055547382,\\\"createTime\\\":1685012347411,\\\"expiredTime\\\":1685098747354,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"G6C5WFdyZNUXDXb3rWv04EwMgDbIyxsJxHoIPpm/Mlw5Cpqz5PiZlcqv+ON6Vljs\\\"}\"\n" +
                " 7) \"{\\\"accessUri\\\":\\\"/p/ZAFp0bP5-MvxinDFcAPZPALpkKFdo514jT_0X4BvoZWmRDYjffv8QRRrOSjiowT2/n/idrw0j0fjn4v/b/a4x-prod-eu-ai-edge/o/\\\",\\\"bucket\\\":\\\"a4x-prod-eu-ai-edge\\\",\\\"cacheInvalidTime\\\":1685055546959,\\\"createTime\\\":1685012347026,\\\"expiredTime\\\":1685098746892,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"pnnfoJKSodHGwomFcL+MHjSVNFUfJ+Jiqzfkfye49T+DCkvRbZ3g7vGjzmgSc5Pc\\\"}\"\n" +
                " 8) \"{\\\"accessUri\\\":\\\"/p/SqCTjL6BmttkjH9ILqN5OcV9A2yYpt79rMpF18uQnDINyJ-MfzTogYLHHUoli4Ak/n/idrw0j0fjn4v/b/prod-eu-vip-7d/o/\\\",\\\"bucket\\\":\\\"prod-eu-vip-7d\\\",\\\"cacheInvalidTime\\\":1685055547207,\\\"createTime\\\":1685012347229,\\\"expiredTime\\\":1685098747186,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"i6NGaboNftTGf5r4T74gpFvxpcQ4JFKjevPnKwxz33rJ5PVRruAccVNVfWiuDW2/\\\"}\"\n" +
                " 9) \"{\\\"accessUri\\\":\\\"/p/q9J7TOEIMQsJBp-F3Yz8CLRZGhT5kXtGbiJ3Cpx3J_bIMBvNCrQ3SfzILVIWiYP8/n/idrw0j0fjn4v/b/prod-eu-vip-15d/o/\\\",\\\"bucket\\\":\\\"prod-eu-vip-15d\\\",\\\"cacheInvalidTime\\\":1685055547291,\\\"createTime\\\":1685012347354,\\\"expiredTime\\\":1685098747229,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"uXApONUdBhP6wGDOjYI854VGi5V2r+SCJH5d7+4EH6V98d2K5/uef1FDHTXBrzm3\\\"}\"\n" +
                "10) \"{\\\"accessUri\\\":\\\"/p/A-1TNvsuU-glCgK4jXkRR8uTdlhV3qdvdUlKioxDmWwqmq6yF9Wtysmtu-b3orre/n/idrw0j0fjn4v/b/prod-eu-vip-60d/o/\\\",\\\"bucket\\\":\\\"prod-eu-vip-60d\\\",\\\"cacheInvalidTime\\\":1685108010507,\\\"createTime\\\":1685064810532,\\\"expiredTime\\\":1685151210482,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"NnZCYmcZRVFUOuL/3bNX9/ffe99iT0jf6ZF5zlnhBq1A/X+DLsuGhhG2yly8chWr\\\"}\"\n" +
                "11) \"{\\\"accessUri\\\":\\\"/p/8pbuRZOFuzY0TmlpweuKbDivwmrj1FSVpvb9b9YoX2ysJvquvO76_ywZG2NXbT4i/n/idrw0j0fjn4v/b/prod-eu-vip-3d/o/\\\",\\\"bucket\\\":\\\"prod-eu-vip-3d\\\",\\\"cacheInvalidTime\\\":1685108010236,\\\"createTime\\\":1685064810262,\\\"expiredTime\\\":1685151210211,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"ajQgvorwcUDUr0LloJmu4bG7nlNDtZ+6dYfhVvikRebqcX+o4suNGpYTtgQLK9vJ\\\"}\"\n" +
                "12) \"{\\\"accessUri\\\":\\\"/p/jIL6CSBo0lfe-3jwfGO2zVNbrt3w36XM0XTMNjW7QbrZzhe63yqHmFfZoTYtqe3e/n/idrw0j0fjn4v/b/a4x-prod-eu-ai-edge/o/\\\",\\\"bucket\\\":\\\"a4x-prod-eu-ai-edge\\\",\\\"cacheInvalidTime\\\":1685108010183,\\\"createTime\\\":1685064810211,\\\"expiredTime\\\":1685151210156,\\\"namespace\\\":\\\"idrw0j0fjn4v\\\",\\\"parId\\\":\\\"XsutteX9ne0IdyX6ghVIWAbPiQVx9lz2EQCU7gUmG4MjiBtwL6o3mIZQ37AyVQ9z\\\"}\"";

        final List<OciPar> ociParList = Arrays.stream(lines.split("\n")).filter(StringUtils::isNotBlank)
                .map(line -> line.substring(4).replace("\\", "").trim())
                .map(line -> line.substring(1, line.length() - 1))
                .map(line -> JSON.parseObject(line, OciPar.class)).collect(Collectors.toList());

        final OciService ociService = createOciServiceByEnv("prod-eu");
        final long time = System.currentTimeMillis();
        final String content = OpenApiUtil.shortUUID();
        for (final OciPar ociPar : ociParList.subList(2, 3)) {
            if (ociPar.getExpiredTime() <= time) {
                System.out.printf("accessUri=%s,respCode=%s\n\n", ociPar.getAccessUri(), "par已过期:" + (time - ociPar.getExpiredTime()));
                continue;
            }
            final GetPreauthenticatedRequestResponse getParResp = ociService.getPar(new StoreBucket().setBucket(ociPar.getBucket()).setRegion(ociPar.getRegion()), ociPar.getParId());
            System.out.printf("getParResp:%s\n", JSON.toJSONString(getParResp));
            final String host = new URL(ociService.getRootUrl(new StoreBucket().setBucket(ociPar.getBucket()))).getHost();
            final String uploadUrl = "https://" + host + ociPar.getAccessUri() + time;
            try (final CloseableHttpClient httpClient = HttpClients.createDefault()) {
                final HttpPost post = new HttpPost(uploadUrl);
                post.setEntity(new ByteArrayEntity(content.getBytes(StandardCharsets.UTF_8)));
                final CloseableHttpResponse resp = httpClient.execute(post);
                System.out.printf("accessUri=%s,respCode=%s\n", ociPar.getAccessUri(), resp.getStatusLine().getStatusCode());
            }
        }
    }

    //    @Test
    @SneakyThrows
    public void test_par2() {
        final OciService ociService = createOciServiceByEnv("prod-eu");
//        final String resp = ociService.getObject("prod-eu-vip-15d", "device_video_slice/7c4b2425a8dadf77567e5a8ec91c67e5/test.txt");
//        System.out.println(resp);

        final OciPar par = ociService.createPar(new StoreBucket().setBucket("prod-eu-vip-60d").setRegion(ociService.getDefaultRegion()), "zyj-test/");
        System.out.println(JSON.toJSONString(par));
    }

//    @Test
    @SneakyThrows
    public void test_listObject() {
        final OciService ociService = createOciServiceByEnv("prod-us");
        /*
        fbb3aa122003c4dbdf9b47dcffb93682
        prod-us
        traceId: 02113811691027846moT2Zq2wnNTh
         */
        String bucket = "prod-us-vip-7d";
        String keyPrefix = "device_video_slice/fbb3aa122003c4dbdf9b47dcffb93682/02113811691027846moT2Zq2wnNTh/";
        final List<String> keys = ociService.listObjects(new StoreBucket().setBucket(bucket).setRegion(ociService.getDefaultRegion()), keyPrefix);
        for (final String key : keys) {
            System.out.println(key);
        }
    }


    @Test
    public void test_bucketRatio(){
        OciConfig ociConfig = new OciConfig();
        ociConfig.setEnable(true)
                .setBucket("default-bucket")
                .setParams(new OciParams().setDefaultRegion("default-region"))
                .setLookBackDays2Buckets(new HashMap<Integer, List<StoreBucket>>(){{
                    put(1, new ArrayList<StoreBucket>() {{
                        add( new StoreBucket().setRegion("region-1").setBucket("bucket-1").setMinRatio(0).setMaxRatio(50));
                        add( new StoreBucket().setRegion("region-2").setBucket("bucket-2").setMinRatio(50).setMaxRatio(100));
                    }});
                    put(3, new ArrayList<StoreBucket>() {{
                        add( new StoreBucket().setRegion("region-3").setBucket("bucket-3").setMinRatio(100).setMaxRatio(100));
                        add( new StoreBucket().setRegion("region-4").setBucket("bucket-4").setMinRatio(0).setMaxRatio(100));
                    }});
                    put(7, new ArrayList<StoreBucket>() {{
                        add( new StoreBucket().setRegion("region-5").setBucket("bucket-5").setMinRatio(0).setMaxRatio(50));
                        add( new StoreBucket().setRegion("region-6").setBucket("bucket-6").setMinRatio(80).setMaxRatio(100));
                    }});
                }});

        Assert.assertEquals("bucket-1", ociConfig.getBucketByLookBackDays(1, 0).getBucket());
        Assert.assertEquals("bucket-1", ociConfig.getBucketByLookBackDays(1, 100).getBucket());
        Assert.assertEquals("bucket-2", ociConfig.getBucketByLookBackDays(1, 150).getBucket());
        Assert.assertEquals("bucket-2", ociConfig.getBucketByLookBackDays(1, 99).getBucket());

        Assert.assertEquals("bucket-4", ociConfig.getBucketByLookBackDays(3, 150).getBucket());
        Assert.assertEquals("bucket-4", ociConfig.getBucketByLookBackDays(3, 99).getBucket());

        Assert.assertEquals("bucket-5", ociConfig.getBucketByLookBackDays(7, 40).getBucket());
        Assert.assertEquals("bucket-6", ociConfig.getBucketByLookBackDays(7, 99).getBucket());
        Assert.assertEquals("default-bucket", ociConfig.getBucketByLookBackDays(7, 60).getBucket());

    }



    @InjectMocks
    private OciService ociServiceTemp;

    @Mock
    private Map<String,ObjectStorageClient>  clientMap;

    @Mock
    private Map<String,String> namespaceMap;

    @Mock
    private OciConfig ociConfigTemp;

    @Mock
    private Map<String, AmazonS3> ociCompatClientMap;

    @Mock
    private ListObjectsResponse listObjectsResponse;

    @Test
    public void test_getNamespace() throws NoSuchFieldException {
        ObjectStorageClient objectStorageClient = Mockito.mock(ObjectStorageClient.class);

        //ociServiceTemp.init();

        when(clientMap.get(any())).thenReturn(objectStorageClient);
        when(clientMap.containsKey(any())).thenReturn(true);
        when(namespaceMap.containsKey(any())).thenReturn(true);
        when(namespaceMap.get(any())).thenReturn("namespace");
        when(objectStorageClient.getNamespace(any())).thenReturn( new GetNamespaceResponse.Builder().value("namespace").build());
        Assert.assertEquals("namespace",ociServiceTemp.getNamespace("us-ashburn-1"));

        when(namespaceMap.containsKey(any())).thenReturn(false);
        when(namespaceMap.get(any())).thenReturn("namespace");
        Assert.assertEquals("namespace",ociServiceTemp.getNamespace("us-ashburn-1"));

        ociServiceTemp.getObject(new StoreBucket().setBucket("bucket-name").setRegion("us-ashburn-1"), "/aaa/ddd.test");
        ociServiceTemp.putObject(new StoreBucket().setBucket("bucket-name").setRegion("us-ashburn-1"), "/aaa/ddd.test", "dddd");
        ListObjects listObjects= PowerMockito.mock(ListObjects.class);
        when(listObjectsResponse.getListObjects()).thenReturn(listObjects);
        PowerMockito.when(listObjects.getObjects()).thenReturn(new ArrayList<>());
        when(clientMap.get(any())).thenReturn(objectStorageClient);
        when(objectStorageClient.listObjects(any())).thenReturn(listObjectsResponse);
        Assert.assertEquals(0, ociServiceTemp.listObjects(new StoreBucket().setBucket("bucket-name").setRegion("us-ashburn-1"), "/aaa/ddd.test").size());

    }

    @Test
    public void test_getCient()  {
        ObjectStorageClient objectStorageClient = Mockito.mock(ObjectStorageClient.class);

        //ociServiceTemp.init();

        when(clientMap.get(any())).thenReturn(objectStorageClient);
        when(clientMap.containsKey(any())).thenReturn(true);

        Assert.assertEquals(objectStorageClient,ociServiceTemp.getClient("us-ashburn-1"));

        when(clientMap.get(any())).thenReturn(objectStorageClient);
        when(clientMap.containsKey(any())).thenReturn(false);
        when(ociConfigTemp.getParams()).thenReturn(new OciParams().setKey_file("classpath:oci/for-us-staging-buckets_2023-02-23T02_06_44.897Z.pem"));
        ObjectStorageClient.Builder b = PowerMockito.mock(ObjectStorageClient.Builder.class);
        PowerMockito.mockStatic(ObjectStorageClient.class);
        PowerMockito.when(ObjectStorageClient.builder()).thenReturn(b);
        PowerMockito.when(b.build(any())).thenReturn(objectStorageClient);
        Assert.assertEquals(objectStorageClient,ociServiceTemp.getClient("us-ashburn-1"));
        when(objectStorageClient.getNamespace(any())).thenReturn( new GetNamespaceResponse.Builder().value("namespace").build());
        Assert.assertEquals("https://objectstorage.us-ashburn-1.oraclecloud.com/n/null/b/ddd/o/xxxx", ociServiceTemp.getObjectUrl(new StoreBucket().setBucket("ddd").setRegion(("us-ashburn-1")), "xxxx").toString());
        ociServiceTemp.closeClients();

    }

    @Test
    public void test_getOciCompatClient() {
        AmazonS3 amazonS3 = Mockito.mock(AmazonS3.class);

        //ociServiceTemp.init();

        when(ociCompatClientMap.get(any())).thenReturn(amazonS3);
        when(ociCompatClientMap.containsKey(any())).thenReturn(true);

        Assert.assertEquals(amazonS3,ociServiceTemp.getOciCompatClient("us-ashburn-1"));

        when(ociCompatClientMap.get(any())).thenReturn(amazonS3);
        when(ociCompatClientMap.containsKey(any())).thenReturn(false);
        when(ociConfigTemp.getParams()).thenReturn(new OciParams().setKey_file("classpath:oci/for-us-staging-buckets_2023-02-23T02_06_44.897Z.pem"));

        ObjectStorageClient objectStorageClient = Mockito.mock(ObjectStorageClient.class);
        when(clientMap.get(any())).thenReturn(objectStorageClient);
        when(namespaceMap.get(any())).thenReturn("namespace");
        when(objectStorageClient.getNamespace(any())).thenReturn( new GetNamespaceResponse.Builder().value("namespace").build());
        when(ociConfigTemp.getAccessKey()).thenReturn("kkkkkkk");
        when(ociConfigTemp.getSecretKey()).thenReturn("sssssss");
        ObjectStorageClient.Builder b = PowerMockito.mock(ObjectStorageClient.Builder.class);
        PowerMockito.mockStatic(ObjectStorageClient.class);
        PowerMockito.when(ObjectStorageClient.builder()).thenReturn(b);
        PowerMockito.when(b.build(any())).thenReturn(objectStorageClient);
        PowerMockito.mockStatic(OciService.class);
        PowerMockito.when(OciService.createOciCompatClient(any(), any(),any(),any())).thenReturn(amazonS3);
        Assert.assertEquals(amazonS3,ociServiceTemp.getOciCompatClient("us-ashburn-1"));

        when(ociConfigTemp.getEnableCreateCredential()).thenReturn(true);
        ociServiceTemp.setRedisService(new MockRedisService());
        when(ociConfigTemp.getLookBackDays2Buckets()).thenReturn(new HashMap<Integer, List<StoreBucket>>(){{ put(1, new ArrayList<StoreBucket>() {{ add(new StoreBucket().setBucket("bucket-name").setRegion("us-ashburn-1"));}});}});
        CreatePreauthenticatedRequestResponse createPreauthenticatedRequestResponse = Mockito.mock(CreatePreauthenticatedRequestResponse.class);

        when(objectStorageClient.createPreauthenticatedRequest(any())).thenReturn(createPreauthenticatedRequestResponse);
        PreauthenticatedRequest preauthenticatedRequest= PowerMockito.mock(PreauthenticatedRequest.class);
        when(createPreauthenticatedRequestResponse.getPreauthenticatedRequest()).thenReturn(preauthenticatedRequest);
        PowerMockito.when(preauthenticatedRequest.getAccessUri()).thenReturn("access-url");
        PowerMockito.when(preauthenticatedRequest.getTimeExpires()).thenReturn(new Date());

        Assert.assertEquals("access-url", ociServiceTemp.createTempCredential("sn-sss",1).getAccessUri().get(0));

    }


}
