package com.addx.iotcamera.service;


import com.addx.iotcamera.bean.device.attributes.*;
import com.addx.iotcamera.bean.device.model.DeviceModelIconDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.DeviceOTADO;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.bean.domain.device.SdCard;
import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.config.device.DeviceSettingConfig;
import com.addx.iotcamera.config.device.DoorBellRingConfig;
import com.addx.iotcamera.constants.DeviceAttributeName;
import com.addx.iotcamera.dao.DeviceModelEnumMappingDAO;
import com.addx.iotcamera.service.device.*;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.internal.LinkedTreeMap;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.thingmodel.ThingModel;
import org.addx.iot.common.thingmodel.ThingModelConfig;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.config.model.DeviceAttributeIntRange;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeanUtils;

import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.addx.iotcamera.constants.DeviceAttributeName.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceAttributeService2Test {

    @Mock
    private UserTierDeviceService userTierDeviceService;
    @Mock
    private VideoSearchService videoSearchService;
    @Mock
    private DeviceOTAService deviceOTAService;
    @Mock
    private UserService userService;
    @Mock
    private TenantSettingService tenantSettingService;
    @Mock
    private DeviceEnumMappingService deviceEnumMappingService;
    @Mock
    private FirmwareService firmwareService;
    @Mock
    private DeviceSdCardStatusService deviceSdCardStatusService;
    @Mock
    private RoleDefinitionService roleDefinitionService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private DeviceModelIconService deviceModelIconService;
    @Mock
    private DeviceSettingConfig deviceSettingConfig;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private DeviceModelService deviceModelService;
    @Mock
    private DeviceService deviceService;
    @Mock
    private DeviceStatusService deviceStatusService;
    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private StateMachineService stateMachineService;
    @Mock
    private DeviceDormancyPlanService deviceDormancyPlanService;
    @Mock
    private LocationInfoService locationInfoService;
    @Mock
    private DeviceEnumMappingService optionEnumMappingService;
    @Mock
    private RedisService redisService;
    @Mock
    private Lock lock;
    @Mock
    private DeviceModelEnumMappingDAO deviceModelEnumMappingDAO;
    @Mock
    private DoorBellRingConfig doorBellRingConfig;
    @Mock
    private S3Service s3Service;

    @InjectMocks
    private DeviceAttributeService deviceAttributeService;
    @Mock
    private Device4GService device4GService;

    private Map<Integer, String> doorBellRingMap = new LinkedHashMap<>();

    @Mock
    private FactoryDataQueryService factoryDataQueryService;

    @Mock
    private VipService vipService;

    @Mock
    private ThingModelConfig thingModelConfig;


    private List<DeviceModifiableAttribute> attrList;
    private JSONObject supportJson;
    private JSONObject propertyJson;

    @Mock
    private DeviceAppSettingsDO appSetting;

    @Mock
    private DeviceAttributeService.ModifySummary summary;

    @Before
    @SneakyThrows
    public void before() {
        attrList = new ArrayList<>();
        supportJson = new JSONObject();
        propertyJson = new JSONObject();

        doorBellRingMap.put(1, "http://a4x.io/doorBellRing_1");
        doorBellRingMap.put(2, "http://a4x.io/doorBellRing_2");
        doorBellRingMap.put(3, "http://a4x.io/doorBellRing_3");
        when(doorBellRingConfig.getConfig()).thenReturn(doorBellRingMap);
        when(s3Service.preSignUrl(anyString())).thenAnswer(it -> it.getArgument(0) + "?signed");

        when(redisService.obtain(anyString())).thenReturn(lock);
        when(lock.tryLock(anyLong(), any())).thenReturn(true);
        when(deviceSettingService.updateUserConfig(any(), any(), anyLong(), anyBoolean())).thenReturn(Result.Success());
        when(userService.queryUserById(any())).thenReturn(new User().setTenantId("tenantId1"));
        when(tenantSettingService.getTenantSettingByTenantId(anyString())).thenReturn(new TenantSetting().setTenantId("tenantId1"));

        when(thingModel.getProperties()).thenAnswer(it -> new ArrayList<>());
    }

    private void mock_modifiableAttributes_all(String sn, String modelNo, Integer userId) {
        final DeviceManualDO manual = new DeviceManualDO();
        manual.setModelNo(modelNo);
        final OptionEnumMapping enumMapping = new OptionEnumMapping();
        final CloudDeviceSupport support = new CloudDeviceSupport();
        final DeviceSettingsDO settings = new DeviceSettingsDO();
        final DeviceDO device = new DeviceDO();
        device.setDeviceSupport(support);
        /* 1.运动检测-总开关*/
        settings.setPir(1);
        /* 2.运动检测-灵敏度*/
        settings.setPirSensitivity("high");
        support.setPirSensitivityOptions(Arrays.asList("high", "mid", "low"));
        /* 3.运动检测-录制时长*/
        settings.setPirRecordTime("10s");
        support.setPirRecordTimeOptions(Arrays.asList("10s", "15s", "20s", "60s", "120s", "180s"));
        /* 3.运动检测-sdCard录制时长*/
        settings.setSdCardPirRecordTime("10s");
        support.setSdCardPirRecordTimeOptions(Arrays.asList("10s", "15s", "20s", "60s", "120s", "180s", "auto"));
        /* 4.运动检测-触发间隔*/
        support.setSupportPirCooldown(1);
        settings.setCooldownUserEnable(true);
        /* 5.运动检测-触发间隔时间*/
        settings.setPirCooldownTime("10s");
        support.setPirCooldownTimeOptions(Arrays.asList("10s", "30s", "60s", "180s", "300s"));
        /* 6.定时休眠-总开关*/
        support.setDeviceDormancySupport(1);
        device.setDormancyPlanSwitch(1);
        /* 6.运动追踪-总开关*/
        support.setSupportMotionTrack(1);
        settings.setMotionTrack(1);
        /* 7.视频-分辨率*/
        settings.setVideoResolution("high");
        support.setVideoResolutionOptions(Arrays.asList("high", "mid"));
        /* 8.视频-翻转开关*/
        support.setDeviceSupportMirrorFlip(true);
        settings.setMirrorFlip(1);
        /* 9.视频-抗频闪开关*/
        support.setAntiflickerSupport(1);
        settings.setAntiflickerSwitch(1);
        /* 10.视频-抗频闪频率*/
        settings.setVideoAntiFlickerFrequency("50Hz");
        support.setVideoAntiFlickerFrequencyOptions(Arrays.asList("50Hz", "60Hz"));
        /* 11.音视频流联动(切换视频编码)*/
        when(deviceInfoService.getSupportChangeCodec(device)).thenReturn(true);
        settings.setDefaultCodec("h265");
        support.setLiveStreamCodecOptions(Arrays.asList("h265", "h264"));
        /* 13.设备所在位置*/
        final LocationDO locationDO1 = new LocationDO();
        locationDO1.setId(1);
        locationDO1.setLocationName("地点1");
        final LocationDO locationDO2 = new LocationDO();
        locationDO2.setId(2);
        locationDO2.setLocationName("地点2");
        device.setLocationId(1);
        when(locationInfoService.listUserLocations(userId)).thenReturn(Arrays.asList(locationDO1, locationDO2));
        /* 14.设备名称*/
        device.setDeviceName("device_name" + sn);
        /* 15.时区*/
        settings.setTimeZone("Asia/Shanghai");
        /* 16.按铃通知开关*/
        support.setSupportDoorbellPressNotifySwitch(true);
        settings.setDoorbellPressNotifySwitch(true);
        /* 17.按铃通知方式*/
        settings.setDoorbellPressNotifyType("push");
        /* 18.一键呼叫开关*/
        support.setSupportDeviceCall(1);
        settings.setDeviceCallToggleOn(true);
        /* 19.机械响铃开关*/
        support.setSupportMechanicalDingDong(true);
        settings.setMechanicalDingDongSwitch(1);
        /* 20.机械响铃时长*/
        settings.setMechanicalDingDongDuration(300);
        support.setMechanicalDingDongDurationRange(new DeviceAttributeIntRange(200, 1000, 100));
        /* 21.运动触发报警开关*/
        support.setDeviceSupportAlarm(true);
        settings.setNeedAlarm(1);
        /* 22.防拆报警开关*/
        support.setAntiDisassemblyAlarm(1);
        settings.setAlarmWhenRemoveToggleOn(true);
        /* 23.报警闪光灯开关*/
        support.setSupportAlarmFlashLight(true);
        settings.setWhiteLightScintillation(1);
        /* 24.指示灯开关*/
        support.setSupportRecLamp(1);
        settings.setRecLamp(1);
        /* 25.夜视开关*/
        support.setSupportNightVisionSwitch(true);
        settings.setIrThreshold(1);
        /* 26.夜视灵敏度*/
        support.setNightVisionSensitivityOptions(Arrays.asList("high", "mid", "low"));
        settings.setNightVisionSensitivityEnum("high");
        /* 27.夜视模式*/
        support.setNightVisionModeOptions(Arrays.asList("infrared", "white"));
        settings.setNightVisionModeEnum("white");
        /* 28.报警时长*/
        support.setDeviceSupportAlarm(true);
        settings.setAlarmDurationEnum("5s");
        support.setAlarmDurationOptions(Arrays.asList("5s", "10s", "15s"));
        /* 29.警铃音量*/
        support.setSupportAlarmVolume(1);
        settings.setAlarmVolume(10);
        support.setAlarmVolumeRange(new DeviceAttributeIntRange(10, 100, 1));
        /* 30.设备语音语种选择*/
        support.setDeviceSupportLanguage(Arrays.asList("cn", "en"));
        settings.setLanguage("cn");
        /* 31.门铃铃声选择*/
        support.setSupportDoorBellRingKey(new ArrayList<>(doorBellRingMap.keySet()));
        settings.setDoorBellRingKey(1);
        /* 32.提示音音量*/
        support.setSupportVoiceVolume(1);
        settings.setVoiceVolume(10);
        support.setVoiceVolumeRange(new DeviceAttributeIntRange(0, 100, 1));
        /* 33.直播收音*/
        support.setSupportLiveAudioToggle(1);
        settings.setLiveAudioToggleOn(true);
        /* 34.录像收音*/
        support.setSupportRecordingAudioToggle(1);
        settings.setRecordingAudioToggleOn(true);
        /* 35.供电方式。选项在后端*/
        enumMapping.setPowerSourceOptions(Stream.of("solar_panel", "plug_in", "battery_power_only", "wired")
                .map(it -> new OptionEnumMapping.Entry<>(it, it)).collect(Collectors.toList()));
        settings.setPowerSource("solar_panel");
        /* 35.供电方式。选项在后端*/
        support.setSupportChargeAutoPowerOn(1);
        settings.setChargeAutoPowerOnSwitch(1);
        settings.setChargeAutoPowerOnCapacity(10);
        enumMapping.setChargeAutoPowerOnCapacityOptions(Arrays.asList());
        /* 38.声音设置-直播对讲音量*/
        support.setSupportLiveSpeakerVolume(1);
        settings.setLiveSpeakerVolume(10);
        support.setLiveSpeakerVolumeRange(new DeviceAttributeIntRange(0, 100, 1));
        support.setSupportCanStandby(1);
        /** 38. pir检测结果偏好 */
        support.setSupportPersonAi(true);
        settings.setDetectPersonAi(true);
        support.setSupportSdCardCooldown(2);
        support.setSupportPirRecordTime(true);
        support.setSupportSdCardVideoModes(Arrays.asList("eventual", "continual"));
        support.setSupportSdCardCooldownSeconds(Arrays.asList("10s", "30s"));
        support.setSupportManualFloodlightSwitch(true);
        support.setSupportManualFloodlightLuminance(true);
        support.setSupportPlanFloodlightSwitch(true);
        support.setSupportTriggerFloodlightSwitch(true);
        support.setSupportPlanFloodlightLuminance(true);
        support.setTriggerFloodlightCooldownTimeOptions(Arrays.asList("10s", "30s"));
        support.setFloodlightModeOptions(Arrays.asList("auto", "always"));
        support.setSupportVideo12Hours(true);

        settings.setFloodlightSchedulePlan("");
        settings.setFloodlightScheduleSwitch(true);
        settings.setMotionFloodlightTime("30s");
        settings.setMotionTriggeredFloodlightSwitch(true);
        settings.setVideo12HourSwitch(true);

        settings.setFloodlightMode("auto");

        when(deviceManualService.getDeviceManualBySerialNumber(sn)).thenReturn(manual);
        when(deviceEnumMappingService.getEnumMappingByModelNo(modelNo)).thenReturn(enumMapping);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(support);
        when(deviceSettingService.getDeviceSettingsBySerialNumber(sn)).thenReturn(settings);
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(device);
        when(userTierDeviceService.getIsNoVipOrFreeTier2(userId, sn)).thenReturn(false);
        when(userRoleService.getDeviceAdminUserRole(sn)).thenReturn(new UserRoleDO() {{
            setUserId(userId);
            setAdminId(userId);
            setSerialNumber(sn);
            setRoleId("1");
        }});
    }

    private void mock_modifiableAttributes_min(String sn, String modelNo, Integer userId) {
        final DeviceManualDO manual = new DeviceManualDO();
        manual.setModelNo(modelNo);
        final OptionEnumMapping enumMapping = new OptionEnumMapping();
        final CloudDeviceSupport support = new CloudDeviceSupport();
        final DeviceSettingsDO settings = new DeviceSettingsDO();
        final DeviceDO device = new DeviceDO();
        device.setDeviceSupport(support);
        /* 1.运动检测-总开关*/
        settings.setPir(1);
        /* 2.运动检测-灵敏度*/
        settings.setPirSensitivity("high");
        support.setPirSensitivityOptions(Arrays.asList("high", "mid", "low"));
        /* 3.运动检测-录制时长*/
        settings.setPirRecordTime("10s");
        support.setPirRecordTimeOptions(Arrays.asList("10s", "15s", "20s"));
        /* 3.运动检测-sdCard录制时长*/
        settings.setSdCardPirRecordTime("10s");
        support.setSdCardPirRecordTimeOptions(Arrays.asList("10s", "15s", "20s", "60s", "120s", "180s", "auto"));
        /* 4.运动检测-触发间隔*/
        support.setSupportPirCooldown(0);
        settings.setCooldownUserEnable(true);
        /* 5.运动检测-触发间隔时间*/
        settings.setPirCooldownTime("10s");
        support.setPirCooldownTimeOptions(Arrays.asList("10s", "30s", "60s", "180s", "300s"));
        /* 6.定时休眠-总开关*/
        support.setDeviceDormancySupport(0);
        device.setDormancyPlanSwitch(1);
        /* 6.运动追踪-总开关*/
        support.setSupportMotionTrack(0);
        settings.setMotionTrack(1);
        /* 7.视频-分辨率*/
        settings.setVideoResolution("high");
        support.setVideoResolutionOptions(Arrays.asList("high", "mid"));
        /* 8.视频-翻转开关*/
        support.setDeviceSupportMirrorFlip(false);
        settings.setMirrorFlip(1);
        /* 9.视频-抗频闪开关*/
        support.setAntiflickerSupport(0);
        settings.setAntiflickerSwitch(1);
        /* 10.视频-抗频闪频率*/
        settings.setVideoAntiFlickerFrequency("50Hz");
        support.setVideoAntiFlickerFrequencyOptions(Arrays.asList("50Hz", "60Hz"));
        /* 11.音视频流联动(切换视频编码)*/
        when(deviceInfoService.getSupportChangeCodec(device)).thenReturn(false);
        settings.setDefaultCodec("h265");
        support.setLiveStreamCodecOptions(Arrays.asList("h265", "h264"));
        /* 13.设备所在位置*/
        final LocationDO locationDO1 = new LocationDO();
        locationDO1.setId(1);
        locationDO1.setLocationName("地点1");
        final LocationDO locationDO2 = new LocationDO();
        locationDO2.setId(2);
        locationDO2.setLocationName("地点2");
        device.setLocationId(1);
        when(locationInfoService.listUserLocations(userId)).thenReturn(Arrays.asList(locationDO1, locationDO2));
        /* 14.设备名称*/
        device.setDeviceName("device_name" + sn);
        /* 15.时区*/
        settings.setTimeZone("Asia/Shanghai");
        /* 16.按铃通知开关*/
        support.setSupportDoorbellPressNotifySwitch(false);
        settings.setDoorbellPressNotifySwitch(true);
        /* 17.按铃通知方式*/
        settings.setDoorbellPressNotifyType("push");
        /* 18.一键呼叫开关*/
        support.setSupportDeviceCall(0);
        settings.setDeviceCallToggleOn(true);
        /* 19.机械响铃开关*/
        support.setSupportMechanicalDingDong(false);
        settings.setMechanicalDingDongSwitch(1);
        /* 20.机械响铃时长*/
        settings.setMechanicalDingDongDuration(300);
        support.setMechanicalDingDongDurationRange(new DeviceAttributeIntRange(200, 1000, 100));
        /* 21.运动触发报警开关*/
        support.setDeviceSupportAlarm(false);
        settings.setNeedAlarm(1);
        /* 22.防拆报警开关*/
        support.setAntiDisassemblyAlarm(0);
        settings.setAlarmWhenRemoveToggleOn(true);
        /* 23.报警闪光灯开关*/
        support.setSupportAlarmFlashLight(false);
        settings.setWhiteLightScintillation(1);
        /* 24.指示灯开关*/
        support.setSupportRecLamp(0);
        settings.setRecLamp(1);
        /* 25.夜视开关*/
        support.setSupportNightVisionSwitch(false);
        settings.setIrThreshold(1);
        /* 26.夜视灵敏度*/
        support.setNightVisionSensitivityOptions(Arrays.asList());
        settings.setNightVisionSensitivityEnum("high");
        /* 27.夜视模式*/
        support.setNightVisionModeOptions(Arrays.asList());
        settings.setNightVisionModeEnum("white");
        /* 28.报警时长*/
        support.setDeviceSupportAlarm(false);
        settings.setAlarmDurationEnum("5s");
        support.setAlarmDurationOptions(Arrays.asList());
        /* 29.警铃音量*/
        support.setSupportAlarmVolume(0);
        settings.setAlarmVolume(10);
        support.setAlarmVolumeRange(new DeviceAttributeIntRange(10, 100, 1));
        /* 30.设备语音语种选择*/
        support.setDeviceSupportLanguage(Arrays.asList());
        settings.setLanguage("cn");
        /* 31.门铃铃声选择*/
        support.setSupportDoorBellRingKey(new ArrayList<>());
        settings.setDoorBellRingKey(1);
        /* 32.提示音音量*/
        support.setSupportVoiceVolume(0);
        settings.setVoiceVolume(10);
        support.setVoiceVolumeRange(new DeviceAttributeIntRange(0, 100, 1));
        /* 33.直播收音*/
        support.setSupportLiveAudioToggle(0);
        settings.setLiveAudioToggleOn(true);
        /* 34.录像收音*/
        support.setSupportRecordingAudioToggle(0);
        settings.setRecordingAudioToggleOn(true);
        /* 35.供电方式。选项在后端*/
        enumMapping.setPowerSourceOptions(Arrays.asList());
        settings.setPowerSource("solar_panel");
        /* 35.供电方式。选项在后端*/
        support.setSupportChargeAutoPowerOn(0);
        settings.setChargeAutoPowerOnSwitch(1);
        settings.setChargeAutoPowerOnCapacity(10);
        enumMapping.setChargeAutoPowerOnCapacityOptions(Arrays.asList());
        /* 38.声音设置-直播对讲音量*/
        support.setSupportLiveSpeakerVolume(0);
        settings.setLiveSpeakerVolume(10);
        support.setLiveSpeakerVolumeRange(new DeviceAttributeIntRange(0, 100, 1));
        /** 38. pir检测结果偏好 */
        support.setSupportPersonAi(false);
        settings.setDetectPersonAi(false);
        support.setSupportSdCardCooldown(0);
        support.setSupportPirRecordTime(false);
        settings.setSdCardCooldownSwitch(0);
        support.setSupportSdCardVideoModes(Arrays.asList());
        settings.setSdCardVideoMode(null);
        support.setSupportSdCardCooldownSeconds(Arrays.asList());
        settings.setSdCardCooldownSeconds(null);
        support.setSupportCanStandby(1);

        enumMapping.setPirRecordTimeOptions(Arrays.asList(new OptionEnumMapping.Entry<Integer>("auto", -1), new OptionEnumMapping.Entry<Integer>("10s", 10), new OptionEnumMapping.Entry<Integer>("15s", 15), new OptionEnumMapping.Entry<Integer>("20s", 20)));

        when(deviceManualService.getDeviceManualBySerialNumber(sn)).thenReturn(manual);
        when(deviceEnumMappingService.getEnumMappingByModelNo(modelNo)).thenReturn(enumMapping);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(support);
        when(deviceSettingService.getDeviceSettingsBySerialNumber(sn)).thenReturn(settings);
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(device);
        when(userTierDeviceService.getIsNoVipOrFreeTier2(userId, sn)).thenReturn(true);
        when(userRoleService.getDeviceAdminUserRole(sn)).thenReturn(new UserRoleDO() {{
            setUserId(userId);
            setAdminId(userId);
            setSerialNumber(sn);
            setRoleId("1");
        }});
    }

    private static void validateModifiableAttributes(List<DeviceAttributeName> attrEnums, List<DeviceModifiableAttribute> attrs) {
        final Map<String, DeviceModifiableAttribute> attrMap = attrs.stream().collect(Collectors.toMap(it -> it.getName(), it -> it));
        for (final DeviceAttributeName attrEnum : attrEnums) {
            final DeviceModifiableAttribute attr = attrMap.get(attrEnum.name());
            Assert.assertNotNull("找不到属性:" + attrEnum, attr);
            Assert.assertNotNull("属性value为NULL:" + JSON.toJSONString(attr));
            if (attrEnum != chargeAutoPowerOnCapacity) {
                assertTrue("属性value不包含在options中:" + JSON.toJSONString(attr), attr.containsValue(attr.getValue()));
            }
        }
    }

    @DisplayName("获取可修改属性，支持所有属性")
    @Test
    public void test_getDeviceAttributes_modifiableAttributes_all() {
        final String sn = OpenApiUtil.shortUUID();
        final String modelNo = "CG-" + OpenApiUtil.randInt();
        final Integer userId = OpenApiUtil.randInt();

        when(device4GService.queryDevice4GSimDO(any())).thenReturn(null);

        SdCard sdCard = new SdCard();
        sdCard.setFormatStatus(0);
        when(deviceSdCardStatusService.querySdCard(any())).thenReturn(sdCard);

        final DeviceAttributesQuery query = new DeviceAttributesQuery();
        query.setSerialNumber(sn).setUserId(userId).setReturnModifiableAttributes(true);
        mock_modifiableAttributes_all(sn, modelNo, userId);
        when(deviceSdCardStatusService.querySdCard(any())).thenReturn(new SdCard());
        final Result<DeviceAttributes> result = deviceAttributeService.getDeviceAttributes(query);
        assertEquals(Result.successFlag, result.getResult());
        //validateModifiableAttributes(Arrays.asList(DeviceAttributeName.values()), result.getData().getModifiableAttributes());
    }

    @DisplayName("获取可修改属性，支持最少属性")
    @Test
    public void test_getDeviceAttributes_modifiableAttributes_min() {
        final String sn = OpenApiUtil.shortUUID();
        final String modelNo = "CG-" + OpenApiUtil.randInt();
        final Integer userId = OpenApiUtil.randInt();

        when(device4GService.queryDevice4GSimDO(any())).thenReturn(null);

        final DeviceAttributesQuery query = new DeviceAttributesQuery();
        query.setSerialNumber(sn).setUserId(userId).setReturnModifiableAttributes(true);
        mock_modifiableAttributes_min(sn, modelNo, userId);
        final Result<DeviceAttributes> result = deviceAttributeService.getDeviceAttributes(query);
        assertEquals(Result.successFlag, result.getResult());
        final List<DeviceAttributeName> attrEnums = Arrays.asList(pirSwitch, pirSensitivity
                , videoResolution, location, deviceName, timeZone);
        final List<DeviceModifiableAttribute> moreAttrs = result.getData().getModifiableAttributes().stream()
                .filter(it -> !attrEnums.contains(DeviceAttributeName.nameOf(it.getName()))).collect(Collectors.toList());
        assertTrue("多余的属性:" + JSON.toJSONString(moreAttrs), moreAttrs.isEmpty());
        validateModifiableAttributes(attrEnums, result.getData().getModifiableAttributes());

    }

    private void mock_realTimeAttributes(String sn, Integer userId) {
        final DeviceStatusDO status = new DeviceStatusDO();
        final DeviceStateDO state = new DeviceStateDO();
        final DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
        final DeviceDormancyInfo dormancyInfo = new DeviceDormancyInfo();
        final DeviceDO device = new DeviceDO();
        final FirmwareViewDO firmware = new FirmwareViewDO();
        final DeviceOTADO ota = new DeviceOTADO();
        final DeviceManualDO manual = new DeviceManualDO();
        final SdCard sdCard = new SdCard();

        onlineInfo.setOfflineTime(PhosUtils.getUTCStamp() - 3600);
        onlineInfo.setOnline(1);
        onlineInfo.setAwake(1);
        dormancyInfo.setDeviceStatus(0);
        /* 供电相关 */
        status.setChargingMode(1);
        status.setIsCharging(1);
        status.setBatteryLevel(1);
        /* 网络相关 */
        status.setDeviceNetType(0);
        status.setSignalStrength(-60);
        status.setNetworkName("addx");
        status.setIp("127.0.0.1");
        status.setWifiChannel(123);
        /* 固件相关 */
        device.setFirmwareId("1.7.0");
        firmware.setTargetFirmware("1.8.0");
        firmware.setFirmwareStatus(1);
        manual.setMcuNumber("abc-123");
        when(deviceInfoService.getDeviceFirmwareBuilder(sn)).thenReturn("gitsha123");
        /* sd卡属性*/
        sdCard.setTotal(100);
        sdCard.setUsed(60);
        sdCard.setFree(40);
        sdCard.setFormatStatus(1);
        sdCard.setSerialNumber(sn);
        status.setWhiteLight(1);
        status.setSignalLevel(100);

        when(stateMachineService.batchGetDeviceState(Arrays.asList(sn))).thenReturn(Collections.singletonMap(sn, state));
        when(deviceStatusService.queryDeviceStatusBySerialNumber(sn)).thenReturn(status);
        when(deviceInfoService.getDeviceDormancyInfo(sn, userId, 1)).thenReturn(dormancyInfo);
        when(deviceStatusService.getDeviceOnlineInfo(eq(sn), anyInt(), eq(status), eq(state))).thenReturn(onlineInfo);
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(device);
        when(firmwareService.buildFirmwareView(device, ota)).thenReturn(firmware);
        when(deviceOTAService.queryDeviceOtaBySerialNumber(sn)).thenReturn(ota);
        when(deviceManualService.getDeviceManualBySerialNumber(sn)).thenReturn(manual);
        when(deviceSdCardStatusService.querySdCard(sn)).thenReturn(sdCard);
        when(userRoleService.getDeviceAdminUserRole(sn)).thenReturn(new UserRoleDO() {{
            setUserId(userId);
            setAdminId(userId);
            setSerialNumber(sn);
            setRoleId("1");
        }});
        when(deviceInfoService.checkIf4GDeviceHasOfficialSimCard(sn)).thenReturn(true);
        when(vipService.isVipDevice(userId, sn)).thenReturn(false);
    }

    @DisplayName("获取实时属性")
    @Test
    @SneakyThrows
    public void test_getDeviceAttributes_realTimeAttributes() {
        final String sn = OpenApiUtil.shortUUID();
        final Integer userId = OpenApiUtil.randInt();

        when(device4GService.queryDevice4GSimDO(any())).thenReturn(Device4GSimDO.builder().serialNumber(sn).simThirdParty(0).simStatus(0).iccid("iccid").imei("imei").build());

        final DeviceAttributesQuery query = new DeviceAttributesQuery();
        query.setSerialNumber(sn).setUserId(userId).setReturnRealTimeAttributes(true);
        mock_realTimeAttributes(sn, userId);
        final Result<DeviceAttributes> result = deviceAttributeService.getDeviceAttributes(query);
        assertEquals(Result.successFlag, result.getResult());
        final DeviceRealTimeAttributes realTimeAttr = result.getData().getRealTimeAttributes();
        Assert.assertNotNull(realTimeAttr);
        final PropertyDescriptor[] pds = BeanUtils.getPropertyDescriptors(realTimeAttr.getClass());
        for (final PropertyDescriptor pd : pds) {
            final Object value = pd.getReadMethod().invoke(realTimeAttr);
            if (pd.getName().equals("inBlackList") || pd.getName().equals("batteryLevel") ||
                    pd.getName().equals("signalStrength")) {
                continue;
            }
            Assert.assertNotNull("实时属性值为NULL:" + pd.getName(), value);
        }

    }

    private void mock_fixedAttributes(String sn, String modelNo, Integer userId) {
        final DeviceModelIconDO modelIcon = new DeviceModelIconDO();
        modelIcon.setModelNo(modelNo);
        modelIcon.setSmallIconUrl("http://a4x.io/small_icon_" + modelNo);
        modelIcon.setIconUrl("http://a4x.io/icon_" + modelNo);
        when(deviceModelIconService.queryDeviceModelIcon(modelNo)).thenReturn(modelIcon);
    }

    @DisplayName("获取固定属性")
    @Test
    @SneakyThrows
    public void test_getDeviceAttributes_fixedAttributes() {
        final String sn = OpenApiUtil.shortUUID();
        final String modelNo = "CG-" + OpenApiUtil.randInt();
        final Integer userId = OpenApiUtil.randInt();

        when(device4GService.queryDevice4GSimDO(any())).thenReturn(Device4GSimDO.builder().serialNumber(sn).simThirdParty(0).simStatus(0).iccid("iccid").imei("imei").build());

        final DeviceAttributesQuery query = new DeviceAttributesQuery();
        query.setSerialNumber(sn).setUserId(userId).setReturnFixedAttributes(true);
//        mock_fixedAttributes(sn, userId);
        mock_realTimeAttributes(sn, userId);
        mock_modifiableAttributes_all(sn, modelNo, userId);
        mock_fixedAttributes(sn, modelNo, userId);
        final Result<DeviceAttributes> result = deviceAttributeService.getDeviceAttributes(query);
        assertEquals(Result.successFlag, result.getResult());
        final DeviceFixedAttributes fixedAttr = result.getData().getFixedAttributes();
        Assert.assertNotNull(fixedAttr);

    }

    @DisplayName("修改可修改属性")
    @Test
    @SneakyThrows
    public void test_modifyDeviceAttribute() {
        final String sn = OpenApiUtil.shortUUID();
        final String modelNo = "CG-" + OpenApiUtil.randInt();
        final Integer userId = OpenApiUtil.randInt();

        mock_realTimeAttributes(sn, userId);
        mock_modifiableAttributes_all(sn, modelNo, userId);
        mock_fixedAttributes(sn, modelNo, userId);

        when(device4GService.queryDevice4GSimDO(any())).thenReturn(null);
        when(factoryDataQueryService.queryDeviceCustomerIdByUserSn(any())).thenReturn("cuid");

        final DeviceAttributesQuery query = new DeviceAttributesQuery();
        query.setSerialNumber(sn).setUserId(userId).setReturnFixedAttributes(true)
                .setReturnRealTimeAttributes(true).setReturnModifiableAttributes(true);
        Result<DeviceAttributes> queryResult = deviceAttributeService.getDeviceAttributes(query);
        final List<DeviceModifiableAttribute> queryAttrs = queryResult.getData().getModifiableAttributes();
        Collections.shuffle(queryAttrs);

        final DeviceAttributesModify modifyNothing = new DeviceAttributesModify().setSerialNumber(sn).setUserId(userId);
        final DeviceAttributesModify modifyAll = new DeviceAttributesModify().setSerialNumber(sn).setUserId(userId);
        final DeviceAttributesModify modifyPart = new DeviceAttributesModify().setSerialNumber(sn).setUserId(userId);
        /* 修改0个属性 */
        test_modifyDeviceAttributes(modifyNothing);
        /* 修改1个属性 */
        for (final DeviceModifiableAttribute queryAttr : queryAttrs) {
            final DeviceModifiableAttribute nextAttr = getNextAttr(queryAttr);
            if (nextAttr == null) continue;
            Assert.assertNotEquals(queryAttr.getValue(), nextAttr.getValue());
            final DeviceAttributesModify modifyOne = new DeviceAttributesModify().setSerialNumber(sn).setUserId(userId);
            modifyOne.getModifiableAttributes().add(nextAttr);
            modifyPart.getModifiableAttributes().add(nextAttr);
            modifyAll.getModifiableAttributes().add(nextAttr);
            test_modifyDeviceAttributes(modifyOne); /* 修改1个属性 */
            test_modifyDeviceAttributes(modifyPart); /* 修改部分属性 */
        }
        /* 修改所有属性 */
        test_modifyDeviceAttributes(modifyAll);

        when(userTierDeviceService.getIsNoVipOrFreeTier2(userId, sn)).thenReturn(true);
        queryResult = deviceAttributeService.getDeviceAttributes(query);
        assertTrue(queryResult.getData() != null);

    }

    private void test_modifyDeviceAttributes(DeviceAttributesModify modify) {
        final DeviceAttributeService.DeviceAttributeSource src = deviceAttributeService.getAttributeSource(modify.getSerialNumber());
        DeviceAttributeService.ModifySummary summary = deviceAttributeService.createModifySummary(modify, src);
        assertEquals("有错误属性", new LinkedHashSet<>(), summary.getErrorAttrs());
        assertEquals("有未知属性", new LinkedHashSet<>(), summary.getUnknowAttrs());
        final Set<String> modifyAttrNames = Stream.of(summary.getAppSettingAttrNames(), summary.getDeviceAttrNames())
                .flatMap(it -> it.stream()).map(it -> it.name()).collect(Collectors.toSet());
        final Set<String> expectAttrNames = modify.getModifiableAttributes().stream().map(it -> it.getName()).collect(Collectors.toSet());
        assertEquals("修改属性失败", expectAttrNames, modifyAttrNames);

        final Result result = deviceAttributeService.modifyDeviceAttributes(modify);
        assertEquals("修改属性失败! msg=" + result.getMsg(), Result.successFlag, result.getResult());
    }

    public static DeviceModifiableAttribute getNextAttr(DeviceModifiableAttribute queryAttr) {
        if (queryAttr.getName() == chargeAutoPowerOnCapacity.name()) return null;
        if (queryAttr.getType() == DeviceAttributeType.SWITCH) {
            final DeviceSwitchAttributes attr = (DeviceSwitchAttributes) queryAttr;
            return new DeviceSwitchAttributes().setName(queryAttr.getName())
                    .setValue(!attr.getValue());

        } else if (queryAttr.getType() == DeviceAttributeType.ENUM) {
            final DeviceEnumAttributes attr = (DeviceEnumAttributes) queryAttr;
            final int valueIndex = attr.getOptions().indexOf(attr.getValue());
            return new DeviceEnumAttributes().setName(queryAttr.getName())
                    .setValue(attr.getOptions().get((valueIndex + 1) % attr.getOptions().size()));

        } else if (queryAttr.getType() == DeviceAttributeType.OBJECT_ENUM) {
            final DeviceObjectEnumAttributes attr = (DeviceObjectEnumAttributes) queryAttr;
            final int valueIndex = attr.getOptions().indexOf(attr.getValue());
            return new DeviceModifiableAttribute().setName(queryAttr.getName())
                    .setValue(attr.getOptions().get((valueIndex + 1) % attr.getOptions().size()).getId());

        } else if (queryAttr.getType() == DeviceAttributeType.INT_RANGE) {
            final DeviceIntRangeAttributes attr = (DeviceIntRangeAttributes) queryAttr;
            int nextValue = attr.getValue() + attr.getIntRange().getInterval();
            return new DeviceIntRangeAttributes().setName(queryAttr.getName())
                    .setValue(nextValue > attr.getIntRange().getMax() ? attr.getIntRange().getMin() : nextValue);

        } else if (queryAttr.getType() == DeviceAttributeType.TEXT) {
            final DeviceTextAttributes attr = (DeviceTextAttributes) queryAttr;
            final String value = StringUtils.isBlank(attr.getValue()) ? "text" : attr.getValue();
            return new DeviceTextAttributes().setName(queryAttr.getName())
                    .setValue(StringUtils.isAllUpperCase(value) ? value.toUpperCase() : value.toLowerCase());

        } else if (queryAttr.getType() == DeviceAttributeType.CHECKBOX) {
            final DeviceCheckBoxAttributes attr = (DeviceCheckBoxAttributes) queryAttr;
            final List<String> value = attr.getValue();
            final List<String> nextValue = new LinkedList<>();
            for (final DeviceCheckBoxAttributes.Item item : attr.getOptions()) {
                item.getCheckables().stream().filter(it -> !value.contains(it)).limit(item.getAt_most()).forEach(nextValue::add);
            }
            return new DeviceCheckBoxAttributes().setName(queryAttr.getName()).setValue(nextValue);
        } else {
            throw new RuntimeException("不支持的DeviceAttributeType:" + queryAttr.getType());
        }
    }

    @Test
    public void test_DeviceObjectEnumAttributes() {
        final DeviceObjectEnumAttributes attr = new DeviceObjectEnumAttributes()
                .setOptions(Arrays.asList(
                        new DeviceObjectEnumAttributes.Item().setId("1"),
                        new DeviceObjectEnumAttributes.Item().setId("2")
                ));
//        Assert.assertEquals(new DeviceObjectEnumAttributes.Item().setId("1"), attr.defaultValue());
        assertEquals("1", attr.defaultValue().getId());
    }

    @Test
    public void test_getModifiableAttr() {
        DeviceSwitchAttributes pirCooldownSwitch = new DeviceSwitchAttributes().setName(DeviceAttributeName.pirCooldownSwitch.name())
                .setDisabled(true).setValue(false);
        DeviceEnumAttributes pirCooldownTime = new DeviceEnumAttributes().setName(DeviceAttributeName.pirCooldownTime.name())
                .setValue("10s")
                .setOptions(Arrays.asList("10s", "20s"))
                .setDisabledOptions(Arrays.asList());

        List<DeviceModifiableAttribute> attrList = new LinkedList<>();
        attrList.add(pirCooldownSwitch);
        attrList.add(pirCooldownTime);

        assertEquals(pirCooldownSwitch, DeviceModifiableAttribute.getSwitchAttr(attrList, DeviceAttributeName.pirCooldownSwitch));
        assertEquals(pirCooldownTime, DeviceModifiableAttribute.getEnumAttr(attrList, DeviceAttributeName.pirCooldownTime));
        Assert.assertNull(DeviceModifiableAttribute.getSwitchAttr(attrList, DeviceAttributeName.pirCooldownTime));
        Assert.assertNull(DeviceModifiableAttribute.getEnumAttr(attrList, DeviceAttributeName.pirCooldownSwitch));
        Assert.assertNull(DeviceModifiableAttribute.getSwitchAttr(null, DeviceAttributeName.pirCooldownSwitch));
        Assert.assertNull(DeviceModifiableAttribute.getEnumAttr(null, DeviceAttributeName.pirCooldownTime));

        ArrayList<DeviceModifiableAttribute> attrList2 = new ArrayList<>();
        attrList2.add(null);
        Assert.assertNull(DeviceModifiableAttribute.getSwitchAttr(attrList2, DeviceAttributeName.pirCooldownSwitch));
        Assert.assertNull(DeviceModifiableAttribute.getEnumAttr(attrList2, DeviceAttributeName.pirCooldownTime));
    }

    @Test
    public void testProcessSwitchAttribute() {
        // 准备测试数据
        ThingModel.ThingEntity entity = new ThingModel.ThingEntity();
        entity.setType("SWITCH");
        entity.setIdentifier("test_switch");
        entity.setDefaultValue(1);

        // 测试情况1: propertyJson为null
        DeviceAttributeService.processThingModelAttribute(attrList, supportJson, null, entity);

        assertEquals(1, attrList.size());
        DeviceSwitchAttributes attr = (DeviceSwitchAttributes) attrList.get(0);
        assertEquals("test_switch", attr.getName());
        assertTrue(attr.getValue());

        // 测试情况2: propertyJson中有值
        attrList.clear();
        propertyJson.put("test_switch", 0L);
        DeviceAttributeService.processThingModelAttribute(attrList, supportJson, propertyJson, entity);

        assertEquals(1, attrList.size());
        attr = (DeviceSwitchAttributes) attrList.get(0);
        assertEquals("test_switch", attr.getName());
        assertFalse(attr.getValue());
    }

    @Test
    public void testProcessEnumAttribute() {
        // 准备测试数据
        ThingModel.ThingEntity entity = new ThingModel.ThingEntity();
        entity.setType("ENUM");
        entity.setIdentifier("test_enum");
        entity.setDefaultValue("default");
        entity.setOptionName("enum_options");

        supportJson.put("enum_options", Arrays.asList("option1", "option2", "default"));

        // 测试情况1: propertyJson为null
        DeviceAttributeService.processThingModelAttribute(attrList, supportJson, null, entity);

        assertEquals(1, attrList.size());
        DeviceEnumAttributes attr = (DeviceEnumAttributes) attrList.get(0);
        assertEquals("test_enum", attr.getName());
        assertEquals("default", attr.getValue());
        assertEquals(3, attr.getOptions().size());

        // 测试情况2: propertyJson中有值
        attrList.clear();
        propertyJson.put("test_enum", "option1");
        DeviceAttributeService.processThingModelAttribute(attrList, supportJson, propertyJson, entity);

        assertEquals(1, attrList.size());
        attr = (DeviceEnumAttributes) attrList.get(0);
        assertEquals("option1", attr.getValue());
    }

    @Test
    public void testProcessRangeAttribute() {
        // 准备测试数据
        ThingModel.ThingEntity entity = new ThingModel.ThingEntity();
        entity.setType("RANGE");
        entity.setIdentifier("test_range");
        entity.setDefaultValue(10);
        entity.setOptionName("range_options");

        JSONObject rangeOptions = new JSONObject();
        rangeOptions.put("min", 0);
        rangeOptions.put("max", 100);
        rangeOptions.put("step", 1);
        supportJson.put("range_options", rangeOptions);

        // 测试情况1: propertyJson为null
        DeviceAttributeService.processThingModelAttribute(attrList, supportJson, null, entity);

        assertEquals(1, attrList.size());
        DeviceIntRangeAttributes attr = (DeviceIntRangeAttributes) attrList.get(0);
        assertEquals("test_range", attr.getName());
        assertEquals(Integer.valueOf(10), attr.getValue());

        // 测试情况2: propertyJson中有值
        attrList.clear();
        propertyJson.put("test_range", 50L);
        DeviceAttributeService.processThingModelAttribute(attrList, supportJson, propertyJson, entity);

        assertEquals(1, attrList.size());
        attr = (DeviceIntRangeAttributes) attrList.get(0);
        assertEquals(Integer.valueOf(50), attr.getValue());
    }

    @Test
    public void testProcessAttributeWithSupportName() {
        // 准备测试数据
        ThingModel.ThingEntity entity = new ThingModel.ThingEntity();
        entity.setType("SWITCH");
        entity.setIdentifier("test_switch");
        entity.setDefaultValue(1);
        entity.setSupportName("feature_support");

        // 测试情况1: 功能不支持
        supportJson.put("feature_support", false);
        DeviceAttributeService.processThingModelAttribute(attrList, supportJson, null, entity);
        assertEquals(0, attrList.size());

        // 测试情况2: 功能支持
        supportJson.put("feature_support", true);
        DeviceAttributeService.processThingModelAttribute(attrList, supportJson, null, entity);
        assertEquals(1, attrList.size());
    }

    @Test
    public void testProcessAttributeWithInvalidData() {
        // 准备测试数据
        ThingModel.ThingEntity entity = new ThingModel.ThingEntity();
        entity.setType("ENUM");
        entity.setIdentifier("test_enum");
        entity.setDefaultValue("default");
        entity.setOptionName("enum_options");

        // 测试情况: supportJson中的options数据类型错误
        supportJson.put("enum_options", "invalid_data");
        DeviceAttributeService.processThingModelAttribute(attrList, supportJson, null, entity);
        assertEquals(0, attrList.size());
    }


    @Test
    public void testAddThingModelAttributeToSummary_Switch() {
        // 准备测试数据
        DeviceModifiableAttribute attr = mock(DeviceModifiableAttribute.class);
        when(attr.getName()).thenReturn("testSwitch");
        when(attr.getValue()).thenReturn(true);

        ThingModel.ThingEntity thingEntity = mock(ThingModel.ThingEntity.class);
        when(thingEntity.getType()).thenReturn("SWITCH");

        // 执行测试
        DeviceAttributeService.addThingModelAttributeToSummary(
                supportJson, summary, appSetting, attr,
                Optional.of(thingEntity), true
        );

        // 验证结果
        verify(appSetting).addToPropertyJson("testSwitch", 1);
        verify(summary).getAppSettingAttrNames();
    }

    @Test
    public void testAddThingModelAttributeToSummary_Enum_Valid() {
        // 准备测试数据
        supportJson.put("testOptions", Arrays.asList("option1", "option2"));

        DeviceModifiableAttribute attr = mock(DeviceModifiableAttribute.class);
        when(attr.getName()).thenReturn("testEnum");
        when(attr.getValue()).thenReturn("option1");

        ThingModel.ThingEntity thingEntity = mock(ThingModel.ThingEntity.class);
        when(thingEntity.getType()).thenReturn("ENUM");
        when(thingEntity.getOptionName()).thenReturn("testOptions");

        // 执行测试
        DeviceAttributeService.addThingModelAttributeToSummary(
                supportJson, summary, appSetting, attr,
                Optional.of(thingEntity), "option1"
        );

        // 验证结果
        verify(appSetting).addToPropertyJson("testEnum", "option1");
        verify(summary, never()).getErrorAttrs();
    }

    @Test
    public void testAddThingModelAttributeToSummary_Enum_Invalid() {
        // 准备测试数据
        supportJson.put("testOptions", Arrays.asList("option1", "option2"));

        DeviceModifiableAttribute attr = mock(DeviceModifiableAttribute.class);
        when(attr.getName()).thenReturn("testEnum");
        when(attr.getValue()).thenReturn("invalidOption");

        ThingModel.ThingEntity thingEntity = mock(ThingModel.ThingEntity.class);
        when(thingEntity.getType()).thenReturn("ENUM");
        when(thingEntity.getOptionName()).thenReturn("testOptions");

        // 执行测试
        DeviceAttributeService.addThingModelAttributeToSummary(
                supportJson, summary, appSetting, attr,
                Optional.of(thingEntity), "invalidOption"
        );

        // 验证结果
        verify(appSetting, never()).addToPropertyJson(anyString(), any());
        verify(summary).getErrorAttrs();
    }

    @Test
    public void testAddThingModelAttributeToSummary_Range_Valid() {
        // 准备测试数据
        LinkedTreeMap<String, Number> rangeJson = new LinkedTreeMap<>();
        rangeJson.put("min", 1);
        rangeJson.put("max", 10);
        rangeJson.put("interval", 1);
        supportJson.put("testRange", rangeJson);

        DeviceModifiableAttribute attr = mock(DeviceModifiableAttribute.class);
        when(attr.getName()).thenReturn("testRange");
        when(attr.getValue()).thenReturn(5);

        ThingModel.ThingEntity thingEntity = mock(ThingModel.ThingEntity.class);
        when(thingEntity.getType()).thenReturn("RANGE");
        when(thingEntity.getOptionName()).thenReturn("testRange");

        // 执行测试
        DeviceAttributeService.addThingModelAttributeToSummary(
                supportJson, summary, appSetting, attr,
                Optional.of(thingEntity), 5
        );

        // 验证结果
        verify(appSetting).addToPropertyJson("testRange", 5);
        verify(summary, never()).getErrorAttrs();
    }

    @Test
    public void testAddThingModelAttributeToSummary_Range_Invalid() {
        // 准备测试数据
        LinkedTreeMap<String, Number> rangeJson = new LinkedTreeMap<>();
        rangeJson.put("min", 1);
        rangeJson.put("max", 10);
        rangeJson.put("interval", 1);
        supportJson.put("testRange", rangeJson);

        DeviceModifiableAttribute attr = mock(DeviceModifiableAttribute.class);
        when(attr.getName()).thenReturn("testRange");
        when(attr.getValue()).thenReturn(20);

        ThingModel.ThingEntity thingEntity = mock(ThingModel.ThingEntity.class);
        when(thingEntity.getType()).thenReturn("RANGE");
        when(thingEntity.getOptionName()).thenReturn("testRange");

        // 执行测试
        DeviceAttributeService.addThingModelAttributeToSummary(
                supportJson, summary, appSetting, attr,
                Optional.of(thingEntity), 20
        );

        // 验证结果
        verify(appSetting, never()).addToPropertyJson(anyString(), any());
        verify(summary).getErrorAttrs();
    }

    @Test
    public void testAddThingModelAttributeToSummary_Exception() {
        // 准备测试数据
        DeviceModifiableAttribute attr = mock(DeviceModifiableAttribute.class);
        when(attr.getName()).thenReturn("test");
        when(attr.getValue()).thenThrow(new RuntimeException("Test Exception"));

        ThingModel.ThingEntity thingEntity = mock(ThingModel.ThingEntity.class);
        when(thingEntity.getType()).thenReturn("SWITCH");

        // 执行测试 - 不应抛出异常
        DeviceAttributeService.addThingModelAttributeToSummary(
                supportJson, summary, appSetting, attr,
                Optional.of(thingEntity), true
        );

        // 验证结果
        verify(appSetting, never()).addToPropertyJson(anyString(), any());
    }

}
