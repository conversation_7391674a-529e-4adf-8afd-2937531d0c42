package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.device.model.DeviceModelIconDO;
import com.addx.iotcamera.bean.domain.DeviceManualDO;
import com.addx.iotcamera.config.device.DeviceSettingConfig;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceAttributeServiceTest {
    @InjectMocks
    private DeviceAttributeService deviceAttributeService;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private DeviceModelConfigService deviceModelConfigService;

    @Mock
    private DeviceSettingConfig deviceSettingConfig;
    @Mock
    private DeviceModelIconService deviceModelIconService;

    @Test
    public void test_getDefaultPirSensitivity(){
        String sn = "sn";
        String modelNo = "CG1";
        DeviceAttributeService.DeviceAttributeSource deviceAttributeSource = deviceAttributeService.getAttributeSource(sn);
        DeviceManualDO deviceManualDO = new DeviceManualDO();
        deviceManualDO.setSerialNumber(sn);
        deviceManualDO.setModelNo(modelNo);
        when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(deviceManualDO);
        when(deviceModelConfigService.queryRowDeviceModelByModelNo(any())).thenReturn(null);
        when(deviceSettingConfig.queryMotionSensitivity(any(),any())).thenReturn(2);
        try{
            deviceAttributeSource.getDefaultPirSensitivity();
        }catch (Exception e){

        }

    }

    @Test
    public void test_getModelIcon(){
        when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);
        when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(new DeviceManualDO());
        DeviceModelIconDO deviceModelIconDO = deviceAttributeService.getAttributeSource("").getModelIcon();
        assertNotNull(deviceModelIconDO);
    }
}
