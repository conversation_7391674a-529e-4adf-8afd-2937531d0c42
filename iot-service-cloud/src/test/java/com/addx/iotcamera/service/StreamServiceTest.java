package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.WebsocketTicketVO;
import com.addx.iotcamera.bean.device.WebsocketTicketRequest;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.device.WowconfigCache;
import com.addx.iotcamera.config.TwilioConfig;
import com.addx.iotcamera.config.Wowconfig;
import com.addx.iotcamera.config.device.DeviceKeepAliveConfig;
import com.addx.iotcamera.config.device.KeepAliveParams;
import com.addx.iotcamera.kiss.bean.IceServer;
import com.addx.iotcamera.kiss.bean.SignalServer;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.service.device.DeviceAsyncService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceWowConfigParamService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.service.live.TwilioService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class StreamServiceTest {

    @InjectMocks
    private StreamService streamService;
    @Mock
    private KissWsService kissWsService;
    @Mock
    private TwilioConfig twilioConfig;
    @Mock
    private TwilioService twilioService;
    @Mock
    private DeviceKeepAliveConfig deviceKeepAliveConfig;
    @Mock
    private Wowconfig wowconfig;
    @Mock
    private UdpService udpService;
    @Mock
    private IKissService kissService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private RedisService redisService;
    @Mock
    private DeviceModelConfigService deviceModelConfigService;

    @Mock
    private MqttSender mqttSender;

    @Mock
    private DeviceAsyncService deviceAsyncService;

    @Mock
    private DeviceWowConfigParamService deviceWowConfigParamService;

    @Before
    public void before() {
    }

    @DisplayName("getWebrtcTicket_老设备_老app")
    @Test
    public void test_getWebrtcTicket_oldDev_oldApp() {
        Integer userId = OpenApiUtil.randInt();
        String sn = OpenApiUtil.shortUUID();

        final SignalServer signalServer = new SignalServer().setIpAddress("*********").setSecret("secret").setAddr("kiss");
        when(kissService.getSignalServerAddr(sn, false)).thenReturn(signalServer);
        when(twilioConfig.getWhiteSnSet()).thenReturn(new HashSet<>());
        final IceServer iceServer = new IceServer().setUrl("url").setCredential("cred").setIpAddress("127.0.0.1").setUsername("user");
        when(kissService.chooseTurnServer(sn, sn)).thenReturn(Arrays.asList(iceServer));
        when(kissService.chooseTurnServer(sn, userId + "")).thenReturn(Arrays.asList(iceServer));
        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(false);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);
        when(userRoleService.getSerialNumbersByUserId(userId)).thenReturn(Arrays.asList(sn));
        streamService.setWebrtcTicketValidDurationMillis(1000L);

        Assert.assertNotNull(streamService.getWebrtcTicket(userId, sn, null, sn, false));
    }

    @DisplayName("getWebrtcTicket_老设备_新app")
    @Test
    public void test_getWebrtcTicket_oldDev_newApp() {
        Integer userId = OpenApiUtil.randInt();
        String sn = OpenApiUtil.shortUUID();

        when(deviceWowConfigParamService.queryWowConfigCache(any())).thenReturn(null);
        doNothing().when(mqttSender).sendRetainedMessage(any(), any(), any());
        doNothing().when(deviceAsyncService).checkDeviceAwake(any(), any(), any(), any());
        final SignalServer signalServer = new SignalServer().setIpAddress("*********").setSecret("secret").setAddr("kiss");
        when(kissService.getSignalServerAddr(sn, false)).thenReturn(signalServer);
        when(twilioConfig.getWhiteSnSet()).thenReturn(new HashSet<>());
        final IceServer iceServer = new IceServer().setUrl("url").setCredential("cred").setIpAddress("127.0.0.1").setUsername("user");
        when(kissService.chooseTurnServer(sn, sn)).thenReturn(Arrays.asList(iceServer));
        when(kissService.chooseTurnServer(sn, userId + "")).thenReturn(Arrays.asList(iceServer));
        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(false);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);
        when(userRoleService.getSerialNumbersByUserId(userId)).thenReturn(Arrays.asList(sn));
        streamService.setWebrtcTicketValidDurationMillis(1000L);

        Assert.assertNotNull(streamService.getWebrtcTicket(userId, sn, null, sn, true));
    }

    @DisplayName("getWebrtcTicket_新设备_新app")
    @Test
    public void test_getWebrtcTicket_newDev_newApp() {
        Integer userId = OpenApiUtil.randInt();
        String sn = OpenApiUtil.shortUUID();

        when(deviceWowConfigParamService.queryWowConfigCache(any())).thenReturn(null);
        doNothing().when(mqttSender).sendRetainedMessage(any(), any(), any());
        doNothing().when(deviceAsyncService).checkDeviceAwake(any(), any(), any(), any());
        final SignalServer signalServer = new SignalServer().setIpAddress("*********").setSecret("secret").setAddr("kiss");
        when(kissService.getSignalServerAddr(sn, false)).thenReturn(signalServer);
        when(twilioConfig.getWhiteSnSet()).thenReturn(new HashSet<>());
        final IceServer iceServer = new IceServer().setUrl("url").setCredential("cred").setIpAddress("127.0.0.1").setUsername("user");
        when(kissService.chooseTurnServer(sn, sn)).thenReturn(Arrays.asList(iceServer));
        when(kissService.chooseTurnServer(sn, userId + "")).thenReturn(Arrays.asList(iceServer));
        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(true);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);
        when(userRoleService.getSerialNumbersByUserId(userId)).thenReturn(Arrays.asList(sn));
        streamService.setWebrtcTicketValidDurationMillis(1000L);

        Assert.assertNotNull(streamService.getWebrtcTicket(userId, sn, null, sn, true));
    }

    @DisplayName("getWebrtcTicket_新设备_老app")
    @Test
    public void test_getWebrtcTicket_newDev_oldApp() {
        Integer userId = OpenApiUtil.randInt();
        String sn = OpenApiUtil.shortUUID();
        when(deviceWowConfigParamService.queryWowConfigCache(any())).thenReturn(null);

        final SignalServer signalServer = new SignalServer().setIpAddress("*********").setSecret("secret").setAddr("kiss");
        when(kissService.getSignalServerAddr(sn, false)).thenReturn(signalServer);
        when(twilioConfig.getWhiteSnSet()).thenReturn(new HashSet<>());
        final IceServer iceServer = new IceServer().setUrl("url").setCredential("cred").setIpAddress("127.0.0.1").setUsername("user");
        when(kissService.chooseTurnServer(sn, sn)).thenReturn(Arrays.asList(iceServer));
        when(kissService.chooseTurnServer(sn, userId + "")).thenReturn(Arrays.asList(iceServer));
        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(true);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);
        when(userRoleService.getSerialNumbersByUserId(userId)).thenReturn(Arrays.asList(sn));
        streamService.setWebrtcTicketValidDurationMillis(1000L);

        Assert.assertNotNull(streamService.getWebrtcTicket(userId, sn, null, sn, false));
    }

    @Test
    public void test_getWebsocketTicket() {
        String sn = OpenApiUtil.shortUUID();
        String modelNo = OpenApiUtil.shortUUID();
        test_getWebsocketTicket(sn, modelNo);
    }

    public WebsocketTicketVO test_getWebsocketTicket(String sn, String modelNo) {
        final SignalServer signalServer = new SignalServer().setIpAddress("*********").setSecret("secret").setAddr("kiss");
        when(kissService.getSignalServerAddr(sn, true)).thenReturn(signalServer);
        when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(modelNo);
        {
            when(udpService.calculateDynamicDTIM(sn, modelNo)).thenReturn(7);
            final KeepAliveParams keepAliveParams = new KeepAliveParams().setInterval(10).setTimeout(11).setDtim(5)
                    .setRetryInterval(3).setRetryCount(4).setBan1Ref(101).setBan2Ref(102).setBan3Ref(103);
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(keepAliveParams);
            when(this.wowconfig.getRetryInterval()).thenReturn(4);
            when(this.wowconfig.getRetryCount()).thenReturn(5);
            when(this.wowconfig.getBan1Ref()).thenReturn(201);
            when(this.wowconfig.getBan2Ref()).thenReturn(202);
            when(this.wowconfig.getBan3Ref()).thenReturn(203);
            when(deviceWowConfigParamService.querySupportWowConfigCache()).thenReturn(new HashSet<>());
            when(deviceWowConfigParamService.queryWowConfigCache(any())).thenReturn(null);
        }
        final WebsocketTicketRequest req = new WebsocketTicketRequest();
        req.setSn(sn);
        final WebsocketTicketVO result = streamService.getWebsocketTicket(req);
        Assert.assertNotNull(result);
        Assert.assertEquals(7L, (long) result.getKeepAliveParams().getDtim());
        return result;
    }

    @DisplayName("获取保活参数_无白名单")
    @Test
    public void test_getKeepAliveParams_notWowconfigWhite() {
        String sn = OpenApiUtil.shortUUID();
        String modelNo = OpenApiUtil.shortUUID();

        when(udpService.calculateDynamicDTIM(sn, modelNo)).thenReturn(7);
        final KeepAliveParams keepAliveParams = new KeepAliveParams().setInterval(10).setTimeout(11).setDtim(5)
                .setRetryInterval(3).setRetryCount(4).setBan1Ref(1).setBan2Ref(2).setBan3Ref(null);
        when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(keepAliveParams);
        when(this.wowconfig.getRetryInterval()).thenReturn(4);
        when(this.wowconfig.getRetryCount()).thenReturn(5);
        when(this.wowconfig.getBan1Ref()).thenReturn(201);
        when(this.wowconfig.getBan2Ref()).thenReturn(202);
        when(this.wowconfig.getBan3Ref()).thenReturn(203);

        when(deviceWowConfigParamService.querySupportWowConfigCache()).thenReturn(new HashSet<>(Arrays.asList("sn")));
        when(deviceWowConfigParamService.queryWowConfigCache(any())).thenReturn(null);

        final KeepAliveParams result = streamService.getKeepAliveParams(sn, modelNo);
        Assert.assertNotNull(result);
        Assert.assertEquals(7L, (long) result.getDtim());
        Assert.assertEquals(1L, (long) result.getBan1Ref());
        Assert.assertEquals(2L, (long) result.getBan2Ref());
        Assert.assertEquals(203L, (long) result.getBan3Ref());
    }

    @DisplayName("获取保活参数_有白名单")
    @Test
    public void test_getKeepAliveParams() {
        String sn = OpenApiUtil.shortUUID();
        String modelNo = OpenApiUtil.shortUUID();

        when(udpService.calculateDynamicDTIM(sn, modelNo)).thenReturn(7);
        final KeepAliveParams keepAliveParams = new KeepAliveParams().setInterval(10).setTimeout(11).setDtim(5)
                .setRetryInterval(3).setRetryCount(4).setBan1Ref(101).setBan2Ref(102).setBan3Ref(103);
        when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(keepAliveParams);
        when(this.wowconfig.getRetryInterval()).thenReturn(4);
        when(this.wowconfig.getRetryCount()).thenReturn(5);
        when(this.wowconfig.getBan1Ref()).thenReturn(201);
        when(this.wowconfig.getBan2Ref()).thenReturn(202);
        when(this.wowconfig.getBan3Ref()).thenReturn(203);

        final WowconfigCache cache = new WowconfigCache();
        cache.setDtim(9);
        cache.setTimeout(400);
        cache.setInterval(401);
        cache.setRetryCount(4);
        cache.setRetryInterval(500);
        when(deviceWowConfigParamService.queryWowConfigCache(any())).thenReturn(cache);
        when(deviceWowConfigParamService.querySupportWowConfigCache()).thenReturn(new HashSet<>(Arrays.asList(sn)));


        final KeepAliveParams result = streamService.getKeepAliveParams(sn, modelNo);
        Assert.assertNotNull(result);
        Assert.assertEquals(9L, (long) result.getDtim());
    }

    @Test
    public void test_getWebrtcConfig_hasTwillio() {
        String sn = OpenApiUtil.shortUUID();

        final IceServer iceServer1 = new IceServer().setUrl("url1").setCredential("cred1").setIpAddress("127.0.0.1").setUsername("user1");
        when(kissService.chooseTurnServer(sn, sn)).thenReturn(Arrays.asList(iceServer1));
        final IceServer iceServer2 = new IceServer().setUrl("url2").setCredential("cred2").setIpAddress("*********").setUsername("user2");
        when(twilioService.getIceServerList()).thenReturn(Arrays.asList(iceServer2));
        when(twilioConfig.getWhiteSnSet()).thenReturn(Collections.singleton(sn));

        final JSONObject result = streamService.getWebrtcConfig(sn);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.toJSONString().contains("url2"));
    }

    @Test
    public void test_getWebrtcConfig() {
        String sn = OpenApiUtil.shortUUID();

        final IceServer iceServer1 = new IceServer().setUrl("url1").setCredential("cred1").setIpAddress("127.0.0.1").setUsername("user1");
        when(kissService.chooseTurnServer(sn, sn)).thenReturn(Arrays.asList(iceServer1));
        final IceServer iceServer2 = new IceServer().setUrl("url2").setCredential("cred2").setIpAddress("*********").setUsername("user2");
        when(twilioService.getIceServerList()).thenReturn(Arrays.asList(iceServer2));
        when(twilioConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());

        final JSONObject result = streamService.getWebrtcConfig(sn);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.toJSONString().contains("url1"));
    }

    @Test
    public void test_updateKeepAliveParamsIfChange_1() {
        String sn = OpenApiUtil.shortUUID();
        String modelNo = OpenApiUtil.shortUUID();
        test_getWebsocketTicket(sn, modelNo);

        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(false);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);
        Assert.assertEquals(1, streamService.updateKeepAliveParamsIfChange(sn, modelNo, false));
    }

    //    /*
    @Test
    public void test_updateKeepAliveParamsIfChange_3() {
        String sn = OpenApiUtil.shortUUID();
        String modelNo = OpenApiUtil.shortUUID();
        test_getWebsocketTicket(sn, modelNo);

        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(true);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);

        final KeepAliveParams keepAliveParams = new KeepAliveParams();
        when(redisService.get("websocket:keepAliveParams:" + sn)).thenReturn(JSON.toJSONString(keepAliveParams));
        when(kissService.updateKeepAliveParams(eq(sn), any())).thenReturn(false);
        Assert.assertEquals(3, streamService.updateKeepAliveParamsIfChange(sn, modelNo, false));
    }

    @Test
    public void test_updateKeepAliveParamsIfChange_0() {
        String sn = OpenApiUtil.shortUUID();
        String modelNo = OpenApiUtil.shortUUID();
        test_getWebsocketTicket(sn, modelNo);

        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(true);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);

        final KeepAliveParams keepAliveParams = new KeepAliveParams();
        when(redisService.get("websocket:keepAliveParams:" + sn)).thenReturn(JSON.toJSONString(keepAliveParams));
        when(kissService.updateKeepAliveParams(eq(sn), any())).thenReturn(true);
        Assert.assertEquals(0, streamService.updateKeepAliveParamsIfChange(sn, modelNo, false));
    }

    @Test
    public void test_updateKeepAliveParamsIfChange_2() {
        String sn = OpenApiUtil.shortUUID();
        String modelNo = OpenApiUtil.shortUUID();
        final WebsocketTicketVO websocketTicket = test_getWebsocketTicket(sn, modelNo);

        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(true);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);

        when(redisService.get("websocket:keepAliveParams:" + sn)).thenReturn(JSON.toJSONString(websocketTicket.getKeepAliveParams()));
        when(kissService.updateKeepAliveParams(eq(sn), any())).thenReturn(true);
        Assert.assertEquals(2, streamService.updateKeepAliveParamsIfChange(sn, modelNo, true));
    }
//    */

    @Test
    public void test_getKeepAliveParamsByModelNo() {
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(61);
                setWsKeepAliveTimeout(120);
                setMinWsKeepAliveInterval(70);
                setMaxWsKeepAliveInterval(79);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(61), result.getInterval());
            Assert.assertEquals(new Integer(120), result.getTimeout());
            Assert.assertEquals(new Integer(70), result.getMinInterval());
            Assert.assertEquals(new Integer(79), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(61);
                setWsKeepAliveTimeout(120);
                setMinWsKeepAliveInterval(0);
                setMaxWsKeepAliveInterval(79);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(61), result.getInterval());
            Assert.assertEquals(new Integer(120), result.getTimeout());
            Assert.assertEquals(new Integer(90), result.getMinInterval());
            Assert.assertEquals(new Integer(99), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(61);
                setWsKeepAliveTimeout(120);
                setMinWsKeepAliveInterval(79);
                setMaxWsKeepAliveInterval(70);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(61), result.getInterval());
            Assert.assertEquals(new Integer(120), result.getTimeout());
            Assert.assertEquals(new Integer(90), result.getMinInterval());
            Assert.assertEquals(new Integer(99), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(61);
                setWsKeepAliveTimeout(120);
                setMinWsKeepAliveInterval(null);
                setMaxWsKeepAliveInterval(79);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(61), result.getInterval());
            Assert.assertEquals(new Integer(120), result.getTimeout());
            Assert.assertEquals(new Integer(90), result.getMinInterval());
            Assert.assertEquals(new Integer(99), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(61);
                setWsKeepAliveTimeout(120);
                setMinWsKeepAliveInterval(70);
                setMaxWsKeepAliveInterval(null);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(61), result.getInterval());
            Assert.assertEquals(new Integer(120), result.getTimeout());
            Assert.assertEquals(new Integer(90), result.getMinInterval());
            Assert.assertEquals(new Integer(99), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(61);
                setWsKeepAliveTimeout(120);
                setMinWsKeepAliveInterval(0);
                setMaxWsKeepAliveInterval(79);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(null);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(61), result.getInterval());
            Assert.assertEquals(new Integer(120), result.getTimeout());
            Assert.assertEquals(new Integer(61), result.getMinInterval());
            Assert.assertEquals(new Integer(61), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(61);
                setWsKeepAliveTimeout(120);
                setMinWsKeepAliveInterval(70);
                setMaxWsKeepAliveInterval(69);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(0);
                setMaxInterval(90);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(61), result.getInterval());
            Assert.assertEquals(new Integer(120), result.getTimeout());
            Assert.assertEquals(new Integer(61), result.getMinInterval());
            Assert.assertEquals(new Integer(61), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(61);
                setWsKeepAliveTimeout(120);
                setMinWsKeepAliveInterval(0);
                setMaxWsKeepAliveInterval(79);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(100);
                setMaxInterval(90);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(61), result.getInterval());
            Assert.assertEquals(new Integer(120), result.getTimeout());
            Assert.assertEquals(new Integer(61), result.getMinInterval());
            Assert.assertEquals(new Integer(61), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(61);
                setWsKeepAliveTimeout(120);
                setMinWsKeepAliveInterval(0);
                setMaxWsKeepAliveInterval(79);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(61), result.getInterval());
            Assert.assertEquals(new Integer(120), result.getTimeout());
            Assert.assertEquals(new Integer(90), result.getMinInterval());
            Assert.assertEquals(new Integer(99), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(null);
                setWsKeepAliveTimeout(120);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(140), result.getInterval());
            Assert.assertEquals(new Integer(310), result.getTimeout());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setWsKeepAliveInterval(61);
                setWsKeepAliveTimeout(null);
            }});
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(140), result.getInterval());
            Assert.assertEquals(new Integer(310), result.getTimeout());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(null);
            when(deviceKeepAliveConfig.getConfigByModelNo(modelNo)).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(140), result.getInterval());
            Assert.assertEquals(new Integer(310), result.getTimeout());
        }
        {
            when(deviceKeepAliveConfig.getConfigByModelNo(any())).thenReturn(new KeepAliveParams() {{
                setInterval(140);
                setTimeout(310);
            }});
            KeepAliveParams result = streamService.getKeepAliveParamsByModelNo(null);
            Assert.assertNotNull(result);
            Assert.assertEquals(new Integer(140), result.getInterval());
            Assert.assertEquals(new Integer(310), result.getTimeout());
        }
    }

    @Test
    public void test_DeviceKeepAliveConfig() {
        KeepAliveParams params1 = new KeepAliveParams().setInterval(100);
        KeepAliveParams params2 = new KeepAliveParams().setInterval(150);
        DeviceKeepAliveConfig config = new DeviceKeepAliveConfig();
        config.setConfig(ImmutableMap.<String, KeepAliveParams>builder()
                .put("model1", params1)
                .put("default", params2)
                .build());
        Assert.assertEquals(params1, config.getConfigByModelNo("model1"));
        Assert.assertEquals(params2, config.getConfigByModelNo("default"));
        Assert.assertEquals(params2, config.getConfigByModelNo("model2"));
        Assert.assertEquals(params2, config.getConfigByModelNo(""));
        Assert.assertEquals(params2, config.getConfigByModelNo(null));
    }

}
