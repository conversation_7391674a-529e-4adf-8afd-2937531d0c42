package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.db.user.UserDiscountBannerReportDO;
import com.addx.iotcamera.bean.exception.ParamException;
import com.addx.iotcamera.dao.user.IUserDiscountBannerReportDAO;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UserDiscountBannerReportServiceTest {

    @InjectMocks
    private UserDiscountBannerReportService userDiscountBannerReportService;

    @Mock
    private IUserDiscountBannerReportDAO userDiscountBannerReportDAO;

    @Test
    @DisplayName("插入用户Banner报告")
    public void test_insertUserBannerReport() {
        // 模拟 DAO 层行为
        when(userDiscountBannerReportDAO.insertUserDiscountBannerReport(any(UserDiscountBannerReportDO.class))).thenReturn(1);

        // 调用方法
        userDiscountBannerReportService.insertUserBannerReport(new UserDiscountBannerReportDO());

        // 验证 DAO 层是否被正确调用
        verify(userDiscountBannerReportDAO, times(1)).insertUserDiscountBannerReport(any(UserDiscountBannerReportDO.class));
    }

    @Test
    @DisplayName("查询最后一个用户Banner报告")
    public void test_queryLastUserBannerReport() {
        // 模拟 DAO 层行为
        UserDiscountBannerReportDO expectedReport = new UserDiscountBannerReportDO();
        when(userDiscountBannerReportDAO.queryLastUserBannerReport(1)).thenReturn(expectedReport);

        // 调用方法
        UserDiscountBannerReportDO actualReport = userDiscountBannerReportService.queryLastUserBannerReport(1);

        // 验证结果
        org.junit.Assert.assertEquals(expectedReport, actualReport);
    }

    @Test
    @DisplayName("更新报告 - 有效 ID")
    public void test_updateReport_ValidId() {
        // 模拟 DAO 层行为
        Long id = 1L;
        UserDiscountBannerReportDO reportDO = new UserDiscountBannerReportDO();
        when(userDiscountBannerReportDAO.queryById(id)).thenReturn(reportDO);

        // 调用方法
        userDiscountBannerReportService.updateReport(id);

        // 验证 DAO 层是否被正确调用
        verify(userDiscountBannerReportDAO, times(1)).appReportUserDiscountBanner(id);
    }

    @Test(expected = ParamException.class)
    @DisplayName("更新报告 - 无效 ID")
    public void test_updateReport_InvalidId() {
        // 模拟 DAO 层行为
        Long id = 2L;
        when(userDiscountBannerReportDAO.queryById(id)).thenReturn(null);

        // 调用方法并验证异常抛出
        userDiscountBannerReportService.updateReport(id);
    }
}