package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.questionback.QuestionBackCommitRequest;
import com.addx.iotcamera.bean.app.questionback.QuestionBackOptionsRequest;
import com.addx.iotcamera.bean.db.DeviceLibraryViewDO;
import com.addx.iotcamera.bean.db.LibraryStatusTb;
import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.domain.questionback.QuestionBackDO;
import com.addx.iotcamera.bean.domain.questionback.QuestionBackData;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.init.CopyWriteInit;
import com.addx.iotcamera.bean.param.OciParams;
import com.addx.iotcamera.bean.video.StoreBucket;
import com.addx.iotcamera.config.CosConfig;
import com.addx.iotcamera.config.GcsOptions;
import com.addx.iotcamera.config.OciConfig;
import com.addx.iotcamera.config.S3;
import com.addx.iotcamera.dao.library.QuestionBackDAO;
import com.addx.iotcamera.enums.VideoQuestion;
import com.addx.iotcamera.enums.VideoTagQuestionEnums;
import com.addx.iotcamera.enums.VideoType;
import com.addx.iotcamera.helper.GoogleStorageService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.InputStream;
import java.net.URL;
import java.util.*;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class QuestionBackServiceTest {

    @InjectMocks
    private QuestionBackService questionBackService;

    @Mock
    private VideoStoreService videoStoreService;
    @Mock
    private S3Service s3Service;
    @Mock
    private GoogleStorageService googleStorageService;
    @Mock
    private LibraryService libraryService;
    @Mock
    private LibraryStatusService libraryStatusService;
    @Mock
    private DeviceModelEventService deviceModelEventService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private VideoService videoService;
    @Mock
    private S3 s3;
    @Mock
    private GcsOptions gcsOptions;
    @Mock
    private QuestionBackDAO questionBackDAO;
    @Mock
    private S3ObjectInputStream s3ObjectInputStream;
    @Mock
    private OciConfig ociConfig;
    @Mock
    private CosConfig cosConfig;
    @Mock
    private OciService ociService;
    @Mock
    private CosService cosService;

    @Before
    @SneakyThrows
    public void init() {
        String resourcePath = "gitconfig/copywrite/iot.csv";
        CopyWrite config = new CopyWrite();
        CopyWriteInit.readStreamCsv(resourcePath, config);

        List<String> notTextTitleKeys = VideoQuestion.initQuestionBackText(config);

        when(s3Service.getUrl(anyString(), anyString())).thenAnswer(it -> {
            return String.format("https://%s.s3.amazon.com/%s", it.getArgument(0), it.getArgument(1));
        });
        when(cosConfig.getBucket()).thenReturn("backup-bucket");
        when(cosService.getObjectUrl(anyString(), anyString())).thenAnswer(it -> {
            final String desBucket = it.getArgument(0);
            final String desKey = it.getArgument(1);
            return new URL("https://" + desBucket + ".cos.ap-beijing.myqcloud.com/" + desKey);
        });
        when(ociConfig.getBucket()).thenReturn("backup-bucket");
        when(ociService.getObjectUrl(any(StoreBucket.class), anyString())).thenAnswer(it -> {
            final StoreBucket desBucket = it.getArgument(0);
            final String desKey = it.getArgument(1);
            return new URL("https://objectstorage.us-ashburn-1.oraclecloud.com/n/idrw0j0fjn4v/b/" + desBucket.getBucket() + "/o/" + desKey);
        });
    }

    private List<Integer> optionCodes = Arrays.asList(
            VideoTagQuestionEnums.PERSON_RECOGNITION_ERROR.getCode(),
            VideoTagQuestionEnums.PET_NOT_IDENTIFY.getCode(),
            VideoTagQuestionEnums.VEHICLE_RECOGNITION_ERROR.getCode(),
            VideoTagQuestionEnums.PACKAGE_NOT_IDENTIFY.getCode(),
            VideoTagQuestionEnums.BIRD_RECOGNITION_ERROR.getCode()
    );

    private List<Integer> codes = Arrays.asList(
            VideoTagQuestionEnums.PERSON_RECOGNITION_ERROR.getCode(),
            VideoTagQuestionEnums.BIRD_RECOGNITION_ERROR.getCode()
    );

    @Test
    public void test_getQuestionBackOptions() {
        DeviceLibraryViewDO library = new DeviceLibraryViewDO();
        library.setSerialNumber("sn_"+OpenApiUtil.shortUUID());
        library.setTraceId(OpenApiUtil.shortUUID());
        library.setId(353409530);
        LibraryStatusTb libraryStatus = new LibraryStatusTb();
        libraryStatus.setLibraryId(library.getId());
        libraryStatus.setTraceId(library.getTraceId());

        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("model1");

        QuestionBackOptionsRequest req = new QuestionBackOptionsRequest();
        {
            Result<QuestionBackData> result = questionBackService.getQuestionBackOptions(req);
            Assert.assertEquals((Integer) ResultCollection.INVALID_PARAMS.getCode(), result.getResult());
        }
        req.setTraceId(library.getTraceId());
        {
            Result<QuestionBackData> result = questionBackService.getQuestionBackOptions(req);
            Assert.assertEquals((Integer) ResultCollection.INVALID_PARAMS.getCode(), result.getResult());
        }
        req.setUserId(43593850);
        {
            Result<QuestionBackData> result = questionBackService.getQuestionBackOptions(req);
            Assert.assertEquals((Integer) ResultCollection.NO_LIBRARY_ACCESS.getCode(), result.getResult());
        }
        when(libraryService.queryUserLibraryViewByIdOrTraceId(any(), any(), any())).thenReturn(library);
        {
            Result<QuestionBackData> result = questionBackService.getQuestionBackOptions(req);
            Assert.assertEquals((Integer) ResultCollection.NO_LIBRARY_ACCESS.getCode(), result.getResult());
        }
        {
            req.setTraceId(null);
            req.setLibraryId(library.getId());
            Result<QuestionBackData> result = questionBackService.getQuestionBackOptions(req);
            Assert.assertEquals((Integer) ResultCollection.NO_LIBRARY_ACCESS.getCode(), result.getResult());
        }
        req.setTraceId(library.getTraceId());
        req.setLibraryId(null);
        when(libraryStatusService.selectLibraryStatusByTraceIdAndUserId(any(), any())).thenReturn(libraryStatus);
        when(deviceModelEventService.queryRowDeviceModelEvent(any())).thenReturn(new HashSet<>(Arrays.asList("bird", "person", "pet", "vehicle", "package")));
        {
            library.setTags("dfjdi");
            Result<QuestionBackData> result = questionBackService.getQuestionBackOptions(req);
            Assert.assertEquals(Result.failureFlag, result.getResult());
        }
        library.setTags("person,bird,vehicle");
        {
            when(questionBackDAO.queryQuestionBackByTraceIdAndUserId(any(), any(), any())).thenReturn(Arrays.asList());
            Result<QuestionBackData> result = questionBackService.getQuestionBackOptions(req);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        QuestionBackDO back = new QuestionBackDO();
        back.setLibraryId(library.getId());
        back.setTraceId(library.getTraceId());
        back.setOptionCodeSet(new HashSet<>(optionCodes));
        back.setCodes(JSON.toJSONString(codes));
        back.setIsBackup(true);
        {
            when(questionBackDAO.queryQuestionBackByTraceIdAndUserId(any(), any(), any())).thenReturn(Arrays.asList(back));
            Result<QuestionBackData> result = questionBackService.getQuestionBackOptions(req);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
    }

    @Test
    public void test_commitQuestionBack() {
        DeviceLibraryViewDO library = new DeviceLibraryViewDO();
        library.setSerialNumber("sn_"+OpenApiUtil.shortUUID());
        library.setTraceId(OpenApiUtil.shortUUID());
        library.setId(353409530);
        library.setTags("person,bird");
        LibraryStatusTb libraryStatus = new LibraryStatusTb();
        libraryStatus.setLibraryId(library.getId());
        libraryStatus.setTraceId(library.getTraceId());

        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("model1");

        QuestionBackCommitRequest req = new QuestionBackCommitRequest();
        {
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals((Integer) ResultCollection.INVALID_PARAMS.getCode(), result.getResult());
        }
        req.setTraceId(library.getTraceId());
        {
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals((Integer) ResultCollection.INVALID_PARAMS.getCode(), result.getResult());
        }
        req.setUserId(43593850);
        {
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals((Integer) ResultCollection.NO_LIBRARY_ACCESS.getCode(), result.getResult());
        }
        req.setCodes(new LinkedHashSet<>(codes));
        {
            req.setRemark("01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789");
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals((Integer) ResultCollection.INVALID_PARAMS.getCode(), result.getResult());
        }
        req.setRemark("0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789");
        {
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals((Integer) ResultCollection.NO_LIBRARY_ACCESS.getCode(), result.getResult());
        }
        when(libraryService.queryUserLibraryViewByIdOrTraceId(any(), any(), any())).thenReturn(library);
        {
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals(Result.failureFlag, result.getResult());
        }
        when(deviceModelEventService.queryRowDeviceModelEvent(any())).thenReturn(new HashSet<>(Arrays.asList("bird", "person", "pet", "vehicle", "package")));
        {
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals((Integer) ResultCollection.NO_LIBRARY_ACCESS.getCode(), result.getResult());
        }
        {
            req.setTraceId(null);
            req.setLibraryId(library.getId());
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals((Integer) ResultCollection.NO_LIBRARY_ACCESS.getCode(), result.getResult());
        }
        req.setTraceId(library.getTraceId());
        req.setLibraryId(null);
        when(libraryStatusService.selectLibraryStatusByTraceIdAndUserId(any(), any())).thenReturn(libraryStatus);
        {
            library.setTags("dfjdi");
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals(Result.failureFlag, result.getResult());
        }
        library.setTags("person,bird,vehicle");
        {
            when(questionBackDAO.queryQuestionBackByTraceIdAndUserId(any(), any(), any())).thenReturn(Arrays.asList());
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        QuestionBackDO back = new QuestionBackDO();
        back.setLibraryId(library.getId());
        back.setTraceId(library.getTraceId());
        back.setOptionCodeSet(new HashSet<>(optionCodes));
        back.setCodes(JSON.toJSONString(codes));
        back.setIsBackup(true);
        {
            when(questionBackDAO.queryQuestionBackByTraceIdAndUserId(any(), any(), any())).thenReturn(Arrays.asList(back));
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        back.setIsBackup(false);
        {
            when(questionBackDAO.queryQuestionBackByTraceIdAndUserId(any(), any(), any())).thenReturn(Arrays.asList(back));
            Result<QuestionBackData> result = questionBackService.commitQuestionBack(req);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }

    }

//    @Test
    @SneakyThrows
    public void test_backupVideo() {
        DeviceLibraryViewDO view = new DeviceLibraryViewDO();
        view.setSerialNumber("601c2322163006873b38b818334ff97f");
        view.setTraceId("06281659325061xFY7HNom1cTFFYC");

        when(s3.getBucket()).thenReturn("bucket0001");
        when(gcsOptions.getBucket()).thenReturn("bucket0002");
        final String expectKey = "video_backup/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/";
        when(s3Service.putObject(anyString(), anyString(), anyString())).thenAnswer(it -> {
            Assert.assertTrue(((String) it.getArgument(1)).startsWith(expectKey));
            return 1;
        });
        when(s3Service.putObject(anyString(), anyString(), any(Long.class), any(InputStream.class))).thenAnswer(it -> {
            Assert.assertTrue(((String) it.getArgument(1)).startsWith(expectKey));
            return 1;
        });
        when(s3Service.copyObject(anyString(), anyString(), anyString(), anyString())).thenAnswer(it -> {
            Assert.assertTrue(((String) it.getArgument(3)).startsWith(expectKey));
            return 1;
        });
        {
            view.setType(10);
            Result result = questionBackService.backupVideo(1, view);
            Assert.assertEquals((Integer) 1, result.getResult());
        }
        view.setType(VideoType.DEVICE_SLICE.getCode());
        {
            when(videoStoreService.querySliceByAdminUserIdAndTraceId(1, view.getTraceId())).thenReturn(Arrays.asList());
            Result<JSONObject> result = questionBackService.backupVideo(1, view);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(0, result.getData().getIntValue("copyNum"));
        }
        view.setImageUrl("https://addx-staging-vip-pro.s3.cn-north-1.amazonaws.com.cn/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/image.jpg");
        {
            when(videoStoreService.querySliceByAdminUserIdAndTraceId(1, view.getTraceId())).thenReturn(Arrays.asList());
            Result<JSONObject> result = questionBackService.backupVideo(1, view);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(1, result.getData().getIntValue("copyNum"));
        }
        view.setType(VideoType.DEVICE_SLICE.getCode());
        { // s3
            List<VideoSliceDO> sliceList = new LinkedList<>();
            for (int i = 0; i < 2; i++) {
                VideoSliceDO slice = new VideoSliceDO();
                slice.setVideoUrl("https://addx-staging-vip-pro.s3.cn-north-1.amazonaws.com.cn/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/slice_3133_" + i + "_0.ts");
                sliceList.add(slice);
            }
            {
                VideoSliceDO slice = new VideoSliceDO();
                slice.setVideoUrl("https://bucket1.google.com.cn/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/slice_3133_1_0.ts");
                sliceList.add(slice);
            }
            when(videoStoreService.querySliceByAdminUserIdAndTraceId(1, view.getTraceId())).thenReturn(sliceList);
            {
                Result<JSONObject> result = questionBackService.backupVideo(1, view);
                Assert.assertEquals(Result.successFlag, result.getResult());
                Assert.assertEquals(3, result.getData().getIntValue("copyNum"));
            }
        }
        { // cos
            List<VideoSliceDO> sliceList = new LinkedList<>();
            for (int i = 0; i < 2; i++) {
                VideoSliceDO slice = new VideoSliceDO();
                slice.setVideoUrl("https://staging-cn-1302606863.cos.ap-beijing.myqcloud.com/video_backup/device_video_slice/f98af5080147855b2fa336b3dc05e28b/09501684906953uOyxzmoXaLyjJXk/slice_1999_" + i + "_0.ts");
                sliceList.add(slice);
            }
            {
                VideoSliceDO slice = new VideoSliceDO();
                slice.setVideoUrl("https://staging-cn-1302606863.cos.ap-beijing.myqcloud.com/video_backup/device_video_slice/f98af5080147855b2fa336b3dc05e28b/09501684906953uOyxzmoXaLyjJXk/slice_1999_2_1.ts");
                sliceList.add(slice);
            }
            when(videoStoreService.querySliceByAdminUserIdAndTraceId(1, view.getTraceId())).thenReturn(sliceList);
            {
                Result<JSONObject> result = questionBackService.backupVideo(1, view);
                Assert.assertEquals(Result.successFlag, result.getResult());
                Assert.assertEquals(4, result.getData().getIntValue("copyNum"));
            }
        }
        { // oci
            List<VideoSliceDO> sliceList = new LinkedList<>();
            for (int i = 0; i < 2; i++) {
                VideoSliceDO slice = new VideoSliceDO();
                slice.setVideoUrl("https://objectstorage.us-ashburn-1.oraclecloud.com/n/idrw0j0fjn4v/b/staging-us-vip-60d/o/device_video_slice/f98af5080147855b2fa336b3dc05e28b/03901684912510pnfDQTtwTNm3vFS/slice_1864_" + i + "_0.ts");
                sliceList.add(slice);
            }
            {
                VideoSliceDO slice = new VideoSliceDO();
                slice.setVideoUrl("https://objectstorage.us-ashburn-1.oraclecloud.com/n/idrw0j0fjn4v/b/staging-us-vip-60d/o/device_video_slice/f98af5080147855b2fa336b3dc05e28b/03901684912510pnfDQTtwTNm3vFS/slice_1864_2_1.ts");
                sliceList.add(slice);
            }
            when(videoStoreService.querySliceByAdminUserIdAndTraceId(1, view.getTraceId())).thenReturn(sliceList);
            when(ociConfig.getParams()).thenReturn(new OciParams().setDefaultRegion("us-ashburn-1"));
            {
                Result<JSONObject> result = questionBackService.backupVideo(1, view);
                Assert.assertEquals(Result.successFlag, result.getResult());
                Assert.assertEquals(4, result.getData().getIntValue("copyNum"));
            }
        }
        view.setType(VideoType.NORMAL.getCode());
        {
            view.setVideoUrl("https://storage.googleapis.com/b/device_video_slice/o/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/video.mp4");
            Result<JSONObject> result = questionBackService.backupVideo(1, view);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(2, result.getData().getIntValue("copyNum"));
        }
        {
            view.setVideoUrl("https://addx-staging-vip-pro.s3.cn-north-1.amazonaws.com.cn/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/video.mp4");
            Result<JSONObject> result = questionBackService.backupVideo(1, view);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(2, result.getData().getIntValue("copyNum"));
        }
        {
            view.setVideoUrl("https://addx-staging-vip-pro.s3-accelerate.cn-north-1.amazonaws.com.cn/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/video.mp4");
            Result<JSONObject> result = questionBackService.backupVideo(1, view);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(2, result.getData().getIntValue("copyNum"));
        }
    }

    @Test
    public void test_queryQuestionBackVideos() {
        Integer adminId = 394583;
        {
            List<JSONObject> list = questionBackService.queryQuestionBackVideos(null);
            Assert.assertEquals(0, list.size());
        }
        {
            when(questionBackDAO.queryQuestionBackByLibraryIdAndUserId(any(), any(), any())).thenReturn(Arrays.asList());
            List<JSONObject> list = questionBackService.queryQuestionBackVideos(adminId);
            Assert.assertEquals(0, list.size());
        }
        QuestionBackDO back = new QuestionBackDO();
        back.setLibraryId(3405834);
        back.setSerialNumber("sn350jreoiurweo");
        back.setTraceId(OpenApiUtil.shortUUID());
        back.setOptionCodeSet(new HashSet<>(optionCodes));
        back.setCodes(JSON.toJSONString(codes));
        back.setIsBackup(true);
        when(questionBackDAO.queryQuestionBackByLibraryIdAndUserId(any(), any(), any())).thenReturn(Arrays.asList(back));
        {
            back.setSerialNumber(null);
            when(questionBackDAO.queryBoundDeviceSn(back.getUserId())).thenReturn(Arrays.asList(""));
            when(libraryService.queryUserLibraryViewByIdOrTraceId(any(), any(), any())).thenReturn(null);
            List<JSONObject> list = questionBackService.queryQuestionBackVideos(adminId);
            Assert.assertEquals(1, list.size());
        }
        back.setSerialNumber("sn350jreoiurweo");
        {
            when(libraryService.queryUserLibraryViewByIdOrTraceId(any(), any(), any())).thenReturn(null);
            List<JSONObject> list = questionBackService.queryQuestionBackVideos(adminId);
            Assert.assertEquals(1, list.size());
        }
        DeviceLibraryViewDO library = new DeviceLibraryViewDO();
        library.setSerialNumber("sn_" + OpenApiUtil.shortUUID());
        library.setTraceId(OpenApiUtil.shortUUID());
        library.setId(353409530);
        library.setType(VideoType.DEVICE_SLICE.getCode());
        LibraryStatusTb libraryStatus = new LibraryStatusTb();
        libraryStatus.setLibraryId(library.getId());
        libraryStatus.setTraceId(library.getTraceId());
        when(libraryService.queryUserLibraryViewByIdOrTraceId(any(), any(), any())).thenReturn(library);
        {
            back.setIsBackup(false);
            List<JSONObject> list = questionBackService.queryQuestionBackVideos(adminId);
            Assert.assertEquals(1, list.size());
        }
        {
            back.setIsBackup(true);
            List<JSONObject> list = questionBackService.queryQuestionBackVideos(adminId);
            Assert.assertEquals(1, list.size());
        }
    }

    @Test
    public void test_getBackupLibraryView() {
        final Integer adminId = 34535;
        final String traceId = OpenApiUtil.shortUUID();
        final String sn = OpenApiUtil.shortUUID();
        DeviceLibraryViewDO view = new DeviceLibraryViewDO() {{
            setTraceId(traceId);
            setSerialNumber(sn);
            setType(VideoType.DEVICE_SLICE.getCode());
        }};
        QuestionBackDO back = QuestionBackDO.builder().traceId(traceId).serialNumber(sn).build();
        {
            back.setIsBackup(false);
            when(libraryService.queryUserLibraryViewByIdOrTraceId(any(), any(), any())).thenReturn(null);
            DeviceLibraryViewDO library = questionBackService.getBackupLibraryView(back, adminId);
            Assert.assertNull(library);
        }
        {
            back.setIsBackup(false);
            when(libraryService.queryUserLibraryViewByIdOrTraceId(any(), any(), any())).thenReturn(view);
            DeviceLibraryViewDO library = questionBackService.getBackupLibraryView(back, adminId);
            Assert.assertNotNull(library);
        }
        {
            back.setIsBackup(true);
            back.setTraceId(null);
            DeviceLibraryViewDO library = questionBackService.getBackupLibraryView(back, adminId);
            Assert.assertNull(library);
        }
        {
            back.setIsBackup(true);
            back.setTraceId(traceId);
            DeviceLibraryViewDO library = questionBackService.getBackupLibraryView(back, adminId);
            Assert.assertNull(library);
        }
        {
            back.setSerialNumber(null);
            back.setIsBackup(true);
            back.setTraceId(traceId);
            DeviceLibraryViewDO library = questionBackService.getBackupLibraryView(back, adminId);
            Assert.assertNull(library);
        }
    }

}
