package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.device.DeviceMessageNotification;
import com.addx.iotcamera.config.device.MessageNotificationConfig;
import com.addx.iotcamera.config.device.ModelAiEventConfig;
import com.addx.iotcamera.dao.MessageNotificationSettingsDao;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.extension.ai.enums.utils.AiObjectEnumUtil;
import org.addx.iot.domain.extension.entity.DeviceAiSwitch;
import org.addx.iot.domain.extension.entity.EventObjectSwitch;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class MessageNotificationSettingsServiceTest {

    @InjectMocks
    MessageNotificationSettingsService messageNotificationSettingsService;
    @InjectMocks
    NotificationService notificationService;
    @Mock
    MessageNotificationSettingsService messageNotificationSettingsService2;
    @Mock
    MessageNotificationConfig messageNotificationConfig;
    @Mock
    ModelAiEventConfig modelAiEventConfig;
    @Mock
    MessageNotificationSettingsDao messageNotificationSettingsDao;
    @Mock
    DeviceManualService deviceManualService;
    @Mock
    DeviceAuthService deviceAuthService;
    @Mock
    UserVipService userVipService;
    @Mock
    private DeviceAiSettingsService deviceAiSettingsService;
    @Mock
    private VipService paasVipService;
    @Mock
    private DeviceModelEventService deviceModelEventService;
    @Mock
    private AiAssistService aiAssistService;

    @Before
    public void init() throws IOException {
        MessageNotificationConfig notification = TestHelper.loadPropertySource(MessageNotificationConfig.class);
//        notification.init();
        ModelAiEventConfig modelAiEventConfig = TestHelper.loadPropertySource(ModelAiEventConfig.class);
        when(this.messageNotificationConfig.getMessage()).thenReturn(notification.getMessage());
        when(this.messageNotificationConfig.isSubEvent(anyString(), anyString())).thenAnswer(AdditionalAnswers.delegatesTo(notification));
//        when(messageNotificationConfig.getMessageRuleMap()).thenReturn(notification.getMessageRuleMap());

        when(this.modelAiEventConfig.getPackageEvents()).thenAnswer(AdditionalAnswers.delegatesTo(modelAiEventConfig));
        //when(this.modelAiEventConfig.getAiEventsByModelNo(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(modelAiEventConfig));
//        when(messageNotificationConfig.getChildEvent2ParentEvent()).thenAnswer(AdditionalAnswers.delegatesTo(notification));
        when(deviceAiSettingsService.initDeviceAiSettings(any(), anyString())).thenReturn(null);
        log.info("");
    }

    //    /updateMessageNotification/v1
//  /queryMessageNotification/v1
    @Test
    public void test_queryMessageNotification() throws IOException {
//        String sn = "sn_test";
//        Integer userId = 100;
////        String modelNo = "CB1";
//        String modelNo = "CG4";
//
//        MessageNotificationSetting setting = new MessageNotificationSetting();
//        setting.setEventObjects("person,pet,package");
//        setting.setPackageEventType("package_drop_off,package_pick_up");
//        setting.setSerialNumber(sn);
//        when(messageNotificationSettingsDao.getMessageNotificationSettingEvent(anyInt(), any())).thenReturn(setting);
//        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(modelNo);
//
////        List<DeviceMessageNotification> notificationList = messageNotificationSettingsService.queryMessageNotificationSettingsListV1(sn, userId);
//        log.info("");
//
////        when(messageNotificationSettingsDao.updateMessageNotificationSettings(any())).thenReturn(1);
//        when(messageNotificationSettingsService2.updateMessageNotificationSettings(any())).thenReturn(1);
//        when(deviceAuthService.checkActivatedAccess(userId, sn)).thenReturn(SUCCESS.getCode());
//        when(userVipService.isVipUser(anyInt())).thenReturn(true);
//        when(messageNotificationSettingsService2.filterEventTypeMapByMessageRule(any(), anyMap()))
//                .thenAnswer(AdditionalAnswers.delegatesTo(messageNotificationSettingsService));
//        ImmutableMap<String, List<String>> eventObjectType = ImmutableMap.<String, List<String>>builder()
//                .put("person", new LinkedList<>()).put("package", Arrays.asList("package_drop_off", "package_pick_up"))
//                .build();
//        MessageNotificationSettingRequest request = new MessageNotificationSettingRequest();
//        request.setSerialNumber(sn);
//        request.setEventObjects(new LinkedList<>(eventObjectType.keySet()));
//        request.setEventObjectType(new LinkedHashMap<>(eventObjectType));
////        Map<String, List<String>> map = messageNotificationSettingsService.filterEventTypeMapByMessageRule(request.getSerialNumber(), request.getEventObjectType());
//        Result result = notificationService.updateMessageNotificationSettingsV1(userId, request);
//        Assert.assertEquals(new Integer(0), result.getResult());
//        log.info("");
    }

    private Set<String> enableEventObjects = ImmutableSet.of("person", "pet", "vehicle", "package");

//    @Test
//    public void test_initDeviceMessageNotificationListV2() {
//        String sn_cg = "sn_cg";
//        String sn_cb = "sn_cb";
//        String sn_x = "sn_x";
//        when(deviceManualService.getModelNoBySerialNumber(sn_cg)).thenReturn("CG1");
//        when(deviceManualService.getModelNoBySerialNumber(sn_cb)).thenReturn("CB0");
//        when(deviceManualService.getModelNoBySerialNumber(sn_x)).thenReturn("CK0");
//
//        MessageNotificationSetting setting = new MessageNotificationSetting();
//        setting.setEventObjects("package,person");
//        setting.setPackageEventType("package_drop_off,package_pick_up");
//        int messageNum = messageNotificationConfig.getMessage().size();
//        List<DeviceMessageNotification> list1 = messageNotificationSettingsService.initDeviceMessageNotificationListV2(sn_cb, setting, enableEventObjects);
//        log.info("list1={}", list1);
//        Assert.assertEquals(messageNum - 2, list1.size());
//        for (DeviceMessageNotification item : list1) {
//            Assert.assertFalse("只有cg和g系列有包裹", item.getName().equals(EventObject.PACKAGE.getObjectName()));
//            Assert.assertFalse("cb系列无车辆", item.getName().equals(EventObject.VEHICLE.getObjectName()));
//            Assert.assertEquals(item.getChoice(), setting.getEventObjects().contains(item.getName()));
//        }
//        List<DeviceMessageNotification> list2 = messageNotificationSettingsService.initDeviceMessageNotificationListV2(sn_cg, setting, enableEventObjects);
//        log.info("list2={}", list2);
//        Assert.assertEquals(messageNum, list2.size());
//        for (DeviceMessageNotification item : list2) {
//            Assert.assertEquals(item.getChoice(), setting.getEventObjects().contains(item.getName()));
//        }
//        List<DeviceMessageNotification> list3 = messageNotificationSettingsService.initDeviceMessageNotificationListV2(sn_x, setting, enableEventObjects);
//        log.info("list3={}", list3);
//        Assert.assertEquals(messageNum - 1, list3.size());
//        for (DeviceMessageNotification item : list3) {
//            Assert.assertFalse("只有cg和g系列有包裹", item.getName().equals(EventObject.PACKAGE.getObjectName()));
//            Assert.assertEquals(item.getChoice(), setting.getEventObjects().contains(item.getName()));
//        }
//    }

    @Test
    public void test_getEventTypesByMessageRule() {
//        String sn_cg = "sn_cg";
//        String sn_cb = "sn_cb";
//        String sn_x = "sn_x";
//        when(deviceManualService.getModelNoBySerialNumber(sn_cg)).thenReturn("CG1");
//        when(deviceManualService.getModelNoBySerialNumber(sn_cb)).thenReturn("CB0");
//        when(deviceManualService.getModelNoBySerialNumber(sn_x)).thenReturn("CK0");
//
//        MessageNotificationSetting setting0 = new MessageNotificationSetting();
//        setting0.setUserId(89989_0);
//        setting0.setPackageFirstUse(0);
//        when(messageNotificationSettingsDao.getMessageNotificationSettingEvent(eq(setting0.getUserId()), anyString())).thenReturn(setting0);
//
//        List<String> events01 = messageNotificationSettingsService.getEventTypesByMessageRule(sn_cg, setting0.getUserId());
//        Assert.assertEquals(Arrays.asList("person", "pet", "vehicle", "package"), events01);
//        List<String> events02 = messageNotificationSettingsService.getEventTypesByMessageRule(sn_cb, setting0.getUserId());
//        Assert.assertEquals(Arrays.asList("person", "pet"), events02);
//        List<String> events03 = messageNotificationSettingsService.getEventTypesByMessageRule(sn_x, setting0.getUserId());
//        Assert.assertEquals(Arrays.asList("person", "pet", "vehicle"), events03);
//
//        MessageNotificationSetting setting1 = new MessageNotificationSetting();
//        setting1.setUserId(89989_1);
//        setting1.setPackageFirstUse(1);
//        when(messageNotificationSettingsDao.getMessageNotificationSettingEvent(eq(setting1.getUserId()), anyString())).thenReturn(setting1);
//
//        List<String> events11 = messageNotificationSettingsService.getEventTypesByMessageRule(sn_cg, setting1.getUserId());
//        Assert.assertEquals(Arrays.asList("person", "pet", "vehicle"), events11);
//        List<String> events12 = messageNotificationSettingsService.getEventTypesByMessageRule(sn_cb, setting1.getUserId());
//        Assert.assertEquals(Arrays.asList("person", "pet"), events12);
//        List<String> events13 = messageNotificationSettingsService.getEventTypesByMessageRule(sn_x, setting1.getUserId());
//        Assert.assertEquals(Arrays.asList("person", "pet", "vehicle"), events13);
    }

    @Test
    public void test_queryMessageNotificationSettingsListV1() {
        String serialNumber = "sn_485385839";
        String modelNo = "model_485385839";
        Integer userId = 485385839;
        when(messageNotificationSettingsDao.getMessageNotificationSettingEvent(userId, serialNumber))
                .thenReturn(MessageNotificationSetting.builder()
                        .eventObjects("person,pet,vehicle")
                        .packageEventType("vehicle_enter,vehicle_out")
                        .enableOther(1).build());
        when(deviceAiSettingsService.queryEnableEventObjects(userId, serialNumber))
                .thenReturn(AiObjectEnumUtil.eventObjectsOfBits(31));
//        when(messageNotificationConfig.getMessage()).thenReturn()
        when(deviceManualService.getModelNoBySerialNumber(serialNumber)).thenReturn(modelNo);
        Set<String> eventObjects = new HashSet<>(Arrays.asList("person", "pet", "vehicle", "package", "bird"));
        when(deviceModelEventService.queryDeviceModelEvent(modelNo)).thenReturn(eventObjects);

        when(aiAssistService.queryEventObjectSwitch(any(), any())).thenReturn(new Result<DeviceAiSwitch>(new DeviceAiSwitch() {{
            setList(Arrays.asList(new EventObjectSwitch() {{
                setEventObject("person");
                setCanModify(true);
            }}, new EventObjectSwitch() {{
                setEventObject("pet");
                setCanModify(true);
            }}, new EventObjectSwitch() {{
                setEventObject("vehicle");
                setCanModify(true);
            }}));
        }}));
        

        List<DeviceMessageNotification> result = messageNotificationSettingsService.queryMessageNotificationSettingsListV1(serialNumber, userId);
        Assert.assertTrue(result.stream().anyMatch(it -> eventObjects.contains(it.getName())));
        Assert.assertTrue(result.stream().anyMatch(it -> "other".equals(it.getName()) && it.getChoice()));
    }

    @Test
    public void test_initDeviceMessageNotificationListV2() {
        String sn = OpenApiUtil.shortUUID();
        String modelNo = OpenApiUtil.shortUUID();
        when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(modelNo);
        when(deviceModelEventService.queryDeviceModelEvent(modelNo)).thenReturn(new HashSet<>(Arrays.asList("person", "pet", "vehicle", "package")));

        when(aiAssistService.queryEventObjectSwitch(any(), any())).thenReturn(new Result(new DeviceAiSwitch().setSerialNumber(sn).setList(Arrays.asList(new EventObjectSwitch(){{
            setEventObject("person");
            setCanModify(true);
        }}, new EventObjectSwitch(){{
            setEventObject("pet");
            setCanModify(true);
        }}, new EventObjectSwitch(){{
            setEventObject("vehicle");
            setCanModify(true);
        }}, new EventObjectSwitch(){{
            setEventObject("package");
            setCanModify(true);
        }})).setDeviceName("anyDevice")));
        
        MessageNotificationSetting setting = new MessageNotificationSetting();
        setting.setEventObjects("person,vehicle,package");
        setting.setPackageEventType("vehicle_enter,vehicle_out");
        Set<String> enableEventObjectNames = new HashSet<>(Arrays.asList("person", "pet", "package"));

        when(aiAssistService.queryEventObjectSwitch(any(), any())).thenReturn(new Result(new DeviceAiSwitch().setSerialNumber(sn).setList(Arrays.asList(new EventObjectSwitch(){{
            setEventObject("person");
            setCanModify(true);
        }}, new EventObjectSwitch(){{
            setEventObject("pet");
            setCanModify(true);
        }}, new EventObjectSwitch(){{
            setEventObject("vehicle");
            setCanModify(true);
        }}, new EventObjectSwitch(){{
            setEventObject("package");
            setCanModify(true);
        }})).setDeviceName("anyDevice")));

        List<DeviceMessageNotification> result = messageNotificationSettingsService.initDeviceMessageNotificationListV2(0, sn, setting, enableEventObjectNames);
        Assert.assertNotNull(result);
        Assert.assertEquals(4, result.size());
        Map<String, DeviceMessageNotification> map = result.stream().collect(Collectors.toMap(it -> it.getName(), it -> it));
        LinkedList<DeviceMessageNotification> vehicleSubEvent = new LinkedList<>(Arrays.asList(
                new DeviceMessageNotification("vehicle_enter", true, new LinkedList<>(), true),
                new DeviceMessageNotification("vehicle_held_up", false, new LinkedList<>(), true),
                new DeviceMessageNotification("vehicle_out", true, new LinkedList<>(), true)
        ));
        LinkedList<DeviceMessageNotification> packageSubEvent = new LinkedList<>(Arrays.asList(
                new DeviceMessageNotification("package_drop_off", false, new LinkedList<>(), true),
                new DeviceMessageNotification("package_exist", false, new LinkedList<>(), true),
                new DeviceMessageNotification("package_pick_up", false, new LinkedList<>(), true)
        ));
        Assert.assertEquals(new DeviceMessageNotification("person", true, new LinkedList<>(), true), map.get("person"));
        Assert.assertEquals(new DeviceMessageNotification("pet", false, new LinkedList<>(), true), map.get("pet"));
        Assert.assertEquals(new DeviceMessageNotification("vehicle", true, vehicleSubEvent, false), map.get("vehicle"));
        Assert.assertEquals(new DeviceMessageNotification("package", false, packageSubEvent, true), map.get("package"));
    }
}
