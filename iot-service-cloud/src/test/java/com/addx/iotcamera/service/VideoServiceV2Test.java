package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.VideoReportEvent;
import com.addx.iotcamera.bean.cache.DevicePirNotifyFactor;
import com.addx.iotcamera.bean.db.LibraryTb;
import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.openapi.AwsCredentials;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.bean.video.*;
import com.addx.iotcamera.config.GcsOptions;
import com.addx.iotcamera.config.S3;
import com.addx.iotcamera.config.S3Config;
import com.addx.iotcamera.config.VideoSliceConfig;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.enums.GcsCredentials;
import com.addx.iotcamera.enums.UserType;
import com.addx.iotcamera.helper.GoogleStorageService;
import com.addx.iotcamera.helper.aws.AwsHelper;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.addx.iotcamera.service.video.StorageAllocateService;
import com.addx.iotcamera.service.video.StorageParamService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.securitytoken.AWSSecurityTokenService;
import com.amazonaws.services.securitytoken.model.AssumeRoleResult;
import com.amazonaws.services.securitytoken.model.Credentials;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.net.URL;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.VideoConstants.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoServiceV2Test {

    @InjectMocks
    private VideoService videoService;
    @Mock
    private StorageParamService storageParamService;
    @Mock
    private StorageAllocateService storageAllocateService;
    @Mock
    private VideoStoreService videoStoreService;
    @Mock
    private DeviceService deviceService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private UserService userService;
    @Mock
    private AIService aiService;
    @Mock
    private AWSSecurityTokenService stsClient;
    @Mock
    private S3Config s3Config;
    @Mock
    private S3 s3;
    @Mock
    private VideoSliceConfig videoSliceConfig;
    @Mock
    private AmazonS3 s3Client;
    @Mock
    private OpenApiConfigService openApiConfigService;
    @Mock
    private AwsHelper awsHelper;
    @Mock
    private OpenApiWebhookService openApiWebhookService;
    @Mock
    private RedisService redisService;
    @Mock
    private LibraryUpdateReceiveAllService libraryUpdateReceiveAllService;
    @Mock
    private LibraryStatusService libraryStatusService;
    @Mock
    private ReportLogService reportLogService;
    @Mock
    private PushService pushService;
    @Mock
    private LibraryService libraryService;
    @Mock
    private S3Service s3Service;
    @Mock
    private UserVipService userVipService;
    @Mock
    private VipService paasVipService;
    @Mock
    private GcsOptions gcsOptions;
    @Mock
    private GoogleStorageService googleStorageService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private MessageNotificationSettingsService messageNotificationSettingsService;
    @Mock
    private NotificationService notificationService;
    @Mock
    private Device4GService device4GService;
    @Mock
    private ForkJoinPool pool;


    @Before
    public void init() {
        final ForkJoinPool forkJoinPool = new ForkJoinPool(1);
        when(pool.submit((Callable) any())).thenAnswer(AdditionalAnswers.delegatesTo(forkJoinPool));
        when(s3.getBucket()).thenReturn("unittest-bucket");
        when(s3.getClientRegion()).thenReturn("unittest-region");
        when(openApiConfigService.getAwsHelper(any())).thenReturn(awsHelper);
        when(s3Service.preSignUrl(any())).thenAnswer(it -> it.getArgument(0) + "?token");
//        when(s3Client.getUrl(anyString(), any())).thenAnswer(it -> {
//            return new URL("https://" + it.getArgument(0) + ".aws/" + it.getArgument(1));
//        });
        when(gcsOptions.getEnable()).thenReturn(true);
        when(deviceInfoService.getDeviceSupport(anyString())).thenReturn(new CloudDeviceSupport() {{
            setSupportGoogleStorage(true);
            setSupportCos(true);
            setSupportOci(true);
        }});
        when(googleStorageService.createGcsCredentials()).thenReturn(new GcsCredentials() {{
            setAccessToken("gcs_token");
            setExpiration(System.currentTimeMillis() + 100_0000);
        }});

        when(storageAllocateService.getStorageCredentials(any(), any(), anyString())).thenReturn(new StorageCredentials());
//        when(s3Service.createAwsCredential(anyString())).thenReturn(new AwsCredentials());
        when(s3Service.createAwsCredential(anyString(), any())).thenAnswer(it -> {
            final String sn = it.getArgument(0);
            final Function<String, AwsCredentials> factory = it.getArgument(1);
            return factory.apply(sn);
        });
        when(storageParamService.deviceNeedCloudStorageParams(any())).thenReturn(true);
    }

    private Integer userId = new Random().nextInt();
    private String sn = "unittest_" + OpenApiUtil.shortUUID();
    private String traceId = "unittest_" + OpenApiUtil.shortUUID();
    private int sliceNum = 10;

    @Test
    public void test_videoUploadBegin() {
        when(userVipService.isNoUserTier(any())).thenReturn(false);
        when(paasVipService.isVipDevice(any(), anyString())).thenReturn(true);
        UploadVideoBeginRequest req = UploadVideoBeginRequest.builder().serialNumber(sn).traceId(traceId)
                .motionType("video").mqttEventTime(System.currentTimeMillis() - 10).build();
        try { // 设备不存在
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(null);
            Result<JSONObject> result = videoService.videoUploadBegin(req);
            Assert.assertFalse(true);
        } catch (BaseException e) {
            Assert.assertEquals(ResultCollection.DEVICE_NO_ACCESS.getCode(), e.getErrorCode().intValue());
        } catch (Throwable e) {
            Assert.assertFalse(true);
        }
        DeviceDO deviceDO = DeviceDO.builder().serialNumber(sn)
                .build();
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(deviceDO);
        { // 绑定关系不存在
            when(userRoleService.queryUserRolesBySn(sn)).thenReturn(new UserRoleService.UserRoles(null, Collections.emptyList()));
            Result<JSONObject> result = videoService.videoUploadBegin(req);
            Assert.assertEquals(ResultCollection.DEVICE_NO_ACCESS.getCode(), result.getResult().intValue());
        }
        when(userRoleService.queryUserRolesBySn(sn)).thenReturn(new UserRoleService.UserRoles(userId, Arrays.asList(userId)));
        User user = new User();
        user.setId(userId);
        user.setTenantId("unittest");
        when(userService.queryUserById(userId)).thenReturn(user);
        AssumeRoleResult credentials = new AssumeRoleResult().withCredentials(new Credentials()
                .withAccessKeyId("ak").withAccessKeyId("sk").withSessionToken("st")
                .withExpiration(new Date())
        );
        when(stsClient.assumeRole(any())).thenReturn(credentials);
        { // 自有用户
            user.setType(UserType.REGISTER.getCode());
            Result<JSONObject> result = videoService.videoUploadBegin(req);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        { // 三方用户
            user.setType(UserType.THIRD.getCode());
            when(awsHelper.assumeRole(sn)).thenReturn(new Result<>(new AwsCredentials(credentials.getCredentials())));
            Result<JSONObject> result = videoService.videoUploadBegin(req);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }

        when(userVipService.isNoUserTier(any())).thenReturn(true);
        when(paasVipService.isVipDevice(any(), anyString())).thenReturn(false);
        when(messageNotificationSettingsService.queryMessageNotificationSetting(any(), any())).thenReturn(new MessageNotificationSetting() {{
            setEnableOther(1);
        }});
        {
            user.setType(UserType.REGISTER.getCode());
            Result<JSONObject> result = videoService.videoUploadBegin(req);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
    }

    @Test
    public void test_recordVideoCostTime() {
        DevicePirNotifyFactor factory = new DevicePirNotifyFactor();
        factory.setVip(true);
        when(pushService.getDevicePirNotifyFactor(anyString())).thenReturn(factory);
        when(userRoleService.findAllUsersForDevice(anyString())).thenReturn(Arrays.asList(345, 678));

        Tuple2<UploadVideoCompleteRequest, JSONObject> tuple = buildUploadVideoCompleteRequest(sn, traceId, sliceNum);
        UploadVideoCompleteRequest req = tuple.get(0);
        JSONObject data = tuple.get(1);
        final String s3VideoSliceInfoKey = S3_VIDEO_SLICE_INFO_PREFIX + traceId;
        {
            when(redisService.hashGetAll(s3VideoSliceInfoKey)).thenReturn(null);
            AssertUtil.assertNotException(() -> videoService.recordVideoCostTime(req, true));
        }
        when(redisService.hashGetAll(s3VideoSliceInfoKey)).thenReturn(data);
        doNothing().when(libraryUpdateReceiveAllService).updateReceiveAllSliceAsync(any());
        DevicePirNotifyFactor pirNotifyFactor = new DevicePirNotifyFactor();
        pirNotifyFactor.setVip(true);
        when(pushService.getDevicePirNotifyFactor(sn)).thenReturn(pirNotifyFactor);

        AssertUtil.assertNotException(() -> videoService.recordVideoCostTime(req, true));

        List<VideoReportEvent> videoReportEvents = Arrays.asList(
                new VideoReportEvent(EReportEvent.DOORBELL_PRESS),
                new VideoReportEvent(EReportEvent.DOORBELL_REMOVE),
                new VideoReportEvent(EReportEvent.DEVICE_CALL));
        List<String> events = videoReportEvents.stream().map(JSON::toJSONString).collect(Collectors.toList());
        when(redisService.rangeList(any())).thenReturn(events);

        AssertUtil.assertNotException(() -> videoService.recordVideoCostTime(req, true));

        AssertUtil.assertNotException(() -> videoService.recordVideoCostTime(req, true));
    }

    private static Tuple2<UploadVideoCompleteRequest, JSONObject> buildUploadVideoCompleteRequest(String sn, String traceId, int sliceNum) {
//        Random random = new Random();
        final long startTime = System.currentTimeMillis() - sliceNum * 2200 - 10 * 1000;
        UploadVideoCompleteRequest req = new UploadVideoCompleteRequest();
        req.setSerialNumber(sn);
        req.setTraceId(traceId);
        req.setSliceList(new LinkedList<>());
        String sliceKeyPrefix = UPLOAD_KEY_PREFIX.replace("${sn}", sn).replace("${traceId}", traceId);
        req.setImageKey(sliceKeyPrefix + UPLOAD_KEY_POSTFIX_IMAGE.replace("${imgType}", "jpeg"));
        req.setTotalStartRecordingTimestamp(startTime);
        req.setTotalEndRecordingTimestamp(startTime + sliceNum * 2200);
        req.setS3AddressReceivedTimestamp(startTime - 10);

        JSONObject data = new JSONObject()
                .fluentPut(S3_VIDEO_SLICE_INFO_NUM, sliceNum)
                .fluentPut(S3_VIDEO_SLICE_INFO_IMAGE_URL, "http://host/image.jpeg");

        for (int order = 0; order < sliceNum; order++) {
//            int period = 2000 + random.nextInt(200);
            int period = 2000;
            long sliceStartTime = startTime + period;
            VideoSliceRequest slice = VideoSliceRequest.builder()
                    .startRecordingTimestamp(sliceStartTime)
                    .endRecordingTimestamp(sliceStartTime + 2000)
                    .startUploadingTimestamp(sliceStartTime + 2020)
                    .endUploadingTimestamp(sliceStartTime + 2100)
                    .order(order).isLast(order == sliceNum)
                    .period(new BigDecimal(period).divide(new BigDecimal(1000)))
                    .uploadSuccess(1).fileSize(period)
                    .videoKey(sliceKeyPrefix + UPLOAD_KEY_POSTFIX_SLICE
                            .replace("${period}", period + "")
                            .replace("${order}", order + "")
                            .replace("${isLast}", (order == sliceNum) + "")
                    ).build();
            req.getSliceList().add(slice);

            data.fluentPut(S3_VIDEO_SLICE_INFO_ORDER_PREFIX + order, "https://host/image.jpeg")
                    .fluentPut(S3_VIDEO_SLICE_INFO_NOTIFY_TIME + order, sliceStartTime + 2800);
        }
        return new Tuple2(req, data);
    }


    @Test
    public void test_updateVideoReportEvents() {
        LibraryTb libraryTb = new LibraryTb();
        libraryTb.setTraceId(traceId);
        String key = S3_VIDEO_SLICE_INFO_REPORT_EVENTS + libraryTb.getTraceId();
        List<String> cache = new LinkedList<>();
        when(redisService.rangeList(key)).thenAnswer(it -> new LinkedList<>(cache));
        when(redisService.listRightPushAll(any(), any(), anyInt())).thenAnswer(it -> {
            List<Object> list = it.getArgument(1);
            list.stream().map(JSON::toJSONString).forEach(cache::add);
            return cache.size();
        });
        when(userRoleService.findAllUsersForDevice(sn)).thenReturn(Arrays.asList(userId));
        {
            LibraryTb library = videoService.updateVideoReportEvents(sn, traceId, Arrays.asList(EReportEvent.DOORBELL_PRESS));
            Assert.assertNotNull(library);
        }
        {
            LibraryTb library = videoService.updateVideoReportEvents(sn, traceId, Arrays.asList(EReportEvent.DOORBELL_PRESS));
            Assert.assertNotNull(library);
            Assert.assertEquals("[{\"event\":18},{\"event\":18}]", library.getDoorbellEventInfo());
            Assert.assertEquals("DOORBELL_PRESS", library.getDoorbellTags());
            Assert.assertEquals("", library.getDeviceCallEventTag());
        }
        {
            // when(libraryService.getLibraryTrace(traceId)).thenReturn(new LibraryTraceDO().setLibraryId(123).setSerialNumber(sn));
            LibraryTb library = videoService.updateVideoReportEvents(sn, traceId, Arrays.asList(EReportEvent.DOORBELL_REMOVE));
            Assert.assertNotNull(library);
            Assert.assertEquals("[{\"event\":18},{\"event\":18},{\"event\":19}]", library.getDoorbellEventInfo());
            Assert.assertEquals("DOORBELL_PRESS,DOORBELL_REMOVE", library.getDoorbellTags());
            Assert.assertEquals("", library.getDeviceCallEventTag());
        }
        {
            // when(libraryService.getLibraryTrace(traceId)).thenReturn(new LibraryTraceDO().setLibraryId(123).setSerialNumber(sn));
            LibraryTb library = videoService.updateVideoReportEvents(sn, traceId, Arrays.asList(EReportEvent.DOORBELL_PRESS));
            Assert.assertNotNull(library);
            Assert.assertEquals("[{\"event\":18},{\"event\":18},{\"event\":19},{\"event\":18}]", library.getDoorbellEventInfo());
            Assert.assertEquals("DOORBELL_PRESS,DOORBELL_REMOVE", library.getDoorbellTags());
            Assert.assertEquals("", library.getDeviceCallEventTag());
        }
        for (int i = 0; i < 2; i++) {
            // when(libraryService.getLibraryTrace(traceId)).thenReturn(new LibraryTraceDO().setLibraryId(123).setSerialNumber(sn));
            LibraryTb library = videoService.updateVideoReportEvents(sn, traceId, Arrays.asList(EReportEvent.DEVICE_CALL));
            Assert.assertNotNull(library);
            Assert.assertEquals("[{\"event\":18},{\"event\":18},{\"event\":19},{\"event\":18}]", library.getDoorbellEventInfo());
            Assert.assertEquals("DOORBELL_PRESS,DOORBELL_REMOVE", library.getDoorbellTags());
            Assert.assertEquals("DEVICE_CALL", library.getDeviceCallEventTag());
        }

    }

    @Test
    public void test_getS3ObjectUrl() {
        {
            when(videoSliceConfig.getDeviceS3AccelerateEnable()).thenReturn(true);
            String url = videoService.getS3ObjectUrl("bucket1", "key1");
            Assert.assertEquals("https://bucket1.s3-accelerate.amazonaws.com/key1", url);
        }
        {
            when(videoSliceConfig.getDeviceS3AccelerateEnable()).thenReturn(false);
            when(s3Client.getUrl(any(), any())).thenAnswer(it -> {
                String bucket = it.getArgument(0);
                String key = it.getArgument(1);
                return new URL("https://" + bucket + ".s3.cn-north-1.amazonaws.com.cn/" + key);
            });
            String url = videoService.getS3ObjectUrl("bucket1", "key1");
            Assert.assertEquals("https://bucket1.s3.cn-north-1.amazonaws.com.cn/key1", url);
        }
    }

    @Test
    public void test_downloadVideoM3u8() {
        when(videoStoreService.querySliceByAdminUserIdAndTraceId(1, traceId)).thenAnswer(it -> {
            List<VideoSliceDO> sliceList = new LinkedList<>();
            sliceList.add(VideoSliceDO.builder().period(new BigDecimal("2.123"))
                    .videoUrl("http://addx/videoSlice/sn/traceId/slice_2123_0_0.ts").build());
            sliceList.add(VideoSliceDO.builder().period(new BigDecimal("2.456"))
                    .videoUrl("http://addx/videoSlice/sn/traceId/slice_2456_0_1.ts").build());
            return sliceList;
        });
        byte[] bytes = videoService.downloadVideoM3u8(1, traceId);
        String m3u8Str = new String(bytes);
        String expectStr = "" +
                "#EXTM3U\n" +
                "#EXT-X-VERSION:3\n" +
                "#EXT-X-PLAYLIST-TYPE:VOD\n" +
                "#EXT-X-ALLOW-CACHE:YES\n" +
                "#EXT-X-MEDIA-SEQUENCE:0\n" +
                "#EXT-X-TARGETDURATION:3\n" +
                "#EXTINF:2.123,\n" +
                "http://addx/videoSlice/sn/traceId/slice_2123_0_0.ts?token\n" +
                "#EXTINF:2.456,\n" +
                "http://addx/videoSlice/sn/traceId/slice_2456_0_1.ts?token\n" +
                "#EXT-X-ENDLIST\n";
        Assert.assertEquals(expectStr, m3u8Str);
    }

    @Test
    @SneakyThrows
    public void test_getS3RootPath() {
        when(s3Client.getUrl(anyString(), anyString())).thenAnswer(it -> new URL("https://" + it.getArgument(0) + ".s3.cn-north-1.amazonaws.com.cn/"));
        String rootPath = videoService.getS3RootPath("bucket1");
        Assert.assertEquals("https://bucket1.s3.cn-north-1.amazonaws.com.cn/", rootPath);
    }

    private void prepareMockRedis(Map<String, Object> mockMap, List<String> mockList) {
//        when(videoSliceDAO.batchSaveSlice(any())).thenReturn(new HashSet<>(Arrays.asList(999)));
        when(videoStoreService.saveVideoSlice(any())).thenReturn(false);
//        Map<String, Object> mockMap = new LinkedHashMap<>();
        when(redisService.hashIncrementInt(anyString(), anyString(), anyInt())).thenAnswer(it -> {
            AtomicInteger count = (AtomicInteger) mockMap.computeIfAbsent(it.getArgument(1), k -> new AtomicInteger(0));
            return count.addAndGet(it.getArgument(2));
        });
        when(redisService.hashGetString(anyString(), anyString())).thenAnswer(it -> mockMap.get(it.getArgument(1)));
        doAnswer(it -> {
            mockMap.putAll(it.getArgument(1));
            return null;
        }).when(redisService).hashPutAll(anyString(), any());
//        List<String> mockList = new LinkedList<>();
        when(redisService.rangeList(anyString())).thenReturn(new ArrayList<>(mockList));
        when(redisService.listRightPushAll(anyString(), any(), anyInt())).thenAnswer(it -> {
            List<Object> list = it.getArgument(1);
            List<String> sliceJsonList = list.stream().map(JSON::toJSONString).collect(Collectors.toList());
            mockList.addAll(sliceJsonList);
            return null;
        });

        when(userRoleService.getDeviceAdminUserRole(anyString())).thenReturn(new UserRoleDO());
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setDeviceName("deviceName1");
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(deviceDO);
        when(libraryService.insertLibrary(any(), any(), any())).thenReturn(true);
        when(redisService.getAndSet(anyString(), any())).thenReturn(null);
    }

    private void test_handleVideoSlice(String sn, String traceId, int order, int sliceNum) {
        VideoSliceDO slice = new VideoSliceDO();
        slice.setSerialNumber(sn);
        slice.setTraceId(traceId);
        slice.setPeriod(new BigDecimal("2.123"));
        slice.setOrder(order);
        slice.setIsLast(order == sliceNum - 1);
        slice.setVideoUrl("https://a4x/video");
        slice.setFileSize(100);
        slice.setS3EventTime(System.currentTimeMillis());
        Result result1 = videoService.handleVideoSliceUpdate(traceId, sn, null, Arrays.asList(slice));
        Assert.assertEquals(result1.getMsg(), Result.successFlag, result1.getResult());
    }

    @Test
    @SneakyThrows
    public void test_handleVideoSliceUpdate() {
        String sn = "sn9988";
        String traceId = "trace9988";
        Map<String, Object> mockMap = new LinkedHashMap<>();
        List<String> mockList = new LinkedList<>();
        prepareMockRedis(mockMap, mockList);
        {
            Result result0 = videoService.handleVideoSliceUpdate(traceId, sn, "https://a4x/image", Collections.emptyList());
            Assert.assertEquals(Result.successFlag, result0.getResult());
            int sliceNum = 6;
            for (int order = 0; order < 3; order++) {
                test_handleVideoSlice(sn, traceId, order, sliceNum);
            }
            when(redisService.getAndSet(anyString(), any())).thenAnswer(it -> {
                LibraryTb libraryTb = new LibraryTb();
                libraryTb.setTraceId(traceId);
                libraryTb.setImageUrl("https://addx/image");
                libraryTb.setPeriod(BigDecimal.valueOf(10.345d));
                libraryTb.setFileSize(10000L);
                libraryTb.setSerialNumber(sn);
                libraryTb.setReceivedAllSlice(true);
                return JSON.toJSONString(libraryTb);
            });
            test_handleVideoSlice(sn, traceId, 3, sliceNum);
            when(redisService.getAndSet(anyString(), any())).thenReturn(null);
            for (int order = 4; order < sliceNum; order++) {
                test_handleVideoSlice(sn, traceId, order, sliceNum);
            }
        }
        Assert.assertTrue(true);
    }

    @Test
    public void test_handleVideoSliceUpdate2() {
        when(videoStoreService.saveVideoSlice(any())).thenReturn(true);
        when(userRoleService.getDeviceAdminUserRole(anyString())).thenReturn(new UserRoleDO());
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setDeviceName("deviceName1");
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(deviceDO);
        when(libraryService.insertLibrary(any(), any(), any())).thenReturn(true);
        {
            test_insertSliceAndImage();
        }
    }

    private void test_insertSliceAndImage() {
        when(redisService.hashGetString(anyString(), eq(S3_VIDEO_SLICE_INFO_IMAGE_URL))).thenReturn(null);
        { // slice order=0
            when(redisService.hashIncrementInt(anyString(), eq(S3_VIDEO_SLICE_INFO_ORDER_PREFIX + 0), anyInt())).thenReturn(1);
            when(redisService.hashIncrementInt(anyString(), eq(S3_VIDEO_SLICE_INFO_NUM), anyInt())).thenReturn(1);
            test_handleVideoSlice(sn, traceId, 0, 2);
        }
        { // slice order=0 duplicate
            when(redisService.hashIncrementInt(anyString(), eq(S3_VIDEO_SLICE_INFO_ORDER_PREFIX + 0), anyInt())).thenReturn(2);
            when(redisService.hashIncrementInt(anyString(), eq(S3_VIDEO_SLICE_INFO_NUM), anyInt())).thenReturn(2);
            test_handleVideoSlice(sn, traceId, 0, 2);
        }
        { // image
            Result result1 = videoService.handleVideoSliceUpdate(traceId, sn, "https://a4x/image", Arrays.asList());
            Assert.assertEquals(result1.getMsg(), Result.successFlag, result1.getResult());
        }
        when(redisService.hashGetString(anyString(), eq(S3_VIDEO_SLICE_INFO_IMAGE_URL))).thenReturn("https://a4x/image");
        { // slice order=1
            when(redisService.hashIncrementInt(anyString(), eq(S3_VIDEO_SLICE_INFO_ORDER_PREFIX + 1), anyInt())).thenReturn(1);
            when(redisService.hashIncrementInt(anyString(), eq(S3_VIDEO_SLICE_INFO_NUM), anyInt())).thenReturn(2);
            test_handleVideoSlice(sn, traceId, 1, 2);
        }
        { // slice order=2
            when(redisService.hashIncrementInt(anyString(), eq(S3_VIDEO_SLICE_INFO_ORDER_PREFIX + 2), anyInt())).thenReturn(1);
            when(redisService.hashIncrementInt(anyString(), eq(S3_VIDEO_SLICE_INFO_NUM), anyInt())).thenReturn(3);
            test_handleVideoSlice(sn, traceId, 2, 2);
        }
    }

    @Test
    public void test_VideoSliceRequest_merge() {
        VideoSliceKey key = new VideoSliceKey();
        key.setPeriod(new BigDecimal("1.234"));
        key.setIsLast(false);
        key.setOrder(0);
        VideoSliceRequest slice = new VideoSliceRequest();
        {
            VideoSliceRequest.merge(slice, key);
        }
        {
            slice.setPeriod(new BigDecimal("1.234"));
            slice.setIsLast(false);
            slice.setOrder(0);
            VideoSliceRequest.merge(slice, key);
        }
    }

    /*
    @Test
    public void test_GcsOptions() {
        ImmutableMap<Integer, String> lookBackDays2Bucket = ImmutableMap.<Integer, String>builder()
                .put(3, "a4x-vip-3d")
                .put(7, "a4x-vip-7d")
                .put(15, "a4x-vip-basic")
                .put(30, "a4x-vip-plus")
                .put(60, "a4x-vip-pro")
                .build();
        GcsOptions gcs = new GcsOptions();
        gcs.setLookBackDays2Bucket(lookBackDays2Bucket);
        {
            Assert.assertEquals("a4x-vip-3d", gcs.getBucketByLookBackDays(3));
            Assert.assertEquals("a4x-vip-7d", gcs.getBucketByLookBackDays(6));
            Assert.assertEquals("a4x-vip-basic", gcs.getBucketByLookBackDays(8));
            Assert.assertEquals("a4x-vip-plus", gcs.getBucketByLookBackDays(19));
            Assert.assertEquals("a4x-vip-pro", gcs.getBucketByLookBackDays(60));
            Assert.assertNull(gcs.getBucketByLookBackDays(61));
        }
        {
            gcs.setEnable(false);
            Assert.assertFalse(gcs.isEnableGoogleStore("sn1", null));
        }
        {
            gcs.setEnable(true);
            Assert.assertTrue(gcs.isEnableGoogleStore("sn1", null));
        }
        {
            gcs.setEnable(false);
            gcs.setWhiteSns(new HashSet<>(Arrays.asList("sn2")));
            Assert.assertFalse(gcs.isEnableGoogleStore("sn1", null));
        }
        {
            gcs.setEnable(false);
            gcs.setWhiteSns(new HashSet<>(Arrays.asList("sn2", "sn1")));
            Assert.assertTrue(gcs.isEnableGoogleStore("sn1", null));
        }
        {
            gcs.setEnable(false);
            gcs.setWhiteSns(new HashSet<>());
            Assert.assertFalse(gcs.isEnableGoogleStore("sn1", 123));
        }
        {
            gcs.setEnable(false);
            gcs.setWhiteSns(new HashSet<>());
            gcs.setWhiteUserIdSet(new HashSet<>(Arrays.asList(345)));
            Assert.assertFalse(gcs.isEnableGoogleStore("sn1", 123));
        }
        {
            gcs.setEnable(false);
            gcs.setWhiteSns(new HashSet<>());
            gcs.setWhiteUserIdSet(new HashSet<>(Arrays.asList(123)));
            Assert.assertTrue(gcs.isEnableGoogleStore("sn1", 123));
        }
        {
            gcs.setEnable(false);
            gcs.setWhiteSns(new HashSet<>());
            gcs.setWhiteUserIdSet(new HashSet<>());
            gcs.setWhiteUserPercent(23);
            Assert.assertFalse(gcs.isEnableGoogleStore("sn1", 123));
        }
        {
            gcs.setEnable(false);
            gcs.setWhiteSns(new HashSet<>());
            gcs.setWhiteUserIdSet(new HashSet<>());
            gcs.setWhiteUserPercent(50);
            Assert.assertTrue(gcs.isEnableGoogleStore("sn1", 123));
        }
        gcs.init();
    }
    */
}
