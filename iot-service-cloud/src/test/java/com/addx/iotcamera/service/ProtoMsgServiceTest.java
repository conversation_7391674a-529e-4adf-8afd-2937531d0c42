package com.addx.iotcamera.service;

import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.enums.ProtoMsgName;
import com.addx.iotcamera.helper.RestLogHelper;
import com.addx.iotcamera.service.proto_msg.ProtoMsgConvertor;
import com.addx.iotcamera.service.proto_msg.ProtoMsgCtx;
import com.addx.iotcamera.service.proto_msg.ProtoMsgService;
import com.addx.iotcamera.service.proto_msg.ProtoMsgTrans;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.GeneratedMessage;
import com.google.protobuf.util.JsonFormat;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.ServletInputStream;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.util.Base64;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ProtoMsgServiceTest {

    @InjectMocks
    private ProtoMsgService protoMsgService;

    @Mock
    private RestLogHelper restLogHelper;
    @Mock
    private HttpServletRequest httReq;
    @Mock
    private HttpServletResponse httpResp;
    @Mock
    private ServletInputStream servletInputStream;
    @Mock
    private ServletOutputStream servletOutputStream;

    @Before
    @SneakyThrows
    public void init() {
        when(httpResp.getOutputStream()).thenReturn(servletOutputStream);
    }

    @Test
    public void test_handleProtoMsg() {
        for (ProtoMsgName msgName : ProtoMsgName.values()) {
            GeneratedMessage pbMsg = msgName.getProtoMsgConvertor().getDefaultProtoMsg();
            byte[] bytes = pbMsg.toByteArray();
            {
                GeneratedMessage pbRespMsg = protoMsgService.handleProtoMsg(msgName, bytes, (msg, obj) -> "{\"result\":0}");
                Assert.assertEquals(msgName.getProtoResponseMsgCls(), pbRespMsg.getClass());
            }
            {
                GeneratedMessage pbRespMsg = protoMsgService.handleProtoMsg(msgName, new byte[0], (msg, obj) -> "{}");
                Assert.assertEquals(msgName.getProtoResponseMsgCls(), pbRespMsg.getClass());
            }
        }
    }

    @Test
    @SneakyThrows
    public void test_handleHttpProtoMsg() {
        when(httReq.getHeader("Content-Encoding")).thenReturn("");
        for (ProtoMsgName msgName : ProtoMsgName.values()) {
            GeneratedMessage pbMsg = msgName.getProtoMsgConvertor().getDefaultProtoMsg();
            byte[] bytes = pbMsg.toByteArray();
            Assert.assertNotNull(bytes);
            when(httReq.getInputStream()).thenAnswer(it -> {
                ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
                when(servletInputStream.read(any(byte[].class))).thenAnswer(it2 -> {
                    return bais.read(it2.getArgument(0));
                });
                return servletInputStream;
            });
            {
                doThrow(new RuntimeException("")).when(servletOutputStream).write(any());
                protoMsgService.handleHttpProtoMsg(httReq, httpResp, msgName.name(), (msg, obj) -> "{}");
            }
            {
                doNothing().when(servletOutputStream).write(any());
                protoMsgService.handleHttpProtoMsg(httReq, httpResp, msgName.name(), (msg, obj) -> "{}");
            }
            {
                when(httReq.getAttribute(RequestAttributeKeys.REQ_BODY_BYTES)).thenReturn(bytes);
                doThrow(new RuntimeException("")).when(servletOutputStream).write(any());
                protoMsgService.handleHttpProtoMsg(httReq, httpResp, msgName.name(), (msg, obj) -> "{}");
            }
            {
                when(httReq.getAttribute(RequestAttributeKeys.REQ_BODY_BYTES)).thenReturn(bytes);
                doNothing().when(servletOutputStream).write(any());
                protoMsgService.handleHttpProtoMsg(httReq, httpResp, msgName.name(), (msg, obj) -> "{}");
            }
        }
        protoMsgService.handleHttpProtoMsg(httReq, httpResp, "xyz", (msg, obj) -> "{}");
    }

    @Test
    public void test_handleTokenInValid() {
        {
            when(httReq.getServletPath()).thenReturn("/deviceMsg/xyz");
            when(httReq.getContentType()).thenReturn("application/protobuf");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertTrue(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/deviceMsg/xyz");
            when(httReq.getContentType()).thenReturn("APPLICATION/PROTOBUF");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertTrue(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/device/xyz");
            when(httReq.getContentType()).thenReturn("application/protobuf");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertFalse(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/deviceMsg/xyz");
            when(httReq.getContentType()).thenReturn("application/json");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertFalse(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/deviceMsg/connection");
            when(httReq.getContentType()).thenReturn("application/protobuf");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertTrue(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/deviceMsg/connection");
            when(httReq.getContentType()).thenReturn("APPLICATION/PROTOBUF");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertTrue(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/device/connection");
            when(httReq.getContentType()).thenReturn("application/protobuf");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertFalse(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/deviceMsg/connection");
            when(httReq.getContentType()).thenReturn("application/json");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertFalse(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/deviceMsg/connection/");
            when(httReq.getContentType()).thenReturn("application/protobuf");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertTrue(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/deviceMsg/connection/");
            when(httReq.getContentType()).thenReturn("APPLICATION/PROTOBUF");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertTrue(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/device/connection/");
            when(httReq.getContentType()).thenReturn("application/protobuf");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertFalse(result);
        }
        {
            when(httReq.getServletPath()).thenReturn("/deviceMsg/connection/");
            when(httReq.getContentType()).thenReturn("application/json");
            boolean result = protoMsgService.handleTokenInValid(httReq, httpResp, Result.Failure(""));
            Assert.assertFalse(result);
        }
    }

    @Test
    public void test_ProtoMsgCtx() {
        {
            ProtoMsgCtx ctx = new ProtoMsgCtx();
            Assert.assertEquals(false, ctx.isReqParseSuccess());
            Assert.assertEquals(0, ctx.getReqByteSize());
            Assert.assertEquals("", ctx.getReqBytesBase64());
            Assert.assertEquals(0, ctx.getReqJsonSize());
            Assert.assertEquals(0, ctx.getRespByteSize());
            Assert.assertEquals(0, ctx.getRespJsonSize());
        }
        {
            String reqJsonStr = "{}";
            byte[] reqBytes = reqJsonStr.getBytes();
            String respJsonStr = "{}";
            byte[] respBytes = respJsonStr.getBytes();

            ProtoMsgCtx ctx = new ProtoMsgCtx();
            ctx.setReqBytes(reqBytes);
            ctx.setReqJsonStr(reqJsonStr);
            ctx.setRespBytes(respBytes);
            ctx.setRespJsonStr(respJsonStr);
            Assert.assertEquals(true, ctx.isReqParseSuccess());
            Assert.assertEquals(respBytes.length, ctx.getReqByteSize());
            Assert.assertEquals(Base64.getEncoder().encodeToString(reqBytes), ctx.getReqBytesBase64());
            Assert.assertEquals(reqJsonStr.length(), ctx.getReqJsonSize());
            Assert.assertEquals(respBytes.length, ctx.getRespByteSize());
            Assert.assertEquals(respJsonStr.length(), ctx.getRespJsonSize());
        }
    }

    @Test
    public void test_transJsonStr() {
        {
            {
                String jsonStr = "{\"data\":{\"value\":{\"apInfo\":\"{}\"}}}";
                String expectResult = "{\"data\":{\"value\":{\"apInfo\":{}}}}";
                String result = ProtoMsgTrans.transJsonStr(ProtoMsgName.syncApInfo, jsonStr);
                Assert.assertEquals(expectResult, result);
            }
            {
                String jsonStr = "{\"data\":{\"value\":{\"apInfo\":\"\"}}}";
                String expectResult = "{\"data\":{\"value\":{}}}";
                String result = ProtoMsgTrans.transJsonStr(ProtoMsgName.syncApInfo, jsonStr);
                Assert.assertEquals(expectResult, result);
            }
        }
        {
            {
                String jsonStr = "{\"data\":{\"dns\":{\"domain1\":[\"ip1\",\"ip2\"]}}}";
                String expectResult = "{\"data\":{\"dns\":{\"domain1\":{\"ip\":[\"ip1\",\"ip2\"]}}}}";
                String result = ProtoMsgTrans.transJsonStr(ProtoMsgName.config, jsonStr);
                Assert.assertEquals(expectResult, result);
            }
        }
        {
            {
                String jsonStr = "{\"data\":{\"value\":{\"floodlightSchedulePlan\":\"[]\"}}}";
                String expectResult = "{\"data\":{\"value\":{\"floodlightSchedulePlan\":[]}}}";
                String result = ProtoMsgTrans.transJsonStr(ProtoMsgName.setting, jsonStr);
                Assert.assertEquals(expectResult, result);
            }
            {
                String jsonStr = "{\"data\":{\"value\":{\"floodlightSchedulePlan\":\"\"}}}";
                String expectResult = "{\"data\":{\"value\":{}}}";
                String result = ProtoMsgTrans.transJsonStr(ProtoMsgName.setting, jsonStr);
                Assert.assertEquals(expectResult, result);
            }
        }
        {
            {
                String jsonStr = "{\"data\":{\"value\":{\"4g\":{}}}}";
                String expectResult = "{\"data\":{\"value\":{\"device4g\":{}}}}";
                String result = ProtoMsgTrans.transJsonStr(ProtoMsgName.setting, jsonStr);
                Assert.assertEquals(expectResult, result);
            }
        }
        {
            {
                String jsonStr = "{\"data\":{\"value\":{\"pirSensor\":{\"SunshineDetection\":{\"sunshineOpend\":true}}}}}";
                String expectResult = "{\"data\":{\"value\":{\"pirSensor\":{\"SunshineDetection\":{},\"sunshineOpend\":true}}}}";
                String result = ProtoMsgTrans.transJsonStr(ProtoMsgName.setting, jsonStr);
                Assert.assertEquals(expectResult, result);
            }
            {
                String jsonStr = "{\"data\":{\"id\":\"cmd:5fa48588128d410ebc7bd074f6e883d3\",\"name\":\"settings\",\"time\":1741600362,\"value\":{\"motionTrackMode\":0,\"detectVehicleAi\":true,\"liveSpeakerVolume\":100,\"wifiPowerLevel\":3,\"powerSource\":\"unselected\",\"language\":\"en\",\"sdCardVideoMode\":\"continual\",\"panTiltSpeedIntRange\":50,\"devicePersonDetect\":0,\"pirRecordTime\":\"180s\",\"detectPackageAi\":true,\"irThreshold\":20,\"pir\":1,\"enableOtherMotionAi\":false,\"videoAntiFlickerFrequency\":\"60Hz\",\"detectPersonAi\":true,\"doorbellPressNotifySwitch\":true,\"recLen\":-1,\"reportPetAi\":true,\"otaAutoUpgrade\":true,\"voiceVolume\":100,\"alarmVolume\":75,\"detectNuisanceAnimalAi\":0,\"timeZone\":-240,\"pirSensitivity\":\"high\",\"recordingAudioToggleOn\":true,\"pirCooldownTime\":\"10s\",\"detectFaceAi\":false,\"mtu\":1480,\"alarmDuration\":5,\"liveAudioToggleOn\":true,\"mirrorFlip\":0,\"chargeAutoPowerOnCapacity\":10,\"video12HourSwitch\":true,\"whiteLightScintillation\":0,\"motionTrack\":1,\"antiflicker\":60,\"snapshotRecordingSwitch\":1,\"sdCardCooldownSwitch\":0,\"deviceCallToggleOn\":true,\"nightVisionMode\":1,\"cryDetectLevel\":3,\"floodlightScheduleSwitch\":true,\"debugReport\":false,\"chargeAutoPowerOnSwitch\":0,\"pirSirenDuration\":0,\"reportPersonAi\":true,\"mechanicalDingDongDuration\":0,\"mechanicalDingDongSwitch\":0,\"sdCardPirRecordTime\":\"10s\",\"logUpload\":false,\"nightThresholdLevel\":2,\"cooldownInS\":0,\"alarmWhenRemoveToggleOn\":false,\"motionFloodlightTimer\":\"180s\",\"voiceVolumeSwitch\":1,\"cryDetect\":0,\"deviceStatusInterval\":60,\"motionTriggeredFloodlightSwitch\":true,\"sdCardCooldownSeconds\":\"10s\",\"videoResolution\":\"2k\",\"floodlightMode\":\"always\",\"floodlightSchedulePlan\":\"[{\\\"endHour\\\":6,\\\"endMinute\\\":0,\\\"luminance\\\":30,\\\"startHour\\\":20,\\\"startMinute\\\":30}]\",\"recLamp\":1,\"detectPetAi\":true,\"snapshotRecordingCaptureInterval\":\"auto\"}},\"msg\":\"Success\",\"result\":0}";
                String expectResult = "{\"data\":{\"id\":\"cmd:5fa48588128d410ebc7bd074f6e883d3\",\"name\":\"settings\",\"time\":1741600362,\"value\":{\"motionTrackMode\":0,\"detectVehicleAi\":true,\"liveSpeakerVolume\":100,\"wifiPowerLevel\":3,\"powerSource\":\"unselected\",\"language\":\"en\",\"sdCardVideoMode\":\"continual\",\"panTiltSpeedIntRange\":50,\"devicePersonDetect\":0,\"pirRecordTime\":\"180s\",\"detectPackageAi\":true,\"irThreshold\":20,\"pir\":1,\"enableOtherMotionAi\":false,\"videoAntiFlickerFrequency\":\"60Hz\",\"detectPersonAi\":true,\"doorbellPressNotifySwitch\":true,\"recLen\":-1,\"reportPetAi\":true,\"otaAutoUpgrade\":true,\"voiceVolume\":100,\"alarmVolume\":75,\"detectNuisanceAnimalAi\":0,\"timeZone\":-240,\"pirSensitivity\":\"high\",\"recordingAudioToggleOn\":true,\"pirCooldownTime\":\"10s\",\"detectFaceAi\":false,\"mtu\":1480,\"alarmDuration\":5,\"liveAudioToggleOn\":true,\"mirrorFlip\":0,\"chargeAutoPowerOnCapacity\":10,\"video12HourSwitch\":true,\"whiteLightScintillation\":0,\"motionTrack\":1,\"antiflicker\":60,\"snapshotRecordingSwitch\":1,\"sdCardCooldownSwitch\":0,\"deviceCallToggleOn\":true,\"nightVisionMode\":1,\"cryDetectLevel\":3,\"floodlightScheduleSwitch\":true,\"debugReport\":false,\"chargeAutoPowerOnSwitch\":0,\"pirSirenDuration\":0,\"reportPersonAi\":true,\"mechanicalDingDongDuration\":0,\"mechanicalDingDongSwitch\":0,\"sdCardPirRecordTime\":\"10s\",\"logUpload\":false,\"nightThresholdLevel\":2,\"cooldownInS\":0,\"alarmWhenRemoveToggleOn\":false,\"motionFloodlightTimer\":\"180s\",\"voiceVolumeSwitch\":1,\"cryDetect\":0,\"deviceStatusInterval\":60,\"motionTriggeredFloodlightSwitch\":true,\"sdCardCooldownSeconds\":\"10s\",\"videoResolution\":\"2k\",\"floodlightMode\":\"always\",\"floodlightSchedulePlan\":[{\"endHour\":6,\"endMinute\":0,\"luminance\":30,\"startHour\":20,\"startMinute\":30}],\"recLamp\":1,\"detectPetAi\":true,\"snapshotRecordingCaptureInterval\":\"auto\"}},\"msg\":\"Success\",\"result\":0}";
                String result = ProtoMsgTrans.transJsonStr(ProtoMsgName.setting, jsonStr);
                Assert.assertEquals(expectResult, result);
            }
        }
        {
            {
                String jsonStr = "{\"data\":{\"value\":{\"k1\":[],\"k2\":[]}}}";
                String expectResult = "{\"data\":{\"value\":{\"k1\":{\"plans\":[]},\"k2\":{\"plans\":[]}}}}";
                String result = ProtoMsgTrans.transJsonStr(ProtoMsgName.dormancyPlanSetting, jsonStr);
                Assert.assertEquals(expectResult, result);
            }
        }
    }

    @Test
    public void test_ProtoMsgConvertor() {
        {
            AssertUtil.assertThrowException(() -> ProtoMsgConvertor.create("", null));
        }
        {
            {
                GeneratedMessage pbMsg = ProtoMsgName.config.getProtoMsgConvertor().getDefaultProtoMsg();
                GeneratedMessage result = ProtoMsgName.config.getProtoMsgConvertor().parseFrom(new byte[0]);
                Assert.assertEquals(pbMsg, result);
            }
            {
                GeneratedMessage pbMsg = ProtoMsgName.config.getProtoMsgConvertor().getDefaultProtoMsg();
                Assert.assertNotNull(ProtoMsgName.config.getProtoMsgConvertor().parseFrom(pbMsg.toByteArray()));
            }
        }
        {
            {
                Assert.assertNull(ProtoMsgName.config.getProtoMsgConvertor().printJsonStr(null));
            }
        }
        {
            Assert.assertNull(ProtoMsgName.config.getProtoMsgConvertor().parseJsonStr(null));
            Assert.assertNull(ProtoMsgName.config.getProtoMsgConvertor().parseJsonStr(""));
            Assert.assertNotNull(ProtoMsgName.config.getProtoMsgConvertor().parseJsonStr("{}"));
        }
    }

    @Test
    @SneakyThrows
    public void test_printJsonStr() {
        String pbStrBase64 = "CmQKEDoOMTczOTUyMjQyN183MDISHgj4ogESFDg5NDMwMTAxNTI0MDU3NDAyNjYxGLO5AhgCIgQIZhAAIgQIbRAAKAAyDjE3Mzk1MjI0MjdfNzAzOABCBkVHOTE1UUj/gwFQP1gFEgZ3YWtldXAYl+/KvQYgAQ==";
        byte[] bytes = Base64.getDecoder().decode(pbStrBase64);
        GeneratedMessage pbMsg = ProtoMsgName.wakeup.getProtoMsgConvertor().parseFrom(bytes);
        log.info("pbMsg:{}", pbMsg);
        {   // 修改前的
            JsonFormat.Printer jsonPrinter0 = JsonFormat.printer();
            String jsonStr = jsonPrinter0.print(pbMsg);
            log.info("jsonStr:{}", jsonStr);
            JSONObject jsonObj = JSON.parseObject(jsonStr);
            JSONObject valueObj = jsonObj.getJSONObject("value");
            Assert.assertNotNull(valueObj);
            Assert.assertFalse(valueObj.containsKey("traffic_data"));
            Assert.assertTrue(valueObj.containsKey("trafficData"));
        }
        {   // 修改后的
            String jsonStr = ProtoMsgName.wakeup.getProtoMsgConvertor().printJsonStr(pbMsg);
            log.info("jsonStr:{}", jsonStr);
            JSONObject jsonObj = JSON.parseObject(jsonStr);
            JSONObject valueObj = jsonObj.getJSONObject("value");
            Assert.assertNotNull(valueObj);
            Assert.assertTrue(valueObj.containsKey("traffic_data"));
            Assert.assertFalse(valueObj.containsKey("trafficData"));
        }
    }

}
