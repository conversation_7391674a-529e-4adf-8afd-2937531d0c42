package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.init.CopyWriteInit;
import com.addx.iotcamera.config.apollo.GlobalConfig;
import com.addx.iotcamera.config.app.ProductExplanReplaceConfig;
import com.addx.iotcamera.enums.VideoQuestion;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class CopyWriteInitTest {


    @InjectMocks
    private CopyWriteInit copyWriteInit;

    @Mock
    private GlobalConfig globalConfig;

    @Mock
    private ProductExplanReplaceConfig productExplanReplaceConfig;

    @Test
    public void test() throws IOException {
        String resourcePath = "gitconfig/copywrite/iot.csv";
        CopyWrite config = new CopyWrite();
        CopyWriteInit.readStreamCsv(resourcePath, config);


        List<String> notTextTitleKeys = VideoQuestion.initQuestionBackText(config);
        Assert.assertEquals(Collections.EMPTY_LIST, notTextTitleKeys);
    }

    @Test
    public void testGetFourGList() {
        // 模拟 productExplainJson 数据
        JSONObject productExplainJson = new JSONObject();
        productExplainJson.put("productExplainFourG1", "4G特性A");
        productExplainJson.put("productExplainFourG2Android", "4G特性B-Android");
        productExplainJson.put("productExplainFourG3", "4G特性C");
        productExplainJson.put("productExplainFourG4Ios", "4G特性D-Ios");


        List<String> resultsAndroid = copyWriteInit.getFourGList("askari", "en", "android", productExplainJson);

        Assert.assertEquals(3, resultsAndroid.size());
    }
}
