package com.addx.iotcamera.service;


import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.ShareQueryRequest;
import com.addx.iotcamera.bean.device.share.ShareUserInfoQueryResponseDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.dao.device.ShareApprovalLocalDAO;
import com.addx.iotcamera.enums.DeviceOnlineStatusEnums;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.addx.iotcamera.enums.DeviceStateMachineEnums.STATE_MQTT_CONNECTED;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceRelationshipServiceTest {

    @InjectMocks
    private DeviceRelationshipService deviceRelationshipService;

    @InjectMocks
    private BindService bindService;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private UserService userService;

    @Mock
    private OpenApiWebhookService openApiWebhookService;

    @Mock
    private RoleDefinitionService roleDefinitionService;

    @Mock
    private DevicePlatformEventPublisher devicePlatformEventPublisher;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private ActivityZoneService activityZoneService;

    @Mock
    private StateMachineService stateMachineService;

    @Mock
    private DeviceService deviceService;

    @Mock
    private NotificationService notificationService;

    @Mock
    private VideoSearchService videoSearchService;

    @Mock
    private ShareApprovalLocalDAO shareApprovalLocalDAO;


    @Test
    public void testShareUndoShare() {
        Integer res = deviceRelationshipService.undoShare(new ShareApprovalDO());
        Assert.assertTrue(res.intValue() == 0);
        res = deviceRelationshipService.undoShareSelf(new ShareApprovalDO());
        Assert.assertTrue(res.intValue() == 0);
    }

    @Test
    public void test_queryRecentApprovalsLocal(){
        List<ShareQueryResponseDO> exceptedResult;
        List<ShareQueryResponseDO> actualResult;
        ShareApprovalDO requestDO = new ShareApprovalDO();
        requestDO.setTargetEmail("email");
        when(userService.queryUserById(any())).thenReturn(new User());
        {
            //没有未处理的分享记录
            when(shareApprovalLocalDAO.queryShareApprovalList(any())).thenReturn(Lists.newArrayList());
            exceptedResult = Lists.newArrayList();
            actualResult = deviceRelationshipService.queryRecentApprovalsLocal(requestDO);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            when(shareApprovalLocalDAO.queryShareApprovalList(any())).thenReturn(
                Arrays.asList(
                    ShareApprovalDO.builder().adminId(1).build(),
                        ShareApprovalDO.builder().adminId(2).build()
                )
            );
            Map<Integer,User> userMap = Maps.newHashMap();
            userMap.put(1,User.builder().id(1).status(1).email("email1").build());
            userMap.put(2,User.builder().id(2).status(0).email("email2").build());
            when(userService.queryUserMap(any())).thenReturn(userMap);

            actualResult = deviceRelationshipService.queryRecentApprovalsLocal(new ShareApprovalDO());
            Assert.assertEquals(actualResult.size(),1);
        }
    }


    @Test
    public void test_getDeviceShareUserList(){
        ShareQueryRequest request = new ShareQueryRequest();
        request.setSerialNumber("sn");
        List<ShareStatusDO> exceptedResult;
        List<ShareStatusDO> actualResult;
        {
            // 已分享、分享中都为空
            when(userRoleService.findAllUsersForDevice(any())).thenReturn(Lists.newArrayList());
            when(shareApprovalLocalDAO.queryDeviceShareingList(any(),any(),any())).thenReturn(Lists.newArrayList());

            exceptedResult = Lists.newArrayList();
            actualResult = deviceRelationshipService.getDeviceShareUserList(request);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            // 已分享不为空
            when(userRoleService.findAllUsersForDevice(any())).thenReturn(Arrays.asList(1,2));
            Map<Integer,User> userMap = new HashMap<>();
            userMap.put(1,User.builder().name("name").email("email").build());
            when(userService.queryUserMap(any())).thenReturn(userMap);

            when(shareApprovalLocalDAO.queryDeviceShareingList(any(),any(),any())).thenReturn(Lists.newArrayList());

            exceptedResult = Collections.singletonList(
                    ShareStatusDO.builder()
                    .userId(1)
                    .userName("name")
                    .userEmail("email")
                    .status(1)
                    .shareDeviceNum(1)
                    .build()
            );
            actualResult = deviceRelationshipService.getDeviceShareUserList(request);
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }

    @Test
    public void test_queryShareUserInfo(){
        ShareQueryRequest request = new ShareQueryRequest();
        request.setApp(new AppInfo());
        String sn = "sn";
        request.setSerialNumber(sn);
        String email = "email";
        request.setTargetEmail(email);

        DeviceManualDO deviceManualDO = new DeviceManualDO();
        deviceManualDO.setModelNo("modelNo");
        when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(deviceManualDO);
        DeviceDO deviceDO = DeviceDO.builder().deviceName("deviceName").build();
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);
        when(stateMachineService.getDeviceState(any())).thenReturn(null);

        ShareUserInfoQueryResponseDO exceptedResult;
        ShareUserInfoQueryResponseDO actualResult;
        {
            //被邀请者未创建账号
            request.setUserId(null);
            when(userService.queryUserById(any())).thenReturn(null);

            ShareUserInfoQueryResponseDO.UserShareDeviceDO userShareDevice = new ShareUserInfoQueryResponseDO.UserShareDeviceDO();
            userShareDevice.setSerialNumber(sn);
            userShareDevice.setOnline(0);
            userShareDevice.setModelNo(deviceManualDO.getModelNo());
            userShareDevice.setDeviceName(deviceDO.getDeviceName());
            userShareDevice.setShared(false);

            exceptedResult = ShareUserInfoQueryResponseDO.builder()
                    .targetEmail(email)
                    .targetId(0)
                    .shareStatus(0)
                    .userShareDevice(userShareDevice)
                    .build();

            actualResult = deviceRelationshipService.queryShareUserInfo(request);
            Assert.assertEquals(actualResult,exceptedResult);
        }

        {
            //被邀请者未创建账号
            request.setUserId(null);
            when(userService.getUserByEmailAndTenantId(any(),any(),any())).thenReturn(User.builder().id(1).build());
            when(userRoleService.getUserRoleDOByUserIdAndSerialNumber(any(),any())).thenReturn(new UserRoleDO());


            ShareUserInfoQueryResponseDO.UserShareDeviceDO userShareDevice = new ShareUserInfoQueryResponseDO.UserShareDeviceDO();
            userShareDevice.setSerialNumber(sn);
            userShareDevice.setOnline(0);
            userShareDevice.setModelNo(deviceManualDO.getModelNo());
            userShareDevice.setDeviceName(deviceDO.getDeviceName());
            userShareDevice.setShared(true);

            exceptedResult = ShareUserInfoQueryResponseDO.builder()
                    .targetEmail(email)
                    .targetId(1)
                    .shareStatus(1)
                    .userShareDevice(userShareDevice)
                    .build();

            actualResult = deviceRelationshipService.queryShareUserInfo(request);
            Assert.assertEquals(actualResult,exceptedResult);
        }
    }

    @Test
    public void test_initDeviceOnlineStatus(){
        DeviceStateDO deviceStateDO ;
        {
            deviceStateDO = DeviceStateDO.builder()
                    .data("unstable")
                    .build();

            Assert.assertTrue(DeviceOnlineStatusEnums.OFFLINE.getCode().equals(deviceStateDO.initDeviceOnlineStatus()));
        }
        {
            deviceStateDO = DeviceStateDO.builder()
                    .stateId(STATE_MQTT_CONNECTED.getCode())
                    .build();

            Assert.assertTrue(DeviceOnlineStatusEnums.ONLINE.getCode().equals(deviceStateDO.initDeviceOnlineStatus()));
        }
    }
}
