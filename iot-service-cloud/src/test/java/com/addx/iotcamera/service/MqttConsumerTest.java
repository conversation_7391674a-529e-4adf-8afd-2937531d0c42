package com.addx.iotcamera.service;

import com.addx.iotcamera.config.MqttConfig;
import com.addx.iotcamera.mqtt.MqttConsumer;
import com.addx.iotcamera.mqtt.handler.extend.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
//@RunWith(MockitoJUnitRunner.Silent.class)
public class MqttConsumerTest {

//    @Test
    @SneakyThrows
    public void test_mqtt() {
        CmdAckHandler cmdAckHandler = new CmdAckHandler();
        ConnectionHandler connectionHandler = new ConnectionHandler();
        RequestHandler requestHandler = new RequestHandler();
        SettingAckHandler settingAckHandler = new SettingAckHandler();
        StatusHandler statusHandler = new StatusHandler();
        WebrtcAckHandler webrtcAckHandler = new WebrtcAckHandler();

        final MqttConsumer mqttConsumer = new MqttConsumer(cmdAck<PERSON><PERSON><PERSON>, connectionHand<PERSON>,
                requestHand<PERSON>, settingAck<PERSON><PERSON><PERSON>, status<PERSON>and<PERSON>, webrtcAckHandler);

        final MqttConfig mqttConfig = new MqttConfig();
        mqttConfig.setServerUri("tcp://vmq-us.addx.live");
        mqttConfig.setMqttConsumer(mqttConsumer);

        mqttConfig.initClient();
        Thread.currentThread().join();
    }

}
