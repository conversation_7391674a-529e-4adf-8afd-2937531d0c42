package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.DeviceOTADO;
import com.addx.iotcamera.bean.domain.device.RoleDefinitionDO;
import com.addx.iotcamera.bean.domain.device.SdCard;
import com.addx.iotcamera.config.device.DeviceSettingConfig;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.service.device.*;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.LinkedList;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceAttributeTest {

    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private VideoSearchService videoSearchService;
    @Mock
    private DeviceModelIconService deviceModelIconService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private DeviceSettingConfig deviceSettingConfig;
    @Mock
    private DeviceModelService deviceModelService;
    @Mock
    private DeviceModelConfigService deviceModelConfigService;
    @Mock
    private DeviceOTAService deviceOTAService;
    @Mock
    private FirmwareService firmwareService;
    @Mock
    private DeviceSdCardStatusService deviceSdCardStatusService;
    @Mock
    private DeviceService deviceService;
    @Mock
    private DeviceStatusService deviceStatusService;
    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private StateMachineService stateMachineService;
    @Mock
    private DeviceDormancyPlanService deviceDormancyPlanService;
    @Mock
    private LocationInfoService locationInfoService;
    @Mock
    private RoleDefinitionService roleDefinitionService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private RedisService redisService;
    @Mock
    private DeviceEnumMappingService deviceEnumMappingService;
    @Mock
    private UserTierDeviceService userTierDeviceService;
    @InjectMocks
    private DeviceAttributeService deviceAttributeService;
    @Mock
    private FactoryDataQueryService factoryDataQueryService;

    @Before
    public void before() {

    }

    private void mockData(String sn, Integer userId, String modelNo) {
        when(deviceModelService.queryDeviceModelCategory(any())).thenReturn(DeviceModelCategoryEnums.CAMERA.getCode());
        when(locationInfoService.listUserLocations(any())).thenReturn(new LinkedList<LocationDO>() {{
            add(new LocationDO() {{
                setId(123);
                setLocationName("客厅");
            }});
            add(new LocationDO() {{
                setId(456);
                setLocationName("车库");
            }});
        }});
        when(userRoleService.getUserRoleDOByUserIdAndSerialNumber(userId, sn)).thenReturn(new UserRoleDO() {{
            setUserId(userId);
            setAdminId(userId);
            setRoleId(UserRoleEnums.ADMIN.getCode() + "");
            setSerialNumber(sn);
        }});
        when(roleDefinitionService.quRoleDefinitionDO(anyInt())).thenReturn(new RoleDefinitionDO() {{
            setRole(65536);
            setRoleName("管理员");
        }});
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(new DeviceDO() {{
            setActivatedTime(PhosUtils.getUTCStamp());
            setActivated(1);
            setFirmwareId("1.2.3");
            setDormancyPlanSwitch(1);
            setLocationId(123);
            setDeviceName("设备1");
            setRecResolution("1920x1080");
        }});
        when(deviceInfoService.getSupportChangeCodec(any())).thenReturn(true);
        when(firmwareService.buildFirmwareView(any(DeviceDO.class), any(DeviceOTADO.class))).thenReturn(new FirmwareViewDO() {{
            setFirmwareStatus(1);
            setTargetFirmware("1.2.4");
        }});
        when(deviceOTAService.queryDeviceOtaBySerialNumber(sn)).thenReturn(new DeviceOTADO() {{
            setStatus(1);
            setTargetFirmware("1.2.4");
        }});
        when(deviceSdCardStatusService.querySdCard(sn)).thenReturn(new SdCard() {{
            setUsed(100);
            setFree(900);
            setTotal(1000);
        }});
        when(deviceInfoService.getDeviceFirmwareBuilder(sn)).thenReturn(OpenApiUtil.shortUUID());
        when(deviceManualService.getDeviceManualBySerialNumber(sn)).thenReturn(new DeviceManualDO() {{
            setUserSn("userSn_" + sn);
            setDisplayModelNo("CG123-Display");
            setModelNo(modelNo);
            setOriginalModelNo("CG123");
            setWiredMacAddress("***************");
            setMacAddress("***************");
            setMcuNumber(OpenApiUtil.shortUUID());
        }});
        when(deviceModelIconService.queryDeviceModelIcon(modelNo)).thenAnswer(it -> "icon_" + it.getArgument(0));
        when(deviceModelConfigService.queryDeviceModelConfig(sn)).thenReturn(new DeviceModel() {{
            setSupportMotionTrack(true);
            setCanStandby(true);
        }});
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(new CloudDeviceSupport(){{
            setSupportCanStandby(1);
            setPirSensitivityOptions(Arrays.asList("high"));
        }});
    }

    @Test
    public void test_createDeviceModifiableAttributes() {

    }


}
