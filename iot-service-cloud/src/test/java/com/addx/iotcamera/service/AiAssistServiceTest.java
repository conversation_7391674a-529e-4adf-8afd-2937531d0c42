package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.config.device.MessageNotificationConfig;
import com.addx.iotcamera.config.device.ModelAiEventConfig;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.entity.DeviceAiSwitch;
import org.addx.iot.domain.extension.entity.EventObjectSwitch;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AiAssistServiceTest {

    @InjectMocks
    private AiAssistService aiAssistService;
    @Mock
    private VipService vipService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private DeviceModelEventService deviceModelEventService;
    @Mock
    private DeviceAiSettingsService deviceAiSettingsService;
    @Mock
    private DeviceService deviceService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private MessageNotificationSettingsService messageNotificationSettingsService;
    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private MessageNotificationConfig messageNotificationConfig;
    @Mock
    private ModelAiEventConfig modelAiEventConfig;

    @PostConstruct
    public void init() {
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(new DeviceDO());
    }

    @Test
    public void test_queryEventObjectSwitch() {
        when(deviceManualService.getModelNoBySerialNumber("sn1")).thenReturn("modelNo1");
        when(deviceModelEventService.queryDeviceModelEvent("modelNo1")).thenReturn(new LinkedHashSet<>(Arrays.asList(
                AiObjectEnum.PERSON.getObjectName(), AiObjectEnum.PET.getObjectName(), AiObjectEnum.VEHICLE.getObjectName(), AiObjectEnum.PACKAGE.getObjectName()
        )));
        when(deviceAiSettingsService.queryEnableEventObjects(123, "sn1")).thenReturn(new LinkedHashSet<>(Arrays.asList(
                AiObjectEnum.PERSON, AiObjectEnum.PET, AiObjectEnum.VEHICLE, AiObjectEnum.PACKAGE
        )));
        CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(cloudDeviceSupport);
        {
            when(vipService.isVipDevice(123,"sn1")).thenReturn(true);
            final Result<DeviceAiSwitch> result = aiAssistService.queryEventObjectSwitch(123, "sn1");
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        cloudDeviceSupport.setSupportPersonAi(true);
        cloudDeviceSupport.setSupportPetAi(true);
        {
            when(vipService.isVipDevice(123,"sn1")).thenReturn(true);
            final Result<DeviceAiSwitch> result = aiAssistService.queryEventObjectSwitch(123, "sn1");
            Assert.assertEquals(Result.successFlag, result.getResult());
            Set<String> eventObjects = result.getData().getList().stream().map(EventObjectSwitch::getEventObject).collect(Collectors.toSet());
            Assert.assertTrue(eventObjects.contains(AiObjectEnum.PERSON.getObjectName()));
            Assert.assertTrue(eventObjects.contains(AiObjectEnum.PET.getObjectName()));
        }
        {
            cloudDeviceSupport.setSupportPetAi(false);
            when(vipService.isVipDevice(123,"sn1")).thenReturn(false);
            final Result<DeviceAiSwitch> result = aiAssistService.queryEventObjectSwitch(123, "sn1");
            Assert.assertEquals(Result.successFlag, result.getResult());
            Set<String> eventObjects = result.getData().getList().stream().map(EventObjectSwitch::getEventObject).collect(Collectors.toSet());
            Assert.assertTrue(eventObjects.contains(AiObjectEnum.PERSON.getObjectName()));
            Assert.assertTrue(!eventObjects.contains(AiObjectEnum.PET.getObjectName()));
        }
    }



    @Test
    public void test_updateEventObjectSwitch() throws Exception {
        Result result = aiAssistService.updateEventObjectSwitch(0, null, null);
        Assert.assertTrue(ObjectUtils.equals(result.getResult(), 0));

        List<EventObjectSwitch> eventObjectSwitchList = new LinkedList<>();

        EventObjectSwitch eventObjectSwitch = new EventObjectSwitch();
        eventObjectSwitch.setEventObject(AiObjectEnum.PERSON.getObjectName());
        eventObjectSwitch.setChecked(true);
        eventObjectSwitchList.add(eventObjectSwitch);
        EventObjectSwitch eventObjectSwitch2 = new EventObjectSwitch();
        eventObjectSwitch2.setEventObject(AiObjectEnum.PET.getObjectName());
        eventObjectSwitch2.setChecked(true);
        eventObjectSwitchList.add(eventObjectSwitch2);
        result = aiAssistService.updateEventObjectSwitch(0, null, eventObjectSwitchList);
        Assert.assertTrue(ObjectUtils.equals(result.getResult(), ResultCollection.DEVICE_NO_EXIT.getCode()));

        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("modelNo");

        when(deviceAiSettingsService.queryEnableEventObjects(any(), any())).thenReturn(new HashSet<AiObjectEnum>(){{
            add(AiObjectEnum.PET);
        }});

        when(messageNotificationSettingsService.queryMessageNotificationSetting(any(), any())).thenReturn(new MessageNotificationSetting(){{
            setEventObjects(AiObjectEnum.PERSON.getObjectName());
            setPackageEventType("");
        }});

        when(deviceModelEventService.queryDeviceModelEvent(any())).thenReturn(new HashSet<String>(){{
            add(AiObjectEnum.PERSON.getObjectName());
        }});

        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(new CloudDeviceSupport() {{
            setSupportPersonAi(true);
        }});
        when(messageNotificationConfig.getChildEvent2ParentEvent()).thenReturn(Collections.emptyMap());
        when(deviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO());

        result = aiAssistService.updateEventObjectSwitch(0, null, eventObjectSwitchList);
        Assert.assertTrue(ObjectUtils.equals(result.getResult(), 0));

        eventObjectSwitch.setChecked(false);
        when(deviceAiSettingsService.queryEnableEventObjects(any(), any())).thenReturn(new HashSet<AiObjectEnum>(){{
            add(AiObjectEnum.PERSON);
        }});
        result = aiAssistService.updateEventObjectSwitch(0, null, eventObjectSwitchList);
        Assert.assertTrue(ObjectUtils.equals(result.getResult(), 0));

        when(deviceAiSettingsService.queryEnableEventObjects(any(), any())).thenReturn(new HashSet<AiObjectEnum>(){{
            add(AiObjectEnum.PET);
        }});
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(new CloudDeviceSupport() {{
            setSupportPetAi(true);
        }});
        result = aiAssistService.updateEventObjectSwitch(0, null, eventObjectSwitchList);
        Assert.assertTrue(ObjectUtils.equals(result.getResult(), 0));

        eventObjectSwitch2.setChecked(false);
        result = aiAssistService.updateEventObjectSwitch(0, null, eventObjectSwitchList);
        Assert.assertTrue(ObjectUtils.equals(result.getResult(), 0));
    } 

    @Test
    public void test_triggerAiEdgeSettingUpdate() throws Exception {
        DeviceAppSettingsDO deviceAppSettingsDO = new DeviceAppSettingsDO();
        when(deviceSettingService.getDeviceSetting(any())).thenReturn(deviceAppSettingsDO);

        aiAssistService.triggerAiEdgeSettingUpdate(0, null, null, null);
        Mockito.verify(deviceSettingService, Mockito.never()).updateUserConfig(any(), any(), anyLong(), anyBoolean());

        aiAssistService.triggerAiEdgeSettingUpdate(0, null, true, true);
        Mockito.verify(deviceSettingService, Mockito.atLeastOnce()).updateUserConfig(any(), any(), anyLong(), anyBoolean());
    }

    @Test
    public void test_triggerAiEdgeSettingUpdateFromDeviceSupport() throws Exception {
        DeviceAppSettingsDO deviceAppSettingsDO = new DeviceAppSettingsDO();
        deviceAppSettingsDO.setReportPersonAi(false);
        deviceAppSettingsDO.setReportPetAi(false);
        when(deviceSettingService.getDeviceSetting(any())).thenReturn(deviceAppSettingsDO);

        Set<AiObjectEnum> enabledEventObjects = new HashSet<>();
        when(deviceAiSettingsService.queryEnableEventObjects(any(), any())).thenReturn(enabledEventObjects);

        CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        aiAssistService.triggerAiEdgeSettingUpdateFromDeviceSupport(0, null, cloudDeviceSupport);
        Mockito.verify(deviceSettingService, Mockito.never()).updateUserConfig(any(), any(), anyLong(), anyBoolean());

        cloudDeviceSupport.setSupportPersonAi(true);
        cloudDeviceSupport.setSupportPetAi(true);
        aiAssistService.triggerAiEdgeSettingUpdateFromDeviceSupport(0, null, cloudDeviceSupport);
        Mockito.verify(deviceSettingService, Mockito.never()).updateUserConfig(any(), any(), anyLong(), anyBoolean());

        enabledEventObjects.add(AiObjectEnum.PERSON);
        enabledEventObjects.add(AiObjectEnum.PET);
        aiAssistService.triggerAiEdgeSettingUpdateFromDeviceSupport(0, null, cloudDeviceSupport);
        Mockito.verify(deviceSettingService, Mockito.atLeastOnce()).updateUserConfig(any(), any(), anyLong(), anyBoolean());
    }
}
