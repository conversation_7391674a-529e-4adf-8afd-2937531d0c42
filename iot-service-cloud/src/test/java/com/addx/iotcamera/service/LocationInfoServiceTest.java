package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.LocationRequest;
import com.addx.iotcamera.bean.domain.LocationDO;
import com.addx.iotcamera.dao.ILocationDAO;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

/**
 * description: location test
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2023/7/18 10:41
 */
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LocationInfoServiceTest {
    @InjectMocks
    private LocationInfoService locationInfoService;
    @Mock
    private ILocationDAO locationDAO;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Test
    public void testDeleteLocation() {
        when(locationDAO.queryLocationByIds(any())).thenReturn(null);
        int userId = 123;
        int otherUser = 456;
        Result result = locationInfoService.deleteLocation(userId, new LocationRequest());
        Result actualResult = Result.Error(INVALID_PARAMS, "INVALID_PARAMS");
        assertEquals(actualResult.getResult(), result.getResult());

        LocationDO locationDO = new LocationDO();
        locationDO.setAdminId(otherUser);
        when(locationDAO.queryLocationByIds(any())).thenReturn(new ArrayList<LocationDO>() {{
            add(locationDO);
        }});
        result = locationInfoService.deleteLocation(userId, new LocationRequest());
        assertEquals(actualResult.getResult(), result.getResult());

        locationDO.setAdminId(userId);
        when(deviceInfoService.queryDeviceByLocationId(anyInt(),anyInt())).thenReturn(new ArrayList<>());
        LocationRequest req = new LocationRequest();
        req.setId(1);
        result = locationInfoService.deleteLocation(userId, req);

    }
}
