package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.domain.device.DeviceApplicationDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.mqtt.cmd.MechanicalDingDongAuditionCmd;
import com.addx.iotcamera.config.RedisExpiredConfig;
import com.addx.iotcamera.config.apollo.DeviceDebugConfig;
import com.addx.iotcamera.config.device.DeviceApplicationConfig;
import com.addx.iotcamera.dao.device.IDeviceSettingDAO;
import com.addx.iotcamera.enums.NightThresholdLevelEnums;
import com.addx.iotcamera.enums.NightVisionSensitivityEnums;
import com.addx.iotcamera.helper.DeviceOperationHelper;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.requests.SetRetainParamRequest;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.openapi.PaasTenantConfigService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.thingmodel.ThingModel;
import org.addx.iot.common.thingmodel.ThingModelConfig;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.*;

import static org.addx.iot.common.enums.ResultCollection.DEVICE_NO_RESPONSE;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceSettingServiceTest {

    @InjectMocks
    @Spy
    private DeviceSettingService deviceSettingService;

    @Mock
    private DeviceBackendConfigService deviceBackendConfigService;

    @Mock
    private DeviceApplicationConfig deviceApplicationConfig;

    @Mock
    private DynamicModelConfigService dynamicModelConfigService;
    @Mock
    private DynamicModelConfigV2Service dynamicModelConfigV2Service;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private RedisService redisService;

    @Mock
    private DeviceOperationHelper deviceOperationHelper;

    @Mock
    private DeviceDebugConfig deviceDebugConfig;

    @Mock
    private DeviceDormancyPlanService deviceDormancyPlanService;

    @Mock
    private RedisExpiredConfig redisExpiredConfig;

    @Mock
    private IDeviceSettingDAO iDeviceSettingDAO;

    @Mock
    private MqttSender mqttSender;

    @Mock
    OpenApiConfigService openApiConfigService;
    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private DeviceModelConfigService deviceModelConfigService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private PaasTenantConfigService paasTenantConfigService;

    @PostConstruct
    public void init() {
        when(paasTenantConfigService.getPaasTenantInfoBySn(any())).thenReturn(null);
    }

    @Test
    public void test_initSetParameterRequest() {
        when(deviceApplicationConfig.getConfig()).thenReturn(new DeviceApplicationDO() {{
            setDeviceStatusInterval(1);
        }});
        when(dynamicModelConfigService.queryDynamicSettingFields(any())).thenReturn(new LinkedHashMap<>());
        when(dynamicModelConfigV2Service.queryDynamicSettingFields(any())).thenReturn(new LinkedHashMap<>());
        when(deviceDebugConfig.getMtu()).thenReturn(null);
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(1);
        SetRetainParamRequest setRetainParamRequest = deviceSettingService.initSetParameterRequest(
                UUID.randomUUID().toString(),
                DeviceSettingsDO.builder()
                        .serialNumber("sn_01")
                        .doorBellRingKey(1)
                        .liveAudioToggleOn(true)
                        .recordingAudioToggleOn(true)
                        .liveSpeakerVolume(100)
                        .alarmWhenRemoveToggleOn(false)
                        .mechanicalDingDongSwitch(1)
                        .mechanicalDingDongDuration(100)
                        .otaAutoUpgrade(true)
                        .floodlightSchedulePlan("")
                        .motionFloodlightTime("30s")
                        .motionTriggeredFloodlightSwitch(true)
                        .floodlightScheduleSwitch(true)
                        .video12HourSwitch(true)
                        .build(),
                DeviceSettingsDO.builder()
                        .serialNumber("sn_01")
                        .pir(1)
                        .pirSirenDuration(1)
                        .irThreshold(1)
                        .needAlarm(1)
                        .needVideo(1)
                        .antiflicker(1)
                        .antiflickerSwitch(1)
                        .floodlightMode("auto")
                        .video12HourSwitch(true)
                        .build(),
                null);
        Assert.assertTrue(setRetainParamRequest != null && setRetainParamRequest.getValue() != null);
        setRetainParamRequest = deviceSettingService.initSetParameterRequest(
                UUID.randomUUID().toString(),
                DeviceSettingsDO.builder()
                        .serialNumber("sn_01")
                        .build(),
                DeviceSettingsDO.builder()
                        .serialNumber("sn_01")
                        .doorBellRingKey(1)
                        .liveAudioToggleOn(true)
                        .recordingAudioToggleOn(true)
                        .liveSpeakerVolume(100)
                        .alarmWhenRemoveToggleOn(false)
                        .pir(1)
                        .pirSirenDuration(1)
                        .irThreshold(1)
                        .needAlarm(1)
                        .needVideo(1)
                        .antiflicker(1)
                        .antiflickerSwitch(1)
                        .mechanicalDingDongDuration(100)
                        .mechanicalDingDongSwitch(1)
                        .otaAutoUpgrade(false)
                        .floodlightSchedulePlan("")
                        .motionFloodlightTime("30s")
                        .motionTriggeredFloodlightSwitch(true)
                        .floodlightScheduleSwitch(true)
                        .build(),
                null);
        Assert.assertTrue(setRetainParamRequest != null && setRetainParamRequest.getValue() != null);
        // 充电自动开机
        DeviceSettingsDO settings = DeviceSettingsDO.builder().serialNumber("sn_04").build();
        DeviceSettingsDO storeSettings = DeviceSettingsDO.builder().serialNumber("sn_05").doorBellRingKey(1)
                .liveAudioToggleOn(true).recordingAudioToggleOn(true).liveSpeakerVolume(100)
                .alarmWhenRemoveToggleOn(false).pir(1).pirSirenDuration(1).irThreshold(1)
                .needAlarm(1).needVideo(1).antiflicker(1).antiflickerSwitch(1)
                .build();
        settings.setChargeAutoPowerOnCapacity(1);
        settings.setChargeAutoPowerOnSwitch(1);
        storeSettings.setChargeAutoPowerOnCapacity(null);
        storeSettings.setChargeAutoPowerOnSwitch(null);
        settings.setSdCardCooldownSwitch(1);
        settings.setSdCardCooldownSeconds("160s");
        settings.setSdCardVideoMode("eventual");
        storeSettings.setSdCardCooldownSwitch(null);
        storeSettings.setSdCardCooldownSeconds(null);
        storeSettings.setSdCardVideoMode(null);
        Map<String, Object> map = new LinkedHashMap<>();
        setRetainParamRequest = deviceSettingService.initSetParameterRequest(UUID.randomUUID().toString(), settings, storeSettings, map);
        Assert.assertTrue(setRetainParamRequest != null && setRetainParamRequest.getValue() != null);

        settings.setChargeAutoPowerOnCapacity(null);
        settings.setChargeAutoPowerOnSwitch(null);
        storeSettings.setChargeAutoPowerOnCapacity(1);
        storeSettings.setChargeAutoPowerOnSwitch(1);
        settings.setSdCardCooldownSwitch(null);
        settings.setSdCardCooldownSeconds(null);
        settings.setSdCardVideoMode(null);
        storeSettings.setSdCardCooldownSwitch(1);
        storeSettings.setSdCardCooldownSeconds("150s");
        storeSettings.setSdCardVideoMode("eventual");
        map.put("other_field1", "a");
        map.put("other_field2", Arrays.asList("b", "c"));
        setRetainParamRequest = deviceSettingService.initSetParameterRequest(UUID.randomUUID().toString(), settings, storeSettings, map);
        Assert.assertTrue(setRetainParamRequest != null && setRetainParamRequest.getValue() != null);


    }

    @Test
    @DisplayName("未唤醒设备")
    public void sendDingdongMessage_Nowake() {
        doNothing().when(deviceInfoService).wakeUpDevice(any(), any());
        doNothing().when(redisService).setDeviceOperationDOWithEmpty(any());

        when(deviceOperationHelper.waitDeviceWake(any())).thenReturn(false);
        Result expectedResult = ResultCollection.getResult(DEVICE_NO_RESPONSE.getCode());
        Result actualResult = deviceSettingService.mechanicalDingDongAudition(any());
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("未唤醒设备")
    public void sendDingdongMessage() {
        doNothing().when(deviceInfoService).wakeUpDevice(any(), any());
        doNothing().when(redisService).setDeviceOperationDOWithEmpty(any());

        when(deviceOperationHelper.waitDeviceWake(any())).thenReturn(true);
        String serialNumber = "serialNumber";
        // 试听机械叮咚
        MechanicalDingDongAuditionCmd cmd = new MechanicalDingDongAuditionCmd(serialNumber);
        redisService.setDeviceOperationDOWithEmpty(cmd.getId());
        when(deviceOperationHelper.waitOperation(any())).thenReturn(ResultCollection.DEVICE_NO_RESPONSE.getResult());

        Result expectedResult = ResultCollection.getResult(DEVICE_NO_RESPONSE.getCode());
        Result actualResult = deviceSettingService.mechanicalDingDongAudition(any());

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void test_initAntiflicker() {
        {
            DeviceSettingsDO settings = new DeviceSettingsDO();
            settings.setSerialNumber(OpenApiUtil.shortUUID());
            settings.setAntiflicker(1);
            settings.setAntiflickerSwitch(1);
            when(deviceInfoService.getDeviceSupport(settings.getSerialNumber())).thenReturn(null);
            deviceSettingService.initAntiflicker(settings);
            assertEquals(new Integer(1), settings.getAntiflicker());
            assertEquals(new Integer(1), settings.getAntiflickerSwitch());
        }
        {
            DeviceSettingsDO settings = new DeviceSettingsDO();
            settings.setSerialNumber(OpenApiUtil.shortUUID());
            settings.setAntiflicker(1);
            settings.setAntiflickerSwitch(1);
            when(deviceInfoService.getDeviceSupport(settings.getSerialNumber())).thenReturn(new CloudDeviceSupport() {{
                setAntiflickerSupport(0);
            }});
            deviceSettingService.initAntiflicker(settings);
            assertEquals(null, settings.getAntiflicker());
            assertEquals(null, settings.getAntiflickerSwitch());
        }
        {
            DeviceSettingsDO settings = new DeviceSettingsDO();
            settings.setSerialNumber(OpenApiUtil.shortUUID());
            settings.setAntiflicker(1);
            settings.setAntiflickerSwitch(1);
            when(deviceInfoService.getDeviceSupport(settings.getSerialNumber())).thenReturn(new CloudDeviceSupport() {{
                setAntiflickerSupport(1);
            }});
            deviceSettingService.initAntiflicker(settings);
            assertEquals(new Integer(1), settings.getAntiflicker());
            assertEquals(new Integer(1), settings.getAntiflickerSwitch());
        }
    }

    @Test
    public void test_updateUserConfig() throws Exception {
        when(deviceApplicationConfig.getConfig()).thenReturn(new DeviceApplicationDO() {{
            setDeviceStatusInterval(1);
        }});

        VernemqPublisher vernemqPublisher = new VernemqPublisher();
        Field mqttSenderField = VernemqPublisher.class.getDeclaredField("mqttSender");
        mqttSenderField.setAccessible(true);
        mqttSenderField.set(vernemqPublisher, mqttSender);
        vernemqPublisher.init();

        when(iDeviceSettingDAO.getDeviceSettingsBySerialNumber(any())).thenReturn(DeviceSettingsDO.builder().pir(0).irThreshold(0).antiflicker(0).antiflickerSwitch(0).needAlarm(0).needVideo(0).build());

        when(deviceDormancyPlanService.checkDeviceDormancyStatus(any())).thenReturn(true);

        when(iDeviceSettingDAO.initDeviceSettings(any())).thenReturn(1);

        when(iDeviceSettingDAO.updateDeviceSettings(any())).thenReturn(1);

        doNothing().when(redisService).set(any(), any(), any());

        Result result = deviceSettingService.updateUserConfig(1, new DeviceAppSettingsDO() {{
            setSerialNumber("sn_01");
        }}, 0, false);
//        Assert.assertTrue(Objects.equal(result.getResult(), ResultCollection.DEVICE_DORMANCY_STATUS.getCode()) );

        result = deviceSettingService.updateUserConfig(1, new DeviceAppSettingsDO() {{
            setSerialNumber("sn_01");
        }}, 0, false, false);
        Assert.assertTrue(Objects.equal(result.getResult(), ResultCollection.SUCCESS.getCode()));

        mqttSenderField.set(vernemqPublisher, null);
        vernemqPublisher.init();

    }

    @Test
    public void test_updateDoorBellRingKeyInitValue() {
        when(iDeviceSettingDAO.updateDoorBellRingKeyInitValue(any(), any())).thenReturn(1);
        Assert.assertEquals(1, deviceSettingService.updateDoorBellRingKeyInitValue("sn", 3));
    }


    @Test
    public void testProcessSettingNullValueNightVisionSensitivityLow() {
        DeviceSettingsDO deviceSettingsDO = new DeviceSettingsDO();
        {
            deviceSettingsDO.setNightVisionSensitivity(-1);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightVisionSensitivityEnums.LOW.getCode(), deviceSettingsDO.getNightVisionSensitivity());
        }
        {
            deviceSettingsDO.setNightVisionSensitivity(2);
            deviceSettingsDO.setNightThresholdLevel(-1);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightThresholdLevelEnums.LOW.getCode(), deviceSettingsDO.getNightThresholdLevel());
        }
        {
            deviceSettingsDO.setIrThreshold(null);
            deviceSettingsDO.setNightThresholdLevel(0);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightVisionSensitivityEnums.CLOSE.getCode(), deviceSettingsDO.getIrThreshold());
        }
        {
            deviceSettingsDO.setIrThreshold(null);
            deviceSettingsDO.setNightThresholdLevel(1);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightVisionSensitivityEnums.LOW.getCode(), deviceSettingsDO.getIrThreshold());
        }
        {
            deviceSettingsDO.setIrThreshold(null);
            deviceSettingsDO.setNightThresholdLevel(2);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightVisionSensitivityEnums.MIDDLE.getCode(), deviceSettingsDO.getIrThreshold());
        }
        {
            deviceSettingsDO.setIrThreshold(null);
            deviceSettingsDO.setNightThresholdLevel(3);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightVisionSensitivityEnums.HIGHT.getCode(), deviceSettingsDO.getIrThreshold());
        }


        {
            deviceSettingsDO.setNightVisionSensitivity(null);
            deviceSettingsDO.setNightThresholdLevel(0);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightVisionSensitivityEnums.CLOSE.getCode(), deviceSettingsDO.getNightVisionSensitivity());
        }
        {
            deviceSettingsDO.setNightVisionSensitivity(null);
            deviceSettingsDO.setNightThresholdLevel(1);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightVisionSensitivityEnums.LOW.getCode(), deviceSettingsDO.getNightVisionSensitivity());
        }
        {
            deviceSettingsDO.setNightVisionSensitivity(null);
            deviceSettingsDO.setNightThresholdLevel(2);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightVisionSensitivityEnums.MIDDLE.getCode(), deviceSettingsDO.getNightVisionSensitivity());
        }
        {
            deviceSettingsDO.setNightVisionSensitivity(null);
            deviceSettingsDO.setNightThresholdLevel(3);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightVisionSensitivityEnums.HIGHT.getCode(), deviceSettingsDO.getNightVisionSensitivity());
        }

        {
            deviceSettingsDO.setPirSirenDuration(0);
            deviceSettingsDO.setNeedAlarm(null);

            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(0L, (long) deviceSettingsDO.getNeedAlarm());
        }

        {
            deviceSettingsDO.setRecLen(0);
            deviceSettingsDO.setNeedVideo(null);

            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(0L, (long) deviceSettingsDO.getNeedVideo());
        }

        {
            deviceSettingsDO.setIrThreshold(8);
            deviceSettingsDO.setNightVisionSensitivity(null);
            deviceSettingsDO.setNightThresholdLevel(null);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightThresholdLevelEnums.LOW.getCode(), deviceSettingsDO.getNightVisionSensitivity());
        }
        {
            deviceSettingsDO.setIrThreshold(20);
            deviceSettingsDO.setNightVisionSensitivity(null);
            deviceSettingsDO.setNightThresholdLevel(null);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightThresholdLevelEnums.MIDDLE.getCode(), deviceSettingsDO.getNightVisionSensitivity());
        }
        {
            deviceSettingsDO.setIrThreshold(50);
            deviceSettingsDO.setNightVisionSensitivity(null);
            deviceSettingsDO.setNightThresholdLevel(null);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(NightThresholdLevelEnums.HIGHT.getCode(), deviceSettingsDO.getNightVisionSensitivity());
        }

        {
            deviceSettingsDO.setPir(1);
            deviceSettingsDO.setMotionSensitivity(null);
            deviceSettingService.processSettingNullValue(deviceSettingsDO);
            assertEquals(1L, (long) deviceSettingsDO.getMotionSensitivity());
        }
    }

    @Test(expected = BaseException.class)
    public void testProcessSettingNullValueInvalidMotionSensitivity() {
        DeviceSettingsDO deviceSettingsDO = new DeviceSettingsDO();
        deviceSettingsDO.setMotionSensitivity(-1);
        deviceSettingService.processSettingNullValue(deviceSettingsDO);
    }

    @Test(expected = BaseException.class)
    public void testProcessSettingNullValueInvalidAlarmDuration() {
        DeviceSettingsDO deviceSettingsDO = new DeviceSettingsDO();
        deviceSettingService.processSettingNullValue(deviceSettingsDO);
    }

    @Test
    public void test_getDeviceMtuValue() {
        Map<String, Integer> map = new HashMap<>();
        map.put("sn", 1000);
        when(deviceDebugConfig.getMtu()).thenReturn(map);
        deviceSettingService.getDeviceMtuValue("sn");
        DeviceModel model = new DeviceModel();
        model.setMtu(1200);
        when(deviceManualService.getModelNoBySerialNumber("sn2")).thenReturn("model");
        when(deviceModelConfigService.queryRowDeviceModelByModelNo("model")).thenReturn(model);
        Integer mtu = deviceSettingService.getDeviceMtuValue("sn2");
        assertEquals(1200L, (long) mtu);

    }

    @Test
    public void test_isStoreCameraLocal() {
        ThingModel thingModel = new ThingModelConfig().thingModel();
        CloudDeviceSupport support = new CloudDeviceSupport();
        support.setSupportJson(new JSONObject());
        DeviceSettingsDO setting = new DeviceSettingsDO();
        setting.setPropertyJson(new JSONObject());
        {
            Assert.assertFalse(DeviceSettingsDO.isStoreCameraLocal(null, null, null));
        }
        {
            Assert.assertFalse(DeviceSettingsDO.isStoreCameraLocal(thingModel, support, setting));
        }
        {
            support.getSupportJson().put("supportStorage", 0);
            setting.getPropertyJson().put("storeSwitch", 0);
            Assert.assertFalse(DeviceSettingsDO.isStoreCameraLocal(thingModel, support, setting));
        }
        {
            support.getSupportJson().put("supportStorage", 2);
            setting.getPropertyJson().put("storeSwitch", 2);
            Assert.assertFalse(DeviceSettingsDO.isStoreCameraLocal(thingModel, support, setting));
        }
        {
            support.getSupportJson().put("supportStorage", 3);
            setting.getPropertyJson().put("storeSwitch", 2);
            Assert.assertFalse(DeviceSettingsDO.isStoreCameraLocal(thingModel, support, setting));
        }
        {
            support.getSupportJson().put("supportStorage", 2);
            setting.getPropertyJson().put("storeSwitch", 3);
            Assert.assertFalse(DeviceSettingsDO.isStoreCameraLocal(thingModel, support, setting));
        }
        {
            support.getSupportJson().put("supportStorage", 1);
            setting.getPropertyJson().put("storeSwitch", 1);
            Assert.assertTrue(DeviceSettingsDO.isStoreCameraLocal(thingModel, support, setting));
        }
        {
            support.getSupportJson().put("supportStorage", 3);
            setting.getPropertyJson().put("storeSwitch", 3);
            Assert.assertTrue(DeviceSettingsDO.isStoreCameraLocal(thingModel, support, setting));
        }
        {
            support.getSupportJson().put("supportStorage", 1);
            setting.getPropertyJson().put("storeSwitch", 3);
            Assert.assertTrue(DeviceSettingsDO.isStoreCameraLocal(thingModel, support, setting));
        }
        {
            support.getSupportJson().put("supportStorage", 3);
            setting.getPropertyJson().put("storeSwitch", 1);
            Assert.assertTrue(DeviceSettingsDO.isStoreCameraLocal(thingModel, support, setting));
        }

    }

}
