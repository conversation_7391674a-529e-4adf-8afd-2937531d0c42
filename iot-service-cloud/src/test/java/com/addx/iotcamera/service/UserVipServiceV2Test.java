package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.additional_tier.AdditionalUserTierInfo;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.AppUserConfig;
import com.addx.iotcamera.config.TenantTierNameConfig;
import com.addx.iotcamera.config.TierReminderConfig;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.config.app.AppColorConfig;
import com.addx.iotcamera.config.app.TierInfoConfig;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.vip.TierGroupService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.service.xinge.PushXingeService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.util.*;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class UserVipServiceV2Test {

    @InjectMocks
    private UserVipService userVipService;
    @Mock
    private IUserVipDAO iUserVipDAO;
    
    @Mock
    private TierInfoConfig tierInfoConfig;
    @Mock
    private TierService tierService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private UserService userService;
    @Mock
    private PushService pushMessage;
    @Mock
    private RedisService redisService;
    @Mock
    private PaymentService paymentService;
    @Mock
    private IOrderDAO iOrderDAO;
    @Mock
    private CenterNotifyConfig centerNotifyConfig;
    @Mock
    private AppUserConfig appUserConfig;
    //    @Value("${openReceiveVip:false}")
//    private boolean openReceiveVip;
    @Mock
    private TenantTierNameConfig tenantTierNameConfig;
    @Mock
    private PushXingeService pushXingeService;
    @Mock
    private PushInfoService pushInfoService;
    @Mock
    private TierReminderConfig tierReminderConfig;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private AppColorConfig appColorConfig;
    @Mock
    private UserTierDeviceService userTierDeviceService;
    @Mock
    private AdditionalUserTierService additionalUserTierService;
    @Mock
    private TierGroupService tierGroupService;

    private Integer userId = new Random().nextInt();
    private String sn = "unittest_" + OpenApiUtil.shortUUID();
    private long millis = System.currentTimeMillis();
    private int seconds = (int) (millis / 1000);

    @Before
    public void init() {
        LinkedHashMap<String, Object> langMap = new LinkedHashMap<String, Object>() {
            @Override
            public Object get(Object lang) {
                Map<String, String> notifyMessageMap = new LinkedHashMap<>();
                notifyMessageMap.put("freeMessage", "abcdef");
                return notifyMessageMap;
            }
        };
        LinkedHashMap<String, Object> tenantIdMap = new LinkedHashMap<String, Object>() {
            @Override
            public Object get(Object key) {
                return langMap;
            }
        };
        when(centerNotifyConfig.getMessage()).thenAnswer(it -> tenantIdMap);

        when(tierService.queryTierById(any())).thenReturn(new Tier());

    }

    @Test
    public void test_queryCurrentTierLookBackDays() {
        UserVipDO userVip1 = UserVipDO.builder().userId(userId)
                .tierId(1)
                .build();
        UserVipDO userVip2 = UserVipDO.builder().userId(userId)
                .tierId(2)
                .build();
        List<UserVipDO> notEndVipList = Arrays.asList(userVip1, userVip2);
        UserVipDO lastEndVip = UserVipDO.builder().userId(userId)
                .tierId(3)
                .build();
        whenQueryTierById(userVip2, 15);
        when(iUserVipDAO.queryUserVipInfo(userId, seconds, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Arrays.asList());
        when(iUserVipDAO.queryUserVipInfoBefor(userId, seconds)).thenReturn(null);
        { // 无套餐
            whenQueryTierById(userVip1, null);
            int days = userVipService.queryCurrentTierLookBackDays(userId, millis);
            Assert.assertEquals(7, days);
        }
        when(iUserVipDAO.queryUserVipInfoBefor(userId, seconds)).thenReturn(lastEndVip);
        { // 有套餐变无套餐，>=3天
            whenQueryTierById(userVip1, null);
            lastEndVip.setEndTime(seconds - 3 * 24 * 60 * 60);
            whenQueryTierById(lastEndVip, 60);
            int days = userVipService.queryCurrentTierLookBackDays(userId, millis);
            Assert.assertEquals(7, days);
        }
        when(iUserVipDAO.queryUserVipInfo(userId, seconds, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(notEndVipList);
        when(iUserVipDAO.queryUserVipInfoBefor(userId, seconds)).thenReturn(lastEndVip);
        { // 无套餐
            whenQueryTierById(userVip1, null);
            int days = userVipService.queryCurrentTierLookBackDays(userId, millis);
            Assert.assertEquals(7, days);
        }
        { // 无过期vip
            whenQueryTierById(userVip1, 30);
            whenQueryTierById(lastEndVip, null);
            int days = userVipService.queryCurrentTierLookBackDays(userId, millis);
            Assert.assertEquals(30, days);
        }
        { // 低级变高级
            whenQueryTierById(userVip1, 30);
            whenQueryTierById(lastEndVip, 15);
            int days = userVipService.queryCurrentTierLookBackDays(userId, millis);
            Assert.assertEquals(30, days);
        }
        { // 高级变低级，>=3天
            whenQueryTierById(userVip1, 30);
            whenQueryTierById(lastEndVip, 60);
            lastEndVip.setEndTime(seconds - 3 * 24 * 60 * 60);
            int days = userVipService.queryCurrentTierLookBackDays(userId, millis);
            Assert.assertEquals(30, days);
        }
        userVipService.queryCurrentTierTime(userId, millis);
    }

    private void whenQueryTierById(UserVipDO userVip, Integer rollingDays) {
        if (rollingDays == null) {
            when(tierService.queryTierById(userVip.getTierId())).thenReturn(null);
        } else {
            Tier tier = new Tier();
            tier.setTierId(userVip.getTierId());
            tier.setRollingDays(rollingDays);
            when(tierService.queryTierById(userVip.getTierId())).thenReturn(tier);
        }
    }

    @Test
    public void test_getVideoRollingDays() {
        String tenantId = "vicoo";
        String serialNumber = "d251ccab75ee9c83e153bcb2554b06a7";
        Integer userId = 5407;
        // '2022-06-20 06:45:43','2024-06-19 06:45:43','2658','5407','10','1655707543','1718779543','1655707543','0','9c078f2a-7d72-4498-9066-4d4165d3f071','1'
        UserVipDO userVip = UserVipDO.builder()
                .tierId(10)
                .build();
        when(iUserVipDAO.queryUserVipInfo(userId, seconds, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Arrays.asList(userVip));
        when(iUserVipDAO.queryUserVipInfoBefor(userId, seconds)).thenReturn(userVip);
        // '10','7','1073741824','0','1','0'
        Tier tier = new Tier();
        tier.setTierId(10);
        tier.setRollingDays(7);
        tier.setStorage(1073741824L);
        tier.setLevel(0);
        when(tierService.queryTierById(userVip.getTierId())).thenReturn(tier);
        int days = userVipService.queryCurrentTierLookBackDays(userId, millis);
        Assert.assertEquals(7, days);
    }

//    @Test
//    public void test_queryUserVipInfo() {
//        queryUserVipInfo();
//    }

    public void queryUserVipInfo() {
        Integer userId = 123566;
        String lang = "en";
        when(appColorConfig.queryAppColor(any())).thenReturn("blue");
        final int month = 3600 * 24 * 30;
        UserVipDO userVip1 = UserVipDO.builder().tierId(1)
                .effectiveTime(seconds - month * 2)
                .endTime(seconds - month * 1).build();
        UserVipDO userVip2 = UserVipDO.builder().tierId(1)
                .effectiveTime(seconds - month * 1)
                .endTime(seconds + month * 1).build();
        List<UserVipDO> userVipList = Arrays.asList(userVip1, userVip2);


        when(iUserVipDAO.queryUserVipInfo(userId, seconds, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(userVipList);
        {
            userVipService.setOpenReceiveVip(true);
            UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, lang, "tenantId");
            Assert.assertNotNull(userVipTier);
        }
//        {
//            //when(redisService.get(any())).thenReturn("false");
//            userVipService.setOpenReceiveVip(false);
//            when(userService.queryUserById(userId)).thenReturn(null);
//            Throwable error = null;
//            try {
//                userVipService.queryUserVipInfo(userId, lang, "tenantId");
//            } catch (Throwable e) {
//                error = e;
//                log.error("queryUserVipInfo error",e);
//            }
//            Assert.assertNotNull(error);
//        }
        userVipService.setOpenReceiveVip(false);
        User user = new User();
        user.setId(userId);
        user.setEmail(userId + "@email.com");
        user.setPhone(userId + "");
        when(userService.queryUserById(userId)).thenReturn(user);
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(1);
        {
            when(redisService.get(any())).thenReturn("");
            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(Arrays.asList());
            UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, lang, "tenantId");
            Assert.assertNotNull(userVipTier);
        }
        {
            when(redisService.get(any())).thenReturn("");
            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(Arrays.asList(sn));
            UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, lang, "tenantId");
            Assert.assertNotNull(userVipTier);
        }
        {
            when(redisService.get(any())).thenReturn((seconds - month * 1) + ":" + 1);
            when(tierReminderConfig.getConfig()).thenReturn(new LinkedHashMap<>());
            UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, lang, "tenantId");
            Assert.assertNotNull(userVipTier);
        }
        when(tierReminderConfig.getConfig()).thenReturn(new LinkedHashMap<Integer, Integer>() {
            @Override
            public boolean containsKey(Object key) {
                return true;
            }

            @Override
            public Integer get(Object key) {
                return month * 1;
            }
        });
        {
            when(redisService.get(any())).thenReturn((seconds - month * 1.5) + ":" + 1);
            UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, lang, "tenantId");
            Assert.assertNotNull(userVipTier);
        }
        {
            when(redisService.get(any())).thenReturn((seconds - month * 0.5) + ":" + 1);
            UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, lang, "tenantId");
            Assert.assertNotNull(userVipTier);
        }

    }

    @Test
    @DisplayName("查询用户身份-订阅用户过期情况")
    public void test_queryUserVipInfo(){
        Integer userId = 123566;
        String lang = "en";
        String tenantId = "tenantId";

        when(appColorConfig.queryAppColor(any())).thenReturn("blue");
        final int month = 3600 * 24 * 30;


        when(iUserVipDAO.queryUserVipInfo(userId, seconds, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Lists.newArrayList());
        userVipService.setOpenReceiveVip(true);



        UserVipTier expectedResult = UserVipTier.builder()
                .vip(false)
                .build();
        UserVipTier actualResult = userVipService.queryUserVipInfo(userId,lang,tenantId);
        Assert.assertEquals(expectedResult.isVip(),actualResult.isVip());
    }

    @Test
    public void test_queryUserVipInfo_additionTiers() {
        when(tierGroupService.sortUserVipDO(any())).then((Answer<List<UserVipDO>>) invocation -> invocation.getArgument(0));
        when(userTierDeviceService.hasActiveFreeLicenceDeviceVip(any(),any())).thenReturn(false);
        {
            when(additionalUserTierService.getActiveAdditionalUserTierInfo(any(), any(), any())).thenReturn(Arrays.asList());
            queryUserVipInfo();
        }
        AdditionalUserTierInfo additionTier = new AdditionalUserTierInfo();
        additionTier.setType(1);
        {
            additionTier.setExpirationMessage(null);
            when(additionalUserTierService.getActiveAdditionalUserTierInfo(any(), any(), any()))
                    .thenReturn(Arrays.asList(additionTier));
            queryUserVipInfo();
        }
        additionTier.setExpirationMessage("expirationMessage1");
        additionTier.setTierUid("tierUid1");
        queryUserVipInfo();
    }

    @Test
    public void test_queryFreeTierLookBackDays() {
        {
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenReturn(Arrays.asList());
            int days = userVipService.queryFreeTierLookBackDays(1234, "",System.currentTimeMillis());
            Assert.assertEquals(7, days);
        }
        {
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenThrow(new RuntimeException());
            int days = userVipService.queryFreeTierLookBackDays(1234,"", System.currentTimeMillis());
            Assert.assertEquals(7, days);
        }

    }

    @Test
    public void test_userVipReceiveTag(){
        doNothing().when(redisService).set(any(),any(),any());
        userVipService.userVipReceiveTag(1,true);
        verify(redisService, times(1)).set(any(), any(),any());

    }
}
