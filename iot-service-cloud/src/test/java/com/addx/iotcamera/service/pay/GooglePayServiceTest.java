package com.addx.iotcamera.service.pay;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.payment.GoogleOrderResult;
import com.addx.iotcamera.bean.app.payment.GooglePaymentRequest;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.domain.pay.GoogleCancelOrderDO;
import com.addx.iotcamera.bean.domain.pay.OrderVerifyResultDO;
import com.addx.iotcamera.bean.domain.pay.PaymentConfig;
import com.addx.iotcamera.config.pay.PaymentCenterConfig;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.redis.PayRedis;
import com.addx.iotcamera.enums.PaymentFlagEnums;
import com.addx.iotcamera.enums.PaymentTypeEnums;
import com.addx.iotcamera.enums.ProductTypeEnums;
import com.addx.iotcamera.service.PaymentService;
import com.addx.iotcamera.service.ProductService;
import com.addx.iotcamera.service.vip.OrderService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class GooglePayServiceTest {
    @InjectMocks
    private GooglePayService googlePayService;

    @Mock
    private PaymentCenterConfig paymentCenterConfig;

    @Mock
    private PayRedis payRedis;

    @Mock
    private ProductService productService;

    @Mock
    private PaymentService paymentService;

    @Mock
    private IOrderDAO iOrderDAO;

    @Mock
    private OrderService orderService;

    @Test
    @DisplayName("获取token 为空")
    public void test_googleOrderVerify_null(){
        GooglePaymentRequest request = this.initGooglePaymentRequest();

        Map<String, PaymentConfig> config = new HashMap<>();
        config.put("guard",new PaymentConfig());
        when(paymentCenterConfig.getConfig()).thenReturn(config);
        when(payRedis.get(any())).thenReturn(null);

        OrderVerifyResultDO expectResult = OrderVerifyResultDO.builder()
                .verify(false)
                .build();
        OrderVerifyResultDO actualResult = googlePayService.googleOrderVerify(request);
        Assert.assertEquals(expectResult.getVerify(),actualResult.getVerify());
    }

    @Test
    @DisplayName("商品不存在")
    public void test_googleOrderVerify_product_null(){
        GooglePaymentRequest request = this.initGooglePaymentRequest();

        Map<String, PaymentConfig> config = new HashMap<>();
        PaymentConfig paymentConfig = new PaymentConfig();
        paymentConfig.setGooglePackage("package");

        config.put("guard",paymentConfig);

        when(paymentCenterConfig.getConfig()).thenReturn(config);
        when(payRedis.get(any())).thenReturn("token");
        when(productService.queryProductById(any())).thenReturn(null);
        {
            request.setProductId(1);
            request.setSubscriptionGroupId("");
            OrderVerifyResultDO expectResult = OrderVerifyResultDO.builder()
                    .verify(false)
                    .build();
            OrderVerifyResultDO actualResult = googlePayService.googleOrderVerify(request);
            Assert.assertEquals(expectResult.getVerify(),actualResult.getVerify());
        }
        {
            request.setProductId(0);
            request.setSubscriptionGroupId("group");
            OrderVerifyResultDO expectResult = OrderVerifyResultDO.builder()
                    .verify(false)
                    .build();
            OrderVerifyResultDO actualResult = googlePayService.googleOrderVerify(request);
            Assert.assertEquals(expectResult.getVerify(),actualResult.getVerify());
        }
    }


    @Test
    @DisplayName("订单信息不存在")
    public void test_googleOrderVerify_verifyResult_null(){
        GooglePaymentRequest request = this.initGooglePaymentRequest();

        Map<String, PaymentConfig> config = new HashMap<>();
        PaymentConfig paymentConfig = new PaymentConfig();
        paymentConfig.setGoogleOrderUrl("url");
        paymentConfig.setGooglePublisher("url");
        paymentConfig.setGoogleCilentSecret("secret");
        paymentConfig.setGoogleClientId("clientid");
        paymentConfig.setGoogleRefreshToken("refreshtoken");
        paymentConfig.setGooglePackage("package");

        config.put("guard",paymentConfig);
        when(paymentCenterConfig.getConfig()).thenReturn(config);
        when(payRedis.get(any())).thenReturn("token");

        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.SUBSCRIBE.getCode());

        {
            when(productService.queryProductById(any())).thenReturn(productDO);
            OrderVerifyResultDO expectResult = OrderVerifyResultDO.builder()
                    .verify(false)
                    .build();
            OrderVerifyResultDO actualResult = googlePayService.googleOrderVerify(request);
            Assert.assertEquals(expectResult.getVerify(),actualResult.getVerify());
        }
        {
            productDO.setType(ProductTypeEnums.PURCHASE.getCode());
            when(productService.queryProductById(any())).thenReturn(productDO);

            OrderVerifyResultDO expectResult = OrderVerifyResultDO.builder()
                    .verify(false)
                    .build();
            OrderVerifyResultDO actualResult = googlePayService.googleOrderVerify(request);
            Assert.assertEquals(expectResult.getVerify(),actualResult.getVerify());
        }

    }


    @Test
    @DisplayName("订阅订单,不符合条件")
    public void test_verifyOrderResult_subscribe_noverify(){
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.SUBSCRIBE.getCode());

        //谷歌订单结构
        GoogleOrderResult googleOrderResult = this.initGoogleOrderResult(System.currentTimeMillis()-10*1000,0);

        OrderVerifyResultDO exceptResult = OrderVerifyResultDO.builder()
                .verify(false)
                .build();
        OrderVerifyResultDO actualResult = googlePayService.verifyOrderResult(JSON.toJSONString(googleOrderResult),productDO,"", "");
        Assert.assertEquals(exceptResult.getVerify(),actualResult.getVerify());
    }

    @Test
    @DisplayName("订阅订单,符合条件-支付")
    public void test_verifyOrderResult_subscribe_pay(){
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.SUBSCRIBE.getCode());

        //谷歌订单结构
        GoogleOrderResult googleOrderResult = this.initGoogleOrderResult(System.currentTimeMillis()+10*1000,1);

        OrderVerifyResultDO exceptResult = OrderVerifyResultDO.builder()
                .verify(true)
                .freeTrial(0)
                .build();
        OrderVerifyResultDO actualResult = googlePayService.verifyOrderResult(JSON.toJSONString(googleOrderResult),productDO,"", "");
        Assert.assertEquals(exceptResult.getVerify(),actualResult.getVerify());
    }


    @Test
    @DisplayName("订阅订单,符合条件-支付-首月免费")
    public void test_verifyOrderResult_subscribe_freeTrial(){
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.SUBSCRIBE.getCode());

        //谷歌订单结构
        GoogleOrderResult googleOrderResult = this.initGoogleOrderResult(System.currentTimeMillis()+10*1000,2);

        OrderVerifyResultDO exceptResult = OrderVerifyResultDO.builder()
                .verify(true)
                .freeTrial(1)
                .build();
        OrderVerifyResultDO actualResult = googlePayService.verifyOrderResult(JSON.toJSONString(googleOrderResult),productDO,"","");
        Assert.assertEquals(exceptResult.getVerify(),actualResult.getVerify());
    }

    @Test
    @DisplayName("订阅订单,符合条件-购买-无购买属性")
    public void test_verifyOrderResult_notsubscribe_consume(){
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PURCHASE.getCode());

        //谷歌订单结构
        GoogleOrderResult googleOrderResult = this.initGoogleOrderResult(System.currentTimeMillis()+10*1000,2);

        OrderVerifyResultDO exceptResult = OrderVerifyResultDO.builder()
                .verify(false)
                .build();

        JSONObject obj = JSONObject.parseObject(JSON.toJSONString(googleOrderResult));

        OrderVerifyResultDO actualResult = googlePayService.verifyOrderResult(obj.toJSONString(),productDO,"","");
        Assert.assertEquals(exceptResult.getVerify(),actualResult.getVerify());
    }

    @Test
    @DisplayName("订阅订单,符合条件-购买-已消费")
    public void test_verifyOrderResult_notsubscribe_consumed(){
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PURCHASE.getCode());

        //谷歌订单结构
        GoogleOrderResult googleOrderResult = this.initGoogleOrderResult(System.currentTimeMillis()+10*1000,2);

        OrderVerifyResultDO exceptResult = OrderVerifyResultDO.builder()
                .verify(false)
                .build();

        JSONObject obj = JSONObject.parseObject(JSON.toJSONString(googleOrderResult));
        obj.put("consumptionState",1);
        obj.put("purchaseState",1);
        OrderVerifyResultDO actualResult = googlePayService.verifyOrderResult(obj.toJSONString(),productDO,"","");
        Assert.assertEquals(exceptResult.getVerify(),actualResult.getVerify());
    }

    @Test
    @DisplayName("订阅订单,符合条件-购买-未验证")
    public void test_verifyOrderResult_notsubscribe_noverify(){
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PURCHASE.getCode());

        //谷歌订单结构
        GoogleOrderResult googleOrderResult = this.initGoogleOrderResult(System.currentTimeMillis()+10*1000,2);

        OrderVerifyResultDO exceptResult = OrderVerifyResultDO.builder()
                .verify(true)
                .freeTrial(0)
                .build();

        JSONObject obj = JSONObject.parseObject(JSON.toJSONString(googleOrderResult));
        obj.put("consumptionState",0);
        obj.put("purchaseState",0);
        OrderVerifyResultDO actualResult = googlePayService.verifyOrderResult(obj.toJSONString(),productDO,"","");
        Assert.assertEquals(exceptResult.getVerify(),actualResult.getVerify());
    }

    @Test
    @DisplayName("订阅订单,符合条件-购买-未验证")
    public void test_verifyOrderResult_notsubscribe_tradeNoNotEqual(){
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PURCHASE.getCode());
        String tradeNo = "tradeNo";
        //谷歌订单结构
        GoogleOrderResult googleOrderResult = this.initGoogleOrderResult(System.currentTimeMillis()+10*1000,0,tradeNo);

        OrderVerifyResultDO exceptResult = OrderVerifyResultDO.builder()
                .verify(false)
                .build();

        JSONObject obj = JSONObject.parseObject(JSON.toJSONString(googleOrderResult));
        obj.put("consumptionState",0);
        obj.put("purchaseState",0);
        OrderVerifyResultDO actualResult = googlePayService.verifyOrderResult(obj.toJSONString(),productDO,"test","");
        Assert.assertEquals(exceptResult.getVerify(),actualResult.getVerify());
    }

    @Test
    @DisplayName("订阅订单,符合条件-购买-未验证")
    public void test_verifyOrderResult_notsubscribe_purchaseState(){
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PURCHASE.getCode());
        String tradeNo = "tradeNo";
        //谷歌订单结构
        GoogleOrderResult googleOrderResult = this.initGoogleOrderResult(System.currentTimeMillis()+10*1000,0,tradeNo);

        OrderVerifyResultDO exceptResult = OrderVerifyResultDO.builder()
                .verify(false)
                .build();

        JSONObject obj = JSONObject.parseObject(JSON.toJSONString(googleOrderResult));
        obj.put("consumptionState",0);
        obj.put("purchaseState",1);
        OrderVerifyResultDO actualResult = googlePayService.verifyOrderResult(obj.toJSONString(),productDO,"test","");
        Assert.assertEquals(exceptResult.getVerify(),actualResult.getVerify());
    }

    @Test
    @DisplayName("订阅订单,符合条件-购买-未验证")
    public void test_verifyOrderResult_notsubscribe(){
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PURCHASE.getCode());
        String tradeNo = "tradeNo";
        //谷歌订单结构
        GoogleOrderResult googleOrderResult = this.initGoogleOrderResult(System.currentTimeMillis()+10*1000,1,tradeNo);

        OrderVerifyResultDO exceptResult = OrderVerifyResultDO.builder()
                .verify(true)
                .freeTrial(0)
                .build();

        JSONObject obj = JSONObject.parseObject(JSON.toJSONString(googleOrderResult));
        obj.put("consumptionState",0);
        obj.put("purchaseState",0);

        OrderVerifyResultDO actualResult = googlePayService.verifyOrderResult(obj.toJSONString(),productDO,tradeNo,"");
        Assert.assertEquals(exceptResult.getVerify(),actualResult.getVerify());
    }

    private GooglePaymentRequest initGooglePaymentRequest(){
        GooglePaymentRequest request = new GooglePaymentRequest();
        request.setProductId(0);
        request.setSubscriptionGroupId("");
        AppInfo app = new AppInfo();
        app.setTenantId("guard");
        app.setAppBuild("guard");
        request.setApp(app);

        request.setPurchaseToken("purchaseToken");
        return request;
    }

    private GoogleOrderResult initGoogleOrderResult(long currentTime,int paymentState){
        return this.initGoogleOrderResult(currentTime,paymentState,"");
    }
    private GoogleOrderResult initGoogleOrderResult(long currentTime,int paymentState,String tradeNo){
        GoogleOrderResult googleOrderResult = new GoogleOrderResult();
        googleOrderResult.setExpiryTimeMillis(currentTime);
        googleOrderResult.setStartTimeMillis(currentTime);
        googleOrderResult.setPaymentState(paymentState);
        googleOrderResult.setOrderId(tradeNo);
        return googleOrderResult;
    }


    @Test
    @DisplayName("payconfig token empty")
    public void test_queryGoogleOrderInfo_null(){
        String tenantId = "vicoo";
        Map<String, PaymentConfig> config = this.initPaymentConfig();
        when(paymentCenterConfig.getConfig()).thenReturn(config);
        when(payRedis.get(any())).thenReturn(null);
        String expectedResult = "";
        String actualResult = googlePayService.queryGoogleOrderInfo("package","tradeNo","token",tenantId);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("payconfig token ")
    public void test_queryGoogleOrderInfo_(){
        String tenantId = "vicoo";
        Map<String, PaymentConfig> config = this.initPaymentConfig();
        when(paymentCenterConfig.getConfig()).thenReturn(config);

        when(payRedis.get(any())).thenReturn("token");

        when(payRedis.get(any())).thenReturn(null);
        String expectedResult = "";
        String actualResult = googlePayService.queryGoogleOrderInfo("package","tradeNo","token",tenantId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    private Map<String, PaymentConfig> initPaymentConfig(){
        Map<String, PaymentConfig> config = Maps.newHashMap();
        PaymentConfig paymentConfig = new PaymentConfig();
        config.put("vicoo",paymentConfig);
        return config;
    }


    @Test
    @DisplayName("验证首月免费-订单信息为空")
    public void test_queryGooglePublisherFreeTrial_order_empty(){
        doNothing().when(paymentService).sysReportPaymentError(null, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.NOTIFY.getCode());
        Boolean expectedResult = false;
        Boolean actualResult = googlePayService.queryGooglePublisherFreeTrial("");
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("验证首月免费-订单信息为空")
    public void test_queryGooglePublisherFreeTrial_paymentState(){
        Boolean expectedResult = true;
        String orderInfo = this.strGoogleOrderResult(1);
        Boolean actualResult = googlePayService.queryGooglePublisherFreeTrial(orderInfo);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("验证首月免费-订单信息为空")
    public void test_queryGooglePublisherFreeTrial_order_null(){
        Boolean expectedResult = true;
        String orderInfo = this.strGoogleOrderResult(2);
        when(iOrderDAO.queryBytradeNo(any())).thenReturn(null);
        Boolean actualResult = googlePayService.queryGooglePublisherFreeTrial(orderInfo);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("验证首月免费-订单流水为空")
    public void test_queryGooglePublisherFreeTrial_paymentFlow_null(){
        Boolean expectedResult = true;
        String orderInfo = this.strGoogleOrderResult(2);
        when(iOrderDAO.queryBytradeNo(any())).thenReturn(new OrderDO());
        when(paymentService.queryPaymentFlow(any())).thenReturn(null);
        Boolean actualResult = googlePayService.queryGooglePublisherFreeTrial(orderInfo);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("验证首月免费")
    public void test_queryGooglePublisherFreeTrial(){
        Boolean expectedResult = true;
        String orderInfo = this.strGoogleOrderResult(2);
        when(iOrderDAO.queryBytradeNo(any())).thenReturn(new OrderDO());
        when(paymentService.queryPaymentFlow(any())).thenReturn(new PaymentFlow());
        doNothing().when(paymentService).updateRefundInfo(any());
        Boolean actualResult = googlePayService.queryGooglePublisherFreeTrial(orderInfo);
        Assert.assertEquals(expectedResult,actualResult);
    }

    private String strGoogleOrderResult(int paymentState){
        GoogleOrderResult googleOrderResult = new GoogleOrderResult();
        googleOrderResult.setPaymentState(paymentState);
        googleOrderResult.setOrderId("orderId");

        return JSON.toJSONString(googleOrderResult);
    }


    @Test
    public void test_refundOrder_nocancel(){
        GoogleOrderResult googleOrderResult = new GoogleOrderResult();
        Boolean expectedResult = false;
        Boolean actualResult = googlePayService.refundOrder(googleOrderResult);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("不再续订")
    public void test_refundOrder(){
        GoogleOrderResult googleOrderResult = new GoogleOrderResult();
        googleOrderResult.setOrderId("orderId");

        googleOrderResult.setAutoRenewing(false);
        googleOrderResult.setCancelReason(1);
        googleOrderResult.setUserCancellationTimeMillis(1000000L);
        when(orderService.notifyGoogleOrder(any())).thenReturn(true);

        Boolean expectedResult = true;
        Boolean actualResult = googlePayService.refundOrder(googleOrderResult);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("继续续订")
    public void test_refundOrder_autorenewing(){
        GoogleOrderResult googleOrderResult = new GoogleOrderResult();
        googleOrderResult.setOrderId("orderId");

        googleOrderResult.setAutoRenewing(true);
        googleOrderResult.setCancelReason(1);
        when(orderService.notifyGoogleOrder(any())).thenReturn(true);

        Boolean expectedResult = true;
        Boolean actualResult = googlePayService.refundOrder(googleOrderResult);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    public void test_queryCancelOrderList_paymentConfig_null(){
        when(paymentCenterConfig.getConfig()).thenReturn(Maps.newHashMap());
        List<GoogleCancelOrderDO.VoidedPurchaseDO> expectedResult = Lists.newArrayList();
        List<GoogleCancelOrderDO.VoidedPurchaseDO> actualResult = googlePayService.queryCancelOrderList(TENANTID_VICOO);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    public void test_queryCancelOrderList_paymentConfig_accessToken_null(){
        Map<String,PaymentConfig> paymentConfigMap = Maps.newHashMap();
        PaymentConfig config = new PaymentConfig();
        paymentConfigMap.put(TENANTID_VICOO,config);

        when(paymentCenterConfig.getConfig()).thenReturn(paymentConfigMap);
        when(payRedis.get(any())).thenReturn("");
        List<GoogleCancelOrderDO.VoidedPurchaseDO> expectedResult = Lists.newArrayList();
        List<GoogleCancelOrderDO.VoidedPurchaseDO> actualResult = googlePayService.queryCancelOrderList(TENANTID_VICOO);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    public void test_queryCancelOrderList_paymentConfig_accessToken(){
        Map<String,PaymentConfig> paymentConfigMap = Maps.newHashMap();
        PaymentConfig config = new PaymentConfig();
        paymentConfigMap.put(TENANTID_VICOO,config);

        when(paymentCenterConfig.getConfig()).thenReturn(paymentConfigMap);
        when(payRedis.get(any())).thenReturn("token");
        List<GoogleCancelOrderDO.VoidedPurchaseDO> expectedResult = Lists.newArrayList();
        List<GoogleCancelOrderDO.VoidedPurchaseDO> actualResult = googlePayService.queryCancelOrderList(TENANTID_VICOO);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("解析订单中指定的设备list-有设备")
    public void testQueryUserTierDeviceRequestWithValidInput() {
        String extend = "{\"tierDeviceList\":[\"iPhone 12\",\"iPad Pro\"]}";
        List<String> expectedOutput = Arrays.asList("iPhone 12", "iPad Pro");
        List<String> actualOutput = googlePayService.queryUserTierDeviceRequest(extend);
        Assert.assertEquals(expectedOutput, actualOutput);
    }

    @Test
    @DisplayName("解析订单中指定的设备list-无设备")
    public void testQueryUserTierDeviceRequestWithEmptyInput() {
        String extend = "";
        List<String> expectedOutput = Lists.newArrayList();
        List<String> actualOutput = googlePayService.queryUserTierDeviceRequest(extend);
        Assert.assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void test_queryGoogleOrderExpireTime(){
        GooglePaymentRequest request = new GooglePaymentRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        request.setApp(app);

        PaymentConfig paymentConfig = new PaymentConfig();
        Map<String,PaymentConfig> map = Maps.newHashMap();
        map.put(TENANTID_VICOO,paymentConfig);
        when(paymentCenterConfig.getConfig()).thenReturn(map);
        when(payRedis.get(any())).thenReturn(null);

        Integer expetedResult = null;
        Integer actualResult = googlePayService.queryGoogleOrderExpireTime(request);
        Assert.assertEquals(expetedResult,actualResult);
    }


    @Test
    public void test_subscriptionOrderCancel(){
        String productId = "cloud_premium_group";
        String bundle = "com.smartaddx.vicohome";
        String purchaseToken = "ekpblnadflignbijaegefmac.AO-J1OxNaaV6snJcsr9uhAqilD0RS0CPUHcAZCaJ-NweLKzvoC2e1kG1Y1RYnZ7gBPShet5BE7uYOTFq3sWCMJlN_Bok-7Lr0LXQAXvEecSUQRLgHkzX4ww";

        Assert.assertFalse(googlePayService.subscriptionOrderCancel(String.valueOf(productId),purchaseToken,bundle));
    }

    @Test
    public void test_queryProductIdBySubscriptionGroup(){
        String appPackage = "appPackage";
        String purchaseToken = "purchaseToken";
        String accessToken = "accessToken";

        Integer expectedResult = 0;
        Integer actualResult = 0;
        {
            actualResult = googlePayService.queryProductIdBySubscriptionGroup(appPackage,purchaseToken,accessToken);
            Assert.assertEquals(expectedResult,actualResult);
        }
    }
}
