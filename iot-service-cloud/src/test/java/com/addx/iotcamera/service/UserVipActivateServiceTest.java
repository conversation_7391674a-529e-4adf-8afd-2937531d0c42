package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.device.attributes.DeviceEnumAttributes;
import com.addx.iotcamera.bean.device.attributes.DeviceModifiableAttribute;
import com.addx.iotcamera.bean.device.attributes.DeviceSwitchAttributes;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.uservip.UserDeviceFreeTierDO;
import com.addx.iotcamera.bean.openapi.PaasTenantInfo;
import com.addx.iotcamera.config.device.FreeUserVipTier2Config;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.constants.DeviceAttributeName;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.service.device.DeviceAttributeService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.vip.FreeLicenseService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.Instant;
import java.util.*;

import static com.addx.iotcamera.bean.domain.AppFormOptionsDO.CooldownOptionValue.*;
import static com.addx.iotcamera.constants.PayConstants.FREE_LICENSE_7_DAY;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.Mockito.*;


@RunWith(PowerMockRunner.class)
@PrepareForTest(FreeUserVipTier2Config.class)
public class UserVipActivateServiceTest {

    @InjectMocks
    private UserVipActivateService userVipActivateService;

    @Mock
    private UserVipService userVipService;

    @Mock
    private OpenApiConfigService deviceThirdConfigService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private UserService userService;

    @Mock
    private TierService tierService;

    @Mock
    private IUserVipDAO iUserVipDAO;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private DeviceAttributeService deviceAttributeService;

    @Mock
    private UserSettingService userSettingService;

    @Mock
    private DeviceSettingService deviceSettingService;

    @Mock
    private UserTierDeviceService userTierDeviceService;

    @Mock
    private PaasTenantConfig paasTenantConfig;

    @Mock
    private MqSender mqSender;

    @Mock
    private RedisService redisService;

    @Mock
    private FreeLicenseService freeLicenseService;

    @Before
    public void init() throws Exception {
        userVipActivateService.setUserVipActivateServiceEnabled(true);
        userVipActivateService.afterPropertiesSet();
        when(paasTenantConfig.getPaasTenantInfo(anyString())).thenReturn(PaasTenantInfo.DEFAULT_INSTANCE);
        when(tierService.queryTierById(any())).thenReturn(new Tier() {{
            setTierId(500);
            setRollingDays(7);
            setStorage(1232332L);
        }});
    }

    @Test
    public void testCreateFreeUserTier() {
        Integer tierId = 100;
        Integer userId = 1;
        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt(),any())).thenReturn(Collections.emptyList());
        when(iUserVipDAO.insertUserVip(any())).thenReturn(1);
        when(userService.queryTenantIdById(any())).thenReturn(TENANTID_VICOO);

        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
        userVipTier.setSubTierListGroup(Maps.newHashMap());
        when(userVipService.queryUserVipInfo(any(),any(),any())).thenReturn(userVipTier);
        doNothing().when(userTierDeviceService).refreshUserDeviceTierCache(userId,userVipTier);
        {
            when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
            boolean createResult = userVipActivateService.createFreeUserTier(userId, 10);
            Assert.assertTrue(createResult);
        }
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(0).build());
        {
            // freeTier 1
            boolean createResult = userVipActivateService.createFreeUserTier(userId, 10);
            Assert.assertTrue(createResult);
        }
        {
            // freeTier 2
            boolean createResult = userVipActivateService.createFreeUserTier(userId, 100);
            Assert.assertTrue(createResult);
        }
    }
//
//    @Test
//    @DisplayName("标记用户userVip 用户userVip list empty ")
//    public void testActivateUserVip_empty() {
//        Integer userId = 1;
//        List<UserVipDO> userVipDOList = Lists.newArrayList();
//        doNothing().when(userTierDeviceService).refreshUserDeviceTierCache(any(),any());
//        doNothing().when(userVipActivateService).sendTierParam2DeviceConfig(any(),any());
//        userVipActivateService.activateUserVip(userId,userVipDOList);
//        verify(userVipActivateService, times(0)).sendTierParam2DeviceConfig(any(),any());
//    }
//    @Test
//    @DisplayName("标记用户userVip ，已标记")
//    public void testActivateUserVip_exist() {
//        List<UserVipDO> userVipDOList = Arrays.asList(UserVipDO.builder().tierId(2).active(true).build());
//        when(tierService.queryTierLevelById(any())).thenReturn(2);
//
//        doNothing().when(userVipService).unmarkNoUserTier(any());
//        doNothing().when(userTierDeviceService).refreshUserDeviceTierCache(any(),any());
//        doNothing().when(userVipActivateService).sendTierParam2DeviceConfig(any(),any());
//
//        userVipActivateService.activateUserVip(1,userVipDOList);
//        verify(userVipActivateService, times(1)).sendTierParam2DeviceConfig(any(),any());
//    }
//
//    @Test
//    @DisplayName("标记用户userVip ")
//    public void testActivateUserVip() {
//        Integer currentTime = (int)Instant.now().getEpochSecond();
//        List<UserVipDO> userVipDOList = Arrays.asList(
//                UserVipDO.builder()
//                        .tierId(2)
//                        .active(false)
//                        .effectiveTime(currentTime - 100)
//                        .endTime(currentTime + 100)
//                        .build(),
//                UserVipDO.builder()
//                        .tierId(1)
//                        .active(true)
//                        .effectiveTime(0)
//                        .endTime(0)
//                        .build()
//        );
//        when(tierService.queryTierLevelById(1)).thenReturn(1);
//        when(tierService.queryTierLevelById(2)).thenReturn(2);
//
//        userVipActivateService.activateUserVip(1,userVipDOList);
//
//        when(iUserVipDAO.updateUserVipActive(any())).thenReturn(1);
//        when(iUserVipDAO.insertUserVipLog(any())).thenReturn(1);
//        verify(userVipActivateService, times(1)).sendTierParam2DeviceConfig(any(),any());
//    }
//    @Test
//    public void testDeactivateUserVip() {
//        when(userRoleService.getUserRoleByUserId(anyInt())).thenReturn(Collections.emptyList());
//
//        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt())).thenReturn(Collections.emptyList());
//        //userVipActivateService.deactivateUserVip(1, 2);
//
//        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt())).thenReturn(new LinkedList(){{
//            add(UserVipDO.builder().tierId(2).active(true).effectiveTime(0).endTime(Integer.MAX_VALUE).build());
//            add(UserVipDO.builder().tierId(2).active(false).effectiveTime(Integer.MAX_VALUE).endTime(0).build());
//            add(UserVipDO.builder().tierId(1).active(true).effectiveTime(0).endTime(Integer.MAX_VALUE).build());
//            add(UserVipDO.builder().tierId(1).active(false).effectiveTime(Integer.MAX_VALUE).endTime(0).build());
//        }});
//        //userVipActivateService.deactivateUserVip(1, 2);
//
//        when(userRoleService.getUserRoleByUserId(anyInt())).thenReturn(Collections.singletonList(UserRoleDO.builder().serialNumber("sn_01").build()));
//        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt())).thenReturn(Collections.emptyList());
//        //userVipActivateService.deactivateUserVip(1, 2);
//
//        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt())).thenReturn(new LinkedList(){{
//            add(UserVipDO.builder().tierId(2).active(true).effectiveTime(0).endTime(Integer.MAX_VALUE).build());
//            add(UserVipDO.builder().tierId(2).active(false).effectiveTime(Integer.MAX_VALUE).endTime(0).build());
//            add(UserVipDO.builder().tierId(1).active(true).effectiveTime(0).endTime(Integer.MAX_VALUE).build());
//            add(UserVipDO.builder().tierId(1).active(false).effectiveTime(Integer.MAX_VALUE).endTime(0).build());
//        }});
//        //userVipActivateService.deactivateUserVip(1, 2);
//    }
//
    @Test
    @DisplayName("可以查询用户list")
    public void testExecuteUserVipActivateAndDeactivate() {
        when(redisService.getFromSlave(any())).thenReturn(null);
        doNothing().when(mqSender).send(any(),anyInt(),any());

        {
            when(userService.selectAllValidUser(anyInt())).thenReturn(Lists.newArrayList());
            Integer exceptResult = 0;
            Integer actualResult = userVipActivateService.executeUserVipActivateAndDeactivate();
            Assert.assertEquals(exceptResult,actualResult);
        }
        {
            when(userService.selectAllValidUser(anyInt())).then((Answer<List<User>>) invocation -> {
                Integer startUserId = (Integer)invocation.getArgument(0);
                if(startUserId == 0) {
                    return Collections.singletonList(new User(){{
                        setId(1);
                        setTenantId("longse");
                    }});
                }
                return Collections.emptyList();
            });
            Integer exceptResult = 1;
            Integer actualResult = userVipActivateService.executeUserVipActivateAndDeactivate();
            Assert.assertEquals(exceptResult,actualResult);
        }
    }

    @Test
    @DisplayName("更新userVip激活状态-当前生效vip为空")
    public void executeActivateUserVip_empty(){
        UserVipTier userVipTier = new UserVipTier();
        Integer userId = 1;
        {
            when(freeLicenseService.queryUserFreeTierIdExpire(any())).thenReturn(Lists.newArrayList());
            when(userVipService.hasUserVipActive(any())).thenReturn(false);
            when(userVipService.isNoUserTier(any())).thenReturn(false);
            doNothing().when(userTierDeviceService).refreshUserDeviceTierCache(any(),any());
            doNothing().when(userVipService).markNoUserTier(any());
            userVipActivateService.executeActivateUserVip(userVipTier,userId);
        }
        {
            when(freeLicenseService.queryUserFreeTierIdExpire(any())).thenReturn(Lists.newArrayList());
            when(userVipService.hasUserVipActive(any())).thenReturn(true);

            userVipTier.setSubTierListGroup(Maps.newHashMap());
            userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
            doNothing().when(userTierDeviceService).refreshUserDeviceTierCache(any(),any());
            userVipActivateService.executeActivateUserVip(userVipTier,userId);
        }
        {
            when(freeLicenseService.queryUserFreeTierIdExpire(any())).thenReturn(Arrays.asList(UserDeviceFreeTierDO.builder().tierId(FREE_LICENSE_7_DAY).endTime((int)Instant.now().getEpochSecond() - 10 * 60 * 60).build()));
            when( userService.queryUserById(any())).thenReturn(User.builder().tenantId(TENANTID_VICOO).build());
            doNothing().when(userTierDeviceService).deleteUserTierDevice(any(),any(),any(),any());


            when(userVipService.hasUserVipActive(any())).thenReturn(true);

            userVipTier.setSubTierListGroup(Maps.newHashMap());
            userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
            doNothing().when(userTierDeviceService).refreshUserDeviceTierCache(any(),any());
            userVipActivateService.executeActivateUserVip(userVipTier,userId);
        }
    }
//
//    @Test
//    @DisplayName("更新userVip激活状态-当前生效vip为空")
//    public void executeActivateUserVip(){
//        Map<Integer,List<UserVipDO>> subTierListGroup = Maps.newHashMap();
//        subTierListGroup.put(1,Lists.newArrayList());
//        UserVipTier userVipTier = UserVipTier.builder()
//                .tierIdList(Arrays.asList(1))
//                .subTierListGroup(subTierListGroup)
//                .build();
//        userVipActivateService.executeActivateUserVip(userVipTier,1);
//
//        verify(userTierDeviceService, times(0)).refreshUserDeviceTierCache(1,null,userVipTier);
//    }
//
//    @Test
//    @DisplayName("取消生效标记-empty")
//    public void executeDeactivateUserVip_test_empty(){
//
//        UserVipTier userVipTier = new UserVipTier();
//        Map<Integer,UserVipDO> activeUserVipTierDOMap = Maps.newHashMap();
//        userVipActivateService.executeDeactivateUserVip(userVipTier,activeUserVipTierDOMap);
//        verify(userVipActivateService, times(0)).deactivateUserVip(any());
//    }
//
//
//    @Test
//    @DisplayName("取消生效标记-跟生效的套餐相同")
//    public void executeDeactivateUserVip_test_contain(){
//        UserVipTier userVipTier = new UserVipTier();
//        userVipTier.setTierIdList(Arrays.asList(1));
//        Map<Integer,UserVipDO> activeUserVipTierDOMap = Maps.newHashMap();
//        activeUserVipTierDOMap.put(1,UserVipDO.builder().build());
//        userVipActivateService.executeDeactivateUserVip(userVipTier,activeUserVipTierDOMap);
//        verify(userVipActivateService, times(0)).deactivateUserVip(any());
//    }
//
//
//    @Test
//    @DisplayName("取消生效标记-跟生效的套餐相同")
//    public void executeDeactivateUserVip_test() {
//        UserVipTier userVipTier = new UserVipTier();
//        userVipTier.setTierIdList(Lists.newArrayList());
//        Map<Integer, UserVipDO> activeUserVipTierDOMap = Maps.newHashMap();
//        activeUserVipTierDOMap.put(1, UserVipDO.builder().build());
//
//        when(iUserVipDAO.updateUserVipActive(any())).thenReturn(1);
//        when(iUserVipDAO.insertUserVipLog(any())).thenReturn(1);
//        userVipActivateService.executeDeactivateUserVip(userVipTier, activeUserVipTierDOMap);
//        verify(userVipActivateService, times(1)).deactivateUserVip(any());
//    }

    @Test
    @DisplayName("未绑定设备")
    public void test_sendTierParam2DeviceConfig_nodevice() throws MqttException, InterruptedException, IdNotSetException {
        when(userRoleService.getUserRoleByUserId(any(),any())).thenReturn(Lists.newArrayList());
        userVipActivateService.sendTierParam2DeviceConfig(1,1);

        verify(deviceSettingService, times(0)).updateUserConfig(1,new DeviceAppSettingsDO(),System.currentTimeMillis(),false,false);
    }

    @Test
    @DisplayName("套餐相同，不刷新")
    public void test_sendTierParam2DeviceConfig_tierEquals() throws MqttException, InterruptedException, IdNotSetException {
        Integer userId = 1;

        UserRoleDO userRoleDO = UserRoleDO.builder()
                .serialNumber("sn")
                .adminId(userId)
                .build();
        when(userRoleService.getUserRoleByUserId(any(),any())).thenReturn(Arrays.asList(userRoleDO));

        when(userTierDeviceService.getDeviceCurrentTier(any(),any())).thenReturn(1);

        userVipActivateService.sendTierParam2DeviceConfig(1,100);

        verify(deviceSettingService, times(0)).updateUserConfig(1,new DeviceAppSettingsDO(),System.currentTimeMillis(),false,false);
    }


    @Test
    @DisplayName("未配置")
    public void test_sendTierParam2DeviceConfig_no_config() throws MqttException, InterruptedException, IdNotSetException {
        Integer userId = 1;
        Integer tierId = 100;
        String modelNo = "modelNo";
        String sn = "sn";
        Integer registTime = 1672531201;
        UserRoleDO userRoleDO = UserRoleDO.builder()
                .serialNumber("sn")
                .adminId(userId)
                .build();
        when(userRoleService.getUserRoleByUserId(any(),any())).thenReturn(Arrays.asList(userRoleDO));

        when(userTierDeviceService.getDeviceCurrentTier(any(),any())).thenReturn(tierId);
        User user = User.builder()
                .registTime(1672531201)
                .build();
        when(userService.queryUserById(any())).thenReturn(user);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("modelNo");

        PowerMockito.mockStatic(FreeUserVipTier2Config.class);
        PowerMockito.when(FreeUserVipTier2Config.getCooldownOptionValueList(tierId, registTime, modelNo, sn)).thenReturn(Lists.newArrayList());

        userVipActivateService.sendTierParam2DeviceConfig(userId,tierId);

        verify(deviceSettingService, times(0)).getDeviceSettingsBySerialNumber(any());
    }


    @Test
    @DisplayName("用户当前设置有效")
    public void test_sendTierParam2DeviceConfig_user() throws MqttException, InterruptedException, IdNotSetException {
        Integer userId = 1;
        Integer tierId = 100;
        String modelNo = "modelNo";
        Integer registTime = 1672531201;
        UserRoleDO userRoleDO = UserRoleDO.builder()
                .serialNumber("sn")
                .adminId(userId)
                .build();
        when(userRoleService.getUserRoleByUserId(any(),any())).thenReturn(Arrays.asList(userRoleDO));

        when(userTierDeviceService.getDeviceCurrentTier(any(),any())).thenReturn(tierId);
        User user = User.builder()
                .registTime(1672531201)
                .build();
        when(userService.queryUserById(any())).thenReturn(user);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("modelNo");

        PowerMockito.mockStatic(FreeUserVipTier2Config.class);

        List<AppFormOptionsDO.CooldownOptionValue> cooldownOptionValues = Arrays.asList(SECONDS_60,SECONDS_180,SECONDS_300);
        PowerMockito.when(FreeUserVipTier2Config.getCooldownOptionValueList(tierId, registTime, modelNo, "sn")).thenReturn(cooldownOptionValues);


        when(deviceSettingService.getDeviceSettingsBySerialNumber(any())).thenReturn(DeviceSettingsDO.builder().cooldownInS(SECONDS_60.getValue()).build());

        userVipActivateService.sendTierParam2DeviceConfig(userId,tierId);

        verify(deviceSettingService, times(0)).updateUserConfig(1,new DeviceAppSettingsDO(),System.currentTimeMillis(),false,false);
    }

    @Test
    @DisplayName("刷新用户设置")
    public void test_sendTierParam2DeviceConfig() throws MqttException, InterruptedException, IdNotSetException {
        Integer userId = 1;
        Integer tierId = 100;
        String modelNo = "modelNo";
        Integer registTime = 1672531201;
        UserRoleDO userRoleDO = UserRoleDO.builder()
                .serialNumber("sn")
                .adminId(userId)
                .build();
        when(userRoleService.getUserRoleByUserId(any(),any())).thenReturn(Arrays.asList(userRoleDO));

        when(userTierDeviceService.getDeviceCurrentTier(any(),any())).thenReturn(tierId);
        User user = User.builder()
                .registTime(1672531201)
                .build();
        when(userService.queryUserById(any())).thenReturn(user);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("modelNo");

        PowerMockito.mockStatic(FreeUserVipTier2Config.class);

        List<AppFormOptionsDO.CooldownOptionValue> cooldownOptionValues = Arrays.asList(SECONDS_60,SECONDS_180,SECONDS_300);
        PowerMockito.when(FreeUserVipTier2Config.getCooldownOptionValueList(tierId, registTime, modelNo, "sn")).thenReturn(cooldownOptionValues);


        when(deviceSettingService.getDeviceSettingsBySerialNumber(any())).thenReturn(DeviceSettingsDO.builder().cooldownInS(SECONDS_10.getValue()).cooldownUserEnable(true).build());

        userVipActivateService.sendTierParam2DeviceConfig(userId,tierId);
        Assert.assertTrue(true);
    }

    @Test
    public void test_autoCorrectCooldown() {
        String sn = OpenApiUtil.shortUUID();
        int adminId = (new Random()).nextInt(100000);
//        when(deviceAttributeService.getAttributeSource(anyString())).thenReturn(new DeviceAttributeService.DeviceAttributeSource());
        {
            when(deviceAttributeService.getPirCooldownSwitchAndTime(anyString())).thenReturn(Arrays.asList());
            userVipActivateService.autoCorrectCooldown(sn, adminId);
        }
        {
            List<DeviceModifiableAttribute> attrList = new LinkedList<>();
            attrList.add(new DeviceSwitchAttributes().setName(DeviceAttributeName.pirCooldownSwitch.name())
                    .setDisabled(true).setValue(true));
            when(deviceAttributeService.getPirCooldownSwitchAndTime(anyString())).thenReturn(attrList);
            userVipActivateService.autoCorrectCooldown(sn, adminId);
        }
        {
            List<DeviceModifiableAttribute> attrList = new LinkedList<>();
            attrList.add(new DeviceSwitchAttributes().setName(DeviceAttributeName.pirCooldownSwitch.name())
                    .setDisabled(true).setValue(false));
            when(deviceAttributeService.getPirCooldownSwitchAndTime(anyString())).thenReturn(attrList);
            userVipActivateService.autoCorrectCooldown(sn, adminId);
        }
        {
            List<DeviceModifiableAttribute> attrList = new LinkedList<>();
            attrList.add(new DeviceSwitchAttributes().setName(DeviceAttributeName.pirCooldownSwitch.name())
                    .setDisabled(false).setValue(false));
            when(deviceAttributeService.getPirCooldownSwitchAndTime(anyString())).thenReturn(attrList);
            userVipActivateService.autoCorrectCooldown(sn, adminId);
        }
        {
            List<DeviceModifiableAttribute> attrList = new LinkedList<>();
            attrList.add(new DeviceSwitchAttributes().setName(DeviceAttributeName.pirCooldownSwitch.name())
                    .setDisabled(false).setValue(true));
            when(deviceAttributeService.getPirCooldownSwitchAndTime(anyString())).thenReturn(attrList);
            userVipActivateService.autoCorrectCooldown(sn, adminId);
        }
        {
            List<DeviceModifiableAttribute> attrList = new LinkedList<>();
            attrList.add(new DeviceEnumAttributes().setName(DeviceAttributeName.pirCooldownTime.name())
                    .setValue("10s")
                    .setOptions(Arrays.asList("10s", "20s"))
                    .setDisabledOptions(Arrays.asList()));
            when(deviceAttributeService.getPirCooldownSwitchAndTime(anyString())).thenReturn(attrList);
            userVipActivateService.autoCorrectCooldown(sn, adminId);
        }
        {
            List<DeviceModifiableAttribute> attrList = new LinkedList<>();
            attrList.add(new DeviceEnumAttributes().setName(DeviceAttributeName.pirCooldownTime.name())
                    .setValue("10s")
                    .setOptions(Arrays.asList("20s"))
                    .setDisabledOptions(Arrays.asList("10s")));
            when(deviceAttributeService.getPirCooldownSwitchAndTime(anyString())).thenReturn(attrList);
            userVipActivateService.autoCorrectCooldown(sn, adminId);
        }
        {
            List<DeviceModifiableAttribute> attrList = new LinkedList<>();
            attrList.add(new DeviceEnumAttributes().setName(DeviceAttributeName.pirCooldownTime.name())
                    .setValue("10s")
                    .setOptions(Arrays.asList())
                    .setDisabledOptions(Arrays.asList("10s")));
            when(deviceAttributeService.getPirCooldownSwitchAndTime(anyString())).thenReturn(attrList);
            userVipActivateService.autoCorrectCooldown(sn, adminId);
        }

    }


    @Test
    public void test_activateUserVipSig(){
        Integer userId = 1;
        UserVipDO actualResult = null;
        {
            actualResult = userVipActivateService.activateUserVipSig(userId,Lists.newArrayList());
            Assert.assertNull(actualResult);
        }
        {
            UserVipDO userVipDO = UserVipDO.builder().tierId(1).active(true).build();
            List<UserVipDO> userVipDOList = Arrays.asList(
                    userVipDO
            );

            when(tierService.queryTierLevelById(any())).thenReturn(1);
            when(tierService.queryTierLevelById(any())).thenReturn(1);
            doNothing().when(userVipService).unmarkNoUserTier(userId);
            actualResult = userVipActivateService.activateUserVipSig(userId,userVipDOList);
            Assert.assertEquals(actualResult,userVipDO);
        }
    }
}
