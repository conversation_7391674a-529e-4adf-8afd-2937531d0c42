package com.addx.iotcamera.service.vip;

import com.addx.iotcamera.bean.domain.Device4GSimDO;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.config.PaasVipConfig;
import com.addx.iotcamera.enums.VipCountType;
import com.addx.iotcamera.service.*;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VipServiceTest {
    @InjectMocks
    private VipService vipService;

    @Mock
    private PlanSupportService planSupportService;
    @Mock
    private PaasVipConfig paasVipConfig;

    @Mock
    private UserVipService userVipService;

    @Mock
    private UserTierDeviceService userTierDeviceService;

    @Mock
    private UserService userService;

    @Before
    public void init() throws Exception {
        when(planSupportService.isNoPlanAndDeviceSupportImageEvent(any(), any())).thenReturn(new Tuple2<>(false, false));
    }

    @Test
    public void test_queryVipCountType(){
        String tenantId = TENANTID_VICOO;
        Integer userId = 1;
        String sn = "sn";

        VipCountType actualResult ;
        {
            when(paasVipConfig.tenantIsSupportDeviceVip(any())).thenReturn(false);
            when(paasVipConfig.tenantIsSupportPaasUserVip(any())).thenReturn(false);
            when(userVipService.queryUserVipList(any(),any(),any())).thenReturn(Lists.newArrayList());

            actualResult = vipService.queryVipCountType(tenantId,userId,sn);
            Assert.assertNull(actualResult);
        }
    }

    @Test
    public void test_deviceHas4GDataVip(){
        String serialNumber = "";
        Boolean device4G = null;
        Integer userId = 1;

        Boolean actualResult = false;
        {
            device4G = false;
            actualResult = vipService.deviceHas4GDataVip(userId,device4G,serialNumber);
            Assert.assertFalse(actualResult);
        }
        {
            device4G = true;
            when(userTierDeviceService.getDeviceCurrentTier(any(),any())).thenReturn(null);
            when(userService.queryUserById(any())).thenReturn(null);
            actualResult = vipService.deviceHas4GDataVip(userId,device4G,serialNumber);
            Assert.assertFalse(actualResult);
        }
    }


    @Test
    public void test_isDeivce4GVip(){
        Integer userId = 1;
        Boolean exceptedResult ;
        Boolean actualResult ;

        {
            exceptedResult = false;
            actualResult = vipService.isDeivce4GVip(userId,null);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            when(userTierDeviceService.getDeviceCurrentTier(any(),any())).thenReturn(null);
            exceptedResult = false;
            actualResult = vipService.isDeivce4GVip(userId,new Device4GSimDO());
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            when(userTierDeviceService.getDeviceCurrentTier(any(),any())).thenReturn(1);

            exceptedResult = true;

            actualResult = vipService.isDeivce4GVip(userId,new Device4GSimDO());
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }
}
