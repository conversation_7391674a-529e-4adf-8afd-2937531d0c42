package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.FirmwareTableDO;
import com.addx.iotcamera.dao.IFirmwareDAO;
import com.addx.iotcamera.service.firmware.FirmwareService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class OTAServiceTest {

    @InjectMocks
    FirmwareService firmwareService;

    @Mock
    IFirmwareDAO firmwareDAO;


    @Test
    public void testOtaShard() {
        List<FirmwareTableDO> firmwareList = new ArrayList<>();
        firmwareList.add(FirmwareTableDO.builder()
                .modelNo("CG1")
                .firmwareId("0.0.1")
                .activated(1)
                .deprecated(0)
                .otaLimit("")
                .build());
        firmwareList.add(FirmwareTableDO.builder()
                .modelNo("CG1")
                .firmwareId("0.0.2")
                .activated(1)
                .deprecated(0)
                .otaLimit("2:0")
                .build());

        when(firmwareDAO.getAllFirmwareForModel(any(), any())).thenReturn(firmwareList);
        firmwareService.setOtaUnlimitedUsers("");

        assertEquals("无白名单 user 0 应该获取到0.0.2",
                firmwareService.getLatestActivatedFirmwareByModelNoAndAdminId("CG1", 0, null).getFirmwareId(),
                "0.0.2");

        assertEquals("无白名单 user 1 应该获取到0.0.1",
                firmwareService.getLatestActivatedFirmwareByModelNoAndAdminId("CG1", 1, null).getFirmwareId(),
                "0.0.1");

        // 引入白名单
        firmwareService.setOtaUnlimitedUsers("0,1,2,3,4,5");
        assertEquals("有白名单 user 0 应该获取到0.0.2",
                firmwareService.getLatestActivatedFirmwareByModelNoAndAdminId("CG1", 0, null).getFirmwareId(),
                "0.0.2");

        assertEquals("有白名单 user 1 应该获取到0.0.2",
                firmwareService.getLatestActivatedFirmwareByModelNoAndAdminId("CG1", 1, null).getFirmwareId(),
                "0.0.2");

        System.out.println("success");
    }
}
