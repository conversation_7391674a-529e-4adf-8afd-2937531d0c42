package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.BeanBatchModify;
import com.addx.iotcamera.bean.domain.InsertLibraryRequest;
import com.addx.iotcamera.bean.openapi.*;
import com.addx.iotcamera.config.PaasTenantConfigTest;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.dao.openapi.DeviceVipDAO;
import com.addx.iotcamera.dao.openapi.PaasVipProductDAO;
import com.addx.iotcamera.dynamo.dao.VideoSliceDAO;
import com.addx.iotcamera.enums.VideoType;
import com.addx.iotcamera.service.openapi.DeviceVipService;
import com.addx.iotcamera.service.openapi.PaasVipProductService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.VideoConstants.PATH_VIDEO_DOWNLOAD_M3U8;
import static com.addx.iotcamera.constants.VideoConstants.POSTFIX_M3U8;
import static com.addx.iotcamera.helper.TimeRecorder.videoSliceTimeRecorder;
import static com.addx.iotcamera.util.NatureMonthUtil.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

// test
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceVipServiceTest {

    @InjectMocks
    private DeviceVipService deviceVipService;
    @Mock
    private PaasTenantConfig paasTenantConfig;
    @Mock
    private UserService userService;
//    @Mock
    private DeviceVipDAO deviceVipDAO;
    @Mock
    private DeviceConfigService deviceConfigService;
    @Mock
    private NotificationService notificationService;
    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private PaasVipProductService paasVipProductService;
    @Mock
    private DeviceInfoService deviceInfoService;

    private static List<DeviceVipLog> vipLogList = Arrays.asList(
            new DeviceVipLog(millisOfYmd(20210301), "1", 1),
            new DeviceVipLog(millisOfYmd(20210401), "0", 0),
            new DeviceVipLog(millisOfYmd(20210406), "3", 3),
            new DeviceVipLog(millisOfYmd(20210416), "0", 0),
            new DeviceVipLog(millisOfYmd(20210420), "2", 2),
            new DeviceVipLog(millisOfYmd(20210620), "1", 1),
            new DeviceVipLog(millisOfYmd(20210720), "0", 0),
            new DeviceVipLog(millisOfYmd(20210920), "1", 1),
            new DeviceVipLog(millisOfYmd(20220320), "0", 0),
            new DeviceVipLog(millisOfYmd(20220921), "2", 2)
    );
    private TestHelper testHelper;
    private Map<String, PaasVipProduct> productMap;

    @Before
    public void before() {
        testHelper = TestHelper.getInstanceByEnv("test");
//        testHelper = TestHelper.getInstanceByLocal();
//        testHelper = TestHelper.getInstanceByEnv("prod");
//        DeviceVipDAO deviceVipDAO = testHelper.getMapper(DeviceVipDAO.class);
        PaasVipProductDAO paasVipProductDAO = testHelper.getMapper(PaasVipProductDAO.class);
        productMap = paasVipProductDAO.queryAllPaasVipProduct().stream().collect(Collectors.toMap(it -> it.getId(), it -> it));
        when(paasVipProductService.queryPaasVipProductById(anyString())).thenAnswer(it->{
            return paasVipProductDAO.queryPaasVipProductById(it.getArgument(0));
        });
        when(paasVipProductService.queryAllPaasVipProduct()).thenAnswer(it->{
            return paasVipProductDAO.queryAllPaasVipProduct();
        });
        when(applicationContext.getBean(DeviceVipService.class)).thenReturn(deviceVipService);

        this.deviceVipDAO = testHelper.getMapper(DeviceVipDAO.class);

        VideoSliceDAO videoSliceDAO = new VideoSliceDAO();
        videoSliceDAO.setDynamoDB(testHelper.getAmazonInit().dynamoDB());
        videoSliceDAO.setVideoSliceMapper(testHelper.getAmazonInit().videoSliceMapper());
        videoSliceDAO.setVideoSliceConfig(testHelper.getAmazonInit().videoSliceMapperConfig());
        videoSliceDAO.setVideoCompleteReportMapper(testHelper.getAmazonInit().videoCompleteReportMapper());
        videoSliceDAO.setVideoCompleteReportConfig(testHelper.getAmazonInit().videoCompleteReportMapperConfig());

        S3Service s3Service = new S3Service();
        s3Service.setS3Client(testHelper.getS3Client());
        LibraryService libraryService = LibraryService.builder()
                .libraryStatusService(LibraryStatusService.builder().build())
                .s3Service(s3Service)
                .build();
        deviceVipService.setDeviceVipDAO(deviceVipDAO);
        deviceVipService.setExecutor(Runnable::run);

        final PaasTenantConfig paasTenantConfig = new PaasTenantConfig();
        when(this.paasTenantConfig.getPaasTenantInfo(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(paasTenantConfig));
        PaasTenantConfigTest.mockPaasTenantConfig(this.paasTenantConfig);
    }

    @After
    public void after() {
//        testHelper.commitAndClose();
    }

//    @Test
//    public void test_computeMonthlyVipGrade() {
//        DeviceVipService.MonthlyVipGrade vipGrade = DeviceVipService.computeMonthlyVipGrade(vipLogList, 202103, 202202);
//        log.info("computeMonthlyVipGrade:{}", vipGrade);
//        int[][] ints = DeviceVipService.countMonthlyVipNum(Arrays.asList(vipGrade), 202103, 202202);
//        String str = Stream.of(ints).map(Arrays::toString).collect(Collectors.joining(",\n", "[\n", "\n]"));
//        log.info("countMonthlyVipNum:{}", str);
//    }

    @Test
    public void test_ymDiff() {
        Assert.assertEquals(11, ymDiff(202101, 202112));
        Assert.assertEquals(12, ymDiff(202101, 202201));
        Assert.assertEquals(7, ymDiff(202106, 202201));
        Assert.assertEquals(20, ymDiff(202101, 202209));
    }

    final String tenantId = "longse";

    private List<String> sns = Arrays.asList(
            "dc53ccf2180bafbad61962f2e14aaaf6",
            "08033bc82cf829775c609511d74f7102",
            "b533db20f9e1a8e00fccfbea80c62d83",
            "d5b9f52c9cc62a446c87d76f169f8a02",
            "6ac641dfe32fb011857f742ef6ced4d2",
            "574c7572e9ef5ed12456d413fd26699c",
            "16b656dc9c04407152ccd6737ad22db4",
            "8494fbfd94d327c9b9cb801231843799",
            "672fdc2960d22523a027aa6a6488341c",
            "40017f5b4c2c2334a76454b1af1aa061",
            "b7f718b82645422be6d3deb7e659c8c1",
            "1c55d9331762a8335211fffd5931b2be",
            "91fc7ac9cec9b97474753da6ff5e3cf3",
            "225a1bf817287d4fb8258cc75e73806f",
            "25fca04613c0957be30151c771c72ab9",
            "8638cb2b44fc8b34ee74c17f97673a20",
            "f3b65a3354af70eba2c1522fe0a98670"
    );

    @Test
    public void test_updateDeviceVip() {
        long expectNum = vipLogList.stream().filter(it -> it.getVipLevel() != PaasVipLevel.NO_VIP.getCode()).count();
        for (int i = 0; i < 1; i++) {
            final String sn = OpenApiUtil.shortUUID();
            for (DeviceVipLog vipLog : vipLogList) {
                DeviceVipUpdate input = new DeviceVipUpdate().setTenantId(tenantId).setSerialNumber(sn)
                        .setProductId(vipLog.getProductId()).setTime(vipLog.getTime());
                Result result = deviceVipService.updateDeviceVip(input, null);
                Assert.assertEquals(Result.successFlag, result.getResult());
            }
            List<DeviceVipLog> deviceVipLogs = deviceVipDAO.queryDeviceVip(tenantId, sn);
            Assert.assertEquals(expectNum, deviceVipLogs.size());

            PaasVipLevel vipGrade = PaasVipLevel.codeOf(deviceVipService.queryLastDeviceVip(tenantId, sn).getVipLevel());
            log.info("getDeviceVipGrade:{}", vipGrade);
        }
    }

    //    @Test
    public void test_updateDeviceVip2() {
        Random random = new Random();
        for (String sn : sns) {
            Calendar calendar = calendarOfYmd(20200101);
            for (int i = 0; i < 10; i++) {
                calendar.add(Calendar.DATE, random.nextInt(50));
                int productId = random.nextInt(4);
                DeviceVipUpdate input = new DeviceVipUpdate().setTenantId(tenantId).setSerialNumber(sn)
                        .setProductId(productId + "").setTime(calendar.getTimeInMillis());
                Result result = deviceVipService.updateDeviceVip(input, null);
                Assert.assertEquals(Result.successFlag, result.getResult());
            }
            PaasVipLevel vipGrade = PaasVipLevel.codeOf(deviceVipService.queryLastDeviceVip(tenantId, sn).getVipLevel());
            log.info("getDeviceVipGrade:{}", vipGrade);
        }
    }

    @Test
    public void test_freeTrail() throws UnsupportedEncodingException {
        long time = System.currentTimeMillis();
        long DAY = 24 * 3600 * 1000;

        String tenantId = "test_freeTrail_" + OpenApiUtil.shortUUID();
        DeviceVipUpdate update = new DeviceVipUpdate().setTenantId(tenantId).setTime(time);
        Result<JSONObject> result;
        update.setSerialNumber(OpenApiUtil.shortUUID()).setProductId("4").setTime(time);
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(102), result.getResult());

        when(this.paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableDeviceVip(true)
                .setEnableDeviceVipFreeTrial(true).setEnableDeviceVipEndsEarly(false));
        // 多次免费领取都返回0
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        update.setTime(time + 7 * DAY);
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        // 设备正在免费试用套餐,不支持提前结束
        update.setProductId("3").setTime(time + 14 * DAY);
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(1003), result.getResult());
        // 设备正在免费试用套餐,支持提前结束
        {
            when(this.paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableDeviceVip(true)
                    .setEnableDeviceVipFreeTrial(true).setEnableDeviceVipEndsEarly(true));
            result = deviceVipService.updateDeviceVip(update, null);
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
            when(this.paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableDeviceVip(true)
                    .setEnableDeviceVipFreeTrial(true).setEnableDeviceVipEndsEarly(false));
        }
        // 免费试用套餐过期
        update.setProductId("3").setTime(time + 32 * DAY);
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        // 设备已经领取过免费试用套餐
        update.setProductId("4").setTime(time + 41 * DAY);
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(1001), result.getResult());

        // 设备已经开通了VIP套餐
        update.setSerialNumber(OpenApiUtil.shortUUID()).setProductId("2").setTime(time);
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        update.setProductId("4").setTime(time + 7 * DAY);
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(1002), result.getResult());

        // 开通vip，自动过期
        update.setSerialNumber(OpenApiUtil.shortUUID()).setProductId("4").setTime(time);
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        DeviceVipLog vipLog = deviceVipService.queryLastDeviceVip(tenantId, update.getSerialNumber());
        Map<String, PaasVipProduct> deviceVipProductMap = paasVipProductService.queryAllPaasVipProduct()
                .stream().collect(Collectors.toMap(it -> it.getId(), it -> it));
        DeviceVipLog newVipLog = deviceVipService.handleFreeTrailExpired(time + 32 * DAY, vipLog, deviceVipProductMap::get);
        Assert.assertNotEquals(vipLog, newVipLog);

        // 打开，关闭
        update.setProductId("1").setTime(time + 41 * DAY);
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        update.setProductId("0").setTime(time + 62 * DAY);
        result = deviceVipService.updateDeviceVip(update, null);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());

        // 导出csv和json
        ExportDeviceVipCount input = new ExportDeviceVipCount().setTenantId(tenantId)
                .setBeginMonth(ymOfMillis(time)).setEndMonth(ymOfMillis(time + 100 * DAY)).setBatchSize(50);

        ByteArrayOutputStream baos1 = new ByteArrayOutputStream();
        PrintStream out1 = new PrintStream(baos1, true, "UTF-8");
        deviceVipService.exportMonthlyVipNumByCsv(input, out1);
        String csvStr = baos1.toString();
        log.info("test_freeTrail csv:\n{}", csvStr);
        Assert.assertNotNull(csvStr);

        ByteArrayOutputStream baos2 = new ByteArrayOutputStream();
        PrintStream out2 = new PrintStream(baos2, true, "UTF-8");
        deviceVipService.exportMonthlyVipNumByJson(input, out2);
        String jsonStr = baos2.toString();
        log.info("test_freeTrail json:\n{}", jsonStr);

    }

    @Test
    public void test_computeMonthlyVipLevel() {
        List<DeviceVipLog> vipLogList = deviceVipDAO.queryDeviceVip("test_freeTrail_yjusOL7rD8iNh0Ar5Hg894", "52HLZmqOC5NUsKwQp035Y1");
        String[] productIds = deviceVipService.computeMonthlyVipLevelV2(vipLogList, 202111, 202206);
        log.info("productIds:{}", JSON.toJSONString(productIds));
    }

    private DeviceVipLog deviceVipLog(int ymd, int vipLevel) {
        long time = calendarOfYmd(ymd).getTimeInMillis();
        return new DeviceVipLog().setVipLevel(vipLevel).setProductId(vipLevel + "")
                .setTime(time).setEndTime(0L);
    }

    private DeviceVipLog exchangeCode(int ymd, int vipLevel0, int month) {
        int vipLevel = vipLevel0 - 1 + PaasVipLevel.EXCHANGE_BASIC.getCode();
        String productId = String.format("e%dm%02d", vipLevel, month);
        return new DeviceVipLog().setVipLevel(vipLevel).setProductId(productId)
                .setTime(calendarOfYmd(ymd).getTimeInMillis());
    }

    @Test
    public void test_computeMonthlyVipLevel2() {
        List<DeviceVipLog> vipLogList = Arrays.asList(
                deviceVipLog(2021_11_01, 1),
                deviceVipLog(2022_01_01, 0),
                deviceVipLog(2022_03_01, 2),
                deviceVipLog(2022_04_02, 0),
                deviceVipLog(2022_04_03, 3),
                deviceVipLog(2022_04_04, 0),
                deviceVipLog(2022_04_05, 1),
//                exchangeCode(2022_04_06, 2, 3),
//                deviceVipLog(2022_07_06, 0),
                exchangeCode(2022_06_06, 2, 3),
                deviceVipLog(2022_09_06, 0),
                deviceVipLog(2022_10_07, 3),
                exchangeCode(2022_11_11, 1, 12)
        );
        BeanBatchModify<DeviceVipLog, Long> modify = DeviceVipService.transferOldDeviceVipLogs(vipLogList);
//        String[] productIds1 = deviceVipService.computeMonthlyVipLevel(vipLogList, 202111, 202206);
        String[] productIds2 = deviceVipService.computeMonthlyVipLevelV2(vipLogList, 202111, 202212);
//        log.info("productIds1:{}", JSON.toJSONString(productIds1));
        log.info("productIds2:{}", JSON.toJSONString(productIds2));
        String[] expectProductIds = {"1"/*11*/, "1"/*12*/, "0"/*1*/, "0"/*2*/, "2"/*3*/, "3"/*4*/, "1"/*5*/,
        "e6m03"/*6*/,"e6m03"/*7*/,"e6m03"/*8*/,"0"/*9*/,"3"/*10*/,"e5m12"/*11*/};
//        Assert.assertTrue(Arrays.equals(expectProductIds, productIds1));
        Assert.assertTrue(Arrays.equals(expectProductIds, productIds2));
    }

    //    @Test
    public void test_exportMonthlyVipNumByCsv() throws IOException {
        ExportDeviceVipCount input = new ExportDeviceVipCount().setTenantId(tenantId)
                .setBeginMonth(202001).setEndMonth(202212).setBatchSize(50);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PrintStream out = new PrintStream(baos, true, "UTF-8");
//        PrintStream out = new PrintStream("/Users/<USER>/test/x1.csv");
        deviceVipService.exportMonthlyVipNumByCsv(input, out);
        out.flush();
        baos.writeTo(System.out);
    }

    //    @Test
    public void test_exportMonthlyVipNumByJson() throws IOException {
        ExportDeviceVipCount input = new ExportDeviceVipCount().setTenantId(tenantId)
                .setBeginMonth(202001).setEndMonth(202212).setBatchSize(50);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PrintStream out = new PrintStream(baos, true, "UTF-8");
//        PrintStream out = new PrintStream("/Users/<USER>/test/x1.csv");
        deviceVipService.exportMonthlyVipNumByJson(input, out);
        out.flush();
        String str = baos.toString();
        System.out.println(str);
        JSONObject data = JSON.parseObject(str);
        JSONArray devices = data.getJSONArray("devices");
        Map<String, Long> snNumAndLogNum = deviceVipDAO.querySnNumAndLogNum(tenantId);
        Assert.assertEquals(snNumAndLogNum.get("snNum").intValue(), devices.size());
//        baos.writeTo(System.out);
    }

//    @Test
    public void test_queryDeviceVip() throws IOException {
        List<String> sns = Arrays.asList("test_20220311_zyj2_0", "test_20220311_zyj2_1", "test_20220311_zyj2_4", "test_20220311_zyj2_9");
//        Result result = deviceVipService.queryDeviceVip(new DeviceVipQuery().setTenantId(tenantId).setSerialNumbers(sns));
        List<DeviceVipLog> vipList = deviceVipService.queryDeviceVipList(new DeviceVipQuery().setTenantId(tenantId).setSerialNumbers(sns)
                .setTime(System.currentTimeMillis()));
        Assert.assertEquals(4, vipList.size());
    }

    private Random random = new Random();

    private InsertLibraryRequest createMockVideo(String sn, Integer utcStamp) {
        String traceId = "traceId_" + random.nextInt();

        InsertLibraryRequest insertLibrary = new InsertLibraryRequest();
        insertLibrary.setTimestamp(utcStamp);
        insertLibrary.setSerialNumber(sn);
        insertLibrary.setTraceId(traceId);
        insertLibrary.setVideoUrl("video_slice" + PATH_VIDEO_DOWNLOAD_M3U8 + "/" + traceId + POSTFIX_M3U8);
        insertLibrary.setImageUrl(null);
        insertLibrary.setPeriod(BigDecimal.valueOf(2d));
        insertLibrary.setFileSize(random.nextLong()); // 1GB
        insertLibrary.setReceivedAllSlice(false);
        insertLibrary.setAdminId(1234);
        insertLibrary.setDeviceName("deviceName");
        insertLibrary.setType(VideoType.DEVICE_SLICE.getCode());
        videoSliceTimeRecorder.recordBegin("insertLibrary");
        return insertLibrary;
    }

    //    @Test
    public void test_batchQuery() {
        IUserDAO userDAO = testHelper.getMapper(IUserDAO.class);
        List list1 = userDAO.selectAllBatch(0, null, null, 100,0);
        List list2 = userDAO.selectAllBatch(0, null, Collections.EMPTY_SET, 100,0);
        List list3 = userDAO.selectAllBatch(0, null, Arrays.asList("guard"), 100,0);
        List list11 = userDAO.selectAllBatch(0, Arrays.asList(0), null, 100,0);
        List list21 = userDAO.selectAllBatch(0, Arrays.asList(0), Collections.EMPTY_SET, 100,0);
        List list31 = userDAO.selectAllBatch(0, Arrays.asList(0), Arrays.asList("guard"), 100,0);

        int totalNum = deviceVipService.batchQueryByTenantId(null, 1000, (vipLogs) -> {
            log.info(DeviceVipLog.getDesc(vipLogs));
        });
        log.info("test_batchQuery:{}", totalNum);
    }

//    @Test
    public void test_export() throws FileNotFoundException {
        ExportDeviceVipCount input = new ExportDeviceVipCount()
                .setTenantId("longse")
                .setFormat("csv")
                .setBeginMonth(202109)
                .setEndMonth(202202);

//        String fileName = "/Users/<USER>/test/device-vip_prod-cn_20220217.csv";
        String fileName = "/Users/<USER>/test/device-vip_prod-eu_20220217.csv";
//        String fileName = "/Users/<USER>/test/device-vip_prod-us_20220217.csv";
        try (PrintStream out = new PrintStream(fileName)) {
            deviceVipService.exportMonthlyVipNumByCsv(input, out);
        }
    }

    private DeviceVipLog vip(String productId, long time, long endTime) {
        Integer vipLevel = productMap.get(productId).getVipLevel();
        return new DeviceVipLog().setId(time).setProductId(productId).setVipLevel(vipLevel).setTime(time).setEndTime(endTime);
    }

    private static Long MAX_TIME = DeviceVipService.MAX_TIME;
    private static Long DAY_TIME = (long) 1000 * 3600 * 24;
    private static Long CUR_TIME = DAY_TIME * 10;

    private BeanBatchModify<DeviceVipLog, Long> handleDeviceVipUpdate(List<DeviceVipLog> deviceVipLogs, String productId, long time, Integer expectResultCode) {
        Assert.assertTrue(DeviceVipService.verifyDeviceVipLogs(deviceVipLogs));
        DeviceVipUpdate input = new DeviceVipUpdate().setTime(time).setProductId(productId);
        PaasVipProduct product = productMap.get(input.getProductId());
        BeanBatchModify<DeviceVipLog, Long> beanBatchModify = new BeanBatchModify<>(DeviceVipLog::new);
//        Result result = DeviceVipService.handleDeviceVipUpdate(input, deviceVipLogs, product, beanBatchModify);
//        Assert.assertEquals(expectResultCode, result.getResult());
        return beanBatchModify;
    }

    private List<DeviceVipLog> vips(DeviceVipLog... arr) {
        List<DeviceVipLog> list = new LinkedList<>();
        for (int i = 0; i < arr.length; i++) {
            list.add(arr[i].setId((long) i));
        }
        return list;
    }

//    @Test
    public void test2() {
        // 无限期vip
        List<Integer> vipLevels1 = Arrays.asList(1, 2, 3);
        List<String> productIds1 = productMap.values().stream().filter(it -> vipLevels1.contains(it.getVipLevel()))
                .map(it -> it.getId()).collect(Collectors.toList());
        // 固定期限vip: 免费试用
        List<Integer> vipLevels4 = Arrays.asList(4);
        List<String> productIds4 = productMap.values().stream().filter(it -> vipLevels4.contains(it.getVipLevel()))
                .map(it -> it.getId()).collect(Collectors.toList());
        // 固定期限vip: 兑换码
        List<Integer> vipLevels6 = Arrays.asList(5, 6, 7);
        List<String> productIds6 = productMap.values().stream().filter(it -> vipLevels6.contains(it.getVipLevel()))
                .map(it -> it.getId()).collect(Collectors.toList());
        List<String> productIds46 = productMap.values().stream()
                .filter(it -> vipLevels4.contains(it.getVipLevel()) || vipLevels6.contains(it.getVipLevel()))
                .map(it -> it.getId()).collect(Collectors.toList());

        // 1. 设置成无限期vip
        for (String productId : productIds1) {
            // 1.1. 无VIP，设置成无限期vip
            {
                List<DeviceVipLog> deviceVipLogs = vips();
                BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                Assert.assertEquals(1, modify.getSaveList().size());
                Assert.assertEquals(0, modify.getDeleteKeys().size());
                Assert.assertEquals(0, modify.getUpdateMap().size());
                Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                Assert.assertEquals(CUR_TIME, modify.getSaveList().get(0).getTime());
                Assert.assertEquals(MAX_TIME, modify.getSaveList().get(0).getEndTime());
            }
            // 1.2. 有无限期vip，设置成无限期vip
            for (String oldProductId : productIds1) {
                // 1.2.1 有已开始无限期VIP，设置成无限期vip
                for (int i = 0; i <= 1; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - i, MAX_TIME));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                    if (oldProductId.equals(productId)) {
                        Assert.assertEquals(0, modify.getSaveList().size());
                        Assert.assertEquals(0, modify.getDeleteKeys().size());
                        Assert.assertEquals(0, modify.getUpdateMap().size());
                    } else {
                        Assert.assertEquals(1, modify.getSaveList().size());
                        Assert.assertEquals(0, modify.getDeleteKeys().size());
                        Assert.assertEquals(1, modify.getUpdateMap().size());
                        Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                        Assert.assertEquals(CUR_TIME, modify.getSaveList().get(0).getTime());
                        Assert.assertEquals(MAX_TIME, modify.getSaveList().get(0).getEndTime());
                        Assert.assertTrue(modify.getUpdateMap().containsKey(0l));
                        Assert.assertEquals(CUR_TIME, modify.getUpdateMap().get(0l).getEndTime());
                    }
                }
                // 1.2.2 有未开始无限期VIP，设置成无限期vip
                for (int i = 1; i <= 2; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME + i, MAX_TIME));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                    Assert.assertEquals(1, modify.getSaveList().size());
                    Assert.assertEquals(1, modify.getDeleteKeys().size());
                    Assert.assertEquals(0, modify.getUpdateMap().size());
                    Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                    Assert.assertEquals(CUR_TIME, modify.getSaveList().get(0).getTime());
                    Assert.assertEquals(MAX_TIME, modify.getSaveList().get(0).getEndTime());
                    Assert.assertTrue(modify.getDeleteKeys().contains(0l));
                }
            }
            // 1.3. 固定期限vip: 设置成无限期vip
            for (String oldProductId : productIds46) {
                // 1.3.1. 有固定期限vip，设置成无限期vip
                for (int i = 0; i <= 1; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME + i));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                }
                // 1.2.2. 有快结束的固定期限vip，设置成无限期vip
                for (int i = 1; i <= 2; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME - i));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                    Assert.assertEquals(1, modify.getSaveList().size());
                    Assert.assertEquals(0, modify.getDeleteKeys().size());
                    Assert.assertEquals(0, modify.getUpdateMap().size());
                    Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                    Assert.assertEquals(new Long(CUR_TIME + DAY_TIME - i), modify.getSaveList().get(0).getTime());
                    Assert.assertEquals(MAX_TIME, modify.getSaveList().get(0).getEndTime());
                }
                // 1.2.3. 有快结束的固定期限vip + 设置成无限期vip，设置成无限期vip
                for (int i = 1; i <= 2; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(
                            vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME - i),
                            vip("2", CUR_TIME + DAY_TIME - i, MAX_TIME)
                    );
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                    Assert.assertEquals(1, modify.getSaveList().size());
                    Assert.assertEquals(1, modify.getDeleteKeys().size());
                    Assert.assertEquals(0, modify.getUpdateMap().size());
                    Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                    Assert.assertEquals(new Long(CUR_TIME + DAY_TIME - i), modify.getSaveList().get(0).getTime());
                    Assert.assertEquals(MAX_TIME, modify.getSaveList().get(0).getEndTime());
                    Assert.assertTrue(modify.getDeleteKeys().contains(1L));
                }
            }
        }
        // 2. 设置成固定限期vip(兑换码)
        for (String productId : productIds6) {
            Long END_TIME = DeviceVipService.computePaasVipEndTime(CUR_TIME, productMap.get(productId).getMonth());

            // 1.1. 无VIP，设置成固定限期vip
            {
                List<DeviceVipLog> deviceVipLogs = vips();
                BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                Assert.assertEquals(1, modify.getSaveList().size());
                Assert.assertEquals(0, modify.getDeleteKeys().size());
                Assert.assertEquals(0, modify.getUpdateMap().size());
                Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                Assert.assertEquals(CUR_TIME, modify.getSaveList().get(0).getTime());
                Assert.assertEquals(END_TIME, modify.getSaveList().get(0).getEndTime());
            }

            // 1.2. 有无限期vip，设置成固定限期vip
            for (String oldProductId : productIds1) {
                // 1.2.1 有已开始无限期VIP，设置成固定限期vip
                for (int i = 0; i <= 1; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - i, MAX_TIME));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                    if (oldProductId.equals(productId)) {
                        Assert.assertEquals(0, modify.getSaveList().size());
                        Assert.assertEquals(0, modify.getDeleteKeys().size());
                        Assert.assertEquals(0, modify.getUpdateMap().size());
                    } else {
                        Assert.assertEquals(1, modify.getSaveList().size());
                        Assert.assertEquals(0, modify.getDeleteKeys().size());
                        Assert.assertEquals(1, modify.getUpdateMap().size());
                        Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                        Assert.assertEquals(CUR_TIME, modify.getSaveList().get(0).getTime());
                        Assert.assertEquals(END_TIME, modify.getSaveList().get(0).getEndTime());
                        Assert.assertTrue(modify.getUpdateMap().containsKey(0l));
                        Assert.assertEquals(CUR_TIME, modify.getUpdateMap().get(0l).getEndTime());
                    }
                }
                // 1.2.2 有未开始无限期VIP，设置成固定限期vip
                for (int i = 1; i <= 2; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME + i, MAX_TIME));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                    Assert.assertEquals(1, modify.getSaveList().size());
                    Assert.assertEquals(1, modify.getDeleteKeys().size());
                    Assert.assertEquals(0, modify.getUpdateMap().size());
                    Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                    Assert.assertEquals(CUR_TIME, modify.getSaveList().get(0).getTime());
                    Assert.assertEquals(END_TIME, modify.getSaveList().get(0).getEndTime());
                    Assert.assertTrue(modify.getDeleteKeys().contains(0l));
                }
            }

            // 1.3. 固定期限vip: 设置成固定限期vip
            for (String oldProductId : productIds46) {
                // 1.3.1. 有固定期限vip，设置成固定限期vip
                for (int i = 0; i <= 1; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME + i));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                }
                // 1.2.2. 有快结束的固定期限vip，设置成固定限期vip
                for (int i = 1; i <= 2; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME - i));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                    Assert.assertEquals(1, modify.getSaveList().size());
                    Assert.assertEquals(0, modify.getDeleteKeys().size());
                    Assert.assertEquals(0, modify.getUpdateMap().size());
                    Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                    Assert.assertEquals(new Long(CUR_TIME + DAY_TIME - i), modify.getSaveList().get(0).getTime());
                    Long endTime = DeviceVipService.computePaasVipEndTime(CUR_TIME + DAY_TIME - i, productMap.get(productId).getMonth());
                    Assert.assertEquals(endTime, modify.getSaveList().get(0).getEndTime());
                }
                // 1.2.3. 有快结束的固定期限vip + 固定限期vip，设置成固定限期vip
                for (int i = 1; i <= 2; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(
                            vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME - i),
                            vip(oldProductId, CUR_TIME + DAY_TIME - i, CUR_TIME + 10 * DAY_TIME - i)
                    );
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 1003);
                }
                // 1.2.4. 有快结束的固定期限vip + 设置成无限期vip，设置成固定限期vip
                for (int i = 1; i <= 2; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(
                            vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME - i),
                            vip("2", CUR_TIME + DAY_TIME - i, MAX_TIME)
                    );
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                    Assert.assertEquals(1, modify.getSaveList().size());
                    Assert.assertEquals(1, modify.getDeleteKeys().size());
                    Assert.assertEquals(0, modify.getUpdateMap().size());
                    Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                    Assert.assertEquals(new Long(CUR_TIME + DAY_TIME - i), modify.getSaveList().get(0).getTime());
                    Long endTime = DeviceVipService.computePaasVipEndTime(CUR_TIME + DAY_TIME - i, productMap.get(productId).getMonth());
                    Assert.assertEquals(endTime, modify.getSaveList().get(0).getEndTime());
                    Assert.assertTrue(modify.getDeleteKeys().contains(1L));
                }
            }
        }
        // 3. 设置成固定限期vip(免费试用)
        for (String productId : productIds4) {
            Long END_TIME = DeviceVipService.computePaasVipEndTime(CUR_TIME, productMap.get(productId).getMonth());

            // 1.1. 无VIP，设置成免费试用
            {
                List<DeviceVipLog> deviceVipLogs = vips();
                BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 0);
                Assert.assertEquals(1, modify.getSaveList().size());
                Assert.assertEquals(0, modify.getDeleteKeys().size());
                Assert.assertEquals(0, modify.getUpdateMap().size());
                Assert.assertEquals(productId, modify.getSaveList().get(0).getProductId());
                Assert.assertEquals(CUR_TIME, modify.getSaveList().get(0).getTime());
                Assert.assertEquals(END_TIME, modify.getSaveList().get(0).getEndTime());
            }

            // 1.2. 有无限期vip，设置成免费试用
            for (String oldProductId : productIds1) {
                // 1.2.1 有已开始无限期VIP，设置成免费试用
                for (int i = 0; i <= 1; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - i, MAX_TIME));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 1002);
                }
                // 1.2.2 有未开始无限期VIP，设置成免费试用
                for (int i = 1; i <= 2; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME + i, MAX_TIME));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 1002);
                }
            }

            // 1.3. 固定期限vip: 设置成免费试用
            for (String oldProductId : productIds6) {
                // 1.3.1. 有固定期限vip，设置成免费试用
                for (int i = 0; i <= 1; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME + i));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 1002);
                }
                // 1.2.2. 有快结束的固定期限vip，设置成免费试用
                for (int i = 1; i <= 2; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME - i));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 1002);
                }
            }

            // 1.4. 免费试用: 设置成免费试用
            for (String oldProductId : productIds4) {
                // 1.3.1. 有免费试用，设置成免费试用
                for (int i = 0; i <= 1; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME + i));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 1001);
                }
                // 1.2.2. 有快结束的免费试用，设置成免费试用
                for (int i = 1; i <= 2; i++) {
                    List<DeviceVipLog> deviceVipLogs = vips(vip(oldProductId, CUR_TIME - DAY_TIME, CUR_TIME + DAY_TIME - i));
                    BeanBatchModify<DeviceVipLog, Long> modify = handleDeviceVipUpdate(deviceVipLogs, productId, CUR_TIME, 1001);
                }
            }
        }

        // test end
    }

    /**
     * 校验现有老数据
     *
     * @param args
     */
    public static void main(String[] args) {
        List<String> nodeIds = Arrays.asList("prod", "prod-eu", "prod-us");
//        List<String> nodeIds = Arrays.asList("prod-eu");
//        List<String> nodeIds = Arrays.asList("prod");
//        List<String> nodeIds = Arrays.asList("prod-us");
        for (String nodeId : nodeIds) {
            handleOldData(nodeId);
        }
    }

    /*
    数据校验sql:
    select t.id,t.serial_number,t.product_id,t.vip_level
    ,from_unixtime(t.`time`/1000) start_date,t.`time`,t.end_time
    ,(cast(t.`end_time` as decimal(20,2))-cast(t.`time` as decimal(20,2)))/(1000*3600*24) duration
    ,(select (cast(t0.`time` as decimal(20,2))-cast(t.`end_time` as decimal(20,2)))/(1000*3600*24)
    from device_vip_log t0 where t0.serial_number=t.serial_number and t0.id>t.id limit 1) next_duration
    from device_vip_log t order by t.serial_number,t.id limit 100;
     */
    public static void handleOldData(String nodeId) {
        TestHelper testHelper = TestHelper.getInstanceByEnv(nodeId);

        DeviceVipDAO deviceVipDAO = testHelper.getMapper(DeviceVipDAO.class);

        List<DeviceVipLog> list = deviceVipDAO.queryAllDeviceVipLog();
        Map<String, List<DeviceVipLog>> map = list.stream().collect(Collectors.groupingBy(it -> it.getTenantId() + "-" + it.getSerialNumber()));

        BeanBatchModify<DeviceVipLog, Long> totalModify = new BeanBatchModify<>(DeviceVipLog::new);
        List<String> verifyFailSns = new LinkedList<>();
        for (List<DeviceVipLog> vipLogs : map.values()) {
            BeanBatchModify<DeviceVipLog, Long> modify = DeviceVipService.transferOldDeviceVipLogs(vipLogs);
            if (DeviceVipService.verifyDeviceVipLogs(vipLogs)) {
                totalModify.merge(modify);
            } else {
                verifyFailSns.add(vipLogs.get(0).getSerialNumber());
            }
        }
        long t = System.currentTimeMillis();
        if (!nodeId.startsWith("prod")) {
            DeviceVipService deviceVipService = new DeviceVipService();
            deviceVipService.setDeviceVipDAO(testHelper.getMapper(DeviceVipDAO.class));
            deviceVipService.modifyDeviceVipLog(totalModify);
            testHelper.commit();
        } else if (true) {
            String outPath = "/Users/<USER>/temp/update_device_vip_log_" + nodeId + "_" + t + ".sql";
            try (PrintStream out = new PrintStream(new FileOutputStream(outPath))) {
                totalModify.getUpdateMap().forEach((id, it) -> {
                    StringBuilder builder = new StringBuilder();
                    builder.append("update `camera`.`device_vip_log` set `id`=`id`");
                    if (it.getTime() != null) {
                        builder.append(" ,`time`=").append(it.getTime());
                    }
                    if (it.getEndTime() != null) {
                        builder.append(" ,`end_time`=").append(it.getEndTime());
                    }
                    builder.append(" where `id`=").append(id).append(";");
                    out.println(builder);
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        log.info("");
    }

    @Test
    public void test_verifyDbData2() {
        log.info("");
    }

    @Test
    public void test_updateDeviceVip_freeTrail() {
        final String tenantId = OpenApiUtil.shortUUID();
        final String sn = OpenApiUtil.shortUUID();
        final long time = System.currentTimeMillis();
        final DeviceVipUpdate input = new DeviceVipUpdate().setTenantId(tenantId)
                .setSerialNumber(sn).setTime(time).setProductId("4");
        final PaasTenantInfo paasTenantInfo = new PaasTenantInfo().setEnableDeviceVipFreeTrial(false).setEnableDeviceVipEndsEarly(false);
        when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenAnswer(it -> paasTenantInfo);
        paasTenantInfo.setEnableDeviceVipFreeTrial(false);
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, null);
            Assert.assertEquals(result.getMsg(), new Integer(102), result.getResult());
        }
        paasTenantInfo.setEnableDeviceVipFreeTrial(true);
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, null);
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        }
        input.setTime(input.getTime() + DAY_TIME * 15).setProductId("1");
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, null);
            Assert.assertEquals(result.getMsg(), new Integer(1003), result.getResult());
        }
        paasTenantInfo.setEnableDeviceVipEndsEarly(true);
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, null);
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        }
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, PaasVipLevel.getUpdateLevels());
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        }
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, PaasVipLevel.getExchangeLevels());
            Assert.assertEquals(result.getMsg(), new Integer(102), result.getResult());
        }
    }

    @Test
    public void test_updateDeviceVip_vip() {
        final String tenantId = OpenApiUtil.shortUUID();
        final String sn = OpenApiUtil.shortUUID();
        final long time = System.currentTimeMillis();
        final DeviceVipUpdate input = new DeviceVipUpdate().setTenantId(tenantId)
                .setSerialNumber(sn).setTime(time).setProductId("2");
        final PaasTenantInfo paasTenantInfo = new PaasTenantInfo().setEnableDeviceVipFreeTrial(false).setEnableDeviceVipEndsEarly(false);
        when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenAnswer(it -> paasTenantInfo);
        paasTenantInfo.setEnableDeviceVipFreeTrial(false);
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, null);
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        }
        paasTenantInfo.setEnableDeviceVipFreeTrial(true);
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, null);
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        }
        paasTenantInfo.setEnableDeviceVipEndsEarly(false);
        input.setTime(input.getTime() + DAY_TIME * 15).setProductId("1");
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, null);
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        }
        paasTenantInfo.setEnableDeviceVipEndsEarly(true);
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, null);
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        }
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, PaasVipLevel.getUpdateLevels());
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        }
        {
            final Result<JSONObject> result = deviceVipService.updateDeviceVip(input, PaasVipLevel.getExchangeLevels());
            Assert.assertEquals(result.getMsg(), new Integer(102), result.getResult());
        }

    }

}
