package com.addx.iotcamera.service.openapi;

import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PaasVipExchangeServiceTest {

    @InjectMocks
    private PaasVipExchangeService paasVipExchangeService;

    @Before
    public void before(){

    }
    @After
    public void after(){

    }
    @Test
    public void test(){

    }
}
