package com.addx.iotcamera.service.device;


import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceStateDO;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.service.PersistentRedisService;
import com.addx.iotcamera.service.StateMachineService;
import com.addx.iotcamera.service.device_msg.DeviceMsgSrcManager;
import com.addx.iotcamera.util.OpenApiUtil;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static com.addx.iotcamera.enums.DeviceStateMachineEnums.STATE_CONNECTION_LOST;
import static com.addx.iotcamera.enums.DeviceStateMachineEnums.STATE_MQTT_CONNECTED;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceStatusServiceTest {

    @InjectMocks
    private DeviceStatusService deviceStatusService;

    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private StateMachineService stateMachineService;

    @Mock
    private IKissService kissService;

    @Mock
    private PersistentRedisService redisService;

    @Mock
    private DeviceMsgSrcManager deviceMsgSrcManager;

    @Test
    @DisplayName("查询设备连接状态-状态机返回空，默认为离线")
    public void checkDeviceConnectionStatus_stateMachineNoValue() {
        String serialNumber = "abc";
        when(stateMachineService.batchGetDeviceState(any())).thenReturn(new HashMap<>());
        Boolean expectedResult = false;
        Boolean actualResult = deviceStatusService.deviceConnectionStatus(serialNumber);
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("查询设备连接状态-返回值不包含当前序列号，默认为离线")
    public void checkDeviceConnectionStatus_stateMachineNoThisValue() {
        String serialNumber1 = "ab";
        String serialNumber2 = "abc";

        Map<String, DeviceStateDO> deviceStateDOHashMap = Maps.newHashMap();
        DeviceStateDO deviceStateDO = new DeviceStateDO();
        deviceStateDO.setStateId(STATE_MQTT_CONNECTED.getCode());

        deviceStateDOHashMap.put(serialNumber1, deviceStateDO);


        when(stateMachineService.batchGetDeviceState(any())).thenReturn(deviceStateDOHashMap);
        Boolean expectedResult = false;
        Boolean actualResult = deviceStatusService.deviceConnectionStatus(serialNumber2);
        assertEquals(expectedResult, actualResult);
    }


    @Test
    @DisplayName("查询设备连接状态-状态机返回标注为不可用，默认为离线")
    public void checkDevicePushImageNoDevice() {
        String serialNumber = "abc";
        Map<String, DeviceStateDO> deviceStateDOHashMap = Maps.newHashMap();
        DeviceStateDO deviceStateDO = new DeviceStateDO();
        deviceStateDO.setStateId(STATE_MQTT_CONNECTED.getCode());
        deviceStateDO.setData("unstable");
        deviceStateDOHashMap.put(serialNumber, deviceStateDO);

        when(stateMachineService.batchGetDeviceState(any())).thenReturn(deviceStateDOHashMap);
        Boolean expectedResult = false;
        Boolean actualResult = deviceStatusService.deviceConnectionStatus(serialNumber);
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("查询设备连接状态-状态机返回标注为不可用，默认为离线")
    public void checkDevicePushImageNoDevice_unstable() {
        String serialNumber = "abc";
        Map<String, DeviceStateDO> deviceStateDOHashMap = Maps.newHashMap();
        DeviceStateDO deviceStateDO = new DeviceStateDO();
        deviceStateDO.setStateId(STATE_MQTT_CONNECTED.getCode());
        deviceStateDO.setData("unstable");
        deviceStateDOHashMap.put(serialNumber, deviceStateDO);

        when(stateMachineService.batchGetDeviceState(any())).thenReturn(deviceStateDOHashMap);
        Boolean expectedResult = false;
        Boolean actualResult = deviceStatusService.deviceConnectionStatus(serialNumber);
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("查询设备连接状态-状态机为连接中")
    public void checkDevicePushImageNoDevice_connection() {
        String serialNumber = "abc";
        Map<String, DeviceStateDO> deviceStateDOHashMap = Maps.newHashMap();
        DeviceStateDO deviceStateDO = new DeviceStateDO();
        deviceStateDO.setStateId(STATE_MQTT_CONNECTED.getCode());
        deviceStateDOHashMap.put(serialNumber, deviceStateDO);

        when(stateMachineService.batchGetDeviceState(any())).thenReturn(deviceStateDOHashMap);
        Boolean expectedResult = true;
        Boolean actualResult = deviceStatusService.deviceConnectionStatus(serialNumber);

        assertEquals(expectedResult, actualResult);
    }


    @Test
    @DisplayName("查询设备连接状态-状态机为断开连接")
    public void checkDevicePushImageNoDevice_lostConnection() {
        String serialNumber = "abc";
        Map<String, DeviceStateDO> deviceStateDOHashMap = Maps.newHashMap();
        DeviceStateDO deviceStateDO = new DeviceStateDO();
        deviceStateDO.setStateId(STATE_CONNECTION_LOST.getCode());
        deviceStateDOHashMap.put(serialNumber, deviceStateDO);

        when(stateMachineService.batchGetDeviceState(any())).thenReturn(deviceStateDOHashMap);
        Boolean expectedResult = false;
        Boolean actualResult = deviceStatusService.deviceConnectionStatus(serialNumber);

        assertEquals(expectedResult, actualResult);
    }


    @Test
    @DisplayName("设备上报status初始化")
    public void checkDeviceStatus_fromDevice() {
        DeviceDO deviceDO = DeviceDO.builder()
                .chargingMode(1)
                .build();
        DeviceStatusDO deviceStatusDO = DeviceStatusDO.parseFrom(deviceDO);

        assertEquals(deviceDO.getChargingMode(), deviceStatusDO.getChargingMode());
    }

    @Test
    public void testInitDeviceStatusDO() {
        deviceStatusService.getDeviceOnlineInfo(null, 0, DeviceStatusDO.builder().lastAct(0).build(), null);

        deviceStatusService.getDeviceOnlineInfo(null, 0, DeviceStatusDO.builder().lastAct(0).build(), DeviceStateDO.builder().stateId(1).build());
    }

    @Test
    public void test_queryDeviceStatusBySerialNumber() {
        {
            when(redisService.get("device:status:info:sn123")).thenReturn(null);
            final DeviceStatusDO status = deviceStatusService.queryDeviceStatusBySerialNumber("sn123");
            Assert.assertNull(status);
        }
        {
            when(redisService.get("device:status:info:sn123")).thenReturn("");
            final DeviceStatusDO status = deviceStatusService.queryDeviceStatusBySerialNumber("sn123");
            Assert.assertNull(status);
        }
        {
            when(redisService.get("device:status:info:sn123")).thenReturn("{}");
            final DeviceStatusDO status = deviceStatusService.queryDeviceStatusBySerialNumber("sn123");
            Assert.assertNotNull(status);
        }

    }

    @Test
    public void testInitDeviceStatusForBind() {
        BindOperationTb op = new BindOperationTb();
        op.setDeviceNetType(3);
        deviceStatusService.initDeviceStatusForBind("sn", op);
    }

    @Test
    public void test_getOnlineTimeBySn() {
        {
            String sn = OpenApiUtil.shortUUID();
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(modelNo);
            when(stateMachineService.getOnlineTimeByModelNo(modelNo)).thenReturn(310);
            int onlineTime = deviceStatusService.getOnlineTimeBySn(sn);
            Assert.assertEquals(310, onlineTime);
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(null);
            int onlineTime = deviceStatusService.getOnlineTimeBySn(sn);
            Assert.assertEquals(130, onlineTime);
        }
        {
            String sn = OpenApiUtil.shortUUID();
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(modelNo);
            when(stateMachineService.getOnlineTimeByModelNo(modelNo)).thenReturn(null);
            int onlineTime = deviceStatusService.getOnlineTimeBySn(sn);
            Assert.assertEquals(130, onlineTime);
        }
        {
            String sn = OpenApiUtil.shortUUID();
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(modelNo);
            when(stateMachineService.getOnlineTimeByModelNo(modelNo)).thenReturn(0);
            int onlineTime = deviceStatusService.getOnlineTimeBySn(sn);
            Assert.assertEquals(130, onlineTime);
        }
        {
            String sn = OpenApiUtil.shortUUID();
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceManualService.getModelNoBySerialNumber(sn)).thenThrow(new RuntimeException("mock error"));
            int onlineTime = deviceStatusService.getOnlineTimeBySn(sn);
            Assert.assertEquals(130, onlineTime);
        }
        {
            String sn = OpenApiUtil.shortUUID();
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(modelNo);
            when(stateMachineService.getOnlineTimeByModelNo(modelNo)).thenThrow(new RuntimeException("mock error"));
            int onlineTime = deviceStatusService.getOnlineTimeBySn(sn);
            Assert.assertEquals(130, onlineTime);
        }
    }

}