package com.addx.iotcamera.service;

import com.addx.iotcamera.dao.StaleDataTransferDAO;
import com.addx.iotcamera.testutil.TestHelper;
import com.amazonaws.services.s3.AmazonS3;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.sql.DataSource;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class StaleDataSaveServiceTest {

    @InjectMocks
    private LibraryService libraryService;
    @Mock
    private StaleDataTransferService staleDataSaveService;
    private S3Service s3Service = new S3Service();

    private AmazonS3 s3Client;
    private DataSource dataSource;

    @Before
    public void init() {
        StaleDataTransferService staleDataSaveService = new StaleDataTransferService();
        staleDataSaveService.setQueryBatchNum(3);
        staleDataSaveService.setTransactionBachNum(8);
        staleDataSaveService.setSaveBatchNum(5);
        staleDataSaveService.setSaveHistoryBatchNum(2);
        staleDataSaveService.setSaveFunction(staleDataSaveService::saveTextToFile);
        staleDataSaveService.setSaveFunction((path, sql) -> System.out.println("\npath:" + path + "\n" + sql));

        TestHelper testHelper = TestHelper.getInstanceByLocal();
        dataSource = testHelper.getDataSource();
        staleDataSaveService.setDataSource(dataSource);
        s3Client = testHelper.getS3Client();
        s3Service.setS3Client(s3Client);
        staleDataSaveService.setS3Service(s3Service);
        StaleDataTransferDAO staleDataTransferDAO = testHelper.getMapper(StaleDataTransferDAO.class);
        staleDataSaveService.setStaleDataTransferDAO(staleDataTransferDAO);
        libraryService.setStaleDataTransferDAO(staleDataTransferDAO);
        libraryService.setS3Service(s3Service);
        when(this.staleDataSaveService.transferStaleData(any())).thenAnswer(AdditionalAnswers.delegatesTo(staleDataSaveService));
//        staleDataSaveService.setSaveFunction(s3Service::saveText);

        AtomicInteger count = new AtomicInteger(0);
        staleDataSaveService.setSaveFunction((path, sql) -> {
            System.out.println("\npath:" + path + "\n" + sql);
            if (count.getAndIncrement() % 2 == 1) {
                throw new RuntimeException("mock save to s3 error!");
            } else {
                s3Service.saveText(path, sql);
            }
        });
    }

    //    @Test
    public void test_saveLibraryStatus() throws Exception {
        List<Integer> ids = getIds("library_status", "library_id", 20);
//        when(s3Client.putObject(any())).thenReturn(null);
        StaleDataTransferService.StaleData<Integer> staleData = StaleDataTransferService.StaleData.<Integer>builder()
                .dbName("camera").tableName("library_status").primaryKeyName("library_id").ids(ids)
                .historyTableName("library_status_log").build();
        StaleDataTransferService.TransResult<Integer> result = staleDataSaveService.transferStaleData(staleData);
        log.info("");
    }

    private List<Integer> getIds(String tableName, String primaryKeyName, int num) throws SQLException {
        ResultSet rs = dataSource.getConnection()
                .prepareStatement("select `" + primaryKeyName + "` from camera." + tableName + " limit " + num + ";")
                .executeQuery();
        List<Integer> ids = new LinkedList<>();
        while (rs.next()) {
            ids.add(rs.getInt(1));
        }
        return ids;
    }

    //    @Test
    public void test_saveVideoLibrary() throws Exception {
        List<Integer> ids = getIds("video_library", "id", 50);
//        List<Integer> ids = Arrays.asList(441134, 441135, 441136, 441137, 441138, 441139, 441140, 441141, 441142, 441143, 441144, 441145, 441146, 441147, 441148, 441149, 441150, 441151, 441152);
//        when(s3Client.putObject(any())).thenReturn(null);
        StaleDataTransferService.StaleData<Integer> staleData = StaleDataTransferService.StaleData.<Integer>builder()
                .dbName("camera").tableName("video_library").primaryKeyName("id")
                .historyTableName("video_library_log").ids(ids).build();
        StaleDataTransferService.TransResult<Integer> result = staleDataSaveService.transferStaleData(staleData);
        ids.removeAll(result.getErrorIds());
        log.info("select * from camera.video_library where id in ({});", ids);
        log.info("select * from camera.video_library_log where id in ({});", ids);
    }

    //    @Test
    public void test_clearVideoLibraryLog() throws Exception {
        StaleDataTransferService.StaleData<Integer> staleData = StaleDataTransferService.StaleData.<Integer>builder()
                .dbName("camera").tableName("video_library_log").primaryKeyName("id")
                .limitNum(10).build();
        StaleDataTransferService.TransResult<Integer> result = staleDataSaveService.transferStaleData(staleData);
        log.info("");
    }

    //    @Test
    public void test_clearLibraryStatusLog() throws Exception {
        staleDataSaveService.transferStaleData(StaleDataTransferService.StaleData.<Integer>builder()
                .dbName("camera").tableName("library_status_log").primaryKeyName("library_id")
                .limitNum(13).build());
    }

//    @Test
//    public void test_clearVideoSlice() throws Exception {
//        List ids = Arrays.asList(460051, 460052, 460056, 460057, 460058);
//        StaleDataTransferService.TransResult result = staleDataSaveService
//                .transferStaleData(StaleDataTransferService.StaleData.<Integer>builder()
//                        .dbName("camera").tableName("video_slice").primaryKeyName("id")
//                        .queryKeyName("library_id").ids(ids).build());
//        log.info("{}", result);
//    }

    @Test
    public void test_libraryService_syncLibrary() throws Exception {
//        libraryService.syncLibrary(20);
        Assert.assertTrue(true);
    }

    @Test
    public void test_libraryService_clearLogTable() throws Exception {
//        libraryService.clearLogTable(20);
        Assert.assertTrue(true);
    }
}
