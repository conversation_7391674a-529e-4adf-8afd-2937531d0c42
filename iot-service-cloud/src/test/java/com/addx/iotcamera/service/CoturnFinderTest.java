package com.addx.iotcamera.service;

import com.addx.iotcamera.kiss.CoturnFinder;
import com.addx.iotcamera.kiss.bean.ChooseNodeCondition;
import com.addx.iotcamera.kiss.helper.NodeChooseHelper;
import com.addx.iotcamera.kiss.node.AbstractZoneNode;
import com.addx.iotcamera.util.Assert.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.LinkedList;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class CoturnFinderTest {

    @InjectMocks
    private CoturnFinder coturnFinder;
    @Mock
    private NodeChooseHelper nodeChooseHelper;

    @Test
    public void test_chooseCoturnNodesBySNAndCountryNo() {
        when(nodeChooseHelper.chooseNodes(any(), any())).thenReturn(new LinkedList<>());
        AssertUtil.assertThrowException(() -> coturnFinder.chooseCoturnNodesBySNAndCountryNo("sn", new ChooseNodeCondition()));
    }

    @Test
    public void test_AbstractZoneNode_purposeExpToSet() {
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList("normal")), AbstractZoneNode.purposeExpToSet(null));
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList("normal")), AbstractZoneNode.purposeExpToSet(""));
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList()), AbstractZoneNode.purposeExpToSet(";"));
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList("tcpdump")), AbstractZoneNode.purposeExpToSet("tcpdump"));
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList("tcpdump")), AbstractZoneNode.purposeExpToSet("tcpdump;"));
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList("tcpdump")), AbstractZoneNode.purposeExpToSet(";tcpdump"));
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList("tcpdump")), AbstractZoneNode.purposeExpToSet(";tcpdump;"));
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList("tcpdump")), AbstractZoneNode.purposeExpToSet(";tcpdump;;"));
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList("tcpdump")), AbstractZoneNode.purposeExpToSet(" tcpdump "));
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList("tcpdump")), AbstractZoneNode.purposeExpToSet("; tcpdump ;"));
        Assert.assertEquals(new LinkedHashSet<>(Arrays.asList("normal", "tcpdump")), AbstractZoneNode.purposeExpToSet("normal;tcpdump"));
    }
}
