package com.addx.iotcamera.service.proto;

import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.enums.ProtoMsgName;
import com.addx.iotcamera.mqtt.enums.ERequestAction;
import com.addx.iotcamera.service.proto_msg.ProtoMsgConvertor;
import com.addx.iotcamera.service.proto_msg.ProtoMsgService;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.proto.deviceMsg.PbDeviceSupport;
import org.addx.iot.common.proto.deviceMsg.PbHttpToken;
import org.addx.iot.common.utils.PhosUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.addx.iotcamera.service.proto.ProtoBuildHelper.getResponseTypeName;
import static com.addx.iotcamera.service.proto.ProtoBuildHelper.getTypeName;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class ProtoDeviceMsgServiceTest {

    @InjectMocks
    private ProtoMsgService protoDeviceMsgService;

    public static final List<String> earlyMsgNames = new LinkedList<>();

    static {
        // 2周以内的所有/deviceMsg/*请求
        String linesText = "" +
                "{uri=\"/deviceMsg/SDCardFormatReq\"}\n" +
                "{uri=\"/deviceMsg/aiEdgeEvent\"}\n" +
                "{uri=\"/deviceMsg/bindOperation\"}\n" +
                "{uri=\"/deviceMsg/connection\"}\n" +
                "{uri=\"/deviceMsg/detectPirResult\"}\n" +
                "{uri=\"/deviceMsg/deviceCall\"}\n" +
                "{uri=\"/deviceMsg/deviceSupport\"}\n" +
                "{uri=\"/deviceMsg/doorbellPress\"}\n" +
                "{uri=\"/deviceMsg/doorbellRemove\"}\n" +
                "{uri=\"/deviceMsg/dormancyStatus\"}\n" +
                "{uri=\"/deviceMsg/floodlightStatus\"}\n" +
                "{uri=\"/deviceMsg/httpToken\"}\n" +
                "{uri=\"/deviceMsg/liveStatistics\"}\n" +
                "{uri=\"/deviceMsg/lowBattery\"}\n" +
                "{uri=\"/deviceMsg/netTest\"}\n" +
                "{uri=\"/deviceMsg/otaFinish\"}\n" +
                "{uri=\"/deviceMsg/otaRequest\"}\n" +
                "{uri=\"/deviceMsg/otaRetry\"}\n" +
                "{uri=\"/deviceMsg/otaSuccess\"}\n" +
                "{uri=\"/deviceMsg/pir\"}\n" +
                "{uri=\"/deviceMsg/powerStatistics\"}\n" +
                "{uri=\"/deviceMsg/queryRetainedMsg\"}\n" +
                "{uri=\"/deviceMsg/rotateCalibration\"}\n" +
                "{uri=\"/deviceMsg/shutDown\"}\n" +
                "{uri=\"/deviceMsg/shutDownConfirm\"}\n" +
                "{uri=\"/deviceMsg/sleep\"}\n" +
                "{uri=\"/deviceMsg/status\"}\n" +
                "{uri=\"/deviceMsg/syncApInfo\"}\n" +
                "{uri=\"/deviceMsg/wakeup\"}\n" +
                "{uri=\"/deviceMsg/wowConfig\"}";

        Pattern ptn = Pattern.compile("/deviceMsg/(\\w+)");
        Matcher matcher = ptn.matcher(linesText);
        List<String> msgNames = new ArrayList<>();
        while (matcher.find()) {
            msgNames.add(matcher.group(1));
        }
        earlyMsgNames.addAll(msgNames);
    }

    //    @Test
    @SneakyThrows
    public void test_EDeviceMsgName() {
        List<String> msgNames = new LinkedList<>();
        Arrays.stream(ERequestAction.values()).map(it -> it.getValue()).forEach(msgNames::add);
        msgNames.remove(ERequestAction.reportEvent.getValue());
        Arrays.stream(EReportEvent.values()).map(it -> it.getMsgName()).forEach(msgNames::add);

        for (String msgName : msgNames) {
            String msgName2 = msgName.substring(0, 1).toUpperCase() + msgName.substring(1);
            String msgNameResp = msgName + "Response";
            String msgNameResp2 = msgNameResp.substring(0, 1).toUpperCase() + msgNameResp.substring(1);

            String pbMsgCls = "Pb" + msgName2 + ".class";
            String pbMsgResponseCls = "Pb" + msgNameResp2 + ".class";
            ERequestAction actionEnum = ERequestAction.findByValue(msgName);
            EReportEvent eventEnum = EReportEvent.msgNameOf(msgName);
            if (actionEnum == null && eventEnum != null) actionEnum = ERequestAction.reportEvent;

            String action = actionEnum != null ? ("ERequestAction." + actionEnum.name()) : "null";
            String event = eventEnum != null ? ("EReportEvent." + eventEnum.name()) : "null";

            System.out.printf("%s(%s, %s, %s, %s),%n"
                    , msgName, action, event, pbMsgCls, pbMsgResponseCls);
        }
    }

    @Test
    @SneakyThrows
    public void test() {
        log.info("ERequestAction.values().length={}", ERequestAction.values().length); // 25
        log.info("EReportEvent.values().length={}", EReportEvent.values().length); // 11
        log.info("DeviceMsgName.values().length={}", ProtoMsgName.values().length); // 36
        log.info("earlyMsgNames.size()={}", earlyMsgNames.size()); // 30
//        Assert.assertEquals(ERequestAction.values().length - 1
//                        + EReportEvent.values().length
//                        + 3 // connection,status,queryRetainedMsg
//                , earlyMsgNames.size());
        for (ProtoMsgName msgName : ProtoMsgName.values()) {
            Assert.assertNotNull(msgName.getProtoMsgCls());
            Assert.assertNotNull(msgName.getProtoResponseMsgCls());
            Assert.assertEquals(getTypeName(msgName.getMsgName()), msgName.getProtoMsgCls().getSimpleName());
            Assert.assertEquals(getResponseTypeName(msgName.getMsgName()), msgName.getProtoResponseMsgCls().getSimpleName());
        }

        LinkedList<ProtoMsgName> deviceMsgNames2 = new LinkedList<>(Arrays.asList(ProtoMsgName.values()));
        deviceMsgNames2.removeIf(it -> earlyMsgNames.contains(it.getMsgName()));
        log.info("prometheus统计到中没有，EDeviceMsgNames中定义中有的:{}", deviceMsgNames2);
    }

    @Test
    public void test_httpToken() {
        ProtoMsgConvertor<PbHttpToken> convertor = (ProtoMsgConvertor<PbHttpToken>) ProtoMsgName.httpToken.getProtoMsgConvertor();

        PbHttpToken pbHttpToken = PbHttpToken.newBuilder()
                .setSerialNumber(OpenApiUtil.shortUUID())
                .setSignature("123")
                .setName("httpToken")
                .setTime(PhosUtils.getUTCStamp())
                .setId(4)
                .build();

        log.info("test {}: build new pbMsg:\n{}", convertor.getMsgName(), pbHttpToken);
        String jsonStr = convertor.printJsonStr(pbHttpToken);
        log.info("test {}: pbMsg to JsonStr:\n{}", convertor.getMsgName(), jsonStr);
        PbHttpToken pbHttpToken2 = convertor.parseJsonStr(jsonStr);
        log.info("test {}: JsonStr to PbMsg:\n{}", convertor.getMsgName(), pbHttpToken);
        Assert.assertEquals(pbHttpToken, pbHttpToken2);
    }

    @Test
    public void test_deviceSupport() {
        ProtoMsgConvertor<PbDeviceSupport> convertor = (ProtoMsgConvertor<PbDeviceSupport>) ProtoMsgName.deviceSupport.getProtoMsgConvertor();

        String jsonStr = "{\"name\":\"deviceSupport\",\"time\":1732728111,\"id\":0,\"value\":{\"deviceSupportResolution\":\"2304x1296,640x360,auto\",\"deviceSupportAlarm\":1,\"deviceSupportMirrorFlip\":true,\"supportWebrtc\":1,\"supportRecLamp\":0,\"supportVoiceVolume\":1,\"supportAlarmVolume\":1,\"supportLiveAudioToggle\":1,\"supportRecordingAudioToggle\":1,\"supportLiveSpeakerVolume\":0,\"supportAlarmWhenRemoveToggle\":0,\"postMotionDetectResult\":0,\"killKeepAlive\":1,\"supportCryDetect\":0,\"deviceDormancySupport\":1,\"p2pConnMgtStrategy\":1,\"supportAlexaWebrtc\":1,\"supportChangeCodec\":1,\"supportPirCooldown\":1,\"resetVolSupport\":0,\"streamProtocol\":\"webrtc\",\"keepAliveProtocol\":\"websocket\",\"audioCodectype\":\"aac\",\"devicePersonDetect\":0,\"supportChargeAutoPowerOn\":0,\"deviceSupportLanguage\":\"cn,en,ja,de,ru,fr,it,es,pt\",\"supportDoorBellRingKey\":\"\",\"supportPirSliceReport\":1,\"supportMechanicalDingDong\":0,\"antiflickerSupport\":1,\"canStandby\":0,\"quantityCharge\":0,\"canRotate\":1,\"supportRotateCalibration\":true,\"supportMotionTrack\":1,\"supportGoogleStorage\":1,\"isShield\":false,\"supportWifiPowerLevel\":true,\"supportNetTest\":true,\"isFilter\":false,\"supportOci\":1,\"supportCos\":1,\"flash\":\"w25q64\",\"supportUnlimitedWebsocket\":true,\"supportOtaAutoUpgrade\":1,\"supportSdCardCooldown\":1,\"supportSdCardVideoModes\":\"eventual,continual\",\"supportSdCardCooldownSeconds\":\"10s,30s,60s,180s,300s\",\"supportDeviceCall\":1,\"supportPersonAi\":1,\"supportPetAi\":1,\"supportPassbySunshine\":0}}";
        log.info("test {}: build new jsonStr:\n{}", convertor.getMsgName(), jsonStr);
        PbDeviceSupport pbDeviceSupport = convertor.parseJsonStr(jsonStr);
        log.info("test {}: JsonStr to PbMsg:\n{}", convertor.getMsgName(), pbDeviceSupport);
        String jsonStr2 = convertor.printJsonStr(pbDeviceSupport);
        log.info("test {}: pbMsg to jsonStr:\n{}", convertor.getMsgName(), jsonStr2);
        PbDeviceSupport pbDeviceSupport2 = convertor.parseJsonStr(jsonStr2);
        log.info("test {}: JsonStr to PbMsg:\n{}", convertor.getMsgName(), pbDeviceSupport2);
        AssertUtil.assertPbMsgEquals(pbDeviceSupport, pbDeviceSupport2);
        Assert.assertTrue(pbDeviceSupport.equals(pbDeviceSupport2));
//        AssertUtil.assertEquals(JSON.parseObject(jsonStr),JSON.parseObject(jsonStr2));

    }


}
