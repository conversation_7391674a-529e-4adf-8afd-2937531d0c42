package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.RotateCalibrationRequest;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.Map;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RotateServiceTest {

    @InjectMocks
    private RotateService rotateService;

    @Mock
    private RedisService redisService;

    @Mock
    private VernemqPublisher vernemqPublisher;

    @Mock
    private MqttSender mqttSender;

    @Before
    public void before() {
    }

    @Test
    public void test_rotateCalibration() throws Exception {
        VernemqPublisher vernemqPublisher = new VernemqPublisher();
        Field mqttSenderField = VernemqPublisher.class.getDeclaredField("mqttSender");
        mqttSenderField.setAccessible(true);
        mqttSenderField.set(vernemqPublisher, mqttSender);
        vernemqPublisher.init();

        RotateCalibrationRequest request = new RotateCalibrationRequest();
        request.setSerialNumber("sn_01");
        request.setNeedCalibration(true);
        Result result = rotateService.rotateCalibrationDevice(request);
        Assert.assertTrue(!(Boolean)(((Map)result.getData()).get("calibrationFinished")));

        request.setNeedCalibration(false);
        result = rotateService.rotateCalibrationDevice(request);
        Assert.assertTrue((Boolean)(((Map)result.getData()).get("calibrationFinished")));

        Mockito.when(redisService.get(Mockito.any())).thenReturn("false");
        result = rotateService.rotateCalibrationDevice(request);
        Assert.assertTrue(!(Boolean)(((Map)result.getData()).get("calibrationFinished")));

        mqttSenderField.set(vernemqPublisher, null);
        vernemqPublisher.init();

        rotateService.rotateCalibrationDeviceTimeout("sn_01");
        Mockito.verify(redisService, Mockito.atLeastOnce()).delete(Mockito.anyString());
    }
}
