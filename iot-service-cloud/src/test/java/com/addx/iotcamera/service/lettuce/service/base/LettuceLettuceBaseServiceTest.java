package com.addx.iotcamera.service.lettuce.service.base;

import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.*;

import java.util.*;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LettuceLettuceBaseServiceTest {


    String key = "a";

    String field = "a";

    @InjectMocks
    LettuceLettuceBaseService<String, String> lettuceLettuceBaseService;

    @Mock
    RedisTemplate<String, String> redisTemplate;
    @Mock
    RedisAdvancedClusterCommands<String, String> lettuceCommand;
    @Mock
    ValueOperations<String, String> valueOperations;
    @Mock
    ListOperations<String, String> listOperations;
    @Mock
    SetOperations<String, String> setOperations;
    @Mock
    ZSetOperations<String, String> zSetOperations;
    @Mock
    HashOperations<String, Object, Object> hashOperations;
    @Mock
    ZSetOperations.TypedTuple<String> typedTuple;


    @Before
    public void before() {
        // mock数据
        List<String> resultList = Collections.singletonList("a");
        Set<String> resultSet = Collections.singleton("a");
        Set<ZSetOperations.TypedTuple<String>> typedTuples = Collections.singleton(new DefaultTypedTuple<>("a", 1d));
        Map<Object, Object> resultMap = new HashMap<>();
        resultMap.put("a", "a");
        Map<String, String> resultLettuceMap = new HashMap<>();
        // mock redis
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(redisTemplate.opsForList()).thenReturn(listOperations);
        when(redisTemplate.opsForSet()).thenReturn(setOperations);
        when(redisTemplate.opsForZSet()).thenReturn(zSetOperations);
        when(redisTemplate.opsForHash()).thenReturn(hashOperations);

        when(redisTemplate.opsForValue().get(key)).thenReturn("a");
        when(redisTemplate.opsForList().range(key, 0, -1)).thenReturn(resultList);
        when(redisTemplate.opsForSet().members(key)).thenReturn(resultSet);
        when(redisTemplate.opsForZSet().rangeWithScores(key, 0, -1)).thenReturn(typedTuples);
        when(redisTemplate.opsForHash().entries(key)).thenReturn(resultMap);
        when(redisTemplate.getExpire(key)).thenReturn(1L);
        //mock lettuce
        when(lettuceCommand.get(key)).thenReturn("a");
        when(lettuceCommand.lrange(key, 0, -1)).thenReturn(resultList);
        when(lettuceCommand.smembers(key)).thenReturn(Collections.singleton("a"));
        when(lettuceCommand.zrangeWithScores(key, 0, -1)).thenReturn(new ArrayList<>());
        when(lettuceCommand.hgetall(key)).thenReturn(resultLettuceMap);
    }

    @Test
    public void getRedisToLettuceValue() {
        lettuceLettuceBaseService.getRedisToLettuceValue(key, redisTemplate, lettuceCommand);

        when(redisTemplate.opsForValue().get(key)).thenReturn("a");
        when(lettuceCommand.get(key)).thenReturn(null);
        lettuceLettuceBaseService.getRedisToLettuceValue(key, redisTemplate, lettuceCommand);
    }

    @Test
    public void getRedisToLettuceList() {
        lettuceLettuceBaseService.getRedisToLettuceList(key, redisTemplate, lettuceCommand);

        when(redisTemplate.opsForList().range(key, 0, -1)).thenReturn(Collections.singletonList("a"));
        when(lettuceCommand.get(key)).thenReturn(null);
        lettuceLettuceBaseService.getRedisToLettuceList(key, redisTemplate, lettuceCommand);
    }

    @Test
    public void getRedisToLettuceSet() {
        lettuceLettuceBaseService.getRedisToLettuceSet(key, redisTemplate, lettuceCommand);

        when(redisTemplate.opsForSet().members(key)).thenReturn(Collections.singleton("a"));
        when(lettuceCommand.get(key)).thenReturn(null);
        lettuceLettuceBaseService.getRedisToLettuceSet(key, redisTemplate, lettuceCommand);
    }

    @Test
    public void getRedisToLettuceZSet() {
        lettuceLettuceBaseService.getRedisToLettuceZSet(key, redisTemplate, lettuceCommand);

        Set<ZSetOperations.TypedTuple<String>> typedTuples = new HashSet<>();
        typedTuples.add(typedTuple);
        when(redisTemplate.opsForZSet().rangeWithScores(key, 0, -1)).thenReturn(typedTuples);
        when(lettuceCommand.get(key)).thenReturn(null);
        lettuceLettuceBaseService.getRedisToLettuceZSet(key, redisTemplate, lettuceCommand);
    }

    @Test
    public void getRedisToLettuceObjMap() {
        lettuceLettuceBaseService.getRedisToLettuceObjMap(key, redisTemplate, lettuceCommand);

        Map<Object, Object> map = new HashMap<>();
        map.put("a", "a");
        when(redisTemplate.opsForHash().entries(key)).thenReturn(map);
        when(lettuceCommand.get(key)).thenReturn(null);
        lettuceLettuceBaseService.getRedisToLettuceObjMap(key, redisTemplate, lettuceCommand);
    }

    @Test
    public void getRedisToLettuceMap() {
        lettuceLettuceBaseService.getRedisToLettuceMap(key, redisTemplate, lettuceCommand);

        Map<Object, Object> map = new HashMap<>();
        map.put("a", "a");
        when(redisTemplate.opsForHash().entries(key)).thenReturn(map);
        when(lettuceCommand.get(key)).thenReturn(null);
        lettuceLettuceBaseService.getRedisToLettuceMap(key, redisTemplate, lettuceCommand);
    }

    @Test
    public void getRedisToLettuceObjMapValue() {
        lettuceLettuceBaseService.getRedisToLettuceObjMapValue(key, redisTemplate, lettuceCommand);

        Map<Object, Object> map = new HashMap<>();
        map.put("a", "a");
        when(redisTemplate.opsForHash().entries(key)).thenReturn(map);
        when(lettuceCommand.get(key)).thenReturn(null);
        lettuceLettuceBaseService.getRedisToLettuceObjMapValue(key, redisTemplate, lettuceCommand);
    }

    @Test
    public void hasKey() {
        boolean b = lettuceLettuceBaseService.hasKey(key, redisTemplate, lettuceCommand);
        System.out.println(b);
        when(redisTemplate.type(key)).thenReturn(DataType.STRING);
        b = lettuceLettuceBaseService.hasKey(key, redisTemplate, lettuceCommand);
        System.out.println(b);
        when(lettuceCommand.exists(key)).thenReturn(0L);
        when(redisTemplate.type(key)).thenReturn(DataType.LIST);
        b = lettuceLettuceBaseService.hasKey(key, redisTemplate, lettuceCommand);
        System.out.println(b);
        when(redisTemplate.type(key)).thenReturn(DataType.SET);
        b = lettuceLettuceBaseService.hasKey(key, redisTemplate, lettuceCommand);
        System.out.println(b);
        when(redisTemplate.type(key)).thenReturn(DataType.ZSET);
        b = lettuceLettuceBaseService.hasKey(key, redisTemplate, lettuceCommand);
        System.out.println(b);
        when(redisTemplate.type(key)).thenReturn(DataType.HASH);
        b = lettuceLettuceBaseService.hasKey(key, redisTemplate, lettuceCommand);
        System.out.println(b);
    }

    @Test
    public void dataTransfer() {
        boolean b = lettuceLettuceBaseService.dataTransfer(key, redisTemplate, lettuceCommand);
        System.out.println(b);
    }
}