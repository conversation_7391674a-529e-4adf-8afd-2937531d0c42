package com.addx.iotcamera.service.firmware;

import com.addx.iotcamera.bean.db.device.FirmwareSubVersionTableDO;
import com.addx.iotcamera.dao.IFirmwareSubVersionDAO;
import com.addx.iotcamera.service.device.FirmwareSubVersionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * description:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2024/1/10 14:06
 */
@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class FirmwareSubVersionServiceTest {
    @InjectMocks
    private FirmwareSubVersionService firmwareSubVersionService;
    @Mock
    private IFirmwareSubVersionDAO firmwareSubVersionDAO;

    @Test
    public void testGetSubversions(){
        List<FirmwareSubVersionTableDO> list = new ArrayList<>();
        FirmwareSubVersionTableDO firmwareSubVersionTableDO = new FirmwareSubVersionTableDO();
        firmwareSubVersionTableDO.setApplication("iot-local");
        when(firmwareSubVersionDAO.queryFirmwareSubVersion(any(), any())).thenReturn(list);
        assertEquals(list, firmwareSubVersionService.getSubversions("0.0.1","model1"));

    }
}
