package com.addx.iotcamera.service.proto;


import com.addx.iotcamera.enums.ProtoMsgName;
import com.addx.iotcamera.mqtt.enums.EInputTopicType;
import com.addx.iotcamera.mqtt.enums.EOutputTopicType;
import com.addx.iotcamera.mqtt.enums.ERequestAction;
import com.addx.iotcamera.service.proto_msg.ProtoMsgTrans;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.GeneratedMessage;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.proto.deviceMsg.*;
import org.addx.iot.common.utils.PhosUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.GZIPOutputStream;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ProtoBuildHelperTest {

    public GeneratedMessage sendHttpProtoMsg(String token, GeneratedMessage reqPbMsg) {
//        return sendHttpProtoMsg("https://api-staging-us.addx.live", token, reqPbMsg);
//        return sendHttpProtoMsg("https://api-pre-eu.vicoo.tech", token, reqPbMsg);
        return sendHttpJsonMsg("https://api-pre-eu.vicoo.tech", token, reqPbMsg);
    }

    public GeneratedMessage sendHttpJsonMsg(String rootUrl, String token, GeneratedMessage reqPbMsg) {
        ProtoMsgName msgName = ProtoMsgName.protoMsgClsOf(reqPbMsg.getClass());
        final String url = rootUrl + "/deviceMsg/" + msgName.getMsgName();
        byte[] respBytes = null;
        GeneratedMessage respPbMsg;
        try {
            String jsonStr = msgName.getProtoMsgConvertor().printJsonStr(reqPbMsg);
            respBytes = sendHttpMsg(url, token, "application/json", false, jsonStr.getBytes(StandardCharsets.UTF_8));
            if (respBytes != null) {
                String respStr = new String(respBytes, StandardCharsets.UTF_8);
                respPbMsg = msgName.getProtoResponseMsgConvertor().parseJsonStr(respStr);
                log.info("sendHttpJsonMsg end! url={},respPbMsg=\n{}", url, respPbMsg);
                return respPbMsg;
            } else {
                log.error("sendHttpJsonMsg fail! url={}", url);
                return null;
            }
        } catch (Throwable e) {
            String respBytesBase64 = respBytes != null ? Base64.getEncoder().encodeToString(respBytes) : null;
            log.error("sendHttpJsonMsg error! url={},respBytesBase64={}", url, respBytesBase64, e);
            return null;
        }
    }

    public GeneratedMessage sendHttpProtoMsg(String rootUrl, String token, GeneratedMessage reqPbMsg) {
        ProtoMsgName msgName = ProtoMsgName.protoMsgClsOf(reqPbMsg.getClass());
        final String url = rootUrl + "/deviceMsg/" + msgName.getMsgName();
        byte[] respBytes = null;
        GeneratedMessage respPbMsg;
        try {
            respBytes = sendHttpMsg(url, token, "application/protobuf", false, reqPbMsg.toByteArray());
            if (respBytes != null) {
                respPbMsg = msgName.getProtoResponseMsgConvertor().parseFrom(respBytes);
                log.info("sendHttpProtoMsg end! url={},respPbMsg=\n{}", url, respPbMsg);
                return respPbMsg;
            } else {
                log.error("sendHttpProtoMsg fail! url={}", url);
                return null;
            }
        } catch (Throwable e) {
            String respBytesBase64 = respBytes != null ? Base64.getEncoder().encodeToString(respBytes) : null;
            log.error("sendHttpProtoMsg error! url={},respBytesBase64={}", url, respBytesBase64, e);
            return null;
        }
    }

    public byte[] sendHttpMsg(String url, String token, String contentType, boolean isGzip, byte[] bytes) {
        int respCode = -1;
        byte[] respBytes = null;
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            post.addHeader("Content-Type", contentType);
            if (token != null) {
                post.addHeader("Authorization", token);
            }
            if (isGzip) {
                post.addHeader("Accept-Encoding", "gzip");
                post.addHeader("Content-Encoding", "gzip");
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                try (GZIPOutputStream gos = new GZIPOutputStream(baos)) {
                    gos.write(bytes);
                }
                post.setEntity(new ByteArrayEntity(baos.toByteArray()));
            } else {
                post.setEntity(new ByteArrayEntity(bytes));
            }
            try (CloseableHttpResponse resp = client.execute(post)) {
                respCode = resp.getStatusLine().getStatusCode();
                if (respCode == HttpStatus.SC_OK) {
                    respBytes = EntityUtils.toByteArray(resp.getEntity());
                }
//                log.info("sendHttpProtoMsg end! url={},respCode={},respBytes=\n{}", url, respCode, Base64.getEncoder().encodeToString(respBytes));
                return respBytes;
            }
        } catch (Throwable e) {
            String respBytesBase64 = respBytes != null ? Base64.getEncoder().encodeToString(respBytes) : null;
            log.error("sendHttpProtoMsg error! url={},respCode={},respBytesBase64={}", url, respCode, respBytesBase64, e);
            return null;
        }
    }

    @Test
    @SneakyThrows
    public void test3() {
        {
            String jsonStr = "{\"data\":{\"serialNumber\":\"59e4e285013dda5613834b78baac414b\",\"ai\":{\"enable\":false},\"dns\":{\"vmq-staging-us.addx.live\":[\"54.221.206.204\"],\"api-staging-us.vicohome.io\":[\"18.205.187.151\"]},\"mediaType\":\"image\",\"eventAnalyticsSwitchOn\":true,\"eventAnalyticsEndPoint\":\"https://us-test-log.theunismart.com\",\"eventAnalyticsNamespace\":\"\",\"bindInfo\":{\"adminId\":1018082},\"common\":{\"canAutoOtaTime\":0},\"iotService\":{\"baseUrl\":\"https://api-staging-us.vicohome.io\"},\"mqtt\":{\"uri\":\"tcp://vmq-staging-us.addx.live\"},\"files\":[],\"dnsServer\":[\"9.9.9.9\",\"149.112.112.112\"],\"uploadAIImage\":{\"endpoint\":\"https://ai-staging-us.addx.live/ai-cloud/deeplens/stream/imageInfer\",\"enable\":false,\"intervalInMs\":2000,\"aiCloudParam\":\"COKRPhIFdmljb28aEnsidmlwQ291bnRUeXBlIjowfSIleyJvdXRFbmNvZGVUeXBlIjowLCJ1c2VyU2V0QXoiOmZhbHNlfSoCQ05KKQoOYTR4LXN0YWdpbmctdXMSE2FpLXNhYXMtb3V0LXN0b3JhZ2UaAlMzUhlzdGFnaW5nLXVzLXZpZGVvLWdlbmVyYXRl\"}},\"msg\":\"Success\",\"result\":0}";
            GeneratedMessage pbMsg = parseResponse(ProtoMsgName.config, jsonStr);
            log.info("config pbMsg:{}", pbMsg);
        }
        {
            String jsonStr = "{\"data\":{\"name\":\"syncApInfo\",\"id\":1,\"time\":1734000180,\"value\":{\"apInfoVersion\":\"01\",\"apInfo\":\"{\\\"ssid\\\":\\\"IPC_G75R71Y70037\\\",\\\"password\\\":\\\"\\\",\\\"safePassword\\\":\\\"2LwtAuKo\\\",\\\"asideServerIp\\\":\\\"***********\\\", \\\"asideServerPort\\\":\\\"23450\\\"}\"}},\"msg\":\"Success\",\"result\":0}";
            GeneratedMessage pbMsg = parseResponse(ProtoMsgName.syncApInfo, jsonStr);
            log.info("syncApInfo pbMsg:{}", pbMsg);
        }
    }

    @SneakyThrows
    public static GeneratedMessage parse(ProtoMsgName msgName, String jsonStr) {
        JSONObject result = JSON.parseObject(jsonStr);
        JSONObject result3 = ProtoMsgTrans.msgName2ParsePreHandler.get(msgName).apply(result);
        return msgName.getProtoMsgConvertor().parseJsonStr(JSON.toJSONString(result3));
    }

    @SneakyThrows
    public static GeneratedMessage parseResponse(ProtoMsgName msgName, String jsonStr) {
        JSONObject result = JSON.parseObject(jsonStr);
        JSONObject result3 = ProtoMsgTrans.msgName2ParsePreHandler.get(msgName).apply(result);
        return msgName.getProtoResponseMsgConvertor().parseJsonStr(JSON.toJSONString(result3));
    }

//    @Test
    @SneakyThrows
    public void test_httpToken() {
        String token = null;
        {
            JSONObject jsonObj = JSON.parseObject("{\n" +
                    "  \"id\": 0,\n" +
                    "  \"name\": \"httpToken\",\n" +
                    "  \"serialNumber\": \"3da5bca723cd60c178d333c53e95c4a4\",\n" +
                    "  \"signature\": \"L7qak6-kBUcN4F_wt9k6CkJx_fQ\\u003d\",\n" +
                    "  \"time\": 0\n" +
                    '}');
            Integer time = PhosUtils.getUTCStamp();
            String signature = OpenApiUtil.sign(jsonObj.getString("serialNumber") + time, "N38Ywi2YR4qddOqXBjDRhQ==");
            jsonObj.put("time", time);
            jsonObj.put("signature", signature);
            GeneratedMessage req = ProtoMsgName.httpToken.getProtoMsgConvertor().parseJsonStr(jsonObj.toJSONString());
            PbHttpTokenResponse resp = (PbHttpTokenResponse) sendHttpProtoMsg(null, req);
            Assert.assertEquals(0, resp.getResult());
            token = resp.getData().getValue().getToken();
        }
        token = "xyz";
        {
            GeneratedMessage req = ProtoMsgName.syncApInfo.getProtoMsgConvertor().parseJsonStr("{\n" +
                    "  \"time\": 1733996101,\n" +
                    "  \"id\": 1,\n" +
                    "  \"value\": {\n" +
                    "    \"apInfoVersion\": \"01\"\n" +
                    "  },\n" +
                    "  \"name\": \"syncApInfo\"\n" +
                    "}");
            PbSyncApInfoResponse resp = (PbSyncApInfoResponse) sendHttpProtoMsg(token, req);
            Assert.assertEquals(0, resp.getResult());
        }
        {
            GeneratedMessage req = ProtoMsgName.config.getProtoMsgConvertor().parseJsonStr("{}");
            PbConfigResponse resp = (PbConfigResponse) sendHttpProtoMsg(token, req);
            Assert.assertEquals(0, resp.getResult());
        }
        {
            GeneratedMessage req = ProtoMsgName.setting.getProtoMsgConvertor().parseJsonStr("{}");
            PbSettingResponse resp = (PbSettingResponse) sendHttpProtoMsg(token, req);
            Assert.assertEquals(0, resp.getResult());
        }
        {
            GeneratedMessage req = ProtoMsgName.dormancyPlanSetting.getProtoMsgConvertor().parseJsonStr("{}");
            PbDormancyPlanSettingResponse resp = (PbDormancyPlanSettingResponse) sendHttpProtoMsg(token, req);
            Assert.assertEquals(0, resp.getResult());
        }
        {
            GeneratedMessage req = ProtoMsgName.dormancyPlanSetting.getProtoMsgConvertor().parseJsonStr("{}");
            String url = "https://api-staging-us.addx.live/deviceMsg/dormancy_plan_setting";
            PbDormancyPlanSettingResponse resp = (PbDormancyPlanSettingResponse) sendHttpProtoMsg(url, token, req);
            Assert.assertEquals(0, resp.getResult());
        }
        {
            GeneratedMessage req = ProtoMsgName.syncApInfo.getProtoMsgConvertor().parseJsonStr("{\n" +
                    "  \"time\": 1733996101,\n" +
                    "  \"id\": 1,\n" +
                    "  \"value\": {\n" +
                    "    \"apInfoVersion\": \"01\"\n" +
                    "  },\n" +
                    "  \"name\": \"syncApInfo\"\n" +
                    "}");
            String url = "https://api-staging-us.addx.live/deviceMsg/syncApInfo";
            String jsonStr = ProtoMsgName.syncApInfo.getProtoMsgConvertor().printJsonStr(req);
            byte[] reqBodyBytes = jsonStr.getBytes(StandardCharsets.UTF_8);
            log.info("syncApInfo reqBodyBytesSize={}", reqBodyBytes.length);
            byte[] respBytes = sendHttpMsg(url, token, "application/json", true, reqBodyBytes);
            Assert.assertNotNull(respBytes);
            String respBodyStr = new String(respBytes, StandardCharsets.UTF_8);
            log.info(respBodyStr);
        }
        log.info("");
    }

    //    @Test
    @SneakyThrows
    public void test_includingDefaultValueFields() {
        PbStatus pbStatus = PbStatus.newBuilder().setStatusList(PbStatus.PbStatusList.newBuilder()
                .setCodec("codec")
                .build()).build();
        String str1 = JsonFormat.printer().print(pbStatus);
        String str2 = JsonFormat.printer().print(pbStatus);
        log.info("str1:{}", str1);
        log.info("str2:{}", str2);
    }

    @Test
    public void test_deviceSupport() throws InvalidProtocolBufferException {
//        String base64Str = "CoQECAEQARgAIAAoATgBQABIAFAAWAFgAHgBgAEBiAEBkAEAmAEBogESZXZlbnR1YWwsY29udGludWFsqgEHZGVmYXVsdKoBBXR1cmJvsAEBugEfcGVyc29uLHBldCx2ZWhpY2xlLHBhY2thZ2UsYmlyZMABAcgBAdIBBjEuOS4xMNgBAOABAOgBAPIBFjIzMDR4MTI5Niw2NDB4MzYwLGF1dG+AAgGIAgCiAgCwAgG4AgHAAgHIAgHQAgDYAgHgAgHoAgGCAwNhYWOKAyYxMHMsMzBzLDYwcywxODBzLDMwMHMsNjAwcywxMjAwcywxODAwc5ADAKADArADALgDAMIDAzEwc8IDAzMwc8IDAzYwc8IDBDE4MHPCAwQzMDBzwgMENjAwc8IDBTEyMDBzwgMFMTgwMHPYAwHgAwDwAwH4AwCABACKBAMxMHOKBAMxNXOKBAMyMHOKBAM2MHOKBAQxMjBzigQEMTgwc5AEAJoEGmNuLGVuLGphLGRlLHJ1LGZyLGl0LGVzLHB0qAQBsAQBuAQBwAQBygQJd2Vic29ja2V00AQB4AQA6gQGd2VicnRj8AQA+gQDMTBz+gQDMTVz+gQDMjBz+gQDNjBz+gQEMTIwc/oEBDE4MHOIBQCQBQGgBQGqBQlQWTI1UTY0SEGwBQG4BQHABQDKBQRBSzFJ0AUB2AUAEMe39LoGGAAiDWRldmljZVN1cHBvcnQ=";
        String base64Str = "CrkECAEQARgAIAEoATgBQABIAFAAWAFgAGgBcgVQODI0TXgAgAEBiAEBkAEBmAEBogEIZXZlbnR1YWyqAQdkZWZhdWx0qgEFdHVyYm+wAQG6AR9wZXJzb24scGV0LHZlaGljbGUscGFja2FnZSxiaXJkwAEByAEB0gEGMS4xMC4w2AEA4AEA6AEA8gEXMzg0MHgyMTYwLDEyODB4NzIwLGF1dG+AAgGIAgCQAgGiAgCoAgGwAgG4AgHAAgHIAgHQAgHYAgHgAgHoAgHwAgH4AgGCAwNhYWOKAyYxMHMsMzBzLDYwcywxODBzLDMwMHMsNjAwcywxMjAwcywxODAwc5ADAZgDAaADALADALgDAMIDAzEwc8IDAzMwc8IDAzYwc8IDBDE4MHPCAwQzMDBzwgMENjAwc8IDBTEyMDBzwgMFMTgwMHPYAwHgAwDwAwH4AwGABAGKBAMxMHOKBAMxNXOKBAMyMHOKBAM2MHOKBAQxMjBzigQEMTgwc5AEAJoEEWRlLGVuLGZyLGl0LHB0LGVzqAQBsAQBuAQBwAQBygQJd2Vic29ja2V02gQHR0QxMzIwMOAEAOoEBndlYnJ0Y/AEAPoEAzEwc/oEAzE1c/oEAzIwc/oEAzYwc/oEBDEyMHP6BAQxODBziAUBkAUBmAUBoAUBqgUAsAUBuAUBwAUBygUESU43RNAFAdgFAeAFAegFAfAFAYAGAYgGAZIGBGF1dG+SBgIxc5IGAjJzkgYCNXOSBgMxMHOwBgEQ1OOzuwYYAiINZGV2aWNlU3VwcG9ydA==";
        byte[] bytes = Base64.getDecoder().decode(base64Str);
        PbDeviceSupport pbDeviceSupport = PbDeviceSupport.parseFrom(bytes);
        log.info("pbDeviceSupport:\n{}", pbDeviceSupport);
        GeneratedMessage pbMsg = ProtoMsgName.deviceSupport.getProtoMsgConvertor().parseFrom(bytes);
        log.info("pbMsg:\n{}", pbMsg);
//        log.info("comparePbMsg:\n{}", ProtoMsgValidateService.comparePbMsg(pbDeviceSupport, pbMsg));
        String str1 = JsonFormat.printer().print(pbMsg);
        String str2 = JsonFormat.printer().print(pbMsg);
        log.info("str1:{}", str1);
        log.info("str2:{}", str2);
//        log.info("field1:{},{}", pbDeviceSupport.hasField1(), pbDeviceSupport.getField1());
//        log.info("field2:{}", pbDeviceSupport.getField2());
//        Int32Value field3 = pbDeviceSupport.getField3();
//        log.info("field3:{},{}", pbDeviceSupport.hasField3(), pbDeviceSupport.getField3());
        int[] i = {0};
        pbDeviceSupport.getAllFields().forEach((fd, v) -> {
            log.info("{}:{},{}", i[0]++, fd, fd.hasDefaultValue(), v);
        });
    }

    @Test
    public void test_dormancyStatus() throws InvalidProtocolBufferException {
        String base64Str = "CIWK9boGEAIaDmRvcm1hbmN5U3RhdHVzIgQIABAA";
        byte[] bytes = Base64.getDecoder().decode(base64Str);
        PbDormancyStatus pbMsg = PbDormancyStatus.parseFrom(bytes);
        log.info("pbMsg:\n{}", pbMsg);
        String str1 = JsonFormat.printer().print(pbMsg);
        String str2 = JsonFormat.printer().print(pbMsg);
        log.info("str1:{}", str1);
        log.info("str2:{}", str2);
        log.info("pbMsg.getValue().hasStatus():{}", pbMsg.getValue().hasStatus());
        log.info("pbMsg.getValue().hasWakeupTime():{}", pbMsg.getValue().hasWakeupTime());
//        log.info("pbMsg.getValue().hasField():{}", pbMsg.getValue().hasField());
        pbMsg.getValue().getAllFields().forEach((pd, v) -> {
            log.info("{},{}", pd, v);
        });
    }

    @Test
    public void test_msgName_sourceMsgName() {
        for (ProtoMsgName msgName : ProtoMsgName.values()) {
            Assert.assertEquals(msgName.name(), msgName.getMsgName());
            String protoMsgClsName = "Pb" + msgName.getMsgName().substring(0, 1).toUpperCase() + msgName.getMsgName().substring(1);
            Assert.assertEquals(protoMsgClsName, msgName.getProtoMsgCls().getSimpleName());
            Assert.assertEquals(protoMsgClsName + "Response", msgName.getProtoResponseMsgCls().getSimpleName());
            if (msgName == ProtoMsgName.baseStationBindOperation) {
                Assert.assertNull(msgName.getSource().getMsgName());
                continue;
            }
            if (msgName == ProtoMsgName.dormancyPlanSetting) {
                Assert.assertEquals(ProtoMsgName.dormancyPlanSetting.name(), msgName.getMsgName());
                Assert.assertEquals(EOutputTopicType.DORMANCY_PLAN_SETTING.getValue(), msgName.getSource().getMsgName());
                continue;
            }
            if (msgName.getSource().getEvent() != null) {
                Assert.assertEquals(ERequestAction.reportEvent, msgName.getSource().getAction());
            }
            if (msgName.getSource().getAction() != null) {
                Assert.assertEquals(EInputTopicType.REQUEST, msgName.getSource().getInputTopicType());
            }
            if (msgName.getSource().getMsgName() != null) {
                Assert.assertEquals(msgName.getMsgName(), msgName.getSource().getMsgName());
            }
        }
    }

    @Test
    @SneakyThrows
    public void test_setting() {
        String jsonStr = "{\"msg\":\"Success\",\"result\":0,\"data\":{\"name\":\"settings\",\"id\":\"cmd:1ec7251158794c01945adae0bb0714c5\",\"time\":1734176516,\"value\":{\"motionTrackMode\":0,\"liveSpeakerVolume\":100,\"wifiPowerLevel\":2,\"powerSource\":\"unselected\",\"language\":\"cn\",\"sdCardVideoMode\":\"continual\",\"devicePersonDetect\":0,\"pirRecordTime\":\"20s\",\"irThreshold\":20,\"pir\":2,\"videoAntiFlickerFrequency\":\"50Hz\",\"detectPersonAi\":false,\"doorbellPressNotifySwitch\":true,\"recLen\":20,\"reportPetAi\":false,\"otaAutoUpgrade\":true,\"voiceVolume\":100,\"alarmVolume\":75,\"timeZone\":480,\"pirSensitivity\":\"mid\",\"recordingAudioToggleOn\":true,\"pirCooldownTime\":\"600s\",\"mtu\":1480,\"alarmDuration\":5,\"liveAudioToggleOn\":true,\"mirrorFlip\":0,\"chargeAutoPowerOnCapacity\":10,\"video12HourSwitch\":false,\"whiteLightScintillation\":0,\"motionTrack\":0,\"antiflicker\":50,\"doorbellRingKey\":2,\"sdCardCooldownSwitch\":0,\"deviceCallToggleOn\":true,\"nightVisionMode\":0,\"cryDetectLevel\":3,\"floodlightScheduleSwitch\":false,\"debugReport\":false,\"chargeAutoPowerOnSwitch\":0,\"pirSirenDuration\":0,\"reportPersonAi\":false,\"mechanicalDingDongDuration\":200,\"mechanicalDingDongSwitch\":1,\"sdCardPirRecordTime\":\"10s\",\"logUpload\":false,\"nightThresholdLevel\":2,\"cooldownInS\":600,\"alarmWhenRemoveToggleOn\":false,\"motionFloodlightTimer\":\"30s\",\"voiceVolumeSwitch\":1,\"cryDetect\":0,\"deviceStatusInterval\":60,\"motionTriggeredFloodlightSwitch\":false,\"sdCardCooldownSeconds\":\"10s\",\"videoResolution\":\"mid\",\"floodlightMode\":\"auto\",\"recLamp\":1}}}";
        GeneratedMessage pbMsg = ProtoMsgName.setting.getProtoResponseMsgConvertor().parseJsonStr(jsonStr);
        log.info("setting:\n{}", pbMsg);

//        ObjectMapper om = new ObjectMapper();
//        VideoSliceReport o1 = om.readValue("{\"utcTimestampMillis\":1234567890123}", VideoSliceReport.class);
//        VideoSliceReport o2 = om.readValue("{\"utcTimestampMillis\":\"1234567890123\"}", VideoSliceReport.class);
//        VideoSliceReport o3 = om.readValue("{\"utcTimestampMillis\":1234567890123.123}", VideoSliceReport.class);
//        VideoSliceReport o4 = om.readValue("{\"utcTimestampMillis\":\"1234567890123.456\"}", VideoSliceReport.class);
//        VideoSliceReport o5 = om.readValue("{\"utcTimestampMillis\":12345678901231234567890123}", VideoSliceReport.class);
//        VideoSliceReport o6 = om.readValue("{\"utcTimestampMillis\":\"12345678901231234567890123\"}", VideoSliceReport.class);
//        log.info("");
    }


}
