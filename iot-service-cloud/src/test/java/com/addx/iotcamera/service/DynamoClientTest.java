package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.openapi.OpenApiDeviceConfig;
import com.addx.iotcamera.bean.openapi.TenantAwsConfig;
import com.addx.iotcamera.bean.video.UploadVideoCompleteRequest;
import com.addx.iotcamera.dynamo.dao.VideoSliceDAO;
import com.addx.iotcamera.dynamo.util.DynamoConditionBuilder;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.JsonUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.datamodeling.*;
import com.amazonaws.services.dynamodbv2.model.*;
import com.amazonaws.services.globalaccelerator.AWSGlobalAccelerator;
import com.amazonaws.services.globalaccelerator.AWSGlobalAcceleratorClientBuilder;
import com.amazonaws.services.globalaccelerator.model.DescribeAcceleratorRequest;
import com.amazonaws.services.globalaccelerator.model.DescribeAcceleratorResult;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.DeleteObjectsRequest;
import com.amazonaws.services.s3.model.DeleteObjectsResult;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DynamoClientTest {

    private AmazonDynamoDB dynamoDB;
    private DynamoDBMapper dynamoDBMapper;
    private VideoSliceDAO videoSliceDynamoDAO;
    private DynamoDBMapper videoSliceCompleteMapper;
    private DynamoDBMapper videoCompleteReportMapper;
    private DynamoDBMapper tenantAwsConfigMapper;

    private TestHelper testHelper;

    @Before
    public void init() {
        testHelper = TestHelper.getInstanceByEnv("test");
//        testHelper = TestHelper.getInstanceByEnv("staging");
//        testHelper = TestHelper.getInstanceByEnv("staging-us");
//        testHelper = TestHelper.getInstanceByEnv("staging-eu");
//        testHelper = TestHelper.getInstanceByEnv("prod");
//        testHelper = TestHelper.getInstanceByEnv("prod-eu");
//        testHelper = TestHelper.getInstanceByEnv("prod-us");

        this.dynamoDB = testHelper.getAmazonInit().dynamoDB();
        this.dynamoDBMapper = testHelper.getAmazonInit().videoSliceMapper();
        this.videoCompleteReportMapper = testHelper.getAmazonInit().videoCompleteReportMapper();
        this.videoSliceCompleteMapper = testHelper.getAmazonInit().videoCompleteReportMapper();
        this.tenantAwsConfigMapper = testHelper.getAmazonInit().tenantAwsConfigMapper();
        //this.libraryDAO = testHelper.getMapper(ILibraryDAO.class);
        videoSliceDynamoDAO = new VideoSliceDAO();
        videoSliceDynamoDAO.setDynamoDB(dynamoDB);
        videoSliceDynamoDAO.setVideoSliceMapper(dynamoDBMapper);
        videoSliceDynamoDAO.setVideoCompleteReportMapper(videoCompleteReportMapper);
        videoSliceDynamoDAO.setVideoSliceConfig(testHelper.getAmazonInit().videoSliceMapperConfig());
        videoSliceDynamoDAO.setVideoCompleteReportConfig(testHelper.getAmazonInit().videoCompleteReportMapperConfig());

    }

    @After
    public void after() {
        TestHelper.getInstanceByLocal().commitAndClose();
    }

    private String tableName = "videoSlice4";

    //    @Test
    public void test_createTable() {
        List<KeySchemaElement> keySchemaElements = Arrays.asList(
                new KeySchemaElement("libraryId", KeyType.HASH) // 必需有一个分区键,Hash
                , new KeySchemaElement("order", KeyType.RANGE) // 排序键,Range
        );
        List<AttributeDefinition> attributeDefinitions = Arrays.asList(
                new AttributeDefinition("libraryId", ScalarAttributeType.S)
                , new AttributeDefinition("order", ScalarAttributeType.N)
        );
        ProvisionedThroughput provisionedThroughput = new ProvisionedThroughput(1L, 1L);

        List<GlobalSecondaryIndex> globalSecondaryIndices = Arrays.asList(
                new GlobalSecondaryIndex().withIndexName("serialNumberIndex")
                        .withKeySchema(new KeySchemaElement("serialNumber", KeyType.HASH))
                        .withProjection(new Projection().withProjectionType(ProjectionType.KEYS_ONLY))
                , new GlobalSecondaryIndex().withIndexName("validIndex")
                        .withKeySchema(new KeySchemaElement("deleted", KeyType.HASH))
                        .withKeySchema(new KeySchemaElement("expired", KeyType.HASH))
                        .withProjection(new Projection().withProjectionType(ProjectionType.KEYS_ONLY))
        );

        CreateTableRequest createTableRequest = new CreateTableRequest(tableName, keySchemaElements);
        createTableRequest.setAttributeDefinitions(attributeDefinitions);
        createTableRequest.setProvisionedThroughput(provisionedThroughput);
//        createTableRequest.setGlobalSecondaryIndexes(globalSecondaryIndices);
        CreateTableResult createTableResult = dynamoDB.createTable(createTableRequest);
        log.info("createTableResult:{}", JSON.toJSONString(createTableResult, true));
    }

    //    @Test
    public void test_describeTable() {
        DescribeTableResult describeTableResult = dynamoDB.describeTable(tableName);
        log.info("describeTableResult:{}", JSON.toJSONString(describeTableResult, true));
    }

    private String libraryId = "460052";
    private String serialNumber = "23ecf0af34304e25b06868b72c79c94e";
    private String imageUrl = "https://addx-test.s3.cn-north-1.amazonaws.com.cn/device_video_slice/23ecf0af34304e25b06868b72c79c94e/traceId_97872de6adee4473a731a9b007b3dff2/image.jpg";

    //    @Test
    public void test_getItem() {
        String[] attributesToGet = {"id", "serialNumber", "libraryId", "order", "uploadSuccess", "videoUrl"};
        GetItemRequest getItemRequest = new GetItemRequest().withTableName(tableName)
                .withAttributesToGet(attributesToGet)
                .addKeyEntry("serialNumber", new AttributeValue().withS(serialNumber))
                .addKeyEntry("id", new AttributeValue().withS(libraryId + "_" + 0));
        log.info("getItemRequest:{}", JSON.toJSONString(getItemRequest, true));
        GetItemResult getItemResult = dynamoDB.getItem(getItemRequest);
        log.info("getItemResult:{}", JSON.toJSONString(getItemResult, true));
        Map<String, AttributeValue> map = getItemResult.getItem();
        Assert.assertEquals(ImmutableSet.copyOf(attributesToGet), map.keySet());
    }

    //    @Test
    public void test_queryItem() {
        String[] attributesToGet = {"id", "serialNumber", "libraryId", "order", "uploadSuccess", "videoUrl"};
        QueryRequest queryRequest = new QueryRequest().withTableName(tableName)
                .withAttributesToGet(attributesToGet)
                .addKeyConditionsEntry("serialNumber", new Condition()
                        .withComparisonOperator(ComparisonOperator.EQ)
                        .withAttributeValueList(new AttributeValue().withS(serialNumber)))
                .addQueryFilterEntry("order", new Condition()
                        .withComparisonOperator(ComparisonOperator.GE)
                        .withAttributeValueList(new AttributeValue().withS("1")))
                .withLimit(2);
        log.info("queryRequest:{}", JSON.toJSONString(queryRequest, true));
        QueryResult queryResult = dynamoDB.query(queryRequest);
        log.info("queryResult:{}", JSON.toJSONString(queryResult, true));
    }

    //    private Set<String> createTableNames = ImmutableSet.of("tenantAwsConfig", "deviceConfig");
//    private Set<String> createTableNames = ImmutableSet.of("deviceConfig");
    private Set<String> createTableNames = ImmutableSet.of("tenantAwsConfig");
    //    private Set<String> envs = ImmutableSet.of("staging", "staging-eu", "staging-us");
    private Set<String> envs = ImmutableSet.of();
    private static Map<String, Class<?>> dynamoModelCls = ImmutableMap.<String, Class<?>>builder()
            .put("videoSlice", VideoSliceDO.class)
            .put("videoSliceComplete", UploadVideoCompleteRequest.class)
            .put("tenantAwsConfig", TenantAwsConfig.class)
            .put("deviceConfig", OpenApiDeviceConfig.class)
            .build();

    @Test
    public void test_dynamoDBMapper_createTable() {
        for (String env : envs) {
            createTables(env, createTableNames);
        }
    }

    public static void createTables(String env, Set<String> createTableNames) {
        TestHelper testHelper = TestHelper.getInstanceByEnv(env);
        AmazonDynamoDB dynamoDB = testHelper.getAmazonInit().dynamoDB();
        final DynamoDBMapper dynamoDBMapper = new DynamoDBMapper(dynamoDB);

        JSONObject dynamo = testHelper.getConfig().getJSONObject("dynamo");
        int i = 0;
        for (String key : dynamo.keySet()) {
            if (!createTableNames.contains(key)) continue;
            String tableName = dynamo.getJSONObject(key).getString("tableName");
            try {
                dynamoDB.deleteTable(tableName);
            } catch (Exception e) {
            }
            CreateTableRequest createTableRequest = dynamoDBMapper.generateCreateTableRequest(dynamoModelCls.get(key));
            createTableRequest.setTableName(tableName);
            createTableRequest.setProvisionedThroughput(new ProvisionedThroughput(1L, 1L));
            CreateTableResult createTableResult1 = dynamoDB.createTable(createTableRequest);
            log.info("createTableResult[{}]:{}", i++, JSON.toJSONString(createTableResult1, true));
        }
//        i = 0;
//        for (String key : dynamo.keySet()) {
//            String tableName = dynamo.getJSONObject(key).getString("tableName");
//            DescribeTableResult describeTableResult = dynamoDB.describeTable(tableName);
//            log.info("describeTableResult[{}]:{}", i, JSON.toJSONString(describeTableResult, true));
//        }
    }

    //    @Test
    public void test_deleteTables() {
        Set<String> tableNames = ImmutableSet.of("staging_cn_deviceConfig", "test_cn_deviceConfig2");
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        AmazonDynamoDB dynamoDB = testHelper.getAmazonInit().dynamoDB();
        for (String tableName : tableNames) {
            dynamoDB.deleteTable(tableName);
        }
    }

    @Test
    public void test_dynamoDBMapper_getTableModel() {
        DynamoDBMapperTableModel<VideoSliceDO> tableModel = dynamoDBMapper.getTableModel(VideoSliceDO.class);
        log.info("tableModel:{}", JSON.toJSONString(tableModel, true));
    }

    //    @Test
    public void test_dynamoDBMapper_load() {
        VideoSliceDO hashKey = VideoSliceDO.builder().traceId(libraryId).order(1).build();
        VideoSliceDO videoSlice = dynamoDBMapper.load(hashKey);
//        VideoSliceDO videoSlice = dynamoDBMapper.load(VideoSliceDO.class, hashKey.getSerialNumber(), hashKey.getId());
        log.info("videoSlice:{}", JSON.toJSONString(videoSlice, true));
        videoSlice.setOrder(6);
        dynamoDBMapper.save(videoSlice);
        videoSlice.setOrder(7);
        dynamoDBMapper.save(videoSlice);
    }

    //    @Test
    public void test_dynamoDBMapper_query() {
        VideoSliceDO hashKey = VideoSliceDO.builder().serialNumber(serialNumber).build();
        DynamoDBQueryExpression<VideoSliceDO> queryExpression = new DynamoDBQueryExpression<VideoSliceDO>()
                .withHashKeyValues(hashKey)
                .withRangeKeyCondition("order", new Condition()
//                .withQueryFilterEntry("order", new Condition()
                        .withComparisonOperator(ComparisonOperator.GE)
                        .withAttributeValueList(new AttributeValue().withS("1")))
                .withLimit(2);
        log.info("queryExpression:{}", JSON.toJSONString(queryExpression, true));
        QueryResultPage<VideoSliceDO> queryResultPage = dynamoDBMapper.queryPage(VideoSliceDO.class, queryExpression);
        log.info("queryResultPage:{}", JSON.toJSONString(queryResultPage, true));
        List<VideoSliceDO> results = queryResultPage.getResults();
        log.info("results:{}", JSON.toJSONString(results, true));
    }

    //    @Test
    public void test_deleteItem() {
        for (int order = 2; order < 8; order++) {
            DeleteItemRequest deleteItemRequest = new DeleteItemRequest().withTableName(tableName)
                    .addKeyEntry("libraryId", new AttributeValue().withS(libraryId))
                    .addKeyEntry("order", new AttributeValue().withN(order + ""));
            DeleteItemResult deleteItemResult = dynamoDB.deleteItem(deleteItemRequest);
            Assert.assertTrue(true);
        }
    }

    //    @Test
    public void test_videoSliceDynamoDAO_update() {
        String libraryId = "461204";
        int order = 4;

        VideoSliceDO slice = new VideoSliceDO();
        slice.setTraceId(461204 + "");
        slice.setOrder(order);
//        slice.setUploadSuccess(false);
//        videoSliceDynamoDAO.updateSliceById(slice);
        Assert.assertTrue(true);
        VideoSliceDO slice2 = videoSliceDynamoDAO.querySliceByTraceIdAndOrder(libraryId, order);
        TestHelper.assertDeepEquals(slice, slice2);
    }

    //    @Test
    public void test_videoSliceDynamoDAO_query() {
        String libraryId = "461204";
        List<VideoSliceDO> sliceList2 = videoSliceDynamoDAO.querySliceByTraceId(libraryId);
        log.info("sliceList2:{}", JSON.toJSONString(sliceList2, true));

        sliceList2 = videoSliceDynamoDAO.querySliceByTraceId(libraryId);
        log.info("sliceList2:{}", JSON.toJSONString(sliceList2, true));
        Assert.assertEquals(4, sliceList2.size());

        List<String> urls2 = videoSliceDynamoDAO.queryVideoUrlByTraceIds(Arrays.asList(libraryId));
    }

    //    @Test
    public void test_sort() {
        VideoSliceDO slice2 = VideoSliceDO.builder().traceId("123").order(2).build();
        dynamoDBMapper.save(slice2);
        VideoSliceDO slice3 = VideoSliceDO.builder().traceId("123").order(3).build();
        dynamoDBMapper.save(slice3);
        VideoSliceDO slice1 = VideoSliceDO.builder().traceId("123").order(1).build();
        dynamoDBMapper.save(slice1);
        VideoSliceDO slice0 = VideoSliceDO.builder().traceId("123").order(1).build();
        dynamoDBMapper.save(slice0);
//        dynamoDBMapper.batchDelete(slice0, slice1, slice2, slice3);
    }

    @Test
    public void test_query() {
        DynamoDBQueryExpression<VideoSliceDO> queryExpression = new DynamoDBQueryExpression<VideoSliceDO>()
                .withHashKeyValues(VideoSliceDO.builder().traceId("123").build())
                .withScanIndexForward(false);
        PaginatedQueryList<VideoSliceDO> list = dynamoDBMapper.query(VideoSliceDO.class, queryExpression);
        log.info("list:{}", JSON.toJSONString(list, true));
    }

    @Test
    public void test_querySliceByLibraryId() {
        List<VideoSliceDO> sliceList = videoSliceDynamoDAO.querySliceByTraceId("474471");
        log.info("sliceList:{}", JSON.toJSONString(sliceList, true));
    }

    @Test
    public void test_batchSaveSlice() throws InterruptedException {
//        videoSliceDynamoDAO.batchSaveSlice(Collections.emptyList());
        List<Thread> threads = new LinkedList<>();
        for (int n = 0; n < 20; n++) {
            String traceId = "test_110_" + n;
            threads.add(new Thread() {
                @Override
                public void run() {

                    List<VideoSliceDO> list = Arrays.asList(
                            VideoSliceDO.builder().traceId(traceId).order(0).videoUrl("url1").build()
                            , VideoSliceDO.builder().traceId(traceId).order(1).videoUrl("url1").build()
                            , VideoSliceDO.builder().traceId(traceId).order(2).videoUrl("url1").build()
                    );
                    videoSliceDynamoDAO.batchSaveSlice(list);
                    //                log.info("batchSaveSlice:"+i);
                    List<VideoSliceDO> list2 = Arrays.asList(
                            VideoSliceDO.builder().traceId(traceId).order(1).fileSize(123).build()
                            , VideoSliceDO.builder().traceId(traceId).order(2).fileSize(345).build()
                    );
                    videoSliceDynamoDAO.batchSaveSlice(list2);
//                log.info("batchSaveSlice:"+i2);
                }
            });
        }
        threads.forEach(it -> it.start());
        for (Thread thread : threads) {
            thread.join();
        }
        log.info("end");
    }

    //    @Test
    public void test_tenantAwsConfig_write() {
        List<String> paths = Arrays.asList(
                "classpath:openapi/tenant_aws_config/netvue.yml",
                "classpath:openapi/tenant_aws_config/paas_owned.yml");
        for (String path : paths) {
            JSONObject config = ConfigHelper.loadYmlConfig(path);
            TenantAwsConfig tenantAwsConfig = config.toJavaObject(TenantAwsConfig.class);
            tenantAwsConfigMapper.save(tenantAwsConfig);
        }
    }

    //    @Test
    public void test_delete_deviceConfig() {
        String tbName = "staging_cn_deviceConfig";
        ScanRequest scanRequest = new ScanRequest().withTableName(tbName)
//                .addScanFilterEntry("configId", DynamoConditionBuilder.eqS("paas_owned"));
                .addScanFilterEntry("serialNumber", DynamoConditionBuilder.eqS("ce08923bf2abff570bd6c3c5def381b8"));
        ScanResult scanResult = dynamoDB.scan(scanRequest);
        List<Map<String, AttributeValue>> list = scanResult.getItems().stream().collect(Collectors.toList());
        log.info("scan list:{}", list);
        for (Map<String, AttributeValue> map : list) {
            DeleteItemRequest deleteItemRequest = new DeleteItemRequest().withTableName(tbName)
                    .addKeyEntry("serialNumber", map.get("serialNumber"))
                    .addKeyEntry("configId", map.get("configId"));
            DeleteItemResult deleteItemResult = dynamoDB.deleteItem(deleteItemRequest);
        }
    }

    //    @Test
    public void test_create_deviceConfig() {
        String tbName = "staging_cn_deviceConfig";
        PutItemRequest putItemRequest = new PutItemRequest().withTableName(tbName)
                .addItemEntry("serialNumber", new AttributeValue().withS("ce08923bf2abff570bd6c3c5def381b8"))
                .addItemEntry("configId", new AttributeValue().withS("VMWBInoGWcUN523GlOZMN"))
                .addItemEntry("tenantId", new AttributeValue().withS("netvue"));
        PutItemResult putItemResult = dynamoDB.putItem(putItemRequest);
    }

    //    @Test
    public void test_gaClient() {
        String accesskey = "********************";
        String secretKey = "FzhWBeQ5g/026eYoGfplIKyyn4MA55LoMcf00AIW";
        String region = "us-east-1";
        String acceleratorArn = "arn:aws:globalaccelerator::002497567426:accelerator/8b376741-1dc4-4f65-b5cc-5e1196823ecd";
        BasicAWSCredentials awsCredentials = new BasicAWSCredentials(accesskey, secretKey);
        AWSGlobalAccelerator gaClient = AWSGlobalAcceleratorClientBuilder.standard()
                .withRegion(region)
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                .build();
        DescribeAcceleratorResult result = gaClient.describeAccelerator(new DescribeAcceleratorRequest().withAcceleratorArn(acceleratorArn));
        log.info("describeAccelerator:{}", JSON.toJSONString(result));
    }

    @Test
    public void test_load_netvueConfig() {
        TenantAwsConfig netvueConfig = tenantAwsConfigMapper.load(TenantAwsConfig.class, "netvue");
//        String netvue = tenantAwsConfigMapper.load("netvue");
        log.info("netvueConfig:{}", JSON.toJSONString(netvueConfig, true));
    }

    @Test
    public void test_queryVideoUrlByTraceIds() throws Exception {
//        List<String> traceIds = Arrays.asList("0rgE0i5n0C8J0QbR0FzJbE3Z3WL07"
//                , "0Jpw0ZMG03FH0dtE0K1c705r424k4", "0RlK00Ck0Zgr0ty00NcC7T7j4C6d2");

        // select concat('"',trace_id,'",') from video_library where period>20 order by id desc limit 20;
//        String fileName = "/Users/<USER>/test/traceId_20211105.sql.csv";
//        BufferedReader reader = new BufferedReader(new FileReader(fileName));
//        List<String> traceIds = reader.lines().collect(Collectors.toList());
        List<String> traceIds = Arrays.asList();

        List<String> videoUrls = videoSliceDynamoDAO.queryVideoUrlByTraceIds(traceIds);
        log.info("queryVideoUrlByTraceIds:{}", videoUrls.size());
        long count = traceIds.stream().map(videoSliceDynamoDAO::querySliceByTraceId).map(it -> it.size()).reduce(Integer::sum).orElse(0);
        log.info("queryVideoUrlByTraceIds: expect={}", count);
        Assert.assertEquals(count, videoUrls.size());
    }

    //    @Test
    public void test_delete() {
        AmazonS3 s3Client = testHelper.getAmazonInit().s3Client();
        // https://addx-test.s3.cn-north-1.amazonaws.com.cn/
        // device_video_slice/d5ea259289595d86b275bfaffb5315dd/03151630570257KdnMYajFvIYNyeI/image.jpg
        String bucket = "addx-test";
        String key = "device_video_slice/d5ea259289595d86b275bfaffb5315dd/03151630570257KdnMYajFvIYNyeI/*";
        DeleteObjectsRequest request = new DeleteObjectsRequest(bucket).withKeys(key);
        DeleteObjectsResult result = s3Client.deleteObjects(request);
        log.info(JSON.toJSONString(result));
    }

    //    @Test
    public void test_queryBySnTimeIndex() {
        AmazonDynamoDB dynamoDB = testHelper.getAmazonInit().dynamoDB();

        DynamoDBMapperConfig mapperConfig = testHelper.getAmazonInit().videoSliceMapperConfig();

        List<String> traceIds = Arrays.asList("0540716297222303GUa2XS1MwdsUt", "0PT00Z3H0tC40JaU0fNkpWlN1vLT5");
        QueryRequest queryRequest = new QueryRequest()
                .withTableName(mapperConfig.getTableNameOverride().getTableName())
                .withIndexName("traceIdIndex")
//                .addKeyConditionsEntry("traceId", DynamoConditionBuilder.eqS(traceIds.get(0)))
                .addKeyConditionsEntry("traceId", DynamoConditionBuilder.inS(traceIds))
                .withAttributesToGet("videoUrl");
        QueryResult queryResult = dynamoDB.query(queryRequest);
        log.info("");
    }

    //    @Test
    public void test_queryIn() {
        AmazonDynamoDB dynamoDB = testHelper.getAmazonInit().dynamoDB();

        DynamoDBMapperConfig mapperConfig = testHelper.getAmazonInit().videoSliceMapperConfig();

        String sn = "09361629116430se69711NYg80UsX";
        String traceId = "09361629116430se69711NYg80UsX";
        String traceId2 = "05kP0usE0dws0N4l0719LUgWc1vh1";

        QueryRequest queryRequest = new QueryRequest()
                .withTableName(mapperConfig.getTableNameOverride().getTableName())
                .withIndexName("snTraceIdIndex")
                .addKeyConditionsEntry("serialNumber", DynamoConditionBuilder.eqS(sn))
                .addQueryFilterEntry("traceId", DynamoConditionBuilder.inS(Arrays.asList(traceId, traceId2)))
                .withAttributesToGet("videoUrl");
        QueryResult queryResult = dynamoDB.query(queryRequest);
        log.info("");
    }

    //    @Test
    public void test_batchQuery() {
        AmazonDynamoDB dynamoDB = testHelper.getAmazonInit().dynamoDB();

        DynamoDBMapperConfig mapperConfig = testHelper.getAmazonInit().videoSliceMapperConfig();

        String sn = "09361629116430se69711NYg80UsX";
        String traceId = "09361629116430se69711NYg80UsX";
        String traceId2 = "05kP0usE0dws0N4l0719LUgWc1vh1";

        KeysAndAttributes keysAndAttributes = new KeysAndAttributes().withKeys(
                ImmutableMap.of("traceId", new AttributeValue().withS(traceId), "order", new AttributeValue().withN("0")),
                ImmutableMap.of("traceId", new AttributeValue().withS(traceId), "order", new AttributeValue().withN("1")),
                ImmutableMap.of("traceId", new AttributeValue().withS(traceId2), "order", new AttributeValue().withN("0")),
                ImmutableMap.of("traceId", new AttributeValue().withS(traceId2), "order", new AttributeValue().withN("1"))
        );
        BatchGetItemRequest request = new BatchGetItemRequest()
                .addRequestItemsEntry(mapperConfig.getTableNameOverride().getTableName(), keysAndAttributes);

        BatchGetItemResult batchGetItemResult = dynamoDB.batchGetItem(request);

        log.info("");
    }

    @Test
    public void test_video_library() {
        AmazonDynamoDB dynamoDB = testHelper.getAmazonInit().dynamoDB();

        PutItemRequest putItemRequest = new PutItemRequest();
        putItemRequest.setTableName("test_cn_video_library");
        Map<String, AttributeValue> attributeValueMap = new HashMap<>();
        attributeValueMap.put("trace_id", new AttributeValue().withS("trace_id_1"));
        attributeValueMap.put("user_id", new AttributeValue().withN("1"));
        attributeValueMap.put("timestamp", new AttributeValue().withN(String.valueOf(Timestamp.valueOf(LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC.normalized())).getTime() / 1000)));
        attributeValueMap.put("ttl_timestamp", new AttributeValue().withN(String.valueOf(Timestamp.valueOf(LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC.normalized())).getTime() / 1000 + 100)));
        Map<String, AttributeValue> userConfigMap = new HashMap<>();
        userConfigMap.put("1", new AttributeValue().withM(Collections.singletonMap("tags", new AttributeValue().withSS("tag1"))));
        userConfigMap.put("2", new AttributeValue().withM(Collections.singletonMap("tags", new AttributeValue().withSS("tag2"))));
        userConfigMap.put("key3", new AttributeValue().withM(Collections.singletonMap("tags", new AttributeValue().withSS("tag3"))));
        userConfigMap.put("user_ids", new AttributeValue().withS("1,2,key3"));
        attributeValueMap.put("user_config", new AttributeValue().withM(userConfigMap));
        putItemRequest.setItem(attributeValueMap);
        PutItemResult putItemResult = dynamoDB.putItem(putItemRequest);
        log.info("put item result:{}", JsonUtil.toJson(putItemResult));

        UpdateItemRequest updateItemRequest = new UpdateItemRequest();
        updateItemRequest.setTableName("test_cn_video_library");
        updateItemRequest.setKey(new HashMap<String, AttributeValue>(){{
            put("trace_id", new AttributeValue().withS("trace_id_1"));
            put("user_id", new AttributeValue().withN("1"));
        }});
        updateItemRequest.withUpdateExpression("SET user_config.#userId.serial_number = :newSerialNumber")
                .withExpressionAttributeNames(Collections.singletonMap("#userId", "1"))
                .withExpressionAttributeValues(new HashMap<String, AttributeValue>() {{
                    put(":newSerialNumber", new AttributeValue().withS("sn_xxx"));
                }});
        updateItemRequest.withReturnValues(ReturnValue.ALL_NEW);
        UpdateItemResult updateItemResult = dynamoDB.updateItem(updateItemRequest);
        log.info("update item result:{}", JsonUtil.toJson(updateItemResult));

        updateItemRequest.withUpdateExpression("ADD user_config.#userId.tags :addTag")
                .withExpressionAttributeValues(new HashMap<String, AttributeValue>() {{
                    put(":addTag", new AttributeValue().withSS("tag_add"));
                }});
        updateItemResult = dynamoDB.updateItem(updateItemRequest);
        log.info("update item result:{}", JsonUtil.toJson(updateItemResult));

        updateItemRequest.withUpdateExpression("DELETE user_config.#userId.tags :deleteTag")
                .withExpressionAttributeValues(new HashMap<String, AttributeValue>() {{
                    put(":deleteTag", new AttributeValue().withSS("tag1"));
                }});
        updateItemResult = dynamoDB.updateItem(updateItemRequest);
        log.info("update item result:{}", JsonUtil.toJson(updateItemResult));

        updateItemRequest.withUpdateExpression("SET user_config.#userId = :updateUserConfig")
                .withExpressionAttributeNames(Collections.singletonMap("#userId", "key3"))
                .withExpressionAttributeValues(new HashMap<String, AttributeValue>() {{
                    put(":updateUserConfig", new AttributeValue().withM(Collections.singletonMap("serialNumber", new AttributeValue().withS("sn_03"))));
                }});
        updateItemResult = dynamoDB.updateItem(updateItemRequest);
        log.info("update item result:{}", JsonUtil.toJson(updateItemResult));

        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setTableName("test_cn_video_library");
        queryRequest.addKeyConditionsEntry("trace_id", DynamoConditionBuilder.eqS("trace_id_1"));
        queryRequest.addKeyConditionsEntry("user_id", DynamoConditionBuilder.eqN("1"));
        queryRequest.withProjectionExpression("user_config.#userId1,user_config.#userId2,user_config.#userId3,user_config.noKey").withExpressionAttributeNames(new HashMap<String, String>() {{
            put("#userId1", "1");
            put("#userId2", "2");
            put("#userId3", "key3");
        }});
        QueryResult queryResult = dynamoDB.query(queryRequest);
        log.info("query result:{}", JsonUtil.toJson(queryResult.getItems()));
    }

}
