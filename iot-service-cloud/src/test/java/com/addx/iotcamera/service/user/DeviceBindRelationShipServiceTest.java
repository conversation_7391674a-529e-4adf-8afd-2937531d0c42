package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.app.device.DeviceBindRelationShipDO;
import com.addx.iotcamera.dao.user.IDeviceBindRelationShipDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DeviceBindRelationShipServiceTest {
    @InjectMocks
    private DeviceBindRelationShipService deviceBindRelationShipService;

    @Mock
    private IDeviceBindRelationShipDao iDeviceBindRelationShipDao;

    @Test
    public void test_saveDeviceBindRelationShip(){
        when(iDeviceBindRelationShipDao.insertDeviceBindRelationShipDO(any())).thenReturn(1);
        deviceBindRelationShipService.saveDeviceBindRelationShip(new DeviceBindRelationShipDO());
        verify(iDeviceBindRelationShipDao, times(1)).insertDeviceBindRelationShipDO(new DeviceBindRelationShipDO());
    }


    @Test
    public void test_queryDeviceBindContent(){
        when(iDeviceBindRelationShipDao.queryDeviceBindRelationShipDO(any())).thenReturn(null);
        deviceBindRelationShipService.queryDeviceBindContent(1);
        verify(iDeviceBindRelationShipDao, times(1)).queryDeviceBindRelationShipDO(any());
    }
}
