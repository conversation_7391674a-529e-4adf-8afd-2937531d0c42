package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.init.AmazonInit;
import com.addx.iotcamera.bean.openapi.*;
import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.bean.tuple.Tuples;
import com.addx.iotcamera.config.*;
import com.addx.iotcamera.config.apollo.DnsServerConfig;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.dao.IUserRoleDAO;
import com.addx.iotcamera.dao.device.IDeviceSettingDAO;
import com.addx.iotcamera.dynamo.dao.DeviceConfigDAO;
import com.addx.iotcamera.dynamo.dao.TenantAwsConfigDAO;
import com.addx.iotcamera.enums.UserType;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.helper.openapi.DeviceConfigHelper;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.publishers.vernemq.requests.SetRetainParamRequest;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.model.DeviceModelBatteryService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.openapi.PaasAccessLogService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.service.video.StorageParamService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceRemoteDO;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ForkJoinPool;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.addx.iotcamera.service.openapi.OpenApiConfigService.PAAS_OWNED_CONFIG_ID;
import static com.addx.iotcamera.service.openapi.OpenApiConfigService.PAAS_OWNED_TENANT_ID;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class OpenApiConfigServiceTest {

    @InjectMocks
    private OpenApiConfigService openApiConfigService;
    @Mock
    private StorageParamService storageParamService;
    @Mock
    private UserTierDeviceService userTierDeviceService;
    @Mock
    private VideoSlicePeriodConfig videoSlicePeriodConfig;
    @Mock
    private PaasTenantConfig paasTenantConfig;
    @Mock
    private FreeStorageService freeStorageService;
    @Mock
    private UserVipService userVipService;
    @Mock
    private VipService vipService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private DeviceModelBatteryService deviceModelBatteryService;
    @Mock
    private ForkJoinPool pool;
    @Mock
    private DeviceService deviceService;
    @Mock
    private RedisService redisService;
    @Mock
    private MqttSender mqttSender;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private UserService userService;
    @Mock
    private VideoSliceConfig videoSliceConfig;
    @Mock
    private S3Config s3Config;
    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private PaasAccessLogService paasAccessLogService;
    @Mock
    private DeviceConfigService deviceConfigService;
    @Mock
    private DnsServerConfig dnsServerConfig;
    @Mock
    private TenantSettingService tenantSettingService;
    @Mock
    private DeviceModelConfigService deviceModelConfigService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private PlanSupportService planSupportService;

    private DeviceConfigDAO deviceConfigDAO;
    private TenantAwsConfigDAO tenantAwsConfigDAO;


    @Before
    public void init() {
        when(tenantSettingService.getTenantSettingByTenantId(anyString())).thenReturn(new TenantSetting().setTrackerUrl("tackerUrl"));
        when(storageParamService.deviceNeedCloudStorageParams(any())).thenReturn(true);
        when(dnsServerConfig.getConfig()).thenReturn(Arrays.asList("***************"));
        PaasTenantConfigTest.mockPaasTenantConfig(this.paasTenantConfig);
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(DeviceDO.builder().recResolution(Resolution.HIGH.getWhDesc()).build());
//        TestHelper testHelper = TestHelper.getInstanceByEnv("staging");
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        JSONObject config = testHelper.getConfig();
        AmazonInit amazonInit = testHelper.getAmazonInit();
        this.deviceConfigDAO = new DeviceConfigDAO();
        deviceConfigDAO.setDeviceConfigMapper(amazonInit.deviceConfigMapper());
        deviceConfigDAO.setDeviceConfigMapperConfig(amazonInit.deviceConfigMapperConfig());
        deviceConfigDAO.setDynamoDB(amazonInit.dynamoDB());
        this.tenantAwsConfigDAO = new TenantAwsConfigDAO();
        tenantAwsConfigDAO.setTenantAwsConfigMapper(amazonInit.tenantAwsConfigMapper());

        when(redisService.setIfAbsent(anyString(), anyString(), anyLong(), any())).thenReturn(true);
        doNothing().when(mqttSender).sendRetainedMessage(any(), any(), (Object) any());
        openApiConfigService.setDeviceConfigDAO(deviceConfigDAO);
        openApiConfigService.setTenantAwsConfigDAO(tenantAwsConfigDAO);
        openApiConfigService.setGlobalDeviceConfig(new GlobalDeviceConfig());
        UserRoleService userRoleService = new UserRoleService();
        userRoleService.userRoleDAO = testHelper.getMapper(IUserRoleDAO.class);
        Answer<Object> userRoleAnswer = AdditionalAnswers.delegatesTo(userRoleService);
        when(this.userRoleService.getDeviceAdminUser(anyString())).thenAnswer(userRoleAnswer);
        when(this.userRoleService.queryAllAdminUserRoleNum()).thenAnswer(userRoleAnswer);
        when(this.userRoleService.queryAllAdminUserRoleIterator("queryAllBindDeviceSn", 100)).thenAnswer(userRoleAnswer);

        UserService userService = new UserService();
        userService.setUserDAO(testHelper.getMapper(IUserDAO.class));
        Answer<Object> userAnswer = AdditionalAnswers.delegatesTo(userService);
        when(this.userService.queryUserById(anyInt())).thenAnswer(userAnswer);

        when(deviceConfigService.getVideoRollingDays(any(), any(), any()))
                .thenReturn(Tuples.createTuple(30, System.currentTimeMillis() / 1000 + 365 * 24 * 60 * 60));
        when(deviceConfigService.getVideoRollingDaysOrNoPlan(any(), any(), any()))
                .thenReturn(Tuples.createTuple(30, System.currentTimeMillis() / 1000 + 365 * 24 * 60 * 60));
        IDeviceSettingDAO iDeviceSettingDAO = testHelper.getMapper(IDeviceSettingDAO.class);
        when(deviceSettingService.getDeviceSettingsBySerialNumber(anyString()))
                .thenAnswer(it -> iDeviceSettingDAO.getDeviceSettingsBySerialNumber("test_zyj_dvFV27GYy2F7Zaz7IMqDw1_2"));

        String bucket = config.getJSONObject("s3config").getString("bucket");
        String rootPath = config.getJSONObject("videoslice").getString("rootPath");
        when(s3Config.getClientRegion()).thenReturn(bucket);
        when(videoSliceConfig.getRootPath()).thenReturn(rootPath);
        openApiConfigService.setS3(config.getObject("s3", S3.class));
//        openApiConfigService.setMqttServerUri(config.getJSONObject("mqtt").getString("serverUri"));
//        openApiConfigService.setMqttIp(Optional.ofNullable(config.getJSONObject("mqtt").getString("ip")).orElse(""));
//        deviceService.setIDeviceDAO(testHelper.getMapper(IDeviceDAO.class));
//        deviceSettingService.setIDeviceSettingDAO(testHelper.getMapper(IDeviceSettingDAO.class));
        openApiConfigService.init();

        when(deviceInfoService.getDeviceSupport(anyString())).thenReturn(new CloudDeviceSupport());
        when(vipService.isVipDevice(anyInt(), anyString())).thenReturn(true);
        final ForkJoinPool forkJoinPool = new ForkJoinPool(1);
        when(pool.submit((Callable) any())).thenAnswer(AdditionalAnswers.delegatesTo(forkJoinPool));
        when(paasTenantConfig.getPaasTenantInfo(any())).thenReturn(new PaasTenantInfo().setEnableThirdStorage(false));
        when(freeStorageService.getPaasFreeStorage(any())).thenReturn(new UserFreeStorage().setIsValid(false));

        VideoSlicePeriodConfig slicePeriodConfig = new VideoSlicePeriodConfig();
        when(videoSlicePeriodConfig.getVipHeadSlicePeriod()).thenAnswer(AdditionalAnswers.delegatesTo(slicePeriodConfig));
        when(videoSlicePeriodConfig.getVipTailSlicePeriod()).thenAnswer(AdditionalAnswers.delegatesTo(slicePeriodConfig));
        when(videoSlicePeriodConfig.getNoVipHeadSlicePeriod()).thenAnswer(AdditionalAnswers.delegatesTo(slicePeriodConfig));
        when(videoSlicePeriodConfig.getNoVipTailSlicePeriod()).thenAnswer(AdditionalAnswers.delegatesTo(slicePeriodConfig));

        when(planSupportService.isNoPlanAndDeviceSupportImageEvent(any(), any())).thenReturn(new Tuple2<>(false, false));
        when(userTierDeviceService.isDeviceCanTriggerPir(any(),any())).thenReturn(true);
    }

    private String tenantId = "netvue";

    //    @Test
    public void test_createDeviceConfigs() throws IOException {
        InputStream is = ConfigHelper.loadConfig("classpath:example/tian_he_rong_v2.json");
        String json = IOUtils.toString(is);
        OpenApiDeviceConfig deviceConfig = JSON.parseObject(json, OpenApiDeviceConfig.class);

        String sn = OpenApiUtil.shortUUID();
//        String sn = "test_zyj_dvFV27GYy2F7Zaz7IMqDw1_2";
        Result result = openApiConfigService.createDeviceConfigs(tenantId, sn, OpenApiUtil::shortUUID, deviceConfig);
        log.info("test_createDeviceConfigs:{}", JSON.toJSONString(result, true));

        String configId = ((JSONObject) result.getData()).getString("configId");
        OpenApiDeviceConfig deviceConfig2 = JSON.parseObject(json, OpenApiDeviceConfig.class);
        OpenApiDeviceConfig.DeviceAudio deviceAudio = new OpenApiDeviceConfig.DeviceAudio();
        deviceAudio.setLiveAudioToggleOn(true);
        deviceAudio.setRecordingAudioToggleOn(null);
        deviceAudio.setLiveSpeakerVolume(77);
        deviceConfig2.setDeviceAudio(deviceAudio);
        Result result2 = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig2);
        log.info("test_createDeviceConfigs update:{}", JSON.toJSONString(result2, true));
    }

    @Test
    public void test_save() throws IOException {
        InputStream json = ConfigHelper.loadConfig("classpath:example/tian_he_rong_v2.json");
        OpenApiDeviceConfig deviceConfig = JSON.parseObject(json, OpenApiDeviceConfig.class);
//        deviceConfig.setConfigId("24ubeWiNqrEnerDyeuWWb7").setSerialNumber("IVspuuUFapi6FJiB75Dzb4").setTenantId(tenantId);
//        deviceConfig.setConfigId(OpenApiUtil.shortUUID()).setSerialNumber("ce08923bf2abff570bd6c3c5def381b8").setTenantId(tenantId);
//        deviceConfig.setConfigId("VMWBInoGWcUN523GlOZMN").setSerialNumber("288eb1ea8971fef2115b9adf41021d3b").setTenantId(tenantId);
        deviceConfig.setConfigId("OpenApiUtil.shortUUID()").setSerialNumber("8cb024383ab4c66c7972b5069cb79bd6").setTenantId(tenantId);
        deviceConfigDAO.save(deviceConfig);
    }

    //    @Test
    public void test_delete() {
//        int i = deviceConfigDAO.deleteBySnAndConfigIds("288eb1ea8971fef2115b9adf41021d3b", Arrays.asList("DPK8sekshiCML583XYjfd4", "wioVCVcMrFdvOiikQHIXB7"));
        int i = deviceConfigDAO.deleteBySnAndConfigIds("288eb1ea8971fef2115b9adf41021d3b", Arrays.asList("UiIugVKmn4qONXHXdf0LR2"));
        log.info("result:{}", i);
    }

    //    @Test
    public void test_updagteDeviceConfigs() throws IOException {
        String sn = "IVspuuUFapi6FJiB75Dzb4";
        String configId = "24ubeWiNqrEnerDyeuWWb7";
        OpenApiDeviceConfig.Motion motion = deviceConfigDAO.queryMotionBySnAndTenantId(sn, tenantId);
        motion.setCooldownInS(motion.getCooldownInS() + 1);
        OpenApiDeviceConfig oldDeviceConfig = deviceConfigDAO.queryBySnAndTenantId(sn, tenantId);

        OpenApiDeviceConfig deviceConfig = new OpenApiDeviceConfig().setMotion(motion);
        Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//        OpenApiDeviceConfig.MotionImage motionImage2 = deviceConfigDAO.queryMotionImageBySnAndTenantId(sn, tenantId);
        log.info("test_updagteDeviceConfigs:{}", JSON.toJSONString(result, true));
    }

    @Test
    public void test_OpenApiDeviceConfig_fromJson() throws IOException {
        InputStream is = ConfigHelper.loadConfig("classpath:openapi/device_config/paas_owned.json");
        String json = IOUtils.toString(is);
        OpenApiDeviceConfig deviceConfig = OpenApiDeviceConfig.fromJson(json);
        log.info("deviceConfig:{}", JSON.toJSONString(deviceConfig));
        Assert.assertNotNull(deviceConfig.getMotion().getConfig().getHeadSlicePeriod());
        Assert.assertNotNull(deviceConfig.getMotion().getConfig().getTailSlicePeriod());
    }

    @Test
    public void test_OpenApiDeviceConfig_fromJson2() {
        String json = "{\n" +
                "\t\"motion\": {\n" +
                "\t\t\"noTriggerTimeoutInS\": 6,\n" +
                "\t\t\"maxDurationInS\": 20,\n" +
                "\t\t\"cooldownInS\": 300,\n" +
                "\t\t\"expireDays\": 30,\n" +
                "\t\t\"coverConfig\": {\n" +
                "\t\t\t\"resolution\": \"MIDDLE\",\n" +
                "\t\t\t\"provider\": \"AWS_S3\",\n" +
                "\t\t\t\"config\": {\n" +
                "\t\t\t\t\"bucket\": {\n" +
                "\t\t\t\t\t\"region\": \"cn-northwest-1\",\n" +
                "\t\t\t\t\t\"bucket\": \"nvcn-cn-northwest-1-motioncapture\",\n" +
                "\t\t\t\t\t\"keyPrefix\": \"7aba1bc3286fd8df/288eb1ea8971fef2/\",\n" +
                "\t\t\t\t\t\"keyRawPrefix\": \"7aba1bc3286fd8df/288eb1ea8971fef2/\",\n" +
                "\t\t\t\t\t\"expireDays\": 0\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}\n" +
                "\t\t},\n" +
                "\t\t\"type\": \"image\",\n" +
                "\t\t\"config\": {\n" +
                "\t\t\t\"resolution\": \"MIDDLE\",\n" +
                "\t\t\t\"intervalInMs\": 2000,\n" +
                "\t\t\t\"maxCount\": 2,\n" +
                "\t\t\t\"provider\": \"AWS_S3\",\n" +
                "\t\t\t\"config\": {\n" +
                "\t\t\t\t\"bucket\": {\n" +
                "\t\t\t\t\t\"region\": \"cn-northwest-1\",\n" +
                "\t\t\t\t\t\"bucket\": \"nvcn-cn-northwest-1-motioncapture\",\n" +
                "\t\t\t\t\t\"keyPrefix\": \"7aba1bc3286fd8df/288eb1ea8971fef2/\",\n" +
                "\t\t\t\t\t\"keyRawPrefix\": \"7aba1bc3286fd8df/288eb1ea8971fef2/\",\n" +
                "\t\t\t\t\t\"expireDays\": 0\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t},\n" +
                "\t\"ai\": {\n" +
                "\t\t\"enable\": true,\n" +
                "\t\t\"endpoint\": \"https://nasm-nvcn.nvts.co/snapshot/addx\",\n" +
                "\t\t\"intervalInMs\": 2000,\n" +
                "\t\t\"token\": \"v1288eb1ea8971fef2a97a9d0b7aad9b2de3221c0d15efb43e\"\n" +
                "\t}\n" +
                "}";
        OpenApiDeviceConfig deviceConfig = OpenApiDeviceConfig.fromJson(json);
        log.info("deviceConfig:{}", JSON.toJSONString(deviceConfig, true));

//        PowerMockito.mockStatic(DeviceConfigHelper.class);
        ImmutableMap<String, PirUploadConfig> map = ImmutableMap.<String, PirUploadConfig>builder()
                .put("coverImage", new PirUploadConfig().setRootPath("r").setKeyTemplate("k").setReportComplete(null))
                .put("motionImage", new PirUploadConfig().setRootPath("r").setKeyTemplate("k").setReportComplete(true))
                .build();
//        PowerMockito.when(DeviceConfigHelper.createPirUploadConfigMap(any(), any(), anyString())).thenReturn(map);
        JSONObject deviceConfigMessage = DeviceConfigHelper.buildDeviceConfigMessage(deviceConfig, map);
        log.info("deviceConfigMessage:{}", JSON.toJSONString(deviceConfigMessage, true));
    }

    @Test
    public void test_OpenApiDeviceConfig_fromJson3() throws Exception {
        String path = "classpath:openapi/device_config/paas_owned.json";
        InputStream is = ConfigHelper.loadConfig(path);
        String paasOwnedDeviceConfigStr = IOUtils.toString(is);
        String configStr = paasOwnedDeviceConfigStr
                .replace("${tenantId}", PAAS_OWNED_TENANT_ID)
                .replace("${sn}", "sn123456789")
                .replace("${configId}", PAAS_OWNED_CONFIG_ID)
                .replace("${bucket}", "bucket123456789")
                .replace("${clientRegion}", "client_region_123456789");
        Pattern ptn = Pattern.compile("\\$\\{[a-zA-Z][a-zA-Z0-9]*\\}");
        Assert.assertEquals(false, ptn.matcher(configStr).find());
        OpenApiDeviceConfig deviceConfig = OpenApiDeviceConfig.fromJson(configStr);
//        DeviceAndSettingUpdate update = openApiConfigService.getDeviceAndSettingUpdate("d9353346a36f55d2e8974388e2dca115");
//        DeviceAndSettingUpdate update = new DeviceAndSettingUpdate().setSn("d9353346a36f55d2e8974388e2dca115")
//                .setResolution("1920x720").setMaxDurationInS(20);
//        update.writeToDeviceConfig(deviceConfig);
        Assert.assertNotNull(deviceConfig);
        Assert.assertNotNull(deviceConfig.getMotion().getConfig().getResolution());
        Assert.assertNotNull(deviceConfig.getMotion().getCoverConfig().getResolution());
    }

    @Test
    public void test_validate() {
        String imageJson = "{\"serialNumber\":\"f4dea255a2a2a1fecaa3e27f5dc28c14\",\"configId\":\"X7lHSVBI4KpDJVPQhVQEU6\",\"ai\":{\"intervalInMs\":1000,\"endpoint\":\"https://nasm.nvts.co/snapshot/addx\",\"enable\":true,\"token\":\"v150315129401000269e87f72b01ce970e16a1206ddf9be23f\"},\"tenantId\":\"netvue\",\"motion\":{\"expireDays\":30,\"maxDurationInS\":20,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"nvs-cn-north-1-motioncapture\",\"region\":\"cn-north-1\",\"keyPrefix\":\"07883c8749bf4fba/5031512940100026/\",\"keyRawPrefix\":\"07883c8749bf4fba/5031512940100026/\"}},\"resolution\":\"MIDDLE\"},\"type\":\"video\",\"config\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"nvs-cn-north-1-videomotion\",\"region\":\"cn-north-1\",\"keyPrefix\":\"07883c8749bf4fba/5031512940100026/\",\"keyRawPrefix\":\"07883c8749bf4fba/5031512940100026/\"}},\"resolution\":\"MIDDLE\"},\"noTriggerTimeoutInS\":6,\"cooldownInS\":180}}";
        String videoJson = "{\"serialNumber\":\"f4dea255a2a2a1fecaa3e27f5dc28c14\",\"configId\":\"X1ccz33wXJI5X4r5jYOqj\",\"ai\":{\"intervalInMs\":1000,\"endpoint\":\"https://nasm.nvts.co/snapshot/addx\",\"enable\":true,\"token\":\"v150315129401000269e87f72b01ce970e16a1206ddf9be23f\"},\"motion\":{\"expireDays\":30,\"maxDurationInS\":20,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"nvs-cn-north-1-motioncapture\",\"region\":\"cn-north-1\",\"keyPrefix\":\"qiao.jia%40netvue.com/5031512940100026/\",\"keyRawPrefix\":\"<EMAIL>/5031512940100026/\"}},\"resolution\":\"MIDDLE\"},\"type\":\"image\",\"config\":{\"intervalInMs\":2000,\"provider\":\"AWS_S3\",\"maxCount\":2,\"config\":{\"bucket\":{\"bucket\":\"nvs-cn-north-1-motioncapture\",\"region\":\"cn-north-1\",\"keyPrefix\":\"qiao.jia%40netvue.com/5031512940100026/\",\"keyRawPrefix\":\"<EMAIL>/5031512940100026/\"}},\"resolution\":\"MIDDLE\"},\"noTriggerTimeoutInS\":6,\"cooldownInS\":300},\"tenantId\":\"netvue\"}";
        OpenApiDeviceConfig config1 = OpenApiDeviceConfig.fromJson(imageJson);
        Assert.assertNotNull(config1);
        Map<String, String> errMap1 = OpenApiDeviceConfig.validateForCreate(config1);
        log.info("errMap1:{}", JSON.toJSONString(errMap1));
        Assert.assertEquals(0, errMap1.size());
        OpenApiDeviceConfig config2 = OpenApiDeviceConfig.fromJson(videoJson);
        Assert.assertNotNull(config2);
        Map<String, String> errMap2 = OpenApiDeviceConfig.validateForCreate(config2);
        log.info("errMap2:{}", JSON.toJSONString(errMap2));
        Assert.assertEquals(0, errMap2.size());

        Set<String> notErrKeys = new HashSet<>(Arrays.asList(
                "$.serialNumber", "$.configId", "$.tenantId",
                "$.motion.coverConfig.config.bucket.keyRawPrefix",
                "$.motion.config.config.bucket.keyRawPrefix"
        ));
        JSONObject root = JSON.parseObject(imageJson);
        FuncUtil.foreachJson(root, (parent, parentKey, val) -> {
            String[] strs = parentKey.split("\\.");
            String key = strs[strs.length - 1];
            ((JSONObject) parent).remove(key);

            OpenApiDeviceConfig config = OpenApiDeviceConfig.fromJson(root.toJSONString());
            Assert.assertNotNull(config);
            Map<String, String> errMap = OpenApiDeviceConfig.validateForCreate(config);
            log.info("parentKey={},errMap:{}", parentKey, JSON.toJSONString(errMap));

            if (notErrKeys.contains(parentKey)) {
                Assert.assertEquals(0, errMap.size());
            } else {
                Assert.assertEquals(1, errMap.size());
                String name = errMap.keySet().stream().findFirst().orElse("");
                Assert.assertEquals((String) parentKey, "$." + name);
            }

            ((JSONObject) parent).put(key, val);
        });
    }


    //    @Test
    public void pushAllDeviceConfig() {
        openApiConfigService.pushAllDeviceConfig(100);
        log.info("");
    }

    @Test
    public void test_buildDeviceConfigMessage() {
        final String sn = OpenApiUtil.shortUUID();
        final int adminId = RandomUtils.nextInt(1000_0000, 9000_0000);
        String tenantId = PAAS_OWNED_TENANT_ID;

        when(userRoleService.getDeviceAdminUser(sn)).thenReturn(0);
        {
            final Result<JSONObject> result = openApiConfigService.buildDeviceConfigMessage(sn);
            Assert.assertEquals(result.getMsg(), 0, (int) result.getResult());
        }
        when(userRoleService.getDeviceAdminUser(sn)).thenReturn(adminId);
        {
            final Result<JSONObject> result = openApiConfigService.buildDeviceConfigMessage(sn);
            Assert.assertEquals(result.getMsg(), 0, (int) result.getResult());
        }
        when(userService.queryUserById(adminId)).thenReturn(new User() {{
            setId(adminId);
            setTenantId(tenantId);
        }});
        {
            final Result<JSONObject> result = openApiConfigService.buildDeviceConfigMessage(sn);
            Assert.assertEquals(result.getMsg(), 0, (int) result.getResult());
        }
//        when(deviceConfigDAO.queryBySnAndTenantIdOrPaasOwned(sn, tenantId)).thenReturn(new OpenApiDeviceConfig());
//        {
//            final Result<JSONObject> result = openApiConfigService.buildDeviceConfigMessage(sn);
//            Assert.assertEquals(result.getMsg(), 0, (int) result.getResult());
//        }
    }

    @Test
    public void test_buildDeviceConfigMessage_netvue() {
        final String sn = OpenApiUtil.shortUUID();
        final int adminId = RandomUtils.nextInt(1000_0000, 9000_0000);
        String tenantId = "netvue";

        when(userRoleService.getDeviceAdminUser(sn)).thenReturn(adminId);
        when(userService.queryUserById(adminId)).thenReturn(new User() {{
            setId(adminId);
            setTenantId(tenantId);
            setType(UserType.THIRD.getCode());
        }});
        when(userVipService.isNoUserTier(adminId)).thenReturn(true);
        when(vipService.isVipDevice(adminId, sn)).thenReturn(false);
        when(tenantSettingService.getTenantSettingByTenantId(anyString())).thenReturn(new TenantSetting());
        {
            final Result<JSONObject> result = openApiConfigService.buildDeviceConfigMessage(sn);
            Assert.assertEquals(result.getMsg(), 0, (int) result.getResult());
        }

    }

    @Test
    public void test_buildDeviceSettingMessage() {
        final String sn = OpenApiUtil.shortUUID();
        {
            when(deviceSettingService.getDeviceSettingsBySerialNumber(sn)).thenReturn(null);
            final Result<SetRetainParamRequest> result = openApiConfigService.buildDeviceSettingMessage(sn);
            Assert.assertEquals(result.getMsg(), Integer.valueOf(-1), result.getResult());
        }
        {
            final DeviceSettingsDO deviceSettingsDO = new DeviceSettingsDO();
            when(deviceSettingService.getDeviceSettingsBySerialNumber(sn)).thenReturn(deviceSettingsDO);
            final Result<SetRetainParamRequest> result = openApiConfigService.buildDeviceSettingMessage(sn);
            Assert.assertEquals(result.getMsg(), Integer.valueOf(0), result.getResult());
        }

    }

    @Test
    public void test_VideoSlicePeriodConfig() {
        {
            VideoSlicePeriodConfig.Item errorPeriodItem = new VideoSlicePeriodConfig.Item();
            VideoSlicePeriodConfig.Item normalPeriodItem = VideoSlicePeriodConfig.Item.createDefault();

            VideoSlicePeriodConfig periodConfig = new VideoSlicePeriodConfig();
            periodConfig.setNoVipHeadSlicePeriod(errorPeriodItem);
            periodConfig.setNoVipTailSlicePeriod(normalPeriodItem);
            periodConfig.init();

            Assert.assertFalse(errorPeriodItem.isValid());
            Assert.assertTrue(normalPeriodItem.isValid());

            errorPeriodItem.setSrcValue(0);
            Assert.assertFalse(errorPeriodItem.isValid());
            errorPeriodItem.setSrcValue(2000);
            Assert.assertFalse(errorPeriodItem.isValid());
            errorPeriodItem.setDestValue(0);
            Assert.assertFalse(errorPeriodItem.isValid());
            errorPeriodItem.setDestValue(4000);
            Assert.assertFalse(errorPeriodItem.isValid());
            errorPeriodItem.setDestRatio(new BigDecimal(-0.5));
            Assert.assertFalse(errorPeriodItem.isValid());
            errorPeriodItem.setDestRatio(new BigDecimal(1.5));
            Assert.assertFalse(errorPeriodItem.isValid());
            errorPeriodItem.setDestRatio(new BigDecimal(0.5));
            Assert.assertTrue(errorPeriodItem.isValid());
        }
        {
            VideoSlicePeriodConfig periodConfig = new VideoSlicePeriodConfig();
            Assert.assertEquals(VideoSlicePeriodConfig.Item.createDefault(), periodConfig.getVipHeadSlicePeriod());
            Assert.assertEquals(VideoSlicePeriodConfig.Item.createDefault(), periodConfig.getVipTailSlicePeriod());
            Assert.assertEquals(VideoSlicePeriodConfig.Item.createDefault(), periodConfig.getNoVipHeadSlicePeriod());
            Assert.assertEquals(VideoSlicePeriodConfig.Item.createDefault(), periodConfig.getNoVipTailSlicePeriod());

            Assert.assertTrue(2000 == periodConfig.getVipHeadSlicePeriod().getValue(null));
            Assert.assertTrue(2000 == periodConfig.getVipTailSlicePeriod().getValue(null));
            Assert.assertTrue(2000 == periodConfig.getNoVipHeadSlicePeriod().getValue(null));
            Assert.assertTrue(2000 == periodConfig.getNoVipTailSlicePeriod().getValue(null));
            Assert.assertTrue(2000 == periodConfig.getVipHeadSlicePeriod().getValue(OpenApiUtil.shortUUID()));
            Assert.assertTrue(2000 == periodConfig.getVipTailSlicePeriod().getValue(OpenApiUtil.shortUUID()));
            Assert.assertTrue(2000 == periodConfig.getNoVipHeadSlicePeriod().getValue(OpenApiUtil.shortUUID()));
            Assert.assertTrue(2000 == periodConfig.getNoVipTailSlicePeriod().getValue(OpenApiUtil.shortUUID()));
        }
        {
            VideoSlicePeriodConfig periodConfig = new VideoSlicePeriodConfig();
            periodConfig.setNoVipTailSlicePeriod(new VideoSlicePeriodConfig.Item().setSrcValue(2000).setDestValue(4000).setDestRatio(new BigDecimal(0.5d)));
            Assert.assertTrue(2000 == periodConfig.getNoVipTailSlicePeriod().getValue(null));
            for (int i = 0; i < 100; i++) {
                String sn = OpenApiUtil.shortUUID();
                if (Math.abs(sn.hashCode()) % 10000 < 5000) {
                    Assert.assertEquals(sn, 4000L, (long) periodConfig.getNoVipTailSlicePeriod().getValue(sn));
                } else {
                    Assert.assertEquals(sn, 2000L, (long) periodConfig.getNoVipTailSlicePeriod().getValue(sn));
                }
                Assert.assertEquals(sn, 2000L, (long) periodConfig.getNoVipHeadSlicePeriod().getValue(sn));
                Assert.assertEquals(sn, 2000L, (long) periodConfig.getVipHeadSlicePeriod().getValue(sn));
                Assert.assertEquals(sn, 2000L, (long) periodConfig.getVipTailSlicePeriod().getValue(sn));
            }
        }
    }


    @Test
    public void test_inflateConfigMessage() throws Exception {
        OpenApiConfigService.DeviceConfigArgs args = new OpenApiConfigService.DeviceConfigArgs(1, "2", "2", "3");
        JSONObject jsonObject = new JSONObject().fluentPut("serialNumber", args.getSerialNumber());

        when(videoSlicePeriodConfig.getNoVipHeadSlicePeriod()).thenReturn(VideoSlicePeriodConfig.Item.createDefault());
        when(videoSlicePeriodConfig.getNoVipTailSlicePeriod()).thenReturn(VideoSlicePeriodConfig.Item.createDefault());
//        doReturn(new HashMap<>()).when(deviceThirdConfigService.getHost2IpFromConfig(Mockito.any()));
//        when(deviceThirdConfigService.getHost2IpFromConfig(Mockito.any())).thenReturn(new HashMap());
        when(tenantSettingService.getTenantSettingByTenantId(anyString())).thenAnswer(it -> new TenantSetting().setTenantId(it.getArgument(0)));

        openApiConfigService.inflateConfigMessage(args, jsonObject);
        openApiConfigService.inflateConfigMessageFields(args, jsonObject);

        Assert.assertTrue(ObjectUtils.equals(jsonObject.get("dnsServer"), Collections.singletonList("***************")));
    }

    @Test
    public void test_saveDbAndRemoveFields() {
        String deviceSupportResolutionStr = "" +
                "1080P,360P\n" +
//                "1280x720,1280x720,auto\n" +
//                "1280x720,640x480\n" +
                "1920x1080,1280x720\n" +
                "1920x1080,1280x720,auto\n" +
                "1920x1080,640x360,auto\n" +
                "2048x1536,640x480\n" +
                "2048x1536,640x480,auto\n" +
                "2304x1296,640x360\n" +
                "2304x1296,640x360,auto\n" +
                "2560x1440,1280x720\n" +
                "2560x1440,1280x720,auto\n" +
                "3840x2160,1280x720,auto\n" +
                "720P,1080P";
        List<CloudDeviceSupport> cloudDeviceSupports = Arrays.stream(deviceSupportResolutionStr.split("\n"))
                .map(it -> {
                    CloudDeviceRemoteDO CloudDeviceRemoteDO = new CloudDeviceRemoteDO();
                    CloudDeviceRemoteDO.setDeviceSupportResolution(it);
                    return CloudDeviceSupport.from(CloudDeviceRemoteDO);
                }).collect(Collectors.toList());

//        {
//            openApiConfigService.saveDbAndRemoveFields(null);
//        }
        for (CloudDeviceSupport cloudDeviceSupport : cloudDeviceSupports) {
            String sn = OpenApiUtil.shortUUID();
            DeviceDO[] devices = new DeviceDO[1];
            when(deviceInfoService.getDeviceSupport(eq(sn))).thenReturn(cloudDeviceSupport);
            doAnswer(ans -> {
                devices[0] = ans.getArgument(0);
                return null;
            }).when(deviceService).updateDeviceInfo(any());

            for (Resolution resolution : Resolution.values()) {

                OpenApiDeviceConfig openApiDeviceConfig = new OpenApiDeviceConfig().setSerialNumber(sn)
                        .setMotion(new OpenApiDeviceConfig.Motion()
                                .setConfig(new OpenApiDeviceConfig.MotionConfig().setResolution(resolution)));
                openApiConfigService.saveDbAndRemoveFields(openApiDeviceConfig);
                String str = "deviceSupport=" + JSON.toJSONString(cloudDeviceSupport) + ",resolution=" + resolution;
                Assert.assertNotNull(str, devices[0]);
                Assert.assertNotNull(str, devices[0].getRecResolution());
            }
        }
    }

}
