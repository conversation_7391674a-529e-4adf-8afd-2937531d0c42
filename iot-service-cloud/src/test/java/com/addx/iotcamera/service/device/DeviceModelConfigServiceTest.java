package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.dao.model.IDeviceModelConfigDAO;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import groovy.util.logging.Slf4j;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceModelConfigServiceTest {
    @InjectMocks
    private DeviceModelConfigService deviceModelConfigService;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private IDeviceModelConfigDAO iDeviceModelConfigDAO;

    private final static String MODEL_NO = "CG1";
    private final static String SERIAL_NUMBER = "serialNumber";

    @Test
    @DisplayName("model config 不存在")
    public void queryDeviceModelConfig_test_deviceModelNotExist(){

        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(null);
        DeviceModel expectedResult = null;
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("modelConfig存在,deviceSupport 不存在")
    public void queryDeviceModelConfig_test_deviceSupportNotExist(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(null);
        DeviceModel expectedResult = this.initDeviceModel();
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("modelConfig存在,deviceSupport存在,设备未上报config")
    public void queryDeviceModelConfig_test_deviceMoConfig(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(new CloudDeviceSupport());
        DeviceModel expectedResult = this.initDeviceModel();
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("modelConfig存在,deviceSupport存在,设备上报StreamProtocol,使用设备上报值")
    public void queryDeviceModelConfig_test_deviceSupportStreamProtocol(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(this.initDeviceSupportStreamProtocol());
        DeviceModel expectedResult = this.initDeviceModel();
        expectedResult.setStreamProtocol("webrtc");
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }
    private CloudDeviceSupport initDeviceSupportStreamProtocol(){
        CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.builder().supportStreamProtocol("webrtc").build();
        return cloudDeviceSupport;
    }


    @Test
    @DisplayName("modelConfig存在,deviceSupport存在,设备上报AudioCodectype,使用设备上报值")
    public void queryDeviceModelConfig_test_deviceNoSupportAudioCodectype(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(this.initDeviceSupportAudioCodectype());
        DeviceModel expectedResult = this.initDeviceModel();
        expectedResult.setAudioCodectype("PCM");
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }
    private CloudDeviceSupport initDeviceSupportAudioCodectype(){
        CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.builder().supportAudioCodectype("PCM").build();
        return cloudDeviceSupport;
    }


    @Test
    @DisplayName("modelConfig存在,deviceSupport存在,设备上报KeepAliveProtocol,使用设备上报值")
    public void queryDeviceModelConfig_test_deviceNoSupportKeepAliveProtocol(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(this.initDeviceSupportKeepAliveProtocol());
        DeviceModel expectedResult = this.initDeviceModel();
        expectedResult.setKeepAliveProtocol("udp");
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }
    private CloudDeviceSupport initDeviceSupportKeepAliveProtocol(){
        CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.builder().supportKeepAliveProtocol("udp").build();
        return cloudDeviceSupport;
    }


    @Test
    @DisplayName("modelConfig存在,deviceSupport存在,设备上报CanStandby,使用设备上报值")
    public void queryDeviceModelConfig_test_deviceNoSupportCanStandby(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(this.initDeviceSupportCanStandby());
        DeviceModel expectedResult = this.initDeviceModel();
        expectedResult.setCanStandby(true);
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }
    private CloudDeviceSupport initDeviceSupportCanStandby(){
        CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.builder().supportCanStandby(1).build();
        return cloudDeviceSupport;
    }


    @Test
    @DisplayName("modelConfig存在,deviceSupport存在,设备上报DevicePersonDetect,使用设备上报值")
    public void queryDeviceModelConfig_test_deviceNoSupportDevicePersonDetect(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(this.initDeviceSupportDevicePersonDetect());
        DeviceModel expectedResult = this.initDeviceModel();
        expectedResult.setDevicePersonDetect(true);
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }
    private CloudDeviceSupport initDeviceSupportDevicePersonDetect(){
        CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.builder().supportDevicePersonDetect(1).build();
        return cloudDeviceSupport;
    }

    private DeviceModel initDeviceModel(){
        DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName(MODEL_NO);
        deviceModel.setStreamProtocol("rtsp");
        deviceModel.setAudioCodectype("AAC");
        deviceModel.setKeepAliveProtocol("tcp");
        deviceModel.setCanStandby(false);
        deviceModel.setDevicePersonDetect(false);
        return deviceModel;
    }

    @Test
    @DisplayName("modelConfig存在,deviceSupport存在,设备上报canRotate,使用设备上报值")
    public void queryDeviceModelConfig_canRotate(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(new CloudDeviceSupport(){{
            setCanRotate(1);
        }});
        DeviceModel expectedResult = this.initDeviceModel();
        expectedResult.setCanRotate(true);
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("modelConfig存在,deviceSupport存在,设备上报supportMotionTrack,使用设备上报值")
    public void queryDeviceModelConfig_supportMotionTrack(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(new CloudDeviceSupport(){{
            setSupportMotionTrack(1);
        }});
        DeviceModel expectedResult = this.initDeviceModel();
        expectedResult.setSupportMotionTrack(true);
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("modelConfig存在,deviceSupport存在,设备上报supportFrequency,使用设备上报值")
    public void queryDeviceModelConfig_supportFrequency(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(new CloudDeviceSupport(){{
            setSupportFrequency(1);
        }});
        DeviceModel expectedResult = this.initDeviceModel();
        expectedResult.setSupportFrequency(true);
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("modelConfig存在,deviceSupport存在,设备上报antiDisassemblyAlarm,使用设备上报值")
    public void queryDeviceModelConfig_antiDisassemblyAlarm(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(MODEL_NO);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(this.initDeviceModel());
        when(deviceInfoService.getRowDeviceSupport(any())).thenReturn(new CloudDeviceSupport(){{
            setAntiDisassemblyAlarm(1);
        }});
        DeviceModel expectedResult = this.initDeviceModel();
        expectedResult.setAntiDisassemblyAlarm(true);
        DeviceModel actualResult = deviceModelConfigService.queryDeviceModelConfig(SERIAL_NUMBER);

        assertEquals(expectedResult,actualResult);
    }
}
