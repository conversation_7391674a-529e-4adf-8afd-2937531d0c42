package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.VideoReportEvent;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.PushInfo;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.enums.PushTypeEnums;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.message.MessagePushManageService;
import com.addx.iotcamera.service.xinge.PushXingeService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.addx.iot.domain.extension.ai.vo.AiTaskResult;
import org.addx.iot.domain.extension.entity.DeviceAiSwitch;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.*;

import static com.addx.iotcamera.service.video.VideoCacheServiceTest.createMockVideoCache;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoNotifyServiceTest {

    @InjectMocks
    private VideoNotifyService videoNotifyService;

    @Mock
    private VideoCacheService videoCacheService;
    @Mock
    private S3Service s3Service;
    @Mock
    private NotificationService notificationService;
    @Mock
    private MessagePushManageService messagePushManageService;
    @Mock
    private DevicePlatformEventPublisher devicePlatformEventPublisher;
    @Mock
    private PushXingeService pushXingeService;
    @Mock
    private PushService pushService;
    @Mock
    private CopyWrite copyWrite;
    @Mock
    private TimeTicker timeTicker;
    @Mock
    private VideoReportLogService videoReportLogService;
    @Mock
    private CenterNotifyConfig centerNotifyConfig;

    @Mock
    private AiAssistService aiAssistService;

    @Mock
    private DeviceService deviceService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private PushInfoService pushInfoService;

    @Mock
    private UserService userService;

    @Mock
    private FactoryDataQueryService factoryDataQueryService;
    @Mock
    private DeviceManualService deviceManualService;

    @Before
    public void init() {
        videoNotifyService.setGson(new Gson());
        when(aiAssistService.queryEventObjectSwitch(any(), any())).thenReturn(new Result(new DeviceAiSwitch(){{
            setList(Collections.emptyList());
        }}));
    }

    @After
    public void after() {

    }

    @Test
    public void test_sendMotionNotify() throws Exception {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
        {
            Field notifyCheckEnableOtherSwicthField = videoNotifyService.getClass().getDeclaredField("notifyCheckEnableOtherSwicth");
            notifyCheckEnableOtherSwicthField.setAccessible(true);
            notifyCheckEnableOtherSwicthField.set(videoNotifyService, false);
            videoNotifyService.sendMotionNotify(video);

            notifyCheckEnableOtherSwicthField.set(videoNotifyService, true);
            when(aiAssistService.queryEventObjectSwitch(any(), any())).thenReturn(new Result(new DeviceAiSwitch(){{
                setList(Collections.singletonList(null));
            }}));
            video.setEnableOther(false);
            videoNotifyService.sendMotionNotify(video);

            video.setEnableOther(true);
            notifyCheckEnableOtherSwicthField.set(videoNotifyService, true);
            videoNotifyService.sendMotionNotify(video);

            when(aiAssistService.queryEventObjectSwitch(any(), any())).thenReturn(new Result(new DeviceAiSwitch(){{
                setList(Collections.emptyList());
            }}));
        }
        {
            video.setIsNotify(false);
            videoNotifyService.sendMotionNotify(video);
        }
        video.setIsNotify(true);
        {
            video.setImageUrl(null);
            videoNotifyService.sendMotionNotify(video);
        }
        video.setImageUrl("http://addx.ai/image.jpg");
        {
            when(messagePushManageService.isPushShied(video.getAdminId())).thenReturn(true);
            videoNotifyService.sendMotionNotify(video);
        }
        when(messagePushManageService.isPushShied(video.getAdminId())).thenReturn(false);
        {
            when(videoCacheService.getPushInfo(video, video.getAdminId())).thenReturn(null);
            videoNotifyService.sendMotionNotify(video);
        }
        when(videoCacheService.getPushInfo(video, video.getAdminId())).thenReturn(new PushInfo());
        {
            videoNotifyService.sendMotionNotify(video);
            Assert.assertTrue(video.getPirNotifyPushedKeys().contains(video.getAdminId() + ":motion"));
        }
        {
            videoNotifyService.sendMotionNotify(video);
            Assert.assertTrue(video.getPirNotifyPushedKeys().contains(video.getAdminId() + ":motion"));
        }

    }

    public static List<AiEvent> createMockEvents() {
        LinkedList<AiEvent> events = new LinkedList<>();
        events.add(new AiEvent().setEventObject(AiObjectEnum.PERSON)
                .setEventType(AiObjectActionEnum.EXIST)
                .setActivatedZones(Arrays.asList(201))
        );
        events.add(new AiEvent().setEventObject(AiObjectEnum.VEHICLE)
                .setEventType(AiObjectActionEnum.VEHICLE_ENTER)
                .setActivatedZones(Arrays.asList(102))
        );
        events.add(new AiEvent().setEventObject(AiObjectEnum.VEHICLE)
                .setEventType(AiObjectActionEnum.VEHICLE_HELD_UP)
                .setActivatedZones(Arrays.asList(102))
        );
        events.add(new AiEvent().setEventObject(AiObjectEnum.VEHICLE)
                .setEventType(AiObjectActionEnum.VEHICLE_OUT)
                .setActivatedZones(Arrays.asList(102))
        );
        events.add(new AiEvent().setEventObject(AiObjectEnum.VEHICLE)
                .setEventType(AiObjectActionEnum.VEHICLE_OUT)
                .setLabelId("label123")
                .setActivatedZones(Arrays.asList(102))
        );
        events.add(new AiEvent().setEventObject(AiObjectEnum.PACKAGE)
                .setEventType(AiObjectActionEnum.PACKAGE_PICK_UP)
                .setActivatedZones(Arrays.asList(102))
        );
        return events;
    }

    @Test
    public void test_sendAIResultNotify() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
//        {
//            video.setIsNotify(false);
//            videoNotifyService.sendAIResultNotify(video, null);
//        }
        video.setIsNotify(true);
        video.setIsMergeMessage(true);

        AiTaskResult input = new AiTaskResult();
        input.setTraceId(traceId);
        input.setSerialNumber(sn);
        input.setOrder(1);
        input.setImageUrl("http://addx.ai/image.jpg");
        {
            input.setEvents(new LinkedList<>());
            videoNotifyService.sendAIResultNotify(video, input);
        }
        input.setEvents(createMockEvents());
        {
            video.setIsMergeMessage(false);
            when(messagePushManageService.isPushShied(video.getAdminId())).thenReturn(true);
            videoNotifyService.sendAIResultNotify(video, input);
        }
        {
            video.setIsMergeMessage(true);
            when(messagePushManageService.isPushShied(video.getAdminId())).thenReturn(true);
            videoNotifyService.sendAIResultNotify(video, input);
        }
        when(messagePushManageService.isPushShied(video.getAdminId())).thenReturn(false);
        {
            when(videoCacheService.getPushInfo(video, video.getAdminId())).thenReturn(null);
            videoNotifyService.sendAIResultNotify(video, input);
        }
        when(videoCacheService.getPushInfo(video, video.getAdminId())).thenReturn(new PushInfo());
        {
            videoNotifyService.sendAIResultNotify(video, input);
            Assert.assertFalse(video.getPirNotifyPushedKeys().contains(video.getAdminId() + ":person:exist"));
            Assert.assertTrue(video.getPirNotifyPushedKeys().contains(video.getAdminId() + ":vehicle:vehicle_enter"));
            Assert.assertFalse(video.getPirNotifyPushedKeys().contains(video.getAdminId() + ":vehicle:vehicle_held_up"));
            Assert.assertFalse(video.getPirNotifyPushedKeys().contains(video.getAdminId() + ":vehicle:vehicle_out"));
        }

    }

    @Test
    public void test_pushAiMessage() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
        String videoEvent = (System.currentTimeMillis() / 1000) + "";

        NotificationService.PushContext pushCtx = new NotificationService.PushContext();
        pushCtx.setUser(new User() {{
            setId(video.getAdminId());
        }});
        pushCtx.setAdminId(video.getAdminId());
        pushCtx.setShowPossibleSubcategoryText(true);

        AiTaskResult input = new AiTaskResult();
        input.setTraceId(traceId);
        input.setSerialNumber(sn);
        input.setOrder(1);
        input.setImageUrl("http://addx.ai/image.jpg");
        input.setEvents(createMockEvents());
        {
            video.setIsNotify(false);
            videoNotifyService.pushAiMessage(video, pushCtx, input, videoEvent);
        }
        video.setIsNotify(true);
        {
            pushCtx.setPushInfo(new PushInfo() {{
                setMsgType(PushTypeEnums.PUSH_XINGE.getCode());
                setMsgToken("token");
            }});
            doThrow(RuntimeException.class).when(pushXingeService).pushMessage(any(), any());
            videoNotifyService.pushAiMessage(video, pushCtx, input, videoEvent);
            doNothing().when(pushXingeService).pushMessage(any(), any());
            videoNotifyService.pushAiMessage(video, pushCtx, input, videoEvent);
        }
        {
            pushCtx.setPushInfo(new PushInfo() {{
                setMsgType(PushTypeEnums.PUSH_FCM.getCode());
                setMsgToken("token");
            }});
            doThrow(RuntimeException.class).when(pushXingeService).pushMessage(any(), any());
            videoNotifyService.pushAiMessage(video, pushCtx, input, videoEvent);
            doNothing().when(pushXingeService).pushMessage(any(), any());
            videoNotifyService.pushAiMessage(video, pushCtx, input, videoEvent);
        }

    }

    @Test
    public void test_notifyVideoReportEvent() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);

        JSONObject config = new JSONObject();
        for (String key : VideoReportEvent.reportEvent2ConfigKey.values()) {
            config.fluentPut(key, new JSONObject().fluentPut(video.getUsers().get(0).getLanguage(), "value_zh"));
        }
        when(copyWrite.getConfig()).thenReturn((Map) config);
        video.setImageUrl(null);
        {
            when(messagePushManageService.isPushShied(video.getAdminId())).thenReturn(true);
            videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DOORBELL_PRESS, MsgType.DEVICE_CALL_MSG);
        }
        when(messagePushManageService.isPushShied(video.getAdminId())).thenReturn(false);
        {
            videoNotifyService.notifyVideoReportEvent(video, EReportEvent.PIR, MsgType.NEW_VIDEO_MSG);
        }
        {
            when(videoCacheService.getPushInfo(video,video.getAdminId())).thenReturn(null);
            videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DOORBELL_PRESS, MsgType.DEVICE_CALL_MSG);
        }
        {
            when(videoCacheService.getPushInfo(video,video.getAdminId())).thenReturn(new PushInfo() {{
                setMsgType(PushTypeEnums.PUSH_XINGE.getCode());
                setMsgToken("token");
            }});
            videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DOORBELL_PRESS, MsgType.DEVICE_CALL_MSG);
        }
        {
            videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DOORBELL_PRESS, MsgType.DEVICE_CALL_MSG);
        }
        {
            when(videoCacheService.getPushInfo(video,video.getAdminId())).thenReturn(new PushInfo() {{
                setMsgType(PushTypeEnums.PUSH_FCM.getCode());
                setMsgToken("token");
            }});
            videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DOORBELL_REMOVE, MsgType.DEVICE_CALL_MSG);
        }

    }

    @Test
    public void test_getVideoReportEventMsgContent() {
        VideoCommonCache.UserSimple user = new VideoCommonCache.UserSimple()
                .setId(123).setTenantId("guard").setLanguage("zh");
        EReportEvent reportEvent = EReportEvent.DOORBELL_PRESS;
        String key = "doorbellPressed";
        {
            when(copyWrite.getConfig()).thenReturn(new LinkedHashMap<>());
            Assert.assertEquals(null, videoNotifyService.getVideoReportEventMsgContent(user, reportEvent));
        }
        {
            when(copyWrite.getConfig()).thenReturn((Map) new JSONObject()
                    .fluentPut(key, new JSONObject().fluentPut("en", "value_en")));
            Assert.assertEquals("value_en", videoNotifyService.getVideoReportEventMsgContent(user, reportEvent));
        }
        {
            when(copyWrite.getConfig()).thenReturn((Map) new JSONObject()
                    .fluentPut(key, new JSONObject().fluentPut(user.getLanguage(), "value_zh")));
            Assert.assertEquals("value_zh", videoNotifyService.getVideoReportEventMsgContent(user, reportEvent));
        }
    }

    @Test
    @DisplayName("4G设备推送")
    public void test_sendMotionNotify4G(){
        String serialNumber = "sn";
        String traceId = "traceId";

        {
            when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().pushIgnored(true).build());
            videoNotifyService.sendMotionNotify4G(serialNumber,traceId);
        }
        when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().pushIgnored(false).build());
        {
            when(userRoleService.findAllUsersForDevice(any())).thenReturn(Lists.newArrayList());
            videoNotifyService.sendMotionNotify4G(serialNumber,traceId);
        }


        when(userRoleService.findAllUsersForDevice(any())).thenReturn(Arrays.asList(1,2,3));
        when(messagePushManageService.isPushShied(1)).thenReturn(true);
        when(messagePushManageService.isPushShied(2)).thenReturn(false);
        when(messagePushManageService.isPushShied(3)).thenReturn(false);

        when(pushInfoService.getPushInfo(2)).thenReturn(null);
        when(pushInfoService.getPushInfo(3)).thenReturn(new PushInfo());
        when(userService.queryUserById(any())).thenReturn(new User());
        doNothing().when(notificationService).pushPirNotify(any(), any(), any(), any(), any(), any());
        videoNotifyService.sendMotionNotify4G(serialNumber,traceId);

    }
}
