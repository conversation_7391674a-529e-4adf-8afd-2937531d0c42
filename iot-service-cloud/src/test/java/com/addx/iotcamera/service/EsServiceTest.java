package com.addx.iotcamera.service;

import com.addx.iotcamera.config.EsConfig;
import com.addx.iotcamera.testutil.TestHelper;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.core5.http.ClassicHttpResponse;
import org.apache.hc.core5.http.HttpStatus;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class EsServiceTest {

    @Mock
    private EsConfig esConfig;
    @InjectMocks
    private EsService esService;

    @Before
    public void init() {
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        JSONObject es = testHelper.getConfig().getJSONObject("elastic-search");
        EsConfig esConfig = es.toJavaObject(EsConfig.class);
        esConfig.setEndpoints(Arrays.asList("127.0.0.1"));
        when(this.esConfig.getIndexes()).thenAnswer(AdditionalAnswers.delegatesTo(esConfig));
        when(this.esConfig.getEndpoints()).thenAnswer(AdditionalAnswers.delegatesTo(esConfig));
        when(this.esConfig.getSplitByDayIndexes()).thenAnswer(AdditionalAnswers.delegatesTo(esConfig));
        when(this.esConfig.isSplitByDayIndexes(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(esConfig));
    }

    @Test
    public void test_esConfig() {
        esConfig.init();
        Assert.assertTrue(esConfig.isSplitByDayIndexes("paas"));
    }

    //    @Test
    public void test_init() {
        esService.init();
    }

    @Test
    public void test_queryById() {
        JSONObject obj = esService.queryById("netTestCmd", "id1");
        Assert.assertNull(obj);
    }

    @Mock
    private CloseableHttpClient httpClient;
    @Mock
    private ClassicHttpResponse httpResponse;

    @Test
    @SneakyThrows
    public void test_search() {
        {
            List<JSONObject> result = esService.search("key", new JSONObject());
            Assert.assertNull(result);
        }
        String key = "netTestResult";
        {
            esService.setHttpClientFactory(() -> {
                throw new RuntimeException("");
            });
            List<JSONObject> result = esService.search(key, new JSONObject());
            Assert.assertNull(result);
        }
        esService.setHttpClientFactory(() -> httpClient);
        {
            esService.setExecuteHttpRequest((client, req) -> {
                throw new RuntimeException("");
            });
            List<JSONObject> result = esService.search(key, new JSONObject());
            Assert.assertNull(result);
        }
        esService.setExecuteHttpRequest((client, req) -> httpResponse);
        when(httpResponse.getEntity()).thenAnswer(it -> new StringEntity(new JSONObject().fluentPut("hits", new JSONObject().fluentPut("hits", new JSONArray().fluentAdd(new JSONObject().fluentPut("_source", new JSONObject().fluentPut("id", "111"))).fluentAdd(new JSONObject().fluentPut("_source", new JSONObject().fluentPut("id", "112"))))).toJSONString()));
        {
            when(httpResponse.getCode()).thenReturn(HttpStatus.SC_NOT_FOUND);
            List<JSONObject> result = esService.search(key, new JSONObject());
            Assert.assertNull(result);
        }
        when(httpResponse.getCode()).thenReturn(HttpStatus.SC_OK);
        {
            List<JSONObject> result = esService.search(key, new JSONObject());
            Assert.assertEquals(2, result.size());
        }
    }

    @Test
    public void test_buildSearchBodyByTimestampDesc() {
        JSONObject mustCondition = new JSONObject().fluentPut("term", new JSONObject().fluentPut("deviceSn", "sn1"));
        final EsService.SearchByTimestampDesc input = new EsService.SearchByTimestampDesc().setMustCondition(Arrays.asList(mustCondition));        input.setBeginTime(System.currentTimeMillis()).setEndTime(null).setFrom(null).setSize(null);
        EsService.buildSearchBodyByTimestampDesc(input);
        input.setBeginTime(null).setEndTime(System.currentTimeMillis()).setFrom(null).setSize(null);
        EsService.buildSearchBodyByTimestampDesc(input);
        input.setBeginTime(null).setEndTime(null).setFrom(0).setSize(null);
        EsService.buildSearchBodyByTimestampDesc(input);
        input.setBeginTime(null).setEndTime(null).setFrom(0).setSize(10);
        EsService.buildSearchBodyByTimestampDesc(input);
    }

//    @Test
    public void test_searchByTimestampDesc() {
        JSONObject mustCondition = new JSONObject().fluentPut("term", new JSONObject().fluentPut("deviceSn", "sn1"));
        {
            esService.setExecuteHttpRequest((client, req) -> null);
            List<JSONObject> result = esService.searchByTimestampDesc(EsConfig.INDEX_NET_TEST_CMD, mustCondition, null, null, null);
            Assert.assertEquals(Collections.emptyList(), result);
        }
        String key = "netTestResult";
        {
            esService.setExecuteHttpRequest((client, req) -> {
                throw new RuntimeException("");
            });
            List<JSONObject> result = esService.searchByTimestampDesc(EsConfig.INDEX_NET_TEST_CMD, mustCondition, null, null, null);
            Assert.assertEquals(Collections.emptyList(), result);
        }
        esService.setHttpClientFactory(() -> httpClient);
        {
            esService.setExecuteHttpRequest((client, req) -> {
                throw new RuntimeException("");
            });
            List<JSONObject> result = esService.searchByTimestampDesc(EsConfig.INDEX_NET_TEST_CMD, mustCondition, null, null, null);
            Assert.assertEquals(Collections.emptyList(), result);
        }
        esService.setExecuteHttpRequest((client, req) -> httpResponse);
        when(httpResponse.getEntity()).thenAnswer(it -> new StringEntity(new JSONObject().fluentPut("hits", new JSONObject()
                .fluentPut("hits", new JSONArray()
                        .fluentAdd(new JSONObject().fluentPut("_source", new JSONObject().fluentPut("id", "111")))
                        .fluentAdd(new JSONObject().fluentPut("_source", new JSONObject().fluentPut("id", "112")))
                        .fluentAdd(new JSONObject().fluentPut("_source", new JSONObject().fluentPut("id", "111")))
                        .fluentAdd(new JSONObject().fluentPut("_source", new JSONObject().fluentPut("id", "113")))
                )).toJSONString()));
        {
            when(httpResponse.getCode()).thenReturn(HttpStatus.SC_NOT_FOUND);
            List<JSONObject> result = esService.searchByTimestampDesc(EsConfig.INDEX_NET_TEST_CMD, mustCondition, null, null, null);
            Assert.assertEquals(Collections.emptyList(), result);
        }
        when(httpResponse.getCode()).thenReturn(HttpStatus.SC_OK);
        {
            List<JSONObject> result = esService.searchByTimestampDesc(EsConfig.INDEX_NET_TEST_CMD, mustCondition, null, null, null);
            Assert.assertEquals(3, result.size());
        }
        {
            List<JSONObject> result = esService.searchByTimestampDesc(EsConfig.INDEX_NET_TEST_CMD, mustCondition, null, null, 2);
            Assert.assertEquals(2, result.size());
        }
        when(httpResponse.getEntity()).thenAnswer(it -> {
            JSONArray arr = new JSONArray();
            for (int i = 0; i < 201; i++) {
                arr.fluentAdd(new JSONObject().fluentPut("_source", new JSONObject().fluentPut("id", i).fluentPut("@timestamp", i)));
            }
            return new StringEntity(new JSONObject().fluentPut("hits", new JSONObject().fluentPut("hits", arr)).toJSONString());
        });
        {
            List<JSONObject> result = esService.searchByTimestampDesc(EsConfig.INDEX_NET_TEST_CMD, mustCondition, null, null, 201);
            Assert.assertEquals(201, result.size());
        }
        {
            List<JSONObject> result = esService.searchByTimestampDesc(EsConfig.INDEX_NET_TEST_CMD, mustCondition, null, null, 10);
            Assert.assertEquals(10, result.size());
        }
    }

    @Test
    public void test_buildCondition() {
        final JSONObject obj = EsService.buildCondition(Arrays.asList("a", "b"), "v");
        final String expectVal = new JSONObject().fluentPut("a", new JSONObject().fluentPut("b", "v")).toJSONString();
        Assert.assertEquals(expectVal, obj.toJSONString());
    }
}
