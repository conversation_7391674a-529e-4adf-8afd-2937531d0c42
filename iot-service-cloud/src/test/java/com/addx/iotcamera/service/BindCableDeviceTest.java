package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.BindOperationRequest;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.domain.IpInfo;
import com.addx.iotcamera.bean.domain.LocationDO;
import com.addx.iotcamera.config.apollo.DeviceLanguageConfig;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.enums.BindContentSrc;
import com.addx.iotcamera.enums.DeviceNetType;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.openapi.NodeMatcherAgency;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DuplicateKeyException;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class BindCableDeviceTest {

    @InjectMocks
    private BindService bindService;
    @Mock
    private FactoryDataQueryService factoryDataQueryService;
    @Mock
    private NodeMatcherAgency nodeMatcherAgency;
    @Mock
    private LocationInfoService locationInfoService;
    @Mock
    private RedisService redisService;
    @Mock
    private IDeviceDAO deviceDAO;
    @Mock
    private DeviceLanguageConfig deviceLanguageConfig;
    @Mock
    private ReportLogService reportLogService;

    private TestHelper testHelper;

    @Before
    public void before() {
        testHelper = TestHelper.getInstanceByEnv("test");

        DeviceManufactureTableDO deviceManu = DeviceManufactureTableDO.builder()
                .serialNumber("sn_test_" + System.currentTimeMillis())
                .build();
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(anyString())).thenReturn(deviceManu);

        when(this.nodeMatcherAgency.setBindContent(anyString(), anyString(), anyString(), anyInt())).thenReturn(true);
        when(this.nodeMatcherAgency.deleteBindContent(any(), any())).thenReturn(true);
        when(this.nodeMatcherAgency.queryBindContent(anyString())).thenReturn("any_bind_content");

        LocationDO locationDO = new LocationDO();
        locationDO.setId(123456789);
        when(locationInfoService.listUserLocations(any())).thenReturn(Arrays.asList(locationDO));
        when(locationInfoService.insertLocationReturnId(any())).thenReturn(locationDO);
        when(locationInfoService.createDefaultLocationByLanguageAndAdminId(anyString(), any(), anyString(),any())).thenReturn(locationDO);
        when(redisService.get(anyString())).thenReturn("" + System.currentTimeMillis());
        when(deviceLanguageConfig.getConfig()).thenReturn(new LinkedHashMap<>());
    }

    @SneakyThrows
    @Test
    public void test_bindCableDevice() {
        String json = "{\"bindCode\":\"248-1650537523074\",\"codeType\":1,\"type\":0,\"width\":864,\"height\":864,\"timeZone\":\"Asia/Shanghai\",\"countryNo\":\"CN\",\"deviceLanguage\":\"cn\",\"language\":\"zh\",\"locationId\":-1,\"deviceNetType\":0,\"bindContentSrc\":2," +
                "\"userSn\":\"ACIADDX20202007\",\"app\":{\"appName\":\"全橙看家 Stage\",\"appType\":\"Android\",\"bundle\":\"com.ai.addx.guard\",\"channelId\":0,\"countlyId\":\"24fefcab16e3bad2\",\"tenantId\":\"guard\",\"version\":200200600,\"versionName\":\"2.6.0\"}}";
        BindOperationRequest request = JSON.parseObject(json, BindOperationRequest.class);
        IpInfo ipInfo = IpInfo.builder().ip("127.0.0.1").countryCode("cn").build();
        try {
            Result result = bindService.bindCableDevice(12345, request, ipInfo);
            Assert.assertEquals((Integer) 0, result.getResult());
        } catch (Throwable e) {
        }
    }

    @SneakyThrows
    @Test
    public void test_bindCableDevice_exception() {
        AtomicInteger num = new AtomicInteger(0);
        when(deviceDAO.initBindOperation(any())).thenAnswer(it -> {
            if (num.getAndIncrement() < 1) {
                throw new DuplicateKeyException("");
            } else {
                return 1;
            }
        });
        String json = "{\"bindCode\":\"248-1650537523074\",\"codeType\":1,\"type\":0,\"width\":864,\"height\":864,\"timeZone\":\"Asia/Shanghai\",\"countryNo\":\"CN\",\"deviceLanguage\":\"cn\",\"language\":\"zh\",\"locationId\":-1,\"deviceNetType\":0,\"bindContentSrc\":2," +
                "\"userSn\":\"ACIADDX20202007\",\"app\":{\"appName\":\"全橙看家 Stage\",\"appType\":\"Android\",\"bundle\":\"com.ai.addx.guard\",\"channelId\":0,\"countlyId\":\"24fefcab16e3bad2\",\"tenantId\":\"guard\",\"version\":200200600,\"versionName\":\"2.6.0\"}}";
        BindOperationRequest request = JSON.parseObject(json, BindOperationRequest.class);
        IpInfo ipInfo = IpInfo.builder().ip("127.0.0.1").countryCode("cn").build();
        try {
            Result result = bindService.bindCableDevice(12345, request, ipInfo);
            Assert.assertEquals((Integer) 0, result.getResult());
        } catch (Throwable e) {
        }
    }

    @SneakyThrows
    @Test
    public void test_nodeMatcherAgency() {
        JSONObject nodeMatcherConfig = testHelper.getConfig().getJSONObject("codebind").getJSONObject("node-matcher");
        NodeMatcherAgency nodeMatcherAgency = new NodeMatcherAgency();
        nodeMatcherAgency.setEndpoint(nodeMatcherConfig.getString("endpoint"));
        nodeMatcherAgency.setAccessKey(nodeMatcherConfig.getString("accessKey"));
        nodeMatcherAgency.setSecretKey(nodeMatcherConfig.getString("secretKey"));
        /*
        when(this.nodeMatcherAgency.setBindContent(anyString(), anyString(), anyString(), anyInt()))
                .thenAnswer(AdditionalAnswers.delegatesTo(nodeMatcherAgency));
        when(this.nodeMatcherAgency.deleteBindContent(any(), any()))
                .thenAnswer(AdditionalAnswers.delegatesTo(nodeMatcherAgency));
        when(this.nodeMatcherAgency.queryBindContent(anyString()))
                .thenAnswer(AdditionalAnswers.delegatesTo(nodeMatcherAgency));
        */
        String sn = "test_sn_" + OpenApiUtil.shortUUID();
        String userSn = "test_userSn_" + OpenApiUtil.shortUUID();
        String content = "content_" + OpenApiUtil.shortUUID();
        {
            boolean setResult = nodeMatcherAgency.setBindContent(userSn, sn, content, 3600);
            Assert.assertTrue(setResult);
            String realContent = nodeMatcherAgency.queryBindContent(sn);
            Assert.assertEquals(content, realContent);
            boolean deleteResult = nodeMatcherAgency.deleteBindContent(null, sn);
            Assert.assertTrue(deleteResult);
        }
        {
            boolean setResult = nodeMatcherAgency.setBindContent(null, null, content, 3600);
            Assert.assertFalse(setResult);
            String realContent = nodeMatcherAgency.queryBindContent(null);
            Assert.assertEquals("", realContent);
            boolean deleteResult = nodeMatcherAgency.deleteBindContent(null, null);
            Assert.assertFalse(deleteResult);
        }
    }

    @Test
    public void test_BindContentSrc_codeOf() {
        Assert.assertEquals(BindContentSrc.QRCODE, BindContentSrc.codeOf(0));
        Assert.assertEquals(BindContentSrc.AP, BindContentSrc.codeOf(1));
        Assert.assertEquals(BindContentSrc.NODE_MATCHER, BindContentSrc.codeOf(2));
        Assert.assertEquals(BindContentSrc.QRCODE, BindContentSrc.codeOf(null));
    }

    @Test
    public void test_DeviceNetType_codeOf() {
        Assert.assertEquals(DeviceNetType.WIFI, DeviceNetType.codeOf(0));
        Assert.assertEquals(DeviceNetType.CABLE, DeviceNetType.codeOf(1));
        Assert.assertEquals(DeviceNetType.WIFI, DeviceNetType.codeOf(null));
    }

}
