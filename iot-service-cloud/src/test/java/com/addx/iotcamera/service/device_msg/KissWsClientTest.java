package com.addx.iotcamera.service.device_msg;

import com.addx.iotcamera.bean.device_msg.*;
import com.addx.iotcamera.kiss.KissFinder;
import com.addx.iotcamera.kiss.KissNodeType;
import com.addx.iotcamera.kiss.node.KissNode;
import com.addx.tracking.interceptor.impl.WebSocketInterceptor;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.opentelemetry.api.OpenTelemetry;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.SpringContextUtil;
import org.java_websocket.handshake.ServerHandshake;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedList;

import static org.powermock.api.mockito.PowerMockito.mock;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtil.class})
@Slf4j
public class KissWsClientTest {

    private KissWsClient kissWsClient;

    @Mock
    private ServerHandshake serverHandshake;
    @Mock
    private KissFinder kissFinder;
    @Mock
    OpenTelemetry openTelemetry;
    @Mock
    WebSocketInterceptor webSocketInterceptor;
    @Mock
    ApplicationContext applicationContext;

    private LinkedList<KissDeviceNode> kissDeviceNodeList = new LinkedList<>();
    private LinkedList<DeviceMsgAck> retainedMsgAckList = new LinkedList<>();
    private LinkedList<DeviceMsgAck> cmdAckList = new LinkedList<>();

    private LinkedList<IotWsReq> reqList = new LinkedList<>();
    private LinkedList<IotWsResp> respList = new LinkedList<>();
    private LinkedList<JSONObject> transmitList = new LinkedList<>();

    @Before
    public void init() {
        kissWsClient = new KissWsClient(URI.create("wss://127.0.0.1:18888"));
        kissWsClient.setKissDeviceNodesListener(kissDeviceNodeList::addAll);
        kissWsClient.setRetainedMsgAckListener(retainedMsgAckList::add);
        kissWsClient.setCmdAckListener(cmdAckList::add);
        kissWsClient.setReqListener(reqList::add);
        kissWsClient.setRespListener(respList::add);
        kissWsClient.setOpenTelemetry(openTelemetry);
        kissWsClient.setTransmitListener(transmitList::add);
    }

    @Test
    public void test() {
        kissWsClient.onOpen(serverHandshake);

        kissWsClient.onClose(3001, "", true);
        kissFinder.getNodeMapByType(KissNodeType.kiss).put("127.0.0.1", new KissNode());
        kissWsClient.onClose(3001, "", true);

        kissWsClient.onError(new Exception("mockError!"));
        Assert.assertNotNull(kissWsClient.toString());

    }

    @Test
    public void test_onMessage() {
        PowerMockito.mockStatic(SpringContextUtil.class);
        Mockito.when(SpringContextUtil.getBean(WebSocketInterceptor.class)).thenReturn(webSocketInterceptor);

        Runnable runnableMock = mock(Runnable.class);
        // 模拟webSocketInterceptor.wrapper方法

        kissWsClient.setRawMsgListener(null);
        {
            kissWsClient.onMessageWithoutWrapper("");
        }
        kissWsClient.setRawMsgListener(rawMsg -> true);
        {
            final JSONObject rawMsgObj = new JSONObject();
            kissWsClient.onMessageWithoutWrapper(rawMsgObj.toJSONString());
        }
        kissWsClient.setRawMsgListener(rawMsg -> false);
        {
            final JSONObject rawMsgObj = new JSONObject();
            kissWsClient.onMessageWithoutWrapper(rawMsgObj.toJSONString());
        }
        {
            final KissDeviceNode kissDeviceNode = new KissDeviceNode().setKissIp("127.0.0.1").setDeviceSn("sn1")
                    .setDeviceStatus(KissDeviceStatus.normal).setLastUpdateTime(System.currentTimeMillis());
            final JSONObject rawMsgObj = new JSONObject()
                    .fluentPut("method", KissMsgMethod.DEVICE_NODE_UPDATE.name())
                    .fluentPut("deviceNodes", Arrays.asList(kissDeviceNode));
            kissWsClient.onMessageWithoutWrapper(rawMsgObj.toJSONString());
            Assert.assertEquals(kissDeviceNodeList.getLast(), kissDeviceNode);
        }
        {
            final DeviceMsgAck ack = new DeviceMsgAck().setMethod(KissMsgMethod.CMD_ACK.name());
            kissWsClient.onMessageWithoutWrapper(JSONObject.toJSONString(ack));
            Assert.assertEquals(cmdAckList.getLast(), ack);
        }
        {
            final DeviceMsgAck ack = new DeviceMsgAck().setMethod(KissMsgMethod.RETAINED_MSG_ACK.name());
            kissWsClient.onMessageWithoutWrapper(JSONObject.toJSONString(ack));
            Assert.assertEquals(retainedMsgAckList.getLast(), ack);
        }
        {
            IotWsReq<Object> req = new IotWsReq<>().setName(IotWsReqName.queryClientCountryNo.name());
            kissWsClient.onMessageWithoutWrapper(JSONObject.toJSONString(req));
            Assert.assertEquals(reqList.getLast(), req);
        }
        {
            IotWsReq<Object> req = new IotWsReq<>().setName(IotWsReqName.deviceReportEvent.name());
            kissWsClient.onMessageWithoutWrapper(JSONObject.toJSONString(req));
            Assert.assertEquals(reqList.getLast(), req);
        }
        {
            IotWsResp<Object> req = new IotWsResp<>().setName("");
            kissWsClient.onMessageWithoutWrapper(JSONObject.toJSONString(req));
            Assert.assertEquals(respList.getLast(), req);
            kissWsClient.onMessageWithWrapper(JSON.toJSONString(req));
        }
        {
            kissWsClient.onMessageWithoutWrapper("");
            kissWsClient.bytesToString("aaa".getBytes(StandardCharsets.UTF_8));
        }
    }

    @Test
    public void test_onMessage_TRANSMIT() {
        String jsonStr = "{\"recipientClientId\":\"iot-service\",\"method\":\"TRANSMIT\",\"messageType\":\"REPORT_CAPABILITY_SET\",\"messagePayload\":\"eyJic3RhdGlvbmQiOiJ7XCJzdGF0aW9uU3RhdHVzXCI6e1widmVyc2lvblwiOlwiMC4zLjRcIixcInN0YXRpb25TZXJpYWxOdW1iZXJcIjpcIjgwMTAxZGI5MjkwODIwMmNmZmMxOTNiNjkxNTUxNzVhXCIsXCJzdGF0aW9uTWFjXCI6XCJlMDowMTpjNzpjMDo2MTpmZVwiLFwiYXBwbGljYXRpb25zXCI6W3tcIm5hbWVcIjpcImlvdC1sb2NhbFwiLFwidmVyc2lvblwiOlwiMC4wLjQ1XCIsXCJnaXRzaGFcIjpcIjBiOTdjNFwifSx7XCJuYW1lXCI6XCJzYWZlLXJ0Y1wiLFwidmVyc2lvblwiOlwiMC4wLjM5XCIsXCJnaXRzaGFcIjpcIjUzMjgyMVwifSx7XCJuYW1lXCI6XCJic3RhdGlvbmRcIixcInZlcnNpb25cIjpcIjAuMC41N1wiLFwiZ2l0c2hhXCI6XCIwN2YxMjVcIn0se1wibmFtZVwiOlwiYWktc3RhdGlvblwiLFwidmVyc2lvblwiOlwiMC4xLjFcIixcImdpdHNoYVwiOlwiMzA4NDRiXCJ9XX19IiwiaW90LWxvY2FsIjoie1wic3RhdGlvblN1cHBvcnRMaXN0XCI6W3tcIm5hbWVcIjpcIkFsZXhhXCIsXCJ2YWx1ZVwiOjEsXCJ2ZXJzaW9uXCI6MSxcImV4dFwiOlwie31cIn0se1wibmFtZVwiOlwiU21hcnRBbGVydFwiLFwidmFsdWVcIjoxLFwidmVyc2lvblwiOjEsXCJleHRcIjpcInt9XCJ9LHtcIm5hbWVcIjpcIlNlY3VyaXR5TW9kZVwiLFwidmFsdWVcIjoxLFwidmVyc2lvblwiOjEsXCJleHRcIjpcInt9XCJ9LHtcIm5hbWVcIjpcIkZhbWlsaWFyRmFjZVwiLFwidmFsdWVcIjoxLFwidmVyc2lvblwiOjEsXCJleHRcIjpcInt9XCJ9LHtcIm5hbWVcIjpcIkNyb3NzQ2FtZXJhXCIsXCJ2YWx1ZVwiOjEsXCJ2ZXJzaW9uXCI6MSxcImV4dFwiOlwie31cIn1dfSIsInJlc3VsdCI6MH0=\",\"traceparent\":\"00-23146024c6b7c00fbef9883b3da1acb7-6ca62a1decfcd1b1-01\",\"senderClientId\":\"80101db92908202cffc193b69155175a\",\"version\":\"0.0.1\",\"senderChannelId\":\"0xfb33303b:02428dfffe66cce0-00000008-0000153d-9fcfbe589b0e6439-fb33303b\",\"timestamp\":1727145018659}";
        JSONObject req = JSON.parseObject(jsonStr);
        kissWsClient.onMessageWithoutWrapper(jsonStr);
        Assert.assertEquals(transmitList.getLast(), req);
    }

}
