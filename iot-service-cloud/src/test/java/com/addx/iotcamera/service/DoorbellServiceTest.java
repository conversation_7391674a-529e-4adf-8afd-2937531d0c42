package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.device.DeviceReportEventDO;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.DoorbellService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DoorbellServiceTest {

    @InjectMocks
    @Spy
    private DoorbellService doorbellService;

    @Mock
    private RedisService redisService;

    @Mock
    private NotificationService notificationService;

    @Mock
    private LibraryService libraryService;
    @Mock
    private LibraryUpdateReceiveAllService libraryUpdateReceiveAllService;
    @Mock
    private VideoService videoService;
    @Mock
    private DeviceSettingService deviceSettingService;

    @Before
    public void beforeTest() {
        doNothing().when(libraryUpdateReceiveAllService).updateReceiveAllSlice(any());
        when(videoService.updateVideoReportEvents(anyString(),anyString(),any())).thenReturn(null);

    }

    @Test
    public void testOnPressOrRemove_noLibrary() {
        // when(libraryService.getLibraryTrace(anyString())).thenReturn(null);

        DeviceReportEventDO deviceReportEventDO = new DeviceReportEventDO();
        deviceReportEventDO.setSerialNumber("sn_01");
        deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue());

        when(redisService.hashGetString(anyString(), anyString())).thenReturn(null);
        deviceReportEventDO.getValue().setEvent(EReportEvent.DOORBELL_PRESS.getEventId());
        doorbellService.onPress(deviceReportEventDO);
        deviceReportEventDO.getValue().setEvent(EReportEvent.DOORBELL_REMOVE.getEventId());
        doorbellService.onRemove(deviceReportEventDO);

        when(redisService.hashGetString(anyString(), anyString())).thenReturn("[{\"event\":18},{\"event\":19}]");
        deviceReportEventDO.getValue().setEvent(EReportEvent.DOORBELL_PRESS.getEventId());
        doorbellService.onPress(deviceReportEventDO);
        deviceReportEventDO.getValue().setEvent(EReportEvent.DOORBELL_REMOVE.getEventId());
        doorbellService.onRemove(deviceReportEventDO);
    }

    @Test
    public void testOnPressOrRemove_hasLibrary() {
        doorbellService.setDevicePlatformEventPublisher(new DevicePlatformEventPublisher());
        // when(libraryService.getLibraryTrace(anyString())).thenReturn(new LibraryTraceDO());

        DeviceReportEventDO deviceReportEventDO = new DeviceReportEventDO();
        deviceReportEventDO.setSerialNumber("sn_01");
        deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue());

        when(redisService.hashGetString(anyString(), anyString())).thenReturn(null);
        deviceReportEventDO.getValue().setEvent(EReportEvent.DOORBELL_PRESS.getEventId());
        doorbellService.onPress(deviceReportEventDO);
        deviceReportEventDO.getValue().setEvent(EReportEvent.DOORBELL_REMOVE.getEventId());
        doorbellService.onRemove(deviceReportEventDO);

        when(redisService.hashGetString(anyString(), anyString())).thenReturn("[{\"event\":18},{\"event\":19}]");
        deviceReportEventDO.getValue().setEvent(EReportEvent.DOORBELL_PRESS.getEventId());
        doorbellService.onPress(deviceReportEventDO);
        deviceReportEventDO.getValue().setEvent(EReportEvent.DOORBELL_REMOVE.getEventId());
        doorbellService.onRemove(deviceReportEventDO);

        doorbellService.setDevicePlatformEventPublisher(null);
    }

}
