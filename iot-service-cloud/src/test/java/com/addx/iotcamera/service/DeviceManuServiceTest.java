package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.domain.DeviceManualDO;
import com.addx.iotcamera.dao.device.IDeviceManualDAO;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.model.DeviceModelZendeskService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.addx.iot.common.enums.ResultCollection.DEVICE_NO_EXIT;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceManuServiceTest {

    @InjectMocks
    private DeviceManualService deviceManualService;
    @Mock
    private IDeviceManualDAO deviceManualDAO;
    @Mock
    private FactoryDataQueryService factoryDataQueryService;
    @Mock
    private DeviceModelZendeskService deviceModelZendeskService;

    @Before
    public void before() {
        when(deviceModelZendeskService.queryDeviceModelZendesk(anyString())).thenReturn("anyString");
    }

    @Test
    public void test_getModelNoByUserSn1() {
        DeviceManualDO manu1 = new DeviceManualDO().setUserSn("sn1").setModelNo("modeNo1");
        when(deviceManualDAO.getModelNoByUserSn(manu1.getUserSn())).thenReturn(manu1);
        Result result1 = deviceManualService.getModelNoByUserSn(manu1.getUserSn(), null);
        Assert.assertEquals(Result.successFlag, result1.getResult());
    }

    @Test
    public void test_getModelNoByUserSn2() {
        DeviceManufactureTableDO manu2 = new DeviceManufactureTableDO();
        manu2.setUserSn("sn2");
        manu2.setModelNo("modeNo2");
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(manu2.getUserSn())).thenReturn(manu2);
        Result result2 = deviceManualService.getModelNoByUserSn(manu2.getUserSn(), null);
        Assert.assertEquals(Result.successFlag, result2.getResult());
    }

    @Test
    public void test_getModelNoByUserSn3() {
        String sn3 = "sn3";
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(sn3)).thenReturn(null);
        Result result3 = deviceManualService.getModelNoByUserSn(sn3, null);
        Assert.assertEquals((Integer) DEVICE_NO_EXIT.getCode(), result3.getResult());
    }

    @Test
    public void test_batchQueryDeviceManualDO() {
        when(deviceManualDAO.batchQueryDeviceManualDO(any(), any())).thenReturn(Arrays.asList(new DeviceManualDO()));
        List<DeviceManualDO> list = deviceManualService.batchQueryDeviceManualDO(0, 10);
        Assert.assertEquals(1, list.size());
    }

    @Test
    public void batchQueryDeviceManualDOByMaxId_ShouldReturnDeviceList() {
        // 准备测试数据
        Long maxId = 100L;
        Integer maxNum = 10;
        List<DeviceManualDO> expectedDevices = Arrays.asList(
            new DeviceManualDO().setId(99L).setSerialNumber("SN001").setModelNo("MODEL001"),
                new DeviceManualDO().setId(98L).setSerialNumber("SN002").setModelNo("MODEL002")
        );

        // 模拟DAO层的行为
        when(deviceManualDAO.batchQueryDeviceManualDOByMaxId(maxId, maxNum))
            .thenReturn(expectedDevices);

        // 执行测试
        List<DeviceManualDO> actualDevices = deviceManualService.batchQueryDeviceManualDOByMaxId(maxId, maxNum);

        // 验证结果
        assertTrue(actualDevices!=null);
        assertTrue(actualDevices.size() == 2);
        assertTrue(actualDevices.equals(expectedDevices));

        // 验证mock是否按预期被调用
        verify(deviceManualDAO).batchQueryDeviceManualDOByMaxId(maxId, maxNum);
    }

}
