package com.addx.iotcamera.service.proto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.PrintStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ProtoBuildHelper {

    @Data
    @Accessors(chain = true)
    public static class MsgDef {
        private final String name;
        private Map<String, FieldDef> key2FieldDef = new LinkedHashMap<>();
        private Map<String, MsgDef> msgType2Def = new LinkedHashMap<>();
    }

    //  optional int32 por = 1; // 2,
    @Data
    @Accessors(chain = true)
    public static class FieldDef {
        private String head;
        private String type;
        private String name;
        private int index;
        private String comment;
    }

//    public static Map<String, String> replacePathMap = new LinkedHashMap<>();
//
//    static {
//        replacePathMap.put("[\"status\",\"statusList\",\"[]\",\"name\"]", )
//    }

    private static boolean isNotVar(String key) {
        return key.matches("^[^a-zA-Z_][\\w]*");
//        return !key.matches("^[a-zA-Z_][\\w]*");
    }

    private static boolean isArray(String str) {
        return "[]".equals(str);
    }

    private static boolean isAllVisibleChars(String str) {
        if (str == null || str.isEmpty()) {
            return true;
        }
        for (char c : str.toCharArray()) {
//            if (Character.isISOControl(c) || Character.isWhitespace(c)) {
            if (Character.isISOControl(c)) {
                return false;
            }
        }
        return true;
    }

    public static void preHandlePath2Obj(Map<String, JSONObject> path2Obj) {
        {
            String key = "[\"queryRetainedMsgResponse\",\"data\",\"config\",\"value\",\"doorbellConfig\"]";
            if (path2Obj.containsKey(key)) {
                path2Obj.remove(key);
                String key2 = "[\"queryRetainedMsgResponse\",\"data\",\"config\",\"value\",\"doorbellConfig\",\"alarmWhenRemoveToggleOn\"]";
                JSONObject obj = new JSONObject().fluentPut("path", key2).fluentPut("num", 0).fluentPut("valueCounts", new JSONArray())
                        .fluentPut("typeClsNames", new JSONArray().fluentAdd("java.lang.Boolean"));
                path2Obj.put(key2, obj);
            }
        }
    }

    public static Map<String, MsgDef> build(JSONObject root) {
        List<JSONObject> pathObjs = root.getJSONArray("paths").toJavaList(JSONObject.class);
        Map<String, JSONObject> path2Obj = pathObjs.stream().collect(Collectors.toMap(it -> it.getString("path"), it -> it));
        preHandlePath2Obj(path2Obj);
        Map<String, MsgDef> msgName2MsgDef = new LinkedHashMap<>();
        for (JSONObject pathObj : path2Obj.values()) {
            String path = pathObj.getString("path");
            if (path.equals("[\"status\",\"statusList\",\"[]\",\"name\"]")) continue;
            if (path.equals("[\"status\",\"statusList\",\"[]\",\"value\"]")) continue;

            List<String> typeClsNames = pathObj.getJSONArray("typeClsNames").toJavaList(String.class);

            String[] paths;
            if (JSON.isValidArray(path)) {
                List<String> pathList = JSON.parseArray(path, String.class);
                pathList.add(0, "$");
                paths = pathList.toArray(new String[0]);
            } else {
                paths = path.split("\\.");
            }
            if (!Arrays.stream(paths).allMatch(ProtoBuildHelper::isAllVisibleChars)) {
                log.error("path中含有不可见字符! path={},paths={}", path, Arrays.toString(paths));
                continue;
            }
            String msgName = paths[1];
            List<JSONObject> valueCounts = pathObj.getJSONArray("valueCounts").toJavaList(JSONObject.class);

            MsgDef msgDef = msgName2MsgDef.computeIfAbsent(msgName, it -> new MsgDef(getTypeName(it)));
            MsgDef lastMsgDef = msgDef;
            for (int i = 2; i < paths.length; i++) {
                String key = paths[i];
                if (isArray(key) || isNotVar(key)) continue;
//                if (isArray(key)) continue;
                boolean isMap = i < paths.length - 1 && isNotVar(paths[i + 1]);
//                boolean isMap = false;x
                boolean isArray = i < paths.length - 1 && isArray(paths[i + 1]);
//                String head = isMap ? "" : (isArray ? "repeated" : "optional");
                String head = isArray ? "repeated" : "";
                boolean isMsg = i < paths.length - 1 && !(i == paths.length - 2 && (isArray || isMap));
                if (msgName.endsWith("Response") && key.equals("data")) {
                    isMsg = true;
                }

                // cmd:c6ced160d4244905bc4d157e4b9d5164@PQAgJsolHAq2bG2LedzWJ6
//                boolean isMapArr = i < paths.length - 2 && isNotVar(paths[i + 1]) && isArray(paths[i + 2]);
                if ("dns".equals(key)) {
                    String type = "PbDns";
                    if (!lastMsgDef.getKey2FieldDef().containsKey(key)) {
                        FieldDef fieldDef = new FieldDef().setHead("")
                                .setType(String.format("map<string, %s>", type))
                                .setName(key).setIndex(lastMsgDef.getKey2FieldDef().size() + 1)
                                .setComment(String.format("// path=%s , path[%d]=%s", path, i, key));
                        lastMsgDef.getKey2FieldDef().put(key, fieldDef);

                        lastMsgDef.getMsgType2Def().computeIfAbsent(type, k -> {
                            List<String> values = valueCounts.stream().sorted(Comparator.comparingInt(it -> -it.getInteger("count")))
                                    .limit(10).map(it -> it.getString("value")).collect(Collectors.toList());
                            FieldDef fieldDef2 = new FieldDef().setHead("repeated").setType("string").setName("ip").setIndex(1)
                                    .setComment(String.format("// path=%s , path[%d]=%s , values=%s", path, 1, key, JSONObject.toJSONString(values)));
                            MsgDef msgDef2 = new MsgDef(k);
                            msgDef2.getKey2FieldDef().put("ip", fieldDef2);
                            return msgDef2;
                        });
                    }
                    break;
                }
                if (isMsg) {
//                    String type = msgDef.getName() + getTypeName(key).substring(2);
                    String type = getTypeName(key);
                    long sameKeyNum = Arrays.stream(paths).limit(i).filter(it -> it.equals(key)).count();
                    if (sameKeyNum > 0) {
                        log.info("sameKeyNum lt 0: {},{},{},{}", path, i, key, sameKeyNum);
                        type = type + (sameKeyNum + 1);
                    }
                    if (!lastMsgDef.getKey2FieldDef().containsKey(key)) {
                        FieldDef fieldDef = new FieldDef().setHead(head)
                                .setType(String.format(isMap ? "map<string, %s>" : "%s", type))
                                .setName(key).setIndex(lastMsgDef.getKey2FieldDef().size() + 1)
                                .setComment(String.format("// path=%s , path[%d]=%s", path, i, key));
                        lastMsgDef.getKey2FieldDef().put(key, fieldDef);
                    }
                    lastMsgDef = lastMsgDef.getMsgType2Def().computeIfAbsent(type, MsgDef::new);
                } else {
                    if (!lastMsgDef.getKey2FieldDef().containsKey(key)) {
                        List<String> values = valueCounts.stream().sorted(Comparator.comparingInt(it -> -it.getInteger("count")))
                                .limit(10).map(it -> it.getString("value")).collect(Collectors.toList());

//                        String type = getFieldType(path, num, valueCounts);
                        String type = getProtoType(path, typeClsNames, valueCounts);
                        FieldDef fieldDef = new FieldDef().setHead(head)
                                .setType(String.format(isMap ? "map<string, %s>" : "%s", type))
                                .setName(key).setIndex(lastMsgDef.getKey2FieldDef().size() + 1)
                                .setComment(String.format("// path=%s , path[%d]=%s , types=%s , values=%s", path, i, key, JSON.toJSONString(typeClsNames), JSONObject.toJSONString(values)));
                        lastMsgDef.getKey2FieldDef().put(key, fieldDef);
                    }
                }
            }
        }
//        for (MsgDef msgDef : msgName2MsgDef.values()) {
//            handleSameStruct(msgDef);
//        }
        return msgName2MsgDef;
    }

    public static void handleSameStruct(MsgDef msgDef) {
        Map<String, String> replaceTypeMap = new LinkedHashMap<>();
        ArrayList<String> msgTypes = new ArrayList<>(msgDef.getMsgType2Def().keySet());
        for (int i = 0; i < msgTypes.size(); i++) {
            for (int j = i + 1; j < msgTypes.size(); j++) {
                MsgDef msgDef1 = msgDef.getMsgType2Def().get(msgTypes.get(i));
                MsgDef msgDef2 = msgDef.getMsgType2Def().get(msgTypes.get(j));
                if (!equals(msgDef1, msgDef2)) continue;
                log.info("equals msgDef1:{} , msgDef2:{}", msgDef2.getName(), msgDef2.getName());
                replaceTypeMap.put(msgTypes.get(j), msgTypes.get(i));
            }
        }
        if (replaceTypeMap.isEmpty()) return;
        log.info("replaceTypeMap:{} , {}", msgDef.getName(), JSON.toJSONString(replaceTypeMap, true));
        replaceType(replaceTypeMap, msgDef);
        msgDef.getMsgType2Def().values().forEach(it -> replaceType(replaceTypeMap, it));
        replaceTypeMap.keySet().forEach(it -> msgDef.getMsgType2Def().remove(it));

        msgDef.getMsgType2Def().values().forEach(ProtoBuildHelper::handleSameStruct);
    }

    public static void replaceType(Map<String, String> replaceTypeMap, MsgDef msgDef) {
        for (FieldDef fieldDef : msgDef.getKey2FieldDef().values()) {
            String newType = replaceTypeMap.get(fieldDef.getType());
            if (newType != null) {
                fieldDef.setType(newType);
            }
        }
    }

    public static boolean equals(MsgDef msgDef1, MsgDef msgDef2) {
        if (msgDef1 == msgDef2) return true;
        Set<String> keys = new LinkedHashSet<>();
        keys.addAll(msgDef1.getKey2FieldDef().keySet());
        keys.addAll(msgDef1.getKey2FieldDef().keySet());
        for (String key : keys) {
            FieldDef fieldDef1 = msgDef1.getKey2FieldDef().get(key);
            FieldDef fieldDef2 = msgDef2.getKey2FieldDef().get(key);
            if (fieldDef1 == null && fieldDef2 == null) continue;
            if (fieldDef1 == null || fieldDef2 == null) return false;
            if (fieldDef1.getType().equals(fieldDef2.getType()) && fieldDef1.getHead().equals(fieldDef2.getHead())) {
                continue;
            }
            if (!equals(msgDef1.getMsgType2Def().get(fieldDef1.getType()), msgDef2.getMsgType2Def().get(fieldDef2.getType()))) {
                return false;
            }
        }
        return true;
    }

    public static String getTypeName(String msgName) {
        return "Pb" + msgName.substring(0, 1).toUpperCase() + msgName.substring(1);
    }

    public static String getResponseTypeName(String msgName) {
        return "Pb" + msgName.substring(0, 1).toUpperCase() + msgName.substring(1) + "Response";
    }

    private static String getFieldType(String path, List<JSONObject> valueCounts) {
        List<String> values = valueCounts.stream().map(it -> it.getString("value"))
                .filter(StringUtils::isNotBlank)
                .filter(it -> !"null".equals(it))
                .collect(Collectors.toList());
        if (!values.isEmpty()) {
            if (values.stream().allMatch(ProtoBuildHelper::isBool)) {
                return "bool";
            }
            if (values.stream().allMatch(ProtoBuildHelper::isDouble)) {
                return "double";
            }
            if (values.stream().allMatch(ProtoBuildHelper::isInt32)) {
                return "int32";
            }
            if (values.stream().allMatch(ProtoBuildHelper::isInt64)) {
                return "int64";
            }
        }
//        if (num >= 1000 && values.size() < 20) {
//            log.info("getFieldType prop is enum! path={},num={},values={}", path, num, values);
//        }
        if (path.startsWith("[\"queryRetainedMsgResponse\",\"data\"") && path.contains("\"binaryData\"")) {
            return "string";
        }
        if (path.startsWith("[\"queryRetainedMsgResponse\",\"data\",\"config\",\"value\"")) {
            if (path.contains("\"endpoint\"")) return "string";
            if (path.contains("\"headSlicePeriod\"")) return "int32";
            if (path.contains("\"tailSlicePeriod\"")) return "int32";
            if (path.contains("\"maxCount\"")) return "int32";
            if (path.contains("\"intervalInMs\"")) return "int32";
            if (path.contains("\"reportComplete\"")) return "bool";
        }
        return "any";
    }

    private static boolean isBool(String str) {
        return str.matches("^true|false$");
    }

    private static boolean isDouble(String str) {
        return NumberUtils.isNumber(str) && str.contains(".");
    }

    private static boolean isInt32(String str) {
        // max_int=2,147,483,647
        return NumberUtils.isNumber(str) && Long.parseLong(str) < Integer.MAX_VALUE;
    }

    private static boolean isInt64(String str) {
        return NumberUtils.isNumber(str);
    }

    public static void printTotal(PrintStream out, String msgName, MsgDef msgInfo) {
        out.print("syntax = \"proto3\";\n");
        out.println();
        out.print("package tutorial;\n");
        out.println();
        out.print("option java_multiple_files = true;\n");
        out.print("option java_package = \"org.addx.iot.common.proto.deviceMsg\";\n");
        out.printf("option java_outer_classname = \"%sProto\";\n", msgInfo.getName());
        out.println();
        out.printf("// h2接口：/deviceMsg/%s%n", msgName);
        print(out, msgInfo, "");
    }

    public static void print(PrintStream out, MsgDef msgInfo, String prefix) {
        out.printf("%smessage %s {\n\n", prefix, msgInfo.getName());
        for (FieldDef field : msgInfo.getKey2FieldDef().values()) {
            //  optional int32 por = 1; // 2,
            out.printf("%s  %s\n", prefix, field.getComment());
            String head = StringUtils.isNotBlank(field.getHead()) ? (field.getHead() + " ") : "";
            out.printf("%s  %s%s %s = %s;\n", prefix, head, field.getType(), field.getName(), field.getIndex());
        }
        for (MsgDef innerMsgInfo : msgInfo.getMsgType2Def().values()) {
            out.println();
            print(out, innerMsgInfo, prefix + "  ");
        }
        out.println();
        out.printf("%s}%n", prefix);
    }

    public static String getProtoType(String path, List<String> clsNames, List<JSONObject> valueCounts) {
        if (clsNames == null || clsNames.isEmpty()) {
            return getFieldType(path, valueCounts);
        }
        if (clsNames.containsAll(Arrays.asList("java.lang.Integer", "java.lang.Long")) && clsNames.size() == 2) {
            return "int64";
        } else if (clsNames.contains("java.lang.Integer") && clsNames.size() == 1) {
            return "int32";
        } else if (clsNames.contains("java.lang.Long") && clsNames.size() == 1) {
            return "int64";
        } else if (clsNames.containsAll(Arrays.asList("java.lang.Float", "java.lang.Double")) && clsNames.size() == 2) {
            return "double";
        } else if (clsNames.contains("java.lang.Float") && clsNames.size() == 1) {
            return "double";
        } else if (clsNames.contains("java.lang.Double") && clsNames.size() == 1) {
            return "double";
        } else if (clsNames.contains("java.math.BigDecimal") && clsNames.size() == 1) {
            return "double";
        } else if (clsNames.contains("java.lang.Boolean") && clsNames.size() == 1) {
            return "bool";
        } else if (clsNames.contains("java.lang.String") && clsNames.size() == 1) {
            return "string";
        } else {
//            throw new RuntimeException("getProtoType error! path=" + path + "," + JSON.toJSONString(clsNames));
            return getFieldType(path, valueCounts);
        }
    }

}
