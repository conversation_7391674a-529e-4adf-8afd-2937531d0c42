package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.param.LinodePar;
import com.addx.iotcamera.bean.param.LinodeUrlParts;
import com.addx.iotcamera.bean.video.StoreBucket;
import com.addx.iotcamera.config.LinodeConfig;
import com.addx.iotcamera.enums.LinodeCredentials;
import com.addx.iotcamera.service.video.StorageAllocateService;
import com.addx.iotcamera.util.BlowfishUtil;
import com.addx.iotcamera.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.addx.iot.common.enums.PirServiceName;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

import java.net.URL;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;


@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LinodeServiceTest {

    @Mock
    private RedisService redisService;

    @InjectMocks
    private LinodeService linodeService;
    @InjectMocks
    private LinodeConfig linodeConfig1;

    @Mock
    private LinodeService linodeService1;

    @Mock
    private LinodeConfig linodeConfig;
    @Mock
    private OkHttpClient okHttpClient;
    @InjectMocks
    private StorageAllocateService storageAllocateService;
    @Mock
    private S3Client s3Client;


    @Before
    public void init() {
        when(linodeConfig.getToken()).thenReturn("asdfasdf");
        when(linodeConfig.getEnableCreateCredential()).thenReturn(true);
        when(linodeConfig.getEnable()).thenReturn(true);
        when(linodeConfig.getSecretKey()).thenReturn("sadf");
        when(linodeConfig.getAccessKey()).thenReturn("dasf");
        when(linodeConfig.getDefaultRegion()).thenReturn("dasf");
    }


    @Test
    public void refreshParNotReturnParFromMaster() {
        when(redisService.obtain(anyString())).thenReturn(new ReentrantLock());

        linodeService.refreshPar(true);

        LinodePar par = new LinodePar();
        par.setCacheInvalidTime(System.currentTimeMillis());
        String jsonString = JSONObject.toJSONString(par);
        String jsonList = JSONObject.toJSONString(Arrays.asList(par));

        when(redisService.get("LINODE_PAR_REDIS_KEY_FOR_FIRST")).thenReturn(jsonString);
        when(redisService.get("LINODE_PAR_REDIS_KEY_FOR_REMOVE_LIST")).thenReturn(jsonList);


        linodeService.refreshPar(true);
        linodeService.deleteAccessKeyByLabel("a");
        linodeService.deleteCore("a", "aasdf");
        Assert.assertNotNull(par);

    }

    @Test
    public void refreshSetParToRedis()  {
        LinodePar par = linodeService.setParToRedis(new JSONObject(), "asdf");
        Assert.assertNull(par);

        JSONObject parMaster = new JSONObject();
        parMaster.put("access_key", "123");
        parMaster.put("secret_key", "123");
        par = linodeService.setParToRedis(parMaster, "asdf");
        Assert.assertNotNull(par);

        StoreBucket bucket = new StoreBucket();
        bucket.setBucket("test-bucket");
        String rootUrl = linodeService.getRootUrl(bucket);
        Assert.assertEquals("https://test-bucket.dasf.linodeobjects.com/", rootUrl);
    }

    @Test
    public void getCreateTempCredential() {
        LinodePar par = new LinodePar();
        par.setExpiredTime(System.currentTimeMillis() + 10000);
        par.setAccessKey("123");
        par.setSecurityKey("123");
        String jsonString = JSONObject.toJSONString(par);
        when(redisService.get(anyString())).thenReturn(jsonString);
        LinodeCredentials credential = linodeService.createTempCredential("adsf", 123);
        Assert.assertNotNull(credential);
        par.setExpiredTime(1);
        when(redisService.get(anyString())).thenReturn(JSONObject.toJSONString(par));
        when(linodeConfig.getEnableCreateCredential()).thenReturn(false);
        credential = linodeService.createTempCredential("adsf", 123);
        Assert.assertNull(credential);
        when(linodeConfig.getEnableCreateCredential()).thenReturn(true);
        par.setExpiredTime(Integer.MAX_VALUE);
        when(redisService.get("LINODE_PAR_REDIS_KEY_FOR_REMOVE_LIST")).thenReturn(null);

        when(redisService.obtain(anyString())).thenReturn(new ReentrantLock());

        credential = linodeService.createTempCredential("adsf", 123);
        Assert.assertNull(credential);
    }

    @Test
    public void testPreSignUrl() {
        linodeService.preSignUrl("https://a.yx-test-nov-22-01.us-sea-9.linodeobjects.com/yx_dir/asdfasdfasf/", 123L);
        linodeService.preSignUrl("https://a.yx-test-nov-22-01.us-sea-9.linodfeobjects.com/yx_dir/asdfasdfasf/", 123L);
        URL url = linodeService.preSignUrl("https://yx-test-nov-22-01.us-sea-9.linodeobjects.com/yx_dir/asdfasdfasf/", 123L);
        Assert.assertNull(url);
    }


    @Test
    public void testLinodeConfig() {
        linodeConfig1.init();
        linodeConfig1.getBucketByLookBackDays(1, 1);
        linodeConfig1.getBucketByAdminId(null, 1);
        List<StoreBucket> list = new ArrayList<>();
        StoreBucket storeBucket = new StoreBucket();
        linodeConfig1.getBucketByAdminId(list, 1);
        list.add(storeBucket);
        linodeConfig1.getBucketByAdminId(list, 1);
        linodeConfig1.getBucketByAdminId(list, 0);
        storeBucket.setMaxRatio(100);
        storeBucket.setMinRatio(100);
        StoreBucket bucketByAdminId = linodeConfig1.getBucketByAdminId(list, 1);
        Assert.assertNotNull(bucketByAdminId);

        LinodePar par = new LinodePar();
        par.setCacheInvalidTime(System.currentTimeMillis());
        String jsonString = JSONObject.toJSONString(par);
        when(redisService.get(anyString())).thenReturn(jsonString);
        try {
            linodeService.init();
            when(linodeConfig.getEnable()).thenReturn(false);
            linodeService.init();
        } catch (Exception e) {

        }



    }

    @Test
    public void testBlowfishUtil() {
        String en = BlowfishUtil.en("aaa", "111");
        String decrypt = BlowfishUtil.decrypt(en, "111");
        Assert.assertEquals("aaa", decrypt);
        NumberUtil.parseIntExceptionWith0("1");
        NumberUtil.parseIntExceptionWith0("sadf");
        String en1 = BlowfishUtil.en(null, "111");
        Assert.assertEquals("", en1);

        en = BlowfishUtil.en("", null);
        Assert.assertEquals("", en);

        decrypt = BlowfishUtil.decrypt("", null);
        Assert.assertEquals("", decrypt);

    }

    @Test
    public void testStorageAllocateService(){
        String objectKeyFromUrl = storageAllocateService.getObjectKeyFromUrl("");
        Assert.assertNull(objectKeyFromUrl);

        objectKeyFromUrl = storageAllocateService.getObjectKeyFromUrl("https://da.sf.linodeobjects.com/");
        Assert.assertNotNull(objectKeyFromUrl);


        objectKeyFromUrl = storageAllocateService.getObjectKeyFromUrl("https://addx-firmware-us.s3.amazonaws.com/CG721_sys_img/staging/2021-03-01/12-24-23/upgrade.fw");
        Assert.assertNotNull(objectKeyFromUrl);

        storageAllocateService.init();
        boolean enableServiceName = storageAllocateService.isEnableServiceName(PirServiceName.LINODE);
        Assert.assertTrue(enableServiceName);

        StoreBucket bucketByLookBackDays = storageAllocateService.getBucketByLookBackDays(PirServiceName.LINODE, 1, 1);
        Assert.assertNull(bucketByLookBackDays);
    }

    @Test
    public void testParseLinodeUrl() {
        LinodeUrlParts linodeUrlParts = LinodeService.parseLinodeUrl("");
        Assert.assertNull(linodeUrlParts);
        linodeUrlParts = LinodeService.parseLinodeUrl("https://dasf.2.2.2..linodeobjects.com/test-bucket");
        Assert.assertNull(linodeUrlParts);
        linodeUrlParts = LinodeService.parseLinodeUrl("https://da");
        Assert.assertNull(linodeUrlParts);

        linodeUrlParts = LinodeService.parseLinodeUrl("https://da.sf.linodeobjects.com");
        Assert.assertNull(linodeUrlParts);
        linodeUrlParts = LinodeService.parseLinodeUrl("asdfasdfasfasdfsadfasdfodeobjects.com/");
        Assert.assertNull(linodeUrlParts);

        linodeUrlParts = LinodeService.parseLinodeUrl("https://dasf.a.linodeobjects.com/test-bucket");
        Assert.assertNotNull(linodeUrlParts);
        LinodeService.getBaseUrl("a");
        Assert.assertNotNull(linodeUrlParts);
        LinodeService.getBaseUrl(null);
        Assert.assertNotNull(linodeUrlParts);

        LinodeService.getRegionUrl(null);
        StoreBucket storeBucket = new StoreBucket();
        storeBucket.setServiceName(PirServiceName.cos);
        LinodeService.getRegionUrl(storeBucket);
        storeBucket.setServiceName(PirServiceName.LINODE);
        storeBucket.setRegion(null);
        LinodeService.getRegionUrl(storeBucket);
        storeBucket.setRegion("sdf");
        LinodeService.getRegionUrl(storeBucket);


    }

    @Test
    public void testQps() {
        StoreBucket storeBucket = new StoreBucket();
        storeBucket.setRegion("213");
        storeBucket.setServiceName(PirServiceName.LINODE);
        storeBucket.setBucket("asdf");
        Map<Integer, List<StoreBucket>> map = new HashMap<>();
        map.put(1, Arrays.asList(storeBucket));
        when(linodeConfig.getLookBackDays2Buckets()).thenReturn(map);
        when(linodeService.uploadObject(anyString(), anyString())).thenReturn(PutObjectResponse.builder().build());

        linodeService.monitorQPSAndSize();
        LinodeUrlParts linodeUrlParts = LinodeService.parseLinodeUrl("");
        Assert.assertNull(linodeUrlParts);
        linodeService.addToNeedRemoveList(null);

        when(redisService.obtain(anyString())).thenReturn(new ReentrantLock());
        LinodePar par = new LinodePar();
        par.setLinodeParName("2");
        par.setCacheInvalidTime(System.currentTimeMillis());
        String jsonList = JSONObject.toJSONString(Arrays.asList(par));

        LinodePar par1 = new LinodePar();
        when(redisService.get("LINODE_PAR_REDIS_KEY_FOR_REMOVE_LIST")).thenReturn(null);
        linodeService.addToNeedRemoveList(par1);
        par1.setLinodeParName("2");
        when(redisService.get("LINODE_PAR_REDIS_KEY_FOR_REMOVE_LIST")).thenReturn(jsonList);
        linodeService.addToNeedRemoveList(par1);



    }
}