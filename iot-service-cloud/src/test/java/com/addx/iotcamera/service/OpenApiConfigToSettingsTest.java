package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.device.CoolDownDO;
import com.addx.iotcamera.bean.device.DeviceModelSettingDO;
import com.addx.iotcamera.bean.device.model.DeviceModelVolumeDO;
import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.init.AmazonInit;
import com.addx.iotcamera.dao.device.IDeviceSettingDAO;
import com.addx.iotcamera.dynamo.dao.DeviceConfigDAO;
import com.addx.iotcamera.service.openapi.DeviceSettingInitDataService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.DeviceCodecUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.addx.iotcamera.constants.DeviceModelSettingConstants.*;

/**
 * config字段迁移到settings
 */
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class OpenApiConfigToSettingsTest {

    private TestHelper testHelper;

    private IDeviceSettingDAO settingDAO;
    private DeviceConfigDAO deviceConfigDAO;

    @Before
    public void before() {
        TestHelper.HelperConfig helperConfig = TestHelper.HelperConfig.builder().autoCommit(true).build();
        testHelper = TestHelper.getInstanceByEnv("test", helperConfig);

        settingDAO = testHelper.getMapper(IDeviceSettingDAO.class);

        AmazonInit amazonInit = testHelper.getAmazonInit();
        deviceConfigDAO = new DeviceConfigDAO();
        deviceConfigDAO.setDeviceConfigMapper(amazonInit.deviceConfigMapper());
        deviceConfigDAO.setDeviceConfigMapperConfig(amazonInit.deviceConfigMapperConfig());
        deviceConfigDAO.setDynamoDB(amazonInit.dynamoDB());
    }

    @After
    public void After() {
        testHelper.commitAndClose();
    }

    private static DeviceModelSettingDO buildModelSetting() {
        DeviceModelVolumeDO modelVolumeDO = new DeviceModelVolumeDO();
        modelVolumeDO.setModelNo("test_zyj_model");
        modelVolumeDO.setVoiceVolume(12);
        modelVolumeDO.setAlarmVolume(34);
        DeviceModelSettingDO modelSetting = DeviceModelSettingDO.builder()
                .mirrorFlip(1).motionSensitivity(1).deviceModelVoiceDO(modelVolumeDO)
                .chargeAutoPowerOnSwitch(0)
                .build();
        return modelSetting;
    }

    private static DeviceAppSettingsDO defaultAppSettings = DeviceAppSettingsDO.defaultSettings(buildModelSetting());

    @Test
    public void test_IDeviceSettingDAO_crud() {
        int snPostfix = 0;
        List<DeviceAppSettingsDO> appSettingsList = Arrays.asList(
                new DeviceAppSettingsDO()
                        .setLiveAudioToggleOn(DEFAULT_VALUE_LIVE_AUDIO_TOGGLE_ON)
                        .setRecordingAudioToggleOn(DEFAULT_VALUE_RECORDING_AUDIO_TOGGLE_ON)
                        .setLiveSpeakerVolume(DEFAULT_VALUE_LIVE_SPEAKER_VOLUME)
                        .setAlarmWhenRemoveToggleOn(DEFAULT_VALUE_ALARM_WHEN_REMOVE_TOGGLE_ON)
                        .setDeviceCallToggleOn(DEFAULT_VALUE_DEVICE_CALL_TOGGLE_ON),
                defaultAppSettings
        );
        for (DeviceAppSettingsDO appSettings : appSettingsList) {
            for (int i = 0; i < 2; i++) {
                appSettings.setSerialNumber("test_zyj_" + OpenApiUtil.shortUUID() + "_" + (snPostfix++));
                DeviceSettingsDO settings = DeviceSettingsDO.ParseFrom(appSettings, null);
                Integer insertNum = settingDAO.initDeviceSettings(settings);
                Assert.assertEquals((Integer) 1, insertNum);
                DeviceSettingsDO settingsDO = settingDAO.getDeviceSettingsBySerialNumber(appSettings.getSerialNumber());
                assertSettingDO("sn=" + appSettings.getSerialNumber(), appSettings, settingsDO);
            }
            DeviceSettingsDO settingsModify = new DeviceSettingsDO().setSerialNumber(appSettings.getSerialNumber())
                    .setCooldownUserEnable(true).setCooldownInS(30).setDefaultCodec(DeviceCodecUtil.H264)
                    .setLiveAudioToggleOn(false).setRecordingAudioToggleOn(false).setLiveSpeakerVolume(50)
                    .setAlarmWhenRemoveToggleOn(true);
            Integer updateNum = settingDAO.updateDeviceSettings(settingsModify);
            Assert.assertEquals((Integer) 1, updateNum);

            DeviceSettingsDO settingsDO2 = settingDAO.getDeviceSettingsBySerialNumber(appSettings.getSerialNumber());
            assertSettingDO("sn=" + appSettings.getSerialNumber(), settingsModify, settingsDO2);
        }
    }

    private static void assertSettingDO(String msg, DeviceAppSettingsDO appSettings, DeviceSettingsDO settingsDO) {
        Assert.assertNotNull(appSettings);
        Optional<CoolDownDO> opt = Optional.ofNullable(appSettings.getCooldown());
        Assert.assertEquals(msg, opt.map(it -> it.getValue()).orElse(null), settingsDO.getCooldownInS());
        Assert.assertEquals(msg, opt.map(it -> it.getUserEnable()).orElse(null), settingsDO.getCooldownUserEnable());
        Assert.assertEquals(msg, appSettings.getLiveAudioToggleOn(), settingsDO.getLiveAudioToggleOn());
        Assert.assertEquals(msg, appSettings.getRecordingAudioToggleOn(), settingsDO.getRecordingAudioToggleOn());
        Assert.assertEquals(msg, appSettings.getLiveSpeakerVolume(), settingsDO.getLiveSpeakerVolume());
        Assert.assertEquals(msg, appSettings.getAlarmWhenRemoveToggleOn(), settingsDO.getAlarmWhenRemoveToggleOn());
    }

    private static void assertSettingDO(String msg, DeviceSettingsDO settingsDO, DeviceSettingsDO realSettingsDO) {
        Assert.assertNotNull(settingsDO);
        Assert.assertEquals(msg, settingsDO.getCooldownInS(), realSettingsDO.getCooldownInS());
        Assert.assertEquals(msg, settingsDO.getCooldownUserEnable(), realSettingsDO.getCooldownUserEnable());
        Assert.assertEquals(msg, settingsDO.getLiveAudioToggleOn(), realSettingsDO.getLiveAudioToggleOn());
        Assert.assertEquals(msg, settingsDO.getRecordingAudioToggleOn(), realSettingsDO.getRecordingAudioToggleOn());
        Assert.assertEquals(msg, settingsDO.getLiveSpeakerVolume(), realSettingsDO.getLiveSpeakerVolume());
        Assert.assertEquals(msg, settingsDO.getAlarmWhenRemoveToggleOn(), realSettingsDO.getAlarmWhenRemoveToggleOn());
    }

//    @Test
    public void test_DeviceSettingInitDataService(){
        DeviceSettingInitDataService initDataService = new DeviceSettingInitDataService()
                .setSettingDAO(settingDAO).setDeviceConfigDAO(deviceConfigDAO);
        initDataService.handleNeedInitData();
    }

}
