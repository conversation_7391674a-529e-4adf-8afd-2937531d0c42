package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.HttpTokenDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class TokenServiceTest {

    @InjectMocks
    TokenService tokenService;

    @Spy
    JwtHelper jwtHelper = new JwtHelper();

    @Mock
    HttpServletRequest httpRequest;
    @Mock
    RedisService redisService;
    @Mock
    RedisTemplate redisTemplate;

    @Test
    public void test_generateMsgToken() {
        //生成随机种子
        String seed = PhosUtils.getUUID();
        User user = new User();
        user.setId(123);
        HttpTokenDO httpTokenDO = tokenService.generateMsgToken(seed, user);
        log.info(JSON.toJSONString(httpTokenDO, true));
        Integer userId = jwtHelper.getUserId(httpTokenDO.getToken());
        log.info("userId=" + userId);
    }

    @Test
    public void test_jwtHelper_getUserId() {
//        String token = "BearereyJhbGciOiJIUzUxMiJ9.eyJzZWVkIjoiNTQyMDA4YWM5NGMwNGY5NjkxMzhjNzE5YzY2MWRiYWYiLCJleHAiOjI2MTQ5MTY0NTAsInVzZXJJZCI6NzE5fQ.Bpul-q6ng9iFiuViVIXYQnLrboA8j74rr-PcZHTWgzEK2GBAZOj_rsScQERxKqUNjdice9yoFqnhONH9rYdjiQ";
//        String token = "BearereyJhbGciOiJIUzUxMiJ9.eyJzZWVkIjoiYzdhYmFlZDBiODViNGZjZDgxYzQyY2JmMjE0ZTc2ZDUiLCJleHAiOjI2MTUxODYzMjUsInVzZXJJZCI6NjY0fQ.w9mtUHwIlD1cNl5jJ37DDwn9Vb-7oH6hA0urI5q9k1WaPQGg0A4E8mcOKgZ-Jc96og7eVDdPY4kWMfyNDBJbyQ";
//        String token = "Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************.Ryq04cqj-q0V2vJoQoUpwIaftj1xIJv-V3-hqIy7gISFzp_HFwwkx6AsGf38xb4hvpat5Snk4v-vrYxtzcwepg";
//        String token = "Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************.abRRzu1SBeXnoU5JaZJbdbNz0fpiVr41hmIlvW48QaxS71fkEl5OQtd-NJdK6H_XLb7l4ddnf6dMLHXtxGGvkw";
//        String token = "Bearer eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************************************************.-AFySoY8-mG33VjdA14ltit7fsqn11OlOso7irY1Ts0Pl9ni-SkGLiIWLBmBbPHO60hJJem5_7krgWeccE3nBQ";
//        String token = "BearereyJhbGciOiJIUzUxMiJ9.**************************************************************************************************.0lEiXmDReJ439nQ7ekYr2tLQdc8dHmc8swJhLfKsIpzg_KiBQGtGGhEva5gFPzSYQrEqlU9ka0fu309LEQrVCQ";
//        String token = "Bearer eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************************************************************************.c3oqGbWlKnE5JvkQL92lnoeVKqgbEGyDo0ugrCG15W8n9-W-HJCVLZhKutuSCCir4oRvN1h8wFNA7lmyyH-qVA";
//        String token = "Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************.50C8rbobEnMPMYoxrUF-i_vX4LpF3CUA-jJaksd5yQe0m_R2eKG9kYYgfHNcs8ejZLU_dPZ6gpNP1m_IlhrobA";
//        String token = "Bearer eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************.fIvvBLgNDil3CLJ2nIHw_OIBbO-NboSK2oknMu0yM79tJo--amQ2pjHnWi4PXMsFyy5tXVVp0k-R-rnSUyZ-zQ";
//        String token = "Bearer eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************.FFrmx6Ml1Pq9ONVehPUOqg1Bo8s_GGcfskjmzzEmv1FDcHL1MEMRK3iTodFUeSnn4YeHQTo0I3Bs6CXosu5hDQ";
//        String token = "Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************.VZoBlos9SLwDeUbguG3kNXsiXVwwdad_dQeNBUVBGn5etjpD7DiV-Rzyl4vEd-SQEWpN8EEMV58cDIpSza2-QQ";
        String token = "Bearer eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************.MPXLtuRPwQF3Z0Offz9lja42mD3I1s0K7BmwecFwCqV-4ZKCNWjAZ9MENeiNlINl4L1yi_-N3LvvWAPBNdXyMw";
        when(httpRequest.getHeader("Authorization")).thenReturn(token);

        Map<String, Object> map = jwtHelper.validateTokenAndGetClaims(httpRequest);
        log.info("map=" + map);
    }

//    @Test
    public void mockLogin() {
        TestHelper testHelper = TestHelper.getInstanceByLocal();
        RedisTemplate redisTemplate2 = testHelper.getRedisTemplate();
        doAnswer(AdditionalAnswers.delegatesTo(redisTemplate2)).when(redisTemplate).opsForValue();

        //生成随机种子
        String seed = PhosUtils.getUUID();
        User user = new User();
        user.setId(105);
        HttpTokenDO httpTokenDO = tokenService.generateMsgToken(seed, user);
        log.info("httpTokenDO:   {}", JSON.toJSONString(httpTokenDO, true));
        user.setThirdUserId(OpenApiUtil.shortUUID());
        user.setAccountId(OpenApiUtil.shortUUID());
        HttpTokenDO thirdUserToken = tokenService.generateThirdUserToken(seed, user);
        log.info("thirdUserToken:{}", JSON.toJSONString(thirdUserToken, true));
        Integer userId = jwtHelper.getUserId(httpTokenDO.getToken());
        log.info("userId=" + userId);
        //生成新UserToken
        tokenService.setUserSeed(String.valueOf(user.getId()), seed);
        // 解析token
        log.info("user: {}", JSON.toJSONString(user, true));
        Map<String, Object> userTokenMap = jwtHelper.getUserToken(httpTokenDO.getToken());
        log.info("userTokenMap:   {}", JSON.toJSONString(userTokenMap, true));
        Map<String, Object> thirdTokenMap = jwtHelper.getUserToken(thirdUserToken.getToken());
        log.info("thirdTokenMap:  {}", JSON.toJSONString(thirdTokenMap, true));
        Assert.assertEquals(user.getId(), userTokenMap.get(TokenService.TOKEN_FIELD_USER_ID));
        Assert.assertEquals(seed, userTokenMap.get(TokenService.TOKEN_FIELD_SEED));
        Assert.assertEquals(user.getId(), thirdTokenMap.get(TokenService.TOKEN_FIELD_USER_ID));
        Assert.assertEquals(seed, thirdTokenMap.get(TokenService.TOKEN_FIELD_SEED));
        Assert.assertEquals(user.getAccountId(), thirdTokenMap.get(TokenService.TOKEN_FIELD_ACCOUNT_ID));
        Assert.assertEquals(user.getThirdUserId(), thirdTokenMap.get(TokenService.TOKEN_FIELD_THIRD_USER_ID));
    }

    @Test
    public void test_generateThirdUserToken() {
        final User user = new User(); // 50979,jiangshuai,b51349e6049844febc343992df978f73
        user.setId(50979);
        user.setThirdUserId("b51349e6049844febc343992df978f73");
        user.setAccountId("jiangshuai");
        String seed = "6a2563c624c149529d0a5c3cde1fdf30";
        HttpTokenDO thirdUserToken = tokenService.generateThirdUserToken(seed, user);
        log.info("thirdUserToken:{}", JSON.toJSONString(thirdUserToken));
    }

    @Test
    public void test_encodePwd() {
        String encodePwd = PhosUtils.CalcSalt("123456", "");
        log.info("encodePwd:{}", encodePwd);
    }
}
