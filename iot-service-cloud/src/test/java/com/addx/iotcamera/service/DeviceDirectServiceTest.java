package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.device.http.DeviceRequestBase;
import com.addx.iotcamera.bean.domain.device.http.DeviceUploadLogRequest;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceDirectServiceTest {

    @InjectMocks
    private DeviceDirectService deviceDirectService;
    @Mock
    private S3Service s3Service;
    @Mock
    private MultipartFile multipartFile;

    @Test
    public void test_LogType() {
        for (DeviceRequestBase.LogType logType : DeviceRequestBase.LogType.values()) {
            Assert.assertEquals(logType, DeviceRequestBase.LogType.findByValue(logType.getValue()));
        }
        Assert.assertEquals(null, DeviceRequestBase.LogType.findByValue(null));
        Assert.assertEquals(null, DeviceRequestBase.LogType.findByValue(OpenApiUtil.shortUUID()));
    }

    @Test
    public void test_uploadDeviceLogToS3() throws IOException {
        DeviceUploadLogRequest request = new DeviceUploadLogRequest();
        request.setSerialNumber(OpenApiUtil.shortUUID());
        request.setLogEndTime(System.currentTimeMillis());
        request.setLogStartTime(request.getLogEndTime() - 1000);
        request.setLog(multipartFile);
        request.setLogType(DeviceRequestBase.LogType.DEVICE_IP_LOG.getValue());
        {
            String expectResult = OpenApiUtil.shortUUID();
            when(s3Service.uploadObject(any(), anyString(), any())).thenReturn(expectResult);
            String result = deviceDirectService.uploadDeviceLogToS3(request);
            Assert.assertEquals(expectResult, result);
        }
        {
            when(s3Service.uploadObject(any(), anyString(), any())).thenThrow(new IOException(""));
            String result = deviceDirectService.uploadDeviceLogToS3(request);
            Assert.assertEquals(null, result);
        }

    }
}
