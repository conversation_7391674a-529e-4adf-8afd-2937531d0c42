package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.CreativeRequest;
import com.addx.iotcamera.bean.db.creative.CreativeDO;
import com.addx.iotcamera.bean.db.creative.SlotDO;
import com.addx.iotcamera.bean.db.creative.SolutionDO;
import com.addx.iotcamera.bean.domain.CreativeInfo;
import com.addx.iotcamera.dao.creative.ICreativeDAO;
import com.addx.iotcamera.dao.creative.ISlotDAO;
import com.addx.iotcamera.dao.creative.ISolutionDAO;
import com.addx.iotcamera.service.creative.CreativeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * description:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2024/4/23 14:03
 */
@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class CreativeServiceTest {

    @Mock
    ICreativeDAO iCreativeDAO;
    @InjectMocks
    CreativeService creativeService;

    @Mock
    ISlotDAO iSlotDAO ;

    @Mock
    ISolutionDAO iSolutionDAO ;


    @Test
    public void testGetCreative() {
        // Prepare test data
        List<CreativeRequest.SolutionInfo> config = new ArrayList<>();
        CreativeRequest.SolutionInfo info = new CreativeRequest.SolutionInfo();
        info.setSlotName("slot1");
        info.setSolutionId("1");
        config.add(info);
        String language = "en";

        SolutionDO solutionDO = new SolutionDO();
        solutionDO.setSlotName("slot1");
        solutionDO.setId(1L);
        solutionDO.setLayout("layout1");
        solutionDO.setImages("3,4");
        solutionDO.setVideo("2");
        solutionDO.setSingleImage("1");

        CreativeDO creativeDO1 = new CreativeDO();
        creativeDO1.setId(1L);
        creativeDO1.setCtype(1);
        creativeDO1.setLinks("{\"en\":\"link1\",\"fr\":\"lien1\"}");

        CreativeDO creativeDO2 = new CreativeDO();
        creativeDO2.setId(2L);
        creativeDO2.setCtype(2);
        creativeDO2.setLinks("{\"en\":\"link2\",\"fr\":\"lien2\"}");

        CreativeDO creativeDO3 = new CreativeDO();
        creativeDO3.setId(3L);
        creativeDO3.setCtype(1);
        creativeDO3.setLinks("{\"en\":\"link3\",\"fr\":\"lien1\"}");

        CreativeDO creativeDO4 = new CreativeDO();
        creativeDO4.setId(4L);
        creativeDO4.setCtype(1);
        creativeDO4.setLinks("{\"en\":\"link4\",\"fr\":\"lien1\"}");

        when(iSolutionDAO.getSolutionsByIds(anyList())).thenReturn(Collections.singletonList(solutionDO));
        when(iCreativeDAO.getAllCreatives(anySet())).thenReturn(Arrays.asList(creativeDO1, creativeDO2, creativeDO3, creativeDO4));

        // Call the method under test
        List<CreativeInfo> result = creativeService.getCreative(config, language);

        // Assertions
        assertEquals(1, result.size());
        CreativeInfo creativeInfo = result.get(0);
        assertEquals("slot1", creativeInfo.getSlotName());
        assertEquals(3, creativeInfo.getSolution().getConfig().size());
        assertEquals("1", creativeInfo.getSolution().getSolutionId());
        assertEquals("layout1", creativeInfo.getSolution().getLayout());
        assertEquals("images", creativeInfo.getSolution().getConfig().get(0).getField());
        assertEquals(Arrays.asList("link3","link4"), creativeInfo.getSolution().getConfig().get(0).getValues());


        config.clear();
        result = creativeService.getCreative(config, language);
        assertEquals(0, result.size());

        info.setSlotName("slot1");
        info.setSolutionId("a");
        config.add(info);
        result = creativeService.getCreative(config, language);
        assertEquals(0, result.size());


        config.clear();
        info.setSolutionId(null);
        config.add(info);
        result = creativeService.getCreative(config, language);
        assertEquals(0, result.size());

        config.clear();
        info.setSolutionId("1");
        config.add(info);
        SolutionDO solutionDO1 = new SolutionDO();
        solutionDO1.setSlotName("slot1");
        solutionDO1.setId(1L);
        solutionDO1.setLayout("layout1");
        solutionDO1.setImages("3,4");
        solutionDO1.setVideo("2");
        solutionDO1.setSingleImage("1");
        solutionDO1.setThemeColor("red");
        solutionDO1.setComponentColorType("solid");
        solutionDO1.setComponentColorStart("x");
        solutionDO1.setComponentColorEnd("y");
        solutionDO1.setLabelColorType("gradient");
        solutionDO1.setLabelColorStart("x");
        solutionDO1.setLabelColorEnd("y");
        when(iSolutionDAO.getSolutionsByIds(anyList())).thenReturn(Collections.singletonList(solutionDO1));
        result = creativeService.getCreative(config, language);
        assertEquals(1, result.size());
    }

    @Test
    public void testAddCreative() {
        CreativeDO creativeDO = new CreativeDO();
        when(iCreativeDAO.insert(creativeDO)).thenReturn(1L);

        Long result = creativeService.addCreative(creativeDO);

        assertEquals(1L, result.longValue());
    }

    @Test
    public void testGetCreatives() {
        when(iCreativeDAO.getCreativesCount()).thenReturn(10L);
        List<CreativeDO> creatives = Arrays.asList(new CreativeDO(), new CreativeDO());
        when(iCreativeDAO.getCreatives(0, 5)).thenReturn(creatives);

        Map<String, Object> result = creativeService.getCreatives(1, 5);

        assertEquals(10L, result.get("total"));
        assertEquals(creatives, result.get("list"));
    }

    @Test
    public void testAddSlot() {
        SlotDO slotDO = new SlotDO();
        when(iSlotDAO.insert(slotDO)).thenReturn(1);

        int result = creativeService.addSlot(slotDO);

        assertEquals(1L, result);
    }

    @Test
    public void testGetSlot() {
        when(iSlotDAO.getSlotsCount()).thenReturn(10L);
        List<SlotDO> slots = Arrays.asList(new SlotDO(), new SlotDO());
        when(iSlotDAO.getSlots(0, 5)).thenReturn(slots);

        Map<String, Object> result = creativeService.getSlots(1, 5);

        assertEquals(10L, result.get("total"));
        assertEquals(slots, result.get("list"));
    }

    @Test
    public void testAddSolution() {
        SolutionDO solutionDO = new SolutionDO();
        when(iSolutionDAO.insert(solutionDO)).thenReturn(1L);

        Long result = creativeService.addSolution(solutionDO);

        assertEquals(1L, result.longValue());
    }

    @Test
    public void testGetSolutions() {
        List<SolutionDO> solutions = Arrays.asList(new SolutionDO(), new SolutionDO());
        when(iSolutionDAO.getSolutions(0,2,"slot")).thenReturn(solutions);

        List<SolutionDO> result = creativeService.getSolutions(1,2,"slot");

        assertEquals(solutions, result);
    }
}
