package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.device.DeviceWhiteListDO;
import com.addx.iotcamera.dao.device.DeviceWhiteListDAO;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.PirServiceName;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceWhiteListServiceTest {

    @InjectMocks
    private DeviceWhiteListService deviceWhiteListService;
    @Mock
    private DeviceWhiteListDAO deviceWhiteListDAO;

    private TestHelper testHelper = TestHelper.getInstanceByEnv("test");

    @Before
    public void init() {
        final DeviceWhiteListDAO dao = testHelper.getMapper(DeviceWhiteListDAO.class);
        when(deviceWhiteListDAO.queryBySn(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(dao));
        when(deviceWhiteListDAO.insert(any())).thenAnswer(AdditionalAnswers.delegatesTo(dao));
        when(deviceWhiteListDAO.update(any())).thenAnswer(AdditionalAnswers.delegatesTo(dao));
        when(deviceWhiteListDAO.queryByCondition(any(), any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(dao));
        when(deviceWhiteListDAO.countByCondition(any())).thenAnswer(AdditionalAnswers.delegatesTo(dao));
        when(deviceWhiteListDAO.queryBySns(any())).thenAnswer(AdditionalAnswers.delegatesTo(dao));
    }

    @After
    public void after() {
        testHelper.commitAndClose();
    }

    @Test
    public void test() {
        {
            Assert.assertEquals(0, deviceWhiteListService.insert(null));
            Assert.assertEquals(0, deviceWhiteListService.insert(new DeviceWhiteListDO()));
            Assert.assertEquals(0, deviceWhiteListService.update(null));
            Assert.assertEquals(0, deviceWhiteListService.update(new DeviceWhiteListDO()));
            Assert.assertEquals(Collections.emptyList(), deviceWhiteListService.queryBySns(null));
            Assert.assertEquals(Collections.emptyList(), deviceWhiteListService.queryBySns(Collections.emptyList()));
        }
        {
            final String sn = OpenApiUtil.shortUUID();
            final DeviceWhiteListDO item = new DeviceWhiteListDO().setSerialNumber(sn).setPirVideoStorageService("oci");
            Assert.assertEquals(1, deviceWhiteListService.insert(item));
            Assert.assertEquals(PirServiceName.oci, deviceWhiteListService.getPirVideoStorageServiceBySn(sn));
//
//            Assert.assertEquals(1, deviceWhiteListService.save(item.setPirVideoStorageService("oci")));
//            Assert.assertEquals(PirServiceName.oci, deviceWhiteListService.getPirVideoStorageServiceBySn(sn));
//
//            Assert.assertEquals(2, deviceWhiteListService.save(item.setPirVideoStorageService("s3")));
//            Assert.assertEquals(PirServiceName.s3, deviceWhiteListService.getPirVideoStorageServiceBySn(sn));
        }
        {
            final String sn = OpenApiUtil.shortUUID();
            final DeviceWhiteListDO item = new DeviceWhiteListDO().setSerialNumber(sn).setPirVideoStorageService("s3");
            Assert.assertEquals(1, deviceWhiteListService.insert(item));
            Assert.assertEquals(PirServiceName.s3, deviceWhiteListService.getPirVideoStorageServiceBySn(sn));

            Assert.assertEquals(1, deviceWhiteListService.update(new DeviceWhiteListDO().setSerialNumber(sn).setPirVideoStorageService("oci")));
            Assert.assertEquals(PirServiceName.oci, deviceWhiteListService.getPirVideoStorageServiceBySn(sn));
        }

    }

    @Test
    public void test_queryByCondition() {
        final int num = PirServiceName.values().length;
        List<String> sns = new LinkedList<>();
        for (int i = 0; i < num; i++) {
            final PirServiceName serviceName = PirServiceName.values()[i];
            final String sn = OpenApiUtil.shortUUID();
            sns.add(sn);
            final DeviceWhiteListDO item = new DeviceWhiteListDO().setSerialNumber(sn).setPirVideoStorageService(serviceName.name());
            Assert.assertEquals(1, deviceWhiteListService.insert(item));
            Assert.assertEquals(serviceName, deviceWhiteListService.getPirVideoStorageServiceBySn(sn));

            final DeviceWhiteListDO condition = new DeviceWhiteListDO().setSerialNumber(sn);
            final List<DeviceWhiteListDO> list = deviceWhiteListService.queryByCondition(condition, null, null);
            Assert.assertEquals(1, list.size());
            Assert.assertEquals(sn, list.get(0).getSerialNumber());
            Assert.assertEquals(serviceName.name(), list.get(0).getPirVideoStorageService());
            Assert.assertEquals(1, deviceWhiteListService.countByCondition(condition));
        }
        final List<DeviceWhiteListDO> list = deviceWhiteListService.queryBySns(sns);
        Assert.assertEquals(new HashSet(sns), list.stream().map(it -> it.getSerialNumber()).collect(Collectors.toSet()));
    }

}
