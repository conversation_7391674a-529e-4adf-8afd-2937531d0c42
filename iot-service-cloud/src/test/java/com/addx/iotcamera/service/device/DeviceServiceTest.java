package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.bean.domain.device.SdCard;
import com.addx.iotcamera.util.Assert.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;


@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceServiceTest {
    @InjectMocks
    private DeviceService deviceService;

    @Test
    public void clearDeviceCache_test() {
        AssertUtil.assertNotException(() -> deviceService.clearDeviceCache("serialNumber"));
    }

    // 不要再用构造函数了 ！！！
    public static DeviceDO createDeviceDO(DeviceModel deviceModel) {
//        return new DeviceDO("serialNumber", "mcuNumber", 0, "deviceName", 0, "modelNo", 0, "macAddress", 0, 0, 0, "locationName", 0, 0, 0, 0, 0, 0, "networkName", 0, "adminName", "adminEmail", "adminPhone", 0, "roleName", "firmwareId", 0, "newestFirmwareId", "userSn", 0, "timeZone", 0, false, deviceModel, "ip", 0, "recResolution", false, new SdCard("serialNumber", 0, 0, 0, 0), 0, "displayModelNo", 0, false, 0, "icon", "smallIcon", null, false, "displayGitSha", 0, 0, "deviceDormancyMessage", 0L, "codec", "defaultCodec", false, "linkedPlatforms", false, false, 0, 0, false, 0, false);
        return DeviceDO.builder()
                .serialNumber("serialNumber")
                .mcuNumber("mcuNumber")
                .locationId(0)
                .deviceName("deviceName")
                .activatedTime(0)
                .modelNo("modelNo")
                .modelCategory(0)
                .macAddress("macAddress")
                .activated(0)
                .userId(0)
                .adminId(0)
                .locationName("locationName")
                .online(0)
                .offlineTime(0)
                .awake(0)
                .firmwareStatus(0)
                .batteryLevel(0)
                .signalStrength(0)
                .wifiPowerLevel(3)
                .networkName("networkName")
                .isCharging(0)
                .adminName("adminName")
                .adminEmail("adminEmail")
                .adminPhone("adminPhone")
                .role(0)
                .roleName("roleName")
                .firmwareId("firmwareId")
                .manufacturerId(0)
                .newestFirmwareId("newestFirmwareId")
                .userSn("userSn")
                .personDetect(0)
                .timeZone("timeZone")
                .otaIgnored(0)
                .pushIgnored(false)
                .deviceModel(deviceModel)
                .ip("ip")
                .wifiChannel(0)
                .recResolution("recResolution")
                .otaOnAwake(false)
                .sdCard(new SdCard("serialNumber", 0, 0, 0, 0))
                .whiteLight(0)
                .displayModelNo("displayModelNo")
                .packagePush(0)
                .antiflickerSupport(false)
                .antiflicker(0)
                .icon("icon")
                .smallIcon("smallIcon")
                .deviceSupport(null)
                .quantityCharge(false)
                .displayGitSha("displayGitSha")
                .deviceStatus(0)
                .dormancyPlanSwitch(0)
                .deviceDormancyMessage("deviceDormancyMessage")
                .deviceDormancyWakeTime(0L)
                .codec("codec")
                .defaultCodec("defaultCodec")
                .showCodecChange(false)
                .linkedPlatforms("linkedPlatforms")
                .liveAudioToggleOn(false)
                .recordingAudioToggleOn(false)
                .liveSpeakerVolume(0)
                .chargingMode(0)
                .alarmWhenRemoveToggleOn(false)
                .deviceVipLevel(0)
                .deviceInVip(false)
                .build();
    }

    // 不要再用构造函数了 ！！！
    public static DeviceStatusDO createDeviceStatusDO() {
//        return new DeviceStatusDO("serialNumber", 0, 0, 0, "networkName", 0, 0, "ip", 0, 0, false, 0, "codec", "linkedPlatforms", false, false, 0, false, 0);
        return DeviceStatusDO.builder()
                .serialNumber("serialNumber")
                .batteryLevel(0)
                .isCharging(0)
                .signalStrength(0)
                .wifiPowerLevel(3)
                .networkName("networkName")
                .lastAct(0)
                .lastAwake(0)
                .ip("ip")
                .wifiChannel(0)
                .antiflicker(0)
                .quantityCharge(false)
                .whiteLight(0)
                .codec("codec")
                .linkedPlatforms("linkedPlatforms")
                .liveAudioToggleOn(false)
                .recordingAudioToggleOn(false)
                .liveSpeakerVolume(0)
                .alarmWhenRemoveToggleOn(false)
                .chargingMode(0)
                .build();
    }

//    @Test
    public void test() {
        {
            List<Object> args = Arrays.asList("serialNumber", "mcuNumber", 0, "deviceName", 0, "modelNo", 0, "macAddress", 0, 0, 0, "locationName", 0, 0, 0, 0, 0, 0, "networkName", 0, "adminName", "adminEmail", "adminPhone", 0, "roleName", "firmwareId", 0, "newestFirmwareId", "userSn", 0, "timeZone", 0, false, null, "ip", 0, "recResolution", false, new SdCard("serialNumber", 0, 0, 0, 0), 0, "displayModelNo", 0, false, 0, "icon", "smallIcon", null, false, "displayGitSha", 0, 0, "deviceDormancyMessage", 0L, "codec", "defaultCodec", false, "linkedPlatforms", false, false, 0, 0, false, 0, false);
            contructorToBuilder(DeviceDO.class, args);
        }
        {
            List<Object> args = Arrays.asList("serialNumber", 0, 0, 0, "networkName", 0, 0, "ip", 0, 0, false, 0, "codec", "linkedPlatforms", false, false, 0, false, 0);
            contructorToBuilder(DeviceStatusDO.class, args);
        }

    }

    // 构造函数转构造器
    public static void contructorToBuilder(Class cls, List<Object> args) {
        Field[] fields = cls.getDeclaredFields();
        log.info("fields:{},args:{}", fields.length, args.size());
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            if ("wifiPowerLevel".equals(fieldName)) continue;
            Object arg = args.get(i);
            String argStr;
            if (arg == null) {
                argStr = "null";
            } else if (arg instanceof String) {
                argStr = "\"" + arg + "\"";
            } else {
                argStr = arg + "";
            }
            i++;
            System.out.printf(".%s(%s)\n", fieldName, argStr);
        }
        System.out.println();
    }
}
