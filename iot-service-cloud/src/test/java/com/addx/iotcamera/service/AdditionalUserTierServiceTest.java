package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.additional_tier.AdditionalUserTierInfo;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.TierActiveConditionDO;
import com.addx.iotcamera.bean.db.additional_tier.AdditionalTierDO;
import com.addx.iotcamera.bean.db.additional_tier.AdditionalUserTierDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.PushInfo;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.dao.additional_tier.AdditionalTierDao;
import com.addx.iotcamera.dao.additional_tier.AdditionalUserTierDao;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.pay.IOrderProductDAO;
import com.addx.iotcamera.enums.ProductTypeEnums;
import com.addx.iotcamera.enums.PushTypeEnums;
import com.addx.iotcamera.service.vip.TierActiveConditionService;
import com.addx.iotcamera.service.vip.TierGroupService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.service.xinge.PushXingeService;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;

import java.lang.reflect.Field;
import java.util.*;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AdditionalUserTierServiceTest {

    @InjectMocks
    private AdditionalUserTierService additionalUserTierService;

    @Mock
    private PushXingeService pushXingeService;

    @Mock
    private PushInfoService pushInfoService;

    @Mock
    private PushService pushService;

    @Mock
    private AdditionalUserTierDao additionalUserTierDao;

    @Mock
    private AdditionalTierDao additionalTierDao;

    @Mock
    private IUserVipDAO iUserVipDAO;

    @Mock
    private CenterNotifyConfig centerNotifyConfig;

    @Mock
    private ProductService productService;

    @Mock
    private PaymentService paymentService;

    @Mock
    private IOrderDAO iOrderDAO;

    @Mock
    private IOrderProductDAO iOrderProductDAO;

    @Mock
    private CopyWrite copyWrite;

    @Mock
    private TierActiveConditionService tierActiveConditionService;

    @Mock
    private TierService tierService;

    @Mock
    private TierGroupService tierGroupService;

    @Mock
    private VipService vipService;

    @Mock
    UserTierDeviceService userTierDeviceService;

    @Before
    public void init() {
        when(centerNotifyConfig.getMessage()).thenReturn(Collections.singletonMap("vicoo", Collections.singletonMap("en", Collections.singletonMap("expirationReminderPopupMessage", "<h5><font color=\"\"#2F3742\"\">您的{tierName}将于</font><font color=\"\"{color}\"\">{day}天</font><font color=\"\"#2F3742\"\">后到期</font></h5>\n" +
                "<font color=\"\"#666666\"\">到期后您将：</font><br/>\n" +
                "失去所有AI功能（提醒区域、AI分析、智能推送）<br/>云存储空间降低为{expireDay}天（总容量不大于{expireSize}GB）<br/><br/>\n" +
                "<font color=\"\"#666666\"\">如有需要请续订智能服务或进入相册下载视频</font>"))));

        when(copyWrite.getConfig()).thenReturn(new HashMap<String, Map<String, String>>() {{
            put("expirationBirdFansReminderPopupMessageToday", Collections.singletonMap("en", "aaaaa"));
            put("expirationBirdFansReminderPopupMessage", Collections.singletonMap("en", "bbbbbb"));
            put("awareness_bird_fans", Collections.singletonMap("en", "ccccc"));
            put("expirationReminderTitle", Collections.singletonMap("en", "dddddd"));
        }});

    }

    @Test
    public void test_getActiveAdditionalUserTierInfo() throws NoSuchFieldException, IllegalAccessException {
        when(additionalUserTierDao.getByUserId(anyInt())).thenReturn(null);

        List<AdditionalUserTierInfo> additionalUserTierInfoList = additionalUserTierService.getActiveAdditionalUserTierInfo(1, "vicoo", "en");
        Assert.assertTrue(CollectionUtils.isEmpty(additionalUserTierInfoList));

        when(additionalUserTierDao.getByUserId(anyInt())).thenReturn(Arrays.asList(new AdditionalUserTierDO() {{
            setTierUid("a");
            setUserId(1);
            setTenantId("vicoo");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000));
            setMonth(1);
            setDay(28);
        }}, new AdditionalUserTierDO() {{
            setTierUid("b");
            setUserId(1);
            setTenantId("vicoo");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 30 * 24 * 3600));
            setMonth(1);
            setDay(25);
        }}, new AdditionalUserTierDO() {{
            setTierUid("c");
            setUserId(1);
            setTenantId("vicoo");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 26 * 24 * 3600));
            setMonth(1);
            setDay(28);
        }}, new AdditionalUserTierDO() {{
            setTierUid("b");
            setUserId(1);
            setTenantId("vicoo");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 100 * 24 * 3600));
            setMonth(1);
            setDay(28);
        }}));
        when(additionalTierDao.getByTierUid(any(), eq("a"))).thenReturn(new AdditionalTierDO() {{
            setTierUid("a");
            setType(1);
            setStatus(0);
            setNeedVipTier(false);
            setTenantId("vicoo");
        }});
        when(additionalTierDao.getByTierUid(any(), eq("b"))).thenReturn(new AdditionalTierDO() {{
            setTierUid("b");
            setType(2);
            setStatus(0);
            setNeedVipTier(false);
            setTenantId("vicoo");
        }});
        when(additionalTierDao.getByTierUid(any(), eq("c"))).thenReturn(new AdditionalTierDO() {{
            setTierUid("c");
            setType(3);
            setStatus(0);
            setNeedVipTier(false);
            setTenantId("vicoo");
        }});
        additionalUserTierInfoList = additionalUserTierService.getActiveAdditionalUserTierInfo(1, "vicoo", "en");
        Assert.assertTrue(CollectionUtils.isNotEmpty(additionalUserTierInfoList) && "a".equals(additionalUserTierInfoList.get(0).getTierUid()));

        Field additionalTierDOMapField = AdditionalUserTierService.class.getDeclaredField("additionalTierDOMap");
        additionalTierDOMapField.setAccessible(true);
        additionalTierDOMapField.set(additionalUserTierService, new HashMap<>());

        when(additionalTierDao.getByTierUid(any(), eq("a"))).thenReturn(new AdditionalTierDO() {{
            setTierUid("a");
            setType(1);
            setStatus(0);
            setNeedVipTier(true);
            setTenantId("vicoo");
        }});
        when(additionalTierDao.getByTierUid(any(), eq("b"))).thenReturn(new AdditionalTierDO() {{
            setTierUid("b");
            setType(1);
            setStatus(0);
            setNeedVipTier(true);
            setTenantId("vicoo");
        }});
        when(additionalTierDao.getByTierUid(any(), eq("c"))).thenReturn(new AdditionalTierDO() {{
            setTierUid("c");
            setType(1);
            setStatus(0);
            setNeedVipTier(false);
            setTenantId("vicoo");
        }});

        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt(),any())).thenReturn(Arrays.asList(
                new UserVipDO() {{
                    setTierId(1);
                    setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 30 * 24 * 3600));
                    setEndTime((int) (System.currentTimeMillis() / 1000 - 15 * 24 * 3600));
                }},
                new UserVipDO() {{
                    setTierId(1);
                    setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 14 * 24 * 3600));
                    setEndTime((int) (System.currentTimeMillis() / 1000 - 5 * 24 * 3600));
                }},
                new UserVipDO() {{
                    setTierId(1);
                    setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 5 * 24 * 3600));
                    setEndTime((int) (System.currentTimeMillis() / 1000 + 5 * 24 * 3600));
                }},
                new UserVipDO() {{
                    setTierId(0);
                    setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 5 * 24 * 3600));
                    setEndTime((int) (System.currentTimeMillis() / 1000 + 5 * 24 * 3600));
                }}
        ));

        when(tierActiveConditionService.getById(any(), any(), any())).thenReturn(new TierActiveConditionDO() {{
            setAdditionalTierActiveSeparately(true);
        }});

        when(tierService.queryTierById(any())).thenReturn(new Tier());

        additionalUserTierInfoList = additionalUserTierService.getActiveAdditionalUserTierInfo(1, "vicoo", "en");
        Assert.assertTrue(CollectionUtils.isNotEmpty(additionalUserTierInfoList) && "a".equals(additionalUserTierInfoList.get(0).getTierUid()));
    }

    @Test
    public void testAddFreeAdditionalTier() throws Exception {
        // Setup
        // Configure ProductService.queryProductById(...).
        final ProductDO productDO = new ProductDO();
        productDO.setId(0);
        productDO.setType(0);
        productDO.setKeyId(0);
        productDO.setBody("body");
        productDO.setPrice(0);
        productDO.setCurrency(0);
        productDO.setSubject("subject");
        productDO.setStatus(0);
        productDO.setCdate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        productDO.setMdate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        productDO.setAdditionalTierUid("null");
        when(productService.queryProductById(anyInt())).thenReturn(productDO);

        when(vipService.isVipUser(any())).thenReturn(true);

        // Run the test
        Result result = additionalUserTierService.addFreeAdditionalTier(0, "additionalTierUid", 0);
        Assert.assertTrue(!Objects.equals(result.getResult(), 0));

        // Configure PaymentService.initOrder(...).
        final OrderDO orderDO = new OrderDO();
        when(paymentService.initOrder(any(), any(), any(), anyBoolean())).thenReturn(orderDO);

        when(iOrderDAO.updateOrderStatus(any())).thenReturn(1);

        // Run the test
        productDO.setAdditionalTierUid("additionalTierUid");
        result = additionalUserTierService.addFreeAdditionalTier(0, "additionalTierUid", 0);
        Assert.assertTrue(Objects.equals(result.getResult(), 0));

        // Verify the results
        verify(iOrderDAO).updateOrderStatus(orderDO);
    }

    @Test
    public void testIsReceivedFreeAdditionalTier() {
        // Setup
        // Configure ProductService.queryProductById(...).
        final ProductDO productDO = new ProductDO();
        productDO.setId(0);
        productDO.setType(0);
        productDO.setKeyId(0);
        productDO.setBody("body");
        productDO.setPrice(0);
        productDO.setCurrency(0);
        productDO.setSubject("subject");
        productDO.setStatus(0);
        productDO.setCdate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        productDO.setMdate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        productDO.setAdditionalTierUid("nulll");
        when(productService.queryProductById(anyInt())).thenReturn(productDO);

        Boolean isReceived = additionalUserTierService.isReceivedFreeAdditionalTier(0, "additionalTierUid", 0);
        Assert.assertTrue(!isReceived);

        // Configure AdditionalUserTierDao.getByUserId(...).
        final List<AdditionalUserTierDO> additionalUserTierDOList = Arrays.asList(new AdditionalUserTierDO(0, 0, "tierUid", 0, 0, 1, 1, 0L, "tradeNo", false, "tenantId"));
        when(additionalUserTierDao.getByUserId(anyInt())).thenReturn(additionalUserTierDOList);

        // Configure IOrderProductDAO.selectOrderProductByOrderId(...).
        final OrderProductDo productDo = OrderProductDo.builder().build();
        when(iOrderProductDAO.selectOrderProductByOrderId(anyLong())).thenReturn(productDo);

        // Run the test
        productDO.setAdditionalTierUid("additionalTierUid");
        isReceived = additionalUserTierService.isReceivedFreeAdditionalTier(0, "additionalTierUid", 0);
        assertFalse(isReceived);
    }

    @Test
    public void testIsReceivedFreeAdditionalTier_AdditionalUserTierDaoReturnsNoItems() {
        // Setup
        // Configure ProductService.queryProductById(...).
        final ProductDO productDO = new ProductDO();
        productDO.setId(0);
        productDO.setType(0);
        productDO.setKeyId(0);
        productDO.setBody("body");
        productDO.setPrice(0);
        productDO.setCurrency(0);
        productDO.setSubject("subject");
        productDO.setStatus(0);
        productDO.setCdate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        productDO.setMdate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(productService.queryProductById(0)).thenReturn(productDO);

        when(additionalUserTierDao.getByUserId(0)).thenReturn(Collections.emptyList());

        // Configure IOrderProductDAO.selectOrderProductByOrderId(...).
        final OrderProductDo productDo = OrderProductDo.builder().build();
        when(iOrderProductDAO.selectOrderProductByOrderId(0L)).thenReturn(productDo);

        // Run the test
        final Boolean isReceived = additionalUserTierService.isReceivedFreeAdditionalTier(0, "additionalTierUid", 0);

        // Verify the results
        assertTrue(!isReceived);
    }

    @Test
    public void test_mergeByTypeAndEffectiveTime() throws Exception {
        List<AdditionalUserTierDO> resultList = additionalUserTierService.mergeByTypeAndEffectiveTime(null);
        Assert.assertTrue(CollectionUtils.isEmpty(resultList));

        Field additionalTierDOMapField = AdditionalUserTierService.class.getDeclaredField("additionalTierDOMap");
        additionalTierDOMapField.setAccessible(true);

        additionalTierDOMapField.set(additionalUserTierService, new HashMap<String, AdditionalTierDO>() {{
            put("a", new AdditionalTierDO() {{
                setType(1);
            }});
            put("b", new AdditionalTierDO() {{
                setType(2);
            }});
        }});

        resultList = additionalUserTierService.mergeByTypeAndEffectiveTime(Arrays.asList(new AdditionalUserTierDO() {{
            setTierUid("a");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 10 * 24 * 60 * 60));
            setMonth(1);
            setDay(5);
        }}, new AdditionalUserTierDO() {{
            setTierUid("a");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 6 * 24 * 60 * 60));
            setMonth(1);
            setDay(5);
        }}, new AdditionalUserTierDO() {{
            setTierUid("b");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 8 * 24 * 60 * 60));
            setMonth(1);
            setDay(7);
        }}, new AdditionalUserTierDO() {{
            setTierUid("b");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000));
            setMonth(1);
            setDay(10);
        }}));
        Assert.assertTrue(resultList.size() == 3 &&
                "a".equals(resultList.get(0).getTierUid()) &&
                "b".equals(resultList.get(1).getTierUid()) &&
                "b".equals(resultList.get(2).getTierUid()));
    }

    @Test
    public void test_getExpirationMessageOptional() {
        Optional<String> expirationMessageOptional = additionalUserTierService.getExpirationMessageOptional(new AdditionalUserTierDO() {{
            setEndTime((int) (System.currentTimeMillis() / 1000) + 24 * 60 * 60);
        }}, "vicoo", "cn");
        Assert.assertTrue(!expirationMessageOptional.isPresent());

        expirationMessageOptional = additionalUserTierService.getExpirationMessageOptional(new AdditionalUserTierDO() {{
            setEndTime((int) (System.currentTimeMillis() / 1000));
        }}, "vicoo", "en");
        Assert.assertTrue(expirationMessageOptional.isPresent());

        expirationMessageOptional = additionalUserTierService.getExpirationMessageOptional(new AdditionalUserTierDO() {{
            setEndTime((int) (System.currentTimeMillis() / 1000) + 24 * 60 * 60);
        }}, "vicoo", "en");
        Assert.assertTrue(expirationMessageOptional.isPresent());

    }

    @Test
    public void test_tryPushAdditionalTierExpireMessage() {
        additionalUserTierService.tryPushAdditionalTierExpireMessage(new User() {{
            setId(1);
            setLanguage("en");
        }});
        when(additionalUserTierDao.getByUserId(anyInt())).thenReturn(Collections.emptyList());
        additionalUserTierService.tryPushAdditionalTierExpireMessage(new User() {{
            setId(1);
            setLanguage("en");
        }});

        when(additionalUserTierDao.getByUserId(anyInt())).thenReturn(Arrays.asList(new AdditionalUserTierDO() {{
            setTierUid("a");
            setUserId(1);
            setTenantId("vicoo");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000));
            setMonth(1);
            setDay(28);
        }}, new AdditionalUserTierDO() {{
            setTierUid("b");
            setUserId(1);
            setTenantId("vicoo");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 30 * 24 * 3600));
            setMonth(1);
            setDay(25);
        }}, new AdditionalUserTierDO() {{
            setTierUid("c");
            setUserId(1);
            setTenantId("vicoo");
            setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 26 * 24 * 3600));
            setMonth(1);
            setDay(28);
        }}));
        when(additionalTierDao.getByTierUid(any(), eq("a"))).thenReturn(new AdditionalTierDO() {{
            setTierUid("a");
            setType(1);
            setStatus(0);
            setNeedVipTier(false);
            setTenantId("vicoo");
        }});
        when(additionalTierDao.getByTierUid(any(), eq("b"))).thenReturn(new AdditionalTierDO() {{
            setTierUid("b");
            setType(1);
            setStatus(0);
            setNeedVipTier(false);
            setTenantId("vicoo");
        }});
        when(additionalTierDao.getByTierUid(any(), eq("c"))).thenReturn(new AdditionalTierDO() {{
            setTierUid("c");
            setType(2);
            setStatus(0);
            setNeedVipTier(false);
            setTenantId("vicoo");
        }});

        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt(),any())).thenReturn(Arrays.asList(
                new UserVipDO() {{
                    setTierId(1);
                    setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 30 * 24 * 3600));
                    setEndTime((int) (System.currentTimeMillis() / 1000 - 15 * 24 * 3600));
                }},
                new UserVipDO() {{
                    setTierId(1);
                    setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 14 * 24 * 3600));
                    setEndTime((int) (System.currentTimeMillis() / 1000 - 5 * 24 * 3600));
                }},
                new UserVipDO() {{
                    setTierId(1);
                    setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 5 * 24 * 3600));
                    setEndTime((int) (System.currentTimeMillis() / 1000 + 5 * 24 * 3600));
                }},
                new UserVipDO() {{
                    setTierId(0);
                    setEffectiveTime((int) (System.currentTimeMillis() / 1000 - 5 * 24 * 3600));
                    setEndTime((int) (System.currentTimeMillis() / 1000 + 5 * 24 * 3600));
                }}
        ));

        when(pushInfoService.getPushInfo(anyInt())).thenReturn(PushInfo.builder().msgType(PushTypeEnums.PUSH_XINGE.getCode()).build());

        when(tierActiveConditionService.getById(any(), any(), any())).thenReturn(new TierActiveConditionDO() {{
            setAdditionalTierActiveSeparately(true);
        }});

        when(tierService.queryTierById(any())).thenReturn(new Tier());

        when(tierGroupService.sortUserVipDO(any())).then(new Answer<List<UserVipDO>>() {
            @Override
            public List<UserVipDO> answer(InvocationOnMock invocation) throws Throwable {
                return invocation.getArgument(0);
            }
        });

        additionalUserTierService.tryPushAdditionalTierExpireMessage(new User() {{
            setId(1);
            setLanguage("en");
        }});
        verify(pushXingeService).pushMessage(any(), any());

        when(pushInfoService.getPushInfo(anyInt())).thenReturn(PushInfo.builder().msgType(PushTypeEnums.PUSH_IOS_DEBUG.getCode()).build());

        additionalUserTierService.tryPushAdditionalTierExpireMessage(new User() {{
            setId(1);
            setLanguage("en");
        }});
        verify(pushService).pushMessage(anyInt(), any());

        when(pushInfoService.getPushInfo(anyInt())).thenReturn(null);
        additionalUserTierService.tryPushAdditionalTierExpireMessage(new User() {{
            setId(1);
            setLanguage("en");
        }});
    }



    @Test
    @DisplayName("商品类型不符合条件")
    public void testVerifyAdditionalTierFree_WithNonDeviceProduct() throws Exception {
        OrderDO orderDO = new OrderDO();
        OrderProductDo orderProductDo = new OrderProductDo();

        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PURCHASE.getCode());
        PowerMockito.when(productService.queryProductById(any())).thenReturn(productDO);

        additionalUserTierService.verifyAdditionalTierFree(orderDO, orderProductDo, Lists.newArrayList());

        // 验证相关的方法是否被正确调用
        //verify(additionalUserTierService, never()).addFreeAdditionalProduct(any(),any());
    }

    @Test
    @DisplayName("没有配置赠送的鸟类套餐")
    public void testVerifyAdditionalTierFree_WithDeviceProduct_NoAdditionalProductConfigured() throws Exception {
        OrderDO orderDO = new OrderDO();
        OrderProductDo orderProductDo = new OrderProductDo();

        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PRODUCT_DEVICE_NUM.getCode());
        when(productService.queryProductById(any())).thenReturn(productDO);
        when(productService.queryAdditionalProductFree(any())).thenReturn(null);

        additionalUserTierService.verifyAdditionalTierFree(orderDO, orderProductDo, Lists.newArrayList());

        // 验证相关的方法是否被正确调用
        //verify(additionalUserTierService, never()).addFreeAdditionalProduct(any(),any());
    }

    @Test
    public void testVerifyAdditionalTierFree_WithDeviceProduct_AdditionalProductConfigured_UserHasAlreadyReceived() throws Exception {
        OrderDO orderDO = new OrderDO();
        OrderProductDo orderProductDo = new OrderProductDo();
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PRODUCT_DEVICE_NUM.getCode());
        when(productService.queryProductById(any())).thenReturn(productDO);
        when(productService.queryAdditionalProductFree(any())).thenReturn(new ProductDO());
        when(additionalUserTierService.getAllTierUidByUserId(any())).thenReturn(Arrays.asList("uid1", "uid2"));

        additionalUserTierService.verifyAdditionalTierFree(orderDO, orderProductDo, Lists.newArrayList());

        // 验证相关的方法是否被正确调用
        //verify(additionalUserTierService, never()).addFreeAdditionalProduct(any(),any());
    }

    @Test
    public void testVerifyAdditionalTierFree_WithDeviceProduct_AdditionalProductConfigured_NoSupportBirdDevice() throws Exception {
        OrderDO orderDO = new OrderDO();
        OrderProductDo orderProductDo = new OrderProductDo();
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PRODUCT_DEVICE_NUM.getCode());
        when(productService.queryProductById(any())).thenReturn(productDO);
        when(productService.queryAdditionalProductFree(any())).thenReturn(new ProductDO());
        when(additionalUserTierService.getAllTierUidByUserId(any())).thenReturn(new ArrayList<>());
        when(userTierDeviceService.initPaymentDeviceSn(any(OrderDO.class))).thenReturn(Arrays.asList("device1", "device2"));
        when(tierService.hasSupportBirdDevice(any(), anyList())).thenReturn(false);

        additionalUserTierService.verifyAdditionalTierFree(orderDO, orderProductDo, Lists.newArrayList());

        // 验证相关的方法是否被正确调用
        //verify(additionalUserTierService, never()).addFreeAdditionalProduct(any(),any());
    }

//    @Test
//    public void testVerifyAdditionalTierFree_WithDeviceProduct_AdditionalProductConfigured_SupportBirdDevice() throws Exception {
//        Integer userId = 1;
//        Integer productId = 1;
//        OrderDO orderDO = new OrderDO();
//        orderDO.setUserId(userId);
//        OrderProductDo orderProductDo = new OrderProductDo();
//        ProductDO productDO = new ProductDO();
//        productDO.setId(productId);
//        productDO.setType(ProductTypeEnums.PRODUCT_DEVICE_NUM.getCode());
//
//        when(productService.queryProductById(any())).thenReturn(productDO);
//        when(productService.queryAdditionalProductFree(any())).thenReturn(new ProductDO());
//
//        when(additionalUserTierDao.getAllTierUidByUserId(any())).thenReturn(new ArrayList<>());
//        when(userTierDeviceService.initPaymentDeviceSn(any(OrderDO.class))).thenReturn(Arrays.asList("device1", "device2"));
//        when(tierService.hasSupportBirdDevice(any(), anyList())).thenReturn(true);
//
//
//        PaymentRequest paymentRequest = new PaymentRequest();
//        paymentRequest.setType(PaymentTypeEnums.ADDITIONAL_TIER_FREE_RECEIVE.getCode());
//        paymentRequest.setProductId(productDO.getId());
//        when(paymentService.initOrder(any(),any(),any(),any())).thenReturn(orderDO);
//        when(iOrderDAO.updateOrderStatus(any())).thenReturn(1);
//        doNothing().when(paymentService).initAdditionalUseTier(any(),any());
//
//        additionalUserTierService.verifyAdditionalTierFree(orderDO, orderProductDo, Lists.newArrayList());
//
//        // 验证相关的方法是否被正确调用
//        //verify(additionalUserTierService).addFreeAdditionalProduct(any(), any());
//    }
}
