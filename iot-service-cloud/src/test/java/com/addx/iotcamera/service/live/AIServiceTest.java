package com.addx.iotcamera.service.live;

import com.addx.iotcamera.bean.cache.DevicePirNotifyFactor;
import com.addx.iotcamera.bean.db.AppAiConfigGlobalDO;
import com.addx.iotcamera.bean.domain.ai.AiTaskConfigRequest;
import com.addx.iotcamera.bean.openapi.SaasAITaskIM;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceSupportService;
import com.addx.iotcamera.service.openapi.SaasAIService;
import com.addx.iotcamera.service.video.VideoAIService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import org.addx.iot.common.proto.AiCloudParam;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Map;

import static com.addx.iotcamera.constants.VideoConstants.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AIServiceTest {
    @InjectMocks
    private AIService aiService;

    @Mock
    private VideoEventRedisService redisService;

    @Mock
    private VideoAIService videoAIService;

    @Mock
    private PushService pushService;

    @Mock
    private ActivityZoneService activityZoneService;

    @Mock
    private DeviceSupportService deviceSupportService;

    @Mock
    private UserService userService;

    @Mock
    private SaasAIService aiSaasService;

    @Before
    public void init() {
    }

    @Test
    @DisplayName("获取当前视频eventKey-参数")
    public void getVideoEventKey_param_serialNumberEmpty() {
        String serialNumber = "";
        String traceId = PhosUtils.getUUID();

        String expectedResult = "";
        String actualResult = aiService.getVideoEventKey(serialNumber, traceId);
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取当前视频eventKey-参数")
    public void getVideoEventKey_param() {
        String serialNumber = PhosUtils.getUUID();
        String traceId = "";

        String expectedResult = "";
        String actualResult = aiService.getVideoEventKey(serialNumber, traceId);
        assertEquals(expectedResult, actualResult);
    }


    @Test
    @DisplayName("获取当前视频eventKey-新视频")
    public void getVideoEventKey_new() {
        String serialNumber = PhosUtils.getUUID();
        String traceId = PhosUtils.getUUID();

        when(redisService.getHashEntries(any())).thenReturn(Maps.newHashMap());
        doNothing().when(redisService).setHashFieldValueMap(any(), any(), any());
        doNothing().when(redisService).set(any(), any(), any());

        aiService.getVideoEventKey(serialNumber, traceId);
        verify(redisService, times(1)).set(any(), any(), any());
    }


    @Test
    @DisplayName("获取当前视频eventKey-距离最近一次上报超过15s")
    public void getVideoEventKey_expire_lastVedio() {
        String serialNumber = PhosUtils.getUUID();
        String traceId = PhosUtils.getUUID();

        Long currentTime = System.currentTimeMillis();
        Map<Object, Object> videoEventMap = Maps.newHashMap();
        videoEventMap.put(DEVICE_VIDEO_EVENT_LAST_TIME, currentTime - 2 * DEVICE_VIDEO_EVENT_LAST_TIME_LIMIT);
        videoEventMap.put(DEVICE_VIDEO_EVENT_START_TIME, currentTime - 2 * DEVICE_VIDEO_EVENT_LAST_TIME_LIMIT);


        when(redisService.getHashEntries(any())).thenReturn(Maps.newHashMap());
        doNothing().when(redisService).setHashFieldValueMap(any(), any(), any());
        doNothing().when(redisService).set(any(), any(), any());

        aiService.getVideoEventKey(serialNumber, traceId);
        verify(redisService, times(1)).set(any(), any(), any());
    }


    @Test
    @DisplayName("获取当前视频eventKey-距离事件开始超过30m")
    public void getVideoEventKey_expire_eventStart() {
        String serialNumber = PhosUtils.getUUID();
        String traceId = PhosUtils.getUUID();

        Long currentTime = System.currentTimeMillis();
        Map<Object, Object> videoEventMap = Maps.newHashMap();
        videoEventMap.put(DEVICE_VIDEO_EVENT_LAST_TIME, currentTime - 5);
        videoEventMap.put(DEVICE_VIDEO_EVENT_START_TIME, currentTime - 2 * DEVICE_VIDEO_EVENT_START_TIME_LIMIT);


        when(redisService.getHashEntries(any())).thenReturn(Maps.newHashMap());
        doNothing().when(redisService).setHashFieldValueMap(any(), any(), any());
        doNothing().when(redisService).set(any(), any(), any());

        aiService.getVideoEventKey(serialNumber, traceId);
        verify(redisService, times(1)).set(any(), any(), any());
    }


    @Test
    @DisplayName("获取当前视频eventKey")
    public void getVideoEventKey() {
        String serialNumber = PhosUtils.getUUID();
        String traceId = PhosUtils.getUUID();

        Long currentTime = System.currentTimeMillis();
        Long eventKey = currentTime - DEVICE_VIDEO_EVENT_START_TIME_LIMIT / 2;
        Map<Object, Object> videoEventMap = Maps.newHashMap();

        videoEventMap.put(DEVICE_VIDEO_EVENT_LAST_TIME, currentTime - 5);
        videoEventMap.put(DEVICE_VIDEO_EVENT_START_TIME, eventKey);


        when(redisService.getHashEntries(any())).thenReturn(videoEventMap);
        doNothing().when(redisService).setHashFieldValue(any(), any(), any());
        doNothing().when(redisService).set(any(), any(), any());
        aiService.getVideoEventKey(serialNumber, traceId);

        String expectedResult = String.valueOf(eventKey);
        String actualResult = aiService.getVideoEventKey(serialNumber, traceId);
        assertEquals(expectedResult, actualResult);
    }


    @Test
    @DisplayName("测试 getSaasAiTaskConfig 方法")
    public void testGetSaasAiTaskConfig() {
        String serialNumber = PhosUtils.getUUID();
        AiTaskConfigRequest request = new AiTaskConfigRequest();
        request.setSerialNumber(serialNumber);
        request.setUserId("111");

        // 测试场景1: 当没有找到配置时
        when(pushService.getDevicePirNotifyFactor(any())).thenReturn(new DevicePirNotifyFactor());
        when(activityZoneService.queryActivityZone(any())).thenReturn(null);
        when(deviceSupportService.queryDeviceSupportBySn(any())).thenReturn(new CloudDeviceSupport());
        when(userService.queryUserById(any())).thenReturn(null);
        SaasAITaskIM result = aiService.getSaasAiTaskConfig(request);
        assertNull(result);

        // 测试场景2: 当找到配置但模板为空时
        AppAiConfigGlobalDO emptyConfig = new AppAiConfigGlobalDO();
        result = aiService.getSaasAiTaskConfig(request);
        assertNull(result);

        // 测试场景3: 当找到配置且有模板和参数时
        AppAiConfigGlobalDO validConfig = new AppAiConfigGlobalDO();
        validConfig.setTemplate("{\"task\": \"${task}\", \"enabled\": ${enabled}}");
        validConfig.setParamList("[{\"name\": \"task\", \"value\": \"detection\"}, {\"name\": \"enabled\", \"value\": \"true\"}]");
        result = aiService.getSaasAiTaskConfig(request);
        assertNull(result);


        // 测试场景4: 当找到配置有模板但没有参数时
        AppAiConfigGlobalDO configWithoutParams = new AppAiConfigGlobalDO();
        configWithoutParams.setTemplate("{\"task\": \"${task}\", \"enabled\": ${enabled}}");
        result = aiService.getSaasAiTaskConfig(request);

    }

    @Test
    @DisplayName("测试 getAiCloudParam 方法")
    public void testGetAiCloudParam() {
        when(videoAIService.getAiTaskConfig(any(), anyBoolean())).thenAnswer(it -> {
            String jsonStr = "{\"activityZoneList\":[],\"context\":{\"vipCountType\":16},\"countryNo\":\"US\",\"deviceSn\":\"b021851d785bcaff2488479f35917440\",\"idBox\":{\"colors\":[],\"visualizeRecognition\":[\"PERSON\",\"PET\",\"VEHICLE\"]},\"images\":[],\"inputType\":0,\"isLast\":0,\"order\":-1,\"outParams\":\"{\\\"outEncodeType\\\":0,\\\"userSetAz\\\":false}\",\"outStorage\":{\"bucket\":\"a4x-staging-us\",\"clientRegion\":\"us-east-1\",\"expiredConfig\":{\"eventCoverImage\":5184000,\"eventSummary\":86400,\"objectImage\":86400},\"expiredSeconds\":1800,\"keyPrefix\":\"ai-saas-out-storage\",\"serviceName\":\"S3\"},\"outputTopic\":\"staging-us-video-generate\",\"outputType\":1,\"ownerId\":\"1017776\",\"recognitionObjects\":[{\"category\":\"PERSON\",\"functions\":[\"RECOGNITION\",\"EVENT\"]},{\"category\":\"PET\",\"functions\":[\"RECOGNITION\",\"EVENT\"]},{\"category\":\"VEHICLE\",\"functions\":[\"EVENT\",\"ID\"]}],\"sliceTotalNum\":-1,\"taskId\":\"12844503\",\"taskSendTime\":1733301908,\"tenantId\":\"vicoo\",\"timeout\":-1,\"traceId\":\"79956538\"}";
            return JSON.parseObject(jsonStr, SaasAITaskIM.class);
        });

        String serialNumber = PhosUtils.getUUID();
        AiTaskConfigRequest request = new AiTaskConfigRequest();
        request.setSerialNumber(serialNumber);
        request.setUserId("111");
        when(pushService.getDevicePirNotifyFactor(any())).thenReturn(new DevicePirNotifyFactor());
        CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportSendAiImageDirect(true);
        when(deviceSupportService.queryDeviceSupportBySn(any())).thenReturn(cloudDeviceSupport);
        // 测试场景1: 当没有找到配置时
        SaasAITaskIM saasAITaskIM = new SaasAITaskIM();
        saasAITaskIM.setOwnerId("111");
        saasAITaskIM.setTenantId("vico");
        saasAITaskIM.setCountryNo("cn");
        saasAITaskIM.setOutParams("");
        saasAITaskIM.setOrder(0);
        saasAITaskIM.setContext(new SaasAITaskIM.Context());
        saasAITaskIM.setOutParams("");
        saasAITaskIM.setOutStorage(new SaasAITaskIM.OutStorage().setBucket("bucket").setServiceName("").setKeyPrefix(""));
        saasAITaskIM.setOutputTopic("");
        saasAITaskIM.setRecognitionObjects(new ArrayList<>());
        saasAITaskIM.setIdBox(new SaasAITaskIM.IdBox());
        saasAITaskIM.setActivityZoneList(new ArrayList<>());
//        when(aiSaasService.getSaasAiTaskConfig(any(), anyBoolean())).thenReturn(saasAITaskIM);
        AiCloudParam result = aiService.getAiCloudParam(request);


        // 测试场景2: 当找到配置但模板为空时
        AppAiConfigGlobalDO emptyConfig = new AppAiConfigGlobalDO();
        result = aiService.getAiCloudParam(request);

        // 测试场景3: 当找到配置且有模板和参数时
        AppAiConfigGlobalDO validConfig = new AppAiConfigGlobalDO();
        validConfig.setTemplate("{\"cloudEnabled\": ${cloudEnabled}, \"sensitivity\": ${sensitivity}}");
        validConfig.setParamList("[{\"name\": \"cloudEnabled\", \"value\": \"true\"}, {\"name\": \"sensitivity\", \"value\": \"0.8\"}]");
        result = aiService.getAiCloudParam(request);


        // 测试场景4: 当找到配置有模板但没有参数时
        AppAiConfigGlobalDO configWithoutParams = new AppAiConfigGlobalDO();
        configWithoutParams.setTemplate("{\"cloudEnabled\": ${cloudEnabled}, \"sensitivity\": ${sensitivity}}");
        result = aiService.getAiCloudParam(request);
    }
}
