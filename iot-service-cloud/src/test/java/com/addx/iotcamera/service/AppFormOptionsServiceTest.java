package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.device.attributes.DeviceAttributes;
import com.addx.iotcamera.bean.device.attributes.DeviceEnumAttributes;
import com.addx.iotcamera.bean.device.attributes.DeviceModifiableAttribute;
import com.addx.iotcamera.bean.device.attributes.DeviceSwitchAttributes;
import com.addx.iotcamera.bean.domain.AppFormOptionsDO;
import com.addx.iotcamera.constants.DeviceAttributeName;
import com.addx.iotcamera.service.device.DeviceAttributeService;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.stream.Collectors;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;


@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppFormOptionsServiceTest {

    @InjectMocks
    private AppFormOptionsService appFormOptionsService;
    @Mock
    private DeviceAttributeService deviceAttributeService;

    @Test
    public void test_getAppFormOptions() {
        final String sn = OpenApiUtil.shortUUID();
        final int userId = (new Random()).nextInt(1000_000);
        {
            when(deviceAttributeService.getDeviceAttributes(any())).thenReturn(Result.Failure(""));
            Assert.assertNull(appFormOptionsService.getAppFormOptions(userId, sn));
        }
        {
            final List<DeviceModifiableAttribute> attrList = new LinkedList<>();
            attrList.add(new DeviceSwitchAttributes().setName(DeviceAttributeName.pirCooldownSwitch.name())
                    .setDisabled(false).setValue(true));
            attrList.add(new DeviceEnumAttributes().setName(DeviceAttributeName.pirCooldownTime.name())
                    .setValue("10s")
                    .setOptions(Arrays.asList("60s", "180s", "300s"))
                    .setDisabledOptions(Arrays.asList("10s", "30s")));
            attrList.add(new DeviceEnumAttributes().setName(DeviceAttributeName.pirRecordTime.name())
                    .setValue("10s")
                    .setOptions(Arrays.asList("10s", "15s", "20s"))
                    .setDisabledOptions(Arrays.asList("auto")));
            DeviceAttributes attrs = new DeviceAttributes().setModifiableAttributes(attrList);
            when(deviceAttributeService.getDeviceAttributes(any())).thenReturn(new Result<>(attrs));

            AppFormOptionsDO result = appFormOptionsService.getAppFormOptions(userId, sn);
            Collection<AppFormOptionsDO.AppFormOptionDO> cooldownUserEnable = result.getDeviceFormOptions().get("cooldownUserEnable");
            Collection<AppFormOptionsDO.AppFormOptionDO> cooldownInS = result.getDeviceFormOptions().get("cooldown_in_s");
            Collection<AppFormOptionsDO.AppFormOptionDO> videoSeconds = result.getDeviceFormOptions().get("videoSeconds");

            Assert.assertEquals(Arrays.asList(true, false), cooldownUserEnable.stream().map(it -> it.getValue()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList(true, false), cooldownUserEnable.stream().filter(it -> it.isEnabled()).map(it -> it.getValue()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList(10, 30, 60, 180, 300), cooldownInS.stream().map(it -> it.getValue()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList(60, 180, 300), cooldownInS.stream().filter(it -> it.isEnabled()).map(it -> it.getValue()).collect(Collectors.toList()));
//            Assert.assertEquals(Arrays.asList(-1, 10, 15, 20), videoSeconds.stream().map(it -> it.getValue()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList(10, 15, 20, -1), videoSeconds.stream().map(it -> it.getValue()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList(10, 15, 20), videoSeconds.stream().filter(it -> it.isEnabled()).map(it -> it.getValue()).collect(Collectors.toList()));
        }

    }

}
