package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.openapi.SaasAITaskIM;
import com.addx.iotcamera.bean.param.CosCredentials;
import com.addx.iotcamera.bean.param.CosParams;
import com.addx.iotcamera.bean.param.OciCredentials;
import com.addx.iotcamera.bean.param.OciParams;
import com.addx.iotcamera.bean.video.StorageCredentials;
import com.addx.iotcamera.bean.video.StorageDest;
import com.addx.iotcamera.bean.video.StoreBucket;
import com.addx.iotcamera.config.*;
import com.addx.iotcamera.enums.GcsCredentials;
import com.addx.iotcamera.helper.GoogleStorageService;
import com.addx.iotcamera.service.device.DeviceWhiteListService;
import com.addx.iotcamera.service.video.StorageAllocateService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.PirServiceName;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.stream.IntStream;

import static org.addx.iot.common.enums.PirServiceName.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class StorageAllocateServiceTest {

    @InjectMocks
    private StorageAllocateService storageAllocateService;

    @Mock
    private DeviceWhiteListService deviceWhiteListService;
    @Mock
    private S3 s3Config;
    @Mock
    private VideoSliceConfig videoSliceConfig;
    @Mock
    private AmazonS3 s3Client;
    @Mock
    private GoogleStorageService gcsService;
    @Mock
    private GcsOptions gcsOptions;
    @Mock
    private CosService cosService;
    @Mock
    private CosConfig cosConfig;
    @Mock
    private OciService ociService;
    @Mock
    private OciConfig ociConfig;
    @Mock
    private LinodeService linodeService;
    @Mock
    private LinodeConfig linodeConfig;
    
    private StorageAllocateConfig config;

    @Before
    public void before() {
        config = new StorageAllocateConfig();
        storageAllocateService.setConfig(config);
        when(gcsOptions.getEnable()).thenReturn(true);
        when(cosConfig.getEnable()).thenReturn(true);
        when(ociConfig.getEnable()).thenReturn(true);
    }

    @Test
    public void test_getBucketByLookBackDays() {
        when(s3Config.getBucketByLookBackDays(eq(7), eq(-1))).thenAnswer(it -> new StoreBucket().setBucket("s3_vip_7d").setRegion("us-s3-1"));
        when(gcsOptions.getBucketByLookBackDays(eq(7))).thenAnswer(it -> "gcs_vip_7d");
        when(cosConfig.getBucketByLookBackDays(eq(7))).thenAnswer(it -> "cos_vip_7d");
        when(cosConfig.getParams()).thenAnswer(it -> new CosParams().setRegion("cos-region"));
        when(ociConfig.getBucketByLookBackDays(eq(7), eq(-1))).thenAnswer(it -> new StoreBucket().setBucket("oci_vip_7d").setRegion("us-oci-1"));

        Assert.assertEquals("s3_vip_7d", storageAllocateService.getBucketByLookBackDays(s3, 7, -1).getBucket());
        Assert.assertEquals("gcs_vip_7d", storageAllocateService.getBucketByLookBackDays(gcs, 7,-1).getBucket());
        Assert.assertEquals("cos_vip_7d", storageAllocateService.getBucketByLookBackDays(cos, 7, -1).getBucket());
        Assert.assertEquals("oci_vip_7d", storageAllocateService.getBucketByLookBackDays(oci, 7, -1).getBucket());

        Assert.assertEquals(null, storageAllocateService.getBucketByLookBackDays(s3, 90, -1));
        Assert.assertEquals(null, storageAllocateService.getBucketByLookBackDays(gcs, 90, -1).getBucket());
        Assert.assertEquals(null, storageAllocateService.getBucketByLookBackDays(cos, 90, -1).getBucket());
        Assert.assertEquals(null, storageAllocateService.getBucketByLookBackDays(oci, 90, -1));

//        Assert.assertEquals(null, storageAllocateService.getBucketByLookBackDays(null, 90));
    }

    @Test
    public void test_getServiceNameForWhiteUserId() {
        Integer userId = 12345;
        Map<String, Set<Integer>> whiteUserIds = new LinkedHashMap<>();
        whiteUserIds.put(s3.name(), Collections.singleton(userId));
        whiteUserIds.put(gcs.name(), Collections.singleton(userId));
        whiteUserIds.put(cos.name(), Collections.singleton(userId));
        whiteUserIds.put(oci.name(), Collections.singleton(userId));
        config.setWhiteUserIds(whiteUserIds);

        Assert.assertEquals(s3, storageAllocateService.getServiceNameForWhiteUserId(ImmutableSet.of(s3), userId));
        Assert.assertEquals(gcs, storageAllocateService.getServiceNameForWhiteUserId(ImmutableSet.of(gcs), userId));
        Assert.assertEquals(cos, storageAllocateService.getServiceNameForWhiteUserId(ImmutableSet.of(cos), userId));
        Assert.assertEquals(oci, storageAllocateService.getServiceNameForWhiteUserId(ImmutableSet.of(oci), userId));

        Assert.assertEquals(s3, storageAllocateService.getServiceNameForWhiteUserId(ImmutableSet.of(s3, gcs, cos, oci), userId));
        Assert.assertEquals(gcs, storageAllocateService.getServiceNameForWhiteUserId(ImmutableSet.of(gcs, cos, oci), userId));
        Assert.assertEquals(cos, storageAllocateService.getServiceNameForWhiteUserId(ImmutableSet.of(cos, oci), userId));

        Assert.assertEquals(null, storageAllocateService.getServiceNameForWhiteUserId(ImmutableSet.of(), userId));
        Assert.assertEquals(null, storageAllocateService.getServiceNameForWhiteUserId(null, userId));

    }

    private void assert_adminIdRange_getServiceNameForRatios(int minAdminId, int maxAdminId, PirServiceName pirServiceName
            , List<StorageAllocateConfig.RatioItem> ratios, List<PirServiceName> supportServiceNames) {
        final Set<PirServiceName> supportServiceNameSet = new HashSet<>(supportServiceNames);
        for (final int adminId : IntStream.rangeClosed(minAdminId, maxAdminId).toArray()) {
            String msg = "ratios=" + ratios + ",supportServiceNames=" + supportServiceNames + ",adminId=" + adminId;
            Assert.assertEquals(msg, pirServiceName, storageAllocateService.getServiceNameForRatios(ratios, supportServiceNameSet, adminId));
        }
    }

    @Test
    public void test_getServiceNameForRatios() {
        when(gcsOptions.getEnable()).thenReturn(true);
        when(cosConfig.getEnable()).thenReturn(true);
        when(ociConfig.getEnable()).thenReturn(true);
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList();
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(), 12346));
        }
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(gcs.name()))
            );
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(), 12346));
        }
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 0, Arrays.asList(cos.name()))
            );
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(cos), 12346));
        }
        { // 国内 cos:s3
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 50, Arrays.asList(cos.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(50, 100, Arrays.asList(s3.name()))
            );
            final List<PirServiceName> supportServiceName = Arrays.asList(cos, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12349, cos, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12350, 12399, s3, ratios, supportServiceName);

            final List<PirServiceName> supportServiceName2 = Arrays.asList(s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12399, s3, ratios, supportServiceName2);
        }
        { // 国外非vip gcs:oci:s3
            /*
              vipRatios: # vip用户/设备中，各云存储的分配比例
                - { "min": 0, "max": 100, "serviceNames": [ "s3" ] }
              noVipRatios: # 非vip用户/设备中，各云存储的分配比例
                - { "min": 0, "max": 50, "serviceNames": [ "gcs", "oci", "s3" ] }
                - { "min": 50, "max": 100, "serviceNames": [ "oci", "gcs", "s3" ] }
             */
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 50, Arrays.asList(gcs.name(), oci.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(50, 80, Arrays.asList(oci.name(), gcs.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(80, 100, Arrays.asList(s3.name()))
            );
            final List<PirServiceName> supportServiceName = Arrays.asList(gcs, oci, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12349, gcs, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12350, 12379, oci, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName);

            final List<PirServiceName> supportServiceName2 = Arrays.asList(s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12399, s3, ratios, supportServiceName2);

            final List<PirServiceName> supportServiceName3 = Arrays.asList(gcs, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12379, gcs, ratios, supportServiceName3);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName3);

            final List<PirServiceName> supportServiceName4 = Arrays.asList(oci, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12379, oci, ratios, supportServiceName4);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName4);
        }

    }

    @Test
    public void test_getServiceNameForRatios_only_gcs() {
        when(gcsOptions.getEnable()).thenReturn(true);
        when(cosConfig.getEnable()).thenReturn(false);
        when(ociConfig.getEnable()).thenReturn(false);
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList();
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(), 12346));
        }
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(gcs.name()))
            );
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(), 12346));
        }
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 0, Arrays.asList(cos.name()))
            );
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(cos), 12346));
        }
        { // 国内 cos:s3
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 50, Arrays.asList(cos.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(50, 100, Arrays.asList(s3.name()))
            );
            final List<PirServiceName> supportServiceName = Arrays.asList(cos, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12349, s3, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12350, 12399, s3, ratios, supportServiceName);

            final List<PirServiceName> supportServiceName2 = Arrays.asList(s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12399, s3, ratios, supportServiceName2);
        }
        { // 国外非vip gcs:oci:s3
            /*
              vipRatios: # vip用户/设备中，各云存储的分配比例
                - { "min": 0, "max": 100, "serviceNames": [ "s3" ] }
              noVipRatios: # 非vip用户/设备中，各云存储的分配比例
                - { "min": 0, "max": 50, "serviceNames": [ "gcs", "oci", "s3" ] }
                - { "min": 50, "max": 100, "serviceNames": [ "oci", "gcs", "s3" ] }
             */
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 50, Arrays.asList(gcs.name(), oci.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(50, 80, Arrays.asList(oci.name(), gcs.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(80, 100, Arrays.asList(s3.name()))
            );
            final List<PirServiceName> supportServiceName = Arrays.asList(gcs, oci, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12349, gcs, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12350, 12379, gcs, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName);

            final List<PirServiceName> supportServiceName2 = Arrays.asList(s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12399, s3, ratios, supportServiceName2);

            final List<PirServiceName> supportServiceName3 = Arrays.asList(gcs, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12379, gcs, ratios, supportServiceName3);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName3);

            final List<PirServiceName> supportServiceName4 = Arrays.asList(oci, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12379, s3, ratios, supportServiceName4);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName4);
        }

    }

    @Test
    public void test_getServiceNameForRatios_only_cos() {
        when(gcsOptions.getEnable()).thenReturn(false);
        when(cosConfig.getEnable()).thenReturn(true);
        when(ociConfig.getEnable()).thenReturn(false);
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList();
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(), 12346));
        }
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(gcs.name()))
            );
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(), 12346));
        }
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 0, Arrays.asList(cos.name()))
            );
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(cos), 12346));
        }
        { // 国内 cos:s3
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 50, Arrays.asList(cos.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(50, 100, Arrays.asList(s3.name()))
            );
            final List<PirServiceName> supportServiceName = Arrays.asList(cos, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12349, cos, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12350, 12399, s3, ratios, supportServiceName);

            final List<PirServiceName> supportServiceName2 = Arrays.asList(s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12399, s3, ratios, supportServiceName2);
        }
        { // 国外非vip gcs:oci:s3
            /*
              vipRatios: # vip用户/设备中，各云存储的分配比例
                - { "min": 0, "max": 100, "serviceNames": [ "s3" ] }
              noVipRatios: # 非vip用户/设备中，各云存储的分配比例
                - { "min": 0, "max": 50, "serviceNames": [ "gcs", "oci", "s3" ] }
                - { "min": 50, "max": 100, "serviceNames": [ "oci", "gcs", "s3" ] }
             */
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 50, Arrays.asList(gcs.name(), oci.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(50, 80, Arrays.asList(oci.name(), gcs.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(80, 100, Arrays.asList(s3.name()))
            );
            final List<PirServiceName> supportServiceName = Arrays.asList(gcs, oci, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12349, s3, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12350, 12379, s3, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName);

            final List<PirServiceName> supportServiceName2 = Arrays.asList(s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12399, s3, ratios, supportServiceName2);

            final List<PirServiceName> supportServiceName3 = Arrays.asList(gcs, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12379, s3, ratios, supportServiceName3);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName3);

            final List<PirServiceName> supportServiceName4 = Arrays.asList(oci, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12379, s3, ratios, supportServiceName4);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName4);
        }

    }

    @Test
    public void test_getServiceNameForRatios_only_oci() {
        when(gcsOptions.getEnable()).thenReturn(false);
        when(cosConfig.getEnable()).thenReturn(false);
        when(ociConfig.getEnable()).thenReturn(true);
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList();
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(), 12346));
        }
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(gcs.name()))
            );
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(), 12346));
        }
        { // s3兜底
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 0, Arrays.asList(cos.name()))
            );
            Assert.assertEquals(s3, storageAllocateService.getServiceNameForRatios(ratios, ImmutableSet.of(cos), 12346));
        }
        { // 国内 cos:s3
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 50, Arrays.asList(cos.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(50, 100, Arrays.asList(s3.name()))
            );
            final List<PirServiceName> supportServiceName = Arrays.asList(cos, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12349, s3, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12350, 12399, s3, ratios, supportServiceName);

            final List<PirServiceName> supportServiceName2 = Arrays.asList(s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12399, s3, ratios, supportServiceName2);
        }
        { // 国外非vip gcs:oci:s3
            /*
              vipRatios: # vip用户/设备中，各云存储的分配比例
                - { "min": 0, "max": 100, "serviceNames": [ "s3" ] }
              noVipRatios: # 非vip用户/设备中，各云存储的分配比例
                - { "min": 0, "max": 50, "serviceNames": [ "gcs", "oci", "s3" ] }
                - { "min": 50, "max": 100, "serviceNames": [ "oci", "gcs", "s3" ] }
             */
            final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 50, Arrays.asList(gcs.name(), oci.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(50, 80, Arrays.asList(oci.name(), gcs.name(), s3.name())),
                    new StorageAllocateConfig.RatioItem(80, 100, Arrays.asList(s3.name()))
            );
            final List<PirServiceName> supportServiceName = Arrays.asList(gcs, oci, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12349, oci, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12350, 12379, oci, ratios, supportServiceName);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName);

            final List<PirServiceName> supportServiceName2 = Arrays.asList(s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12399, s3, ratios, supportServiceName2);

            final List<PirServiceName> supportServiceName3 = Arrays.asList(gcs, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12379, s3, ratios, supportServiceName3);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName3);

            final List<PirServiceName> supportServiceName4 = Arrays.asList(oci, s3);
            assert_adminIdRange_getServiceNameForRatios(12300, 12379, oci, ratios, supportServiceName4);
            assert_adminIdRange_getServiceNameForRatios(12380, 12399, s3, ratios, supportServiceName4);
        }

    }

    @Test
    public void test_getServiceName() {
        final String sn = OpenApiUtil.shortUUID();
        Integer userId = 12399;
        {
            Map<String, Set<Integer>> whiteUserIds = new LinkedHashMap<>();
            whiteUserIds.put(s3.name(), Collections.singleton(userId));
            whiteUserIds.put(gcs.name(), Collections.singleton(userId));
            whiteUserIds.put(cos.name(), Collections.singleton(userId));
            whiteUserIds.put(oci.name(), Collections.singleton(userId));
            config.setWhiteUserIds(whiteUserIds);

            when(deviceWhiteListService.getPirVideoStorageServiceBySn(sn)).thenReturn(null);
            Assert.assertEquals(s3, storageAllocateService.getServiceName(Arrays.asList(), ImmutableSet.of(s3), sn, userId));
            Assert.assertEquals(gcs, storageAllocateService.getServiceName(Arrays.asList(), ImmutableSet.of(gcs), sn, userId));
            Assert.assertEquals(cos, storageAllocateService.getServiceName(Arrays.asList(), ImmutableSet.of(cos), sn, userId));
            Assert.assertEquals(oci, storageAllocateService.getServiceName(Arrays.asList(), ImmutableSet.of(oci), sn, userId));

            when(deviceWhiteListService.getPirVideoStorageServiceBySn(sn)).thenReturn(s3);
            Assert.assertEquals(s3, storageAllocateService.getServiceName(Arrays.asList(), ImmutableSet.of(s3), sn, userId));
            Assert.assertEquals(gcs, storageAllocateService.getServiceName(Arrays.asList(), ImmutableSet.of(gcs), sn, userId));
            when(deviceWhiteListService.getPirVideoStorageServiceBySn(sn)).thenReturn(null);
        }
        {
            config.setWhiteUserIds(new LinkedHashMap<>());
            Assert.assertEquals(s3, storageAllocateService.getServiceName(Arrays.asList(), ImmutableSet.of(), sn, userId));
        }
    }

    @Test
    @SneakyThrows
    public void test_getStorageDest() {
        when(gcsOptions.getEnable()).thenReturn(true);
        when(cosConfig.getEnable()).thenReturn(true);
        when(ociConfig.getEnable()).thenReturn(true);

        final String sn = OpenApiUtil.shortUUID();
        Integer userId = 12366;
        final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                new StorageAllocateConfig.RatioItem(0, 50, Arrays.asList(gcs.name(), oci.name(), s3.name())),
                new StorageAllocateConfig.RatioItem(50, 80, Arrays.asList(oci.name(), gcs.name(), s3.name())),
                new StorageAllocateConfig.RatioItem(80, 100, Arrays.asList(s3.name()))
        );
        final ImmutableSet<PirServiceName> supportServiceNames = ImmutableSet.of(gcs, oci, s3);
        {
            final Map<Integer, List<StoreBucket>> lookBackDays2Bucket = new LinkedHashMap<>();
            lookBackDays2Bucket.put(3, new ArrayList<StoreBucket>() {{ add(new StoreBucket().setBucket("test").setRegion("us-ashburn-1"));}});
            lookBackDays2Bucket.put(7, new ArrayList<StoreBucket>() {{ add(new StoreBucket().setBucket("test-vip-7d").setRegion("us-ashburn-1"));}});
            final OciConfig ociConfig = new OciConfig().setLookBackDays2Buckets(lookBackDays2Bucket).setParams(new OciParams().setDefaultRegion("us-ashburn-1"));
            when(this.ociConfig.getBucketByLookBackDays(anyInt(), anyInt())).thenAnswer(AdditionalAnswers.delegatesTo(ociConfig));
            when(ociService.getRootUrl(any())).thenReturn("https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/test-vip-7d/o/");

            final StorageDest storageDest = new StorageDest().setServiceName(oci).setBucket("test-vip-7d")
                    .setRootUrl("https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/test-vip-7d/o/");
            Assert.assertEquals(storageDest, storageAllocateService.getStorageDest(ratios, supportServiceNames, sn, userId, 7));
        }
    }

    @Test
    public void test_createOutStorage() {
        when(s3Config.getClientRegion()).thenReturn("s3-region");
//        when(s3Config.getBucket()).thenReturn("s3-vip-60d");
        when(s3Config.getMaxLookBackDaysBucket(-1)).thenAnswer(it -> new StoreBucket().setBucket("s3-vip-60d").setRegion("s3-region"));
        when(cosConfig.getParams()).thenReturn(new CosParams().setRegion("cos-region"));

        final SaasAITaskIM.OutStorage oldOutStorage = new SaasAITaskIM.OutStorage();
        { // s3 普通上传
            String sliceUrl = "https://a4x-staging-eu.s3.eu-central-1.amazonaws.com/device_video_slice/8633a332f14a428c3f030aa886128209/055671629125540OKGy74S79K0zPf/slice_1999_0_0.ts";
            final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(sliceUrl);
            final SaasAITaskIM.OutStorage outStorage = storageAllocateService.createOutStorage(oldOutStorage, storeBucket);            Assert.assertEquals(s3.name(), outStorage.getServiceName());
            Assert.assertEquals("a4x-staging-eu", outStorage.getBucket());
            Assert.assertEquals("s3-region", outStorage.getClientRegion());
        }
        { // s3 加速上传
            String sliceUrl = "https://addx-staging-vip-pro.s3-accelerate.cn-north-1.amazonaws.com.cn/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/slice_1999_0_0.ts";
            final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(sliceUrl);
            final SaasAITaskIM.OutStorage outStorage = storageAllocateService.createOutStorage(oldOutStorage, storeBucket);
            Assert.assertEquals(s3.name(), outStorage.getServiceName());
            Assert.assertEquals("addx-staging-vip-pro", outStorage.getBucket());
            Assert.assertEquals("s3-region", outStorage.getClientRegion());
        }
        { // cos saasAi不支持 能找到同样回看天数
            when(cosConfig.getLookBackDays2Bucket()).thenReturn(ImmutableMap.<Integer, String>builder()
                    .put(7, "addx-test-1302606863").build());
            when(s3Config.getBucketByLookBackDays(eq(7), eq(-1))).thenAnswer(it -> new StoreBucket().setBucket("s3_vip_7d").setRegion("s3-region"));
            String sliceUrl = "https://addx-test-1302606863.cos.ap-beijing.myqcloud.com/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/slice_1999_0_0.ts";
            final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(sliceUrl);
            final SaasAITaskIM.OutStorage outStorage = storageAllocateService.createOutStorage(oldOutStorage, storeBucket);
            Assert.assertEquals(s3.name(), outStorage.getServiceName());
            Assert.assertEquals("s3_vip_7d", outStorage.getBucket());
            Assert.assertEquals("s3-region", outStorage.getClientRegion());
        }
        { // cos saasAi不支持 能找到同样回看天数
            when(cosConfig.getLookBackDays2Bucket()).thenReturn(ImmutableMap.<Integer, String>builder()
                    .put(7, "addx-test-1302606863").build());
            String sliceUrl = "https://yx-test-nov-22-01.us-sea-9.linodeobjects.com/yx_dir/asdfasdfasf/test123.txt";
            final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(sliceUrl);
            Assert.assertEquals(LINODE.name(), storeBucket.getServiceName().name());
        }
        { // cos saasAi不支持 不能找到同样回看天数
            when(cosConfig.getLookBackDays2Bucket()).thenReturn(ImmutableMap.<Integer, String>builder()
                    .put(7, "addx-test-1302606863").build());
            when(s3Config.getBucketByLookBackDays(eq(7), eq(-1))).thenAnswer(it -> null);
            String sliceUrl = "https://addx-test-1302606863.cos.ap-beijing.myqcloud.com/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/slice_1999_0_0.ts";
            final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(sliceUrl);
            final SaasAITaskIM.OutStorage outStorage = storageAllocateService.createOutStorage(oldOutStorage, storeBucket);
            Assert.assertEquals(s3.name(), outStorage.getServiceName());
            Assert.assertEquals("s3-vip-60d", outStorage.getBucket());
            Assert.assertEquals("s3-region", outStorage.getClientRegion());
        }
        { // cos saasAi支持
            config.setSaasAiSupportServiceNames(new HashSet<>(Arrays.asList(s3.name(), cos.name())));
            String sliceUrl = "https://addx-test-1302606863.cos.ap-beijing.myqcloud.com/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/slice_1999_0_0.ts";
            final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(sliceUrl);
            final SaasAITaskIM.OutStorage outStorage = storageAllocateService.createOutStorage(oldOutStorage, storeBucket);
            Assert.assertEquals(cos.name(), outStorage.getServiceName());
            Assert.assertEquals("addx-test-1302606863", outStorage.getBucket());
            Assert.assertEquals("ap-beijing", outStorage.getClientRegion());
        }
        { // oci saasAi支持
            config.setSaasAiSupportServiceNames(new HashSet<>(Arrays.asList(s3.name(), oci.name())));
            String sliceUrl = "https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/oci-bucket/o/device_video_slice/601c2322163006873b38b818334ff97f/06281659325061xFY7HNom1cTFFYC/slice_1999_1_0.ts";
            final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(sliceUrl);
            final SaasAITaskIM.OutStorage outStorage = storageAllocateService.createOutStorage(oldOutStorage, storeBucket);
            Assert.assertEquals(oci.name(), outStorage.getServiceName());
            Assert.assertEquals("oci-bucket", outStorage.getBucket());
            Assert.assertEquals("us-ashburn-1", outStorage.getClientRegion());
        }
        { // oci saasAi不支持
            config.setSaasAiSupportServiceNames(new HashSet<>(Arrays.asList(s3.name())));
            String sliceUrl = "https://storage.googleapis.com/download/storage/v1/b/test-a4x/o/device_video_slice%2Fsn1234%2FtraceId123444%2Fslice_2000_1_0.ts";
            final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(sliceUrl);
            final SaasAITaskIM.OutStorage outStorage = storageAllocateService.createOutStorage(oldOutStorage, storeBucket);
            Assert.assertEquals(s3.name(), outStorage.getServiceName());
            Assert.assertEquals("s3-vip-60d", outStorage.getBucket());
            Assert.assertEquals("s3-region", outStorage.getClientRegion());
        }
        { // 其他上传方式
            config.setSaasAiSupportServiceNames(new HashSet<>(Arrays.asList(s3.name(), cos.name())));
            String sliceUrl = "https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/test/o/123";
            final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(sliceUrl);
            final SaasAITaskIM.OutStorage outStorage = storageAllocateService.createOutStorage(oldOutStorage, storeBucket);
            Assert.assertEquals(s3.name(), outStorage.getServiceName());
            Assert.assertEquals("s3-vip-60d", outStorage.getBucket());
            Assert.assertEquals("s3-region", outStorage.getClientRegion());
        }

    }

    @Test
    public void test_getRootUrlByBucket() {
        {
            Assert.assertEquals("https://storage.googleapis.com/upload/storage/v1/b/bucket1/o/"
                    , storageAllocateService.getRootUrlByBucket(gcs, new StoreBucket().setBucket("bucket1")));
        }
        {
            when(cosService.getRootUrl("bucket1")).thenReturn("https://bucket1-1302606863.cos.ap-beijing.myqcloud.com/");
            Assert.assertEquals("https://bucket1-1302606863.cos.ap-beijing.myqcloud.com/"
                    , storageAllocateService.getRootUrlByBucket(cos, new StoreBucket().setBucket("bucket1").setRegion("ap-beijing")));
        }
        {
            when(ociService.getRootUrl(any(StoreBucket.class))).thenReturn("https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/bucket1/o/");
            Assert.assertEquals("https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/bucket1/o/"
                    , storageAllocateService.getRootUrlByBucket(oci, new StoreBucket().setBucket("bucket1").setRegion("us-ashburn-1")));
        }
        {
            when(linodeService.getRootUrl(any())).thenReturn("https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/test-vip-7d/o/");
            Assert.assertEquals("https://objectstorage.us-ashburn-1.oraclecloud.com/n/idkrj1ijdi7s/b/test-vip-7d/o/"
                    , storageAllocateService.getRootUrlByBucket(LINODE  , new StoreBucket().setBucket("bucket1")));
        }
    }

    @Test
    public void test_getStorageCredentials() {
        String sn= OpenApiUtil.shortUUID();

        when(gcsOptions.getEnable()).thenReturn(false);
        when(cosConfig.getEnable()).thenReturn(false);
        when(ociConfig.getEnable()).thenReturn(false);
        when(gcsService.createTempCredentials(sn)).thenReturn(new GcsCredentials());
        when(cosService.createTempCredential(sn)).thenReturn(new CosCredentials());
        when(ociService.createTempCredential(any(), any())).thenReturn(new OciCredentials());
//        when(ociService.createTempCredentialAndCache(sn)).thenReturn(new OciCredentials());

        final Set<PirServiceName> supportServiceNames = new LinkedHashSet<>(Arrays.asList(s3, gcs, cos, oci));
        {
            config.setVipRatios(Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(s3.name()))
            ));
            config.setNoVipRatios(Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(s3.name()))
            ));
            final StorageCredentials cred = storageAllocateService.getStorageCredentials(supportServiceNames, 123, sn);
            Assert.assertNull(cred.getGcsCredentials());
            Assert.assertNull(cred.getCosCredentials());
            Assert.assertNull(cred.getOciCredentials());
        }
        {
            config.setVipRatios(Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(gcs.name()))
            ));
            config.setNoVipRatios(Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(gcs.name()))
            ));
            when(gcsOptions.getEnable()).thenReturn(true);
            final StorageCredentials cred = storageAllocateService.getStorageCredentials(supportServiceNames, 123, sn);
            Assert.assertNotNull(cred.getGcsCredentials());
            Assert.assertNull(cred.getCosCredentials());
            Assert.assertNull(cred.getOciCredentials());
            when(gcsOptions.getEnable()).thenReturn(false);
        }
        {
            config.setVipRatios(Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(cos.name()))
            ));
            config.setNoVipRatios(Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(cos.name()))
            ));
            when(cosConfig.getEnable()).thenReturn(true);
            final StorageCredentials cred = storageAllocateService.getStorageCredentials(supportServiceNames, 123, sn);
            Assert.assertNull(cred.getGcsCredentials());
            Assert.assertNotNull(cred.getCosCredentials());
            Assert.assertNull(cred.getOciCredentials());
            when(cosConfig.getEnable()).thenReturn(false);
        }
        {
            config.setVipRatios(Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(oci.name()))
            ));
            config.setNoVipRatios(Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(oci.name()))
            ));
            when(ociConfig.getEnable()).thenReturn(true);
            final StorageCredentials cred = storageAllocateService.getStorageCredentials(supportServiceNames, 123, sn);
            Assert.assertNull(cred.getGcsCredentials());
            Assert.assertNull(cred.getCosCredentials());
            Assert.assertNotNull(cred.getOciCredentials());
            when(ociConfig.getEnable()).thenReturn(false);
        }

        final Set<PirServiceName> supportServiceNames1 = new LinkedHashSet<>(Arrays.asList(s3, gcs, cos, oci, LINODE));
        {
            when(linodeConfig.getEnable()).thenReturn(true);
            config.setVipRatios(Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(s3.name()))
            ));
            config.setNoVipRatios(Arrays.asList(
                    new StorageAllocateConfig.RatioItem(0, 100, Arrays.asList(s3.name()))
            ));
            final StorageCredentials cred = storageAllocateService.getStorageCredentials(supportServiceNames1, 123, sn);
            Assert.assertNull(cred.getGcsCredentials());
            Assert.assertNull(cred.getCosCredentials());
            Assert.assertNull(cred.getOciCredentials());
        }
    }

//    @Test
    public void test_getStorageDest2() {
        final String sn = OpenApiUtil.shortUUID();
        /*
        [{"max":30,"min":0,"serviceNames":["gcs"]},{"max":60,"min":30,"serviceNames":["oci"]},
        {"max":100,"min":60,"serviceNames":["s3"]}],
        supportServiceNames=["s3","gcs","cos","oci"],adminId=172301,lookBackDays=3,
        storageDest={"bucket":"a4x-pre-eu-vip-3d",
        "rootUrl":"https://a4x-pre-eu-vip-3d.s3-accelerate.amazonaws.com/","serviceName":"s3"}
         */
        final List<StorageAllocateConfig.RatioItem> ratios = Arrays.asList(
                new StorageAllocateConfig.RatioItem().setMin(0).setMax(30).setServiceNames(Arrays.asList("gcs")),
                new StorageAllocateConfig.RatioItem().setMin(30).setMax(60).setServiceNames(Arrays.asList("oci")),
                new StorageAllocateConfig.RatioItem().setMin(60).setMax(100).setServiceNames(Arrays.asList("s3"))
        );
        final LinkedHashSet<PirServiceName> pirServiceNames = new LinkedHashSet<>(Arrays.asList(s3, gcs, cos, oci));

        final TestHelper testHelper = TestHelper.getInstanceByEnv("pre-eu");
        final StorageAllocateService service = new StorageAllocateService();
        service.setS3(testHelper.getConfig().getObject("s3", S3.class));
        service.setGcsOptions(testHelper.getConfig().getObject("google-storage", GcsOptions.class));
        service.setCosConfig(testHelper.getConfig().getObject("cos", CosConfig.class));
        service.setOciConfig(testHelper.getConfig().getObject("oci", OciConfig.class));
        service.setConfig(new StorageAllocateConfig().setVipRatios(ratios).setNoVipRatios(ratios));
        service.init();

        final StorageDest dest = service.getStorageDest(ratios, pirServiceNames, sn, 172301, 3);
        Assert.assertEquals(gcs, dest.getServiceName());
    }

}
