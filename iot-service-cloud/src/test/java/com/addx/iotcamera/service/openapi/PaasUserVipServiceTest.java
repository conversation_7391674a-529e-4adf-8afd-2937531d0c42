package com.addx.iotcamera.service.openapi;

import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.openapi.*;
import com.addx.iotcamera.config.PaasTenantConfigTest;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.dao.openapi.PaasUserVipDAO;
import com.addx.iotcamera.dao.openapi.PaasVipProductDAO;
import com.addx.iotcamera.dynamo.dao.VideoSliceDAO;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.RandomUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.addx.iotcamera.util.NatureMonthUtil.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PaasUserVipServiceTest {

    @InjectMocks
    private PaasUserVipService paasUserVipService;
    @Mock
    private PaasTenantConfig paasTenantConfig;
    @Mock
    private UserService userService;
    //    @Mock
    private PaasUserVipDAO paasUserVipDAO;
    @Mock
    private DeviceConfigService deviceConfigService;
    @Mock
    private NotificationService notificationService;
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private UserRoleService userRoleService;

    @Mock
    private PaasVipProductService paasVipProductService;

    private static List<PaasUserVipLog> vipLogList = Arrays.asList(
            new PaasUserVipLog(millisOfYmd(20210301), "1", 1),
            new PaasUserVipLog(millisOfYmd(20210401), "0", 0),
            new PaasUserVipLog(millisOfYmd(20210406), "3", 3),
            new PaasUserVipLog(millisOfYmd(20210416), "0", 0),
            new PaasUserVipLog(millisOfYmd(20210420), "2", 2),
            new PaasUserVipLog(millisOfYmd(20210620), "1", 1),
            new PaasUserVipLog(millisOfYmd(20210720), "0", 0),
            new PaasUserVipLog(millisOfYmd(20210920), "1", 1),
            new PaasUserVipLog(millisOfYmd(20220320), "0", 0),
            new PaasUserVipLog(millisOfYmd(20220921), "2", 2)
    );
    private TestHelper testHelper;
    private Map<String, PaasVipProduct> productMap;

    @Before
    public void before() {
        testHelper = TestHelper.getInstanceByEnv("test");
//        testHelper = TestHelper.getInstanceByLocal();
//        testHelper = TestHelper.getInstanceByEnv("prod");
        PaasVipProductDAO paasVipProductDAO = testHelper.getMapper(PaasVipProductDAO.class);
        productMap = paasVipProductDAO.queryAllPaasVipProduct().stream().collect(Collectors.toMap(it -> it.getId(), it -> it));
        when(paasVipProductService.queryPaasVipProductById(anyString())).thenAnswer(it -> {
            return paasVipProductDAO.queryPaasVipProductById(it.getArgument(0));
        });
        when(paasVipProductService.queryAllPaasVipProduct()).thenAnswer(it -> {
            return paasVipProductDAO.queryAllPaasVipProduct();
        });
        when(applicationContext.getBean(PaasUserVipService.class)).thenReturn(paasUserVipService);

        this.paasUserVipDAO = testHelper.getMapper(PaasUserVipDAO.class);

        VideoSliceDAO videoSliceDAO = new VideoSliceDAO();
        videoSliceDAO.setDynamoDB(testHelper.getAmazonInit().dynamoDB());
        videoSliceDAO.setVideoSliceMapper(testHelper.getAmazonInit().videoSliceMapper());
        videoSliceDAO.setVideoSliceConfig(testHelper.getAmazonInit().videoSliceMapperConfig());
        videoSliceDAO.setVideoCompleteReportMapper(testHelper.getAmazonInit().videoCompleteReportMapper());
        videoSliceDAO.setVideoCompleteReportConfig(testHelper.getAmazonInit().videoCompleteReportMapperConfig());

        S3Service s3Service = new S3Service();
        s3Service.setS3Client(testHelper.getS3Client());
        LibraryService libraryService = LibraryService.builder()
                .libraryStatusService(LibraryStatusService.builder().build())
                .s3Service(s3Service)
                .build();
        paasUserVipService.setPaasUserVipDAO(paasUserVipDAO);
//        paasUserVipService.setExecutor(Runnable::run);

        final PaasTenantConfig paasTenantConfig = new PaasTenantConfig();
        when(this.paasTenantConfig.getPaasTenantInfo(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(paasTenantConfig));
        when(userRoleService.getUserSerialNumberByUserId(anyInt())).thenReturn(Arrays.asList("sn1", "sn2"));
        PaasTenantConfigTest.mockPaasTenantConfig(this.paasTenantConfig);
    }

    @After
    public void after() {
//        testHelper.commitAndClose();
    }

//    @Test
//    public void test_computeMonthlyVipGrade() {
//        PaasUserVipService.MonthlyVipGrade vipGrade = PaasUserVipService.computeMonthlyVipGrade(vipLogList, 202103, 202202);
//        log.info("computeMonthlyVipGrade:{}", vipGrade);
//        int[][] ints = PaasUserVipService.countMonthlyVipNum(Arrays.asList(vipGrade), 202103, 202202);
//        String str = Stream.of(ints).map(Arrays::toString).collect(Collectors.joining(",\n", "[\n", "\n]"));
//        log.info("countMonthlyVipNum:{}", str);
//    }

    @Test
    public void test_ymDiff() {
        Assert.assertEquals(11, ymDiff(202101, 202112));
        Assert.assertEquals(12, ymDiff(202101, 202201));
        Assert.assertEquals(7, ymDiff(202106, 202201));
        Assert.assertEquals(20, ymDiff(202101, 202209));
    }

    final String tenantId = "longse";

    private List<String> userIds = Arrays.asList(
            "dc53ccf2180bafbad61962f2e14aaaf6",
            "08033bc82cf829775c609511d74f7102",
            "b533db20f9e1a8e00fccfbea80c62d83",
            "d5b9f52c9cc62a446c87d76f169f8a02",
            "6ac641dfe32fb011857f742ef6ced4d2",
            "574c7572e9ef5ed12456d413fd26699c",
            "16b656dc9c04407152ccd6737ad22db4",
            "8494fbfd94d327c9b9cb801231843799",
            "672fdc2960d22523a027aa6a6488341c",
            "40017f5b4c2c2334a76454b1af1aa061",
            "b7f718b82645422be6d3deb7e659c8c1",
            "1c55d9331762a8335211fffd5931b2be",
            "91fc7ac9cec9b97474753da6ff5e3cf3",
            "225a1bf817287d4fb8258cc75e73806f",
            "25fca04613c0957be30151c771c72ab9",
            "8638cb2b44fc8b34ee74c17f97673a20",
            "f3b65a3354af70eba2c1522fe0a98670"
    );

    @Test
    public void test_updatePaasUserVip() {
        long expectNum = vipLogList.stream().filter(it -> it.getVipLevel() != PaasVipLevel.NO_VIP.getCode()).count();
        for (int i = 0; i < 1; i++) {
            final String userId = OpenApiUtil.shortUUID();
            when(userService.queryByTenantIdAndThirdUserId(tenantId, userId)).thenReturn(new User() {{
                setThirdUserId(userId);
                setTenantId(tenantId);
            }});
            for (PaasUserVipLog vipLog : vipLogList) {
                PaasUserVipUpdate input = new PaasUserVipUpdate().setTenantId(tenantId).setUserId(userId)
                        .setProductId(vipLog.getProductId()).setTime(vipLog.getTime());
                Result result = paasUserVipService.updatePaasUserVip(input);
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            List<PaasUserVipLog> paasUserVipLogs = paasUserVipDAO.queryPaasUserVip(tenantId, userId);
            Assert.assertEquals(expectNum, paasUserVipLogs.size());

            PaasVipLevel vipGrade = PaasVipLevel.codeOf(paasUserVipService.queryLastPaasUserVip(tenantId, userId).getVipLevel());
            log.info("getPaasUserVipGrade:{}", vipGrade);
        }
    }

    //    @Test
    public void test_updatePaasUserVip2() {
        for (String userId : userIds) {
            Calendar calendar = calendarOfYmd(20200101);
            for (int i = 0; i < 10; i++) {
                calendar.add(Calendar.DATE, RandomUtils.nextInt(0, 50));
                int productId = RandomUtils.nextInt(0, 4);
                PaasUserVipUpdate input = new PaasUserVipUpdate().setTenantId(tenantId).setUserId(userId)
                        .setProductId(productId + "").setTime(calendar.getTimeInMillis());
                Result result = paasUserVipService.updatePaasUserVip(input);
                Assert.assertEquals(Result.successFlag, result.getResult());
            }
            PaasVipLevel vipGrade = PaasVipLevel.codeOf(paasUserVipService.queryLastPaasUserVip(tenantId, userId).getVipLevel());
            log.info("getPaasUserVipGrade:{}", vipGrade);
        }
    }

    @Test
    public void test_freeTrail() throws UnsupportedEncodingException {
        long time = System.currentTimeMillis();
        long DAY = 24 * 3600 * 1000;
        String tenantId = "test_freeTrail_" + OpenApiUtil.shortUUID();

        when(userService.queryByTenantIdAndThirdUserId(eq(tenantId), anyString())).thenAnswer(it -> new User() {{
            setThirdUserId(it.getArgument(0));
            setTenantId(tenantId);
        }});

        PaasUserVipUpdate update = new PaasUserVipUpdate().setTenantId(tenantId).setTime(time);
        Result<JSONObject> result;
        update.setUserId(OpenApiUtil.shortUUID()).setProductId("4").setTime(time);
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(102), result.getResult());

        when(this.paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnablePaasUserVip(true)
                .setEnablePaasUserVipFreeTrial(true).setEnablePaasUserVipEndsEarly(false));
        // 多次免费领取都返回0
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        update.setTime(time + 7 * DAY);
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        // 设备正在免费试用套餐,不支持提前结束
        update.setProductId("3").setTime(time + 14 * DAY);
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(1003), result.getResult());
        // 设备正在免费试用套餐,支持提前结束
        {
            when(this.paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnablePaasUserVip(true)
                    .setEnablePaasUserVipFreeTrial(true).setEnablePaasUserVipEndsEarly(true));
            result = paasUserVipService.updatePaasUserVip(update);
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
            when(this.paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnablePaasUserVip(true)
                    .setEnablePaasUserVipFreeTrial(true).setEnablePaasUserVipEndsEarly(false));
        }
        // 免费试用套餐过期
        update.setProductId("3").setTime(time + 32 * DAY);
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        // 设备已经领取过免费试用套餐
        update.setProductId("4").setTime(time + 41 * DAY);
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(1001), result.getResult());

        // 设备已经开通了VIP套餐
        update.setUserId(OpenApiUtil.shortUUID()).setProductId("2").setTime(time);
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        update.setProductId("4").setTime(time + 7 * DAY);
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(1002), result.getResult());

        // 开通vip，自动过期
        update.setUserId(OpenApiUtil.shortUUID()).setProductId("4").setTime(time);
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
//        PaasUserVipLog vipLog = paasUserVipService.queryLastPaasUserVip(tenantId, update.getUserId());
//        Map<String, PaasVipProduct> deviceVipProductMap = paasVipProductService.queryAllPaasVipProduct()
//                .stream().collect(Collectors.toMap(it -> it.getId(), it -> it));
//        PaasUserVipLog newVipLog = paasUserVipService.handleFreeTrailExpired(time + 32 * DAY, vipLog, deviceVipProductMap::get);
//        Assert.assertNotEquals(vipLog, newVipLog);

        // 打开，关闭
        update.setProductId("1").setTime(time + 41 * DAY);
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        update.setProductId("0").setTime(time + 62 * DAY);
        result = paasUserVipService.updatePaasUserVip(update);
        Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
    }

    private static Long DAY_TIME = (long) 1000 * 3600 * 24;

    @Test
    public void test_updatePaasUserVip_freeTrail() {
        final String tenantId = OpenApiUtil.shortUUID();
        final String userId = OpenApiUtil.shortUUID();
        final long time = System.currentTimeMillis();

        when(userService.queryByTenantIdAndThirdUserId(eq(tenantId), anyString())).thenAnswer(it -> new User() {{
            setThirdUserId(it.getArgument(0));
            setTenantId(tenantId);
        }});

        final PaasUserVipUpdate input = new PaasUserVipUpdate().setTenantId(tenantId)
                .setUserId(userId).setTime(time).setProductId("4");
        when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnablePaasUserVipEndsEarly(false));
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo()
                    .setEnablePaasUserVipFreeTrial(false));
            final Result<JSONObject> result = paasUserVipService.updatePaasUserVip(input);
            Assert.assertEquals(result.getMsg(), new Integer(102), result.getResult());
        }
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo()
                    .setEnablePaasUserVipFreeTrial(true));
            final Result<JSONObject> result = paasUserVipService.updatePaasUserVip(input);
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        }
        input.setTime(input.getTime() + DAY_TIME * 15).setProductId("1");
        {
            final Result<JSONObject> result = paasUserVipService.updatePaasUserVip(input);
            Assert.assertEquals(result.getMsg(), new Integer(1003), result.getResult());
        }
        when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnablePaasUserVipEndsEarly(true));
        {
            final Result<JSONObject> result = paasUserVipService.updatePaasUserVip(input);
            Assert.assertEquals(result.getMsg(), new Integer(0), result.getResult());
        }

    }


}
