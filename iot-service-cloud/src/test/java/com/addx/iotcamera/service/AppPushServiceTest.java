package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.PushInfo;
import com.addx.iotcamera.config.AppPushConfig;
import com.addx.iotcamera.config.device.MessageNotificationConfig;
import com.addx.iotcamera.service.xinge.PushXingeService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import com.tencent.xinge.XingeApp;
import com.tencent.xinge.bean.AudienceType;
import com.tencent.xinge.bean.Message;
import com.tencent.xinge.bean.MessageAndroid;
import com.tencent.xinge.push.app.PushAppRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@RunWith(MockitoJUnitRunner.Silent.class)
@EnableConfigurationProperties({AppPushConfig.class, MessageNotificationConfig.class})
@PropertySource(value = {
        "classpath:/app/app_push_xinge.yml",
        "classpath:/app/message_notification.yml"},
        encoding = "utf-8", factory = MixPropertySourceFactory.class)
@TestPropertySource("classpath:application.yml")
@Slf4j
public class AppPushServiceTest {
    @InjectMocks
    private PushXingeService pushXingeService;

    @Mock
    private UserService userService;

    @Mock
    private PushInfoService pushInfoService;

    @Autowired
    @Spy
    private MessageNotificationConfig messageNotificationConfig;

    @Value("${servernode}")
    private String servernode;

    @Autowired
    @Spy
    private AppPushConfig appPushConfig;

    @Before
    public void before() throws Exception {
        TestHelper.injectSpyPropertySource(this);
    }

    @Test
    public void test() {
        log.info("servernode {}", messageNotificationConfig.getMessage());
    }

    @Test
    @DisplayName("查询用户所处tenantId的腾讯推送配置-tenantId为空")
    public void testGetXingeAppConfigTenantIdNull() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Integer userId = 1;
        String packageName = "";

        Class c = PushXingeService.class;
        // 声明调用哪个类的哪个方法
        Class[] cArg = new Class[2];
        cArg[0] = Integer.class;
        cArg[1] = String.class;
        Method method = c.getDeclaredMethod("getXingeApp", cArg);
        //允许处理私有方法
        method.setAccessible(true);
        //when(userService.queryTenantIdById(any())).thenReturn(AppConstants.tenantIdVicoo);
        Object result = method.invoke(pushXingeService, userId, packageName);
        assertEquals(null, result);

    }

    @Test
    @DisplayName("查询用户所处tenantId的腾讯推送配置-tenantId不包含")
    public void testGetXingeAppConfigTenantIdNonSupport() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Integer userId = 1;
        String packageName = "";
        String tenantId = "vicoohome";
        Class c = PushXingeService.class;
        // 声明调用哪个类的哪个方法
        Class[] cArg = new Class[2];
        cArg[0] = Integer.class;
        cArg[1] = String.class;
        Method method = c.getDeclaredMethod("getXingeApp", cArg);
        //允许处理私有方法
        method.setAccessible(true);
        ReflectionTestUtils.setField(pushXingeService, "servernode", "CN");

        when(userService.queryTenantIdById(any())).thenReturn(tenantId);

        PushInfo pushInfo = new PushInfo();
        pushInfo.setBundleName("test");
        when(pushInfoService.getPushInfo(any())).thenReturn(pushInfo);

        XingeApp xingeApp = (XingeApp) method.invoke(pushXingeService, userId, packageName);
        assertEquals(null, xingeApp);
    }


    @Test
    @DisplayName("查询用户所处tenantId的腾讯推送配置-不包含渠道")
    public void test_initPushAppRequest_haschannel() {
        Integer userId = 1;
        Message message = new Message();
        MessageAndroid android = new MessageAndroid();
        message.setAndroid(android);

        String tenantId = "guard";

        PushInfo pushInfo = new PushInfo();
        pushInfo.setBundleName("test");
        pushInfo.setMsgToken("asdasdasd@asdasd");
        when(pushInfoService.getPushInfo(userId)).thenReturn(pushInfo);
        when(userService.queryTenantIdById(userId)).thenReturn(tenantId);


        AudienceType expectResult = AudienceType.account;

        PushAppRequest actualResult = pushXingeService.initPushAppRequest(userId,message);
        assertEquals(expectResult, actualResult.getAudience_type());

        pushInfo.setMsgToken("zasldjasjdlajsdsasd");
        actualResult = pushXingeService.initPushAppRequest(userId,message);
        assertEquals(AudienceType.token, actualResult.getAudience_type());
    }


    @Test
    @DisplayName("查询用户所处tenantId的腾讯推送配置-包含渠道")
    public void test_initPushAppRequest() {
        Integer userId = 1;
        Message message = new Message();
        MessageAndroid android = new MessageAndroid();
        android.setHwChId("1");
//        android.setHwCategory("1");
        android.setXmChId("1");
        android.setOppoChId("1");
        android.setVivoChId("1");
        message.setAndroid(android);

        String tenantId = "guard";

        PushInfo pushInfo = new PushInfo();
        pushInfo.setBundleName("test");
        pushInfo.setMsgToken("asdasdasd@asdasd");
        when(pushInfoService.getPushInfo(userId)).thenReturn(pushInfo);
        when(userService.queryTenantIdById(userId)).thenReturn(tenantId);

        AudienceType expectResult = AudienceType.account;

        PushAppRequest actualResult = pushXingeService.initPushAppRequest(userId,message);
        assertEquals(expectResult, actualResult.getAudience_type());

        pushInfo.setMsgToken("zasldjasjdlajsdsasd");
        actualResult = pushXingeService.initPushAppRequest(userId,message);
        assertEquals(AudienceType.token, actualResult.getAudience_type());
    }
}