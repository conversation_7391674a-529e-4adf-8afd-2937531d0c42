package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.domain.DeviceManualDO;
import com.addx.iotcamera.bean.domain.DeviceStateDO;
import com.addx.iotcamera.bean.domain.FirmwareViewDO;
import com.addx.iotcamera.config.device.DeviceOTADebugConfig;
import com.addx.iotcamera.dao.device.IDeviceOTADAO;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.StateMachineService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.firmware.KernelFirmwareService;
import com.addx.iotcamera.util.JsonUtil;
import com.alibaba.fastjson.JSONObject;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceOtaServiceTest {

    @InjectMocks
    private DeviceOTAService deviceOTAService;

    @Mock
    private IDeviceOTADAO iDeviceOTADAO;

    @Mock
    private FirmwareService firmwareService;

    @Mock
    private DeviceStatusService deviceStatusService;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private StateMachineService stateMachineService;

    @Mock
    private DeviceService deviceService;

    @Mock
    private DeviceOTADebugConfig deviceOTADebugConfig;

    @Mock
    private RedisService redisService;

    @Mock
    private Executor executor;

    @Mock
    private ScheduledThreadPoolExecutor commonScheduledExecutor;

    @Mock
    private KernelFirmwareService kernelFirmwareService;

    @Test
    public void test_createSilentOtaPlan() {
        AtomicReference<String> otaPlanIdRef = new AtomicReference<>();
        doAnswer(invocation -> {
            otaPlanIdRef.set(invocation.getArgument(1));
            return null;
        }).when(redisService).saveList(any(), any(), anyLong());

        when(redisService.rangeList(any())).thenReturn(Arrays.asList(otaPlanIdRef.get(), "oldPlanId"));

        when(deviceService.queryDeviceFirmware(anyString())).thenReturn("1.10.0");

        when(deviceManualService.batchQueryDeviceManualDOByMaxId(0L, 100)).thenReturn(Arrays.asList(new DeviceManualDO(){{
            setOriginalModelNo("test-model-no");
            setSerialNumber("sn_01");
            setModelNo("CG1");
        }}, new DeviceManualDO(){{
            setOriginalModelNo("test-model-no");
            setSerialNumber("sn_02");
            setModelNo("CG6");
        }}, new DeviceManualDO(){{
            setOriginalModelNo("test-model-no");
            setSerialNumber("sn_03");
            setModelNo("CG6");
        }}, new DeviceManualDO(){{
            setOriginalModelNo("test-model-n2");
            setSerialNumber("sn_04");
            setModelNo("CG6");
        }}, new DeviceManualDO(){{
            setOriginalModelNo("test-model-no");
            setSerialNumber("sn_05");
            setModelNo("CG6");
        }}, new DeviceManualDO(){{
            setOriginalModelNo("test-model-no");
            setSerialNumber("sn_06");
            setModelNo("CG6");
        }}));

        when(deviceManualService.getModelNoBySerialNumber("sn_01")).thenReturn("CG1");

        when(firmwareService.buildFirmwareView(eq("sn_01"), anyString())).thenReturn(new FirmwareViewDO(){{
            setFirmwareStatus(1);
        }});

        when(firmwareService.buildFirmwareView(eq("sn_02"), anyString())).thenReturn(null);

        when(firmwareService.buildFirmwareView(eq("sn_03"), anyString())).thenReturn(new FirmwareViewDO(){{
            setFirmwareStatus(0);
        }});

        when(stateMachineService.batchGetDeviceState(any())).thenReturn(new HashMap(){{
            put("sn_01", DeviceStateDO.builder().stateId(1).build());
            put("sn_02", DeviceStateDO.builder().stateId(1).build());
            put("sn_03", DeviceStateDO.builder().stateId(1).build());
            put("sn_05", DeviceStateDO.builder().stateId(9).data("unstable").build());
            put("sn_06", DeviceStateDO.builder().stateId(9).build());
        }});

        when(kernelFirmwareService.queryFirmwareByFirmwareId("1.10.10")).thenReturn(Collections.singletonList("CG6"));

        String otaPlanId = PhosUtils.getUUID();
        Map otaPlanMap = deviceOTAService.createSilentOtaPlan(otaPlanId, "test-model-no", "1.10.10", 50);
        Assert.isTrue(Objects.equals((Integer)otaPlanMap.get("otaSnCount"), 4));


        otaPlanMap = deviceOTAService.createSilentOtaPlan(otaPlanId, null, "1.10.10", 50);
        Assert.isTrue(Objects.equals((Integer)otaPlanMap.get("otaSnCount"), 5));

        otaPlanMap = deviceOTAService.createSilentOtaPlan(otaPlanId, null, "1.9.10", 50);
        Assert.isTrue(otaPlanMap==null);

        when(deviceService.queryDeviceFirmware(anyString())).thenReturn(null);
        otaPlanMap = deviceOTAService.createSilentOtaPlan(otaPlanId, null, "1.10.10", 50);
        Assert.isTrue(Objects.equals((Integer)otaPlanMap.get("otaSnCount"), 0));

        when(kernelFirmwareService.queryFirmwareByFirmwareId("1.9.10")).thenReturn(Collections.singletonList("CG6"));
        otaPlanMap = deviceOTAService.createSilentOtaPlan(otaPlanId, null, "1.9.10", 50);
        Assert.isTrue(Objects.equals((Integer)otaPlanMap.get("otaSnCount"), 0));
    }

    @Test
    public void test_silentOta() {
        when(redisService.containsKey(any())).thenReturn(false);
        deviceOTAService.slientOta("1", 1);

        when(redisService.containsKey(any())).thenReturn(true);
        when(redisService.hashGetString(any(), any())).thenReturn(null);
        deviceOTAService.slientOta("1", 1);

        String otaPlanId = PhosUtils.getUUID();
        String otaPlanCacheKey = String.join("", "otaPlan_", otaPlanId);
        String silentOtaSuccessKey = String.join("", otaPlanCacheKey, "_success");
        String silentOtaFailKey = String.join("", otaPlanCacheKey, "_fail");

        when(redisService.hashGetString(any(), any())).thenReturn(JsonUtil.toJson(IntStream.range(1, 100).mapToObj(i -> "sn_0" + i).collect(Collectors.toList())));
        
        when(firmwareService.buildFirmwareView(eq("sn_01"), anyString())).thenReturn(new FirmwareViewDO(){{
            setFirmwareStatus(1);
        }});
        when(firmwareService.buildFirmwareView(eq("sn_03"), anyString())).thenReturn(new FirmwareViewDO(){{
            setFirmwareStatus(0);
        }});
        when(firmwareService.buildFirmwareView(eq("sn_04"), anyString())).thenReturn(new FirmwareViewDO(){{
            setFirmwareStatus(1);
        }});
        when(firmwareService.buildFirmwareView(eq("sn_05"), anyString())).thenReturn(new FirmwareViewDO(){{
            setFirmwareStatus(1);
        }});

        when(firmwareService.startDeviceOTAWithVersion(any(), eq("sn_01"), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(Result.Success());
        when(firmwareService.startDeviceOTAWithVersion(any(), eq("sn_04"), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(Result.Failure(""));
        when(firmwareService.startDeviceOTAWithVersion(any(), eq("sn_05"), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenThrow(RuntimeException.class);
        
        doAnswer(invocation -> {
            ((Runnable)invocation.getArgument(0)).run();
            return null;
        }).when(executor).execute(any());

        when(commonScheduledExecutor.scheduleAtFixedRate(any(), anyLong(), anyLong(), any())).thenAnswer(invocation -> {
            when(redisService.hasFieldKey(eq(silentOtaFailKey), eq("sn_02"))).thenReturn(true);
            when(redisService.hasFieldKey(eq(silentOtaSuccessKey), eq("sn_03"))).thenReturn(true);

            when(firmwareService.buildFirmwareView(eq("sn_04"), anyString())).thenReturn(null);

            when(firmwareService.buildFirmwareView(eq("sn_05"), anyString())).thenReturn(new FirmwareViewDO(){{
                setFirmwareStatus(4);
            }});
            when(firmwareService.buildFirmwareView(eq("sn_01"), anyString())).thenReturn(new FirmwareViewDO(){{
                setFirmwareStatus(0);
            }});
            when(firmwareService.buildFirmwareView(eq("sn_06"), anyString())).thenReturn(new FirmwareViewDO(){{
                setFirmwareStatus(1);
            }});
            ((Runnable)invocation.getArgument(0)).run();

            when(firmwareService.buildFirmwareView(eq("sn_05"), anyString())).thenReturn(new FirmwareViewDO(){{
                setFirmwareStatus(1);
            }});
            ((Runnable)invocation.getArgument(0)).run();

            return null;
        });

        when(stateMachineService.getDeviceState(eq("sn_01"))).thenReturn(DeviceStateDO.builder().stateId(1).build());
        when(stateMachineService.getDeviceState(eq("sn_02"))).thenReturn(DeviceStateDO.builder().stateId(1).build());
        when(stateMachineService.getDeviceState(eq("sn_03"))).thenReturn(DeviceStateDO.builder().stateId(1).build());
        when(stateMachineService.getDeviceState(eq("sn_04"))).thenReturn(DeviceStateDO.builder().stateId(1).build());
        when(stateMachineService.getDeviceState(eq("sn_05"))).thenReturn(DeviceStateDO.builder().stateId(1).build());
        when(stateMachineService.getDeviceState(eq("sn_06"))).thenReturn(DeviceStateDO.builder().stateId(1).build());
        when(stateMachineService.getDeviceState(eq("sn_07"))).thenReturn(DeviceStateDO.builder().stateId(1).data("unstable").build());
        deviceOTAService.slientOta(otaPlanId, 1);
        verify(redisService, Mockito.atLeastOnce()).hashPut(eq(silentOtaFailKey), any(), any());
        verify(redisService, Mockito.atLeastOnce()).hashPut(eq(silentOtaSuccessKey), eq("sn_01"), any());
    }

    @Test
    public void test_getSilentInfo() {
        when(redisService.containsKey(any())).thenReturn(false);
        deviceOTAService.getSilentOtaInfo("1", null, null);

        when(redisService.containsKey(any())).thenReturn(true);
        when(redisService.hashMultiGet(any(), any())).thenReturn(new JSONObject());
    
        Map otaInfoMap = deviceOTAService.getSilentOtaInfo("1", null, null);
        Assert.isTrue(otaInfoMap.containsKey("successCount"));

        otaInfoMap = deviceOTAService.getSilentOtaInfo("1", 1, null);
        Assert.isTrue(otaInfoMap.containsKey("silentOtaBatchInProgress"));

        otaInfoMap = deviceOTAService.getSilentOtaInfo("1", 1, "sn_01");
        Assert.isTrue(otaInfoMap.containsKey("deviceOtaResult"));
    }
}
