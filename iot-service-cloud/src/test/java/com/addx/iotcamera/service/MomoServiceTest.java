package com.addx.iotcamera.service;

import com.addx.iotcamera.config.pay.MoMoConfig;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.PropertySource;

//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@RunWith(MockitoJUnitRunner.Silent.class)
@EnableConfigurationProperties({MoMoConfig.class})
@PropertySource(value = {
        "classpath:/pay/momoapi.yml"
},
        encoding = "utf-8", factory = MixPropertySourceFactory.class)
@Slf4j
public class MomoServiceTest {
    @Autowired
    @Spy
    private MoMoConfig moMoConfig;

    @Test
    public void test() {
        log.info("config {}", moMoConfig.toString());
    }
}
