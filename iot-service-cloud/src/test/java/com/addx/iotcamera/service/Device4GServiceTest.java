package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.Device4GSimDO;
import com.addx.iotcamera.dao.Device4GDAO;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class Device4GServiceTest {

    @Mock
    private Device4GDAO device4GDAO;

    @InjectMocks
    private Device4GService device4GService;

    @Test
    public void testQueryDevice4GSimDO() {
        String serialNumber = "123456789";
        Device4GSimDO expectedDevice = new Device4GSimDO();
        expectedDevice.setSerialNumber(serialNumber);
        expectedDevice.setIccid("987654321");

        when(device4GDAO.queryDevice4GSimDOBySerialNumber(serialNumber)).thenReturn(expectedDevice);

        Device4GSimDO result = device4GService.queryDevice4GSimDO(serialNumber);

        assertEquals(expectedDevice, result);
        verify(device4GDAO).queryDevice4GSimDOBySerialNumber(serialNumber);
    }

    @Test
    public void test_saveDeviceTrafficDataDay() {
        String serialNumber = "serialNumber";
        Long time = System.currentTimeMillis()/1000L;
        JSONObject jsonObject = new JSONObject().fluentPut("rx_bytes", 1024).fluentPut("tx_bytes", 2048).fluentPut("iccid", "12345");
        when(device4GDAO.insertDeviceTrafficDataDay(any())).thenReturn(1);
        device4GService.saveDeviceTrafficDataDay(serialNumber, time, jsonObject);

        when(device4GDAO.insertDeviceTrafficDataDay(any())).thenThrow(new RuntimeException("insert error"));
        try {
            device4GService.saveDeviceTrafficDataDay(serialNumber, time, jsonObject);
        } catch (RuntimeException e) {
            assertEquals("insert error", e.getMessage());
        }
    }

    @Test
    public void test_clearSimTrafficData() {
        when(device4GDAO.deleteDeviceTrafficDataDay(any())).thenReturn(100);
        device4GService.clearSimTrafficData();
    }
}
