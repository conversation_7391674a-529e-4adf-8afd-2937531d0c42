package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.init.AmazonInit;
import com.addx.iotcamera.bean.video.UploadVideoBeginRequest;
import com.addx.iotcamera.enums.ERecordingTrigger;
import com.addx.iotcamera.helper.TraceIdHelper;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.HttpUtils;
import com.alibaba.fastjson.JSON;
import com.amazonaws.services.sns.AmazonSNS;
import com.amazonaws.services.sns.model.PublishRequest;
import com.amazonaws.services.sns.model.PublishResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class HandleSlicePressTest {

    @Test
    public void test() {
    }

    private static final String msgPtn = "{\n" +
            "\t\"Records\": [{\n" +
            "\t\t\"eventVersion\": \"2.1\",\n" +
            "\t\t\"eventSource\": \"aws:s3\",\n" +
            "\t\t\"awsRegion\": \"us-east-1\",\n" +
            "\t\t\"eventTime\": \"2021-06-09T09:36:18.835Z\",\n" +
            "\t\t\"eventName\": \"ObjectCreated:Put\",\n" +
            "\t\t\"userIdentity\": {\n" +
            "\t\t\t\"principalId\": \"AWS:AROAWWAGBGRUHDL52AUYA:4667a1e3d6f446308113801838763f07\"\n" +
            "\t\t},\n" +
            "\t\t\"requestParameters\": {\n" +
            "\t\t\t\"sourceIPAddress\": \"*************\"\n" +
            "\t\t},\n" +
            "\t\t\"responseElements\": {\n" +
            "\t\t\t\"x-amz-request-id\": \"SKXJF99QDQQ2X8Q9\",\n" +
            "\t\t\t\"x-amz-id-2\": \"qmUSf0ZSBfitxxB2cfqxB54kn1qVl6nIreFLjbsJB3Ify1QM6tTc+pT8qxqdmOaVMR0A7vKMY88wNHbMB6g950y+KeWyr4sd\"\n" +
            "\t\t},\n" +
            "\t\t\"s3\": {\n" +
            "\t\t\t\"s3SchemaVersion\": \"1.0\",\n" +
            "\t\t\t\"configurationId\": \"device_video_slice_put_event_staging_us\",\n" +
            "\t\t\t\"bucket\": {\n" +
            "\t\t\t\t\"name\": \"addx-staging-us\",\n" +
            "\t\t\t\t\"ownerIdentity\": {\n" +
            "\t\t\t\t\t\"principalId\": \"A1T2AEJ02PXEO3\"\n" +
            "\t\t\t\t},\n" +
            "\t\t\t\t\"arn\": \"arn:aws:s3:::addx-staging-us\"\n" +
            "\t\t\t},\n" +
            "\t\t\t\"object\": {\n" +
//                "\t\t\t\t\"key\": \"device_video_slice/674313e0a4e263e09b90b28c9c72694a/0Uzz0d5J0pWx0Qtd022CBw9H7Ac35/slice_1999_3_0.ts\",\n" +
            "\t\t\t\t\"key\": \"device_video_slice/${sn}/${traceId}/slice_1999_${order}_${isLast}.ts\",\n" +
            "\t\t\t\t\"size\": 62980,\n" +
            "\t\t\t\t\"eTag\": \"2fb3c4fbfe15449e88ff6d1785e309c9\",\n" +
            "\t\t\t\t\"sequencer\": \"0060C08B9326D8B592\"\n" +
            "\t\t\t}\n" +
            "\t\t}\n" +
            "\t}]\n" +
            "}";

    private static final List<String> snList = Arrays.asList(
            "e8775c08fe1a732c83e79b56c4fc6210",
            "30a1e5d13fc1f21ac47fcaaebde27425",
            "c8adb5ac6caf00b8052d4b1e858a7816",
            "22272ff90c5e5ac0c23b3f201a3961b6",
            "08c5f5ae0b755dfc9a41a8b1f7c1ef23",
            "3cf5845a30d59ca45611578de4156388",
            "948b1e21876e312f529bb13d4f49a921",
            "AICCG1ENGAA0001UID",
            "d7d7a0666615cc39540726ed06d3eafc",
            "e02cc310706d773c2032edbf41518f7f",
            "e30279aff54405d202d84378446ae8f3",
            "e5f024cee5dab2d06d27b74acaa1a930"
    );

    // device_video_slice_put_staging_us
    private String topicArn = "arn:aws:sns:us-east-1:************:device_video_slice_put_staging_us";
    private String env = "staging-us";
    //    private String topicArn = "arn:aws:sns:eu-central-1:************:device_video_slice_put_staging_eu";
//    private String env = "staging-eu";
    private AmazonSNS snsClient;
    private ScheduledExecutorService executorService;
    private String uploadBeginUrl;
    private int sliceNum = 10;
    private long period = 2000;

    //    @Before
    public void init() {
        TestHelper testHelper = TestHelper.getInstanceByEnv(env);
        AmazonInit amazonInit = testHelper.getAmazonInit();
        this.snsClient = amazonInit.snsClient();
        this.executorService = Executors.newScheduledThreadPool(10);
        // https://api-test.addx.live
        String rootPath = testHelper.getConfig().getJSONObject("videoslice").getString("rootPath");
        this.uploadBeginUrl = rootPath + "/video/uploadBegin";
    }

    //    @Test
    public void test_press_one() throws InterruptedException {
        mockVideo("d49d92e78fa7e3b2e60f07a1fcec79b3");
        Thread.currentThread().join();
    }

    //    @Test
    public void test_press() throws InterruptedException {
        for (int i = 0; true; i++) {
            long t1 = System.currentTimeMillis();
            for (String sn : snList) {
                mockVideo(sn);
            }
            long t2 = System.currentTimeMillis();
            log.info("第{}轮:{}", i, t2 - t1);
        }
    }

    public void mockVideo(String sn) {
        String traceId = TraceIdHelper.generateRandomTraceId(123, ERecordingTrigger.PIR.getValue()) + ":press_test";
        UploadVideoBeginRequest uploadBegin = UploadVideoBeginRequest.builder()
                .traceId(traceId).serialNumber(sn).mqttEventTime(System.currentTimeMillis()).build();
        String resp = HttpUtils.httpPostPojo(uploadBegin, uploadBeginUrl);
        log.info("uploadBegin sn={},traceId={},resp={}", sn, traceId, resp);
        if ("".equals(resp) || JSON.parseObject(resp).getIntValue("code") != 0) {
            throw new RuntimeException("uploadBegin error!");
        }
        for (int i = 0; i < sliceNum; i++) {
            String msg = msgPtn.replace("${sn}", sn)
                    .replace("${traceId}", traceId)
                    .replace("${order}", i + "")
                    .replace("${isLast}", i == sliceNum - 1 ? "1" : "0");
            // 模拟每2秒录制完一个切片
            executorService.schedule(() -> {
                final PublishRequest publishRequest = new PublishRequest(topicArn, msg);
                final PublishResult publishResponse = snsClient.publish(publishRequest);
                log.info("publishResponse:{}", JSON.toJSONString(publishResponse));
            }, i * period, TimeUnit.MILLISECONDS);
        }
    }
}
