package com.addx.iotcamera.service;

import com.addx.iotcamera.enums.DevicePlatformEventEnums;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.publishers.alexa.AlexaClient;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.HashMap;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DevicePlatformEventPublisherTest {

    @Mock
    private MqSender mqSender;
    @Mock
    private AlexaClient alexaClient;
    @InjectMocks
    private DevicePlatformEventPublisher devicePlatformEventPublisher;

    @Test
    public void test_sendEventSync(){
        {
            devicePlatformEventPublisher.sendEventSync(DevicePlatformEventEnums.DOORBELL_PRESS, new HashMap<>(Collections.singletonMap("serialNumber", "sn1")));
        }
        {
            doThrow(new RuntimeException("")).when(alexaClient).alexaKafkaMsgListener(any());
            devicePlatformEventPublisher.sendEventSync(DevicePlatformEventEnums.DOORBELL_PRESS, new HashMap<>(Collections.singletonMap("serialNumber", "sn1")));
        }
    }
}
