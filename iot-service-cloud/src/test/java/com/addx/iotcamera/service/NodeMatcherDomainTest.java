package com.addx.iotcamera.service;

import com.addx.iotcamera.config.CosConfig;
import com.addx.iotcamera.config.GcsOptions;
import com.addx.iotcamera.config.OciConfig;
import com.addx.iotcamera.config.VideoSliceConfig;
import com.addx.iotcamera.helper.GoogleStorageService;
import com.addx.iotcamera.helper.aws.BucketHelper;
import com.addx.iotcamera.testutil.TestHelper;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.FileWriter;
import java.io.PrintWriter;
import java.net.URL;
import java.util.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class NodeMatcherDomainTest {

    @Test
    @SneakyThrows
    public void test() {
//        buildNodeMatcherYaml();
    }

    @SneakyThrows
    public void buildNodeMatcherYaml() {
        final List<String> envs = Arrays.asList("test"
                , "staging", "staging-eu", "staging-us"
                , "pre", "pre-eu", "pre-us"
                , "prod", "prod-eu", "prod-us"
        );
        List<String> lines = new LinkedList<>();
        List<String> lines2 = new LinkedList<>();
        lines.add("backend-domain:");
        for (final String env : envs) {
            final TestHelper testHelper = TestHelper.getInstanceByEnv(env);
            final JSONObject config = testHelper.getConfig();
            config.put("env", env);
            final JSONObject codebind = config.getJSONObject("codebind");
            final String codeBindNode = codebind.getString("node");

            List<String> domains = new LinkedList<>();
            final String iotRootPath = config.getJSONObject("videoslice").getString("rootPath");
            domains.add(new URL(iotRootPath).getHost());
            final String mqttRootPath = config.getJSONObject("mqtt").getString("clientUri");
            domains.add(getDomain(mqttRootPath));
            { // s3
                final String s3Bucket = config.getJSONObject("s3").getString("bucket");
                domains.add(new URL(getS3RootPath(testHelper, s3Bucket)).getHost());
                final JSONObject lookBackDays2Bucket = config.getJSONObject("s3").getJSONObject("lookBackDays2Bucket");
                for (final String s3Bucket2 : (Collection<String>) (Collection) lookBackDays2Bucket.values()) {
                    domains.add(new URL(getS3RootPath(testHelper, s3Bucket2)).getHost());
                }
            }
            // gcs
            final GcsOptions gcsOptions = config.getObject("google-storage", GcsOptions.class);
            if (gcsOptions.getEnable()) {
                domains.add(new URL(GoogleStorageService.URL_GCS).getHost());
            }
            // cos
            final CosConfig cosConfig = config.getObject("cos", CosConfig.class);
            if (cosConfig.getEnable()) {
                final CosService cosService = new CosService();
                cosService.setConfig(cosConfig);
                cosService.init();
                domains.add(new URL(cosService.getRootUrl(cosConfig.getBucket())).getHost());
                for (final String bucket : cosConfig.getLookBackDays2Bucket().values()) {
                    domains.add(new URL(cosService.getRootUrl(bucket)).getHost());
                }
            }
            // oci
            final OciConfig ociConfig = config.getObject("oci", OciConfig.class);
            if (ociConfig.getEnable()) {
                domains.add(new URL(OciService.getBaseUrl(ociConfig.getParams().getDefaultRegion())).getHost());
            }
            addDomains(lines, env, domains);
            if (!env.equals(codeBindNode)) {
                lines2.add(String.format("  # 设备绑定%s时，后端返回二维码信息里面的节点名称是%s", env, codeBindNode));
                addDomains(lines2, codeBindNode, domains);
            }
        }
//        final String outFile = "/Users/<USER>/workspace/iot-service-old/src/test/resources/device/node-matcher.yml";
        final String outFile = "/Users/<USER>/workspace/node-matcher/src/main/resources/backend-domain.yaml";
        try (final PrintWriter out = new PrintWriter(new FileWriter(outFile))) {
            lines.forEach(out::println);
            lines2.forEach(out::println);
        }
    }

    private static String getS3RootPath(TestHelper testHelper, String bucket) {
        final VideoSliceConfig videoSliceConfig = testHelper.getConfig().getObject("videoslice", VideoSliceConfig.class);
        final Boolean s3AccEnable = videoSliceConfig.getDeviceS3AccelerateEnable();
        return new BucketHelper(testHelper.getS3Client(), bucket, s3AccEnable).getDeviceUploadRootPath();
    }

    private static void addDomains(List<String> lines, String env, List<String> domains) {
        lines.add(String.format("  %s:", env));
        Set<String> set = new LinkedHashSet<>();
        for (final String domain : domains) {
            if (!set.add(domain)) continue;
            lines.add(String.format("    - '%s'", domain));
        }
    }

    @SneakyThrows
    private static String getDomain(String url) {
        final int start = url.indexOf("://");
        if (start != -1) url = "http" + url.substring(start);
        return new URL(url).getHost();
    }

}
