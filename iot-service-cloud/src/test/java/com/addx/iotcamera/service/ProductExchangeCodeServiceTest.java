package com.addx.iotcamera.service;


import com.addx.iotcamera.bean.app.device.DeviceBindInfoDO;
import com.addx.iotcamera.bean.app.device.DeviceModelMatchResult;
import com.addx.iotcamera.bean.app.vip.TierInfo;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.ProductExchangeCodeTb;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.response.CouponTierInfoResponse;
import com.addx.iotcamera.config.SimTierTermConfig;
import com.addx.iotcamera.config.TierTermConfig;
import com.addx.iotcamera.config.app.TierProductConfig;
import com.addx.iotcamera.dao.ProductExchangeCodeDAO;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.factory.DeviceUniqCodeService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import static org.junit.Assert.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.bean.db.ProductExchangeCodeTb.AdditionalOrderStatus.CREATING;
import static com.addx.iotcamera.bean.db.ProductExchangeCodeTb.AdditionalOrderStatus.WAIT_CREATE;
import static com.addx.iotcamera.constants.CopyWriteConstans.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ProductExchangeCodeServiceTest {

    @InjectMocks
    private ProductExchangeCodeService productExchangeCodeService;

    @Mock
    private AdditionalUserTierService additionalUserTierService;
    @Mock
    private ProductExchangeCodeDAO exchangeCodeDAO;
    @Mock
    private ProductService productService;
    @Mock
    private UserService userService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private OpenApiConfigService openApiConfigService;
    @Mock
    private PaymentService paymentService;
    @Mock
    private TierService tierService;
    @Mock
    private TierProductConfig tierProductConfig;
    @Mock
    private CopyWrite copyWrite;
    @Mock
    private TierTermConfig tierTermConfig;
    @Mock
    private SimTierTermConfig simTierTermConfig;
    @Mock
    private FactoryDataQueryService factoryDataQueryService;
    @Mock
    private DeviceUniqCodeService deviceUniqCodeService;

    @Test
    public void test_useExchangeCode() {
        final String code = ProductExchangeCodeService.generateRandomExchangeCode();
        final int userId = OpenApiUtil.randInt();

        final ProductExchangeCodeTb codeTb = new ProductExchangeCodeTb();
        codeTb.setCode(code);
        codeTb.setStatus(0);
        codeTb.setExpiredTime(PhosUtils.getUTCStamp() + 3600 * 24);
        codeTb.setProductId(3);
        when(exchangeCodeDAO.queryByCode(code)).thenReturn(codeTb);
        final ProductDO productDO = new ProductDO();
        productDO.setId(3);
        when(productService.queryProductById(3)).thenReturn(productDO);
        when(tierProductConfig.isPertainToTenant(any(), anyString())).thenReturn(true);
        final User user = new User();
        user.setId(userId);
        when(userService.queryUserById(any())).thenReturn(user);
        when(exchangeCodeDAO.useExchangeCode(eq(code), eq(userId), any(), eq(0), eq(1), any())).thenReturn(1);
        {
            when(userRoleService.getUserRoleByUserId(userId, 1)).thenReturn(Arrays.asList());
            productExchangeCodeService.useExchangeCode(code, userId, "t1");
        }
        {
            final UserRoleDO userRoleDO = new UserRoleDO();
            userRoleDO.setAdminId(userId);
            userRoleDO.setUserId(userId);
            userRoleDO.setRoleId("1");
            when(userRoleService.getUserRoleByUserId(userId, 1)).thenReturn(Arrays.asList(userRoleDO));
            productExchangeCodeService.useExchangeCode(code, userId, "t2");
        }
        {
            codeTb.setAdditionalOrderStatus(WAIT_CREATE.getCode());
            when(exchangeCodeDAO.queryByCode(code)).thenReturn(codeTb);
            final UserRoleDO userRoleDO = new UserRoleDO();
            userRoleDO.setAdminId(userId);
            userRoleDO.setUserId(userId);
            userRoleDO.setRoleId("1");
            when(userRoleService.getUserRoleByUserId(userId, 1)).thenReturn(Arrays.asList(userRoleDO));
            productExchangeCodeService.useExchangeCode(code, userId, "t2");
        }
        {
            when(userRoleService.getUserRoleByUserId(userId, 1)).thenReturn(Lists.newArrayList());
            Result<ProductDO> result = productExchangeCodeService.useExchangeCode(code, userId, "t2");
            Assert.assertEquals(ResultCollection.PRODUCT_EXCHANGE_CODE_NO_DEVICE.getCode(), result.getResult().intValue());
        }
    }

    @Test
    public void test_sendAdditionalTierToExchangeCodeUser() throws Exception {

        final int userId = RandomUtils.nextInt(1000_0000, 10000_0000);
        final String tenantId = OpenApiUtil.shortUUID();
        final String code = OpenApiUtil.shortUUID();
        final int productId = RandomUtils.nextInt(1000_0000, 10000_0000);
        {
            when(productService.queryProductById(productId)).thenReturn(null);
            Result result = productExchangeCodeService.sendAdditionalTierToExchangeCodeUser(userId, tenantId, code, productId);
            Assert.assertNotEquals(Result.successFlag, result.getResult());
        }
        {
            when(productService.queryProductById(productId)).thenReturn(new ProductDO().setTenantId("xyz"));
            Result result = productExchangeCodeService.sendAdditionalTierToExchangeCodeUser(userId, tenantId, code, productId);
            Assert.assertNotEquals(Result.successFlag, result.getResult());
        }
        when(productService.queryProductById(productId)).thenReturn(new ProductDO().setTenantId(tenantId));
        {
            when(additionalUserTierService.getAllTierUidByUserId(userId)).thenReturn(Arrays.asList("1"));
            Result result = productExchangeCodeService.sendAdditionalTierToExchangeCodeUser(userId, tenantId, code, productId);
            Assert.assertNotEquals(Result.successFlag, result.getResult());
        }
        when(additionalUserTierService.getAllTierUidByUserId(userId)).thenReturn(Arrays.asList());
        {
            when(exchangeCodeDAO.updateAdditionalOrderByCode(eq(code), isNull(), eq(WAIT_CREATE), eq(CREATING))).thenReturn(0);
            Result result = productExchangeCodeService.sendAdditionalTierToExchangeCodeUser(userId, tenantId, code, productId);
            Assert.assertNotEquals(Result.successFlag, result.getResult());
        }
        when(exchangeCodeDAO.updateAdditionalOrderByCode(eq(code), isNull(), eq(WAIT_CREATE), eq(CREATING))).thenReturn(1);
        {
            when(additionalUserTierService.addFreeAdditionalProduct(eq(userId), any())).thenReturn(Result.Failure(""));
            Result result = productExchangeCodeService.sendAdditionalTierToExchangeCodeUser(userId, tenantId, code, productId);
            Assert.assertNotEquals(Result.successFlag, result.getResult());
        }
        {
            when(additionalUserTierService.addFreeAdditionalProduct(eq(userId), any())).thenThrow(new RuntimeException(""));
            Result result = productExchangeCodeService.sendAdditionalTierToExchangeCodeUser(userId, tenantId, code, productId);
            Assert.assertNotEquals(Result.successFlag, result.getResult());
        }
        {
            OrderDO orderDO = new OrderDO();
            orderDO.setOrderSn(OpenApiUtil.shortUUID());
            when(additionalUserTierService.addFreeAdditionalProduct(eq(userId), any())).thenReturn(new Result<>(orderDO));
            Result result = productExchangeCodeService.sendAdditionalTierToExchangeCodeUser(userId, tenantId, code, productId);
            Assert.assertEquals(result, new Result(orderDO));
        }
    }

    @Test
    public void test_sendAdditionalTierToOldExchangeCodeUser() {

        final int userId = RandomUtils.nextInt(1000_0000, 10000_0000);
        final String tenantId = OpenApiUtil.shortUUID();
        final String code = OpenApiUtil.shortUUID();
        final int productId = RandomUtils.nextInt(1000_0000, 10000_0000);
        {
            when(exchangeCodeDAO.queryByUserIdAndAdditionalOrderStatus(eq(userId), eq(WAIT_CREATE))).thenReturn(Arrays.asList());
            Result result = productExchangeCodeService.sendAdditionalTierToOldExchangeCodeUser(userId, tenantId);
        }
        {
            ProductExchangeCodeTb exchangeCode = new ProductExchangeCodeTb();
            exchangeCode.setAdditionalProductId(productId);
            exchangeCode.setCode(code);
            when(exchangeCodeDAO.queryByUserIdAndAdditionalOrderStatus(eq(userId), eq(WAIT_CREATE))).thenReturn(Arrays.asList(exchangeCode));
            Result result = productExchangeCodeService.sendAdditionalTierToOldExchangeCodeUser(userId, tenantId);
        }
    }

    @Test
    public void test_getTierInfoByProductId() {
        Integer productId = 123;
        String tenantId = "test";
        String language = "test";
        String serialNumber = null;
        ProductDO productDO = new ProductDO();
        productDO.setId(productId);
        productDO.setTierId(13);
        productDO.setMonth(6);
        productDO.setTenantId(tenantId);
        when(tierProductConfig.isPertainToTenant(productId, tenantId)).thenReturn(true);
        Result result;

        // productId is null
        result = productExchangeCodeService.getTierInfoByProductId(null, tenantId, language, serialNumber);
        Assert.assertEquals(Integer.valueOf(ResultCollection.INVALID_PARAMS.getCode()), result.getResult());
        Assert.assertEquals("id不能为空", result.getMsg());

        // product is null
        when(productService.queryProductById(productId)).thenReturn(null);
        result = productExchangeCodeService.getTierInfoByProductId(productId, tenantId, language, serialNumber);
        Assert.assertEquals(Integer.valueOf(ResultCollection.INVALID_PARAMS.getCode()), result.getResult());
        Assert.assertEquals("创建兑换码的商品不存在或已经被禁用", result.getMsg());

        // normal
        // 一代套餐
        Tier tier = new Tier();
        tier.setTierId(0);
        tier.setRollingDays(7);
        tier.setStorage(1L * 1024 * 1024 * 1024);
        tier.setMaxDeviceNum(0);
        tier.setTierType(0);
        tier.setTierServiceType(0);
        tier.setSize(1);
        tier.setLevel(0);
        when(productService.queryProductById(productId)).thenReturn(productDO);
        when(tierService.queryTierById(productDO.getTierId())).thenReturn(tier);
        List<TierInfo> tierInfos = Lists.newArrayList();
        for (int i = 100; i < 105; i++) {
            TierInfo tierInfo = new TierInfo();
            tierInfo.setId(i);
            tierInfo.setName("name"+i);
            tierInfos.add(tierInfo);
        }
        when(tierService.queryTierList(tenantId, language)).thenReturn(tierInfos);
        result = productExchangeCodeService.getTierInfoByProductId(productId, tenantId, language, serialNumber);
        JSONObject jsonObjectResult = JSON.parseObject(result.getData().toString());
        ProductDO product = jsonObjectResult.getObject("product", ProductDO.class);
        Assert.assertEquals("name103", jsonObjectResult.getString("name"));
        Assert.assertEquals(productDO.getId(), product.getId());
        Assert.assertEquals(productDO.getTierId(), product.getTierId());
        Assert.assertEquals(productDO.getMonth(), product.getMonth());

        // 二代套餐
        tier.setLevel(1);
        tier.setTierType(1);
        tier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
        Map<String, Map<String, String>> config = Maps.newHashMap();
        initCloudConfig(config, language);

        Map<String, List<String>> urls = Maps.newHashMap();
        List<String> urlList = Lists.newArrayList("url1", "url2", "url3");
        urls.put(tenantId, urlList);
        when(tierTermConfig.getUrl()).thenReturn(urls);
        when(copyWrite.getConfig()).thenReturn(config);
        when(tierService.queryTierById(productDO.getTierId())).thenReturn(tier);
        when(tierService.initTierNameKey(tier, tenantId)).thenReturn("key");

        result = productExchangeCodeService.getTierInfoByProductId(productId, tenantId, language, serialNumber);
        CouponTierInfoResponse couponTierInfoResponse = JSON.parseObject(result.getData().toString(), CouponTierInfoResponse.class);
        Assert.assertEquals("key", couponTierInfoResponse.getTierNameKey());
        Assert.assertEquals(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode(), couponTierInfoResponse.getTierServiceType());
        Assert.assertEquals(3, couponTierInfoResponse.getTierDescribeList().size());

        // 4g 套餐
        tier.setTierServiceType(TierServiceTypeEnums.TIER_4G.getCode());
        Map<String, List<String>> simUrls = Maps.newHashMap();
        List<String> simUrlList = Lists.newArrayList("url1", "url2", "url3");
        simUrls.put(tenantId, simUrlList);
        when(simTierTermConfig.getUrl()).thenReturn(simUrls);
        when(tierService.queryTierById(productDO.getTierId())).thenReturn(tier);
        initSimConfig(config, language);
        when(copyWrite.getConfig()).thenReturn(config);

        result = productExchangeCodeService.getTierInfoByProductId(productId, tenantId, language, serialNumber);
        CouponTierInfoResponse couponTierInfoResponse1 = JSON.parseObject(result.getData().toString(), CouponTierInfoResponse.class);
        Assert.assertEquals(TierServiceTypeEnums.TIER_4G.getCode(), couponTierInfoResponse1.getTierServiceType());
        Assert.assertEquals(3, couponTierInfoResponse1.getTierDescribeList().size());
    }

    public void initSimConfig(Map<String, Map<String, String>> config, String language) {
        Map<String,String> tierNameMap = Maps.newHashMap();
        tierNameMap.put(language,"无线流量");
        config.put(OFFICIAL_SIM_FEATURE_UNLIMITED_DATA,tierNameMap);
        Map<String,String> storageMap = Maps.newHashMap();
        storageMap.put(language,"不用担心流量耗尽，随时随地查看设备");
        config.put(OFFICIAL_SIM_FEATURE_UNLIMITED_DATA_DESCR,storageMap);
        Map<String,String> unitMap = Maps.newHashMap();
        unitMap.put(language,"极速网络");
        config.put(OFFICIAL_SIM_FEATURE_MAX_SPEED,unitMap);
        Map<String,String> subheadMap = Maps.newHashMap();
        subheadMap.put(language,"无网速限制，尽享流畅直播");
        config.put(OFFICIAL_SIM_FEATURE_MAX_SPEED_DESCR,subheadMap);
        Map<String,String> discountMap = Maps.newHashMap();
        discountMap.put(language,"支持多运营商");
        config.put(OFFICIAL_SIM_FEATURE_MULTI_CARRIER,discountMap);
        Map<String,String> multiCarrierMap = Maps.newHashMap();
        multiCarrierMap.put(language,"同时兼容 Verizon, AT&T 与 T-Mobile");
        config.put(OFFICIAL_SIM_FEATURE_MULTI_CARRIER_DESCR,multiCarrierMap);

    }

    public void initCloudConfig(Map<String, Map<String, String>> config, String language) {
        Map<String,String> tierNameMap = Maps.newHashMap();
        tierNameMap.put(language,"{day_count} 天云存储");
        config.put(AWARENESS_COULD_STORAGE_FEATURE,tierNameMap);
        Map<String,String> storageMap = Maps.newHashMap();
        storageMap.put(language,"可享受 {gb_count}GB 云存储");
        config.put(AWARENESS_CODE_CLOUD_STORAGE_DESCR,storageMap);
        Map<String,String> unitMap = Maps.newHashMap();
        unitMap.put(language,"智能推送");
        config.put(AWARENESS_SMART_PUSH_FEATURE,unitMap);
        Map<String,String> subheadMap = Maps.newHashMap();
        subheadMap.put(language,"人形推送及更多");
        config.put(AWARENESS_SMART_PUSH_DESCR,subheadMap);
        Map<String,String> discountMap = Maps.newHashMap();
        discountMap.put(language,"提醒区域");
        config.put(AWARENESS_ACTIVITY_ZONE_FEATURE,discountMap);
        Map<String,String> multiCarrierMap = Maps.newHashMap();
        multiCarrierMap.put(language,"自定义您想要接收通知的区域");
        config.put(AWARENESS_ACTIVITY_ZONE_DESCR,multiCarrierMap);
    }

    @Test
    public void test_isDeviceOrRelatedDevicesInModelSet() {
        String serialNumber = "test123";
        Set<String> modelNoSet = new HashSet<>(Arrays.asList("MODEL1", "MODEL2", "MODEL3"));
        
        // 1. 测试 deviceManufactureTableDO 为 null 的情况
        when(factoryDataQueryService.queryDeviceManufactureBySn(serialNumber)).thenReturn(null);
        
        DeviceModelMatchResult result1 = productExchangeCodeService.isDeviceOrRelatedDevicesInModelSet(serialNumber, modelNoSet);
        assertFalse(result1.isMatched());
        assertTrue(result1.getBindInfoList() != null && result1.getBindInfoList().isEmpty());
        
        // 2. 测试 userSn 为 null 的情况
        DeviceManufactureTableDO tableWithNullUserSn = new DeviceManufactureTableDO();
        tableWithNullUserSn.setRegisterModelNo("MODEL4");
        tableWithNullUserSn.setUserSn(null);
        
        when(factoryDataQueryService.queryDeviceManufactureBySn(serialNumber)).thenReturn(tableWithNullUserSn);
        
        DeviceModelMatchResult result2 = productExchangeCodeService.isDeviceOrRelatedDevicesInModelSet(serialNumber, modelNoSet);
        assertFalse(result2.isMatched());
        assertTrue(result2.getBindInfoList() != null && result2.getBindInfoList().isEmpty());
        
        // 3. 测试 modelNo 为 null 的情况
        DeviceManufactureTableDO tableWithNullModelNo = new DeviceManufactureTableDO();
        tableWithNullModelNo.setRegisterModelNo(null);
        tableWithNullModelNo.setUserSn("userSn123");
        tableWithNullModelNo.setModelNo(null);
        
        when(factoryDataQueryService.queryDeviceManufactureBySn(serialNumber)).thenReturn(tableWithNullModelNo);
        
        DeviceModelMatchResult result3 = productExchangeCodeService.isDeviceOrRelatedDevicesInModelSet(serialNumber, modelNoSet);
        assertFalse(result3.isMatched());
        assertTrue(result3.getBindInfoList() != null && result3.getBindInfoList().isEmpty());
        
        // 4. 测试正常情况 - 设备型号匹配
        DeviceManufactureTableDO normalTable = new DeviceManufactureTableDO();
        normalTable.setRegisterModelNo("MODEL2");
        normalTable.setUserSn("userSn123");
        normalTable.setModelNo("ORIGINAL_MODEL");
        
        when(factoryDataQueryService.queryDeviceManufactureBySn(serialNumber)).thenReturn(normalTable);
        when(deviceUniqCodeService.findAllParentUniqCodesByUniqCode("userSn123")).thenReturn(Arrays.asList("userSn123"));
        List<DeviceBindInfoDO> emptyBindList = new ArrayList<>();
        when(deviceUniqCodeService.findDeviceBindInfoByParentUniqCode("userSn123")).thenReturn(emptyBindList);
        
        DeviceModelMatchResult result4 = productExchangeCodeService.isDeviceOrRelatedDevicesInModelSet(serialNumber, modelNoSet);
        assertTrue(result4.isMatched());
        assertEquals(emptyBindList, result4.getBindInfoList());
        
        // 5. 测试关联设备匹配的情况
        DeviceManufactureTableDO tableWithParent = new DeviceManufactureTableDO();
        tableWithParent.setRegisterModelNo("MODEL_NOT_MATCHED");
        tableWithParent.setUserSn("userSn456");
        tableWithParent.setModelNo("ORIGINAL_MODEL");
        
        when(factoryDataQueryService.queryDeviceManufactureBySn(serialNumber)).thenReturn(tableWithParent);
        when(deviceUniqCodeService.findAllParentUniqCodesByUniqCode("userSn456")).thenReturn(Arrays.asList("userSn456", "parentSn789"));
        when(deviceUniqCodeService.findDeviceBindInfoByParentUniqCode("parentSn789")).thenReturn(
            Arrays.asList(
                DeviceBindInfoDO.builder().uniqCode("child1").modelNo("MODEL_NOT_MATCHED").build(),
                DeviceBindInfoDO.builder().uniqCode("child2").modelNo("MODEL3").build()
            )
        );
        
        DeviceModelMatchResult result5 = productExchangeCodeService.isDeviceOrRelatedDevicesInModelSet(serialNumber, modelNoSet);
        assertTrue(result5.isMatched());
        assertEquals(2, result5.getBindInfoList().size());
    }
}

