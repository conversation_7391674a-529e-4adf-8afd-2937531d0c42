package com.addx.iotcamera.service.deviceplatform.alexa.safemo;

import com.addx.iotcamera.bean.app.alexa.result.DeviceEvent;
import com.addx.iotcamera.bean.app.alexa.result.DeviceInfoAndStatusDO;
import com.addx.iotcamera.publishers.alexa.AlexaClient;
import com.addx.iotcamera.service.deviceplatform.alexa.AlexaService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static com.addx.iotcamera.constants.AlexaSafemoDeviceEventType.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AlexaSafemoSyncServiceTest {

    @InjectMocks
    AlexaSafemoSyncService alexaSafemoSyncService;
    @Mock
    AlexaClient alexaClient;
    @Mock
    AlexaService alexaService;
    @Mock
    AlexaSafemoDeviceQueryService alexaSafemoDeviceQueryService;

    @Test
    public void syncDeviceToAlexa() {
        Integer adminUserId = 1;
        String cxSerialNumber = "1";
        Map<String, Object> amazonTokenAndEventGatewayMap = new HashMap<>();
        amazonTokenAndEventGatewayMap.put(AlexaService.KEY_ACCESS_TOKEN, "1");
        amazonTokenAndEventGatewayMap.put(AlexaService.KEY_EVENT_GATEWAY_ENDPOINT, "1");

        // 条件2
        // when(alexaSafemoDeviceQueryService.queryDeviceInfoAndStatus(adminUserId, cxSerialNumber)).thenReturn(null);
        // alexaSafemoSyncService.syncBindDeviceToAlexa(adminUserId, cxSerialNumber);

        // 条件3
        when(alexaService.getAmazonTokenAndEventGateway(String.valueOf(adminUserId))).thenReturn(amazonTokenAndEventGatewayMap);
        when(alexaSafemoDeviceQueryService.queryDeviceInfoAndStatus(adminUserId, cxSerialNumber)).thenReturn(new DeviceInfoAndStatusDO());
        // alexaSafemoSyncService.syncBindDeviceToAlexa(adminUserId, cxSerialNumber);
    }

    @Test
    public void syncDeviceUnBindToAlexa() {
        DeviceEvent deviceEvent = new DeviceEvent();
        Integer adminUserId = 1;
        Map<String, Object> amazonTokenAndEventGatewayMap = new HashMap<>();
        amazonTokenAndEventGatewayMap.put(AlexaService.KEY_ACCESS_TOKEN, "1");
        amazonTokenAndEventGatewayMap.put(AlexaService.KEY_EVENT_GATEWAY_ENDPOINT, "1");
        when(alexaService.getAmazonTokenAndEventGateway(String.valueOf(adminUserId))).thenReturn(amazonTokenAndEventGatewayMap);

        // 条件1
        alexaSafemoSyncService.syncDeviceEventAlexa(null);

        // 条件2
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件3
        deviceEvent.setAdminUserId(1);
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件4
        deviceEvent.setAdminUserId(1);
        deviceEvent.setCxSerialNumber("1");
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件5
        deviceEvent.setAdminUserId(1);
        deviceEvent.setCxSerialNumber("1");
        deviceEvent.setDeviceEvent("");
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件5.5
        when(alexaSafemoDeviceQueryService.queryDeviceInfoAndStatus(adminUserId, "1")).thenReturn(new DeviceInfoAndStatusDO());
        deviceEvent.setAdminUserId(1);
        deviceEvent.setCxSerialNumber("1");
        deviceEvent.setDeviceEvent(BIND);
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件5.6
        when(alexaService.getAmazonTokenAndEventGateway(anyString())).thenReturn(null);
        deviceEvent.setAdminUserId(1);
        deviceEvent.setCxSerialNumber("1");
        deviceEvent.setDeviceEvent(BIND);
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件6
        deviceEvent.setAdminUserId(1);
        deviceEvent.setCxSerialNumber("1");
        deviceEvent.setDeviceEvent(DEACTIVATE);
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件7
        deviceEvent.setDeviceEvent(UPDATE_NAME);
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件8
        deviceEvent.setDeviceEvent(ONLINE_OFFLINE);
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件8
        deviceEvent.setDeviceEvent(MOTION_DETECTED);
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件9
        deviceEvent.setDeviceEvent(BATTERY_LOW);
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

        // 条件10
        deviceEvent.setDeviceEvent("");
        alexaSafemoSyncService.syncDeviceEventAlexa(deviceEvent);

    }

}
