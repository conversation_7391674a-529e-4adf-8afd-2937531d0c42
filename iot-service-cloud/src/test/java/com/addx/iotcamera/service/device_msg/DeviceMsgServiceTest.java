package com.addx.iotcamera.service.device_msg;

import com.addx.iotcamera.bean.device_msg.DeviceMsgAck;
import com.addx.iotcamera.bean.device_msg.DeviceRetainedMsg;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.mqtt.MqttConsumer;
import com.addx.iotcamera.mqtt.enums.EOutputTopicType;
import com.addx.iotcamera.mqtt.enums.ERequestAction;
import com.addx.iotcamera.mqtt.handler.extend.RequestHandler;
import com.addx.iotcamera.publishers.vernemq.requests.DeviceDormancyPlanCmd;
import com.addx.iotcamera.publishers.vernemq.requests.SetRetainParamRequest;
import com.addx.iotcamera.service.device.DeviceDormancyPlanService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.apache.commons.codec.binary.Hex;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.crypto.Mac;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceMsgServiceTest {

    @InjectMocks
    private DeviceMsgService deviceMsgService;
    @Mock
    private MqttConsumer mqttConsumer;
    @Mock
    private RequestHandler requestHandler;
    @Mock
    private OpenApiConfigService openApiConfigService;
    @Mock
    private DeviceDormancyPlanService deviceDormancyPlanService;
    @Mock
    private DeviceMsgSrcManager deviceMsgSrcManager;

    private final String httpTokenSecret = "N38Ywi2YR4qddOqXBjDRhQ==";

    @Before
    public void before() {
        deviceMsgService.setHttpTokenSecret(httpTokenSecret);
    }

    @Test
    public void test_msgNameOf() {
        final EReportEvent reportEvent = EReportEvent.DETECT_PIR_RESULT;

        Assert.assertEquals("detectPirResult", reportEvent.getMsgName());
        Assert.assertEquals(reportEvent, EReportEvent.msgNameOf("detectPirResult"));
    }

    @Test
    public void test_validateSignature1() throws Exception {
        String serialNumber = "f98af5080147855b2fa336b3dc05e28b";
        Integer time = PhosUtils.getUTCStamp();
        String httpTokenSecret = "N38Ywi2YR4qddOqXBjDRhQ==";
        final String signature = OpenApiUtil.sign(serialNumber + time, httpTokenSecret);
        log.info("validateSignature signature:{}", signature);
        final String reqBody = new JSONObject()
                .fluentPut("serialNumber", serialNumber)
                .fluentPut("time", time)
                .fluentPut("signature", signature)
                .toJSONString();
        final Result<String> result = deviceMsgService.validateSignature(reqBody, Integer.MAX_VALUE);
        Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
        Assert.assertEquals(serialNumber, result.getData());
    }

    @Test
    public void test_validateSignature() throws Exception {
        final String serialNumber = OpenApiUtil.shortUUID();
        final long time = PhosUtils.getUTCStamp() - 100L;
        final String signature = OpenApiUtil.sign(serialNumber + time, httpTokenSecret);

        {
            final String reqBody = new JSONObject()
//                    .fluentPut("serialNumber", serialNumber)
                    .fluentPut("time", time)
                    .fluentPut("signature", signature)
                    .toJSONString();
            final Result<String> result = deviceMsgService.validateSignature(reqBody, Integer.MAX_VALUE);
            Assert.assertEquals(result.getMsg(), ResultCollection.ILLEGAL_SIGNATURE.getCode(), (int) result.getResult());
        }
        {
            final String reqBody = new JSONObject()
                    .fluentPut("serialNumber", serialNumber)
//                    .fluentPut("time", time)
                    .fluentPut("signature", signature)
                    .toJSONString();
            final Result<String> result = deviceMsgService.validateSignature(reqBody, Integer.MAX_VALUE);
            Assert.assertEquals(result.getMsg(), ResultCollection.ILLEGAL_SIGNATURE.getCode(), (int) result.getResult());
        }
        {
            final String reqBody = new JSONObject()
                    .fluentPut("serialNumber", serialNumber)
                    .fluentPut("time", time)
//                    .fluentPut("signature", signature)
                    .toJSONString();
            final Result<String> result = deviceMsgService.validateSignature(reqBody, Integer.MAX_VALUE);
            Assert.assertEquals(result.getMsg(), ResultCollection.ILLEGAL_SIGNATURE.getCode(), (int) result.getResult());
        }
        {
            final String reqBody = new JSONObject()
                    .fluentPut("serialNumber", serialNumber)
                    .fluentPut("time", time)
                    .fluentPut("signature", signature)
                    .toJSONString();
            final Result<String> result = deviceMsgService.validateSignature(reqBody, 90);
            Assert.assertEquals(result.getMsg(), ResultCollection.ILLEGAL_SIGNATURE.getCode(), (int) result.getResult());
        }
        {
            final String reqBody = new JSONObject()
                    .fluentPut("serialNumber", serialNumber)
                    .fluentPut("time", time)
                    .fluentPut("signature", signature + "_error")
                    .toJSONString();
            final Result<String> result = deviceMsgService.validateSignature(reqBody, 110);
            Assert.assertEquals(result.getMsg(), ResultCollection.ILLEGAL_SIGNATURE.getCode(), (int) result.getResult());
        }
        {
            final String reqBody = new JSONObject()
                    .fluentPut("serialNumber", serialNumber)
                    .fluentPut("time", time)
                    .fluentPut("signature", signature)
                    .toJSONString();
            final Result<String> result = deviceMsgService.validateSignature(reqBody, 110);
            Assert.assertEquals(result.getMsg(), ResultCollection.SUCCESS.getCode(), (int) result.getResult());
        }
    }

    //    @Test
    public void test_validateSignature3() {
        final String reqBody = "{\"serialNumber\":\"8e8f96d3c2bcfb93795f205d310228f0\",\"signature\":\"iJ070zZITMgOdEZryTwQYmaQ92w=\",\"time\":2272600167,\"name\":\"httpToken\",\"id\":4,\"value\":{}}";
        final Result<String> result = deviceMsgService.validateSignature(reqBody, 60 * 5);
        Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
    }

    @Test
    public void test_validateSignature2() throws Exception {
//        final String reqBody = "{\"serialNumber\":\"8e8f96d3c2bcfb93795f205d310228f0\",\"signature\":\"XkdHtLvrVNEfVc2wEJ0bf3QoYEs=\",\"time\":1897455622,\"name\":\"httpToken\",\"id\":4,\"value\":{}}";
        final String reqBody = "{\"serialNumber\":\"%s\",\"time\":%s,\"signature\":\"%s\",\"name\":\"httpToken\",\"id\":4,\"value\":{}}";

        final Integer time = PhosUtils.getUTCStamp();
        for (int i = 1; i < 1000; i += i) {
            final String sn = OpenApiUtil.shortUUID();
            final String signature = OpenApiUtil.sign(sn + (time + i), "N38Ywi2YR4qddOqXBjDRhQ==");
            System.out.println(String.format(reqBody, sn, time + i, signature));
        }
    }

    @Mock
    private FactoryDataQueryService factoryDataQueryService;

    @Test
    @SneakyThrows
    public void test_validateSignature4() {
        {
            deviceMsgService.setEnableIgnoreTime(false);
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp());
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp() - (5 * 60 - 10));
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp() + (5 * 60 - 10));
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp() - (5 * 60 + 10));
                Assert.assertEquals(result.getMsg(), new Integer(-401), result.getResult());
            }
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp() + (5 * 60 + 10));
                Assert.assertEquals(result.getMsg(), new Integer(-401), result.getResult());
            }
        }
        {
            deviceMsgService.setEnableIgnoreTime(true);
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp());
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp() - (5 * 60 - 10));
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp() + (5 * 60 - 10));
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp() - (5 * 60 + 10));
                Assert.assertEquals(result.getMsg(), new Integer(-401), result.getResult());
            }
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp() + (5 * 60 + 10));
                Assert.assertEquals(result.getMsg(), new Integer(-401), result.getResult());
            }
            {
                Result<String> result = test_validateSignature("CG621", PhosUtils.getUTCStamp() + (5 * 60 + 10));
                Assert.assertEquals(result.getMsg(), new Integer(-401), result.getResult());
            }
        }
        {
            deviceMsgService.setEnableIgnoreTime(true);
            deviceMsgService.setIgnoreTimeModelNos("BX150,BX160");
            {
                Result<String> result = test_validateSignature("BX150", PhosUtils.getUTCStamp());
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            {
                Result<String> result = test_validateSignature("BX150", PhosUtils.getUTCStamp() - (5 * 60 - 10));
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            {
                Result<String> result = test_validateSignature("BX150", PhosUtils.getUTCStamp() + (5 * 60 - 10));
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            {
                Result<String> result = test_validateSignature("BX150", PhosUtils.getUTCStamp() - (5 * 60 + 10));
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
            {
                Result<String> result = test_validateSignature("BX150", PhosUtils.getUTCStamp() + (5 * 60 + 10));
                Assert.assertEquals(result.getMsg(), Result.successFlag, result.getResult());
            }
        }
    }

    @SneakyThrows
    public Result<String> test_validateSignature(String modelNo, long time) {
        String sn = OpenApiUtil.shortUUID();
        when(factoryDataQueryService.queryModelNoBySn(sn)).thenReturn(modelNo);
        final String signature = OpenApiUtil.sign(sn + time, httpTokenSecret);
        final String reqBody = "{\"serialNumber\":\"" + sn + "\",\"signature\":\"" + signature + "\",\"time\":" + time + ",\"name\":\"httpToken\",\"id\":999,\"value\":{}}";
        final Result<String> result = deviceMsgService.validateSignature(reqBody, 60 * 5);
        return result;
    }

    @Test
    public void test() throws Exception {
        final String secret = "N38Ywi2YR4qddOqXBjDRhQ==";
        final byte[] secretBytes = Base64.getDecoder().decode(secret);
        final String secretHex = Hex.encodeHexString(secretBytes);
        log.info("secretHex:{}", secretHex);

        final Integer time = PhosUtils.getUTCStamp();
        log.info("time:{}", time);

        final String content = "f98af5080147855b2fa336b3dc05e28b" + time;
        final byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
        final String contentHex = Hex.encodeHexString(contentBytes);
        log.info("contentHex:{}", contentHex);

        final Mac mac = OpenApiUtil.createMacForHmacSHA1(secret);
        final byte[] signatureBytes = mac.doFinal(contentBytes);
        final String signatureHex = Hex.encodeHexString(signatureBytes);
        log.info("signatureHex:{}", signatureHex);

        final String signatureBase64 = Base64.getEncoder().encodeToString(signatureBytes);
        log.info("signatureBase64:{}", signatureBase64);

        final String signature = OpenApiUtil.sign(content, secret);
        log.info("signature:{}", signature);

        // hwgTvbW7ukcUpssMxsYTw61689843736
    }

    @Test
    public void test_statusByJson() {
        final Result<Object> result = deviceMsgService.statusByJson("sn", "{}");
        Assert.assertEquals(result.getMsg(), ResultCollection.SUCCESS.getCode(), (int) result.getResult());
    }

    @Test
    public void test_connectionByJson() {
        final Result<Object> result = deviceMsgService.connectionByJson("sn", "{}");
        Assert.assertEquals(result.getMsg(), ResultCollection.SUCCESS.getCode(), (int) result.getResult());
    }

    @Test
    public void test_requestByJson() {
        final String sn = OpenApiUtil.shortUUID();
        final JSONObject payload = new JSONObject();
        {
            final Result<Object> result = deviceMsgService.requestByJson("msgName", sn, payload.toJSONString());
            Assert.assertEquals(result.getMsg(), ResultCollection.INVALID_PARAMS.getCode(), (int) result.getResult());
        }
        {
            final Result<Object> result = deviceMsgService.requestByJson(ERequestAction.wowza.getValue(), sn, payload.toJSONString());
            Assert.assertEquals(result.getMsg(), ResultCollection.INVALID_PARAMS.getCode(), (int) result.getResult());
        }
        payload.fluentPut("id", OpenApiUtil.shortUUID());
        for (final ERequestAction requestAction : ERequestAction.values()) {
            final Result<Object> result = deviceMsgService.requestByJson(requestAction.getValue(), sn, payload.toJSONString());
            Assert.assertEquals(result.getMsg(), 0, (int) result.getResult());
        }
        for (final EReportEvent reportEvent : EReportEvent.values()) {
            final Result<Object> result = deviceMsgService.requestByJson(reportEvent.getMsgName(), sn, payload.toJSONString());
            Assert.assertEquals(result.getMsg(), ResultCollection.SUCCESS.getCode(), (int) result.getResult());
        }

    }

    @Test
    public void test_onCmdAck() {
        deviceMsgService.onCmdAck(new DeviceMsgAck());
    }

    @Test
    public void test_onRetainedMsgAck() {
        deviceMsgService.onRetainedMsgAck(new DeviceMsgAck().setName(EOutputTopicType.SETTING.getValue()));
        deviceMsgService.onRetainedMsgAck(new DeviceMsgAck().setName(EOutputTopicType.CONFIG.getValue()));
        deviceMsgService.onRetainedMsgAck(new DeviceMsgAck().setName(EOutputTopicType.DORMANCY_PLAN_SETTING.getValue()));
    }

    @Test
    public void test_queryRetainedMsgByJson() {
        final String sn = OpenApiUtil.shortUUID();

        when(openApiConfigService.buildDeviceConfigMessage(sn)).thenReturn(new Result<>(new JSONObject()));
        final Result<SetRetainParamRequest> setRetainParamRequestResult = new Result<>(new SetRetainParamRequest());
        when(openApiConfigService.buildDeviceSettingMessage(sn)).thenReturn(setRetainParamRequestResult);
        final DeviceDormancyPlanCmd deviceDormancyPlanCmd = new DeviceDormancyPlanCmd(sn, new LinkedHashMap<>());
        when(deviceDormancyPlanService.buildDeviceDormancyPlan(sn)).thenReturn(deviceDormancyPlanCmd);

        {
            final Result<List<DeviceRetainedMsg>> result = deviceMsgService.queryRetainedMsgByJson(sn, new JSONObject().fluentPut("names", Arrays.asList("setting")).toJSONString());
            Assert.assertEquals(ResultCollection.SUCCESS.getCode(), (int) result.getResult());
            Assert.assertNotEquals(Collections.emptyList(), result.getData());
            Assert.assertNotEquals(setRetainParamRequestResult, result.getData().get(0));
        }
        {
            final Result<List<DeviceRetainedMsg>> result = deviceMsgService.queryRetainedMsgByJson(sn, new JSONObject().fluentPut("names", Arrays.asList("dormancy_plan_setting")).toJSONString());
            Assert.assertEquals(ResultCollection.SUCCESS.getCode(), (int) result.getResult());
            Assert.assertNotEquals(Collections.emptyList(), result.getData());
            Assert.assertNotEquals(deviceDormancyPlanCmd, result.getData().get(0));
        }
        {
            final Result<List<DeviceRetainedMsg>> result = deviceMsgService.queryRetainedMsgByJson(sn, new JSONObject().fluentPut("names", Arrays.asList("error_key")).toJSONString());
            Assert.assertEquals(ResultCollection.SUCCESS.getCode(), (int) result.getResult());
            Assert.assertEquals(Collections.emptyList(), result.getData());
        }

    }

    @Test
    public void test_queryDeviceRetainedMsg() {
        final String sn = OpenApiUtil.shortUUID();

        final Result<JSONObject> deviceConfigMessage = new Result<>(new JSONObject());
        when(openApiConfigService.buildDeviceConfigMessage(sn)).thenReturn(deviceConfigMessage);
        final Result<SetRetainParamRequest> setRetainParamRequestResult = new Result<>(new SetRetainParamRequest());
        when(openApiConfigService.buildDeviceSettingMessage(sn)).thenReturn(setRetainParamRequestResult);
        final DeviceDormancyPlanCmd deviceDormancyPlanCmd = new DeviceDormancyPlanCmd(sn, new LinkedHashMap<>());
        when(deviceDormancyPlanService.buildDeviceDormancyPlan(sn)).thenReturn(deviceDormancyPlanCmd);

        Assert.assertNotNull(deviceMsgService.queryDeviceRetainedMsg("config", sn));
        Assert.assertNotNull(deviceMsgService.queryDeviceRetainedMsg("setting", sn));
        Assert.assertNotNull(deviceMsgService.queryDeviceRetainedMsg("dormancy_plan_setting", sn));

        final String sn2 = OpenApiUtil.shortUUID();
        when(openApiConfigService.buildDeviceConfigMessage(sn2)).thenReturn(new Result<>(null));
        Assert.assertNull(deviceMsgService.queryDeviceRetainedMsg("config", sn2));
    }

    @Test
    public void test_clearKissWsRetainedMsg() {
        {
            final String sn = OpenApiUtil.shortUUID();
            deviceMsgService.clearKissWsRetainedMsg(sn);
        }
    }

    @Test
    @SneakyThrows
    public void test_httpTokenByJson() {
        final String sn = OpenApiUtil.shortUUID();
        int time = PhosUtils.getUTCStamp();
        String signature = OpenApiUtil.sign(sn + time, httpTokenSecret);
        JSONObject root = new JSONObject().fluentPut("name", "httpToken").fluentPut("id", 1)
                .fluentPut("serialNumber", sn).fluentPut("time", time).fluentPut("signature", signature);
        String payload = root.toJSONString();
        {
            Result<Object> result = deviceMsgService.httpTokenByJson(null, payload);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        {
            Result<Object> result = deviceMsgService.httpTokenByJson(sn, payload);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
    }

}
