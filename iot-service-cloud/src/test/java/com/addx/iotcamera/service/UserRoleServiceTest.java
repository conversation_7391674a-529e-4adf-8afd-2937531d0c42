package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.AdminAndSettingDO;
import com.addx.iotcamera.bean.domain.ShareApprovalDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.dao.IShareDAO;
import com.addx.iotcamera.dao.IUserRoleDAO;
import com.addx.iotcamera.helper.BatchQueryIterator;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class UserRoleServiceTest {

    @InjectMocks
    private UserRoleService userRoleService;
    @Mock
    private IUserRoleDAO userRoleDAO;

    @Mock
    private IShareDAO shareDAO;

    @Mock
    private RedisService redisService;

    @SneakyThrows
//    @Test
    public void test_queryAllAdminUserRoleIterator() {
        final TestHelper testHelper = TestHelper.getInstanceByEnv("staging");
        final IUserRoleDAO userRoleDAO = testHelper.getMapper(IUserRoleDAO.class);
        Set<String> expectKeys = new LinkedHashSet<>();
        try (final Connection conn = testHelper.getDataSource().getConnection()) {
            final String sql = "select user_id,serial_number from camera.user_role where role_id=1;";
            try (final ResultSet rs = conn.prepareStatement(sql).executeQuery()) {
                while (rs.next()) {
                    expectKeys.add(rs.getInt(1) + "-" + rs.getString(2));
                }
                log.info("user_role count:{}", expectKeys.size());
            }
        }
//        when(this.userRoleDAO.queryAdminUserRoleByUserIdGtAndNum(any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(userRoleDAO));
        when(this.userRoleDAO.queryAdminUserRoleByUserIdGtAndNum(any(), any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(userRoleDAO));

        final List set20000 = getRepeatUserIdAndSn(expectKeys, 20000);
        final List set1000 = getRepeatUserIdAndSn(expectKeys, 1000);
        final List set500 = getRepeatUserIdAndSn(expectKeys, 500);
//        final List set100 = getRepeatUserIdAndSn(100);
        log.info("");
    }

    private List getRepeatUserIdAndSn(Set<String> expectKeys, int batchSize) {
        final HashSet<String> remainKeys = new LinkedHashSet<>(expectKeys);
        final Iterator<UserRoleDO> iterator = userRoleService.queryAllAdminUserRoleIterator("test", batchSize);
        Set<String> userIdAndSn = new LinkedHashSet<>();
        Map<String, Integer> repeatUserIdAndSn = new LinkedHashMap<>();
        int i = 0;
        while (iterator.hasNext()) {
            final UserRoleDO userRole = iterator.next();
            final String key = userRole.getUserId() + "-" + userRole.getSerialNumber();
            if (!userIdAndSn.add(key)) {
                repeatUserIdAndSn.put(key, repeatUserIdAndSn.getOrDefault(key, 1) + 1);
            }
            remainKeys.remove(key);
            i++;
        }
        log.info("getRepeatUserIdAndSn:{},{}", batchSize, userIdAndSn.size());
        return Arrays.asList(i, userIdAndSn, repeatUserIdAndSn, remainKeys);
    }

    @Test
    public void test_BatchQueryIterator() {
        List<String[]> list = new ArrayList<>(2000);
        outLoop:
        for (int i = 1; true; i *= 2) {
            final String key = OpenApiUtil.shortUUID();
            for (int j = 0; j < i; j++) {
                list.add(new String[]{key});
                if (list.size() >= 2000) break outLoop;
            }
        }
        Collections.sort(list, Comparator.comparing(it -> it[0]));
        assertEquals(list, createBatchQueryIterator(null, list, 1));
        assertEquals(list, createBatchQueryIterator(null, list, 9));
        assertEquals(list, createBatchQueryIterator(null, list, 99));
        assertEquals(list, createBatchQueryIterator(null, list, 999));
        assertEquals(list, createBatchQueryIterator(null, list, 9999));
        {
            assertEquals(list, createBatchQueryIterator("logPrefix", list, 999));
        }
        {
            final List<String[]> list2 = Arrays.asList(new String[]{"1"}, new String[]{"2"}, null, new String[]{"4"});
            final BatchQueryIterator<String, String[]> iterator = createBatchQueryIterator(null, list2, 999);
            while (iterator.hasNext()) iterator.next();
            Assert.assertEquals(BatchQueryIterator.ErrCode.NEXT_ITEM_IS_NULL, iterator.getErrCode());
        }
        {
            final List<String[]> list2 = Arrays.asList(new String[]{"1"}, new String[]{"2"}, new String[]{null}, new String[]{"4"});
            final BatchQueryIterator<String, String[]> iterator = createBatchQueryIterator(null, list2, 999);
            while (iterator.hasNext()) iterator.next();
            Assert.assertEquals(BatchQueryIterator.ErrCode.NEXT_KEY_IS_NULL, iterator.getErrCode());
        }
        {
            final List<String[]> list2 = Arrays.asList(new String[]{"1"}, new String[]{"2"}, new String[]{null}, new String[]{"4"});
            final BatchQueryIterator<String, String[]> iterator = createBatchQueryIterator(null, list2, 999);
            while (iterator.hasNext()) iterator.next();
            Assert.assertEquals(BatchQueryIterator.ErrCode.NEXT_KEY_IS_NULL, iterator.getErrCode());
        }
        {
            final List<String[]> list2 = Arrays.asList(new String[]{"1"}, new String[]{"4"}, new String[]{"2"});
            final BatchQueryIterator<String, String[]> iterator = createBatchQueryIterator(null, list2, 999);
            while (iterator.hasNext()) iterator.next();
            Assert.assertEquals(BatchQueryIterator.ErrCode.ORDER_ERR, iterator.getErrCode());
        }
        try { // 老方法有bug
            test_BatchQueryIteratorV0(list, 9);
            Assert.assertFalse(true);
        } catch (Throwable e) {
        }
    }

    private BatchQueryIterator<String, String[]> createBatchQueryIterator(String logPrefix, List<String[]> list, int batchSize1) {
        final BatchQueryIterator<String, String[]> iterator = new BatchQueryIterator<>(logPrefix, (key, offset, batchSize) -> {
            return list.stream().filter(it -> key == null || it[0].compareTo(key) >= 0)
                    .skip(offset).limit(batchSize).collect(Collectors.toList());
        }, it -> it[0], String::compareTo, batchSize1, null);
        return iterator;
    }

    private void assertEquals(List<String[]> list, BatchQueryIterator<String, String[]> iterator) {
        for (final String[] expectKey : list) {
            Assert.assertTrue(iterator.hasNext());
            Assert.assertArrayEquals(expectKey, iterator.next());
        }
        Assert.assertFalse(iterator.hasNext());
        Assert.assertEquals(BatchQueryIterator.ErrCode.NOT_ERR, iterator.getErrCode());
    }

    private void test_BatchQueryIteratorV0(List<String[]> list, int batchSize1) {
        final BatchQueryIteratorV0<String, String[]> iterator = new BatchQueryIteratorV0<String, String[]>(null, (key, batchSize) -> {
            return list.stream().filter(it -> key == null || it[0].compareTo(key) >= 0)
                    .limit(batchSize).collect(Collectors.toList());
        }, it -> it[0], Arrays::equals, batchSize1, null);
        for (final String[] expectKey : list) {
            Assert.assertTrue(iterator.hasNext());
            Assert.assertArrayEquals(expectKey, iterator.next());
        }
        Assert.assertFalse(iterator.hasNext());
    }

    @Slf4j
    public static class BatchQueryIteratorV0<K, R> implements Iterator<R> {

        private final String logPrefix;
        private final BiFunction<K, Integer, List<R>> query;
        private final Function<R, K> keyGetter;
        private final BiPredicate<R, R> equals;
        private final Integer batchSize;
        private K key;

        public BatchQueryIteratorV0(String logPrefix, BiFunction<K, Integer, List<R>> query, Function<R, K> keyGetter
                , BiPredicate<R, R> equals, Integer batchSize, K startKey) {
            this.logPrefix = logPrefix;
            this.query = query;
            this.keyGetter = keyGetter;
            this.equals = equals;
            this.batchSize = batchSize;
            this.key = startKey;
        }

        private Long t1 = System.currentTimeMillis();
        private List<R> result = Collections.emptyList();
        private R next;
        private int i = 0;

        @Override
        public boolean hasNext() {
            if (result == null) return false;
            if (!result.isEmpty()) {
                next = result.remove(0);
                return true;
            }
            result = query.apply(key, batchSize);
            log.info("{} t1={},i={},size={}", logPrefix, t1, i++, result.size());
            if (result.isEmpty()) {
                result = null;
                return false;
            }
            R last = result.get(result.size() - 1);
            // 比较最后一个元素是否相等，作为结束循环的条件
            if (next != null && equals.test(last, next)) {
                result = null;
                return false;
            }
            key = keyGetter.apply(last);
            next = result.remove(0);
            return true;
        }

        @Override
        public R next() {
            return next;
        }
    }

//    @Test
    public void test_queryAdminAndSettingByUserIdGtAndNum() {
        final TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        final IUserRoleDAO userRoleDAO = testHelper.getMapper(IUserRoleDAO.class);
//        when(this.userRoleDAO.queryAdminUserRoleByUserIdGtAndNum(any(), any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(userRoleDAO));
        when(this.userRoleDAO.queryAdminAndSettingByUserIdGtAndNum(any(), any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(userRoleDAO));

        final Iterator<AdminAndSettingDO> iterator = userRoleService.queryAllAdminAndSettingIterator("", 1000);
        Assert.assertTrue(iterator.hasNext());
        final AdminAndSettingDO item = iterator.next();
        Assert.assertNotNull(item);
    }

    @Test
    @DisplayName("解除分享清除缓存")
    public void test_cleanShareUserRole(){
        when(shareDAO.undoShareSelf(any())).thenReturn(1);
        doReturn(true).when(redisService).delete(anyString());

        ShareApprovalDO approvalDO = new ShareApprovalDO();
        approvalDO.setTargetId(1);
        approvalDO.setSerialNumber("sn");

        Integer exceptedResult = 1;
        Integer actualResult = userRoleService.cleanShareUserRole(approvalDO);
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    public void test_queryUserRolesBySn() {
        UserRoleService.UserRoles userRoles = userRoleService.queryUserRolesBySn(anyString(), true);
        Assert.assertNotNull(userRoles);
        userRoles = userRoleService.queryUserRolesBySn(anyString(), false);
        Assert.assertNotNull(userRoles);
        userRoles = userRoleService.queryUserRolesBySn(anyString());
        Assert.assertNotNull(userRoles);

    }

}
