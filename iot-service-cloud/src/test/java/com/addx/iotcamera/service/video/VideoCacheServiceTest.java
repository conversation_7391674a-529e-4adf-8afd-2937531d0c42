package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.db.ActivityZoneDO;
import com.addx.iotcamera.bean.db.DeviceLibraryViewDO;
import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.library.ActivityZone;
import com.addx.iotcamera.bean.openapi.PaasTenantInfo;
import com.addx.iotcamera.bean.openapi.SaasAITaskIM;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.bean.tuple.Tuples;
import com.addx.iotcamera.bean.video.StoreBucket;
import com.addx.iotcamera.config.PaasTenantConfigTest;
import com.addx.iotcamera.config.VideoSliceConfig;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.dao.ActivityZoneDAO;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.enums.PirServiceName;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.addx.iotcamera.constants.VideoConstants.*;
import static com.addx.iotcamera.service.video.DeviceCache.VIDEO_COMMON_CACHE_EXPIRATION_MILLIS;
import static com.addx.iotcamera.service.video.VideoCache.Flag.INIT_SUCCESS;
import static com.addx.iotcamera.service.video.VideoCache.Flag.RESUME_SUCCESS;
import static com.addx.iotcamera.service.video.VideoGenerateServiceTest.createMockSliceList;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoCacheServiceTest {

    @InjectMocks
    private VideoCacheService videoCacheService;

    @Mock
    private VideoStoreService videoStoreService;
    @Mock
    private VipService vipService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private AIService aiService;
    @Mock
    private UserTierDeviceService userTierDeviceService;
    @Mock
    private DeviceConfigService deviceConfigService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private UserService userService;
    @Mock
    private VideoSliceConfig videoSliceConfig;
    @Mock
    private DeviceService deviceService;
    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private VideoAIService videoAIService;
    @Mock
    private RedisService redisService;
    @Mock
    private VideoEventRedisService videoEventRedisService;
    @Mock
    private MessageNotificationSettingsService messageNotificationSettingsService;
    @Mock
    private PaasTenantConfig paasTenantConfig;
    @Mock
    private UserSettingService userSettingService;
    @Mock
    private ReIdService reIdService;
    @Mock
    private PushInfoService pushInfoService;
    @Mock
    private ActivityZoneDAO activityZoneDAO;
    @Mock
    private TimeTicker timeTicker;
    @Mock
    private StorageAllocateService storageAllocateService;
    @Mock
    private DeviceAiSettingsService deviceAiSettingsService;

    @Before
    public void init() {
        when(deviceConfigService.getVideoRollingDays(anyString(), any(), anyString())).thenReturn(new Tuple2<>(30, 123456L));
        when(paasTenantConfig.getPaasTenantInfo(anyString())).thenReturn(new PaasTenantInfo().setIsThirdTenant(true).setBirdDetectSwitchOn(true));
        PaasTenantConfigTest.mockPaasTenantConfig(this.paasTenantConfig);
        when(deviceConfigService.getVideoRollingDaysOrNoPlan(any(), any(), any()))
                .thenReturn(Tuples.createTuple(30, System.currentTimeMillis() / 1000 + 365 * 24 * 60 * 60));
        when(vipService.isDeviceNoPlan(any(), any())).thenReturn(false);
    }

    @After
    public void after() {

    }

    public static VideoCache createMockVideoCache(String sn, String traceId) {
        Integer userId = (new Random()).nextInt(1000_0000);
        VideoCache video = new VideoCache(sn, traceId, 0);
        video.setAdminId(userId).setTenantId("guard").setHasAiAbility(true);
        video.setIsNotify(true);
        video.setIsMergeMessage(true);
        VideoCommonCache.UserSimple user = new VideoCommonCache.UserSimple();
        user.setLanguage("zh");
        user.setId(userId);
        user.setTenantId("guard");
        video.setUsers(Arrays.asList(user));
        video.setAdminId(userId);
        video.setRollingDays(7);
        String defaultSaasAITask = "{\"activityZoneList\":[],\"context\":{\"vipCountType\":16},\"deviceSn\":\"ddab6b8341458650d384b4c43362f7ff\",\"idBox\":{\"colors\":[],\"visualizeRecognition\":[\"PACKAGE\",\"PERSON\",\"PET\",\"VEHICLE\"]},\"images\":[],\"inputType\":0,\"isLast\":1,\"order\":4,\"outParams\":\"{\\\"outEncodeType\\\":0}\",\"outStorage\":{\"bucket\":\"a4x-staging-us-vip-pro\",\"clientRegion\":\"us-east-1\",\"expiredConfig\":{\"eventCoverImage\":5184000,\"eventSummary\":86400,\"objectImage\":86400},\"expiredSeconds\":1800,\"keyPrefix\":\"ai-saas-out-storage\",\"serviceName\":\"S3\"},\"outputTopic\":\"staging-us-video-generate\",\"outputType\":1,\"ownerId\":\"390\",\"recognitionObjects\":[{\"category\":\"PACKAGE\",\"functions\":[\"EVENT\"]},{\"category\":\"PERSON\",\"functions\":[\"RECOGNITION\",\"EVENT\"]},{\"category\":\"PET\",\"functions\":[\"RECOGNITION\",\"EVENT\"]},{\"category\":\"VEHICLE\",\"functions\":[\"EVENT\",\"ID\"]}],\"sliceTotalNum\":-1,\"taskId\":\"kAAyNDCxU6VjjDM0HN1Rt2\",\"taskSendTime\":1667546573,\"tenantId\":\"dzees\",\"timeout\":-1,\"traceId\":\"03901667546554HSmMQpMp7sDkB6F\",\"videoUrl\":\"https://a4x-staging-us-vip-pro.s3.amazonaws.com/device_video_slice/ddab6b8341458650d384b4c43362f7ff/03901667546554HSmMQpMp7sDkB6F/slice_2333_4_1.ts?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20221104T072253Z&X-Amz-SignedHeaders=host&X-Amz-Expires=172800&X-Amz-Credential=AKIAQBFG53LBEX2DCL6R%2F20221104%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=4ad58570780a451df350a4636b5300ec64033355d9d910230b810822acababa7\"}";
        video.setDefaultSaasAITask(JSON.parseObject(defaultSaasAITask, SaasAITaskIM.class));
        video.getDefaultSaasAITask().setActivityZoneList(Arrays.asList(
                new ActivityZone() {{
                    setId(101);
                }}, new ActivityZone() {{
                    setId(102);
                }}
        ));
        video.setNotifyEventObjects(new HashSet<>(Arrays.asList(
                AiObjectEnum.PERSON.getObjectName(),
                AiObjectEnum.VEHICLE.getObjectName()
        )));
        video.setNotifyEventTypes(new HashSet<>(Arrays.asList(
                AiObjectActionEnum.EXIST.getEventTypeName(),
                AiObjectActionEnum.VEHICLE_ENTER.getEventTypeName()
        )));
        return video;
    }

    @Test
    public void test_initVideoCache() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
        VideoCommonCache commonCache = new VideoCommonCache();
        {
            video.setFlag(INIT_SUCCESS);
            Assert.assertEquals(true, videoCacheService.initVideoCache(video, commonCache));
        }
        video.clearFlag(INIT_SUCCESS);
//        {
//            when(aiService.getVideoEventKey(anyString(), anyString())).thenThrow(new RuntimeException(""));
//            Assert.assertEquals(false, videoCacheService.initVideoCache(video, commonCache));
//        }
        when(userTierDeviceService.getDeviceCurrentTierWithTierGroupId(any(), anyString())).thenReturn(null);
        when(aiService.getVideoEventKey(anyString(), anyString())).thenReturn("12345345");
        {
            when(timeTicker.readMillis()).thenReturn(0L);
            commonCache.setInitTime(0);
            Assert.assertEquals(true, videoCacheService.initVideoCache(video, commonCache));
        }
        when(userTierDeviceService.getDeviceCurrentTierWithTierGroupId(any(), anyString())).thenReturn(new Tuple2<>(100, 1));
        {
            when(timeTicker.readMillis()).thenReturn(0L);
            commonCache.setInitTime(0);
            Assert.assertEquals(true, videoCacheService.initVideoCache(video, commonCache));
        }
    }

    @Test
    public void test_initVideoCommonCache() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);

        video.setInitTime(0);
        {
            when(timeTicker.readMillis()).thenReturn(0L);
            Assert.assertEquals(true, videoCacheService.initVideoCommonCache(video));
        }
        when(timeTicker.readMillis()).thenReturn(VIDEO_COMMON_CACHE_EXPIRATION_MILLIS + 1);
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(null);
            Assert.assertEquals(false, videoCacheService.initVideoCommonCache(video));
        }
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(new DeviceDO() {{
            setDeviceName("camera1");
            setModelNo("modelNo1");
            setPushIgnored(false);
        }});
        {
            when(userRoleService.findAllUsersForDevice(sn)).thenReturn(Arrays.asList());
            Assert.assertEquals(false, videoCacheService.initVideoCommonCache(video));
        }
        Integer userId = (new Random()).nextInt(1000_0000);
        when(userRoleService.findAllUsersForDevice(sn)).thenReturn(Arrays.asList(userId));
        {
            when(userService.queryUserById(userId)).thenReturn(null);
            Assert.assertEquals(false, videoCacheService.initVideoCommonCache(video));
        }
        when(userService.queryUserById(userId)).thenReturn(new User() {{
            setId(userId);
            setTenantId("guard");
            setLanguage("zh");
        }});
        {
            when(deviceSettingService.getDeviceSettingsBySerialNumber(sn)).thenReturn(null);
            Assert.assertEquals(false, videoCacheService.initVideoCommonCache(video));
        }
        when(deviceSettingService.getDeviceSettingsBySerialNumber(sn)).thenReturn(new DeviceSettingsDO() {{
            setDoorbellPressNotifySwitch(true);
            setDoorbellPressNotifyType("phone");
        }});
        {
            Assert.assertEquals(true, videoCacheService.initVideoCommonCache(video));
        }
        {
            when(messageNotificationSettingsService.queryMessageNotificationSetting(sn, video.getAdminId())).thenReturn(new MessageNotificationSetting());
            Assert.assertEquals(true, videoCacheService.initVideoCommonCache(video));
        }
        {
            when(userTierDeviceService.getDeviceCurrentTierWithTierGroupId(video.getAdminId(), sn)).thenReturn(null);
            when(messageNotificationSettingsService.queryMessageNotificationSetting(sn, video.getAdminId())).thenReturn(new MessageNotificationSetting() {{
                setEnableOther(1);
                setEnableVehicleKnown(1);
                setEnableVehicleKnown(1);
            }});
            Assert.assertEquals(true, videoCacheService.initVideoCommonCache(video));
        }
        {
            when(userTierDeviceService.getDeviceCurrentTierWithTierGroupId(video.getAdminId(), sn)).thenReturn(new Tuple2<>(100, 1));

            when(messageNotificationSettingsService.queryMessageNotificationSetting(sn, video.getAdminId())).thenReturn(new MessageNotificationSetting() {{
                setEnableOther(0);
                setEnableVehicleKnown(0);
                setEnableVehicleKnown(0);
            }});
            Assert.assertEquals(true, videoCacheService.initVideoCommonCache(video));
        }
        {
            when(timeTicker.readMillis()).thenReturn(VIDEO_COMMON_CACHE_EXPIRATION_MILLIS + 15 * 1000);
            when(vipService.isBirdVipDevice(video.getAdminId(), video.getSerialNumber())).thenReturn(true);
            when(paasTenantConfig.getPaasTenantInfo(video.getTenantId())).thenReturn(new PaasTenantInfo().setIsThirdTenant(true)
                    .setBirdDetectSwitchOn(true));
            Assert.assertEquals(true, videoCacheService.initVideoCommonCache(video));
        }
        {
            when(timeTicker.readMillis()).thenReturn(VIDEO_COMMON_CACHE_EXPIRATION_MILLIS + 30 * 1000);
            when(vipService.isBirdVipDevice(video.getAdminId(), video.getSerialNumber())).thenReturn(true);
            when(paasTenantConfig.getPaasTenantInfo(video.getTenantId())).thenReturn(new PaasTenantInfo().setIsThirdTenant(true)
                    .setBirdDetectSwitchOn(false));
            Assert.assertEquals(true, videoCacheService.initVideoCommonCache(video));
        }
        {
            when(timeTicker.readMillis()).thenReturn(VIDEO_COMMON_CACHE_EXPIRATION_MILLIS + 45 * 1000);
            when(vipService.isBirdVipDevice(video.getAdminId(), video.getSerialNumber())).thenReturn(false);
            when(paasTenantConfig.getPaasTenantInfo(video.getTenantId())).thenReturn(new PaasTenantInfo().setIsThirdTenant(true)
                    .setBirdDetectSwitchOn(false));
            Assert.assertEquals(true, videoCacheService.initVideoCommonCache(video));
        }
        {
            when(timeTicker.readMillis()).thenReturn(VIDEO_COMMON_CACHE_EXPIRATION_MILLIS + 60 * 1000);
            when(vipService.isBirdVipDevice(video.getAdminId(), video.getSerialNumber())).thenReturn(false);
            when(paasTenantConfig.getPaasTenantInfo(video.getTenantId())).thenReturn(new PaasTenantInfo().setIsThirdTenant(true)
                    .setBirdDetectSwitchOn(true));
            Assert.assertEquals(true, videoCacheService.initVideoCommonCache(video));
        }
    }

    @Test
    public void test_resumeVideoData() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
        {
            video.setFlag(RESUME_SUCCESS);
            Assert.assertEquals(true, videoCacheService.resumeVideoData(video));
        }
        video.clearFlag(RESUME_SUCCESS);
        {
            when(videoStoreService.selectLibraryViewByAdminIdAndTraceId(1, traceId)).thenReturn(null);
            Assert.assertEquals(true, videoCacheService.resumeVideoData(video));
        }
        video.clearFlag(RESUME_SUCCESS);
        when(videoStoreService.selectLibraryViewByAdminIdAndTraceId(1, traceId)).thenReturn(new DeviceLibraryViewDO() {{
            setEventInfo("[]");
            setDoorbellEventInfo("[]");
            setDeviceCallEventTag("");
        }});
        {
            video.setReceiveAllSlice(true);
            Assert.assertEquals(true, videoCacheService.resumeVideoData(video));
        }
        when(videoStoreService.selectLibraryViewByAdminIdAndTraceId(1, traceId)).thenReturn(new DeviceLibraryViewDO() {{
            setEventInfo("[{\"activatedZones\":[],\"eventObject\":\"BIRD\",\"eventType\":\"EXIST\",\"labelId\":\"\",\"possibleSubcategory\":[],\"summaryUrl\":\"https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667541194bfak1qBSlwt_bird_gallery.jpg\"},{\"activatedZones\":[],\"eventObject\":\"PERSON\",\"eventType\":\"EXIST\",\"labelId\":\"\",\"possibleSubcategory\":[]}]");
            setDoorbellEventInfo("[{\"event\":18},{\"event\":19}]");
            setDeviceCallEventTag("DEVICE_CALL");
        }});
        {
            video.setReceiveAllSlice(false);
            when(videoStoreService.querySliceByAdminUserIdAndTraceId(1, traceId)).thenReturn(createMockSliceList(video, 5));
            Assert.assertEquals(true, videoCacheService.resumeVideoData(video));
        }
        video.setReceiveAllSlice(false);
        video.clearFlag(RESUME_SUCCESS);
        when(videoStoreService.selectLibraryViewByAdminIdAndTraceId(anyInt(), anyString())).thenReturn(new DeviceLibraryViewDO() {{
            setEventInfo("[]");
            setDoorbellEventInfo("[]");
            setDeviceCallEventTag("");
        }});
        videoCacheService.resumeVideoData(video);
    }

    @Test
    public void test_getActivityZoneNames() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
        {
            List<String> list = videoCacheService.getActivityZoneNames(video, null);
            Assert.assertEquals(Arrays.asList(), list);
        }
        List<AiEvent> events = Arrays.asList(new AiEvent().setActivatedZones(null)
                , new AiEvent().setActivatedZones(Arrays.asList(123, null))
                , new AiEvent().setActivatedZones(Arrays.asList(456, 789))
        );
        when(activityZoneDAO.getActivityZonesById(123)).thenReturn(new ActivityZoneDO() {{
            setZoneName("zone_" + 123);
        }});
        when(activityZoneDAO.getActivityZonesById(456)).thenReturn(null);
        when(activityZoneDAO.getActivityZonesById(789)).thenReturn(new ActivityZoneDO() {{
            setZoneName("zone_" + 789);
        }});
        {
            List<String> list = videoCacheService.getActivityZoneNames(video, events);
            Assert.assertEquals(Arrays.asList("zone_123", "zone_789"), list);
        }
    }

    @Test
    public void test_VideoCache() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
        {
            video.setKnownLabelIds(new HashSet<>(Arrays.asList("label1")));
            video.setEnableLabelIdKnown(false);
            video.setEnableLabelIdUnknown(true);
            Assert.assertEquals(false, video.getIsNotifyLabelId("label1"));
            Assert.assertEquals(true, video.getIsNotifyLabelId("label2"));
        }
        {
            List<VideoSliceDO> sliceList = createMockSliceList(video, 4);
            sliceList.get(0).setVideoUrl("");
            video.addSlice(sliceList.get(0));
//            Assert.assertNull(video.getBucket());

            sliceList.get(1).setVideoUrl("https://gcs/slice.ts");
            video.addSlice(sliceList.get(1));
//            Assert.assertNull(video.getBucket());

            sliceList.get(2).setVideoUrl("https://a4x-staging-us-vip-plus.s3.amazonaws.com/device_video_slice/slice.ts");
            video.addSlice(sliceList.get(2));
//            Assert.assertEquals("a4x-staging-us-vip-plus", video.getBucket());

            video.addSlice(sliceList.get(3));
//            Assert.assertEquals("a4x-staging-us-vip-plus", video.getBucket());
        }
        {
            video.setActivatedZoneIds(new LinkedHashSet<>(Arrays.asList()));
            Assert.assertEquals("", video.getActivityZoneId());
            video.setActivatedZoneIds(new LinkedHashSet<>(Arrays.asList(11)));
            Assert.assertEquals(",11,", video.getActivityZoneId());
            video.setActivatedZoneIds(new LinkedHashSet<>(Arrays.asList(11, 22)));
            Assert.assertEquals(",11,22,", video.getActivityZoneId());
        }
        {
            Assert.assertTrue(video.isMatchAnyActivityZone(Arrays.asList()));
            SaasAITaskIM aiTask = new SaasAITaskIM();
            video.setDefaultSaasAITask(aiTask);
            aiTask.setActivityZoneList(Arrays.asList());
            Assert.assertTrue(video.isMatchAnyActivityZone(Arrays.asList(new AiEvent())));
            aiTask.setActivityZoneList(Arrays.asList(new ActivityZone() {{
                setId(333);
            }}));
            Assert.assertTrue(video.isMatchAnyActivityZone(Arrays.asList(new AiEvent().setActivatedZones(Arrays.asList(333)))));
        }
        {
            video.addEvent(null);
            video.addEvent(new AiEvent());
            video.addActivatedZoneId(null);
            video.addActivatedZoneId(Arrays.asList());
        }
    }

    @Test
    public void test_getPushInfo() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);

        String videoEvent = (System.currentTimeMillis() / 1000) + "";
        when(aiService.getVideoEventKey(sn, traceId)).thenReturn(videoEvent);
        Assert.assertEquals(videoEvent, videoCacheService.getVideoEvent(video));

        String videoEvent2 = (System.currentTimeMillis() / 1000) + "";
        when(aiService.getVideoEventKey(sn, traceId)).thenReturn(videoEvent2);
        Assert.assertEquals(videoEvent, videoCacheService.getVideoEvent(video));
    }

    @Test
    public void test_setImageUrlFromDevice() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
        Integer videoType = VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW.getCode();
        {
            video.setImageUrl(null);
            videoCacheService.setImageUrlFromDevice(video, null, videoType);
            Assert.assertEquals(null, video.getImageUrl());
        }
        {
            video.setImageUrl("123");
            videoCacheService.setImageUrlFromDevice(video, "123", videoType);
            Assert.assertEquals("123", video.getImageUrl());
        }
        {
            video.setImageUrl("123");
            videoCacheService.setImageUrlFromDevice(video, "456", videoType);
            Assert.assertEquals("123", video.getImageUrl());
        }
        {
            video.setImageUrl(null);
            videoCacheService.setImageUrlFromDevice(video, "456", videoType);
            Assert.assertEquals("456", video.getImageUrl());
        }
    }

    @Test
    public void test_setImageUrlFromAI() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
        {
            video.setImageUrl(null);
            videoCacheService.setImageUrlFromAI(video, null);
            Assert.assertEquals(null, video.getImageUrl());
        }
        {
            video.setImageUrl("123");
            videoCacheService.setImageUrlFromAI(video, "123");
            Assert.assertEquals("123", video.getImageUrl());
        }
        {
            video.setImageUrl("123");
            videoCacheService.setImageUrlFromAI(video, "456");
            Assert.assertEquals("456", video.getImageUrl());
        }
        {
            Integer videoType = VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW.getCode();
            video.setImageUrl(null);
            videoCacheService.setImageUrlFromDevice(video, "456", videoType);
            Assert.assertEquals("456", video.getImageUrl());
        }
    }

    @Test
    public void test_setImageUrlFromLibrary() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
        {
            video.setImageUrl(null);
            videoCacheService.setImageUrlFromLibrary(video, null);
            Assert.assertEquals(null, video.getImageUrl());
        }
        {
            video.setImageUrl("123");
            videoCacheService.setImageUrlFromLibrary(video, "123");
            Assert.assertEquals("123", video.getImageUrl());
        }
        {
            video.setImageUrl("123");
            videoCacheService.setImageUrlFromLibrary(video, "456");
            Assert.assertEquals("123", video.getImageUrl());
        }
        {
            video.setImageUrl(null);
            videoCacheService.setImageUrlFromLibrary(video, "456");
            Assert.assertEquals("456", video.getImageUrl());
        }
        {
            video.setServiceName(null);
            Assert.assertEquals(null, video.getServiceName());
            video.setServiceName("s3");
            Assert.assertEquals("s3", video.getServiceName());
            video.setServiceName("s3");
            Assert.assertEquals("s3", video.getServiceName());
            video.setServiceName("gcs");
            Assert.assertEquals("s3", video.getServiceName());
        }
    }

    @Test
    public void test_getIsNotify() {
        Assert.assertTrue(VideoCacheService.getIsNotify(false, false));
        Assert.assertFalse(VideoCacheService.getIsNotify(false, true));
        Assert.assertTrue(VideoCacheService.getIsNotify(true, false));
        Assert.assertTrue(VideoCacheService.getIsNotify(true, true));
    }

    @Test
    public void test_extractStoreBucket() {
        {
            final VideoCache video = new VideoCache("", "", 0L);
            String sliceUrl = "https://a4x-staging-eu.s3.eu-central-1.amazonaws.com/device_video_slice/8633a332f14a428c3f030aa886128209/055671629125540OKGy74S79K0zPf/slice_1999_0_0.ts";
            when(storageAllocateService.getStoreBucketFromUrl(sliceUrl)).thenReturn(null);
            videoCacheService.extractStoreBucket(video, sliceUrl);
            Assert.assertEquals(null, video.getStoreBucket());
        }
        {
            final VideoCache video = new VideoCache("", "", 0L);
            String sliceUrl = "https://a4x-staging-eu.s3.eu-central-1.amazonaws.com/device_video_slice/8633a332f14a428c3f030aa886128209/055671629125540OKGy74S79K0zPf/slice_1999_0_1.ts";
            final StoreBucket storeBucket = new StoreBucket();
            when(storageAllocateService.getStoreBucketFromUrl(sliceUrl)).thenReturn(storeBucket);
            videoCacheService.extractStoreBucket(video, sliceUrl);
            Assert.assertEquals(storeBucket, video.getStoreBucket());
        }
        {
            final VideoCache video = new VideoCache("", "", 0L);
            final StoreBucket storeBucket0 = new StoreBucket().setServiceName(PirServiceName.s3);
            video.setStoreBucket(storeBucket0);
            String sliceUrl = "https://a4x-staging-eu.s3.eu-central-1.amazonaws.com/device_video_slice/8633a332f14a428c3f030aa886128209/055671629125540OKGy74S79K0zPf/slice_1999_0_2.ts";
            final StoreBucket storeBucket = new StoreBucket();
            when(storageAllocateService.getStoreBucketFromUrl(sliceUrl)).thenReturn(storeBucket);
            videoCacheService.extractStoreBucket(video, sliceUrl);
            Assert.assertEquals(storeBucket0, video.getStoreBucket());
        }

    }

    @Test
    public void test_getRollingDaysFromSliceUrl() {
        Assert.assertEquals(Optional.of(3), VideoCache.getRollingDaysFromSliceUrl("https://a4x-prod-us-vip-3d.s3.amazonaws.com/ai-saas-out-storage/012360081720656242wFsLqL7AGBm_gallery.jpg"));
        Assert.assertEquals(Optional.of(60), VideoCache.getRollingDaysFromSliceUrl("https://objectstorage.us-sanjose-1.oraclecloud.com/n/idrw0j0fjn4v/b/staging-us-vip-60d-us-sanjose-1/o/device_video_slice/5c28fa2eeed3eae6e0ee1ece1427613e/010176761722944310SSDlMzGwJrV/image.jpg"));
        Assert.assertEquals(Optional.empty(), VideoCache.getRollingDaysFromSliceUrl("https://xyz.s3.amazonaws.com/ai-saas-out-storage/012360081720656242wFsLqL7AGBm_gallery.jpg"));
        Assert.assertEquals(Optional.empty(), VideoCache.getRollingDaysFromSliceUrl(""));
        Assert.assertEquals(Optional.empty(), VideoCache.getRollingDaysFromSliceUrl(null));
    }

    @Test
    public void test_videoInVideoEvent() {
        {
            Map<Object, Object> videoEventMap = new LinkedHashMap<>();
            videoEventMap.put(DEVICE_VIDEO_EVENT_START_TIME, 1000L);
            videoEventMap.put(DEVICE_VIDEO_EVENT_LAST_TIME, 2000L);

            Assert.assertEquals(AIService.VideoInVideoEvent.EXPIRED, AIService.videoInVideoEvent(videoEventMap, 0L));
            Assert.assertEquals(AIService.VideoInVideoEvent.EXPIRED, AIService.videoInVideoEvent(videoEventMap, 999L));

            Assert.assertEquals(AIService.VideoInVideoEvent.NOT_APPEND, AIService.videoInVideoEvent(videoEventMap, 1000L));
            Assert.assertEquals(AIService.VideoInVideoEvent.NOT_APPEND, AIService.videoInVideoEvent(videoEventMap, 2000L));

            Assert.assertEquals(AIService.VideoInVideoEvent.APPEND, AIService.videoInVideoEvent(videoEventMap, 2001L));
            Assert.assertEquals(AIService.VideoInVideoEvent.APPEND, AIService.videoInVideoEvent(videoEventMap, 2000L + DEVICE_VIDEO_EVENT_LAST_TIME_LIMIT));

            Assert.assertEquals(AIService.VideoInVideoEvent.NEW, AIService.videoInVideoEvent(videoEventMap, 2000L + DEVICE_VIDEO_EVENT_LAST_TIME_LIMIT + 1));
        }
        {
            Map<Object, Object> videoEventMap = new LinkedHashMap<>();
            videoEventMap.put(DEVICE_VIDEO_EVENT_START_TIME, 1000L);
            videoEventMap.put(DEVICE_VIDEO_EVENT_LAST_TIME, 1000L + DEVICE_VIDEO_EVENT_START_TIME_LIMIT);

            Assert.assertEquals(AIService.VideoInVideoEvent.EXPIRED, AIService.videoInVideoEvent(videoEventMap, 999L));
            Assert.assertEquals(AIService.VideoInVideoEvent.NOT_APPEND, AIService.videoInVideoEvent(videoEventMap, 1000L));
            Assert.assertEquals(AIService.VideoInVideoEvent.NOT_APPEND, AIService.videoInVideoEvent(videoEventMap, 1000L + DEVICE_VIDEO_EVENT_START_TIME_LIMIT - 1));
            Assert.assertEquals(AIService.VideoInVideoEvent.NOT_APPEND, AIService.videoInVideoEvent(videoEventMap, 1000L + DEVICE_VIDEO_EVENT_START_TIME_LIMIT));
            Assert.assertEquals(AIService.VideoInVideoEvent.NEW, AIService.videoInVideoEvent(videoEventMap, 1000L + DEVICE_VIDEO_EVENT_START_TIME_LIMIT + 1));
        }
    }

}
