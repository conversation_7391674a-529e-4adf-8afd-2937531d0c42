package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.device.KxDeviceSettingsDO;
import com.addx.iotcamera.bean.device_msg.*;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device_msg.IotLocalService;
import com.addx.iotcamera.service.device_msg.KissWsClient;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.service.device_msg.KxDeviceSettingService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.LinkedMap;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.Map;
import java.util.Optional;

import static com.addx.iotcamera.service.device_msg.IotLocalAndCloudProtocol.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class IotLocalServiceTest {

    @InjectMocks
    private IotLocalService iotLocalService;

    @Mock
    private KxDeviceSettingService kxDeviceSettingService;
    @Mock
    private DeviceModelConfigService deviceModelConfigService;
    @Mock
    private KissWsService kissSafeRTCWsService;
    @Mock
    private RedisService redisService;

    private Map<String, String> mockRedis = new LinkedMap();

    @Before
    public void init() {
        doAnswer(it -> {
            mockRedis.put(it.getArgument(0), it.getArgument(1));
            return null;
        }).when(redisService).set(anyString(), anyString(), anyInt());

        when(redisService.get(anyString())).thenAnswer(it -> {
            String redisValue = mockRedis.get(it.getArgument(0));
            return redisValue;
        });
    }

    @Test
    public void test_getSupportCloudDeviceConfig() {
        final JSONObject payload = JSON.parseObject("{\n" +
                "        \"recipientClientId\": \"iot-service\",\n" +
                "        \"method\": \"TRANSMIT\",\n" +
                "        \"messageType\": \"REPORT_CAPABILITY_SET\",\n" +
                "        \"messagePayload\": \"eyJic3RhdGlvbmQiOiJ7XCJzdGF0aW9uU3RhdHVzXCI6e1widmVyc2lvblwiOlwiMC4wLjUwMTMxLWRcIixcInN0YXRpb25TZXJpYWxOdW1iZXJcIjpcIjgwMTAxZGI5MjkwODIwMmNmZmMxOTNiNjkxNTUxNzVhXCIsXCJzdGF0aW9uTWFjXCI6XCJlMDowMTpjNzpjMDo2MTpmZVwiLFwiYXBwbGljYXRpb25zXCI6W3tcIm5hbWVcIjpcImlvdC1sb2NhbFwiLFwidmVyc2lvblwiOlwiMC4wLjUwXCIsXCJnaXRzaGFcIjpcIjEwYjFhMlwifSx7XCJuYW1lXCI6XCJzYWZlLXJ0Y1wiLFwidmVyc2lvblwiOlwiMC4wLjQxXCIsXCJnaXRzaGFcIjpcImI0NDQ5ZFwifSx7XCJuYW1lXCI6XCJic3RhdGlvbmRcIixcInZlcnNpb25cIjpcIjAuMC42MlwiLFwiZ2l0c2hhXCI6XCI3NmZmOGNcIn0se1wibmFtZVwiOlwiYWktc3RhdGlvblwiLFwidmVyc2lvblwiOlwiMC4xLjNcIixcImdpdHNoYVwiOlwiNGQ5YzEwXCJ9XX19IiwiaW90LWxvY2FsIjoie1wic3RhdGlvblN1cHBvcnRMaXN0XCI6W3tcIm5hbWVcIjpcIkFsZXhhXCIsXCJ2YWx1ZVwiOjEsXCJ2ZXJzaW9uXCI6MSxcImV4dFwiOlwie31cIn0se1wibmFtZVwiOlwic21hcnRBbGVydFwiLFwidmFsdWVcIjoxLFwidmVyc2lvblwiOjIsXCJleHRcIjpcInt9XCJ9LHtcIm5hbWVcIjpcIm1vdGlvblpvbmVcIixcInZhbHVlXCI6MSxcInZlcnNpb25cIjoyLFwiZXh0XCI6XCJ7fVwifSx7XCJuYW1lXCI6XCJTZWN1cml0eU1vZGVcIixcInZhbHVlXCI6MSxcInZlcnNpb25cIjoxLFwiZXh0XCI6XCJ7fVwifSx7XCJuYW1lXCI6XCJmYW1pbGlhckZhY2VcIixcInZhbHVlXCI6MSxcInZlcnNpb25cIjoxLFwiZXh0XCI6XCJ7fVwifSx7XCJuYW1lXCI6XCJjcm9zc0NhbWVyYVwiLFwidmFsdWVcIjoxLFwidmVyc2lvblwiOjEsXCJleHRcIjpcInt9XCJ9LHtcIm5hbWVcIjpcInN1cHBvcnRMaXN0VXNlckRldmljZXNWZXJzaW9uc1wiLFwidmFsdWVcIjpbMSwzXSxcInZlcnNpb25cIjoxfSx7XCJuYW1lXCI6XCJzdXBwb3J0Q2xvdWREZXZpY2VDb25maWdcIixcInZhbHVlXCI6dHJ1ZSxcInZlcnNpb25cIjoxfV19IiwicmVzdWx0IjowfQ==\",\n" +
                "        \"traceparent\": \"00-b264235fac473a38ae7fdac371ba04ac-3f2d13102ff09418-01\",\n" +
                "        \"senderClientId\": \"80101db92908202cffc193b69155175a\",\n" +
                "        \"version\": \"0.0.1\",\n" +
                "        \"timestamp\": 1727081446556\n" +
                "    }");
        final String bxSn = payload.getString("senderClientId");

        iotLocalService.onTransmit(null, payload);
        Assert.assertTrue(mockRedis.containsKey("bxReportCapabilitySet:" + bxSn));

        Optional<JSONObject> bxReportCapabilitySetOptional = iotLocalService.getBxReportCapabilitySet(bxSn);
        Assert.assertEquals(true, bxReportCapabilitySetOptional.isPresent());

        JSONObject bxReportCapabilitySet = JSON.parseObject(new String(Base64.getDecoder().decode(payload.getString("messagePayload")), StandardCharsets.UTF_8));
        Assert.assertEquals(bxReportCapabilitySet, bxReportCapabilitySetOptional.get());

        Map<String, JSONObject> stationSupportMap = iotLocalService.getStationSupportMap(bxSn);
        Assert.assertTrue(stationSupportMap.containsKey("supportCloudDeviceConfig"));

        Boolean supportCloudDeviceConfig = iotLocalService.getSupportCloudDeviceConfig(bxSn);
        Assert.assertEquals(true, supportCloudDeviceConfig);
    }

    @Test
    public void test_saveBxRejectCloudRequest() {
        RejectCloudRequest data = new RejectCloudRequest().setBxSn(OpenApiUtil.shortUUID())
                .setTimestamp(System.currentTimeMillis()).setRejectRequestId(OpenApiUtil.shortUUID());
        iotLocalService.saveBxRejectCloudRequest(data);
        {
            RejectCloudRequest data2 = iotLocalService.getBxRejectCloudRequest(data.getBxSn());
            Assert.assertEquals(data, data2);
        }
        {
            RejectCloudRequest data2 = iotLocalService.getBxRejectCloudRequest(null);
            Assert.assertEquals(null, data2);
        }
        {
            RejectCloudRequest data2 = iotLocalService.getBxRejectCloudRequest(OpenApiUtil.shortUUID());
            Assert.assertEquals(null, data2);
        }
    }

    @Test
    public void test_saveKxDeviceInfo() {
        ReportDeviceInfoToCloud data = new ReportDeviceInfoToCloud().setBxSn(OpenApiUtil.shortUUID())
                .setTimestamp(System.currentTimeMillis()).setCloudUserId(12345)
                .setDevices(Arrays.asList(new ReportDeviceInfoToCloud.DeviceInfo()
                        .setSn(OpenApiUtil.shortUUID())
                        .setModelNo("SS121")
                        .setBaseStation(0)
                        .setKissDeviceNode(new KissDeviceNode())
                        .setSupport(new ReportDeviceInfoToCloud.DeviceSupport())
                        .setSettings(new ReportDeviceInfoToCloud.DeviceSettings())
                        .setStatus(new ReportDeviceInfoToCloud.DeviceStatus())
                ));
        iotLocalService.saveKxDeviceInfo(data);
        {
            ReportDeviceInfoToCloud data2 = iotLocalService.getKxDeviceInfo(data.getBxSn());
            Assert.assertEquals(data, data2);
        }
        {
            ReportDeviceInfoToCloud data2 = iotLocalService.getKxDeviceInfo(null);
            Assert.assertEquals(null, data2);
        }
        {
            ReportDeviceInfoToCloud data2 = iotLocalService.getKxDeviceInfo(OpenApiUtil.shortUUID());
            Assert.assertEquals(null, data2);
        }
    }

    @Test
    public void test_saveBxReportCapabilitySet() {
        // saveBxReportCapabilitySet begin! bxSn=80101db92908202cffc193b69155175a,data={"result":0,"bstationd":"{\"stationStatus\":{\"version\":\"0.0.50164-d\",\"stationSerialNumber\":\"80101db92908202cffc193b69155175a\",\"stationMac\":\"e0:01:c7:c0:61:fe\",\"applications\":[{\"name\":\"iot-local\",\"version\":\"**********\",\"gitsha\":\"3b4ac5\"},{\"name\":\"safe-rtc\",\"version\":\"0.0.41\",\"gitsha\":\"0d09f9\"},{\"name\":\"bstationd\",\"version\":\"0.0.62\",\"gitsha\":\"e1c0fd\"},{\"name\":\"ai-station\",\"version\":\"0.1.3\",\"gitsha\":\"277987\"}]}}","iot-local":"{\"stationSupportList\":[{\"name\":\"Alexa\",\"value\":1,\"version\":1,\"ext\":\"{}\"},{\"name\":\"smartAlert\",\"value\":1,\"version\":2,\"ext\":\"{}\"},{\"name\":\"motionZone\",\"value\":1,\"version\":2,\"ext\":\"{}\"},{\"name\":\"SecurityMode\",\"value\":1,\"version\":1,\"ext\":\"{}\"},{\"name\":\"familiarFace\",\"value\":1,\"version\":1,\"ext\":\"{}\"},{\"name\":\"crossCamera\",\"value\":1,\"version\":1,\"ext\":\"{}\"},{\"name\":\"supportListUserDevicesVersions\",\"value\":[1,3],\"version\":1},{\"name\":\"supportCloudDeviceConfig\",\"value\":true,\"version\":1}]}"}
        String jsonStr = "{\n" +
                "\t\"recipientClientId\": \"iot-service\",\n" +
                "\t\"method\": \"TRANSMIT\",\n" +
                "\t\"messageType\": \"REPORT_CAPABILITY_SET\",\n" +
                "\t\"messagePayload\": \"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\n" +
                "\t\"senderClientId\": \"80101db92908202cffc193b69155175a\",\n" +
                "\t\"version\": \"0.0.1\",\n" +
                "\t\"senderChannelId\": \"0x5722b6a5:0242fafffeb1492b-00000007-00002cb2-c325ad2ef76423d8-5722b6a5\",\n" +
                "\t\"timestamp\": 1727341370038\n" +
                "}";
        JSONObject payload = JSON.parseObject(jsonStr);
        String bxSn = payload.getString(FIELD_SENDER_CLIENT_ID);
        JSONObject data = IotLocalService.extractMessagePayload(payload).get();
        iotLocalService.saveBxReportCapabilitySet(bxSn, data);
        {
            Optional<JSONObject> optional = iotLocalService.getBxReportCapabilitySet(bxSn);
            Assert.assertEquals(true, optional.isPresent());
            Assert.assertEquals(true, iotLocalService.getSupportCloudDeviceConfig(bxSn));
        }
        {
            Optional<JSONObject> optional = iotLocalService.getBxReportCapabilitySet(null);
            Assert.assertEquals(false, optional.isPresent());
            Assert.assertEquals(null, iotLocalService.getSupportCloudDeviceConfig(null));
        }
        {
            Optional<JSONObject> optional = iotLocalService.getBxReportCapabilitySet(OpenApiUtil.shortUUID());
            Assert.assertEquals(false, optional.isPresent());
            Assert.assertEquals(null, iotLocalService.getSupportCloudDeviceConfig(OpenApiUtil.shortUUID()));
        }

    }

    @Mock
    private KissWsClient kissWsClient;

    @Test
    public void test_onReqFromIotLocal() {
        {
            JSONObject payload = new JSONObject();
            iotLocalService.onReqFromIotLocal(kissWsClient, payload);
        }
        {
            JSONObject payload = new JSONObject()
                    .fluentPut(FIELD_MESSAGE_TYPE, ReqToIotLocalMessageType.query_device_config.name())
                    .fluentPut(FIELD_DATA, new QueryDeviceConfigFromCloud());
            iotLocalService.onReqFromIotLocal(kissWsClient, payload);
        }
        {
            test_onReqFromIotLocal_query_device_config(null, null, null);
            test_onReqFromIotLocal_query_device_config(5, null, 5);
            test_onReqFromIotLocal_query_device_config(5, 6, 5);
            test_onReqFromIotLocal_query_device_config(null, 6, 6);
        }
        {
            JSONObject payload = new JSONObject()
                    .fluentPut(FIELD_MESSAGE_TYPE, ReqToIotLocalMessageType.report_device_info.name())
                    .fluentPut(FIELD_DATA, new ReportDeviceInfoToCloud().setBxSn(OpenApiUtil.shortUUID()));
            iotLocalService.onReqFromIotLocal(kissWsClient, payload);
        }
        {
            JSONObject payload = new JSONObject()
                    .fluentPut(FIELD_MESSAGE_TYPE, ReqToIotLocalMessageType.reject_cloud_request.name())
                    .fluentPut(FIELD_DATA, new RejectCloudRequest().setBxSn(OpenApiUtil.shortUUID()));
            iotLocalService.onReqFromIotLocal(kissWsClient, payload);
        }
    }

    private void test_onReqFromIotLocal_query_device_config(Integer deviceValue, Integer modelValue, Integer expectValue) {
        String[] msgs = new String[1];
        doAnswer(it -> msgs[0] = it.getArgument(0)).when(kissWsClient).send(anyString());

        QueryDeviceConfigFromCloud.DeviceInfo cxDevice = new QueryDeviceConfigFromCloud.DeviceInfo()
                .setSn(OpenApiUtil.shortUUID()).setBaseStation(0)
                .setModelNo(OpenApiUtil.shortUUID());
        JSONObject payload = new JSONObject()
                .fluentPut(FIELD_MESSAGE_TYPE, ReqToIotLocalMessageType.query_device_config.name())
                .fluentPut(FIELD_DATA, new QueryDeviceConfigFromCloud().setDevices(Arrays.asList(cxDevice)));

        when(kxDeviceSettingService.queryBySn(cxDevice.getSn())).thenReturn(new KxDeviceSettingsDO()
                .setSn(cxDevice.getSn()).setWifiPowerLevel(deviceValue));
        when(deviceModelConfigService.queryRowDeviceModelByModelNo(cxDevice.getModelNo())).thenReturn(new DeviceModel() {{
            setModelNo(cxDevice.getModelNo());
            setWifiPowerLevel(modelValue);
        }});
        msgs[0] = null;
        iotLocalService.onReqFromIotLocal(kissWsClient, payload);
        Assert.assertNotNull(msgs[0]);
        JSONArray devices = JSON.parseObject(msgs[0]).getJSONObject(FIELD_DATA).getJSONArray("devices");
        Assert.assertEquals(1, devices.size());
        Integer wifiPowerLevel = devices.getJSONObject(0).getInteger("wifiPowerLevel");
        Assert.assertEquals(expectValue, wifiPowerLevel);
    }

    @Test
    public void test_triggerQueryDeviceConfig() {
        iotLocalService.triggerQueryDeviceConfig(OpenApiUtil.shortUUID());
    }

    @Test
    public void test_triggerReportDeviceInfo() {
        iotLocalService.triggerReportDeviceInfo(OpenApiUtil.shortUUID());
    }

    @Test
    public void test_queryKxCapabilitySet() {
        iotLocalService.queryKxCapabilitySet(OpenApiUtil.shortUUID());
    }

    @Test
    public void test_onTransmit() {
        {
            JSONObject payload = new JSONObject();
            iotLocalService.onTransmit(kissWsClient, payload);
        }
        {
            String bxSn = OpenApiUtil.shortUUID();
            JSONObject payload = new JSONObject().fluentPut(FIELD_RECIPIENT_CLIENT_ID, "XYZ")
                    .fluentPut(FIELD_RECIPIENT_CLIENT_ID, bxSn);
            iotLocalService.onTransmit(kissWsClient, payload);
        }
        {
            String bxSn = OpenApiUtil.shortUUID();
            JSONObject payload = new JSONObject().fluentPut(FIELD_RECIPIENT_CLIENT_ID, CLIENT_ID_IOT_SERVICE)
                    .fluentPut(FIELD_RECIPIENT_CLIENT_ID, bxSn)
                    .fluentPut(FIELD_MESSAGE_TYPE, "IJK");
            iotLocalService.onTransmit(kissWsClient, payload);
        }
        {
            String bxSn = OpenApiUtil.shortUUID();
            JSONObject payload = new JSONObject().fluentPut(FIELD_RECIPIENT_CLIENT_ID, CLIENT_ID_IOT_SERVICE)
                    .fluentPut(FIELD_RECIPIENT_CLIENT_ID, bxSn)
                    .fluentPut(FIELD_MESSAGE_TYPE, TransmitMessageType.REPORT_CAPABILITY_SET.name());
            String redisKey = IotLocalService.REDIS_KEY_PREFIX_BX_CAPABILITY_SET + bxSn;
            doThrow(new RuntimeException("mock")).when(redisService).set(eq(redisKey), any(), anyInt());
            iotLocalService.onTransmit(kissWsClient, payload);
        }
        String jsonStr = "{\n" +
                "        \"recipientClientId\": \"iot-service\",\n" +
                "        \"method\": \"TRANSMIT\",\n" +
                "        \"messageType\": \"REPORT_CAPABILITY_SET\",\n" +
                "        \"messagePayload\": \"eyJic3RhdGlvbmQiOiJ7XCJzdGF0aW9uU3RhdHVzXCI6e1widmVyc2lvblwiOlwiMC4wLjUwMTMxLWRcIixcInN0YXRpb25TZXJpYWxOdW1iZXJcIjpcIjgwMTAxZGI5MjkwODIwMmNmZmMxOTNiNjkxNTUxNzVhXCIsXCJzdGF0aW9uTWFjXCI6XCJlMDowMTpjNzpjMDo2MTpmZVwiLFwiYXBwbGljYXRpb25zXCI6W3tcIm5hbWVcIjpcImlvdC1sb2NhbFwiLFwidmVyc2lvblwiOlwiMC4wLjUwXCIsXCJnaXRzaGFcIjpcIjEwYjFhMlwifSx7XCJuYW1lXCI6XCJzYWZlLXJ0Y1wiLFwidmVyc2lvblwiOlwiMC4wLjQxXCIsXCJnaXRzaGFcIjpcImI0NDQ5ZFwifSx7XCJuYW1lXCI6XCJic3RhdGlvbmRcIixcInZlcnNpb25cIjpcIjAuMC42MlwiLFwiZ2l0c2hhXCI6XCI3NmZmOGNcIn0se1wibmFtZVwiOlwiYWktc3RhdGlvblwiLFwidmVyc2lvblwiOlwiMC4xLjNcIixcImdpdHNoYVwiOlwiNGQ5YzEwXCJ9XX19IiwiaW90LWxvY2FsIjoie1wic3RhdGlvblN1cHBvcnRMaXN0XCI6W3tcIm5hbWVcIjpcIkFsZXhhXCIsXCJ2YWx1ZVwiOjEsXCJ2ZXJzaW9uXCI6MSxcImV4dFwiOlwie31cIn0se1wibmFtZVwiOlwic21hcnRBbGVydFwiLFwidmFsdWVcIjoxLFwidmVyc2lvblwiOjIsXCJleHRcIjpcInt9XCJ9LHtcIm5hbWVcIjpcIm1vdGlvblpvbmVcIixcInZhbHVlXCI6MSxcInZlcnNpb25cIjoyLFwiZXh0XCI6XCJ7fVwifSx7XCJuYW1lXCI6XCJTZWN1cml0eU1vZGVcIixcInZhbHVlXCI6MSxcInZlcnNpb25cIjoxLFwiZXh0XCI6XCJ7fVwifSx7XCJuYW1lXCI6XCJmYW1pbGlhckZhY2VcIixcInZhbHVlXCI6MSxcInZlcnNpb25cIjoxLFwiZXh0XCI6XCJ7fVwifSx7XCJuYW1lXCI6XCJjcm9zc0NhbWVyYVwiLFwidmFsdWVcIjoxLFwidmVyc2lvblwiOjEsXCJleHRcIjpcInt9XCJ9LHtcIm5hbWVcIjpcInN1cHBvcnRMaXN0VXNlckRldmljZXNWZXJzaW9uc1wiLFwidmFsdWVcIjpbMSwzXSxcInZlcnNpb25cIjoxfSx7XCJuYW1lXCI6XCJzdXBwb3J0Q2xvdWREZXZpY2VDb25maWdcIixcInZhbHVlXCI6dHJ1ZSxcInZlcnNpb25cIjoxfV19IiwicmVzdWx0IjowfQ==\",\n" +
                "        \"traceparent\": \"00-b264235fac473a38ae7fdac371ba04ac-3f2d13102ff09418-01\",\n" +
                "        \"senderClientId\": \"80101db92908202cffc193b69155175a\",\n" +
                "        \"version\": \"0.0.1\",\n" +
                "        \"timestamp\": 1727081446556\n" +
                "    }";
        JSONObject payload = JSON.parseObject(jsonStr);
        iotLocalService.onTransmit(kissWsClient, payload);
    }


}
