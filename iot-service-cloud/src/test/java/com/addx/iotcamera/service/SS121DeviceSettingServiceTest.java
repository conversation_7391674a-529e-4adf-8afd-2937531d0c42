package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.dao.device.IDeviceSettingDAO;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class SS121DeviceSettingServiceTest {

    @InjectMocks
    private DeviceSettingService deviceSettingService;

    @Mock
    private IDeviceSettingDAO deviceSettingDAO;

    private TestHelper testHelper;
    private IDeviceSettingDAO deviceSettingDAOReal;

    @Before
    public void before() {
        testHelper = TestHelper.getInstanceByEnv("test");
        deviceSettingDAOReal = testHelper.getMapper(IDeviceSettingDAO.class);
        when(deviceSettingDAO.initDeviceSettings(any())).thenAnswer(AdditionalAnswers.delegatesTo(deviceSettingDAOReal));
        when(deviceSettingDAO.getDeviceSettingsBySerialNumber(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(deviceSettingDAOReal));
        when(deviceSettingDAO.updateDeviceSettings(any())).thenAnswer(AdditionalAnswers.delegatesTo(deviceSettingDAOReal));
    }

    @After
    public void after() {
        testHelper.commitAndClose();
    }

    @Test
    public void test() {
        /*
        "snapshotRecordingSwitch": true, // 是否打开快照录像
        "snapshotCaptureInterval": "3600s", // 快照录像拍摄间隔
        "eventRecordingDualViewSwitch": true, // 是否打开事件录像的双画面功能
         */
        String sn = OpenApiUtil.shortUUID();
        JSONObject jsonObj;
        DeviceSettingsDO deviceSettingsDO;
        {
            jsonObj = new JSONObject()
                    .fluentPut("snapshotRecordingSwitch", true)
                    .fluentPut("snapshotCaptureInterval", "3600s")
                    .fluentPut("eventRecordingDualViewSwitch", true);
            DeviceAppSettingsDO deviceAppSettingsDO = jsonObj.toJavaObject(DeviceAppSettingsDO.class);
            deviceAppSettingsDO.setSerialNumber(sn);
            deviceSettingsDO = DeviceSettingsDO.ParseFrom(deviceAppSettingsDO, null);
        }
//        DeviceAppSettingsDO deviceAppSettingsDO2 = DeviceAppSettingsDO.ParseFrom(deviceSettingsDO, new DeviceSupport());
        JSONObject settingJsonObj = (JSONObject) JSON.toJSON(deviceSettingsDO);
//        JSONObject appSettingJsonObj = (JSONObject) JSON.toJSON(deviceAppSettingsDO2);
        for (String key : jsonObj.keySet()) {
            Assert.assertEquals("settingJsonObj error! key=" + key, jsonObj.get(key), settingJsonObj.get(key));
//            Assert.assertEquals("appSettingJsonObj error! key=" + key, jsonObj.get(key), appSettingJsonObj.get(key));
        }

        Integer insertNum = deviceSettingService.setDeviceSettings(deviceSettingsDO);
        Assert.assertEquals(new Integer(1), insertNum);
        {
            DeviceSettingsDO deviceSettingsDO2 = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
            JSONObject settingJsonObj2 = (JSONObject) JSONObject.toJSON(deviceSettingsDO2);
            for (String key : jsonObj.keySet()) {
                Object v1 = jsonObj.get(key);
                Object v2 = settingJsonObj2.get(key);
                Assert.assertEquals(String.format("key=%s,v1=%s,v2=%s", key, v1, v2), v1, v2);
            }
        }

        Integer insertNum2 = deviceSettingService.setDeviceSettings(deviceSettingsDO);
        Assert.assertEquals(new Integer(1), insertNum2);
        {
            DeviceSettingsDO deviceSettingsDO2 = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
            JSONObject settingJsonObj2 = (JSONObject) JSONObject.toJSON(deviceSettingsDO2);
            for (String key : jsonObj.keySet()) {
                Object v1 = jsonObj.get(key);
                Object v2 = settingJsonObj2.get(key);
                Assert.assertEquals(String.format("key=%s,v1=%s,v2=%s", key, v1, v2), v1, v2);
            }
        }

        {
            jsonObj = new JSONObject()
                    .fluentPut("snapshotRecordingSwitch", false)
                    .fluentPut("snapshotCaptureInterval", "auto")
                    .fluentPut("eventRecordingDualViewSwitch", false);
            DeviceAppSettingsDO deviceAppSettingsDO = jsonObj.toJavaObject(DeviceAppSettingsDO.class);
            deviceAppSettingsDO.setSerialNumber(sn);
            deviceSettingsDO = DeviceSettingsDO.ParseFrom(deviceAppSettingsDO, null);
        }
        Integer insertNum3 = deviceSettingService.setDeviceSettings(deviceSettingsDO);
        Assert.assertEquals(new Integer(1), insertNum3);
        {
            DeviceSettingsDO deviceSettingsDO2 = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
            JSONObject settingJsonObj2 = (JSONObject) JSONObject.toJSON(deviceSettingsDO2);
            for (String key : jsonObj.keySet()) {
                Object v1 = jsonObj.get(key);
                Object v2 = settingJsonObj2.get(key);
                Assert.assertEquals(String.format("key=%s,v1=%s,v2=%s", key, v1, v2), v1, v2);
            }
        }
    }

    @Test
    public void test2() {
        String sn = OpenApiUtil.shortUUID();
        DeviceSettingsDO setting1 = new DeviceSettingsDO();
        setting1.setSerialNumber(sn);
        {
            deviceSettingService.setDeviceSettings(setting1);
            DeviceSettingsDO settings2 = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
            Assert.assertNull(settings2.getDefaultCodec());
        }
        {
            DeviceSettingsDO modify = new DeviceSettingsDO();
            modify.setSerialNumber(sn);
            {
                modify.setDefaultCodec("h264");
                deviceSettingService.updateDeviceSetting(modify);
                DeviceSettingsDO settings2 = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
                Assert.assertEquals("h264", settings2.getDefaultCodec());
            }
            {
                modify.setDefaultCodec("h265");
                deviceSettingService.updateDeviceSetting(modify);
                DeviceSettingsDO settings2 = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
                Assert.assertEquals("h265", settings2.getDefaultCodec());
            }
            {
                modify.setClearDefaultCodec(true);
                deviceSettingService.updateDeviceSetting(modify);
                DeviceSettingsDO settings2 = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
                Assert.assertNull(settings2.getDefaultCodec());
            }
        }
    }

}
