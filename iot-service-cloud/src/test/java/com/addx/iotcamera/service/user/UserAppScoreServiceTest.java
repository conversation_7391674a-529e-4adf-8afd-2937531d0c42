package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.app.user.UserAppScoreRequest;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.user.UserAppScoreDO;
import com.addx.iotcamera.bean.response.UserAppScoreResponse;
import com.addx.iotcamera.dao.device.IBindOperationDAO;
import com.addx.iotcamera.dao.user.IUserAppScoreDao;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.service.vip.OrderService;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.addx.iotcamera.enums.user.UserAppScoreMomentEnum.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UserAppScoreServiceTest {
    @InjectMocks
    private UserAppScoreService userAppScoreService;

    @Mock
    private IUserAppScoreDao iUserAppScoreDao;

    @Mock
    private IBindOperationDAO iBindOperationDAO;

    @Mock
    private UserLiveReportService userLiveReportService;

    @Mock
    private UserService userService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private UserVipService userVipService;

    @Mock
    private OrderService orderService;

    @Test
    @DisplayName("实时弹出-已有弹出记录,不需要再次弹出")
    public void test_userAppScoreMoment_hasRecord_realTime(){
        Integer userId = 1;

        when(iUserAppScoreDao.deleteNotEffectiveUserAppScore(userId)).thenReturn(1);
        when(iUserAppScoreDao.queryUserAppScoreDOExist(userId)).thenReturn(1);

        userAppScoreService.userAppScoreMoment(userId,DEVICE_SECOND_BIND);
        verify(iUserAppScoreDao, times(1)).deleteNotEffectiveUserAppScore(any());
        verify(iUserAppScoreDao, times(0)).insertUserAppScore(any());
    }


    @Test
    @DisplayName("非实时弹出-已有弹出记录,不需要再次弹出")
    public void test_userAppScoreMoment_hasRecord_notRealTime(){
        Integer userId = 1;
        when(iUserAppScoreDao.queryUserAppScoreDOExist(userId)).thenReturn(1);
        userAppScoreService.userAppScoreMoment(userId,DEVICE_SECOND_BIND);
        verify(iUserAppScoreDao, times(0)).insertUserAppScore(any());
    }


    @Test
    @DisplayName("第一次绑定设备")
    public void test_userAppScoreMoment_firstDeviceBind(){
        Integer userId = 1;
        when(iUserAppScoreDao.queryUserAppScoreDOExist(userId)).thenReturn(0);

        //首次绑定
        userAppScoreService.userAppScoreMoment(userId,DEVICE_FIRST_BIND);
        verify(iUserAppScoreDao, times(1)).insertUserAppScore(any());
    }

    @Test
    @DisplayName("账号注册后2月")
    public void test_userAppScoreMoment_register2month(){
        Integer userId = 1;
        when(iUserAppScoreDao.queryUserAppScoreDOExist(userId)).thenReturn(0);

        userAppScoreService.userAppScoreMoment(userId,USER_CONTINUOUSUSE);
        verify(iUserAppScoreDao, times(1)).insertUserAppScore(any());
    }


    @Test
    @DisplayName("第一次购买套餐")
    public void test_userAppScoreMoment_firstPayment(){
        Integer userId = 1;
        when(iUserAppScoreDao.queryUserAppScoreDOExist(userId)).thenReturn(0);

        userAppScoreService.userAppScoreMoment(userId,USER_FIRST_PAYMENT);
        verify(iUserAppScoreDao, times(1)).insertUserAppScore(any());
    }

    @Test
    @DisplayName("第2次绑定设备")
    public void test_userAppScoreMoment_secondDeviceBind(){
        Integer userId = 1;
        when(iUserAppScoreDao.queryUserAppScoreDOExist(userId)).thenReturn(0);

        userAppScoreService.userAppScoreMoment(userId,DEVICE_SECOND_BIND);
        verify(iUserAppScoreDao, times(1)).insertUserAppScore(any());
    }


    @Test
    @DisplayName("设备绑定次数过多")
    public void test_userAppScoreMomentDeviceBind_bindMany(){
        Integer userId = 1;
        String serialNumber = "serialNumber";

        when(iBindOperationDAO.queryBindDeviceHistory(userId)).thenReturn(2);
        when(iBindOperationDAO.currentDeviceBindTotal(userId,serialNumber)).thenReturn(2);
        userAppScoreService.userAppScoreMomentDeviceBind(userId,serialNumber);
        verify(iUserAppScoreDao, times(0)).insertUserAppScore(any());




        when(iBindOperationDAO.queryBindDeviceHistory(userId)).thenReturn(3);
        when(iBindOperationDAO.currentDeviceBindTotal(userId,serialNumber)).thenReturn(1);
        userAppScoreService.userAppScoreMomentDeviceBind(userId,serialNumber);

        verify(iUserAppScoreDao, times(0)).insertUserAppScore(any());
    }

    @Test
    @DisplayName("设备首次绑定")
    public void test_userAppScoreMomentDeviceBind_firstBind(){
        Integer userId = 1;
        String serialNumber = "serialNumber";

        when(iBindOperationDAO.queryBindDeviceHistory(userId)).thenReturn(1);
        when(iBindOperationDAO.currentDeviceBindTotal(userId,serialNumber)).thenReturn(1);

        when(iUserAppScoreDao.insertUserAppScore(any())).thenReturn(1);
        when(iUserAppScoreDao.queryUserAppScoreDOExist(userId)).thenReturn(0);
        when(iUserAppScoreDao.insertUserAppScore(any())).thenReturn(1);
        userAppScoreService.userAppScoreMomentDeviceBind(userId,serialNumber);


        verify(iUserAppScoreDao, times(1)).insertUserAppScore(any());
    }


    @Test
    @DisplayName("查询评分引导记录-无记录")
    public void test_queryUserAppScoreDO_noEffectiveRecord(){
        Integer userId = 1;
        when(iUserAppScoreDao.queryUserAppScoreDO(userId)).thenReturn(null);

        UserAppScoreResponse expectedResult = UserAppScoreResponse.builder()
                .id(0L)
                .eject(false)
                .moment(0)
                .build();
        UserAppScoreResponse actualResult = userAppScoreService.queryUserAppScoreDO(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("查询评分引导记录-已弹出")
    public void test_queryUserAppScoreDO_ejected(){
        Integer userId = 1;
        UserAppScoreDO userAppScoreDO = UserAppScoreDO.builder()
                .id(1L)
                .eject(true)
                .moment(0)
                .build();
        when(iUserAppScoreDao.queryUserAppScoreDO(userId)).thenReturn(userAppScoreDO);

        UserAppScoreResponse expectedResult = UserAppScoreResponse.builder()
                .id(0L)
                .eject(false)
                .moment(0)
                .build();
        UserAppScoreResponse actualResult = userAppScoreService.queryUserAppScoreDO(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("查询评分引导记录-已弹出")
    public void test_queryUserAppScoreDO_noEffective(){
        Integer userId = 1;
        UserAppScoreDO userAppScoreDO = UserAppScoreDO.builder()
                .id(1L)
                .eject(true)
                .moment(0)
                .effectiveTime(Instant.now().getEpochSecond() + 1000)
                .build();
        when(iUserAppScoreDao.queryUserAppScoreDO(userId)).thenReturn(userAppScoreDO);

        UserAppScoreResponse expectedResult = UserAppScoreResponse.builder()
                .id(0L)
                .eject(false)
                .moment(0)
                .build();
        UserAppScoreResponse actualResult = userAppScoreService.queryUserAppScoreDO(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("查询评分引导记录-第一台设备绑定")
    public void test_queryUserAppScoreDO_firstDeviceBind(){
        Integer userId = 1;
        UserAppScoreDO userAppScoreDO = UserAppScoreDO.builder()
                .id(1L)
                .eject(false)
                .moment(DEVICE_FIRST_BIND.getCode())
                .effectiveTime(Instant.now().getEpochSecond() - 1000)
                .build();
        when(iUserAppScoreDao.queryUserAppScoreDO(userId)).thenReturn(userAppScoreDO);

        // 直播成功率不符合条件
        when(userLiveReportService.liveSuccessRate(userAppScoreDO)).thenReturn(false);
        UserAppScoreResponse expectedResult = UserAppScoreResponse.builder()
                .id(0L)
                .eject(false)
                .moment(0)
                .build();
        UserAppScoreResponse actualResult = userAppScoreService.queryUserAppScoreDO(userId);
        Assert.assertEquals(expectedResult,actualResult);



        //直播成功率符合条件
        when(userLiveReportService.liveSuccessRate(userAppScoreDO)).thenReturn(true);
        expectedResult = UserAppScoreResponse.builder()
                .id(1L)
                .eject(true)
                .moment(DEVICE_FIRST_BIND.getCode())
                .build();
        actualResult = userAppScoreService.queryUserAppScoreDO(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("查询评分引导记录-非第一台设备绑定")
    public void test_queryUserAppScoreDO(){
        Integer userId = 1;
        UserAppScoreDO userAppScoreDO = UserAppScoreDO.builder()
                .id(1L)
                .eject(false)
                .moment(USER_FIRST_PAYMENT.getCode())
                .effectiveTime(Instant.now().getEpochSecond() - 1000)
                .build();
        when(iUserAppScoreDao.queryUserAppScoreDO(userId)).thenReturn(userAppScoreDO);


        UserAppScoreResponse expectedResult = UserAppScoreResponse.builder()
                .id(1L)
                .eject(true)
                .moment(USER_FIRST_PAYMENT.getCode())
                .build();
        UserAppScoreResponse actualResult = userAppScoreService.queryUserAppScoreDO(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("无评分")
    public void test_userAppScoreEjectReport_no_score(){
        UserAppScoreRequest request = new UserAppScoreRequest();
        request.setId(1L);

        when(iUserAppScoreDao.updateUserAppScore(any())).thenReturn(1);
        Map<String,Object> expectResult = Maps.newHashMap();
        Map<String,Object> actualResult = userAppScoreService.userAppScoreEjectReport(request);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("有评分-满意")
    public void test_userAppScoreEjectReport(){
        UserAppScoreRequest request = new UserAppScoreRequest();
        request.setId(1L);
        request.setScore(4);

        when(iUserAppScoreDao.updateUserAppScore(any())).thenReturn(1);
        Map<String,Object> expectResult = Maps.newHashMap();
        expectResult.put("satisfied",true);
        Map<String,Object> actualResult = userAppScoreService.userAppScoreEjectReport(request);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("有评分-不满意")
    public void test_userAppScoreEjectReport_score_low(){
        UserAppScoreRequest request = new UserAppScoreRequest();
        request.setId(1L);
        request.setScore(2);

        when(iUserAppScoreDao.updateUserAppScore(any())).thenReturn(1);
        Map<String,Object> expectResult = Maps.newHashMap();
        expectResult.put("satisfied",false);
        Map<String,Object> actualResult = userAppScoreService.userAppScoreEjectReport(request);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("评分引导老用户-用户列表为空")
    public void test_syncStoreUserMoment_userEmpty(){
        when(userService.selectAllUser(any(),any())).thenReturn(Lists.newArrayList());
        Integer expectedResult = 0;
        Integer actualResult = userAppScoreService.syncStoreUserMoment();
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("评分引导老用户-设备数满足")
    public void test_syncStoreUserMoment_hasSerialNumber(){
        //when(iUserAppScoreDao.deleteNotEffectiveUserAppScore(any())).thenReturn(1);
        when(iUserAppScoreDao.queryUserAppScoreDOExist(any())).thenReturn(1);

//        // 有用户
        List<User> userList = new ArrayList<>();
        User user = new User();
        user.setId(1);
        userList.add(user);
        when(userService.selectAllUser(any(),any())).thenReturn(userList);
        //返回多个设备
        List<String> serialNumberList = Lists.newArrayList();
        serialNumberList.add("serialNumber1");
        serialNumberList.add("serialNumber2");
        serialNumberList.add("serialNumber3");
        when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(serialNumberList);
        Integer expectedResult = 1;
        Integer actualResult = userAppScoreService.syncStoreUserMoment();
        Assert.assertEquals(expectedResult,actualResult);


    }


    @Test
    @DisplayName("评分引导老用户-设备数不满足,当前无可用套餐")
    public void test_syncStoreUserMoment_novip(){
        Integer expectedResult = 0;

        //when(iUserAppScoreDao.deleteNotEffectiveUserAppScore(any())).thenReturn(1);
        //when(iUserAppScoreDao.queryUserAppScoreDOExist(any())).thenReturn(1);


//        // 有用户
        List<User> userList = new ArrayList<>();
        User user = new User();
        user.setId(1);
        userList.add(user);
        when(userService.selectAllUser(any(),any())).thenReturn(userList);
        //返回多个设备

        when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(Lists.newArrayList());

        //绑定设备不否和条件、当前无生效套餐
        when(userVipService.queryUserOrderIdAccess(any())).thenReturn(Lists.newArrayList());
        expectedResult = 1;
        Integer actualResult = userAppScoreService.syncStoreUserMoment();
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("评分引导老用户-设备不满足-购买套餐")
    public void test_syncStoreUserMoment_pay(){
        Integer expectedResult = 1;

        //when(iUserAppScoreDao.deleteNotEffectiveUserAppScore(any())).thenReturn(1);
        when(iUserAppScoreDao.queryUserAppScoreDOExist(any())).thenReturn(1);


//        // 有用户
        List<User> userList = new ArrayList<>();
        User user = new User();
        user.setId(1);
        userList.add(user);
        when(userService.selectAllUser(any(),any())).thenReturn(userList);

        when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(Lists.newArrayList());



        //绑定设备不否和条件、当前无生效套餐
        when(userVipService.queryUserOrderIdAccess(any())).thenReturn(Lists.newArrayList());


        //绑定设备不否和条件、当前有生效套餐
        List<Long> orderIds = Lists.newArrayList();
        orderIds.add(1L);
        when(userVipService.queryUserOrderIdAccess(any())).thenReturn(orderIds);


        when(orderService.userOrderPaymentByUser(orderIds)).thenReturn(1);
        Integer actualResult = userAppScoreService.syncStoreUserMoment();
        Assert.assertEquals(expectedResult,actualResult);
    }
}
