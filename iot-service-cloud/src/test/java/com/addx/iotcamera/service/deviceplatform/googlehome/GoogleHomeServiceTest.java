package com.addx.iotcamera.service.deviceplatform.googlehome;

import com.addx.iotcamera.bean.app.device.DeviceConfigRequest;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.config.GoogleHomeWhiteConfig;
import com.addx.iotcamera.service.DeviceConfigService;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.device.DeviceServiceTest;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.addx.iotcamera.service.deviceplatform.googlehome.GoogleHomeService.GoogleHomeGraphConfig;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.util.DeviceCodecUtil;
import com.addx.iotcamera.util.RedisUtil;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.ServiceAccountCredentials;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.IOException;
import java.time.Instant;
import java.util.*;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GoogleHomeServiceTest {

    @Mock
    private GoogleHomeWhiteConfig mockGoogleHomeWhiteSnConfig;
    @Mock
    private JdbcTemplate mockJdbcTemplate;
    @Mock
    private RedisUtil mockRedisUtil;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private DeviceInfoService mockDeviceInfoService;
    @Mock
    private DeviceConfigService mockDeviceConfigService;
    @Mock
    private DeviceStatusService mockDeviceStatusService;
    @Mock
    private FactoryDataQueryService mockFactoryDataQueryService;
    @Mock
    private GoogleHomeGraphConfig googleHomeGraphConfig;


    @InjectMocks
    private GoogleHomeService googleHomeServiceUnderTest;

    @Test
    public void testCanAccessGoogleHome() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final DeviceDO deviceDO = DeviceServiceTest.createDeviceDO(deviceModel);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = googleHomeServiceUnderTest.canAccessGoogleHome(deviceDO);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanAccessGoogleHome_GoogleHomeWhiteSnConfigReturnsNoItems() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final DeviceDO deviceDO = DeviceServiceTest.createDeviceDO(deviceModel);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = googleHomeServiceUnderTest.canAccessGoogleHome(deviceDO);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanAccessGoogleHomeUseCache() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final DeviceDO deviceDO = DeviceServiceTest.createDeviceDO(deviceModel);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = googleHomeServiceUnderTest.canAccessGoogleHomeUseCache(deviceDO);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanAccessGoogleHomeUseCache_GoogleHomeWhiteSnConfigReturnsNoItems() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final DeviceDO deviceDO = DeviceServiceTest.createDeviceDO(deviceModel);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = googleHomeServiceUnderTest.canAccessGoogleHomeUseCache(deviceDO);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanFirmwareSupport() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final DeviceDO deviceDO = DeviceServiceTest.createDeviceDO(deviceModel);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(0)
                .supportAlexaWebrtc(0)
                .build());
        // Run the test
        final boolean result = googleHomeServiceUnderTest.canFirmwareSupport(deviceDO);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanSnSupport() {
        // Setup
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = googleHomeServiceUnderTest.canSnSupport("sn", "userSn");

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanSnSupport_GoogleHomeWhiteSnConfigReturnsNoItems() {
        // Setup
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = googleHomeServiceUnderTest.canSnSupport("sn", "userSn");

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanSnSupportUseCache() {
        // Setup
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = googleHomeServiceUnderTest.canSnSupportUseCache("sn", "userSn");

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanSnSupportUseCache_GoogleHomeWhiteSnConfigReturnsNoItems() {
        // Setup
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = googleHomeServiceUnderTest.canSnSupportUseCache("sn", "userSn");

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testIsUserLinked() {
        // Setup
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Arrays.asList("value"));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        when(mockGoogleHomeWhiteSnConfig.getWhiteTenantIdSet()).thenReturn(Collections.singleton("vicoo"));

        // Run the test
        boolean result = googleHomeServiceUnderTest.isUserLinked(null, 0);
        // Verify the results
        assertTrue(!result);

        result = googleHomeServiceUnderTest.isUserLinked("vicoo", 0);
        // Verify the results
        assertTrue(!result);

        result = googleHomeServiceUnderTest.isUserLinked("guard", 0);
        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testIsUserLinked_JdbcTemplateReturnsNoItems() {
        // Setup
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Collections.emptyList());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = googleHomeServiceUnderTest.isUserLinked(null, 0);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testIsUserLinkedUseCache() {
        // Setup
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Arrays.asList("value"));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        when(mockGoogleHomeWhiteSnConfig.getWhiteTenantIdSet()).thenReturn(Collections.singleton("vicoo"));

        // Run the test
        boolean result = googleHomeServiceUnderTest.isUserLinkedUseCache(null,0);
        // Verify the results
        assertTrue(!result);

        result = googleHomeServiceUnderTest.isUserLinkedUseCache("vicoo", 0);
        // Verify the results
        assertTrue(!result);

        result = googleHomeServiceUnderTest.isUserLinkedUseCache("guard", 0);
        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testIsUserLinkedUseCache_JdbcTemplateReturnsNoItems() {
        // Setup
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Collections.emptyList());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = googleHomeServiceUnderTest.isUserLinkedUseCache(null,0);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testGetGoogleHomeApiKey() {
        // Setup
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Arrays.asList("value"));

        // Run the test
        final Map<String, Object> result = googleHomeServiceUnderTest.getGoogleHomeApiKey("oauthClientId");

        // Verify the results
    }

    @Test
    public void testGetGoogleHomeApiKey_JdbcTemplateReturnsNoItems() {
        // Setup
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, Object> result = googleHomeServiceUnderTest.getGoogleHomeApiKey("oauthClientId");

        // Verify the results
    }

    @Test
    public void testAccountLinkSuccess() {
        // Setup
        // Configure DeviceInfoService.listDevicesByUserId(...).
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        deviceDOList.get(0).setCodec(DeviceCodecUtil.H264);
        when(mockDeviceInfoService.listDevicesByUserId(0)).thenReturn(deviceDOList);

        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("serialNumber")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);
        when(mockDeviceConfigService.updateDefaultCodec(new DeviceConfigRequest())).thenReturn(new Result<>("data"));

        // Configure DeviceStatusService.queryDeviceStatusBySerialNumber(...).
        final DeviceStatusDO deviceStatusDO = DeviceServiceTest.createDeviceStatusDO();
        when(mockDeviceStatusService.queryDeviceStatusBySerialNumber("serialNumber")).thenReturn(deviceStatusDO);

        // Run the test
        googleHomeServiceUnderTest.accountLinkSuccess(0);

        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(0)
                .supportAlexaWebrtc(1)
                .build());
        googleHomeServiceUnderTest.accountLinkSuccess(0);

        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(null)
                .supportAlexaWebrtc(1)
                .build());
        deviceStatusDO.setLinkedPlatforms("googlehome");
        googleHomeServiceUnderTest.accountLinkSuccess(0);

        deviceDOList.get(0).setDeviceSupport(null);
        deviceStatusDO.setLinkedPlatforms("alexa");
        googleHomeServiceUnderTest.accountLinkSuccess(0);

        when(mockDeviceStatusService.queryDeviceStatusBySerialNumber("serialNumber")).thenReturn(null);
        googleHomeServiceUnderTest.accountLinkSuccess(0);

        // Verify the results
        verify(mockRedisService, times(5)).set("user_linked_googlehome_0", "1", 600);
    }

    @Test
    public void testAccountLinkSuccess_DeviceInfoServiceReturnsNoItems() {
        // Setup
        when(mockDeviceInfoService.listDevicesByUserId(0)).thenReturn(Collections.emptyList());
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);
        when(mockDeviceConfigService.updateDefaultCodec(new DeviceConfigRequest())).thenReturn(new Result<>("data"));

        // Configure DeviceStatusService.queryDeviceStatusBySerialNumber(...).
        final DeviceStatusDO deviceStatusDO = DeviceServiceTest.createDeviceStatusDO();
        when(mockDeviceStatusService.queryDeviceStatusBySerialNumber("serialNumber")).thenReturn(deviceStatusDO);

        // Run the test
        googleHomeServiceUnderTest.accountLinkSuccess(0);

        // Verify the results
        verify(mockRedisService).set("user_linked_googlehome_0", "1", 600);
    }

    @Test
    public void testAccountLinkSuccess_GoogleHomeWhiteSnConfigReturnsNoItems() {
        // Setup
        // Configure DeviceInfoService.listDevicesByUserId(...).
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        when(mockDeviceInfoService.listDevicesByUserId(0)).thenReturn(deviceDOList);

        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);
        when(mockDeviceConfigService.updateDefaultCodec(new DeviceConfigRequest())).thenReturn(new Result<>("data"));

        // Configure DeviceStatusService.queryDeviceStatusBySerialNumber(...).
        final DeviceStatusDO deviceStatusDO = DeviceServiceTest.createDeviceStatusDO();
        when(mockDeviceStatusService.queryDeviceStatusBySerialNumber("serialNumber")).thenReturn(deviceStatusDO);

        // Run the test
        googleHomeServiceUnderTest.accountLinkSuccess(0);

        // Verify the results
        verify(mockRedisService).set("user_linked_googlehome_0", "1", 600);
    }

    @Test
    public void testAccountLinkSuccess_DeviceConfigServiceReturnsFailure() {
        // Setup
        // Configure DeviceInfoService.listDevicesByUserId(...).
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        when(mockDeviceInfoService.listDevicesByUserId(0)).thenReturn(deviceDOList);

        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockGoogleHomeWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);
        when(mockDeviceConfigService.updateDefaultCodec(new DeviceConfigRequest())).thenReturn(Result.Exception(new Exception("message")));

        // Configure DeviceStatusService.queryDeviceStatusBySerialNumber(...).
        final DeviceStatusDO deviceStatusDO = DeviceServiceTest.createDeviceStatusDO();
        when(mockDeviceStatusService.queryDeviceStatusBySerialNumber("serialNumber")).thenReturn(deviceStatusDO);

        // Run the test
        googleHomeServiceUnderTest.accountLinkSuccess(0);

        // Verify the results
        verify(mockRedisService).set("user_linked_googlehome_0", "1", 600);
    }

    @Test
    public void testGetGoogleHomeAcessToken() throws IOException {
        when(mockRedisService.containsKey(any())).thenReturn(true);
        when(mockRedisService.get(any())).thenReturn("mockAccessToken");
        String googleHomeAccessToken = googleHomeServiceUnderTest.getGoogleHomeAccessToken(null, "1");
        Assert.assertTrue(StringUtils.equals(googleHomeAccessToken, "mockAccessToken"));

        when(mockRedisService.containsKey(any())).thenReturn(false);
        when(googleHomeGraphConfig.getConfig()).thenReturn(new HashMap(){{
            put(null, Collections.singletonMap("svcKeyPath", "classpath:/app/googlehome_graph/home-test-3d82b-980e249c8936.json"));
        }});
        googleHomeAccessToken = googleHomeServiceUnderTest.getGoogleHomeAccessToken(null, "1");
        Assert.assertTrue(StringUtils.isEmpty(googleHomeAccessToken));

        ServiceAccountCredentials svc = Mockito.mock(ServiceAccountCredentials.class);
        when(svc.refreshAccessToken()).thenReturn(new AccessToken("a", new Date(Instant.now().plusMillis(10000).toEpochMilli())));
        googleHomeServiceUnderTest.getSvcMap().put(null, svc);
        googleHomeAccessToken = googleHomeServiceUnderTest.getGoogleHomeAccessToken(null, "1");
        Assert.assertTrue(StringUtils.equals(googleHomeAccessToken, "a"));
    }
}
