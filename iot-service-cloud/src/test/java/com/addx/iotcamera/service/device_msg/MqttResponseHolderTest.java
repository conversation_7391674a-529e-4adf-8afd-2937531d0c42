package com.addx.iotcamera.service.device_msg;

import com.addx.iotcamera.mqtt.MqttPayload;
import com.addx.iotcamera.mqtt.enums.EOutputTopicType;
import com.addx.iotcamera.mqtt.enums.ERequestAction;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class MqttResponseHolderTest {

    @Test
    @SneakyThrows
    public void test() {
        final ERequestAction action = ERequestAction.reportEvent;
        final String sn = OpenApiUtil.shortUUID();
        final String id = OpenApiUtil.shortUUID();

        try (final MqttResponseHolder mqttRespHolder = MqttResponseHolder.waitResponse(action, sn, id)) {
            {
                final MqttResponseHolder holder = MqttResponseHolder.getHolder(EOutputTopicType.CONFIG);
                Assert.assertNull(holder);
                final MqttPayload mqttPayload = new MqttPayload("{}", null);
                Assert.assertFalse(MqttResponseHolder.setResponse(holder, sn, mqttPayload));
                Assert.assertNull(mqttRespHolder.getResponse());
            }
            final MqttResponseHolder holder = MqttResponseHolder.getHolder(EOutputTopicType.RESPONSE);
            {
                final JSONObject data = new JSONObject();
                final MqttPayload mqttPayload = new MqttPayload(data.toJSONString(), null);
                Assert.assertFalse(MqttResponseHolder.setResponse(holder, null, mqttPayload));
                Assert.assertNull(mqttRespHolder.getResponse());
            }
            {
                final JSONObject data = new JSONObject();
                final MqttPayload mqttPayload = new MqttPayload(data.toJSONString(), null);
                Assert.assertFalse(MqttResponseHolder.setResponse(holder, sn, mqttPayload));
                Assert.assertNull(mqttRespHolder.getResponse());
            }
            {
                final JSONObject data = new JSONObject().fluentPut("id", id);
                final MqttPayload mqttPayload = new MqttPayload(data.toJSONString(), null);
                Assert.assertFalse(MqttResponseHolder.setResponse(holder, sn, mqttPayload));
                Assert.assertNull(mqttRespHolder.getResponse());
            }
            {
                final JSONObject data = new JSONObject().fluentPut("id", id).fluentPut("name", "error");
                final MqttPayload mqttPayload = new MqttPayload(data.toJSONString(), null);
                Assert.assertFalse(MqttResponseHolder.setResponse(holder, sn, mqttPayload));
                Assert.assertNull(mqttRespHolder.getResponse());
            }
            {
                final JSONObject data = new JSONObject().fluentPut("name", action.getValue());
                final MqttPayload mqttPayload = new MqttPayload(data.toJSONString(), null);
                Assert.assertFalse(MqttResponseHolder.setResponse(holder, sn, mqttPayload));
                Assert.assertNull(mqttRespHolder.getResponse());
            }
            {
                final JSONObject data = new JSONObject().fluentPut("name", action.getValue()).fluentPut("id", "error");
                final MqttPayload mqttPayload = new MqttPayload(data.toJSONString(), null);
                Assert.assertFalse(MqttResponseHolder.setResponse(holder, sn, mqttPayload));
                Assert.assertNull(mqttRespHolder.getResponse());
            }
            {
                final JSONObject data = new JSONObject().fluentPut("name", action.getValue()).fluentPut("id", id).fluentPut("content", OpenApiUtil.shortUUID());
                final MqttPayload mqttPayload = new MqttPayload(data.toJSONString(), null);
                Assert.assertTrue(MqttResponseHolder.setResponse(holder, sn, mqttPayload));
                Assert.assertEquals(data, JSON.parseObject(mqttRespHolder.getResponse().getTextData()));
            }
            {
                final JSONObject data = new JSONObject().fluentPut("name", action.getValue()).fluentPut("id", id).fluentPut("content", OpenApiUtil.shortUUID());
                final MqttPayload mqttPayload = new MqttPayload(data.toJSONString(), null);
                Assert.assertTrue(MqttResponseHolder.setResponse(holder, sn, mqttPayload));
                Assert.assertEquals(data, JSON.parseObject(mqttRespHolder.getResponse().getTextData()));
            }

        }

    }

}
