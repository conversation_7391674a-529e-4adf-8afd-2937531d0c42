package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.vip.*;
import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.TierGroupDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.db.user.UserEjectReportDO;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.library.TierFreeUserExpireDO;
import com.addx.iotcamera.bean.domain.library.TierFreeUserVipDO;
import com.addx.iotcamera.bean.domain.uservip.UserDeviceFreeTierDO;
import com.addx.iotcamera.bean.domain.uservip.UserFreeLicenseNoThanksDO;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.exception.ParamException;
import com.addx.iotcamera.bean.response.device.UserTierDeviceInfoResponse;
import com.addx.iotcamera.bean.response.user.*;
import com.addx.iotcamera.bean.response.vip.DeviceCloudVipInfoResponse;
import com.addx.iotcamera.config.TierReminderConfig;
import com.addx.iotcamera.config.TierTermConfig;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.config.apollo.DeviceModelNoCreativeConfig;
import com.addx.iotcamera.config.app.AppColorConfig;
import com.addx.iotcamera.config.app.AppRecommendProductConfig;
import com.addx.iotcamera.config.app.TenantFreeTierConfig;
import com.addx.iotcamera.config.app.TierInfoConfig;
import com.addx.iotcamera.config.device.FreeUserVipTier2Config;
import com.addx.iotcamera.config.pay.AppTierKeyTranslatedConfig;
import com.addx.iotcamera.config.pay.RotationChartConfig;
import com.addx.iotcamera.config.pay.TierCopywriteDiffConfig;
import com.addx.iotcamera.config.reminder.BlacklistConfig;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.vip.IUserDeviceFreeTierDao;
import com.addx.iotcamera.dao.vip.IUserFreeLicenseNoThanksDAO;
import com.addx.iotcamera.enums.OrderSubTypeEnums;
import com.addx.iotcamera.enums.ProductTypeEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.enums.user.UserEjectTypeEnum;
import com.addx.iotcamera.service.abtest.AbTestService;
import com.addx.iotcamera.service.creative.CreativeService;
import com.addx.iotcamera.service.template.UserTierReminderService;
import com.addx.iotcamera.service.user.UserEjectReportService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.vip.*;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.annotation.PropertySource;

import java.lang.reflect.Field;
import java.time.Instant;
import java.util.*;

import static com.addx.iotcamera.constants.CopyWriteConstans.*;
import static com.addx.iotcamera.constants.PayConstants.*;
import static com.addx.iotcamera.enums.pay.UserCloudVipTagEnums.*;
import static org.addx.iot.common.constant.AppConstants.SUPPORT_ROLLING_DAY;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
@PropertySource(value = {
        "classpath:/custom/payment/payment_local.yml"
},
        encoding = "utf-8", factory = MixPropertySourceFactory.class)
@Slf4j
public class UserVipServiceTest {

    @InjectMocks
    private UserVipService userVipService;
    @Mock
    private IUserVipDAO iUserVipDAO;
    @Mock
    private BlacklistConfig blacklistConfig;
    @Mock
    private TierService tierService;

    @Mock
    private VipService vipService;
    @Mock
    private AbTestService abTestService;
    @Mock
    private UserService userService;
    @Mock
    private RedisService redisService;
    @Mock
    private CenterNotifyConfig centerNotifyConfig;
    @Mock
    private TierInfoConfig tierInfoConfig;
    @Mock
    private IOrderDAO iOrderDAO;
    @Mock
    private AppColorConfig appColorConfig;

    @Mock
    private TierGroupService tierGroupService;

    @Mock
    private UserTierReminderService userTierReminderService;

    @Mock
    private PaymentService paymentService;

    @Mock
    private AppRecommendProductConfig appRecommendProductConfig;

    @Mock
    private TierCopywriteDiffConfig tierCopywriteDiffConfig;


    @Mock
    private UserEjectReportService userEjectReportService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private TierReminderConfig tierReminderConfig;
    
    @Mock
    private RotationChartConfig rotationChartConfig;

    @Mock
    private ProductService productService;

    @Mock
    private UserTierDeviceService userTierDeviceService;

    @Mock
    private CopyWrite copyWrite;

    @Mock
    private TierTermConfig tierTermConfig;
    @Mock
    private TenantFreeTierConfig tenantFreeTierConfig;

    @Mock
    private UserVipActivateService userVipActivateService;

    @Mock
    private CreativeService creativeService;
    @Mock
    private DeviceModelNoCreativeConfig deviceModelNoCreativeConfig;
    @Mock
    private BindService bindService;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private Device4GService device4GService;
    @Mock
    private OrderService orderService;

    @Mock
    private UserSettingService userSettingService;


    @Mock
    private IDeviceDAO iDeviceDAO;

    @Mock
    private IUserDeviceFreeTierDao userDeviceFreeTierDao;

    @Mock
    private IUserFreeLicenseNoThanksDAO freeLicenseNoThanksDAO;

    @Mock
    private FreeLicenseService freeLicenseService;

    @Mock
    private AppTierKeyTranslatedConfig appTierKeyTranslatedConfig;

    @Before
    public void init() {
        when(centerNotifyConfig.getMessage()).thenReturn(Collections.singletonMap("vicoco", Collections.singletonMap("en", new HashMap<String, String>(){{
            put("freeMessage", "free");
            put("noUserTierMessage", "noUserTier");
            put("expirationReminderPopupMessage", "aaa");
            put("changeRemindePopupMessage", "aaa");
            put("protectionMessageToday", "aaaa");
            put("protectionMessage", "aaaa");
        }})));
    }

    /**
     * 用户没有vip时视频固定保存7天
     */
    @Test
    public void test_queryCurrentTierTime_noVip() {
        when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenReturn(Collections.EMPTY_LIST);
        when(iUserVipDAO.queryUserVipInfoBefor(any(), any())).thenReturn(null);

        Calendar cal = Calendar.getInstance();
        long curTime = cal.getTimeInMillis();
        cal.add(Calendar.DATE, -7);
        int expTierTime = getMaxInsertTime(cal);
        // 用户下保存时间小于tierTime的视频，需要删除
        int tierTime = userVipService.queryCurrentTierTime(0, curTime);
        Assert.assertEquals(expTierTime, tierTime);
    }

    // 计算用户视频的最大保存时间
    private static int getMaxInsertTime(Calendar cal) {
        cal.set(Calendar.HOUR, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        int expTierTime = (int) (cal.getTimeInMillis() / 1000);
        return expTierTime;
    }

    /**
     * 用户当前有有效vip且没有过期vip时，保存当前vip等级所设置的天数
     */
    @Test
    public void test_queryCurrentTierTime_haveValidVip() {
        int rollingDays = new Random().nextInt(50) + 10;

        Calendar cal = Calendar.getInstance();
        long curTime = cal.getTimeInMillis();
        cal.add(Calendar.DATE, -rollingDays);
        int expTierTime = getMaxInsertTime(cal);

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setEndTime((int) (curTime / 1000) + 1);
        userVipDO.setTierId(0);

        Tier tier = new Tier();
        tier.setTierId(100);
        tier.setRollingDays(rollingDays);

        when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenReturn(Arrays.asList(userVipDO));
        when(iUserVipDAO.queryUserVipInfoBefor(any(), any())).thenReturn(null);
        when(tierService.queryTierById(anyInt())).thenReturn(tier);

        // 用户下保存时间小于tierTime的视频，需要删除
        int tierTime = userVipService.queryCurrentTierTime(0, curTime);
        Assert.assertEquals(expTierTime, tierTime);

        when(userService.queryUserById(any())).thenReturn(new User() {{
            setRegistTime((int)(System.currentTimeMillis()/1000));
        }});
        FreeUserVipTier2Config freeUserVipTier2Config = new FreeUserVipTier2Config();
        try {
            freeUserVipTier2Config.afterPropertiesSet();
        } catch (Exception e) {
            e.printStackTrace();
        }
        freeUserVipTier2Config.setRegisterConfigList(Arrays.asList(new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(100);
            setRegisterStart(0);
            setRegisterEnd(Integer.MAX_VALUE);
            setLookBackDay(rollingDays);
        }}));
        tierTime = userVipService.queryCurrentTierTime(0, curTime);
        Assert.assertEquals(expTierTime, tierTime);
    }

    private static int getExpiratione() {
        try {
            Field field = UserVipService.class.getDeclaredField("expiratione");
            field.setAccessible(true);
            return field.getInt(UserVipService.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void test_queryCurrentTierTime_haveUnValidVip_gtExpiration() {
        int rollingDays = 55;
        // 过期且已过保护保护期
        test_queryCurrentTierTime_haveUnValidVip(-(getExpiratione() + 1), rollingDays, 7);
    }

    /**
     * @param diffDays       vip与当前时间相差的天数
     * @param rollingDays    vip天数
     * @param expRollingDays 期望计算出来的vip天数
     */
    public void test_queryCurrentTierTime_haveUnValidVip(int diffDays, int rollingDays, int expRollingDays) {
        Calendar cal = Calendar.getInstance();
        long curTime = cal.getTimeInMillis();
        cal.add(Calendar.DATE, -expRollingDays);
        int expTierTime = getMaxInsertTime(cal);

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setEndTime((int) (curTime / 1000) + diffDays);
        userVipDO.setTierId(0);

        Tier tier = new Tier();
        tier.setRollingDays(rollingDays);

        when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenReturn(Collections.EMPTY_LIST);
        when(iUserVipDAO.queryUserVipInfoBefor(any(), any())).thenReturn(userVipDO);
        when(tierService.queryTierById(anyInt())).thenReturn(tier);

        // 用户下保存时间小于tierTime的视频，需要删除
        int tierTime = userVipService.queryCurrentTierTime(0, curTime);
        Assert.assertEquals(expTierTime, tierTime);
    }

    /**
     * 高级vip变低级vip,且不在保护期test_queryCurrentTierTime_haveUnValidVip_ltExpiration
     */
    @Test
    public void test_queryCurrentTierTime_tier_high2low_gtExpiration() {
        int rollingDays = 55;
        int oldRollingDays = rollingDays + 5;
        test_queryCurrentTierTime_tier_change(-(getExpiratione() + 1), oldRollingDays, rollingDays, rollingDays);
    }

    /**
     * 高级vip变低级vip,且不在保护期
     */
    @Test
    public void test_queryCurrentTierTime_tier_low2high_gtExpiration() {
        int rollingDays = new Random().nextInt(50) + 10;
        int oldRollingDays = rollingDays - 5;
        test_queryCurrentTierTime_tier_change(-(getExpiratione() + 1), oldRollingDays, rollingDays, rollingDays);
    }

    /**
     * @param diffDays       vip与当前时间相差的天数
     * @param oldRollingDays 最后一个已过期的vip的天数
     * @param rollingDays    vip天数
     * @param expRollingDays 期望计算出来的vip天数
     */
    public void test_queryCurrentTierTime_tier_change(int diffDays, int oldRollingDays, int rollingDays, int expRollingDays) {
        Calendar cal = Calendar.getInstance();
        long curTime = cal.getTimeInMillis();
        cal.add(Calendar.DATE, -expRollingDays);
        int expTierTime = getMaxInsertTime(cal);

        UserVipDO oldUserVipDO = new UserVipDO();
        oldUserVipDO.setEndTime((int) (curTime / 1000) + diffDays);
        oldUserVipDO.setTierId(2);

        Tier oldTier = new Tier();
        oldTier.setTierId(2);
        oldTier.setRollingDays(oldRollingDays);

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setEndTime((int) (curTime / 1000) + 10);
        userVipDO.setTierId(1);

        Tier tier = new Tier();
        tier.setTierId(1);
        tier.setRollingDays(rollingDays);

        when(iUserVipDAO.queryUserVipInfoBefor(any(), any())).thenReturn(oldUserVipDO);
        when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenReturn(Arrays.asList(userVipDO));
        when(tierService.queryTierById(oldUserVipDO.getTierId())).thenReturn(oldTier);
        when(tierService.queryTierById(userVipDO.getTierId())).thenReturn(tier);

        // 用户下保存时间小于tierTime的视频，需要删除
        int tierTime = userVipService.queryCurrentTierTime(0, curTime);
        //Assert.assertEquals(expTierTime, tierTime);
    }

//    @Test
//    public void test_queryUserVipInfo() throws Exception {
//        Field openReceiveVipField = UserVipService.class.getDeclaredField("openReceiveVip");
//        openReceiveVipField.setAccessible(true);
//        openReceiveVipField.set(userVipService, true);
//
//        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt())).thenReturn(Arrays.asList(UserVipDO.builder().tierId(1).effectiveTime((int)(System.currentTimeMillis()/1000) - 10).endTime((int)(System.currentTimeMillis()/1000) + 10).build(), UserVipDO.builder().tierId(2).effectiveTime((int)(System.currentTimeMillis()/1000) - 100).endTime((int)(System.currentTimeMillis()/1000) - 99).build()));
//        when(tierInfoConfig.getTierInfos(anyString(), anyString())).thenReturn(Collections.singletonList(new TierInfo(){{
//            setId(1);
//            setName("vip1");
//        }}));
//
//        UserVipTier userVipTier = userVipService.queryUserVipInfo(1, "en", "vicoco");
//        Assert.assertTrue(userVipTier.isVip());
//        Assert.assertTrue(userVipTier.getTierId().intValue() == 1);
//
//        when(iUserVipDAO.queryUserVipInfo(anyInt(), anyInt())).thenReturn(Collections.emptyList());
//        when(iUserVipDAO.queryLastUserVipInfo(anyInt())).thenReturn(UserVipDO.builder().tierId(1).endTime((int)(System.currentTimeMillis()/1000)).orderId(0L).build());
//        when(tierInfoConfig.getTierInfos(anyString(), anyString())).thenReturn(Collections.singletonList(new TierInfo(){{
//            setId(1);
//            setName("vip1");
//        }}));
//        when(iOrderDAO.queryByOrderId(anyLong())).thenReturn(OrderDO.builder().subType(OrderSubTypeEnums.SUBSCRIBE.getCode()).build());
//        when(appColorConfig.queryAppColor(anyString())).thenReturn("blue");
//        when(tierService.queryTierById(anyInt())).thenReturn(new Tier() {{setRollingDays(1);setSize(1024);setLevel(1);}});
//
//        userVipTier = userVipService.queryUserVipInfo(1, "en", "vicoco");
//        Assert.assertTrue(userVipTier.isVip());
//        Assert.assertTrue(userVipTier.getTierId().intValue() == 1);
//    }

    @Test
    public void test_markNoUserTier() {
        userVipService.markNoUserTier(1);
        userVipService.unmarkNoUserTier(1);
        boolean isNoUserTier = userVipService.isNoUserTier(1);
        assertTrue(!isNoUserTier);
    }

    @Test
    public void test_queryCurrentTierSize() {
        long tierSize = userVipService.queryCurrentTierSize(1, (int)(System.currentTimeMillis()));
        assertTrue(tierSize == 1L*1024*1024*1024);

        when(iUserVipDAO.queryUserVipInfo(any(), any(),any())).thenReturn(Arrays.asList(new UserVipDO(){{
            setTierId(100);
        }}));
        when(tierService.queryTierById(any())).thenReturn(new Tier(){{
            setStorage(2L*1024*1024*1024);
        }});
        tierSize = userVipService.queryCurrentTierSize(1, (int)(System.currentTimeMillis()));
        assertTrue(tierSize == 2L*1024*1024*1024);

        when(userService.queryUserById(any())).thenReturn(new User(){{
            setRegistTime((int)(System.currentTimeMillis()/1000));
        }});
        FreeUserVipTier2Config freeUserVipTier2Config = new FreeUserVipTier2Config();
        try {
            freeUserVipTier2Config.afterPropertiesSet();
        } catch (Exception e) {
            e.printStackTrace();
        }
        freeUserVipTier2Config.setRegisterConfigList(Arrays.asList(new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(100);
            setRegisterStart(0);
            setRegisterEnd(Integer.MAX_VALUE);
            setCapacityGB(3);
        }}));
        tierSize = userVipService.queryCurrentTierSize(1, (int)(System.currentTimeMillis()));
        assertTrue(tierSize == 3L*1024*1024*1024);
    }



//    @Test
//    @DisplayName("OEM 定制app")
//    public void test_oemTenant() throws Exception {
//        AppRequestBase requestBase = new AppRequestBase();
//        AppInfo app = new AppInfo();
//        app.setTenantId("askari");
//        requestBase.setApp(app);
//
//
//
//        userVipService.receiveTier(1,requestBase);
//        verify(paymentService, times(0)).initUserVipRegister(any(),any());
//    }


    @Test(expected = ParamException.class)
    @DisplayName("数据库中未配置商品")
    public void test_noReceive_product_null() throws Exception {
        AppRequestBase requestBase = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        requestBase.setApp(app);
        User user = new User();
        user.setId(1);
        when(userService.queryUserById(any())).thenReturn(user);

        Set<Integer> userSet = Sets.newHashSet();
        userSet.add(1);
        when(userService.queryUserListByUser(any())).thenReturn(userSet);
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(0);
        when(appRecommendProductConfig.queryFreeProductId(any())).thenReturn(null);

        userVipService.receiveTier(1,requestBase);
        verify(paymentService, times(0)).initUserVipRegister(any(),any());
    }

    @Test
    @DisplayName("领取免费商品")
    public void test_noReceive_product() throws Exception {
        AppRequestBase requestBase = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        requestBase.setApp(app);
        User user = new User();
        user.setId(1);
        when(userService.queryUserById(any())).thenReturn(user);

        Set<Integer> userSet = Sets.newHashSet();
        userSet.add(1);
        when(userService.queryUserListByUser(any())).thenReturn(userSet);
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(0);
        when(appRecommendProductConfig.queryFreeProductId(any())).thenReturn(1);

        doNothing().when(paymentService).initUserVipRegister(any(),any());

        userVipService.receiveTier(1,requestBase);

        verify(paymentService, times(1)).initUserVipRegister(any(),any());
    }

    @Test
    public void test_initFreeUserVip_hasReceive(){
        Integer userId = 1;
        AppRequestBase appRequestBase = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        appRequestBase.setApp(app);

        User user = new User();
        when(userService.queryUserById(any())).thenReturn(user);
        when(userService.queryUserListByUser(any())).thenReturn(Sets.newHashSet());
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(1);

        TierFreeUserVipDO expectedResult = TierFreeUserVipDO.builder()
                .freeVip(false)
                .build();
        TierFreeUserVipDO actualResult = userVipService.initFreeUserVip(userId,appRequestBase);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    public void test_initFreeUserVip_noVip(){
        Integer userId = 1;
        AppRequestBase appRequestBase = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        appRequestBase.setApp(app);

        User user = new User();
        when(userService.queryUserById(any())).thenReturn(user);
        when(userService.queryUserListByUser(any())).thenReturn(Sets.newHashSet());
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(0);

        when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(Lists.newArrayList());


        TierFreeUserVipDO expectedResult = TierFreeUserVipDO.builder()
                .freeVip(false)
                .build();
        TierFreeUserVipDO actualResult = userVipService.initFreeUserVip(userId,appRequestBase);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    public void test_initFreeUserVip(){
        Integer userId = 1;
        AppRequestBase appRequestBase = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        appRequestBase.setApp(app);

        User user = new User();
        when(userService.queryUserById(any())).thenReturn(user);
        when(userService.queryUserListByUser(any())).thenReturn(Sets.newHashSet());
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(0);

        List<UserVipDO> userVipDOList = Lists.newArrayList();
        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setTierId(110);
        userVipDOList.add(userVipDO);

        when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(userVipDOList);
        when(tierService.initRecommendProduct(any(),any())).thenReturn(true);

        TierFreeUserVipDO expectedResult = TierFreeUserVipDO.builder()
                .freeVip(true)
                .tierId(110)
                .recommendProductV1(Lists.newArrayList())
                .build();
        TierFreeUserVipDO actualResult = userVipService.initFreeUserVip(userId,appRequestBase);
        Assert.assertEquals(expectedResult.isFreeVip(),actualResult.isFreeVip());
    }


    @Test
    @DisplayName("套餐权益对比-用户不存在")
    public void test_queryTierCopywriteDiff_user_null(){
        TierListRequest appRequestBase = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        appRequestBase.setApp(app);
        when(userService.queryUserById(any())).thenReturn(null);
        Map<String,Object> expectedResult = Maps.newHashMap();
        Map<String,Object> actualResult = userVipService.queryTierCopywriteDiff(1,"", appRequestBase);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("套餐权益对比-vip用户")
    public void test_queryTierCopywriteDiff_vip(){
        TierListRequest appRequestBase = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        appRequestBase.setApp(app);
        Integer userId = 1;
        User user = new User();
        user.setId(userId);
        when(userService.queryUserById(any())).thenReturn(user);

        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(0).build());


        Integer currentTime = (int)Instant.now().getEpochSecond()+10000;

        List<UserVipDO> userVipDOList = Lists.newArrayList();
        UserVipDO vip = UserVipDO.builder()
                .tierId(1)
                .endTime(currentTime)
                .build();
        userVipDOList.add(vip);
        when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(userVipDOList);

        Tier tier = new Tier();
        tier.setLevel(1);
        when(tierService.queryTierById(any())).thenReturn(tier);
        when(tenantFreeTierConfig.queryFreeTier(any())).thenReturn(100);
        when (abTestService.getFreeLicenseAbResult(userId, appRequestBase)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                .build());
        when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build());
        Map<String,Object> expectedResult = Maps.newHashMap();
        expectedResult.put("diffList",Lists.newArrayList());
        expectedResult.put("storageDayCount",0);
        expectedResult.put("isSubscribed",true);
        expectedResult.put("vipType",TIER_COPYWRITE_DIFF_LITE);
        expectedResult.put("abFeatureSimpleResultList",new HashMap<>());
        Map<String,Object> actualResult = userVipService.queryTierCopywriteDiff(userId,"vicoo", appRequestBase);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("套餐权益对比")
    public void test_queryTierCopywriteDiff(){
        Integer userId = 1;
        User user = new User();
        user.setId(userId);
        user.setRegistTime(1);
        Integer currentTime = (int)Instant.now().getEpochSecond();
        TierListRequest appRequestBase = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        appRequestBase.setApp(app);

        when(userService.queryUserById(any())).thenReturn(user);
        {
            // free tier
            when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(0).build());

            List<UserVipDO> userVipDOList = Lists.newArrayList();
            UserVipDO vip = UserVipDO.builder()
                    .tierId(100)
                    .endTime(currentTime + 10000)
                    .build();
            userVipDOList.add(vip);
            when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(userVipDOList);

            Tier tier = new Tier();
            tier.setLevel(0);
            when(tierService.queryTierById(any())).thenReturn(tier);

            when(tierCopywriteDiffConfig.queryTierCopywriteDiffList(any(),any())).thenReturn(Lists.newArrayList());

            when(tierService.queryTierLookBackDay(any(),any())).thenReturn(3);

            when(tenantFreeTierConfig.queryFreeTier(any())).thenReturn(100);
            when (abTestService.getFreeLicenseAbResult(userId, appRequestBase)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String,Object> expectedResult = Maps.newHashMap();
            expectedResult.put("diffList",Lists.newArrayList());
            expectedResult.put("storageDayCount",3);
            expectedResult.put("isSubscribed",false);
            expectedResult.put("vipType",TIER_COPYWRITE_DIFF_LITE);
            expectedResult.put("abFeatureSimpleResultList",new HashMap<>());
            Map<String,Object> actualResult = userVipService.queryTierCopywriteDiff(userId,"vicoo", appRequestBase);
            Assert.assertEquals(expectedResult,actualResult);
        }
        {
            //no plan
            when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());

            List<UserVipDO> userVipDOList = Lists.newArrayList();
            when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(userVipDOList);

            Tier tier = new Tier();
            tier.setLevel(0);
            when(tierService.queryTierById(any())).thenReturn(tier);

            when(tierCopywriteDiffConfig.queryTierCopywriteDiffList(any(),any())).thenReturn(Lists.newArrayList());

            when(tierService.queryTierLookBackDay(any(),any())).thenReturn(3);

            when(tenantFreeTierConfig.queryFreeTier(any())).thenReturn(100);

            Map<String,Object> expectedResult = Maps.newHashMap();
            expectedResult.put("diffList",Lists.newArrayList());
            expectedResult.put("storageDayCount",60);
            expectedResult.put("isSubscribed",false);
            expectedResult.put("vipType",TIER_COPYWRITE_DIFF_NOPLAN);
            expectedResult.put("abFeatureSimpleResultList",new HashMap<>());
            Map<String,Object> actualResult = userVipService.queryTierCopywriteDiff(userId,"vicoo", appRequestBase);
            Assert.assertEquals(expectedResult,actualResult);
        }

        {
            //no plan
            when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());

            List<UserVipDO> userVipDOList = Lists.newArrayList();
            when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(userVipDOList);

            Tier tier = new Tier();
            tier.setLevel(0);
            when(tierService.queryTierById(any())).thenReturn(tier);

            when(tierCopywriteDiffConfig.queryTierCopywriteDiffList(any(),any())).thenReturn(Lists.newArrayList());

            when(tierService.queryTierLookBackDay(any(),any())).thenReturn(3);

            when(tenantFreeTierConfig.queryFreeTier(any())).thenReturn(100);

            Map<String,Object> expectedResult = Maps.newHashMap();
            expectedResult.put("diffList",Lists.newArrayList());
            expectedResult.put("storageDayCount",60);
            expectedResult.put("isSubscribed",true);
            expectedResult.put("vipType",TIER_COPYWRITE_DIFF_NOPLAN);
            Map<String, Integer> abFeatureSimpleResultList = Maps.newHashMap();
            abFeatureSimpleResultList.put("1", 1);
            expectedResult.put("abFeatureSimpleResultList",abFeatureSimpleResultList);
            when (abTestService.getFreeLicenseAbResult(userId, appRequestBase)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(true).isFreeTrial(false)
                            .abFeatureSimpleResultList(abFeatureSimpleResultList).build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(abFeatureSimpleResultList).build());
            Map<String,Object> actualResult = userVipService.queryTierCopywriteDiff(userId,"vicoo", appRequestBase);
            Assert.assertEquals(expectedResult,actualResult);
        }
    }



    @Test
    @DisplayName("以领取过")
    public void testInitUserTierRemindResponse_WhenUserHasReceivedVip_ReturnsShouldReminderFalse() {
        // Arrange
        Integer userId = 123;
        AppRequestBase request = new AppRequestBase();
        // Mock the isTierReceived() method to return true
        User user = new User();
        user.setId(userId);

        when(userTierReminderService.eligible(any(), any())).thenReturn(TierFreeUserExpireDO.builder().recommendProductType(1).notify(false).build());
        when(blacklistConfig.queryBlacklist()).thenReturn(new HashSet<>());
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
        when(iDeviceDAO.queryBindHistoryCompleted(userId)).thenReturn(new BindOperationTb());
        // Act
        UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

        // Assert
        assertFalse(response.isShouldReminder());
    }

    @Test
    @DisplayName("未领取过-未弹出过")
    public void testInitUserTierRemindResponse_WhenUserNoReceivedVip_noReload() {
        // Arrange
        Integer userId = 123;
        AppRequestBase request = new AppRequestBase();
        // Mock the isTierReceived() method to return true
        User user = new User();
        user.setId(userId);

        when(userTierReminderService.eligible(any(), any())).thenReturn(TierFreeUserExpireDO.builder().notify(true)
                .recommendProductType(0).build());


        when(userService.queryUserById(any())).thenReturn(user);
        when(userService.queryUserListByUser(any())).thenReturn(new HashSet<>(Arrays.asList(userId)));
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(0);
        //not record
        when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(null);
        when(rotationChartConfig.queryRotationChartConfigByTenantId(any())).thenReturn(Lists.newArrayList());

        when(bindService.getLatestBindModelNo(userId)).thenReturn("1");
        when(creativeService.getSlotName(1)).thenReturn("home_no_1");

        HashMap<String, Map<String, String>> map = new HashMap<>();
        map.put("1", ImmutableMap.of("home_no_1", "home_no_1_bird"));
        when(deviceModelNoCreativeConfig.getModel2CreativeReplaceMap()).thenReturn(map);
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
        when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                .build());
        when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build());
        when(blacklistConfig.queryBlacklist()).thenReturn(new HashSet<>());
        {

            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            when(iDeviceDAO.queryBindHistoryCompleted(userId)).thenReturn(new BindOperationTb());
            Map<String, Integer> abTest = new HashMap<>();
            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(Product4GABTestResult.builder().experimentSuccessful(true).billingCycleDuration(12)
                    .abFeatureSimpleResultList(abTest).build());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertEquals("home_no_1_bird", response.getSlotName());
            assertTrue(response.isShouldReminder());
        }

        {

            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(null);
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertFalse(response.isShouldReminder());
        }
    }

//    @Test
    @DisplayName("未领取过-未弹出过")
    public void testInitUserTierRemindResponse_WhenUserNoReceivedVip_noReload_withTIERFREE() {
        // Arrange
        Integer userId = 123;
        AppRequestBase request = new AppRequestBase();
        // Mock the isTierReceived() method to return true
        User user = new User();
        user.setId(userId);

        when(userTierReminderService.eligible(any(), any())).thenReturn(TierFreeUserExpireDO.builder().notify(true)
                .recommendProductType(0).build());

        when(userService.queryUserById(any())).thenReturn(user);
        when(userService.queryUserListByUser(any())).thenReturn(new HashSet<>(Arrays.asList(userId)));
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(0);
        //not record
        UserEjectReportDO userEjectReportDO = UserEjectReportDO.builder().createdTime((int) Instant.now().getEpochSecond() - 10000)
                .ejectNum(1).userId(userId).build();
        when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(userEjectReportDO);
        when(rotationChartConfig.queryRotationChartConfigByTenantId(any())).thenReturn(Lists.newArrayList());
        when(bindService.getLatestBindModelNo(userId)).thenReturn("1");
        when(creativeService.getSlotName(1)).thenReturn("home_no_1");

        HashMap<String, Map<String, String>> map = new HashMap<>();
        map.put("1", ImmutableMap.of("home_no_1", "home_no_1_bird"));
        when(deviceModelNoCreativeConfig.getModel2CreativeReplaceMap()).thenReturn(map);
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
        when(iDeviceDAO.queryBindHistoryCompleted(userId)).thenReturn(new BindOperationTb());
        {
            when(blacklistConfig.queryBlacklist()).thenReturn(new HashSet<>());
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(Product4GABTestResult.builder().experimentSuccessful(true).billingCycleDuration(12)
                    .abFeatureSimpleResultList(new HashMap<>()).build());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertFalse(response.isShouldReminder());
        }

        {

            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(null);
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertFalse(response.isShouldReminder());
        }
    }

    @Test
    @DisplayName("未领取过-未弹出过")
    public void testInitUserTierRemindResponse_WhenUserNoReceivedVip_noReload_withTIER4G() {
        // Arrange
        Integer userId = 123;
        AppRequestBase request = new AppRequestBase();
        // Mock the isTierReceived() method to return true
        User user = new User();
        user.setId(userId);

        when(userTierReminderService.eligible(any(), any())).thenReturn(TierFreeUserExpireDO.builder().notify(true)
                .recommendProductType(0).build());


        when(userService.queryUserById(any())).thenReturn(user);
        when(userService.queryUserListByUser(any())).thenReturn(new HashSet<>(Arrays.asList(userId)));
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(0);
        //not record
        UserEjectReportDO userEjectReportDO = UserEjectReportDO.builder().createdTime((int) Instant.now().getEpochSecond() - 10000)
                .ejectNum(1).userId(userId).build();
        when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_4G, null)).thenReturn(userEjectReportDO);
        when(rotationChartConfig.queryRotationChartConfigByTenantId(any())).thenReturn(Lists.newArrayList());
        when(bindService.getLatestBindModelNo(userId)).thenReturn("1");
        when(creativeService.getSlotName(1)).thenReturn("home_no_1");

        HashMap<String, Map<String, String>> map = new HashMap<>();
        map.put("1", ImmutableMap.of("home_no_1", "home_no_1_bird"));
        when(deviceModelNoCreativeConfig.getModel2CreativeReplaceMap()).thenReturn(map);
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
        when(iDeviceDAO.queryBindHistoryCompleted(userId)).thenReturn(new BindOperationTb());
        when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                .build());
        when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build());
        {
            Map<String, Integer> abTest = new HashMap<>();
            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(Product4GABTestResult.builder().experimentSuccessful(true).billingCycleDuration(12)
                    .abFeatureSimpleResultList(abTest).build());
            when(blacklistConfig.queryBlacklist()).thenReturn(new HashSet<>());

            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }
        {
            Set<String> set = new HashSet<>();
            when(userService.queryUserById(userId)).thenReturn(User.builder().id(userId).email("email").build());
            set.add("email");
            when(blacklistConfig.queryBlacklist()).thenReturn(set);

            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertFalse(response.isShouldReminder());
        }

        {

            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(null);
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertFalse(response.isShouldReminder());
        }
    }


    @Test
    @DisplayName("未领取过-不替换补鸟器")
    public void testInitUserTierRemindResponse_WhenNotReplace() {
        // Arrange
        Integer userId = 123;
        AppRequestBase request = new AppRequestBase();
        // Mock the isTierReceived() method to return true
        User user = new User();
        user.setId(userId);

        when(userTierReminderService.eligible(any(), any())).thenReturn(TierFreeUserExpireDO.builder().notify(true).recommendProductType(0).build());


        when(userService.queryUserById(any())).thenReturn(user);
        when(userService.queryUserListByUser(any())).thenReturn(new HashSet<>(Arrays.asList(userId)));
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(0);
        //not record
        when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(null);
        when(rotationChartConfig.queryRotationChartConfigByTenantId(any())).thenReturn(Lists.newArrayList());
        when(bindService.getLatestBindModelNo(userId)).thenReturn("2");
        when(creativeService.getSlotName(1)).thenReturn("home_no_1");
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
        when(iDeviceDAO.queryBindHistoryCompleted(userId)).thenReturn(new BindOperationTb());
        when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                .build());
        when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build());
        Map<String, Integer> abTest = new HashMap<>();
        when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(Product4GABTestResult.builder().experimentSuccessful(true).billingCycleDuration(12)
                .abFeatureSimpleResultList(abTest).build());
        {
            when(blacklistConfig.queryBlacklist()).thenReturn(new HashSet<>());
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertEquals("home_no_1", response.getSlotName());
            assertTrue(response.isShouldReminder());
        }

        {
            when(bindService.getLatestBindModelNo(userId)).thenReturn("1");
            when(creativeService.getSlotName(1)).thenReturn("home_no_1");
            HashMap<String, Map<String, String>> map = new HashMap<>();
            map.put("1", ImmutableMap.of("home_no_2", "home_no_2_bird"));
            when(deviceModelNoCreativeConfig.getModel2CreativeReplaceMap()).thenReturn(map);
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(null);
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);
            assertEquals("home_no_1", response.getSlotName());
        }

        {
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(null);
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            when(bindService.getLatestBindModelNo(userId)).thenReturn("");
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);
            assertEquals("home_no_1", response.getSlotName());
        }
    }


    @Test
    @DisplayName("未领取过-有弹出过")
    public void testInitUserTierRemindResponse_WhenUserNoReceivedVip_hasReload() {
        // Arrange
        Integer userId = 123;
        AppRequestBase request = new AppRequestBase();
        // Mock the isTierReceived() method to return true
        User user = new User();
        user.setId(userId);

        when(userTierReminderService.eligible(any(), any())).thenReturn(TierFreeUserExpireDO.builder().notify(true)
                .recommendProductType(1).build());


        when(userService.queryUserById(any())).thenReturn(user);
        when(userService.queryUserListByUser(any())).thenReturn(new HashSet<>(Arrays.asList(userId)));
        when(iUserVipDAO.queryUserVipInfoByUserIds(any(),any())).thenReturn(0);

        when(rotationChartConfig.queryRotationChartConfigByTenantId(any())).thenReturn(Lists.newArrayList());
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
        when(iDeviceDAO.queryBindHistoryCompleted(userId)).thenReturn(new BindOperationTb());
        when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                .build());
        // Assert
        {
            //has record
            UserEjectReportDO userEjectReportDO = new UserEjectReportDO();
            userEjectReportDO.setCreatedTime(**********); // 设置上次弹出时间为2020年9月13日 10:40:00
            userEjectReportDO.setEjectNum(6); // 设置弹出次数为6
            when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(userEjectReportDO);

            when(userTierReminderService.verifyGrayscale(any())).thenReturn(false);
            when(userTierReminderService.verifyRepeatedMinder(any())).thenReturn(false);
            when(bindService.getLatestBindModelNo(userId)).thenReturn("1");
            when(creativeService.getSlotName(1)).thenReturn("home_no_1");
            when(blacklistConfig.queryBlacklist()).thenReturn(new HashSet<>());
            HashMap<String, Map<String, String>> map = new HashMap<>();
            map.put("1", ImmutableMap.of("home_no_1", "home_no_1_bird"));
            when(deviceModelNoCreativeConfig.getModel2CreativeReplaceMap()).thenReturn(map);
            // 超过弹出次数限制
            when(tierReminderConfig.getConfig()).thenReturn(Maps.newHashMap());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);
            assertFalse(response.isShouldReminder());
        }

        {
            Integer currentTime = (int) Instant.now().getEpochSecond();
            //has record
            UserEjectReportDO userEjectReportDO = new UserEjectReportDO();
            userEjectReportDO.setCreatedTime(currentTime);
            userEjectReportDO.setEjectNum(1); // 设置弹出次数为1
            when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(userEjectReportDO);

            // 已绑定设备--有配置推荐商品
            when(userTierReminderService.initNotifyConfig(any())).thenReturn(this.initTierReminderConfig());

            when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(Arrays.asList("new"));

            when(userTierReminderService.verifyGrayscale(any())).thenReturn(true);
            when(userTierReminderService.verifyRepeatedMinder(any())).thenReturn(true);


            //未配置推荐商品
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(null);
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            when(userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndSerialNumber(userId, "new")).thenReturn(
                    new UserDeviceFreeTierDO()
            );
            when(freeLicenseNoThanksDAO.getRecordByUserIdAndSerialNumber(userId, "new")).thenReturn(
                    new UserFreeLicenseNoThanksDO()
            );
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertFalse(response.isShouldReminder());
        }

        {
            //has record
            Integer currentTime = (int) Instant.now().getEpochSecond();
            //has record
            UserEjectReportDO userEjectReportDO = new UserEjectReportDO();
            userEjectReportDO.setCreatedTime(currentTime - 70*60);
            userEjectReportDO.setEjectNum(1); // 设置弹出次数为1
            when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(userEjectReportDO);

            when(userTierReminderService.verifyGrayscale(any())).thenReturn(true);
            when(userTierReminderService.verifyRepeatedMinder(any())).thenReturn(true);
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String, Integer> abTest = new HashMap<>();
            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(Product4GABTestResult.builder().experimentSuccessful(true).billingCycleDuration(6)
                    .abFeatureSimpleResultList(abTest).build());
            // 已绑定设备--配置推荐商品
            when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(Arrays.asList("new"));
            when(userTierReminderService.initNotifyConfig(any())).thenReturn(this.initTierReminderConfig());
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }

        {
            //has record
            Integer currentTime = (int) Instant.now().getEpochSecond();
            //has record
            UserEjectReportDO userEjectReportDO = new UserEjectReportDO();
            userEjectReportDO.setCreatedTime(currentTime - 70*60);
            userEjectReportDO.setEjectNum(1); // 设置弹出次数为1
            when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(userEjectReportDO);

            when(userTierReminderService.verifyGrayscale(any())).thenReturn(true);
            when(userTierReminderService.verifyRepeatedMinder(any())).thenReturn(true);
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String, Integer> abTest = new HashMap<>();
            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(Product4GABTestResult.builder().experimentSuccessful(true).billingCycleDuration(6)
                    .abFeatureSimpleResultList(abTest).build());
            // 已绑定设备--配置推荐商品
            when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(Arrays.asList("new"));
            when(userTierReminderService.initNotifyConfig(any())).thenReturn(this.initTierReminderConfig());
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            when(appRecommendProductConfig.initRecommendProductDO(userId,request.getApp().getAppType(),request.getLanguage(),request.getApp().getTenantId(), request)).thenReturn(new RecommendProductDO());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }
        {
            //has record
            Integer currentTime = (int) Instant.now().getEpochSecond();
            //has record
            UserEjectReportDO userEjectReportDO = new UserEjectReportDO();
            userEjectReportDO.setCreatedTime(currentTime - 70*60);
            userEjectReportDO.setEjectNum(1); // 设置弹出次数为1
            when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(userEjectReportDO);

            when(userTierReminderService.verifyGrayscale(any())).thenReturn(true);
            when(userTierReminderService.verifyRepeatedMinder(any())).thenReturn(true);
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String, Integer> abTest = new HashMap<>();
            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(Product4GABTestResult.builder().experimentSuccessful(true).billingCycleDuration(6)
                    .abFeatureSimpleResultList(abTest).build());
            // 已绑定设备--配置推荐商品
            when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(Arrays.asList("new"));
            when(userTierReminderService.initNotifyConfig(any())).thenReturn(this.initTierReminderConfig());
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            when(appRecommendProductConfig.initRecommendProductDO(userId,request.getApp().getAppType(),request.getLanguage(),request.getApp().getTenantId(), request)).thenReturn(RecommendProductDO.builder().abFeatureSimpleResultList(new HashMap<>()).build());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }

        {
            //has record
            Integer currentTime = (int) Instant.now().getEpochSecond();
            //has record
            UserEjectReportDO userEjectReportDO = new UserEjectReportDO();
            userEjectReportDO.setCreatedTime(currentTime - 70*60);
            userEjectReportDO.setEjectNum(1); // 设置弹出次数为1
            when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(userEjectReportDO);

            when(userTierReminderService.verifyGrayscale(any())).thenReturn(true);
            when(userTierReminderService.verifyRepeatedMinder(any())).thenReturn(true);
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(Product4GABTestResult.builder().experimentSuccessful(true).billingCycleDuration(6)
                    .abFeatureSimpleResultList(null).build());
            // 已绑定设备--配置推荐商品
            when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(Arrays.asList("new"));
            when(userTierReminderService.initNotifyConfig(any())).thenReturn(this.initTierReminderConfig());
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            when(appRecommendProductConfig.initRecommendProductDO(userId,request.getApp().getAppType(),request.getLanguage(),request.getApp().getTenantId(), request)).thenReturn(new RecommendProductDO());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }

        {
            //has record
            Integer currentTime = (int) Instant.now().getEpochSecond();
            //has record
            UserEjectReportDO userEjectReportDO = new UserEjectReportDO();
            userEjectReportDO.setCreatedTime(currentTime - 70*60);
            userEjectReportDO.setEjectNum(1); // 设置弹出次数为1
            when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(userEjectReportDO);

            when(userTierReminderService.verifyGrayscale(any())).thenReturn(true);
            when(userTierReminderService.verifyRepeatedMinder(any())).thenReturn(true);

            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(true).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());

            // 已绑定设备--配置推荐商品
            when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(Arrays.asList("new"));
            when(userTierReminderService.initNotifyConfig(any())).thenReturn(this.initTierReminderConfig());
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }
        {
            //has record
            Integer currentTime = (int) Instant.now().getEpochSecond();
            //has record
            UserEjectReportDO userEjectReportDO = new UserEjectReportDO();
            userEjectReportDO.setCreatedTime(currentTime - 70*60);
            userEjectReportDO.setEjectNum(1); // 设置弹出次数为1
            when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(userEjectReportDO);

            when(userTierReminderService.verifyGrayscale(any())).thenReturn(true);
            when(userTierReminderService.verifyRepeatedMinder(any())).thenReturn(true);

            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());

            // 已绑定设备--配置推荐商品
            when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(Arrays.asList("new"));
            when(userTierReminderService.initNotifyConfig(any())).thenReturn(this.initTierReminderConfig());
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }
        {
            //has record
            Integer currentTime = (int) Instant.now().getEpochSecond();
            //has record
            UserEjectReportDO userEjectReportDO = new UserEjectReportDO();
            userEjectReportDO.setCreatedTime(currentTime - 70*60);
            userEjectReportDO.setEjectNum(1); // 设置弹出次数为1
            when(userEjectReportService.queryUserEjectReportDOLast(userId, UserEjectTypeEnum.TIER_FREE, null)).thenReturn(userEjectReportDO);

            when(userTierReminderService.verifyGrayscale(any())).thenReturn(true);
            when(userTierReminderService.verifyRepeatedMinder(any())).thenReturn(true);

            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(true)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());

            // 已绑定设备--配置推荐商品
            when(userRoleService.getUserSerialNumberByUserId(any())).thenReturn(Arrays.asList("new"));
            when(userTierReminderService.initNotifyConfig(any())).thenReturn(this.initTierReminderConfig());
            when(tierService.queryRecommendProduct(true,new AppRequestBase())).thenReturn(new UserVipTier.RecommendProduct());
            doNothing().when(userEjectReportService).insertUserEjectReport(any());
            UserTierRemindResponse response = userVipService.initUserTierRemindResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }
    }

    @Test
    public void testInitUserTierBirdProductListResponse() {
        // Arrange
        Integer userId = 123;
        AppRequestBase request = new AppRequestBase();
        // Mock the isTierReceived() method to return true
        User user = new User();
        user.setId(userId);

        {
            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(true)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(Arrays.asList("dev1", "birdDev"));
            when(tierService.isBirdDevice("dev1")).thenReturn(false);
            when(tierService.isBirdDevice("birdDev")).thenReturn(true);
            when(vipService.isVipDevice(userId, "birdDev")).thenReturn(false);

            UserTierRemindResponse response = userVipService.initUserTierBirdProductListResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }

        {
            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(true).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(true)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(Arrays.asList("dev1", "birdDev"));
            when(tierService.isBirdDevice("dev1")).thenReturn(false);
            when(tierService.isBirdDevice("birdDev")).thenReturn(true);
            when(vipService.isVipDevice(userId, "birdDev")).thenReturn(false);

            UserTierRemindResponse response = userVipService.initUserTierBirdProductListResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }

        {
            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(true)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(Arrays.asList("dev1", "birdDev"));
            when(tierService.isBirdDevice("dev1")).thenReturn(false);
            when(tierService.isBirdDevice("birdDev")).thenReturn(true);
            when(vipService.isVipDevice(userId, "birdDev")).thenReturn(false);

            UserTierRemindResponse response = userVipService.initUserTierBirdProductListResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }
        {
            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(Arrays.asList("dev1", "birdDev"));
            when(tierService.isBirdDevice("dev1")).thenReturn(false);
            when(tierService.isBirdDevice("birdDev")).thenReturn(true);
            when(vipService.isVipDevice(userId, "birdDev")).thenReturn(false);

            UserTierRemindResponse response = userVipService.initUserTierBirdProductListResponse(userId, request);

            assertTrue(response.isShouldReminder());
        }
    }

    private Map<Integer,Integer> initTierReminderConfig(){
        Map<Integer, Integer> config = new HashMap<>();
        config.put(1,60);
        config.put(2,120);
        config.put(3,180);
        return config;
    }

    @Test(expected = BaseException.class)
    @DisplayName("回看天数不符合要求")
    public void updateUserVipRollingDay_InvalidParams_rollingDay() {
        // Arrange
        Integer userId = 123;
        Long userVipId = 456L;
        UserVipRollingDayRequest request = new UserVipRollingDayRequest();
        request.setUserVipId(userVipId);
        request.setRollingDay(1);
        userVipService.updateUserVipRollingDay(userId, request);
    }

    @Test(expected = BaseException.class)
    @DisplayName("更新回看天数-无指定套餐记录")
    public void updateUserVipRollingDay_InvalidParams_ThrowsException() {
        // Arrange
        Integer userId = 123;
        Long userVipId = 456L;
        UserVipRollingDayRequest request = new UserVipRollingDayRequest();
        request.setUserVipId(userVipId);
        request.setRollingDay(30);

        when(iUserVipDAO.queryUserVipById(userVipId)).thenReturn(null);

        // Act
        userVipService.updateUserVipRollingDay(userId, request);
    }

    @Test(expected = BaseException.class)
    @DisplayName("更新回看天数-不是当前用户的套餐期间")
    public void updateUserVipRollingDay_InvalidParams_userNotEqual() {
        // Arrange
        Integer userId = 123;
        Long userVipId = 456L;
        UserVipRollingDayRequest request = new UserVipRollingDayRequest();
        request.setUserVipId(userVipId);
        request.setRollingDay(30);

        when(iUserVipDAO.queryUserVipById(userVipId)).thenReturn(UserVipDO.builder().userId(1).build());

        // Act
        userVipService.updateUserVipRollingDay(userId, request);
    }

    @Test
    @DisplayName("更新指定套餐区间的回看天数")
    public void updateUserVipRollingDay_ValidParams_Success() {
        // Arrange
        Integer userId = 123;
        Long userVipId = 456L;
        UserVipRollingDayRequest request = new UserVipRollingDayRequest();
        request.setUserVipId(userVipId);
        request.setRollingDay(30);


        UserVipDO userVipDO = UserVipDO.builder().userId(userId).id(userVipId).build();
        when(iUserVipDAO.queryUserVipById(userVipId)).thenReturn(userVipDO);

        // Act
        userVipService.updateUserVipRollingDay(userId, request);

        // Assert
        verify(iUserVipDAO).updateUserVipRollingDay(userVipId, userId, 30);
    }

    @Test
    @DisplayName("当前用户无套餐记录")
    public void test_queryUserCurrentVip_vipempty(){
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        request.setApp(app);

        when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(Lists.newArrayList());
        when(tierService.queryTierById(any())).thenReturn(Tier.builder().maxDeviceNum(1).build());
        {
            request.setTierServiceType(1);
            UserTierDeviceInfoResponse expectResult = UserTierDeviceInfoResponse.builder()
                    .rollingDay(0)
                    .supportRollingDay(Lists.newArrayList())
                    .serviceActiveDevice(UserTierDeviceInfo.builder().tierId(0).build())
                    .build();
            UserTierDeviceInfoResponse actualResult = userVipService.queryUserCurrentVipInfo(1,request);
            Assert.assertEquals(expectResult,actualResult);
        }
        {
            request.setTierServiceType(0);
            UserTierDeviceInfoResponse expectResult = UserTierDeviceInfoResponse.builder()
                    .rollingDay(0)
                    .supportRollingDay(SUPPORT_ROLLING_DAY)
                    .serviceActiveDevice(UserTierDeviceInfo.builder().tierId(0).build())
                    .build();
            UserTierDeviceInfoResponse actualResult = userVipService.queryUserCurrentVipInfo(1,request);
            Assert.assertEquals(expectResult,actualResult);
        }
    }

    @Test
    @DisplayName("用户当前服务下设备")
    public void test_queryUserCurrentVipInfo(){
        TierServiceInfoRequest requestBase = new TierServiceInfoRequest();
        requestBase.setTierServiceType(1);
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        requestBase.setApp(app);
        int effectTime = (int)Instant.now().getEpochSecond() - 1000;
        int endTime = (int)Instant.now().getEpochSecond() + 1000;
        int tierId = 221;
        int rollingDay = 30;

        UserVipDO userVipDO1 = UserVipDO.builder()
                .id(1L)
                .tierId(tierId)
                .effectiveTime(effectTime)
                .endTime(endTime)
                .tradeNo("x1")
                .rollingDay(rollingDay)
                .build();
        when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(Arrays.asList(userVipDO1));
        Tier tier = new Tier();
        tier.setTierId(tierId);
        tier.setTierGroupId(null);
        tier.setSize(1);
        tier.setRollingDays(rollingDay);
        tier.setTierServiceType(0);
        tier.setTierType(1);
        tier.setLevel(1);
        when(tierService.queryTierById(any())).thenReturn(tier);

        when(productService.queryTierProductType(any())).thenReturn(3);


        when(tierGroupService.getById(any(),any())).thenReturn(TierGroupDO.builder()
                .id(0)
                .name("")
                .tenantId(TENANTID_VICOO)
                .weight(0)
                .tierActiveOrder(3)
                .build());


        when(copyWrite.getConfig()).thenReturn(this.initCopyWrite());
        when(tierTermConfig.getUrl()).thenReturn(this.initTierTermUrl());


        when(userTierDeviceService.queryUserAdminDevice(any(),any())).thenReturn(Arrays.asList(
                DeviceDO.builder().serialNumber("sn1").simThirdParty(0).build(),
                DeviceDO.builder().serialNumber("sn2").simThirdParty(0).build()
        ));
//        {
//            // 无设备数量限制的套餐情况
//            tier.setMaxDeviceNum(null);
//            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(false);
//            when(userTierDeviceService.initAdditionalUserTierInfo(any(),any(),any())).thenReturn(Lists.newArrayList());
//            UserTierDeviceInfo userTierDeviceInfo = UserTierDeviceInfo.builder()
//                    .tierId(tierId)
//                    .effectiveTime(userVipDO1.getEffectiveTime())
//                    .endTime(userVipDO1.getEndTime())
//                    .maxDeviceNum(tier.getMaxDeviceNum() == null ? 0: tier.getMaxDeviceNum())
//                    .build();
//            UserTierDeviceInfoResponse expectResult = UserTierDeviceInfoResponse.builder()
//                    .userVipId(1L)
//                    .rollingDay(30)
//                    .supportRollingDay(Lists.newArrayList())
//                    .serviceActiveDevice(userTierDeviceInfo)
//                    .serviceAdditionList(Lists.newArrayList())
//                    .build();
//            UserTierDeviceInfoResponse actualResult = userVipService.queryUserCurrentVipInfo(1,requestBase);
//            Assert.assertEquals(expectResult.getUserVipId(),actualResult.getUserVipId());
//        }
        {
            // 有设备数量限制的套餐情况
            // 无套餐指定设备
            tier.setMaxDeviceNum(2);
            tier.setTierServiceType(TierServiceTypeEnums.TIER_4G.getCode());
            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(true);

            when(userTierDeviceService.queryUserAdminDevice(any(),any())).thenReturn(Arrays.asList(
                    DeviceDO.builder().serialNumber("sn1").iccid("iccid").simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn2").iccid("iccid").simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn3").iccid("iccid").simThirdParty(0).build()
            ));


            when(userTierDeviceService.getActiveTierDeviceList(any(),any())).thenReturn(new HashSet<>(Arrays.asList("sn1","sn2")));

            when(appTierKeyTranslatedConfig.queryAppTierTranslatedKey(any(),any())).thenReturn("");
            when(appTierKeyTranslatedConfig.queryAppTierTranslatedKey(any(),any())).thenReturn("");

            when(userTierDeviceService.initAdditionalUserTierInfo(any(),any(),any())).thenReturn(Lists.newArrayList());
            UserTierDeviceInfo userTierDeviceInfo = UserTierDeviceInfo.builder()
                    .tierId(tierId)
                    .effectiveTime(userVipDO1.getEffectiveTime())
                    .endTime(userVipDO1.getEndTime())
                    .maxDeviceNum(tier.getMaxDeviceNum() == null ? 0: tier.getMaxDeviceNum())
                    .activeDeviceList(Arrays.asList(DeviceDO.builder().serialNumber("sn1").build(), DeviceDO.builder().serialNumber("sn2").build()))
                    .build();
            UserTierDeviceInfoResponse expectResult = UserTierDeviceInfoResponse.builder()
                    .userVipId(1L)
                    .rollingDay(30)
                    .supportRollingDay(Lists.newArrayList())
                    .serviceActiveDevice(userTierDeviceInfo)
                    .serviceAdditionList(Lists.newArrayList())
                    .build();

            when(paymentService.queryPaymentFlow("x1")).thenReturn(PaymentFlow.builder().productId(1).build());
            when(productService.queryProductById(1)).thenReturn(ProductDO.builder().month(6).subscriptionPeriod(0).build());
            UserTierDeviceInfoResponse actualResult = userVipService.queryUserCurrentVipInfo(1,requestBase);
            Assert.assertEquals(expectResult.getServiceActiveDevice().getActiveDeviceList().size(),actualResult.getServiceActiveDevice().getActiveDeviceList().size());
        }

        {
            // 有设备数量限制的套餐情况
            // 无套餐指定设备
            tier.setMaxDeviceNum(2);
            tier.setTierServiceType(TierServiceTypeEnums.TIER_4G.getCode());
            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(true);

            when(userTierDeviceService.queryUserAdminDevice(any(),any())).thenReturn(Arrays.asList(
                    DeviceDO.builder().serialNumber("sn1").iccid("iccid").simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn2").iccid("iccid").simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn3").iccid("iccid").simThirdParty(0).build()
            ));


            when(userTierDeviceService.getActiveTierDeviceList(any(),any())).thenReturn(new HashSet<>(Arrays.asList("sn1","sn2")));

            when(appTierKeyTranslatedConfig.queryAppTierTranslatedKey(any(),any())).thenReturn("");
            when(appTierKeyTranslatedConfig.queryAppTierTranslatedKey(any(),any())).thenReturn("");

            when(userTierDeviceService.initAdditionalUserTierInfo(any(),any(),any())).thenReturn(Lists.newArrayList());
            UserTierDeviceInfo userTierDeviceInfo = UserTierDeviceInfo.builder()
                    .tierId(tierId)
                    .effectiveTime(userVipDO1.getEffectiveTime())
                    .endTime(userVipDO1.getEndTime())
                    .maxDeviceNum(tier.getMaxDeviceNum() == null ? 0: tier.getMaxDeviceNum())
                    .activeDeviceList(Arrays.asList(DeviceDO.builder().serialNumber("sn1").build(), DeviceDO.builder().serialNumber("sn2").build()))
                    .build();
            UserTierDeviceInfoResponse expectResult = UserTierDeviceInfoResponse.builder()
                    .userVipId(1L)
                    .rollingDay(30)
                    .supportRollingDay(Lists.newArrayList())
                    .serviceActiveDevice(userTierDeviceInfo)
                    .serviceAdditionList(Lists.newArrayList())
                    .build();

            when(paymentService.queryPaymentFlow("x1")).thenReturn(null);
            when(productService.queryProductById(1)).thenReturn(null);
            UserTierDeviceInfoResponse actualResult = userVipService.queryUserCurrentVipInfo(1,requestBase);
            Assert.assertEquals(expectResult.getServiceActiveDevice().getActiveDeviceList().size(),actualResult.getServiceActiveDevice().getActiveDeviceList().size());
        }
    }

    private Map<String, Map<String, String>> initCopyWrite(){
        Map<String, Map<String, String>> config = Maps.newHashMap();
        String language = "zh";
        Map<String, String> tierDescribecloudTitleMap = Maps.newHashMap();
        tierDescribecloudTitleMap.put(language,"tierDescribecloudTitleMap");
        Map<String, String> tierDescribecloudBodyMap = Maps.newHashMap();
        tierDescribecloudBodyMap.put(language,"tierDescribecloudBodyMap");
        Map<String, String> tierDescribecloudNotificationMap = Maps.newHashMap();
        tierDescribecloudNotificationMap.put(language,"tierDescribecloudNotificationMap");
        Map<String, String> tierDescribecloudActivityMap = Maps.newHashMap();
        tierDescribecloudActivityMap.put(language,"tierDescribecloudActivityMap");

        config.put(tier_message_tierDescribecloud_title,tierDescribecloudTitleMap);
        config.put(tier_message_tierDescribecloud_describe,tierDescribecloudBodyMap);
        config.put(tier_message_notification_title,tierDescribecloudNotificationMap);
        config.put(tier_message_activity_title,tierDescribecloudActivityMap);
        return config;
    }
    private Map<String,List<String>> initTierTermUrl(){
        Map<String,List<String>> map = Maps.newHashMap();
        map.put(TENANTID_VICOO,Arrays.asList("url1","url2","url3","url1"));
        return map;
    }

    @Test
    public void test_userVipRefundOrUpgrade(){
        String tradeNo = "tradeNo";
        Integer productId = 1;
        PaymentFlow paymentFlow = PaymentFlow.builder()
                .tradeNo(tradeNo)
                .productId(productId)
                .build();
        Integer userId = 1;

        {
            //userVip 记录不存在
            when(productService.queryProductById(any())).thenReturn(ProductDO.builder().type(ProductTypeEnums.PURCHASE.getCode()).build());
            when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(Lists.newArrayList());
            userVipService.userVipRefundOrUpgrade(paymentFlow,userId);
            verify(userVipActivateService, times(0)).activateUserVip( any(),anyInt());
        }
        {
            when(productService.queryProductById(any())).thenReturn(ProductDO.builder().type(ProductTypeEnums.PRODUCT_DEVICE_4G.getCode()).build());

            //商品不存在
            when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(
                    Arrays.asList(
                            UserVipDO.builder().tierId(100).build(),
                            UserVipDO.builder().tierId(1).tradeNo("a").build(),
                            UserVipDO.builder().tierId(1).effectiveTime(10).endTime(100).tradeNo(tradeNo).build(),
                            UserVipDO.builder().tierId(1).effectiveTime(100).endTime(200).tradeNo("b").build()
                    )
            );
            when(iUserVipDAO.updateUserVipTime(any())).thenReturn(1);
            when(iUserVipDAO.updateActiveExpire(any())).thenReturn(1);
            doNothing().when(userVipActivateService).activateUserVip(userId,(Integer) null);
            userVipService.userVipRefundOrUpgrade(paymentFlow,userId);
            verify(userVipActivateService, times(1)).activateUserVip(userId,(Integer) null);
        }
    }

    @Test
    @DisplayName("用户是否有vip")
    public void test_hasUserVipActive(){
        UserVipTier userVipTier = new UserVipTier();
        boolean actualResult = false;
        {
            List<Integer> activeTierIdList = Arrays.asList(1);
            userVipTier.setTierIdList(activeTierIdList);
            actualResult = userVipService.hasUserVipActive(userVipTier);
            Assert.assertTrue(actualResult);
        }
        {
            userVipTier = new UserVipTier();
            UserVipTier.TierDeviceInfo tierDeviceInfo = new UserVipTier.TierDeviceInfo();
            tierDeviceInfo.setHasVip(true);
            Map<Integer, UserVipTier.TierDeviceInfo> tierDeviceInfoMap = Maps.newHashMap();
            tierDeviceInfoMap.put(TierServiceTypeEnums.TIER_4G.getCode(),tierDeviceInfo);
            userVipTier.setTierDeviceInfoMap(tierDeviceInfoMap);
            actualResult = userVipService.hasUserVipActive(userVipTier);
            Assert.assertTrue(actualResult);
        }

        {
            userVipTier = new UserVipTier();

            userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
            actualResult = userVipService.hasUserVipActive(userVipTier);
            Assert.assertFalse(actualResult);
        }
    }

    @Test
    public void test_queryTierId4GList(){
        UserVipTier userVipTier = new UserVipTier();
        List<Integer> actualResult = Lists.newArrayList();
        List<Integer> exceptedResult = Lists.newArrayList();
        {
            userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
            actualResult = userVipService.queryTierId4GList(userVipTier);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            exceptedResult = Arrays.asList(1);
            UserVipTier.TierDeviceInfo tierDeviceInfo = new UserVipTier.TierDeviceInfo();
            tierDeviceInfo.setHasVip(true);
            tierDeviceInfo.setCurrentTierId(1);
            Map<Integer, UserVipTier.TierDeviceInfo> tierDeviceInfoMap = Maps.newHashMap();
            tierDeviceInfoMap.put(TierServiceTypeEnums.TIER_4G.getCode(),tierDeviceInfo);
            userVipTier.setTierDeviceInfoMap(tierDeviceInfoMap);
            actualResult = userVipService.queryTierId4GList(userVipTier);
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }




    @Test
    public void test_QueryDeviceCloudVipInfo() {
        Integer userId = 1;
        DeviceVipInfoRequest request = new DeviceVipInfoRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        request.setApp(app);
        request.setSerialNumber("sn");
        DeviceCloudVipInfoResponse exceptedResult;
        DeviceCloudVipInfoResponse actualResult;
        when(userSettingService.queryUserSetting(any())).thenReturn(UserSettingsDO.builder().supportFreeLicense(0).build());
        when(freeLicenseService.queryUserDeviceFreeTierBySn(any(),any())).thenReturn(null);
//        {
//            //4g 设备不展示入口
//            when(device4GService.queryDevice4GSimDO(request.getSerialNumber())).thenReturn(new Device4GSimDO());
//            exceptedResult = DeviceCloudVipInfoResponse.builder()
//                    .vipNotifyShow(false)
//                    .vipTag(UserCloudVipTagEnums.NO_CLOUD_TIER.getCode())
//                    .build();
//
//            actualResult = userVipService.queryDeviceCloudVipInfo(userId, request);
//            assertEquals(exceptedResult, actualResult);
//        }
        when(device4GService.queryDevice4GSimDO(request.getSerialNumber())).thenReturn(null);
        {
            when(userSettingService.queryUserSetting(any())).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());

            // 无云服务记录
            when(iUserVipDAO.queryUserVipInfo(any(), any(), any())).thenReturn(Collections.emptyList());
            when(userTierDeviceService.getDeviceCurrentTier(any(),any())).thenReturn(null);

            List<DeviceCloudVipInfoResponse.DeviceCloudVipFreeTier> tierList = Lists.newArrayList();

            when(appTierKeyTranslatedConfig.queryNoPlanKey(any())).thenReturn(TIER_CLOUD_NO_PLAN_NAME);

            exceptedResult = DeviceCloudVipInfoResponse.builder()
                    .vipNotifyShow(true)
                    .vipTag(NO_CLOUD_TIER.getCode())
                    .endTime(0)
                    .hasVip(false)
                    .freeLicenseId(0)
                    .tierNameKey(TIER_CLOUD_NO_PLAN_NAME)
                    .tierList(tierList)
                    .build();

            actualResult = userVipService.queryDeviceCloudVipInfo(userId, request);
            assertEquals(exceptedResult, actualResult);
        }
        when(userSettingService.queryUserSetting(any())).thenReturn(UserSettingsDO.builder().supportFreeLicense(0).build());
        {
            // 当前设备无套餐,  云服务已过期
            UserVipDO freeUserVip = new UserVipDO();
            freeUserVip.setTierId(100);
            freeUserVip.setEndTime((int) Instant.now().getEpochSecond() + 10000);
            when(iUserVipDAO.queryUserVipInfo(any(), any(), any())).thenReturn(Collections.singletonList(freeUserVip));
            when(userTierDeviceService.getDeviceCurrentTier(userId, request.getSerialNumber())).thenReturn(null);

            DeviceCloudVipInfoResponse.DeviceCloudVipFreeTier deviceCloudVipFreeTier = new DeviceCloudVipInfoResponse.DeviceCloudVipFreeTier();

            exceptedResult =  DeviceCloudVipInfoResponse.builder()
                .vipNotifyShow(true)
                .tierNameKey("service_status_no_plan")
                .endTime(0)
                .vipTag(CLOUD_TIER_EXPIRED.getCode())
                .tierList(Collections.singletonList(deviceCloudVipFreeTier))
                .build();

            actualResult = userVipService.queryDeviceCloudVipInfo(userId, request);
            assertEquals(exceptedResult.isVipNotifyShow(), actualResult.isVipNotifyShow());
            assertEquals(exceptedResult.getVipTag(), actualResult.getVipTag());
        }
        {
            UserVipDO expiredVip = new UserVipDO();
            expiredVip.setTierId(100);
            expiredVip.setEndTime((int) Instant.now().getEpochSecond() - 10000);
            when(iUserVipDAO.queryUserVipInfo(any(), any(), any())).thenReturn(Collections.singletonList(expiredVip));
            when(userTierDeviceService.getDeviceCurrentTier(userId, request.getSerialNumber())).thenReturn(100);

            when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(100).level(0).build());

            DeviceCloudVipInfoResponse response = userVipService.queryDeviceCloudVipInfo(userId, request);

            assertTrue(response.isVipNotifyShow());
            assertEquals(TIER_CLOUD_FREE_NAME, response.getTierNameKey());
            assertEquals(CLOUD_TIER_EXPIRE_SOON.getCode(), response.getVipTag());
        }
//        {
//            //vip 有效期大于7天
//            UserVipDO freeVip = new UserVipDO();
//            freeVip.setTierId(100);
//            freeVip.setEndTime((int) Instant.now().getEpochSecond() + 100000);
//
//            UserVipDO activeVipVip = new UserVipDO();
//            activeVipVip.setTierId(1);
//            activeVipVip.setEndTime((int) Instant.now().getEpochSecond() + 1000000);
//            when(iUserVipDAO.queryUserVipInfo(any(), any(), any())).thenReturn(Arrays.asList(activeVipVip,freeVip));
//            when(userTierDeviceService.getDeviceCurrentTier(userId, request.getSerialNumber())).thenReturn(1);
//            Tier currentTier = new Tier();
//            currentTier.setLevel(1);
//            currentTier.setTierType(0);
//            currentTier.setTierId(1);
//            when(tierService.queryTierById(1)).thenReturn(currentTier);
//
//            DeviceCloudVipInfoResponse response = userVipService.queryDeviceCloudVipInfo(userId, request);
//
//            assertEquals(true, response.isVipNotifyShow());
//            assertEquals(TierIdUtil.initTierNameKey(currentTier), response.getTierNameKey());
//            assertEquals(CLOUD_TIER_NO_TAG.getCode(), response.getVipTag());
//        }
//        {
//            //vip 有效期小于7天。续订中
//            UserVipDO freeVip = new UserVipDO();
//            freeVip.setTierId(100);
//            freeVip.setEndTime((int) Instant.now().getEpochSecond() + 100000);
//
//            UserVipDO activeVipVip = new UserVipDO();
//            activeVipVip.setTierId(1);
//            activeVipVip.setEndTime((int) Instant.now().getEpochSecond() + 100000);
//            activeVipVip.setOrderId(1L);
//
//            when(iUserVipDAO.queryUserVipInfo(any(), any(), any())).thenReturn(Arrays.asList(activeVipVip,freeVip));
//            when(userTierDeviceService.getDeviceCurrentTier(userId, request.getSerialNumber())).thenReturn(1);
//            Tier currentTier = new Tier();
//            currentTier.setLevel(1);
//            currentTier.setTierType(0);
//            currentTier.setTierId(1);
//            when(tierService.queryTierById(1)).thenReturn(currentTier);
//
//            when(orderService.verifyOrderSubStatus(any())).thenReturn(true);
//
//            DeviceCloudVipInfoResponse response = userVipService.queryDeviceCloudVipInfo(userId, request);
//
//            assertEquals(true, response.isVipNotifyShow());
//            assertEquals(TierIdUtil.initTierNameKey(currentTier), response.getTierNameKey());
//            assertEquals(CLOUD_TIER_NO_TAG.getCode(), response.getVipTag());
//        }
//        {
//            //vip 有效期小于7天。非续订中
//            UserVipDO freeVip = new UserVipDO();
//            freeVip.setTierId(100);
//            freeVip.setEndTime((int) Instant.now().getEpochSecond() + 100000);
//
//            UserVipDO activeVipVip = new UserVipDO();
//            activeVipVip.setTierId(1);
//            activeVipVip.setEndTime((int) Instant.now().getEpochSecond() + 100000);
//            activeVipVip.setOrderId(1L);
//
//            when(iUserVipDAO.queryUserVipInfo(any(), any(), any())).thenReturn(Arrays.asList(activeVipVip,freeVip));
//            when(userTierDeviceService.getDeviceCurrentTier(userId, request.getSerialNumber())).thenReturn(1);
//            Tier currentTier = new Tier();
//            currentTier.setLevel(1);
//            currentTier.setTierType(0);
//            currentTier.setTierId(1);
//            when(tierService.queryTierById(1)).thenReturn(currentTier);
//
//            when(orderService.verifyOrderSubStatus(any())).thenReturn(false);
//
//            DeviceCloudVipInfoResponse response = userVipService.queryDeviceCloudVipInfo(userId, request);
//
//            assertEquals(true, response.isVipNotifyShow());
//            assertEquals(TierIdUtil.initTierNameKey(currentTier), response.getTierNameKey());
//            assertEquals(CLOUD_TIER_RENEW.getCode(), response.getVipTag());
//        }
        {
            //vip 有效期小于7天。非续订中
            UserVipDO freeVip = new UserVipDO();
            freeVip.setTierId(100);
            freeVip.setEndTime((int) Instant.now().getEpochSecond() - 100000);

            UserVipDO activeVipVip = new UserVipDO();
            activeVipVip.setTierId(1);
            activeVipVip.setEndTime((int) Instant.now().getEpochSecond() + 100000);
            activeVipVip.setOrderId(1L);

            when(iUserVipDAO.queryUserVipInfo(any(), any(), any())).thenReturn(Arrays.asList(activeVipVip,freeVip));
            when(userTierDeviceService.getDeviceCurrentTier(userId, request.getSerialNumber())).thenReturn(1);
            Tier currentTier = new Tier();
            currentTier.setLevel(1);
            currentTier.setTierType(0);
            currentTier.setTierId(1);
            when(tierService.queryTierById(1)).thenReturn(currentTier);

            when(orderService.verifyOrderSubStatus(any())).thenReturn(false);

            DeviceCloudVipInfoResponse response = userVipService.queryDeviceCloudVipInfo(userId, request);

            assertTrue(response.isVipNotifyShow());
            assertEquals(CLOUD_TIER_EXPIRE_SOON.getCode(), response.getVipTag());
        }
    }

    @Test
    public void test_initUserVipTierInfo(){
        Integer userId = 1;
        Integer tierServiceType = TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode();
        List<UserVipDO> userVipDOList = Lists.newArrayList();
        Integer currentTime = (int)Instant.now().getEpochSecond();

        when(tierService.queryTierById(any())).thenReturn(Tier.builder().maxDeviceNum(1).build());

        {
            when(iUserVipDAO.queryLastUserVipInfo(any(),any())).thenReturn(null);
            UserVipTier userVipTier = userVipService.initUserVipTierInfo(userId,tierServiceType,userVipDOList);
            Assert.assertFalse(userVipTier.isVip());
        }
        {
            when(iUserVipDAO.queryLastUserVipInfo(any(),any())).thenReturn(UserVipDO.builder().tierId(100).endTime(currentTime + 1*24*60*60).build());
            when(iOrderDAO.queryByOrderId(any())).thenReturn(OrderDO.builder().subType(OrderSubTypeEnums.SUBSCRIBE.getCode()).build());
            UserVipTier userVipTier = userVipService.initUserVipTierInfo(userId,tierServiceType,userVipDOList);
            Assert.assertTrue(userVipTier.isVip());
        }
        {
            userVipDOList = Arrays.asList(UserVipDO.builder().id(1L).orderId(1L).endTime(currentTime).tierId(1).rollingDay(1).build());
            when(orderService.verifyOrderSub(any())).thenReturn(true);
            UserVipTier userVipTier = userVipService.initUserVipTierInfo(userId,tierServiceType,userVipDOList);
            Assert.assertTrue(userVipTier.isVip());
        }
    }

    @Test
    public void test_getAbResultMap(){
        {
            Integer userId = 1;
            AppRequestBase request = new AppRequestBase();
            AppInfo app = new AppInfo();
            app.setTenantId(TENANTID_VICOO);
            request.setApp(app);
            Map<String, Integer> ab = new HashMap<>();
            ab.put("1", 1);
            when(abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false).abFeatureSimpleResultList(ab)
                    .build());
            when(abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(true).abFeatureSimpleResultList(ab)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            UserVipTier userVipTier = UserVipTier.builder().build();
            assertNotNull(userVipService.getAbResultMap(userId, request, userVipTier));
        }

        {
            Integer userId = 1;
            AppRequestBase request = new AppRequestBase();
            AppInfo app = new AppInfo();
            app.setTenantId(TENANTID_VICOO);
            request.setApp(app);
            Map<String, Integer> ab = new HashMap<>();
            ab.put("1", 1);
            when(abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(true).isFreeTrial(false).abFeatureSimpleResultList(ab)
                    .build());
            when(abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(true).abFeatureSimpleResultList(ab)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<Integer, UserVipTier.TierDeviceInfo> tierDeviceInfoMap = new HashMap<>();
            UserVipTier.TierDeviceInfo tierDeviceInfo = new UserVipTier.TierDeviceInfo();
            tierDeviceInfo.setHasReceive(true);
            tierDeviceInfoMap.put(0, tierDeviceInfo);
            tierDeviceInfoMap.put(1, tierDeviceInfo);
            UserVipTier userVipTier = UserVipTier.builder().tierDeviceInfoMap(tierDeviceInfoMap).build();

            assertNotNull(userVipService.getAbResultMap(userId, request, userVipTier));
        }
        {
            Integer userId = 1;
            AppRequestBase request = new AppRequestBase();
            AppInfo app = new AppInfo();
            app.setTenantId(TENANTID_VICOO);
            request.setApp(app);
            Map<String, Integer> ab = new HashMap<>();
            ab.put("1", 1);
            when(abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false).abFeatureSimpleResultList(ab)
                    .build());
            when(abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(true).abFeatureSimpleResultList(ab)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<Integer, UserVipTier.TierDeviceInfo> tierDeviceInfoMap = new HashMap<>();
            UserVipTier.TierDeviceInfo tierDeviceInfo = new UserVipTier.TierDeviceInfo();
            tierDeviceInfo.setHasReceive(true);
            tierDeviceInfoMap.put(0, tierDeviceInfo);
            tierDeviceInfoMap.put(1, tierDeviceInfo);
            UserVipTier userVipTier = UserVipTier.builder().tierDeviceInfoMap(tierDeviceInfoMap).build();
            assertNotNull(userVipService.getAbResultMap(userId, request, userVipTier));
        }

    }

    public void testQueryBlacklist_ValidUsers() {
        when(blacklistConfig.queryBlacklist()).thenCallRealMethod();
        doCallRealMethod().when(blacklistConfig).setUsers(anyString());

        // 设置黑名单用户列表
        blacklistConfig.setUsers("1, 2, 3");

        // 执行方法
        Set<String> result = blacklistConfig.queryBlacklist();

        // 验证结果
        assertEquals(3, result.size());
    }

    @Test
    public void testQueryBlacklist_UsersIsNull() {
        // 设置 users 为 null
        blacklistConfig.setUsers(null);

        // 执行方法
        Set<String> result = blacklistConfig.queryBlacklist();

        // 验证结果
        assertNotNull(result);
    }

    @Test
    public void testQueryBlacklist_UsersIsEmpty() {
        // 设置 users 为空字符串
        blacklistConfig.setUsers("");

        // 执行方法
        Set<String> result = blacklistConfig.queryBlacklist();

        // 验证结果
        assertNotNull(result);
    }
}
