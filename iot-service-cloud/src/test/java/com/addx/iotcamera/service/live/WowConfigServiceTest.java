package com.addx.iotcamera.service.live;

import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceGlobal;
import com.addx.iotcamera.bean.domain.DeviceRequestDO;
import com.addx.iotcamera.config.Wowconfig;
import com.addx.iotcamera.config.device.DeviceDtimConfig;
import com.addx.iotcamera.config.device.DeviceGlobalConfig;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UdpService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class WowConfigServiceTest {
    @InjectMocks
    private UdpService udpService;

    @Mock
    private DeviceModelConfigService deviceModelConfigService;

    @Mock
    private Wowconfig wowconfig;

    @Mock
    private DeviceService deviceService;

    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private RedisService redisService;

    @Mock
    private DeviceDtimConfig deviceDtimConfig;

    @Mock
    private IKissService kissService;

    @Mock
    private DeviceGlobalConfig deviceGlobalConfig;


    @Before
    public void setUp() throws Exception {
        wowconfig = new Wowconfig();
    }


    @Test
    public void test() throws MqttException, IdNotSetException {
        String serialNumber = "serialNumber";
        String modelNo = "CG1";


        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setSerialNumber(serialNumber);
        deviceDO.setActivated(1);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(modelNo);

        CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportKeepAliveProtocol("udp");
        when(deviceInfoService.getDeviceSupport(any())).thenReturn(cloudDeviceSupport);

        when(redisService.getHashFieldValue(any(),any())).thenReturn(null);

        when(kissService.getKissParams(any(),any(),any())).thenReturn(null);


        Result expectedResult = Result.OperationResult(false);


        DeviceRequestDO sleepRequest = new DeviceRequestDO();
        sleepRequest.setSerialNumber(serialNumber);
        sleepRequest.setRequestId(1);

        when(deviceDtimConfig.getConfig()).thenReturn(Maps.newHashMap());
        when(deviceDtimConfig.getDefaultDtim()).thenReturn(5);

        when(deviceGlobalConfig.getConfig()).thenReturn(this.queryGlobalConfig(modelNo));

        Result actualResult = udpService.handleWowConfigRequest(sleepRequest);
        assertEquals(expectedResult, actualResult);
    }


    private Map<String, DeviceGlobal> queryGlobalConfig(String modelNo){
        Map<String, DeviceGlobal> map = Maps.newHashMap();
        DeviceGlobal deviceGlobal = new DeviceGlobal();
        deviceGlobal.setInterval(1);
        deviceGlobal.setTimeout(1);
        deviceGlobal.setModelNo(modelNo);
        map.put(modelNo,deviceGlobal);
        return map;
    }
}
