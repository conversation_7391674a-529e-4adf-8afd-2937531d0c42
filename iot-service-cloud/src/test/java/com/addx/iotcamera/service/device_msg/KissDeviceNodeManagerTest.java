package com.addx.iotcamera.service.device_msg;

import com.addx.iotcamera.bean.device_msg.KissDeviceNode;
import com.addx.iotcamera.bean.device_msg.KissDeviceStatus;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.SpringContextUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.util.*;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest(SpringContextUtil.class)
public class KissDeviceNodeManagerTest {

    @InjectMocks
    private KissDeviceNodeManager kissDeviceNodeManager;
    @Mock
    private StringRedisTemplate businessRedisTemplateClusterClient;
    @Mock
    RedisService redisService;
    @Mock
    private HashOperations oldHashOperations;
    @Mock
    private RedisOperations redisOperations;
    @Mock
    private HashOperations hashOperations;
    @Mock
    private HashOperations hashOperations2;

    private Map<String, Map<String, Object>> mockRedis = new LinkedHashMap<>();

    @Before
    public void before() {
        PowerMockito.mockStatic(SpringContextUtil.class);
        when(SpringContextUtil.getBean(KissDeviceNodeManager.class)).thenReturn(kissDeviceNodeManager);
        // org.springframework.data.redis.core.RedisTemplate.executePipelined(org.springframework.data.redis.core.SessionCallback<?>, org.springframework.data.redis.serializer.RedisSerializer<?>)
        when(businessRedisTemplateClusterClient.executePipelined((SessionCallback) any(), (RedisSerializer) any())).thenAnswer(it -> {
            SessionCallback<Object> sessionCallback = (SessionCallback) it.getArgument(0);
            sessionCallback.execute(redisOperations);
            return null;
        });
        when(redisOperations.opsForHash()).thenReturn(hashOperations);
        doAnswer(it -> {
            final String key = it.getArgument(0);
            final Map map = it.getArgument(1);
            mockRedis.computeIfAbsent(key, it2 -> new LinkedHashMap<>()).putAll(map);
            return null;
        }).when(hashOperations).putAll(any(), anyMap());
        // redisTemplate.opsForHash().entries(redisKey)
        when(businessRedisTemplateClusterClient.opsForHash()).thenReturn(hashOperations2);
        when(hashOperations2.entries(any())).thenAnswer(it -> mockRedis.get(it.getArgument(0)));
    }

    @Test
    public void test() {
        final String sn1 = OpenApiUtil.shortUUID();
        final String sn2 = OpenApiUtil.shortUUID();
        final String sn3 = OpenApiUtil.shortUUID();
        final long time1 = System.currentTimeMillis();
        final long time2 = time1 + 1L;
        {
            List<KissDeviceNode> nodes = new LinkedList<>();
            nodes.add(new KissDeviceNode().setDeviceSn(sn1).setKissIp("************").setDeviceStatus(KissDeviceStatus.normal).setLastUpdateTime(time1));
            nodes.add(new KissDeviceNode().setDeviceSn(sn2).setKissIp("************").setDeviceStatus(KissDeviceStatus.dormant).setLastUpdateTime(time1));
            nodes.add(new KissDeviceNode().setDeviceSn(sn3).setKissIp("************").setDeviceStatus(KissDeviceStatus.offline).setLastUpdateTime(time1));
            kissDeviceNodeManager.putAll(nodes);
        }
        {
            final String redisKey = "deviceOnKiss:" + sn1;
            final Map<String, Object> map = mockRedis.getOrDefault(redisKey, new LinkedHashMap<>());
            Assert.assertEquals(time1 + "", map.get("onlineTime"));
            Assert.assertEquals(null, map.get("offlineTime"));
            Assert.assertEquals("normal", map.get("deviceStatus"));
            Assert.assertEquals("************", map.get("onlineKissIp"));
            Assert.assertEquals(null, map.get("offlineKissIp"));

            final KissDeviceNode kissDeviceNode = kissDeviceNodeManager.get(sn1);
            Assert.assertEquals(KissDeviceStatus.normal, kissDeviceNode.getDeviceStatus());
            Assert.assertEquals("************", kissDeviceNode.getKissIp());
            Assert.assertEquals(time1, (long) kissDeviceNode.getLastUpdateTime());
        }
        {
            final String redisKey = "deviceOnKiss:" + sn2;
            final Map<String, Object> map = mockRedis.getOrDefault(redisKey, new LinkedHashMap<>());
            Assert.assertEquals(time1 + "", map.get("onlineTime"));
            Assert.assertEquals(null, map.get("offlineTime"));
            Assert.assertEquals("dormant", map.get("deviceStatus"));
            Assert.assertEquals("************", map.get("onlineKissIp"));
            Assert.assertEquals(null, map.get("offlineKissIp"));

            final KissDeviceNode kissDeviceNode = kissDeviceNodeManager.get(sn2);
            Assert.assertEquals(KissDeviceStatus.dormant, kissDeviceNode.getDeviceStatus());
            Assert.assertEquals("************", kissDeviceNode.getKissIp());
            Assert.assertEquals(time1, (long) kissDeviceNode.getLastUpdateTime());
        }
        {
            final String redisKey = "deviceOnKiss:" + sn3;
            final Map<String, Object> map = mockRedis.getOrDefault(redisKey, new LinkedHashMap<>());
            Assert.assertEquals(null, map.get("onlineTime"));
            Assert.assertEquals(time1 + "", map.get("offlineTime"));
            Assert.assertEquals("offline", map.get("deviceStatus"));
            Assert.assertEquals(null, map.get("onlineKissIp"));
            Assert.assertEquals("************", map.get("offlineKissIp"));

            final KissDeviceNode kissDeviceNode = kissDeviceNodeManager.get(sn3);
            Assert.assertEquals(KissDeviceStatus.offline, kissDeviceNode.getDeviceStatus());
            Assert.assertEquals("************", kissDeviceNode.getKissIp());
            Assert.assertEquals(time1, (long) kissDeviceNode.getLastUpdateTime());
        }
        {
            List<KissDeviceNode> nodes = new LinkedList<>();
            nodes.add(new KissDeviceNode().setDeviceSn(sn1).setKissIp("************").setDeviceStatus(KissDeviceStatus.offline).setLastUpdateTime(time2));
            nodes.add(new KissDeviceNode().setDeviceSn(sn2).setKissIp("************").setDeviceStatus(KissDeviceStatus.normal).setLastUpdateTime(time2));
            nodes.add(new KissDeviceNode().setDeviceSn(sn3).setKissIp("************").setDeviceStatus(KissDeviceStatus.dormant).setLastUpdateTime(time2));
            kissDeviceNodeManager.putAll(nodes);
        }
        {
            final String redisKey = "deviceOnKiss:" + sn1;
            final Map<String, Object> map = mockRedis.getOrDefault(redisKey, new LinkedHashMap<>());
            Assert.assertEquals(time1 + "", map.get("onlineTime"));
            Assert.assertEquals(time2 + "", map.get("offlineTime"));
            Assert.assertEquals("offline", map.get("deviceStatus"));
            Assert.assertEquals("************", map.get("onlineKissIp"));
            Assert.assertEquals("************", map.get("offlineKissIp"));

            final KissDeviceNode kissDeviceNode = kissDeviceNodeManager.get(sn1);
            Assert.assertEquals(KissDeviceStatus.offline, kissDeviceNode.getDeviceStatus());
            Assert.assertEquals("************", kissDeviceNode.getKissIp());
            Assert.assertEquals(time2, (long) kissDeviceNode.getLastUpdateTime());
        }
        {
            final String redisKey = "deviceOnKiss:" + sn2;
            final Map<String, Object> map = mockRedis.get(redisKey);
            Assert.assertEquals(null, map.get("offlineTime"));
            Assert.assertEquals(time2 + "", map.get("onlineTime"));
            Assert.assertEquals("normal", map.get("deviceStatus"));
            Assert.assertEquals(null, map.get("offlineKissIp"));
            Assert.assertEquals("************", map.get("onlineKissIp"));

            final KissDeviceNode kissDeviceNode = kissDeviceNodeManager.get(sn2);
            Assert.assertEquals(KissDeviceStatus.normal, kissDeviceNode.getDeviceStatus());
            Assert.assertEquals("************", kissDeviceNode.getKissIp());
            Assert.assertEquals(time2, (long) kissDeviceNode.getLastUpdateTime());
        }
        {
            final String redisKey = "deviceOnKiss:" + sn3;
            final Map<String, Object> map = mockRedis.get(redisKey);
            Assert.assertEquals(time1 + "", map.get("offlineTime"));
            Assert.assertEquals(time2 + "", map.get("onlineTime"));
            Assert.assertEquals("dormant", map.get("deviceStatus"));
            Assert.assertEquals("************", map.get("offlineKissIp"));
            Assert.assertEquals("************", map.get("onlineKissIp"));

            final KissDeviceNode kissDeviceNode = kissDeviceNodeManager.get(sn3);
            Assert.assertEquals(KissDeviceStatus.dormant, kissDeviceNode.getDeviceStatus());
            Assert.assertEquals("************", kissDeviceNode.getKissIp());
            Assert.assertEquals(time2, (long) kissDeviceNode.getLastUpdateTime());
        }

    }

    @Test
    public void test_queryNewOrOld() {

        when(businessRedisTemplateClusterClient.opsForHash()).thenReturn(hashOperations);
        {
            final String redisKey = "deviceOnKiss:" + OpenApiUtil.shortUUID();
            when(hashOperations.entries(redisKey)).thenReturn(Collections.emptyMap());
            when(oldHashOperations.entries(redisKey)).thenReturn(Collections.emptyMap());
            Map<Object, Object> result = kissDeviceNodeManager.queryFromNewOrOld(redisKey);
            Assert.assertEquals(Collections.emptyMap(), result);
        }
        {
            final String redisKey = "deviceOnKiss:" + OpenApiUtil.shortUUID();
            when(hashOperations.entries(redisKey)).thenThrow(new RuntimeException());
            when(oldHashOperations.entries(redisKey)).thenReturn(Collections.emptyMap());
            Map<Object, Object> result = kissDeviceNodeManager.queryFromNewOrOld(redisKey);
            Assert.assertEquals(Collections.emptyMap(), result);
        }
        {
            final String redisKey = "deviceOnKiss:" + OpenApiUtil.shortUUID();
            when(hashOperations.entries(redisKey)).thenReturn(Collections.emptyMap());
            when(oldHashOperations.entries(redisKey)).thenThrow(new RuntimeException());
            Map<Object, Object> result = kissDeviceNodeManager.queryFromNewOrOld(redisKey);
            Assert.assertEquals(Collections.emptyMap(), result);
        }
        {
            final String redisKey = "deviceOnKiss:" + OpenApiUtil.shortUUID();
            when(hashOperations.entries(redisKey)).thenThrow(new RuntimeException());
            when(oldHashOperations.entries(redisKey)).thenThrow(new RuntimeException());
            Map<Object, Object> result = kissDeviceNodeManager.queryFromNewOrOld(redisKey);
            Assert.assertEquals(Collections.emptyMap(), result);
        }
        {
            final String redisKey = "deviceOnKiss:" + OpenApiUtil.shortUUID();
            Map<Object, Object> map = new LinkedHashMap<>();
            map.put("onlineKissIp", "127.0.0.1");
            when(hashOperations.entries(redisKey)).thenReturn(map);
            when(oldHashOperations.entries(redisKey)).thenReturn(Collections.emptyMap());
            Map<Object, Object> result = kissDeviceNodeManager.queryFromNewOrOld(redisKey);
            Assert.assertEquals(map, result);
        }
        {
            final String redisKey = "deviceOnKiss:" + OpenApiUtil.shortUUID();
            Map<Object, Object> map = new LinkedHashMap<>();
            map.put("onlineKissIp", "127.0.0.1");
            map.put("oldData", new JSONObject().fluentPut("onlineKissIp", "*********"));
            when(hashOperations.entries(redisKey)).thenReturn(map);
            when(oldHashOperations.entries(redisKey)).thenReturn(Collections.emptyMap());
            Map<Object, Object> result = kissDeviceNodeManager.queryFromNewOrOld(redisKey);
            Map<Object, Object> expectMap = new LinkedHashMap<>();
            expectMap.put("onlineKissIp", "*********");
            Assert.assertEquals(map, result);
        }
        {
            final String redisKey = "deviceOnKiss:" + OpenApiUtil.shortUUID();
            Map<Object, Object> map = new LinkedHashMap<>();
            map.put("onlineKissIp", "127.0.0.1");
            map.put("oldData", new JSONObject().fluentPut("onlineKissIp", "*********")
                    .fluentPut("deviceStatus", "normal"));
            when(hashOperations.entries(redisKey)).thenReturn(Collections.emptyMap());
            when(businessRedisTemplateClusterClient.opsForHash()).thenReturn(oldHashOperations);
            when(oldHashOperations.entries(redisKey)).thenReturn(map);
            Map<Object, Object> result = kissDeviceNodeManager.queryFromNewOrOld(redisKey);
            Map<Object, Object> expectMap = new LinkedHashMap<>();
            expectMap.put("onlineKissIp", "127.0.0.1");
            expectMap.put("deviceStatus", "normal");
            // Assert.assertEquals(map, result);
        }
        {
            final String redisKey = "deviceOnKiss:" + OpenApiUtil.shortUUID();
            Map<Object, Object> newMap = new LinkedHashMap<>();
            newMap.put("oldData", "[]");
            when(hashOperations.entries(redisKey)).thenReturn(Collections.emptyMap());
            when(oldHashOperations.entries(redisKey)).thenReturn(Collections.emptyMap());
            Map<Object, Object> result = kissDeviceNodeManager.queryFromNewOrOld(redisKey);
            Assert.assertEquals(Collections.emptyMap(), result);
        }

    }

}
