package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.app.device.DeviceBindInfoDO;
import com.addx.iotcamera.bean.warranty.WarrantyOrderDO;
import com.addx.iotcamera.bean.warranty.WarrantyOrderRequest;
import com.addx.iotcamera.dao.factory.DeviceBindInfoDao;
import com.addx.iotcamera.dao.factory.DeviceWarrantyOrderDAO;
import com.addx.iotcamera.service.factory.DeviceBindInfoService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DeviceBindInfoServiceTest {

    @InjectMocks
    private DeviceBindInfoService deviceBindInfoService;

    @Mock
    private DeviceBindInfoDao deviceBindInfoDao;

    @Mock
    private DeviceWarrantyOrderDAO deviceWarrantyOrderDAO;

    private static final int CURRENT_TIME = (int) (System.currentTimeMillis() / 1000);

    @Test
    public void testQueryAllDeviceBindInfoDO_withSupportedModel() {
        // Given: userSn found, no parentUniqCode, modelNo in SUPPORTED_MODELS_SET
        List<String> serialNumberList = Arrays.asList("serial123");
        when(deviceBindInfoDao.findUserSnBySerialNumber("serial123")).thenReturn("userSn123");
        when(deviceBindInfoDao.findParentUniqCodeByUniqCode("userSn123")).thenReturn(null);
        when(deviceBindInfoDao.findModelNoByUserSn("userSn123")).thenReturn("CX124A");
        when(deviceBindInfoDao.findIconUrlByModelNo("CX124A")).thenReturn("iconUrl123");

        WarrantyOrderDO warrantyOrderDO = WarrantyOrderDO.builder()
                .userSn("userSn123")
                .expirationTime(999999)
                .build();
        when(deviceWarrantyOrderDAO.getWarrantyOrder(eq("userSn123"))).thenReturn(warrantyOrderDO);

        // When
        List<DeviceBindInfoDO> result = deviceBindInfoService.queryAllDeviceBindInfoDO(serialNumberList);

        // Then
        assertEquals(1, result.size());
        DeviceBindInfoDO bindInfo = result.get(0);
        assertEquals("CX124A", bindInfo.getModelNo());
        assertEquals("iconUrl123", bindInfo.getIcon());
        assertEquals(true, bindInfo.getWarrantyRegistered());
        assertEquals(999999, bindInfo.getExpiredTimeStamp().intValue());
    }

    @Test
    public void testQueryAllDeviceBindInfoDO_withUnsupportedModel() {
        // Given: userSn found, no parentUniqCode, modelNo not in SUPPORTED_MODELS_SET
        List<String> serialNumberList = Arrays.asList("serial456");
        when(deviceBindInfoDao.findUserSnBySerialNumber("serial456")).thenReturn("userSn456");
        when(deviceBindInfoDao.findParentUniqCodeByUniqCode("userSn456")).thenReturn(null);
        when(deviceBindInfoDao.findModelNoByUserSn("userSn456")).thenReturn("XYZ123");

        // When
        List<DeviceBindInfoDO> result = deviceBindInfoService.queryAllDeviceBindInfoDO(serialNumberList);

        // Then: No device added since model is not supported
        assertTrue(result.isEmpty());
    }

    @Test
    public void testQueryAllDeviceBindInfoDO_withNoUserSn() {
        // Given: userSn not found
        List<String> serialNumberList = Arrays.asList("serial789");
        when(deviceBindInfoDao.findUserSnBySerialNumber("serial789")).thenReturn(null);

        // When
        List<DeviceBindInfoDO> result = deviceBindInfoService.queryAllDeviceBindInfoDO(serialNumberList);

        // Then: No device added since userSn is null
        assertTrue(result.isEmpty());
    }

    @Test
    public void testQueryAllDeviceBindInfoDO_withPriorityModel() {
        // Given: userSn found, parentUniqCode with device bind info
        List<String> serialNumberList = Arrays.asList("serial123");
        when(deviceBindInfoDao.findUserSnBySerialNumber("serial123")).thenReturn("userSn123");
        when(deviceBindInfoDao.findParentUniqCodeByUniqCode("userSn123")).thenReturn("parentUniq123");

        DeviceBindInfoDO priorityBindInfo = DeviceBindInfoDO.builder().modelNo("SK0111W1").uniqCode("uniq123").build();
        List<DeviceBindInfoDO> bindInfoList = Arrays.asList(priorityBindInfo);
        when(deviceBindInfoDao.findDeviceBindInfoByParentUniqCode("parentUniq123")).thenReturn(bindInfoList);

        when(deviceBindInfoDao.findIconUrlByModelNo("SK0111W1")).thenReturn("iconUrl123");
        when(deviceBindInfoDao.findSaleNameByModelNo("SK0111W1")).thenReturn("SaleName123");

        // When
        List<DeviceBindInfoDO> result = deviceBindInfoService.queryAllDeviceBindInfoDO(serialNumberList);

        // Then: The device with priority model is selected
        assertEquals(1, result.size());
        DeviceBindInfoDO bindInfo = result.get(0);
        assertEquals("SK0111W1", bindInfo.getModelNo());
        assertEquals("iconUrl123", bindInfo.getIcon());
        assertEquals("SaleName123", bindInfo.getProductName());
    }

    @Test
    public void testInsertWarrantyOrders_withValidData() {
        // Given: Valid warranty order
        WarrantyOrderRequest.Order.Product product = new WarrantyOrderRequest.Order.Product();
        product.setUserSn("userSn123");
        product.setModelNo("CX124A");

        WarrantyOrderRequest.Order order = new WarrantyOrderRequest.Order();
        order.setOrderNo("order123");
        order.setPurchasedAt("2024-09-19");
        order.setProducts(Arrays.asList(product));

        List<WarrantyOrderRequest.Order> orderList = Arrays.asList(order);

        when(deviceWarrantyOrderDAO.getWarrantyOrder(eq("userSn123"))).thenReturn(null);

        // When
        deviceBindInfoService.insertWarrantyOrders(orderList, 1);

        // Then: The warranty order should be inserted
        verify(deviceWarrantyOrderDAO, times(1)).insertWarrantyOrder(any(WarrantyOrderDO.class));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testInsertWarrantyOrders_withMissingOrderNo() {
        // Given: Missing order number
        WarrantyOrderRequest.Order order = new WarrantyOrderRequest.Order();
        order.setOrderNo(null);  // Missing order number
        order.setPurchasedAt("2024-09-19");
        order.setProducts(Arrays.asList(new WarrantyOrderRequest.Order.Product()));

        List<WarrantyOrderRequest.Order> orderList = Arrays.asList(order);

        // When
        deviceBindInfoService.insertWarrantyOrders(orderList, 1);

        // Then: Expect IllegalArgumentException
    }

    @Test(expected = IllegalArgumentException.class)
    public void testInsertWarrantyOrders_withExistingWarrantyOrder() {
        // Given: Existing unexpired warranty order
        WarrantyOrderRequest.Order.Product product = new WarrantyOrderRequest.Order.Product();
        product.setUserSn("userSn123");
        product.setModelNo("CX124A");

        WarrantyOrderRequest.Order order = new WarrantyOrderRequest.Order();
        order.setOrderNo("order123");
        order.setPurchasedAt("2024-09-19");
        order.setProducts(Arrays.asList(product));

        List<WarrantyOrderRequest.Order> orderList = Arrays.asList(order);

        when(deviceWarrantyOrderDAO.getWarrantyOrder(eq("userSn123"))).thenReturn(
                WarrantyOrderDO.builder().userSn("userSn123").expirationTime(CURRENT_TIME + 1000).build()
        );

        // When
        deviceBindInfoService.insertWarrantyOrders(orderList, 1);

        // Then: Expect IllegalArgumentException due to existing unexpired order
    }

    @Test(expected = IllegalArgumentException.class)
    public void testInsertWarrantyOrders_withEmptyUserSnOrModelNo() {
        // Given: Product with empty UserSn and ModelNo
        WarrantyOrderRequest.Order.Product productWithEmptyUserSn = new WarrantyOrderRequest.Order.Product();
        productWithEmptyUserSn.setUserSn(""); // Empty UserSn
        productWithEmptyUserSn.setModelNo("CX124A");

        WarrantyOrderRequest.Order.Product productWithEmptyModelNo = new WarrantyOrderRequest.Order.Product();
        productWithEmptyModelNo.setUserSn("userSn123");
        productWithEmptyModelNo.setModelNo(""); // Empty ModelNo

        WarrantyOrderRequest.Order order = new WarrantyOrderRequest.Order();
        order.setOrderNo("order123");
        order.setPurchasedAt("2024-09-19");
        order.setProducts(Arrays.asList(productWithEmptyUserSn, productWithEmptyModelNo));

        List<WarrantyOrderRequest.Order> orderList = Arrays.asList(order);

        // When
        deviceBindInfoService.insertWarrantyOrders(orderList, 1);

        // Then: Expect IllegalArgumentException due to empty UserSn or ModelNo
    }
}
