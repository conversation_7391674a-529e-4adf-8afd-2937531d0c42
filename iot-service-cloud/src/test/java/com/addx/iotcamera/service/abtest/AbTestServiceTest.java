package com.addx.iotcamera.service.abtest;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.response.user.ABTestResult;
import com.addx.iotcamera.bean.response.user.Product4GABTestResult;
import com.addx.iotcamera.dao.IProductDAO;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.ReportLogService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.abtest.model.AbFeatureSimpleResult;
import com.addx.iotcamera.service.abtest.model.UserTag;
import com.addx.iotcamera.service.user.UserSettingService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.addx.iotcamera.constants.AbTestConstants.YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT;
import static junit.framework.TestCase.assertEquals;
import static org.addx.iot.common.constant.AppConstants.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * description: abtest test
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2023/9/15 15:36
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class AbTestServiceTest {
    //需要是公网能访问到的地址，不然jenkins单测会失败
    public static final String STAGEING_AB_SERVER = "https://us-ab-management-api.addx.live/api/features/sdk-y7OgJgjDMoZKer";

    public static final String UNIT_TEST_FEATURE = "unit-test";
    @InjectMocks
    private AbTestService abTestService;

    @Mock
    private IOrderDAO iOrderDAO;

    @Mock
    private IProductDAO iProductDAO;

    @Mock
    private IUserDAO iUserDAO;

    @Mock
    private RedisService redisService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private UserSettingService userSettingService;

    @Mock
    private IUserVipDAO iUserVipDAO;

    @Mock
    private ReportLogService reportLogService;

    private static final String SEVEN_DAYS_FREE_TRIAL = "sevenDaysFreeTrial";
    private static final String ZERO_DAYS_EXPERIMENT_GROUP = "0";
    private static final String AWARENESS_CONTROL_GROUP = "1";
    private static final String SEVEN_DAYS_EXPERIMENT_GROUP = "7";


    @Test
    public void testGetFeatures() {
        when(redisService.get(any())).thenReturn(AbTestService.DEFAULT_JSON);
        // Act
        String result = abTestService.getFeatures();

        // Assert
        assertNotNull(result);
        assertEquals(AbTestService.DEFAULT_JSON, result);

        // Arrange
        abTestService.serverUrl = STAGEING_AB_SERVER;

        // Act
        result = abTestService.getFeatures();

        // Assert
        assertNotNull(result);
        // assertNotEquals(AbTestService.DEFAULT_JSON, result);
    }

    @Test
    public void testQueryBucketParam() {
        // Arrange
        Integer userId = 123;
        OrderDO order1 = new OrderDO();
        order1.setUserId(userId);
        order1.setOrderType(4);
        OrderDO order2 = new OrderDO();
        order2.setUserId(userId);
        order2.setOrderType(1);
        List<OrderDO> orderList = Arrays.asList(order1, order2);
        when(iOrderDAO.queryUserTestOrderList(userId)).thenReturn(orderList);
        when(iProductDAO.selectNewPackCount(userId)).thenReturn(1);
        when(iProductDAO.selectOldPackCount(userId)).thenReturn(1);
        User user = new User();
        //2023/11/5 03:53:15
        user.setRegistTime(1699127595);
        when(iUserDAO.getUserById(any())).thenReturn(user);
        UserTag expected = new UserTag();
        expected.setTested(true);
        expected.setPurchasedNew(true);
        expected.setPurchasedOld(true);
        expected.setNewUser(false);

        // Act
        UserTag actual = abTestService.queryUserTag(123);

        // Assert
        assertEquals(expected.getTested(), actual.getTested());
        assertEquals(expected.getNewUser(), actual.getNewUser());
        assertEquals(expected.getPurchasedOld(), actual.getPurchasedOld());
        assertEquals(expected.getPurchasedNew(), actual.getPurchasedNew());
    }

//    @Test
    public void testAllFeatureCheck() throws URISyntaxException, IOException, URISyntaxException, IOException {
        // Arrange
        String userId = "123";
       /*
          {
          "status": 200,
          "features": {
            "first-screen": {
              "defaultValue": false,
              "rules": [
                {
                  "condition": {
                    "purchased": false,
                    "tested": false
                  },
                  "coverage": 1,
                  "hashAttribute": "id",
                  "seed": "2ebd84cc-1076-4670-8956-667f5df67832",
                  "hashVersion": 2,
                  "variations": [
                    false,
                    true
                  ],
                  "weights": [
                    0.5,
                    0.5
                  ],
                  "key": "first-screen",
                  "meta": [
                    {
                      "key": "0"
                    },
                    {
                      "key": "1"
                    }
                  ],
                  "phase": "2"
                }
              ]
            },
            "vip-checkin": {
              "defaultValue": false,
              "rules": [
                {
                  "condition": {
                    "userId": 123
                  },
                  "force": true
                }
              ]
            }
          },
          "dateUpdated": "2023-09-18T02:54:42.781Z"
        }
        *
        * */
        abTestService.serverUrl = STAGEING_AB_SERVER;


        List<AbFeatureSimpleResult> result = abTestService.allFeatureCheck(userId);
        assertEquals(result.stream().filter(item->item.getFeatureId().equals(UNIT_TEST_FEATURE)).findFirst().get().getValue(), "true");

    }



    @Test
    public void test_singleFeatureCheck() throws URISyntaxException, IOException {
        // Arrange
        String userId = "123";
        String appVersion = "";
        abTestService.serverUrl = STAGEING_AB_SERVER;
        try{
            AbFeatureSimpleResult result = abTestService.singleFeatureCheck(userId, YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,APP_TYPE_IOS,APP_LANGUAGE_EN, TENANTID_VICOO, appVersion);

        }catch (Exception e){

        }
        // assertEquals(result.getValue(), "0");
    }

    @Test
    public void testExtractVersionAsInteger_ValidInput() {
        assertEquals(Integer.valueOf(22504), abTestService.extractVersionAsInteger("2.25.4(a45bfb)"));
        assertEquals(Integer.valueOf(11500), abTestService.extractVersionAsInteger("1.15.0"));
        assertEquals(Integer.valueOf(10501), abTestService.extractVersionAsInteger("1.5.1"));
        assertEquals(Integer.valueOf(20000), abTestService.extractVersionAsInteger("2.0.0"));

        // Case 1: Major version comparison
        assertTrue(abTestService.extractVersionAsInteger("1.5.1") < abTestService.extractVersionAsInteger("1.10.2"));
        assertTrue(abTestService.extractVersionAsInteger("3.0.0") > abTestService.extractVersionAsInteger("2.0.0"));

        // Case 2: Minor version comparison
        assertTrue(abTestService.extractVersionAsInteger("1.10.0") > abTestService.extractVersionAsInteger("1.9.9"));
        assertTrue(abTestService.extractVersionAsInteger("1.15.0") > abTestService.extractVersionAsInteger("1.5.0"));

        // Case 3: Patch version comparison
        assertTrue(abTestService.extractVersionAsInteger("1.0.1") > abTestService.extractVersionAsInteger("1.0.0"));
        assertTrue(abTestService.extractVersionAsInteger("1.0.10") > abTestService.extractVersionAsInteger("1.0.5"));

        // Case 4: Combined comparisons
        assertTrue(abTestService.extractVersionAsInteger("2.5.5") > abTestService.extractVersionAsInteger("2.5.4"));
        assertTrue(abTestService.extractVersionAsInteger("2.10.0") > abTestService.extractVersionAsInteger("2.9.9"));
        assertTrue(abTestService.extractVersionAsInteger("10.0.0") > abTestService.extractVersionAsInteger("9.99.99"));

        // Case 5: Edge cases
        assertTrue(abTestService.extractVersionAsInteger("0.0.1") > abTestService.extractVersionAsInteger("0.0.0"));
        assertTrue(abTestService.extractVersionAsInteger("99.99.99") > abTestService.extractVersionAsInteger("99.99.98"));
    }

    @Test
    public void testGetAwarenessFreeTrailDayAbResult_HitExperiment() {
        // Arrange
        Integer userId = 12345;
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        app.setAppType("iOS");
        app.setVersionName("3.26.0");
        request.setApp(app);

        abTestService.serverUrl = STAGEING_AB_SERVER;
        when(userRoleService.getSerialNumbersByUserId(userId)).thenReturn(new ArrayList<>());
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().userId(userId).supportFreeLicense(1).build());
        when(iUserVipDAO.queryUserVipInfo(userId, 0, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(new ArrayList<>());
        // Act
        ABTestResult result = abTestService.getAwarenessFreeTrailDayAbResult(userId, request);

        // Assert
        assertTrue(result.isExperimentSuccessful());
    }

    @Test
    public void testGetProduct4GABTestResult_notHit() {
        // Arrange
        Integer userId = 12345;
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("guard");
        app.setAppType("iOS");
        app.setVersionName("3.26.0");
        request.setApp(app);

        abTestService.serverUrl = STAGEING_AB_SERVER;
        // Act
        Product4GABTestResult product4GABTestResult = abTestService.getProduct4GABTestResult(userId, request);

        // Assert
        assertFalse(product4GABTestResult.isExperimentSuccessful());
    }

    @Test
    public void testGetProduct4GABTestResult_hit() {
        // Arrange
        Integer userId = 12345;
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        app.setAppType("iOS");
        app.setVersionName("3.26.0");
        request.setApp(app);

        abTestService.serverUrl = STAGEING_AB_SERVER;
        // Act
        Product4GABTestResult product4GABTestResult = abTestService.getProduct4GABTestResult(userId, request);

        // Assert
        assertTrue(product4GABTestResult.isExperimentSuccessful());
    }

    @Test
    public void testGetAwarenessFreeTrailDayAbResult_NotHitExperiment() {
        // Arrange
        Integer userId = 12345;
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("guard");
        app.setAppType("iOS");
        app.setVersionName("3.26.0");
        request.setApp(app);

        abTestService.serverUrl = STAGEING_AB_SERVER;
        when(userRoleService.getSerialNumbersByUserId(userId)).thenReturn(new ArrayList<>());
        when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().userId(userId).supportFreeLicense(1).build());
        when(iUserVipDAO.queryUserVipInfo(userId, 0, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(new ArrayList<>());
        // Act
        ABTestResult result = abTestService.getAwarenessFreeTrailDayAbResult(userId, request);

        // Assert
        assertTrue(result.isExperimentSuccessful());
    }

    @Test
    public void testParseValue_NormalInput() {
        {// Arrange
            String valueStr = "123.0";

            // Act
            Integer result = abTestService.parseValue(valueStr);

            // Assert
            assertEquals(Integer.valueOf(123), result);
        }

        {
            // Arrange
            String valueStr = null;

            // Act
            Integer result = abTestService.parseValue(valueStr);

            // Assert
            assertNull(result);
        }

        {
            // Arrange
            String valueStr = "";

            // Act
            Integer result = abTestService.parseValue(valueStr);

            // Assert
            assertNull(result);
        }
    }

    @Test
    public void testParseValue_InvalidInput() {
        // Arrange
        String valueStr = "invalid_number";

        // Act
        Integer result = abTestService.parseValue(valueStr);

        // Assert
        assertNull(result);
    }

}
