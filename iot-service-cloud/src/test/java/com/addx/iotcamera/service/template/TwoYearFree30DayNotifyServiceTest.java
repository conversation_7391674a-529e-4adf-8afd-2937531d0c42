package com.addx.iotcamera.service.template;

import com.addx.iotcamera.mq.MqSender;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class TwoYearFree30DayNotifyServiceTest {
    @InjectMocks
    private TwoYearFree30DayNotifyService twoYearFree30DayNotifyService;

    @Mock
    private MqSender mqSender;

    @Test
    public void test_saveSendList(){
        doNothing().when(mqSender).send(anyString(), anyInt(), any());

        twoYearFree30DayNotifyService.saveMessage(1);
    }
}
