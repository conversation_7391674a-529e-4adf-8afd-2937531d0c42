package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.device.model.DeviceModelBatteryDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class BatteryConfigPushServiceTest {

    @Mock
    private UserRoleService userRoleService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private OpenApiConfigService openApiConfigService;
    @InjectMocks
    private BatteryConfigPushService batteryConfigPushService;

    @Before
    public void before() {

    }

    @Test
    public void test_publishBatteryConfig() {
        DeviceModelBatteryDO config = new DeviceModelBatteryDO();
        config.setBatteryCode("123");
        config.setVoltameterType(456);

        List<CloudDeviceSupport> cloudDeviceSupports = new ArrayList<>();
        cloudDeviceSupports.add(CloudDeviceSupport.builder().batteryCode(config.getBatteryCode()).voltameterType(config.getVoltameterType()).build());
        cloudDeviceSupports.add(CloudDeviceSupport.builder().batteryCode("789").voltameterType(config.getVoltameterType()).build());
        cloudDeviceSupports.add(CloudDeviceSupport.builder().batteryCode(config.getBatteryCode()).voltameterType(789).build());
        List<UserRoleDO> userRoles = new LinkedList<>();
        for (int i = 0; i < 10; i++) {
            UserRoleDO userRole = UserRoleDO.builder().userId(i).adminId(i).serialNumber("sn_" + i).build();
            userRoles.add(userRole);
            CloudDeviceSupport cloudDeviceSupport = cloudDeviceSupports.get(i % cloudDeviceSupports.size());
            when(deviceInfoService.getDeviceSupport(userRole.getSerialNumber())).thenReturn(cloudDeviceSupport);
        }
        when(userRoleService.queryAllAdminUserRoleNum()).thenReturn(10);
        when(userRoleService.queryAllAdminUserRoleIterator("publishBatteryConfig", 3))
                .thenAnswer(it -> userRoles.iterator());
        when(openApiConfigService.publishDeviceConfig(any(), any())).thenReturn(Result.Success());

        batteryConfigPushService.publishBatteryConfig(config, 3);

    }


}
