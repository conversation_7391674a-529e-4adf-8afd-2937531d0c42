package com.addx.iotcamera.service.pay;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.payment.ApplePaymentRequest;
import com.addx.iotcamera.bean.app.payment.GooglePaymentRequest;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.UserTierDeviceDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.pay.OrderVerifyResultDO;
import com.addx.iotcamera.bean.domain.pay.PaymentConfig;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.config.pay.GooglePaySandboxConfig;
import com.addx.iotcamera.config.pay.PaymentCenterConfig;
import com.addx.iotcamera.config.serve.ServeConfig;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.dao.pay.*;
import com.addx.iotcamera.dao.redis.PayRedis;
import com.addx.iotcamera.enums.ProductTypeEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.enums.pay.TierTypeEnums;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import com.addx.iotcamera.service.vip.TierGroupService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.DateUtils;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.assertj.core.util.Sets;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.annotation.Lazy;

import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.PayConstants.DEFAULT_ROOLING_DAY;
import static com.addx.iotcamera.enums.PaymentTypeEnums.APPLE;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.addx.iot.common.enums.ResultCollection.IOS_ORDER_EMPTY;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PaymentServiceTest {
    @InjectMocks
    private PaymentService paymentService;

    @Mock
    private IPaymentFlowDAO iPaymentFlowDAO;

    @Mock
    private IUserPaymentTimeSnDAO iUserPaymentTimeSnDAO;

    @Mock
    private PaymentCenterConfig paymentCenterConfig;

    @Mock
    private ApplePayService applePayService;

    @Mock
    private UserService userService;

    @Mock
    private ServeConfig serveConfig;

    @Mock
    private ProductService productService;

    @Mock
    private UserVipService userVipService;

    @Mock
    private IOrderDAO iOrderDAO;

    @Mock
    private IOrderProductDAO iOrderProductDAO;

    @Mock
    private PayRedis payRedis;

    @Mock
    private ReportLogService reportLogService;

    @Mock
    private GooglePayService googlePayService;

    @Mock
    private GooglePaySandboxConfig googlePaySandboxConfig;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private IUserVipDAO iUserVipDAO;

    @Mock
    private UserTierDeviceService userTierDeviceService;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private DeviceModelIconService deviceModelIconService;

    @Mock
    private TierService tierService;

    @Mock
    @Lazy
    private AdditionalUserTierService additionalUserTierService;

    @Mock
    private IUserDAO iUserDAO;

    @Mock
    private TierGroupService tierGroupService;

    @Mock
    private UserVipActivateService userVipActivateService;

    @Mock
    private IPurchaseSelectDeviceDAO iPurchaseSelectDeviceDAO;

    @Test
    @DisplayName("记录支付流水")
    public void paymentFlow_test(){
        Integer userId = 1;
        OrderDO orderDO = OrderDO.builder()
                .orderSn("orderSn")
                .tradeNo("tradeNo")
                .orderType(APPLE.getCode())
                .userId(1)
                .timeStart(1)
                .timeEnd(1)
                .tenantId("vicoo")
                .status(0)
                .extend("")
                .build();

        OrderProductDo orderProductDo = OrderProductDo.builder()
                .id(1L)
                .productId(1)
                .body("body")
                .subject("subject")
                .price(1)
                .build();

        when(iPaymentFlowDAO.insertPayment(any())).thenReturn(1L);
        when(iUserPaymentTimeSnDAO.insertUserPaymentTimeSn(any())).thenReturn(1);
        when(userService.queryUserById(any())).thenReturn(new User());

        when(serveConfig.getServeReleaseTag()).thenReturn("");
        PaymentFlow expectResult = PaymentFlow.builder()
                .outTradeNo(orderDO.getOrderSn())
                .tradeNo(orderDO.getTradeNo())
                .type(orderDO.getOrderType())
                .userId(orderDO.getUserId())
                .productId(orderProductDo.getProductId())
                .productSubject(orderProductDo.getSubject())
                .productBody(orderProductDo.getBody())
                .amount(orderProductDo.getPrice())
                .currency(0)
                .timeStart(orderDO.getTimeStart())
                .timeEnd(orderDO.getTimeEnd())
                .status(orderDO.getStatus())
                .tradeType(0)
                .extend(orderDO.getExtend())
                .tenantId(orderDO.getTenantId())
                .build();

        int purchaseTime = orderDO.getTimeStart();
        OrderVerifyResultDO verifyResultDO =  OrderVerifyResultDO.builder()
                .tradeNo(orderDO.getTradeNo())
                .purchaseTime(purchaseTime)
                .purchaseDate(DateUtils.timeStamp2Date(purchaseTime,DateUtils.YYYY_MM_DD_HH_MM_SS))
                .purchaseDatePst(DateUtils.timeStamp2Date(purchaseTime,DateUtils.YYYY_MM_DD_HH_MM_SS))
                .build();

        when(productService.queryProductById(any())).thenReturn(ProductDO.builder().tierId(1).build());
        when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(1).tierServiceType(0).build());

        List<DeviceDO> deviceDOList = Arrays.asList(
                DeviceDO.builder().serialNumber("sn1").userId(userId).adminId(userId).build(),
                DeviceDO.builder().serialNumber("sn2").userId(userId).adminId(userId).build()
        );
        when(userTierDeviceService.queryUserAdminDevice(any(),any())).thenReturn(deviceDOList);


        {
            // renew->true,
            //

            when(userTierDeviceService.queryUserTierDeviceDOListByUser(any(),any())).thenReturn(Arrays.asList(
                    UserTierDeviceDO.builder().tierId(1).serialNumber("sn1").build(),
                    UserTierDeviceDO.builder().tierId(1).serialNumber("sn2").build()
            ));
            when(iPurchaseSelectDeviceDAO.insertPurchaseSelectDevice(any())).thenReturn(1);

            when(userTierDeviceService.filterOutDeviceVip(any(),any())).thenReturn(deviceDOList);
            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(false);
            when(iUserPaymentTimeSnDAO.insertUserPaymentTimeSn(any())).thenReturn(1);

            PaymentFlow actualResult = paymentService.initPayment(orderDO,orderProductDo,"",verifyResultDO,true);
            assertEquals(expectResult.getTradeNo(), actualResult.getTradeNo());
        }
        {
            when(userTierDeviceService.initUserTierDeviceList(any(),any(),any())).thenReturn(Arrays.asList(
                   "sn1","sn2"
            ));
            when(iPurchaseSelectDeviceDAO.insertPurchaseSelectDevice(any())).thenReturn(1);

            when(userTierDeviceService.filterOutDeviceVip(any(),any())).thenReturn(deviceDOList);
            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(false);
            when(iUserPaymentTimeSnDAO.insertUserPaymentTimeSn(any())).thenReturn(1);

            PaymentFlow actualResult = paymentService.initPayment(orderDO,orderProductDo,"",verifyResultDO,false);
            assertEquals(expectResult.getTradeNo(), actualResult.getTradeNo());
        }


    }

    @Test
    @DisplayName("记录支付流水")
    public void paymentFlow_test_freeTrialNull(){
        Integer userId = 1;
        OrderDO orderDO = OrderDO.builder()
                .orderSn("orderSn")
                .tradeNo("tradeNo")
                .orderType(APPLE.getCode())
                .userId(1)
                .timeStart(1)
                .timeEnd(1)
                .tenantId("vicoo")
                .status(0)
                .extend("")
                .freeTrial(1)
                .build();

        OrderProductDo orderProductDo = OrderProductDo.builder()
                .id(1L)
                .productId(1)
                .body("body")
                .subject("subject")
                .price(1)
                .build();

        when(iPaymentFlowDAO.insertPayment(any())).thenReturn(1L);
        when(iUserPaymentTimeSnDAO.insertUserPaymentTimeSn(any())).thenReturn(1);
        when(userService.queryUserById(any())).thenReturn(new User());

        when(serveConfig.getServeReleaseTag()).thenReturn("");
        PaymentFlow expectResult = PaymentFlow.builder()
                .outTradeNo(orderDO.getOrderSn())
                .tradeNo(orderDO.getTradeNo())
                .type(orderDO.getOrderType())
                .userId(orderDO.getUserId())
                .productId(orderProductDo.getProductId())
                .productSubject(orderProductDo.getSubject())
                .productBody(orderProductDo.getBody())
                .amount(orderProductDo.getPrice())
                .currency(0)
                .timeStart(orderDO.getTimeStart())
                .timeEnd(orderDO.getTimeEnd())
                .status(orderDO.getStatus())
                .tradeType(0)
                .extend(orderDO.getExtend())
                .tenantId(orderDO.getTenantId())
                .freeTrial(1)
                .build();

        int purchaseTime = orderDO.getTimeStart();
        OrderVerifyResultDO verifyResultDO =  OrderVerifyResultDO.builder()
                .tradeNo(orderDO.getTradeNo())
                .purchaseTime(purchaseTime)
                .purchaseDate(DateUtils.timeStamp2Date(purchaseTime,DateUtils.YYYY_MM_DD_HH_MM_SS))
                .purchaseDatePst(DateUtils.timeStamp2Date(purchaseTime,DateUtils.YYYY_MM_DD_HH_MM_SS))
                .build();



        when(productService.queryProductById(any())).thenReturn(ProductDO.builder().tierId(1).build());
        when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(1).tierServiceType(0).build());

        List<DeviceDO> deviceDOList = Arrays.asList(
                DeviceDO.builder().serialNumber("sn1").userId(userId).adminId(userId).build(),
                DeviceDO.builder().serialNumber("sn2").userId(userId).adminId(userId).build()
        );
        when(userTierDeviceService.queryUserAdminDevice(any(),any())).thenReturn(deviceDOList);


        {
            // renew->true,
            //

            when(userTierDeviceService.queryUserTierDeviceDOListByUser(any(),any())).thenReturn(Arrays.asList(
                    UserTierDeviceDO.builder().tierId(1).serialNumber("sn1").build(),
                    UserTierDeviceDO.builder().tierId(1).serialNumber("sn2").build()
            ));
            when(iPurchaseSelectDeviceDAO.insertPurchaseSelectDevice(any())).thenReturn(1);

            when(userTierDeviceService.filterOutDeviceVip(any(),any())).thenReturn(deviceDOList);
            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(false);
            when(iUserPaymentTimeSnDAO.insertUserPaymentTimeSn(any())).thenReturn(1);

            PaymentFlow actualResult = paymentService.initPayment(orderDO,orderProductDo,"",verifyResultDO,true);
            assertEquals(expectResult.getTradeNo(), actualResult.getTradeNo());
        }
        {
            when(userTierDeviceService.initUserTierDeviceList(any(),any(),any())).thenReturn(Arrays.asList(
                    "sn1","sn2"
            ));
            when(iPurchaseSelectDeviceDAO.insertPurchaseSelectDevice(any())).thenReturn(1);

            when(userTierDeviceService.filterOutDeviceVip(any(),any())).thenReturn(deviceDOList);
            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(false);
            when(iUserPaymentTimeSnDAO.insertUserPaymentTimeSn(any())).thenReturn(1);

            PaymentFlow actualResult = paymentService.initPayment(orderDO,orderProductDo,"",verifyResultDO,false);
            assertEquals(expectResult.getTradeNo(), actualResult.getTradeNo());
        }
    }

    @Test
    @DisplayName("批量获取已存在的账单-参数empty")
    public void queryExistPaymentBatch_empty(){
        Set<String> expectResult = Sets.newHashSet();
        Set<String> actualResult = paymentService.queryExistPaymentBatch(Sets.newHashSet());
        assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("批量获取已存在的账单")
    public void queryExistPaymentBatch(){
        Set<String> tradeNoSet = Sets.newHashSet();
        tradeNoSet.add("123");

        Set<String> expectResult = tradeNoSet;
        when(iPaymentFlowDAO.queryPaymentFlowExistBatch(any())).thenReturn(tradeNoSet);
        Set<String> actualResult = paymentService.queryExistPaymentBatch(tradeNoSet);
        assertEquals(expectResult,actualResult);
    }
//
//    @Test
//    @DisplayName("Ios支付结果验证-验证失败")
//    public void test_applePaymentVerifyV1_resultCode_error() throws Exception {
//        Integer userId = 1;
//        String tenantId = "vicoo";
//        ApplePaymentRequest receipt = new ApplePaymentRequest();
//        AppInfo app = new AppInfo();
//        app.setTenantId(tenantId);
//        receipt.setApp(app);
//        Integer type = 0;
//
//        Map<String, PaymentConfig> config = new HashMap<>();
//        config.put(tenantId,new PaymentConfig());
//        when(paymentCenterConfig.getConfig()).thenReturn(config);
//        when(applePayService.compatibleSandboxOrder(any(),any())).thenReturn("");
//
//        OrderVerifyResultDO orderVerifyResultDO = OrderVerifyResultDO.builder()
//                .productId(IOS_ORDER_EMPTY.getCode())
//                .build();
//        when(applePayService.appleOrderVerifyV1(any(),any(),any())).thenReturn(orderVerifyResultDO);
//
//        Result expectResult = Result.Error(IOS_ORDER_EMPTY.getCode(), "订单验证错误");
//        Result actualResult = paymentService.applePaymentVerifyV1(userId,receipt,type);
//        Assert.assertEquals(expectResult,actualResult);
//    }

    @Test
    @DisplayName("Ios支付结果验证-验证失败")
    public void test_applePaymentVerify_resultCode_error() throws Exception {
        Integer userId = 1;
        String tenantId = "vicoo";
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(tenantId);
        receipt.setApp(app);
        Integer type = 0;

        Map<String, PaymentConfig> config = new HashMap<>();
        config.put(tenantId,new PaymentConfig());
        when(paymentCenterConfig.getConfig()).thenReturn(config);
        when(applePayService.compatibleSandboxOrder(any(),any())).thenReturn("");

        OrderVerifyResultDO orderVerifyResultDO = OrderVerifyResultDO.builder()
                .productId(IOS_ORDER_EMPTY.getCode())
                .build();
        when(applePayService.appleOrderVerify(any(),any(),any())).thenReturn(orderVerifyResultDO);
        doNothing().when(reportLogService).sysReportUpdateUserConfig(any(),any());


        boolean expectResult = false;
        boolean actualResult = false;
        try {
            actualResult = paymentService.applePaymentVerify(userId,receipt,type);
        }catch (Exception e){

        }

        assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("Ios支付结果验证-验证失败")
    public void test_applePaymentVerifyV1_resultCode_error() throws Exception {
        Integer userId = 1;
        String tenantId = "vicoo";
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(tenantId);
        receipt.setApp(app);
        Integer type = 0;

        Map<String, PaymentConfig> config = new HashMap<>();
        config.put(tenantId,new PaymentConfig());
        when(paymentCenterConfig.getConfig()).thenReturn(config);
        when(applePayService.compatibleSandboxOrder(any(),any())).thenReturn("");

        OrderVerifyResultDO orderVerifyResultDO = OrderVerifyResultDO.builder()
                .productId(IOS_ORDER_EMPTY.getCode())
                .build();
        when(applePayService.appleOrderVerifyV1(any(),any(),any())).thenReturn(orderVerifyResultDO);

        Result expectResult = Result.Error(IOS_ORDER_EMPTY.getCode(), "订单验证错误");
        Result actualResult = paymentService.applePaymentVerifyV1(userId,receipt,type);
        assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("Ios支付结果验证-已验证")
    public void test_applePaymentVerifyV1_verify() throws Exception {
        Integer userId = 1;
        String tenantId = "vicoo";
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(tenantId);
        receipt.setApp(app);
        Integer type = 0;

        Map<String, PaymentConfig> config = new HashMap<>();
        config.put(tenantId,new PaymentConfig());
        when(paymentCenterConfig.getConfig()).thenReturn(config);
        when(applePayService.compatibleSandboxOrder(any(),any())).thenReturn("");
        OrderVerifyResultDO orderVerifyResultDO = OrderVerifyResultDO.builder()
                .productId(0)
                .build();
        when(applePayService.appleOrderVerifyV1(any(),any(),any())).thenReturn(orderVerifyResultDO);

        Result expectResult = Result.Success();
        Result actualResult = paymentService.applePaymentVerifyV1(userId,receipt,type);
        assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("Ios支付结果验证-已验证")
    public void test_applePaymentVerifyV1_success() throws Exception {
        Integer userId = 1;
        String tenantId = "vicoo";
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(tenantId);
        receipt.setApp(app);
        Integer type = 0;

        Map<String, PaymentConfig> config = new HashMap<>();
        config.put(tenantId,new PaymentConfig());
        when(paymentCenterConfig.getConfig()).thenReturn(config);
        when(applePayService.compatibleSandboxOrder(any(),any())).thenReturn("");
        OrderVerifyResultDO orderVerifyResultDO = OrderVerifyResultDO.builder()
                .productId(1)
                .build();
        when(applePayService.appleOrderVerifyV1(any(),any(),any())).thenReturn(orderVerifyResultDO);

        ProductDO productDO = new ProductDO();
        productDO.setId(1);
        productDO.setType(0);
        productDO.setMonth(1);
        productDO.setType(1);
        when(productService.queryProductById(any())).thenReturn(productDO);

        User user = new User();
        user.setId(1);
        user.setTenantId(tenantId);
        user.setLanguage("zh");
        when(userService.queryUserById(any())).thenReturn(user);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(null);
        when(userVipService.queryUserOrderIdAccess(any())).thenReturn(null);
        when(applePayService.isSandboxOrder(any())).thenReturn(false);
        when(iOrderDAO.insertOrder(any())).thenReturn(1);
        when(iOrderProductDAO.insertOrderProduct(any())).thenReturn(1);
        when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(new OrderProductDo());
        when(payRedis.incre(any())).thenReturn(2L);


        Result expectResult = Result.Success();
        Result actualResult = Result.Success();;
        try {
            actualResult = paymentService.applePaymentVerifyV1(userId,receipt,type);
        }catch (Exception e){

        }
        assertEquals(expectResult,actualResult);
    }




    @Test
    public void test_updateRefundInfo(){
        PaymentFlow paymentFlow = new PaymentFlow();
        when(iPaymentFlowDAO.insertPayment(paymentFlow)).thenReturn(1L);
        when(userService.queryUserById(any())).thenReturn(new User());
        when(serveConfig.getServeReleaseTag()).thenReturn("");
        paymentService.updateRefundInfo(paymentFlow);
        verify(iPaymentFlowDAO, times(1)).updateRefundOrderInfo(paymentFlow);
    }


    @Test
    @DisplayName("谷歌订单验证-已通知")
    public void test_googlePaymentVerify(){
        GooglePaymentRequest request = new GooglePaymentRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);
        request.setOutTradeNo("test");
        when(googlePayService.googleOrderVerify(any())).thenReturn(OrderVerifyResultDO.builder().verify(true).build());
        ProductDO productDO = new ProductDO();
        productDO.setType(0);
        when(productService.queryProductById(any())).thenReturn(productDO);
        when(iOrderDAO.queryBytradeNo(any())).thenReturn(new OrderDO());
        boolean expectedResult = true;
        boolean actualResult = paymentService.googlePaymentVerify(request,1);
        assertEquals(expectedResult,actualResult);

    }


    @Test
    @DisplayName("谷歌订单验证-已通知")
    public void test_googlePaymentVerify_orderProduct_null(){
        GooglePaymentRequest request = new GooglePaymentRequest();
        request.setOutTradeNo("test");
        AppInfo app = new AppInfo();
        request.setApp(app);
        when(googlePayService.googleOrderVerify(any())).thenReturn(OrderVerifyResultDO.builder().verify(true).build());
        ProductDO productDO = new ProductDO();
        productDO.setType(0);
        when(productService.queryProductById(any())).thenReturn(productDO);

        when(iOrderDAO.queryBytradeNo(any())).thenReturn(null);

        User user = new User();
        user.setEmail("email");
        Mockito.when(userService.queryUserById(any())).thenReturn(user);
        Mockito.when(googlePaySandboxConfig.getConfig()).thenReturn(Maps.newHashMap());

        when(iOrderDAO.insertOrder(any())).thenReturn(1);
        when(iOrderProductDAO.insertOrderProduct(any())).thenReturn(1);

        when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(null);

        boolean expectedResult = false;
        boolean actualResult = paymentService.googlePaymentVerify(request,1);
        assertEquals(expectedResult,actualResult);

        when(googlePayService.googleOrderVerify(any())).thenReturn(OrderVerifyResultDO.builder().verify(false).build());
        actualResult = paymentService.googlePaymentVerify(request,1);
        assertEquals(expectedResult,actualResult);

    }

    @Test(expected = BaseException.class)
    @DisplayName("套餐是否可购买-商品不存在")
    public void testVerifyProductPurchase_WithInvalidInput_productNull() {
        when(productService.queryProductById(any())).thenReturn(null);
        paymentService.verifyProductPurchase(1,1, TENANTID_VICOO);
    }

    @Test
    public void testVerifyProductPurchase_WithInvalidInput_ShouldReturnInvalidResult() {
        // Arrange
        Integer userId = 1;
        Integer productId = 1;
        String tenantId = TENANTID_VICOO;

        ProductDO productDO = new ProductDO();
        productDO.setId(productId);
        productDO.setTierId(1);
        when(productService.queryProductById(productId)).thenReturn(productDO);
        Map<String,Object> expectResult = Maps.newHashMap();


        {
            // 商品类型不需要验证
            // 设备list 为空
            productDO.setType(0);
            when(deviceInfoService.listDevicesByUserId(userId)).thenReturn(Lists.newArrayList());

            //是否可购买
            expectResult.put("availableForPurchase", true);
            expectResult.put("allSimThirdParty",true);
            //待指定设备列表
            expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult, actualResult);
        }
        {
            // 商品类型需要校验
            // 用户当前无套餐，无指定设备
            productDO.setType(3);

            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(null);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            when(tierService.queryTierById(1)).thenReturn(buyTier);
            when(iUserVipDAO.queryUserVipInfo(any(),any(),any())).thenReturn(Lists.newArrayList());


            List<DeviceDO> deviceDOList = Arrays.asList(
                    DeviceDO.builder().serialNumber("sn1").adminId(userId).userId(userId).simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn2").adminId(userId).userId(userId).iccid("iccid").simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn2").adminId(userId).userId(userId).iccid("iccid").simThirdParty(1).build()
            );
            when(deviceInfoService.listDevicesByUserId(userId)).thenReturn(deviceDOList);
            when(deviceInfoService.checkIfDeviceUse4G("sn1")).thenReturn(false);
            when(deviceInfoService.checkIfDeviceUse4G("sn2")).thenReturn(true);
            when(deviceInfoService.checkIfDeviceUse4G("sn3")).thenReturn(true);


            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);
            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }

        {
            // 商品类型需要校验
            // 用户当前有套餐，无购买记录，无指定设备
            productDO.setType(3);
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(null);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            when(tierService.queryTierById(1)).thenReturn(buyTier);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(null);
            List<String> serialNumberList = Arrays.asList("sn1","sn2");
            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).adminId(userId).userId(userId).deviceName("name").build()).collect(Collectors.toList());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);
            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }

        {
            // 商品类型需要校验
            // 用户当前有套餐，无购买记录，无指定设备
            productDO.setType(3);
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(null);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            when(tierService.queryTierById(1)).thenReturn(buyTier);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                  .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("orderSn").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(null);

            List<String> serialNumberList = Arrays.asList("sn1","sn2");
            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).deviceName("name").adminId(userId).userId(userId).build()).collect(Collectors.toList());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);
            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }

        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录，订单已取消，无指定设备
            productDO.setType(3);

            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(1).build());
            List<String> serialNumberList = Arrays.asList("sn1","sn2");
            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).deviceName("name").adminId(userId).userId(userId).build()).collect(Collectors.toList());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);
            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }

        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录
            // 当前生效的套餐商品无数量限制
            productDO.setType(3);
            productDO.setTierId(1);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(0).build());

            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(null);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            when(tierService.queryTierById(1)).thenReturn(buyTier);


            List<String> serialNumberList = Arrays.asList("sn1","sn2");
            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).adminId(userId).userId(userId).deviceName("name").build()).collect(Collectors.toList());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);
            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }
        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录
            // 当前生效的套餐商品有效
            // 套餐类型不同
            productDO.setType(3);
            productDO.setTierId(1);
            productDO.setMonth(1);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(0).build());
            // 要购买的套餐信息
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(1);
            when(tierService.queryTierById(1)).thenReturn(buyTier);

            // 当前套餐信息
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(OrderProductDo.builder().productId(2).build());
            ProductDO currentProduct = new ProductDO();
            currentProduct.setId(2);
            currentProduct.setTierId(2);
            currentProduct.setMonth(1);

            when(productService.queryProductById(2)).thenReturn(currentProduct);
            Tier currentTier = new Tier();
            currentTier.setTierId(2);
            currentTier.setMaxDeviceNum(1);
            currentTier.setTierType(2);
            currentTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());

            when(tierService.queryTierById(2)).thenReturn(currentTier);


            List<String> serialNumberList = Arrays.asList("sn1","sn2");
            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).adminId(userId).userId(userId).deviceName("name").build()).collect(Collectors.toList());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);


            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }

        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录
            // 当前生效的套餐商品有效
            // 套餐类型不同
            productDO.setType(3);
            productDO.setTierId(1);
            productDO.setMonth(1);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(0).build());
            // 要购买的套餐信息
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(1);
            buyTier.setTierType(2);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            when(tierService.queryTierById(1)).thenReturn(buyTier);

            // 当前套餐信息
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(OrderProductDo.builder().productId(2).build());
            ProductDO currentProduct = new ProductDO();
            currentProduct.setId(2);
            currentProduct.setTierId(2);
            currentProduct.setMonth(1);

            when(productService.queryProductById(2)).thenReturn(currentProduct);
            Tier currentTier = new Tier();
            currentTier.setTierId(2);
            currentTier.setMaxDeviceNum(1);
            currentTier.setTierType(2);
            currentTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());

            when(tierService.queryTierById(2)).thenReturn(currentTier);


            List<String> serialNumberList = Arrays.asList("sn1","sn2");
            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).adminId(userId).userId(userId).deviceName("name").build()).collect(Collectors.toList());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);


            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }

        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录
            // 当前生效的套餐商品有效
            // v2套餐-当前双设备,要购买单设备,不可买
            productDO.setType(3);
            productDO.setTierId(1);
            productDO.setMonth(1);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(0).build());
            // 要购买的套餐信息
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(1);
            buyTier.setTierType(2);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            when(tierService.queryTierById(1)).thenReturn(buyTier);

            // 当前套餐信息
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(OrderProductDo.builder().productId(2).build());
            ProductDO currentProduct = new ProductDO();
            currentProduct.setId(2);
            currentProduct.setTierId(2);
            currentProduct.setMonth(1);

            when(productService.queryProductById(2)).thenReturn(currentProduct);
            Tier currentTier = new Tier();
            currentTier.setTierId(2);
            currentTier.setMaxDeviceNum(2);
            currentTier.setTierType(2);
            currentTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            when(tierService.queryTierById(2)).thenReturn(currentTier);


//            List<String> serialNumberList = Arrays.asList("sn1","sn2");
//            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).deviceName("name").build()).collect(Collectors.toList());
//
//            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
//            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);
//
//
//            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);
//
//            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
//            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);
//

            //是否可购买
            expectResult.put("availableForPurchase", false);
            //待指定设备列表
            expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }

        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录
            // 当前生效的套餐商品有效
            // v2套餐-当前单设备,要购买单设备,返回套餐已指定设备
            productDO.setType(3);
            productDO.setTierId(1);
            productDO.setMonth(1);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(0).build());
            // 要购买的套餐信息
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(1);
            buyTier.setTierType(2);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());

            when(tierService.queryTierById(1)).thenReturn(buyTier);

            // 当前套餐信息
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(OrderProductDo.builder().productId(2).build());
            ProductDO currentProduct = new ProductDO();
            currentProduct.setId(2);
            currentProduct.setTierId(2);
            currentProduct.setMonth(1);

            when(productService.queryProductById(2)).thenReturn(currentProduct);
            Tier currentTier = new Tier();
            currentTier.setTierId(2);
            currentTier.setMaxDeviceNum(1);
            currentTier.setTierType(2);
            currentTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());

            when(tierService.queryTierById(2)).thenReturn(currentTier);

            when(userTierDeviceService.queryUserTierDeviceDOListByUser(any(),any())).thenReturn(Arrays.asList(UserTierDeviceDO.builder().serialNumber("sn1").tierId(1).build()));


            List<String> serialNumberList = Arrays.asList("sn1","sn2");
            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).adminId(userId).userId(userId).deviceName("name").build()).collect(Collectors.toList());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);


            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            //expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }
        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录
            // 当前生效的套餐商品有效
            // v1套餐-当前双设备,要购买单设备,期间相同，套餐等级相同
            productDO.setType(3);
            productDO.setTierId(1);
            productDO.setMonth(1);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(0).build());
            // 要购买的套餐信息
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(1);
            buyTier.setTierType(TierTypeEnums.TIER_LEVEL_DEVICE_LIMIT.getCode());
            buyTier.setLevel(1);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());

            when(tierService.queryTierById(1)).thenReturn(buyTier);

            // 当前套餐信息
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(OrderProductDo.builder().productId(2).build());
            ProductDO currentProduct = new ProductDO();
            currentProduct.setId(2);
            currentProduct.setTierId(2);
            currentProduct.setMonth(1);

            when(productService.queryProductById(2)).thenReturn(currentProduct);
            Tier currentTier = new Tier();
            currentTier.setTierId(2);
            currentTier.setMaxDeviceNum(2);
            currentTier.setTierType(TierTypeEnums.TIER_LEVEL_DEVICE_LIMIT.getCode());
            currentTier.setLevel(1);
            currentTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());

            when(tierService.queryTierById(2)).thenReturn(currentTier);

            //when(userTierDeviceService.queryUserTierDeviceDOListByUser(any(),any())).thenReturn(Arrays.asList(UserTierDeviceDO.builder().serialNumber("sn1").tierId(1).build()));


            List<String> serialNumberList = Arrays.asList("sn1","sn2");
            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).adminId(userId).userId(userId).deviceName("name").build()).collect(Collectors.toList());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);


            //是否可购买
            expectResult.put("availableForPurchase", false);
            //待指定设备列表
            //expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }

        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录
            // 当前生效的套餐商品有效
            // v1套餐-当前双设备,要购买单设备,期间相同，套餐等级相同
            productDO.setType(3);
            productDO.setTierId(1);
            productDO.setMonth(1);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(0).build());
            // 要购买的套餐信息
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(1);
            buyTier.setTierType(TierTypeEnums.TIER_LEVEL_DEVICE_LIMIT.getCode());
            buyTier.setLevel(1);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            when(tierService.queryTierById(1)).thenReturn(buyTier);

            // 当前套餐信息
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(OrderProductDo.builder().productId(2).build());
            ProductDO currentProduct = new ProductDO();
            currentProduct.setId(2);
            currentProduct.setTierId(2);
            currentProduct.setMonth(1);

            when(productService.queryProductById(2)).thenReturn(currentProduct);
            Tier currentTier = new Tier();
            currentTier.setTierId(2);
            currentTier.setMaxDeviceNum(1);
            currentTier.setTierType(TierTypeEnums.TIER_LEVEL_DEVICE_LIMIT.getCode());
            currentTier.setLevel(1);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());

            when(tierService.queryTierById(2)).thenReturn(currentTier);

            when(userTierDeviceService.queryUserTierDeviceDOListByUser(any(),any())).thenReturn(Arrays.asList(UserTierDeviceDO.builder().serialNumber("sn1").tierId(1).build()));


            List<String> serialNumberList = Arrays.asList("sn1","sn2");
            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).adminId(userId).userId(userId).deviceName("name").build()).collect(Collectors.toList());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);


            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            //expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }
        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录
            // 当前生效的套餐商品有效
            // v1套餐-当前双设备,要购买单设备,期间相同，套餐等级相同
            productDO.setType(3);
            productDO.setTierId(1);
            productDO.setMonth(1);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(0).build());
            // 要购买的套餐信息
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(1);
            buyTier.setTierType(TierTypeEnums.TIER_LEVEL_DEVICE_LIMIT.getCode());
            buyTier.setLevel(1);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());

            when(tierService.queryTierById(1)).thenReturn(buyTier);

            // 当前套餐信息
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(OrderProductDo.builder().productId(2).build());
            ProductDO currentProduct = new ProductDO();
            currentProduct.setId(2);
            currentProduct.setTierId(2);
            currentProduct.setMonth(1);

            when(productService.queryProductById(2)).thenReturn(currentProduct);
            Tier currentTier = new Tier();
            currentTier.setTierId(2);
            currentTier.setMaxDeviceNum(1);
            currentTier.setTierType(TierTypeEnums.TIER_LEVEL_DEVICE_LIMIT.getCode());
            currentTier.setLevel(2);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());

            when(tierService.queryTierById(2)).thenReturn(currentTier);

            //when(userTierDeviceService.queryUserTierDeviceDOListByUser(any(),any())).thenReturn(Arrays.asList(UserTierDeviceDO.builder().serialNumber("sn1").tierId(1).build()));


            List<String> serialNumberList = Arrays.asList("sn1","sn2");
            List<DeviceDO> deviceDOList = serialNumberList.stream().map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).adminId(userId).userId(userId).deviceName("name").build()).collect(Collectors.toList());

            when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(serialNumberList);
            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceInfoService.queDeviceBySerialNumbers(any())).thenReturn(deviceDOList);

            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);


            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            //expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }

        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录
            // 当前生效的套餐商品有效
            // v1套餐-当前双设备,要购买单设备,期间相同，套餐等级相同
            productDO.setType(5);
            productDO.setTierId(1);
            productDO.setMonth(1);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(0).build());
            // 要购买的套餐信息
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(1);
            buyTier.setTierType(TierTypeEnums.TIER_4GDATA.getCode());
            buyTier.setLevel(1);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_4G.getCode());

            when(tierService.queryTierById(1)).thenReturn(buyTier);

            // 当前套餐信息
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(OrderProductDo.builder().productId(2).build());
            ProductDO currentProduct = new ProductDO();
            currentProduct.setId(2);
            currentProduct.setTierId(2);
            currentProduct.setMonth(1);

            when(productService.queryProductById(2)).thenReturn(currentProduct);
            Tier currentTier = new Tier();
            currentTier.setTierId(2);
            currentTier.setMaxDeviceNum(1);
            currentTier.setTierType(TierTypeEnums.TIER_4GDATA.getCode());
            currentTier.setLevel(2);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_4G.getCode());
            when(tierService.queryTierById(2)).thenReturn(currentTier);

            //是否可购买
            expectResult.put("availableForPurchase", false);
            //待指定设备列表
            //expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }

        {
            // 商品类型需要校验
            // 用户当前有套餐，有购买记录
            // 当前生效的套餐商品有效
            // v1套餐-当前双设备,要购买单设备,期间相同，套餐等级相同
            productDO.setType(5);
            productDO.setTierId(1);
            productDO.setMonth(1);
            when(iUserVipDAO.queryUserVipInfo(any(), any(),any()))
                    .thenReturn(Arrays.asList(UserVipDO.builder().tradeNo("tradeNo").build()));
            when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(PaymentFlow.builder().outTradeNo("trade").build());
            when(iOrderDAO.queryByOrderSn(any())).thenReturn(OrderDO.builder().orderCancel(0).build());
            // 要购买的套餐信息
            Tier buyTier = new Tier();
            buyTier.setTierId(1);
            buyTier.setMaxDeviceNum(1);
            buyTier.setTierType(TierTypeEnums.TIER_4GDATA.getCode());
            buyTier.setLevel(2);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_4G.getCode());

            when(tierService.queryTierById(1)).thenReturn(buyTier);

            // 当前套餐信息
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(OrderProductDo.builder().productId(2).build());
            ProductDO currentProduct = new ProductDO();
            currentProduct.setId(2);
            currentProduct.setTierId(2);
            currentProduct.setMonth(1);

            when(productService.queryProductById(2)).thenReturn(currentProduct);
            Tier currentTier = new Tier();
            currentTier.setTierId(2);
            currentTier.setMaxDeviceNum(1);
            currentTier.setTierType(TierTypeEnums.TIER_4GDATA.getCode());
            currentTier.setLevel(1);
            buyTier.setTierServiceType(TierServiceTypeEnums.TIER_4G.getCode());
            when(tierService.queryTierById(2)).thenReturn(currentTier);

            List<DeviceDO> deviceDOList = Arrays.asList(
                    DeviceDO.builder().serialNumber("sn1").adminId(userId).userId(userId).simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn2").adminId(userId).userId(userId).iccid("iccid").simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn2").adminId(userId).userId(userId).iccid("iccid").simThirdParty(1).build()
            );
            when(deviceInfoService.listDevicesByUserId(userId)).thenReturn(deviceDOList);
            when(deviceInfoService.checkIfDeviceUse4G("sn1")).thenReturn(false);
            when(deviceInfoService.checkIfDeviceUse4G("sn2")).thenReturn(true);
            when(deviceInfoService.checkIfDeviceUse4G("sn3")).thenReturn(true);


            when(userTierDeviceService.filterOutDeviceVip(tenantId,deviceDOList)).thenReturn(deviceDOList);


            when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
            when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);

            //是否可购买
            expectResult.put("availableForPurchase", true);
            //待指定设备列表
            //expectResult.put("deviceList",Lists.newArrayList());
            Map<String,Object> actualResult = paymentService.verifyProductPurchase(userId, productId, tenantId);
            // Assert
            assertEquals(expectResult.get("availableForPurchase"), actualResult.get("availableForPurchase"));
        }
    }


    @Test
    public void testInitUserVipRoolingDayWithNullUserVipDOAndDeviceLimitTier() {

        Tier tier = new Tier();
        tier.setTierType(TierTypeEnums.TIER_DEVICE_LIMIT.getCode());

        int rollingDays = paymentService.initUserVipRoolingDay(null, tier, TENANTID_VICOO);
        Assert.assertEquals(DEFAULT_ROOLING_DAY, rollingDays);
    }

    @Test
    public void testInitUserVipRoolingDayWithNullUserVipDOAndNonDeviceLimitTier() {
        Tier tier = new Tier();
        tier.setTierType(TierTypeEnums.TIER_LEVEL_DEVICE_LIMIT.getCode());
        tier.setRollingDays(60);
        int rollingDays = paymentService.initUserVipRoolingDay(null, tier, TENANTID_VICOO);
        Assert.assertEquals(60, rollingDays);
    }

    @Test
    public void testInitUserVipRoolingDayWithNonNullUserVipDO() {
        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setRollingDay(15);
        int rollingDays = paymentService.initUserVipRoolingDay(userVipDO, null, TENANTID_VICOO);
        Assert.assertEquals(15, rollingDays);
    }


    @Test
    public void testInitUserVip() {
        // 创建OrderDO和OrderProductDo对象
        OrderDO orderDO = new OrderDO();
        orderDO.setUserId(1);
        orderDO.setId(1L);
        orderDO.setTradeNo("TRADE001");

        OrderProductDo orderProductDo = new OrderProductDo();
        orderProductDo.setProductId(1);

        // 创建ProductDO对象
        ProductDO productDO = new ProductDO();
        productDO.setTierId(1);
        Mockito.when(productService.queryProductById(Mockito.anyInt())).thenReturn(productDO);



        // 创建User对象
        User user = new User();
        user.setId(1);
        user.setLanguage("en_US");
        user.setTenantId("TENANT001");
        Mockito.when(iUserDAO.getUserById(Mockito.any(User.class))).thenReturn(user);


        // 创建Tier对象
        Tier tier = new Tier();
        tier.setTierGroupId(1);
        Mockito.when(tierService.queryTierById(Mockito.anyInt())).thenReturn(tier);

        // 创建UserVipDO对象
        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setTierId(1);
        List<UserVipDO> userVipDOList = Arrays.asList(userVipDO);
        Mockito.when(iUserVipDAO.queryUserVipInfo(Mockito.anyInt(), Mockito.anyInt(),any())).thenReturn(userVipDOList);

        when(tierGroupService.addUserVipDO(any(),any(),any(),any())).thenReturn(userVipDOList);
        when(iUserVipDAO.insertUserVip(any())).thenReturn(1);

        // 调用被测试的函数
        try {
            paymentService.initUserVip(orderDO, orderProductDo);
        } catch (Exception e) {

        }

        // 验证行为
        Mockito.verify(userVipActivateService, times(0)).activateUserVip(1,(Integer) null);
    }

    @Test
    @DisplayName("商品类型不对")
    public void testInitPaymentTierDeviceList_Negative() {
        ProductDO productDO = new ProductDO();
        productDO.setType(0);
        List<String> result = paymentService.initPaymentTierDeviceList(Lists.newArrayList(), productDO);

        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testInitPaymentTierDeviceList_Positive() {
        ProductDO productDO = new ProductDO();
        productDO.setType(ProductTypeEnums.PRODUCT_DEVICE_NUM.getCode());


        {
            Tier tier = new Tier();
            List<String> list = Arrays.asList("device1", "device2");
            when(tierService.queryTierById(any())).thenReturn(tier);

            List<String> result = paymentService.initPaymentTierDeviceList(list, productDO);
            List<String> expected = Arrays.asList("device1", "device2");
            Assert.assertEquals(expected, result);
        }
        {
            List<String> list = Arrays.asList("device1", "device2");

            Tier tier = new Tier();
            tier.setMaxDeviceNum(1);
            when(tierService.queryTierById(any())).thenReturn(tier);
            List<String> result = paymentService.initPaymentTierDeviceList(list, productDO);
            List<String> expected = Arrays.asList("device1");
            Assert.assertEquals(expected, result);
        }
        {
            List<String> list = Arrays.asList("device1", "device2");

            Tier tier = new Tier();
            tier.setMaxDeviceNum(2);
            when(tierService.queryTierById(any())).thenReturn(tier);
            List<String> result = paymentService.initPaymentTierDeviceList(list, productDO);
            List<String> expected = Arrays.asList("device1","device2");
            Assert.assertEquals(expected, result);
        }
        {

            Tier tier = new Tier();
            List<String> list = Arrays.asList("device1");
            tier.setMaxDeviceNum(2);
            when(tierService.queryTierById(any())).thenReturn(tier);
            List<String> result = paymentService.initPaymentTierDeviceList(list, productDO);
            List<String> expected = Arrays.asList("device1");
            Assert.assertEquals(expected, result);
        }
    }

    @Test
    public void test_queryPaymentBatch(){
        List<PaymentFlow> exceptedResult;
        List<PaymentFlow> actualResult;

        {
            exceptedResult = Lists.newArrayList();
            actualResult = paymentService.queryPaymentBatch(Lists.newArrayList());
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            when(iPaymentFlowDAO.queryPaymentFlowBatch(any())).thenReturn(Collections.singletonList(PaymentFlow.builder().build()));
            exceptedResult = Collections.singletonList(PaymentFlow.builder().build());
            actualResult = paymentService.queryPaymentBatch(Collections.singletonList("test"));
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }


    @Test
    public void test_updatePaymentFlowExpire(){
        when(iPaymentFlowDAO.updatePaymentFlow(any(),any())).thenReturn(1);
        paymentService.updatePaymentFlowExpire("test",1);
        verify(iPaymentFlowDAO, times(1)).updatePaymentFlow(any(),any());
    }
}
