package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.apollo.MailSend;
import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.HandleApprovalRequest;
import com.addx.iotcamera.bean.app.RequireApprovalRequest;
import com.addx.iotcamera.bean.app.user.CkApprovalRequest;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.exception.ParamException;
import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.bean.msg.GuestRequestEntity;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.config.apollo.MailSendConfig;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.dao.IShareDAO;
import com.addx.iotcamera.dao.device.ShareApprovalLocalDAO;
import com.addx.iotcamera.enums.PushTypeEnums;
import com.addx.iotcamera.enums.device.DeviceShareTypeEnum;
import com.addx.iotcamera.helper.MsgHelper;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.addx.iotcamera.service.xinge.PushXingeService;
import com.addx.iotcamera.util.Mail;
import com.alibaba.fastjson.JSON;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.addx.iot.common.constant.AppConstants.APP_LANGUAGE_ZH;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.addx.iot.common.enums.ResultCollection.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PhosUtils.class, JSON.class,Mail.class})
public class DeviceShareServiceTest {
    private static Map<String, Map<String, Map<String, String>>> notifyMessage = Maps.newHashMap();
    @InjectMocks
    private DeviceShareService deviceShareService;

    @Mock(name = "shareDAO")
    private IShareDAO shareDAO;

    @Mock
    private DeviceRelationshipService deviceRelationshipService;

    @Mock
    private UserService userService;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private PushService pushService;

    @Mock
    private RedisService redisService;

    @Mock
    private MsgHelper msgHelper;

    @Mock
    private PushInfoService pushInfoService;

    @Mock
    CenterNotifyConfig centerNotifyConfig;

    @Mock
    private PushXingeService pushXingeService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private OpenApiWebhookService openApiWebhookService;

    @Mock
    private ShareApprovalLocalDAO shareApprovalLocalDAO;

    @Mock
    private MailSendConfig mailSendConfig;

    @Mock
    private MailConfirmService mailConfirmService;

    @Mock
    private TenantTierConfig tenantTierConfig;

    @Mock
    private AppAccountConfig appAccountConfig;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(PhosUtils.class);
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(Mail.class);

        Map<String,Map<String,MailSend>> map = Maps.newHashMap();
        Map<String,MailSend> languageMap = Maps.newHashMap();
        languageMap.put(APP_LANGUAGE_ZH,new MailSend());
        map.put(TENANTID_VICOO,languageMap);

        when(mailSendConfig.getConfig()).thenReturn(map);
    }

    @Test
    public void checkShareRequestWithINVALID_SHARE_ID() {

        PowerMockito.when(redisService.getShareCacheDO(Mockito.anyString())).thenReturn(null);

        Result expectedResult = Result.Error(-2012, "INVALID_SHARE_ID");
        Result actualResult = deviceShareService.checkShareRequest("testShareId", -1, "vicoo");

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void checkShareRequestWithSHARE_ID_EXPIRED() {
        ShareCacheDO shareCacheDO = new ShareCacheDO();
        shareCacheDO.setInsertTime(1);

        PowerMockito.when(redisService.getShareCacheDO(Mockito.anyString())).thenReturn(shareCacheDO);
        PowerMockito.when(PhosUtils.getUTCStamp()).thenReturn(10000);

        Result expectedResult = Result.Error(-2011, "SHARE_ID_EXPIRED");
        Result actualResult = deviceShareService.checkShareRequest("testShareId", -1, "vicoo");

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void checkShareRequestWithSUCCESS() {
        ShareCacheDO shareCacheDO = new ShareCacheDO();
        shareCacheDO.setInsertTime(1);

        PowerMockito.when(redisService.getShareCacheDO(Mockito.anyString())).thenReturn(shareCacheDO);
        PowerMockito.when(PhosUtils.getUTCStamp()).thenReturn(1);

        Result expectedResult = Result.Success();
        Result actualResult = deviceShareService.checkShareRequest("testShareId", -1, "vicoo");

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void requireShareWithLanguageZh() {
        ShareCacheDO shareCacheDO = new ShareCacheDO();
        shareCacheDO.setAdminId(1);
        PowerMockito.when(redisService.getShareCacheDO(Mockito.any())).thenReturn(shareCacheDO);

        when(deviceRelationshipService.insertApproval(any(), any())).thenReturn(1);

        PowerMockito.when(PhosUtils.getUTCStamp()).thenReturn(100);

        doNothing().when(pushService).pushGuestRequestMessages(any(), any());

        GuestRequestEntity guestRequestEntity = new GuestRequestEntity();
        when(centerNotifyConfig.getMessage()).thenReturn(getNotifyMessage());
        Result expectedResult = new Result(guestRequestEntity);

        PushInfo pushInfo = new PushInfo();
        pushInfo.setMsgType(PushTypeEnums.PUSH_XINGE.getCode());
        when(pushInfoService.getPushInfo(any())).thenReturn(pushInfo);

        when(deviceInfoService.getAllPushDetailBySerialNumberNoFollowPushIgnored(any())).thenReturn(Lists.newArrayList());

        User user = new User();
        user.setTenantId("vicoo");
        user.setLanguage("zh");
//        when(configService.getConfigMessage(any(), any(), any())).thenReturn("");
        when(userService.queryUserById(any())).thenReturn(user);
        Result actualResult = deviceShareService.requireShare(1, new RequireApprovalRequest());


        assertEquals(expectedResult.getClass(), actualResult.getClass());
    }

    @Test
    public void requireShareWithLanguageEn() {
        ShareCacheDO shareCacheDO = new ShareCacheDO();
        shareCacheDO.setAdminId(1);
        PowerMockito.when(redisService.getShareCacheDO(Mockito.any())).thenReturn(shareCacheDO);

        when(deviceRelationshipService.insertApproval(any(), any())).thenReturn(1);

        PowerMockito.when(PhosUtils.getUTCStamp()).thenReturn(100);

        doNothing().when(pushService).pushGuestRequestMessages(any(), any());

        GuestRequestEntity guestRequestEntity = new GuestRequestEntity();

        Result expectedResult = new Result(guestRequestEntity);
        PushInfo pushInfo = new PushInfo();
        pushInfo.setMsgType(PushTypeEnums.PUSH_XINGE.getCode());
        when(pushInfoService.getPushInfo(any())).thenReturn(pushInfo);

        when(deviceInfoService.getAllPushDetailBySerialNumberNoFollowPushIgnored(any())).thenReturn(Lists.newArrayList());

        User user = new User();
        user.setTenantId("vicoo");
        user.setLanguage("zh");
        when(centerNotifyConfig.getMessage()).thenReturn(getNotifyMessage());
        when(userService.queryUserById(any())).thenReturn(user);
        Result actualResult = deviceShareService.requireShare(1, new RequireApprovalRequest());

        assertEquals(expectedResult.getClass(), actualResult.getClass());
    }

    @Test
    @DisplayName("分享记录不存在")
    public void handleShareRequestNoRecord() {
        ApprovalDO approvalDO = new ApprovalDO();
        approvalDO.setStatus(-1);
        PowerMockito.when(JSON.parseObject(JSON.toJSONString(any()), ApprovalDO.class)).thenReturn(approvalDO);

        when(shareDAO.getSingleApprovalDetail(any())).thenReturn(null);
        Result expectedResult = Result.Error(REQUEST_EXPIRED);

        HandleApprovalRequest handleApprovalRequest = new HandleApprovalRequest();
        Result actualResult = deviceShareService.handleShareRequest(1, handleApprovalRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("处理分享请求-非admin")
    public void handleShareRequestWithNotAdmin() {
        Integer userId = 1;
        ApprovalDO approvalDO = new ApprovalDO();
        approvalDO.setStatus(-1);
        PowerMockito.when(JSON.parseObject(JSON.toJSONString(any()), ApprovalDO.class)).thenReturn(approvalDO);

        when(shareDAO.getSingleApprovalDetail(any())).thenReturn(new ShareQueryResponseDO());
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(0);

        when(shareDAO.setApprovalStatus(any())).thenReturn(1);

        Result expectedResult = Result.Error(REQUEST_EXPIRED);

        HandleApprovalRequest handleApprovalRequest = new HandleApprovalRequest();
        Result actualResult = deviceShareService.handleShareRequest(userId, handleApprovalRequest);

        assertEquals(expectedResult, actualResult);
    }

//    @Test
    public void handleShareRequestWithNoSuchApproval() {
        ApprovalDO approvalDO = new ApprovalDO();
        approvalDO.setStatus(0);
        PowerMockito.when(JSON.parseObject(JSON.toJSONString(any()), ApprovalDO.class)).thenReturn(approvalDO);

        ShareCacheDO shareCacheDO = new ShareCacheDO();
        PowerMockito.when(redisService.getShareCacheDO(any())).thenReturn(shareCacheDO);
        when(shareDAO.getSingleApprovalDetail(any())).thenReturn(new ShareQueryResponseDO());
        when(redisService.getShareCacheDO(any())).thenReturn(new ShareCacheDO());

        Result expectedResult = Result.Failure("No such approval.");

        HandleApprovalRequest handleApprovalRequest = new HandleApprovalRequest();
        handleApprovalRequest.setTargetId(123);
        Result actualResult = deviceShareService.handleShareRequest(1, handleApprovalRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void handleShareRequestWithSendMessage() {
        Integer userId = 1;
        ApprovalDO approvalDO = new ApprovalDO();
        approvalDO.setStatus(0);
        approvalDO.setTargetId(1);
        PowerMockito.when(JSON.parseObject(JSON.toJSONString(any()), ApprovalDO.class)).thenReturn(approvalDO);


        ShareQueryResponseDO shareQueryResponseDO = new ShareQueryResponseDO();
        shareQueryResponseDO.setId(1L);
        when(shareDAO.getSingleApprovalDetail(any())).thenReturn(shareQueryResponseDO);

        when(userRoleService.getDeviceAdminUser(any())).thenReturn(userId);

        when(shareDAO.setApprovalStatus(any())).thenReturn(1);

        when(userRoleService.saveUserRole(any())).thenReturn(1);

        doNothing().when(msgHelper).Send(any(), any());
        when(centerNotifyConfig.getMessage()).thenReturn(getNotifyMessage());
        MsgResponseDO msgResponseDO = new MsgResponseDO();
        msgResponseDO.setUserId(1);
        msgResponseDO.setMsgType(0);
        List<MsgResponseDO> list = new ArrayList<>();
        list.add(msgResponseDO);
        when(deviceInfoService.getAllPushDetailBySerialNumber(any())).thenReturn(list);

        when(openApiWebhookService.callWebhookForDeviceShareHandle(any(),any(),any(),any())).thenReturn(true);
        Result expectedResult = Result.Success();

        HandleApprovalRequest handleApprovalRequest = new HandleApprovalRequest();

        PushInfo pushInfo = new PushInfo();
        pushInfo.setMsgType(PushTypeEnums.PUSH_XINGE.getCode());
        when(pushInfoService.getPushInfo(any())).thenReturn(pushInfo);

        when(deviceInfoService.getAllPushDetailBySerialNumberNoFollowPushIgnored(any())).thenReturn(Lists.newArrayList());

        User user = new User();
        user.setTenantId("vicoo");
        user.setLanguage("zh");

        when(userService.queryUserById(any())).thenReturn(user);
        Result actualResult = deviceShareService.handleShareRequest(userId, handleApprovalRequest);

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void handleShareRequestWithNotSendMessage() {
        Integer userId = 1;
        ApprovalDO approvalDO = new ApprovalDO();
        approvalDO.setStatus(0);
        approvalDO.setTargetId(0);
        PowerMockito.when(JSON.parseObject(JSON.toJSONString(any()), ApprovalDO.class)).thenReturn(approvalDO);

        ShareQueryResponseDO shareQueryResponseDO = new ShareQueryResponseDO();
        shareQueryResponseDO.setId(1L);
        when(shareDAO.getSingleApprovalDetail(any())).thenReturn(shareQueryResponseDO);

        when(userRoleService.getDeviceAdminUser(any())).thenReturn(userId);

        when(shareDAO.setApprovalStatus(any())).thenReturn(1);

        when(userRoleService.saveUserRole(any())).thenReturn(1);

        MsgResponseDO msgResponseDO = new MsgResponseDO();
        msgResponseDO.setUserId(1);
        msgResponseDO.setMsgType(0);
        List<MsgResponseDO> list = new ArrayList<>();
        list.add(msgResponseDO);
        when(deviceInfoService.getAllPushDetailBySerialNumber(any())).thenReturn(list);

        when(openApiWebhookService.callWebhookForDeviceShareHandle(any(),any(),any(),any())).thenReturn(true);


        Result expectedResult = Result.SqlOperationResult(1);
        PushInfo pushInfo = new PushInfo();
        pushInfo.setMsgType(PushTypeEnums.PUSH_XINGE.getCode());
        when(pushInfoService.getPushInfo(any())).thenReturn(pushInfo);

        when(deviceInfoService.getAllPushDetailBySerialNumberNoFollowPushIgnored(any())).thenReturn(Lists.newArrayList());



        User user = new User();
        user.setTenantId("vicoo");
        user.setLanguage("zh");
        when(centerNotifyConfig.getMessage()).thenReturn(getNotifyMessage());
        when(userService.queryUserById(any())).thenReturn(user);
        doNothing().when(pushXingeService).pushMessage(any(), any());
        HandleApprovalRequest handleApprovalRequest = new HandleApprovalRequest();
        Result actualResult = deviceShareService.handleShareRequest(userId, handleApprovalRequest);

        assertEquals(expectedResult, actualResult);
    }

    private Map<String, Map<String, Map<String, String>>> getNotifyMessage() {
        Map<String, Map<String, Map<String, String>>> notifyMessage = Maps.newHashMap();
        Map<String, String> keyMap = Maps.newHashMap();
        keyMap.put("GuestRequestTitle", "GuestRequestTitle");
        keyMap.put("GuestRequestBody", "GuestRequestBody");

        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        languageMap.put("zh", keyMap);
        languageMap.put("en", keyMap);
        notifyMessage.put("vicoo", languageMap);
        return notifyMessage;
    }


    @Test(expected = ParamException.class)
    @DisplayName("发送邀请邮件-条件验证")
    public void test_shareInviteEmail_param_shareId(){
        String shareEmail = "shareEmail";
        Integer userId = 1;

        CkApprovalRequest request = new CkApprovalRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        request.setApp(app);
        request.setEmail(shareEmail);
        request.setLanguage(APP_LANGUAGE_ZH);

        request.setShareDeviceType(DeviceShareTypeEnum.HUB.getCode());
        deviceShareService.shareInviteEmail(userId,request);
    }
    @Test(expected = ParamException.class)
    @DisplayName("发送邀请邮件-条件验证")
    public void test_shareInviteEmail_param_sn(){
        String shareEmail = "shareEmail";
        Integer userId = 1;

        CkApprovalRequest request = new CkApprovalRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        request.setApp(app);
        request.setEmail(shareEmail);
        request.setLanguage(APP_LANGUAGE_ZH);

        request.setShareDeviceType(DeviceShareTypeEnum.CLOUD.getCode());
        deviceShareService.shareInviteEmail(userId,request);
    }

    @Test(expected = ParamException.class)
    @DisplayName("发送邀请邮件-条件验证")
    public void test_shareInviteEmail_param_cloud_shareid(){
        String shareEmail = "shareEmail";
        Integer userId = 1;

        CkApprovalRequest request = new CkApprovalRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        request.setApp(app);
        request.setEmail(shareEmail);
        request.setLanguage(APP_LANGUAGE_ZH);

        request.setShareDeviceType(DeviceShareTypeEnum.CLOUD.getCode());
        request.setSerialNumber("sn");
        request.setShareId("shareid");
        deviceShareService.shareInviteEmail(userId,request);
    }

    @Test
    @DisplayName("发送邀请邮件")
    public void test_shareInviteEmail(){
        String shareEmail = "shareEmail";
        Integer userId = 1;

        CkApprovalRequest request = new CkApprovalRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        request.setApp(app);
        request.setEmail(shareEmail);
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setShareId("shareId");

        Result exceptedResult ;
        Result actualResult ;
        {
            //不能分享给自己
            when(userService.queryUserById(any())).thenReturn(User.builder().email(shareEmail).build());
            exceptedResult = Result.Error(SHARE_TO_SELF);
            actualResult = deviceShareService.shareInviteEmail(userId,request);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        when(userService.queryUserById(any())).thenReturn(User.builder().name("name").email("email").build());
        {
            //分享id已通知过
            when(shareApprovalLocalDAO.queryByShareId(any())).thenReturn(ShareApprovalDO.builder().id(1).build());
            exceptedResult = Result.Success();
            actualResult = deviceShareService.shareInviteEmail(userId,request);
            Assert.assertEquals(exceptedResult,actualResult);
        }

        when(shareApprovalLocalDAO.queryByShareId(any())).thenReturn(null);
        when(mailConfirmService.getSendEmailTitle(any(),any())).thenReturn("");
        when(mailConfirmService.getSendEmailBody(any(),any(),any(),any(),any(),any())).thenReturn("");
        when(tenantTierConfig.queryAppNameByTenant(any())).thenReturn("");
        when(appAccountConfig.queryEmailAccount(any())).thenReturn(new EmailAccount());
        when(Mail.SendHtmlEmail(any(),any(),any(),any())).thenReturn(true);
        when(shareApprovalLocalDAO.queryByShareId(any())).thenReturn(null);
        when(userService.getUserByEmailAndTenantId(shareEmail,null, TENANTID_VICOO)).thenReturn(null);
        when(shareApprovalLocalDAO.insertApproval(any())).thenReturn(1);

        deviceShareService.shareInviteEmail(userId,request);
    }

    @Test
    @DisplayName("处理基站分享")
    public void test_deviceShareHandLocal(){
        Integer userId = 1;
        String shareEmail = "shareEmail";
        HandleApprovalRequest request = new HandleApprovalRequest();
        request.setTargetId(1);
        Result exceptedResult ;
        Result actualResult ;
        {
            when(shareApprovalLocalDAO.queryByShareId(any())).thenReturn(null);
            exceptedResult = Result.Error(INVALID_PARAMS);
            actualResult = deviceShareService.deviceShareHandLocal(userId,request);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            when(shareApprovalLocalDAO.queryByShareId(any())).thenReturn(new ShareApprovalDO());
            when(shareApprovalLocalDAO.setApprovalStatus(any())).thenReturn(1);

            when(userService.queryUserById(any())).thenReturn(new User());
            actualResult = deviceShareService.deviceShareHandLocal(userId,request);
            Assert.assertTrue(actualResult.getResult().equals(0));
        }
    }

    @Test
    public void test_stationDeviceShareCancel(){
        Integer userId = 1;
        List<String> shareIdList = Arrays.asList("1","2");
        when(shareApprovalLocalDAO.queryByShareId("1")).thenReturn(null);
        when(shareApprovalLocalDAO.queryByShareId("2")).thenReturn(ShareApprovalDO.builder().adminId(1).build());
        deviceShareService.stationDeviceShareCancel(userId,shareIdList);
        verify(shareApprovalLocalDAO, times(1)).setApprovalStatus(any());
    }

    @Test(expected = BaseException.class)
    public void test_stationDeviceShareCancel_exception(){
        Integer userId = 1;
        List<String> shareIdList = Arrays.asList("1");
        when(shareApprovalLocalDAO.queryByShareId("1")).thenReturn(ShareApprovalDO.builder().adminId(2).build());
        deviceShareService.stationDeviceShareCancel(userId,shareIdList);
    }


    @Test
    public void queryDeviceShared(){
        CkApprovalRequest request = new CkApprovalRequest();
        request.setEmail("email");
        User targetUser = null;
        Integer exceptedResult ;
        Integer actualResult ;
        {
            // local-已分享
            request.setShareDeviceType(DeviceShareTypeEnum.HUB.getCode());
            when(shareApprovalLocalDAO.queryByShareId(any())).thenReturn(ShareApprovalDO.builder().id(1).build());

            exceptedResult = 1;
            actualResult = deviceShareService.queryDeviceShared(request,null);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            // local-未分享
            request.setShareDeviceType(DeviceShareTypeEnum.HUB.getCode());
            when(shareApprovalLocalDAO.queryByShareId(any())).thenReturn(null);

            exceptedResult = 0;
            actualResult = deviceShareService.queryDeviceShared(request,null);
            Assert.assertEquals(exceptedResult,actualResult);
        }

        request.setShareDeviceType(DeviceShareTypeEnum.CLOUD.getCode());
        {
            targetUser = User.builder().id(1).email("email").build();
            when(userRoleService.getUserRoleDOByUserIdAndSerialNumber(any(),any())).thenReturn(null);
            when(shareApprovalLocalDAO.queryDeviceShareingList(any(),any(),any())).thenReturn(Lists.newArrayList());

            exceptedResult = 0;
            actualResult = deviceShareService.queryDeviceShared(request,targetUser);
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }
}