package com.addx.iotcamera.service.lettuce.aop.aspect;

import com.addx.iotcamera.service.lettuce.aop.annotation.CacheDataTransfer;
import com.addx.iotcamera.service.lettuce.service.LettuceDataTransClient;
import com.addx.iotcamera.service.lettuce.service.RedisDataTransClient;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class CacheDataTransferAspectTest {

    @InjectMocks
    CacheDataTransferAspect cacheDataTransferAspect;
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    RedisDataTransClient redisDataTransClient;
    @Mock
    LettuceDataTransClient lettuceDataTransClient;
    @Mock
    StringRedisTemplate stringRedisTemplate;
    @Mock
    RedisAdvancedClusterCommands<String, String> businessRedisClusterSyncClient;
    @Mock
    JoinPoint joinPoint;
    @Mock
    CacheDataTransfer cacheDataTransfer;

    String key = "a";
    String source = "a";
    String dest = "b";

    Object[] args = new Object[]{key};

    @Before
    public void before() {
        when(applicationContext.getBean("a")).thenReturn(stringRedisTemplate);
        when(applicationContext.getBean("b", RedisAdvancedClusterCommands.class)).thenReturn(businessRedisClusterSyncClient);
        when(joinPoint.getArgs()).thenReturn(args);
        when(cacheDataTransfer.source()).thenReturn("aaa");
        when(cacheDataTransfer.dest()).thenReturn("bbb");
    }

    @Test
    public void dataTransfer() {
        cacheDataTransferAspect.dataTransfer(joinPoint, cacheDataTransfer);
        when(cacheDataTransfer.source()).thenReturn(null);
        when(cacheDataTransfer.dest()).thenReturn(null);
        cacheDataTransferAspect.dataTransfer(joinPoint, cacheDataTransfer);

        when(joinPoint.getArgs()).thenReturn(null);
        cacheDataTransferAspect.dataTransfer(joinPoint, cacheDataTransfer);

        when(joinPoint.getArgs()).thenReturn(new Object[]{});
        cacheDataTransferAspect.dataTransfer(joinPoint, cacheDataTransfer);

        when(joinPoint.getArgs()).thenReturn(new Object[]{1});
        cacheDataTransferAspect.dataTransfer(joinPoint, cacheDataTransfer);


        cacheDataTransferAspect.dataTransfer(key, null, null);

        when(applicationContext.getBean("aaa")).thenReturn(stringRedisTemplate);
        cacheDataTransferAspect.dataTransfer(key, "aaa", "aaa");

        when(applicationContext.getBean("aaa")).thenReturn(businessRedisClusterSyncClient);
        cacheDataTransferAspect.dataTransfer(key, "aaa", "aaa");
    }

    @Test
    public void getDataSourceByName() {
        cacheDataTransferAspect.getDataSourceByName(null);
        cacheDataTransferAspect.getDataSourceByName("aaa");
        when(applicationContext.getBean("aaa")).thenThrow(new RuntimeException());
        cacheDataTransferAspect.getDataSourceByName("aaa");

        cacheDataTransferAspect.getDataSourceByName(null, null);
        cacheDataTransferAspect.getDataSourceByName("aaa", null);
        cacheDataTransferAspect.getDataSourceByName(null, Object.class);

        when(applicationContext.getBean("aaa", Object.class)).thenReturn(new Object());
        cacheDataTransferAspect.getDataSourceByName("aaa", Object.class);

        when(applicationContext.getBean("aaa", Object.class)).thenThrow(new RuntimeException());
        cacheDataTransferAspect.getDataSourceByName("aaa", Object.class);
    }

}