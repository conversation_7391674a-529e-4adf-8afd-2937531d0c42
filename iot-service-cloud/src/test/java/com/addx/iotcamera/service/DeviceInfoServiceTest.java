package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.additional_tier.AdditionalUserTierInfo;
import com.addx.iotcamera.bean.app.device.DeviceConfigRequest;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.device.FoundDeviceInfoQuery;
import com.addx.iotcamera.bean.device.FoundDeviceInfoResult;
import com.addx.iotcamera.bean.device.attributes.DeviceOnlineInfo;
import com.addx.iotcamera.bean.device.model.DeviceModelIconDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.DeviceApInfoDO;
import com.addx.iotcamera.bean.domain.device.DeviceReportEventDO;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.bean.openapi.DeviceVipLog;
import com.addx.iotcamera.bean.openapi.PaasVipLevel;
import com.addx.iotcamera.bean.response.device.DevicePushImage;
import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.config.PowerStatConfig;
import com.addx.iotcamera.config.apollo.DnsServerConfig;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.dao.device.IDeviceApInfoDAO;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.enums.EReportEventSource;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.enums.VipAiAnalyzeType;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.helper.DeviceDetectHelper;
import com.addx.iotcamera.helper.TraceIdHelper;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.service.device.*;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import com.addx.iotcamera.service.device.model.DeviceModelTenantService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.service.video.VideoGenerateService;
import com.addx.iotcamera.service.video.VideoNotifyService;
import com.addx.iotcamera.util.DeviceApInfoGenerateUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.proto.deviceMsg.PbSyncTimeZoneOffset;
import org.addx.iot.common.proto.deviceMsg.PbSyncTimeZoneOffsetResponse;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.apache.commons.lang3.RandomUtils;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.service.DeviceInfoService.REDIS_KEY_FILL_DEVICE_STATUS_REASON_TENANT_IDS;
import static com.addx.iotcamera.service.openapi.DeviceVipService.MAX_TIME;
import static com.addx.iotcamera.service.openapi.DeviceVipService.MIN_TIME;
import static com.addx.iotcamera.util.DTIMUtil.calculateDTIMFatigue;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
@ExtendWith(MockitoExtension.class)

public class DeviceInfoServiceTest {

    @InjectMocks
    @Spy
    private DeviceInfoService deviceInfoService;

    @Mock
    private CloudDeviceSupport mockCloudDeviceSupport;

    @Mock
    private DeviceDetectHelper deviceDetectHelper;

    @Mock
    private VipService vipService;

    @Mock
    private TenantSettingService tenantSettingService;

    @Mock
    private AdditionalUserTierService additionalUserTierService;

    @Mock
    private DeviceAiSettingsService deviceAiSettingsService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private RedisService redisService;

    @Mock
    private PersistentRedisService persistentRedisService;

    @Mock
    private S3Service s3Service;

    @Mock
    private FactoryDataQueryService factoryDataQueryService;

    @Mock
    private DeviceModelIconService deviceModelIconService;

    @Mock
    private DeviceService deviceService;

    @Mock
    private DeviceSettingService deviceSettingService;

    @Mock
    private IDeviceApInfoDAO deviceApInfoDAO;

    @Mock
    private DeviceBatteryService deviceBatteryService;

    @Mock
    private DoorbellService doorbellService;

    @Mock
    private DeviceCallService deviceCallService;

    @Mock
    private UserService userService;

    @Mock
    private IDeviceDAO deviceDAO;

    @Mock
    private LocationInfoService locationInfoService;

    @Mock
    private DeviceStatusService deviceStatusService;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private DeviceModelEventService deviceModelEventService;

    @Mock
    private DeviceOTAService deviceOTAService;

    @Mock
    private VipService paasVipService;

    @Mock
    private DeviceConfigService deviceConfigService;

    @Mock
    private DeviceModelConfigService deviceModelConfigService;

    @Mock
    private DeviceSdCardStatusService deviceSdCardStatusService;

    @Mock
    private StateMachineService stateMachineService;

    @Mock
    WowzaService wowzaService;

    @Mock
    private DnsServerConfig dnsServerConfig;

    @Mock
    private ReportLogService reportLogService;

    @Mock
    private LibraryService libraryService;

    @Mock
    private TraceIdHelper traceIdHelper;

    @Mock
    private PowerStatConfig powerStatConfig;

    @Mock
    private VernemqPublisher vernemqPublisher;

    @Mock
    private UserVipService userVipService;

    @Mock
    private DeviceSupportService deviceSupportService;

    @Mock
    private DeviceEnumMappingService deviceEnumMappingService;
    @Mock
    private DeviceModelService deviceModelService;
    @Mock
    private PushService pushService;
    @Mock
    private OpenApiWebhookService openApiWebhookService;
    @Mock
    private DevicePlatformEventPublisher devicePlatformEventPublisher;
    @Mock
    private IKissService kissService;
    @Mock
    private VideoNotifyService videoNotifyService;
    @Mock
    private VideoGenerateService videoGenerateService;
    @Mock
    private DeviceFloodLightService deviceFloodLightService;

    @Mock
    private Device4GService device4GService;

    @Mock
    private DeviceModelTenantService deviceModelTenantService;

    @Before
    public void before() {
        deviceInfoService.setGson(new Gson());
        when(powerStatConfig.getPowerStatSwitch()).thenReturn(true);
        when(powerStatConfig.getDischargeStatCapThresh()).thenReturn(5);
        when(powerStatConfig.getDischargeNonReportDays()).thenReturn(10);
        when(powerStatConfig.getChargingStatCapThresh()).thenReturn(20);
        when(powerStatConfig.getChargingNonReportDays()).thenReturn(5);

        when(deviceStatusService.queryDeviceStatusBySerialNumber(any())).thenReturn(new DeviceStatusDO());
        when(tenantSettingService.getTenantSettingByTenantId(any())).thenReturn(new TenantSetting());
    }

    /**
     * 用户名下无设备
     */
    @Test
    public void checkDevicePushImageNoDevice() {
        List<DevicePushImage> devicePushImageList = Lists.newArrayList();
        when(userRoleService.getUserRoleByUserId(any())).thenReturn(new ArrayList<UserRoleDO>());
        List<DevicePushImage> devicePushImageListResult = deviceInfoService.queryUserDevicePushImage(any());
        assertEquals(devicePushImageList, devicePushImageListResult);
    }

    /**
     * 用户名下设备无推送记录
     */
    @Test
    public void checkDevicePushImageNoRedisValue() {
        String serialNumber = "c8adb5ac6caf00b8052d4b1e858a7816";
        List<DevicePushImage> devicePushImageList = Lists.newArrayList();
        List<UserRoleDO> userRoleDOList = Lists.newArrayList();
        UserRoleDO roleDO = new UserRoleDO();
        roleDO.setAdminId(1);
        roleDO.setSerialNumber(serialNumber);
        userRoleDOList.add(roleDO);
        when(userRoleService.getUserRoleByUserId(any())).thenReturn(userRoleDOList);
        when(redisService.get(any())).thenReturn(null);
        List<DevicePushImage> devicePushImageListResult = deviceInfoService.queryUserDevicePushImage(any());
        assertEquals(devicePushImageList, devicePushImageListResult);
    }

    /**
     * 用户名下设备有推送记录
     */
    @Test
    public void checkDevicePushImageHasRedisValue() {
        String serialNumber = "c8adb5ac6caf00b8052d4b1e858a7816";
        String imageUrl = "imageUrl";
        DevicePushImage devicePushImage = DevicePushImage.builder()
                .lastPushImageUrl(imageUrl)
                .lastPushTime(Instant.now().getEpochSecond())
                .serialNumber(serialNumber)
                .build();

        List<DevicePushImage> devicePushImageList = Lists.newArrayList();
        devicePushImageList.add(devicePushImage);
        List<UserRoleDO> userRoleDOList = Lists.newArrayList();
        UserRoleDO roleDO = new UserRoleDO();
        roleDO.setAdminId(1);
        roleDO.setSerialNumber(serialNumber);
        userRoleDOList.add(roleDO);
        when(userRoleService.getUserRoleByUserId(any())).thenReturn(userRoleDOList);

        Gson gson = new Gson();
        when(persistentRedisService.get(any())).thenReturn(gson.toJson(devicePushImage));
        when(s3Service.preSignUrl(any())).thenReturn(imageUrl);
        List<DevicePushImage> devicePushImageListResult = deviceInfoService.queryUserDevicePushImage(any());
        assertEquals(devicePushImageList, devicePushImageListResult);
    }

    @Test
    public void queryDeviceApInfoTest() throws Exception {
        DeviceManufactureTableDO deviceManufactureTableDO = new DeviceManufactureTableDO();
        deviceManufactureTableDO.setSerialNumber("c8adb5ac6caf00b8052d4b1e858a7816");
        deviceManufactureTableDO.setUserSn("AICa12312312312");
        deviceManufactureTableDO.setModelNo("CG1");
        deviceManufactureTableDO.setApInfo("{}");
        when(factoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(any(), any())).thenReturn(deviceManufactureTableDO);

        DeviceModelIconDO deviceModelIconDO = new DeviceModelIconDO();
        deviceModelIconDO.setSmallIconUrl("http://www.baidu.com");
        when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(deviceModelIconDO);

        when(deviceService.getAllDeviceInfo(any())).thenReturn(null);

        when(deviceApInfoDAO.queryBySn(any())).thenReturn(null);

        DeviceApInfoGenerateUtil deviceApInfoGenerateUtil = new DeviceApInfoGenerateUtil();
        deviceApInfoGenerateUtil.setVersionMap(Collections.singletonMap("00", "{\"ssid\":\"empty_tenant_%s\", \"passwprd\":\"%s\"}"));
        deviceApInfoGenerateUtil.afterPropertiesSet();

        when(userService.queryTenantIdById(any())).thenReturn(TENANTID_VICOO);
        when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(new HashSet<>(Collections.singletonList("CG1")));


        DeviceApInfoDO deviceApInfoDO = deviceInfoService.queryDeviceApInfo(1, deviceManufactureTableDO.getSerialNumber(), deviceManufactureTableDO.getUserSn(), "00");
        assertNotNull(deviceApInfoDO);
        assertNotNull(deviceApInfoDO.getApInfo());
        assertNotNull(deviceApInfoDO.getSmallIcon());

        deviceApInfoGenerateUtil.setVersionMap(Collections.singletonMap("02_null", "{\"ssid\":\"empty_tenant_%s\", \"passwprd\":\"%s\"}"));
        deviceApInfoGenerateUtil.afterPropertiesSet();
        deviceApInfoDO = deviceInfoService.queryDeviceApInfo(1, deviceManufactureTableDO.getSerialNumber(), deviceManufactureTableDO.getUserSn(), "02");
        assertNotNull(deviceApInfoDO);
        assertNotNull(deviceApInfoDO.getApInfo());
        assertNotNull(deviceApInfoDO.getSmallIcon());
    }

    @Test
    public void testUpdateDeviceApInfoFromSync() {
        DeviceManufactureTableDO deviceManufactureTableDO = new DeviceManufactureTableDO();
        deviceManufactureTableDO.setSerialNumber("c8adb5ac6caf00b8052d4b1e858a7816");
        deviceManufactureTableDO.setUserSn("AICa12312312312");
        deviceManufactureTableDO.setModelNo("CG1");
        deviceManufactureTableDO.setApInfo("{}");
        when(factoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(any(), any())).thenReturn(deviceManufactureTableDO);

        when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().serialNumber(deviceManufactureTableDO.getSerialNumber()).userSn(deviceManufactureTableDO.getUserSn()).build());

        String deviceApInfo = deviceInfoService.updateDeviceApInfoFromSync(deviceManufactureTableDO.getSerialNumber(), null);
        assertNotNull(deviceApInfo);

        deviceManufactureTableDO.setApInfo(null);
        when(factoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(any(), any())).thenReturn(deviceManufactureTableDO);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().serialNumber(deviceManufactureTableDO.getSerialNumber()).userSn(deviceManufactureTableDO.getUserSn()).build());
        DeviceApInfoGenerateUtil deviceApInfoGenerateUtil = new DeviceApInfoGenerateUtil();
        deviceApInfoGenerateUtil.setVersionMap(Collections.singletonMap("01", "{\"ssid\":\"empty_tenant_%s\", \"passwprd\":\"%s\"}"));
        deviceApInfoGenerateUtil.afterPropertiesSet();
        deviceApInfo = deviceInfoService.updateDeviceApInfoFromSync(deviceManufactureTableDO.getSerialNumber(), null);
        assertNotNull(deviceApInfo);
    }


    @Test
    public void test_queryFoundDeviceInfo() {
        when(factoryDataQueryService.queryDeviceManufactureByUserSns(any())).thenAnswer(it -> {
            List<String> userSns = it.getArgument(0);
            return userSns.stream().map(userSn -> {
                if ("ACIADDX20200004".equals(userSn)) return DeviceManufactureTableDO.builder()
                        .userSn(userSn).modelNo(null).build();
                if ("ACIADDX20200006".equals(userSn)) return DeviceManufactureTableDO.builder()
                        .userSn(userSn).modelNo("CG6-3").build();
                if ("ACIADDX20200008".equals(userSn)) return DeviceManufactureTableDO.builder()
                        .userSn(userSn).modelNo("CG6-4").build();
                return null;
            }).filter(it2 -> it2 != null).collect(Collectors.toList());
        });
        when(deviceModelIconService.queryDeviceModelIcon("CG6-4")).thenAnswer(it -> {
            String modelNo = it.getArgument(0);
            return new DeviceModelIconDO().setModelNo(modelNo).setIconUrl("icon_icon_icon")
                    .setSmallIconUrl("small_icon_icon_icon");
        });
        FoundDeviceInfoQuery input = new FoundDeviceInfoQuery()
                .setUserSns(Arrays.asList("ACIADDX20200001", "ACIADDX20200004", "ACIADDX20200006", "ACIADDX20200008"));

        Result<FoundDeviceInfoResult> result = deviceInfoService.queryFoundDeviceInfo(input);
        Assert.assertEquals(4, result.getData().getDevices().size());
        long statusNum = result.getData().getDevices().stream().map(it -> it.getQueryStatus()).distinct().count();
        Assert.assertEquals(4, statusNum);
    }

    @Test
    public void test_getSingleDevice_4G() {
        Integer userId = 1;
        DeviceDO queryDeviceDO = DeviceDO.builder().serialNumber("sn").userId(userId).build();
        when(deviceService.getAllDeviceInfo(any())).thenReturn(queryDeviceDO);

        List<DeviceDO> deviceDOList = Arrays.asList(
                DeviceDO.builder().serialNumber("sn1").iccid("iccid").build()
        );

        doReturn(deviceDOList).when(deviceInfoService).listDevicesByUserId(any(), any());

        when(vipService.isVipDevice(any(), any())).thenReturn(false);
        when(deviceSettingService.getDeviceSettingsBySerialNumber(any())).thenReturn(null);
        when(deviceSdCardStatusService.querySdCard(any())).thenReturn(null);

        DeviceDO deviceDOResult = deviceInfoService.getSingleDevice(queryDeviceDO, userId);
        Assert.assertEquals(deviceDOResult.getSerialNumber(), "sn1");
    }

    @Test
    public void test_getSingleDevice() throws Exception {
        Integer userId = 1;
        DeviceSettingsDO deviceSettingsDO = DeviceSettingsDO.builder()
                .serialNumber("sn_01")
                .liveAudioToggleOn(true).recordingAudioToggleOn(true).liveSpeakerVolume(100)
                .alarmWhenRemoveToggleOn(false)
                .build();

        when(deviceSettingService.getDeviceSettingsBySerialNumber(any())).thenReturn(deviceSettingsDO);
        when(paasVipService.isVipDevice(any(), any())).thenReturn(false);
        deviceInfoService.getDeviceInfoFromSetting(DeviceDO.builder().serialNumber("sn_01").build());

        when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().serialNumber("sn_01").build());
        when(userService.queryUserById(any())).thenReturn(new User());
        when(userRoleService.getUserRoleByUserId(any(), any())).thenReturn(Arrays.asList(UserRoleDO.builder().serialNumber("sn_01").build(), UserRoleDO.builder().serialNumber("sn_02").build(), UserRoleDO.builder().serialNumber("sn_03").build()));
        when(deviceDAO.queryDeviceBynSerialNumbers(any())).thenReturn(Collections.singletonList(DeviceDO.builder().serialNumber("sn_01").build()));
        when(locationInfoService.queryLocationDOs(any())).thenReturn(Collections.emptyMap());
        when(userService.queryUserMap(any())).thenReturn(Collections.emptyMap());
        when(deviceStatusService.queryDeviceStatus(any())).thenReturn(Collections.emptyMap());
        when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(new DeviceManualDO());
        when(deviceOTAService.queryDeviceOtaBySerialNumber(anyList())).thenReturn(Collections.emptyMap());
        when(paasVipService.queryLastDeviceVip(any(), any())).thenReturn(new DeviceVipLog() {{
            setSerialNumber("sn_01");
            setVipLevel(1);
        }});
        when(deviceConfigService.queryDeviceAudio(any(), any())).thenReturn(new DeviceConfigRequest.DeviceAudio());
        when(deviceModelConfigService.queryDeviceModelConfig(any())).thenReturn(null);
        when(deviceSdCardStatusService.querySdCard(any())).thenReturn(null);
        when(vipService.queryLastDeviceVip(any(), any())).thenReturn(new DeviceVipLog().setVipLevel(1));

        DeviceDO deviceDO = deviceInfoService.getSingleDevice(DeviceDO.builder().serialNumber("sn_01").build(), userId);
        Assert.assertTrue(deviceDO != null && deviceDO.getSerialNumber().equalsIgnoreCase("sn_01"));
        // unit test
        when(deviceService.getAllDeviceInfo("sn_02")).thenReturn(null);
        DeviceDO deviceDO2 = deviceInfoService.getSingleDevice(DeviceDO.builder().serialNumber("sn_02").build(), userId);
        Assert.assertNull(deviceDO2);

        CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.builder().supportChangeCodec(1).build();
        when(deviceService.getAllDeviceInfo("sn_03")).thenReturn(DeviceDO.builder()
                .serialNumber("sn_03").defaultCodec("h265").codec("h264").showCodecChange(true)
                .deviceSupport(cloudDeviceSupport)
                .build());
        DeviceDO deviceDO3 = deviceInfoService.getSingleDevice(DeviceDO.builder().serialNumber("sn_03").build(), userId);
        Assert.assertNotNull(deviceDO3);
    }

    @Test
    public void test_handleDoorbellEventReport() {
        DeviceReportEventDO deviceReportEventDO = new DeviceReportEventDO();
        deviceReportEventDO.setId(1);
        deviceReportEventDO.setTime(1701166660L);
        deviceReportEventDO.setSerialNumber("");
        deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue() {{
            setEvent(EReportEvent.DOORBELL_PRESS.getEventId());
        }});


        when(videoGenerateService.isEnable(any(), any())).thenReturn(false);
        doNothing().when(doorbellService).onRemove(any());
        Result result = deviceInfoService.handleEventReport(deviceReportEventDO);
        Assert.assertTrue(result != null && result.getResult().intValue() == 0);

        deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue() {{
            setEvent(EReportEvent.DOORBELL_REMOVE.getEventId());
        }});
        result = deviceInfoService.handleEventReport(deviceReportEventDO);
        Assert.assertTrue(result != null && result.getResult().intValue() == 0);

        deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue() {{
            setEvent(EReportEvent.DEVICE_CALL.getEventId());
        }});
        result = deviceInfoService.handleEventReport(deviceReportEventDO);
        Assert.assertTrue(result != null && result.getResult().intValue() == 0);

        when(wowzaService.getRecWowzaServerWithLeastLoad(any())).thenReturn(new StreamServerDO());
        when(vernemqPublisher.reportEventResponse(anyString(), any(), anyInt())).thenReturn(true);
        deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue() {{
            setEvent(EReportEvent.PIR.getEventId());
        }});
        when(device4GService.queryDevice4GSimDO(any())).thenReturn(null);

        result = deviceInfoService.handleEventReport(deviceReportEventDO);
        Assert.assertTrue(result != null && result.getResult().intValue() == 0);

       deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue() {{
//           setEvent(EReportEvent.CRY.getEventId());
       }});
       result = deviceInfoService.handleEventReport(deviceReportEventDO);
       Assert.assertTrue(result != null && result.getResult().intValue() == 0);

        deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue() {{
            setEvent(EReportEvent.DEVICE_CALL.getEventId());
        }});
        result = deviceInfoService.handleEventReport(deviceReportEventDO);
        Assert.assertTrue(result != null && result.getResult().intValue() == 0);

        deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue() {{
            setEvent(EReportEvent.DOORBELL_PRESS.getEventId());
        }});
        result = deviceInfoService.handleEventReport(deviceReportEventDO);
        Assert.assertTrue(result != null && result.getResult().intValue() == 0);

        deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue() {{
            setEvent(EReportEvent.DOORBELL_REMOVE.getEventId());
        }});
        result = deviceInfoService.handleEventReport(deviceReportEventDO);
        Assert.assertTrue(result != null && result.getResult().intValue() == 0);

        deviceReportEventDO.setValue(deviceReportEventDO.new ReportEventRequestValue() {{
            setEvent(EReportEvent.AI_EDGE_EVENT.getEventId());
        }});
        result = deviceInfoService.handleEventReport(deviceReportEventDO);
        Assert.assertTrue(result != null && result.getResult().intValue() == 0);
    }


    @Test
    @DisplayName("替换设备型号config-未配置config")
    public void testDeviceModelForWebrtc_noModelConfig() {
        String serialNumber = "sn";
        when(deviceModelConfigService.queryDeviceModelConfig(any())).thenReturn(null);
        DeviceModel expectedDeviceModel = new DeviceModel();

        DeviceModel actualDeviceModel = deviceInfoService.processDeviceModelForWebrtc(serialNumber, null);
        assertEquals(expectedDeviceModel, actualDeviceModel);
    }

    @Test
    @DisplayName("替换设备型号config-deviceSupport null")
    public void testDeviceModelForWebrtc_deviceSupportNull() {
        String serialNumber = "sn";
        when(deviceModelConfigService.queryDeviceModelConfig(any())).thenReturn(new DeviceModel());
        DeviceModel expectedDeviceModel = new DeviceModel();

        DeviceModel actualDeviceModel = deviceInfoService.processDeviceModelForWebrtc(serialNumber, null);
        assertEquals(expectedDeviceModel, actualDeviceModel);
    }


    @Test
    @DisplayName("替换设备型号config-deviceSupport 不支持webrtc")
    public void testDeviceModelForWebrtc_deviceSupport_webrtcNoSupport() {
        String serialNumber = "sn";
        when(deviceModelConfigService.queryDeviceModelConfig(any())).thenReturn(new DeviceModel());
        DeviceModel expectedDeviceModel = new DeviceModel();
        CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.builder().supportWebrtc(0).build();
        DeviceModel actualDeviceModel = deviceInfoService.processDeviceModelForWebrtc(serialNumber, cloudDeviceSupport);
        assertEquals(expectedDeviceModel,actualDeviceModel);
    }

    @Test
    @DisplayName("替换设备型号config-deviceSupport 支持webrtc")
    public void testDeviceModelForWebrtc_deviceSupport_supportwebrtc() {
        String serialNumber = "sn";
        when(deviceModelConfigService.queryDeviceModelConfig(any())).thenReturn(new DeviceModel());
        DeviceModel expectedDeviceModel = new DeviceModel();
        expectedDeviceModel.setStreamProtocol("webrtc");

        CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.builder().supportWebrtc(1).build();
        DeviceModel actualDeviceModel = deviceInfoService.processDeviceModelForWebrtc(serialNumber, cloudDeviceSupport);
        assertEquals(expectedDeviceModel,actualDeviceModel);
    }

    @Test
    @DisplayName("替换设备型号config")
    public void testDeviceModelForWebrtc() {
        when(userService.queryUserById(any())).thenReturn(new User());
        when(userRoleService.getUserRoleByUserId(any(), any())).thenReturn(this.queryUserRoleList());
        when(deviceDAO.queryDeviceBynSerialNumbers(any())).thenReturn(this.queryUserDeviceList());
        when(locationInfoService.queryLocationDOs(any())).thenReturn(Maps.newHashMap());
        when(userService.queryUserMap(any())).thenReturn(Maps.newHashMap());
        when(deviceStatusService.queryDeviceStatus(any())).thenReturn(Maps.newHashMap());
        when(stateMachineService.batchGetDeviceState(any())).thenReturn(Maps.newHashMap());
        when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(new DeviceManualDO());
        when(deviceOTAService.queryDeviceOtaBySerialNumber((List<String>) any())).thenReturn(Maps.newHashMap());
        when(paasVipService.queryLastDeviceVip(any(), any())).thenReturn(new DeviceVipLog().setTenantId("").setSerialNumber("").setProductId("1").setVipLevel(PaasVipLevel.NO_VIP.getCode()).setTime(MIN_TIME).setEndTime(MAX_TIME));
        when(deviceConfigService.queryDeviceAudio(any(), any())).thenReturn(null);
        when(deviceFloodLightService.getFloodLightMsgMap(any())).thenReturn(Maps.newHashMap());


        when(deviceModelConfigService.queryDeviceModelConfig(any())).thenReturn(null);
        when(vipService.queryLastDeviceVip(any(), any())).thenReturn(new DeviceVipLog().setVipLevel(1));

        List<DeviceDO> actualResult = deviceInfoService.listDevicesByUserId(any());

        List<DeviceDO> expectResult = this.expectList(actualResult);
        assertEquals(expectResult, actualResult);
    }

    private List<UserRoleDO> queryUserRoleList() {
        String seralNumber = "seralNumber";
        List<UserRoleDO> userRoleDOList = Lists.newArrayList();
        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setUserId(1);
        userRoleDO.setRoleId("1");
        userRoleDO.setSerialNumber(seralNumber);
        userRoleDOList.add(userRoleDO);
        return userRoleDOList;
    }

    private List<DeviceDO> queryUserDeviceList() {
        String modelNo = "CG1";
        List<DeviceDO> deviceDOList = Lists.newArrayList();
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setSerialNumber("seralNumber");
        deviceDO.setModelNo(modelNo);
        CloudDeviceSupport support = new CloudDeviceSupport();
        support.setSupportManualFloodlightSwitch(true);
        support.setSupportManualFloodlightLuminance(true);
        deviceDO.setDeviceSupport(support);
        deviceDOList.add(deviceDO);
        return deviceDOList;
    }


    private List<DeviceDO> expectList(List<DeviceDO> sourceList) {
        List<DeviceDO> deviceDOList = Lists.newArrayList();

        for (DeviceDO deviceDO : sourceList) {
            DeviceDO device = new DeviceDO();
            BeanUtils.copyProperties(deviceDO, device);
            deviceDOList.add(device);
        }
        return deviceDOList;
    }


    @Test
    public void deviceInfoFillDeviceStatus() {
        String serialNumber = "serialNumber";
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setSerialNumber(serialNumber);
        deviceDO.setOnline(0);

        Map<String, DeviceStatusDO> mapDeviceStatusDO = Maps.newHashMap();
        DeviceStatusDO deviceStatusDO = new DeviceStatusDO();
        deviceStatusDO.setChargingMode(1);
        mapDeviceStatusDO.put(serialNumber, deviceStatusDO);

        Map<String, DeviceStateDO> deviceStateDOMap = Maps.newHashMap();

        when(redisService.get(any())).thenReturn(null);

        DeviceDO expectedDeviceDO = new DeviceDO();
        expectedDeviceDO.setChargingMode(1);

        when(deviceStatusService.getDeviceOnlineInfo(any(), anyInt(), any(), any())).thenReturn(new DeviceOnlineInfo() {{
            setOnline(0);
            setAwake(0);
            setOfflineTime(PhosUtils.getUTCStamp());
        }});

        deviceInfoService.fillDeviceStatus(deviceDO, mapDeviceStatusDO, deviceStateDOMap, 1, new User().setId(1));

        assertEquals(expectedDeviceDO.getChargingMode(), deviceDO.getChargingMode());
    }


    @Test
    @DisplayName("计算设备dtim-未上传wifi信息")
    public void deviceComputeDtimByWifiWakeInfo_noWifiInfo() {
        DeviceRequestDO requestDO = new DeviceRequestDO();
        requestDO.setReason(new DeviceRequestDO.AwakeReason());
        String modelNo = "CG1";

        Double expectResult = 0D;
        Double actualResult = deviceInfoService.deviceComputeDtimByWifiWakeInfo(requestDO, modelNo);

        assertEquals(expectResult, actualResult);
    }


    @Test
    @DisplayName("计算设备dtim-wifi信息无异常")
    public void deviceComputeDtimByWifiWakeInfo_wifiInfo_empty() {
        DeviceRequestDO requestDO = new DeviceRequestDO();
        DeviceRequestDO.AwakeReason awakeReason = new DeviceRequestDO.AwakeReason();

        List<DeviceRequestDO.WifiWakeInfo> list = Lists.newArrayList();
        DeviceRequestDO.WifiWakeInfo wifiWakeInfo102 = new DeviceRequestDO.WifiWakeInfo();
        wifiWakeInfo102.setCode("102");
        wifiWakeInfo102.setCnt(0);
        list.add(wifiWakeInfo102);
        DeviceRequestDO.WifiWakeInfo wifiWakeInfo109 = new DeviceRequestDO.WifiWakeInfo();
        wifiWakeInfo109.setCode("109");
        wifiWakeInfo109.setCnt(0);
        list.add(wifiWakeInfo109);
        awakeReason.setWifiWakeInfo(list);

        requestDO.setReason(awakeReason);

        String modelNo = "CG1";
        Double expectResult = 0D;
        Double actualResult = deviceInfoService.deviceComputeDtimByWifiWakeInfo(requestDO, modelNo);

        assertEquals(expectResult, actualResult);
    }


    @Test
    @DisplayName("计算设备dtim-wifiInfo第一次上报")
    public void deviceComputeDtimByWifiWakeInfo_wifiInfo_first() {
        DeviceRequestDO requestDO = new DeviceRequestDO();
        DeviceRequestDO.AwakeReason awakeReason = new DeviceRequestDO.AwakeReason();

        List<DeviceRequestDO.WifiWakeInfo> list = Lists.newArrayList();
        DeviceRequestDO.WifiWakeInfo wifiWakeInfo102 = new DeviceRequestDO.WifiWakeInfo();
        wifiWakeInfo102.setCode("102");
        wifiWakeInfo102.setCnt(1);
        list.add(wifiWakeInfo102);
        DeviceRequestDO.WifiWakeInfo wifiWakeInfo109 = new DeviceRequestDO.WifiWakeInfo();
        wifiWakeInfo109.setCode("109");
        wifiWakeInfo109.setCnt(2);
        list.add(wifiWakeInfo109);
        awakeReason.setWifiWakeInfo(list);

        requestDO.setReason(awakeReason);

        String modelNo = "CG1";
        when(redisService.getHashFieldValue(any(), any())).thenReturn(null);
        doNothing().when(redisService).setHashFieldValue(any(), any(), any());
        Double expectResult = 1D;
        Double actualResult = deviceInfoService.deviceComputeDtimByWifiWakeInfo(requestDO, modelNo);

        assertEquals(expectResult, actualResult);
    }


    @Test
    @DisplayName("计算设备dtim-wifiInfo上报")
    public void deviceComputeDtimByWifiWakeInfo_wifiInfo() {
        DeviceRequestDO requestDO = new DeviceRequestDO();
        DeviceRequestDO.AwakeReason awakeReason = new DeviceRequestDO.AwakeReason();

        List<DeviceRequestDO.WifiWakeInfo> list = Lists.newArrayList();
        DeviceRequestDO.WifiWakeInfo wifiWakeInfo102 = new DeviceRequestDO.WifiWakeInfo();
        wifiWakeInfo102.setCode("102");
        wifiWakeInfo102.setCnt(10);
        list.add(wifiWakeInfo102);
        DeviceRequestDO.WifiWakeInfo wifiWakeInfo109 = new DeviceRequestDO.WifiWakeInfo();
        wifiWakeInfo109.setCode("109");
        wifiWakeInfo109.setCnt(15);
        list.add(wifiWakeInfo109);
        awakeReason.setWifiWakeInfo(list);

        requestDO.setReason(awakeReason);

        String modelNo = "CG1";
        long storeTime = Instant.now().getEpochSecond();
        DeviceDTIMFatigue storeDeviceDTIMFatigue = DeviceDTIMFatigue.builder()
                .modelNo(modelNo)
                .timestamp(storeTime - 1)
                .value(1D)
                .build();
        Gson gson = new Gson();
        when(redisService.getHashFieldValue(any(), any())).thenReturn(gson.toJson(storeDeviceDTIMFatigue));
        doNothing().when(redisService).setHashFieldValue(any(), any(), any());
        Double expectResult = calculateDTIMFatigue(storeDeviceDTIMFatigue) + 10;
        Double actualResult = deviceInfoService.deviceComputeDtimByWifiWakeInfo(requestDO, modelNo);

        assertEquals(expectResult, actualResult);
    }

    @Test
    @DisplayName("设备唤醒-未发现设备")
    public void handleDeviceAwake_noDevice() throws MqttException, IdNotSetException {
        when(deviceService.getAllDeviceInfo(any())).thenReturn(null);
        DeviceRequestDO awakeDO = new DeviceRequestDO();
        awakeDO.setRequestId(1);
        Result actualResult = deviceInfoService.handleDeviceAwake(awakeDO);
        Result expectResult = Result.Error(ResultCollection.DEVICE_NO_ACCESS);

        assertEquals(actualResult, expectResult);
    }


    @Test
    public void test_handleEventReport() {
        DeviceReportEventDO input = new DeviceReportEventDO();
        input.setId(0);
        input.setName("eventReport");
        input.setSerialNumber("dkfjskfsdlfjsldk");
        DeviceReportEventDO.ReportEventRequestValue value = input.new ReportEventRequestValue();
        value.setEvent(EReportEvent.POWER_STATISTICS.getEventId());
        input.setValue(value);
        deviceInfoService.handleEventReport(input);

        value = input.new ReportEventRequestValue();
        value.setEvent(EReportEvent.ROTATE_CALIBRATION.getEventId());
        input.setValue(value);
        deviceInfoService.handleEventReport(input);

        value = input.new ReportEventRequestValue();
        value.setEvent(EReportEvent.LOW_BATTERY.getEventId());
        input.setValue(value);
        deviceInfoService.handleEventReport(input);

        DeviceInfoService.reportEventSourceThreadLocal.set(EReportEventSource.KISS);
        DeviceInfoService.reportEventSourceIpThreadLocal.set("127.0.0.1");
        value.setEvent(EReportEvent.POWER_STATISTICS.getEventId());
        input.setValue(value);
        deviceInfoService.handleEventReport(input);
        verify(kissService, Mockito.atLeast(1)).sendReportEventResponse(Mockito.any(), Mockito.any(), Mockito.any());

        value = input.new ReportEventRequestValue();
        value.setEvent(EReportEvent.LOW_BATTERY.getEventId());
        input.setValue(value);
        deviceInfoService.handleEventReport(input);
        verify(kissService, Mockito.atLeast(1)).sendReportEventResponse(Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void test_listDevicesByUserId() {
        Integer userId = 1230321;
        String sn = "sn1230321";
        DeviceDO device = DeviceDO.builder()
                .serialNumber(sn)
                .build();

        when(userService.queryUserById(userId)).thenReturn(null);
        try {
            List<DeviceDO> list = deviceInfoService.listDevicesByUserId(userId, Arrays.asList(), 0);
            Assert.assertTrue(false);
        } catch (Throwable e) {
        }
        User user = new User();
        user.setId(userId);
        when(userService.queryUserById(userId)).thenReturn(user);
        {
            when(userRoleService.getUserRoleByUserId(userId)).thenReturn(Arrays.asList());
            List<DeviceDO> list = deviceInfoService.listDevicesByUserId(userId, Arrays.asList(), 0);
            Assert.assertEquals(0, list.size());
        }
        UserRoleDO userRole = new UserRoleDO();
        userRole.setAdminId(userId);
        userRole.setUserId(userId);
        userRole.setSerialNumber(sn);
        when(userRoleService.getUserRoleByUserId(userId)).thenReturn(Arrays.asList(userRole));
        {
            when(deviceDAO.queryDeviceBynSerialNumbers(any())).thenReturn(Arrays.asList());
            List<DeviceDO> list = deviceInfoService.listDevicesByUserId(userId, Arrays.asList(), 0);
            Assert.assertEquals(0, list.size());
        }

        {
            when(userRoleService.getUserRoleByUserId(userId, 0)).thenReturn(Arrays.asList(userRole));
            DeviceDO deviceDO = new DeviceDO();
            CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
            cloudDeviceSupport.setSupportManualFloodlightSwitch(true);
            cloudDeviceSupport.setSupportPlanFloodlightLuminance(true);
            deviceDO.setDeviceSupport(cloudDeviceSupport);
            List<DeviceDO> list = deviceInfoService.listDevicesByUserId(userId, Arrays.asList(deviceDO), 0);
        }
//        when(deviceDAO.queryDeviceBynSerialNumbers(any())).thenReturn(Arrays.asList(device));

    }

    @Test
    public void test_setDeviceSupportPirSliceReport() {
        doNothing().when(redisService).set(any(), any());
        deviceInfoService.setDeviceSupportPirSliceReport("sn", 1);
        deviceInfoService.setDeviceSupportPirSliceReport("sn", null);
    }

    @Test
    public void test_getDeviceSupportPirSliceReport() {
        {
            CloudDeviceSupport tmpCloudDeviceSupport = deviceInfoService.getDeviceSupport("sn123");
            tmpCloudDeviceSupport.setSupportPirSliceReport(1);
            when(deviceSupportService.queryDeviceSupportBySn(any())).thenReturn(tmpCloudDeviceSupport);
            CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport("sn123");
            Assert.assertEquals(new Integer(1), cloudDeviceSupport.getSupportPirSliceReport());
        }
        {
            CloudDeviceSupport tmpCloudDeviceSupport = deviceInfoService.getDeviceSupport("sn123");
            tmpCloudDeviceSupport.setSupportPirSliceReport(0);
            CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport("sn123");
            Assert.assertEquals(new Integer(0), cloudDeviceSupport.getSupportPirSliceReport());
        }
        {
            when(deviceSupportService.queryDeviceSupportBySn(any())).thenReturn(null);
            CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport("sn123");
//            Assert.assertNull(deviceSupport);
            Assert.assertNotNull(cloudDeviceSupport);
        }

    }

    //    @Test
    public void test_gson_fastjson() {
        Gson gson = new Gson();
        String jsonStr = "{\"deviceSupportResolution\":\"1080P,360P\",\"deviceSupportAlarm\":1,\"deviceSupportMirrorFlip\":true,\"supportWebrtc\":1,\"supportRecLamp\":1,\"supportVoiceVolume\":1,\"supportAlarmVolume\":1,\"supportLiveAudioToggle\":1,\"supportRecordingAudioToggle\":1,\"supportLiveSpeakerVolume\":1,\"supportAlarmWhenRemoveToggle\":0,\"postMotionDetectResult\":0,\"killKeepAlive\":1,\"supportCryDetect\":0,\"deviceDormancySupport\":1,\"p2pConnMgtStrategy\":1,\"supportAlexaWebrtc\":1,\"supportChangeCodec\":0,\"supportPirCooldown\":1,\"resetVolSupport\":0,\"streamProtocol\":\"webrtc\",\"canStandby\":0,\"keepAliveProtocol\":\"tcp\",\"audioCodectype\":\"aac\",\"devicePersonDetect\":0,\"supportDeviceCall\":1,\"supportChargeAutoPowerOn\":0,\"deviceSupportLanguage\":\"cn,en,ja,de,ru,fr,it,es,pt\",\"doorBellRingKey\":0,\"supportDoorBellRingKey\":0,\"supportPirSliceReport\":1,\"batteryCode\":\"\",\"supportMechanicalDingDong\":0}";
        CloudDeviceSupport cloudDeviceSupport1 = JSON.parseObject(jsonStr, CloudDeviceSupport.class); // 不支持builder
        CloudDeviceSupport cloudDeviceSupport2 = gson.fromJson(jsonStr, CloudDeviceSupport.class);
        String str1 = gson.toJson(cloudDeviceSupport1);
        String str2 = gson.toJson(cloudDeviceSupport2);
        Assert.assertEquals(str1, str2);
    }

    @Test
    public void test_pushAiList() {
        {
            List<String> list = pushAiList((VipAiAnalyzeType) null, null, null);
            Assert.assertEquals(Arrays.asList(), list);
        }
        {
            List<String> list = pushAiList(VipAiAnalyzeType.NONE, null, null);
            Assert.assertEquals(Arrays.asList(), list);
        }
        {
            List<String> list = pushAiList(VipAiAnalyzeType.NORMAL_WITH_BIRD, null, null);
            Assert.assertEquals(Arrays.asList(), list);
        }
        {
            List<String> list = pushAiList(VipAiAnalyzeType.NORMAL_WITH_BIRD, null, null);
            Assert.assertEquals(Arrays.asList(), list);
        }
        when(deviceAiSettingsService.queryEnableEventObjects(any(), anyString())).thenAnswer(it -> new LinkedHashSet<>(Arrays.asList(AiObjectEnum.BIRD, AiObjectEnum.PERSON)));
        when(deviceManualService.getModelNoBySerialNumber(anyString())).thenReturn("modelNo");
        User user = new User();
        user.setId(123);
        user.setTenantId("guard");
        user.setLanguage("zh");
        when(userService.queryUserById(any())).thenReturn(user);
//        UserRoleDO userRole = new UserRoleDO();
//        userRole.setUserId(123);
//        userRole.setAdminId(123);
//        userRole.setSerialNumber(OpenApiUtil.shortUUID());
        String sn = OpenApiUtil.shortUUID();
        Integer userId = 123;
        /*** 1.设备支持鸟类 */
        when(deviceModelEventService.queryRowDeviceModelEvent(any())).thenAnswer(it -> new LinkedHashSet<>(Arrays.asList("person", "pet", "vehicle", "package", "bird")));
        {   /*** 1.1.用户无鸟类叠加包 */
            when(additionalUserTierService.getActiveAdditionalUserTierInfo(any(), anyString(), anyString())).thenReturn(Arrays.asList());
            List<String> list = pushAiList(VipAiAnalyzeType.NORMAL, sn, userId);
            Assert.assertEquals(Arrays.asList("person"), list);
        }
        {   /*** 1.2.用户有鸟类叠加包 */
            when(additionalUserTierService.getActiveAdditionalUserTierInfo(any(), anyString(), anyString())).thenReturn(Arrays.asList(new AdditionalUserTierInfo().setType(1)));
            List<String> list = pushAiList(VipAiAnalyzeType.NORMAL_WITH_BIRD, sn, userId);
            Assert.assertEquals(new HashSet<>(Arrays.asList("person", "bird")), new HashSet<>(list));
        }
        /*** 2.设备不支持鸟类 */
        when(deviceModelEventService.queryRowDeviceModelEvent(any())).thenAnswer(it -> new LinkedHashSet<>(Arrays.asList("person", "pet", "vehicle", "package")));
        {   /*** 2.1.用户无鸟类叠加包 */
            when(additionalUserTierService.getActiveAdditionalUserTierInfo(any(), anyString(), anyString())).thenReturn(Arrays.asList());
            List<String> list = pushAiList(VipAiAnalyzeType.NORMAL, sn, userId);
            Assert.assertEquals(Arrays.asList("person"), list);
        }
        {   /*** 2.2.用户有鸟类叠加包 */
            when(additionalUserTierService.getActiveAdditionalUserTierInfo(any(), anyString(), anyString())).thenReturn(Arrays.asList(new AdditionalUserTierInfo().setType(1)));
            List<String> list = pushAiList(VipAiAnalyzeType.NORMAL_WITH_BIRD, sn, userId);
            Assert.assertEquals(new HashSet<>(Arrays.asList("person")), new HashSet<>(list));
        }
    }

    @Test
    public void test_isBirdVip() {
        Integer adminId = 384958;
        User admin = new User() {{
            setId(adminId);
            setTenantId("vicoo");
            setLanguage("zh");
        }};
        when(userService.queryUserById(adminId)).thenReturn(admin);
        {
            when(additionalUserTierService.getActiveAdditionalUserTierInfo(eq(admin.getId()), eq(admin.getTenantId()), eq(admin.getLanguage())))
                    .thenThrow(new RuntimeException());
            Assert.assertEquals(false, deviceInfoService.isBirdVip(adminId));
        }
        {
            when(additionalUserTierService.getActiveAdditionalUserTierInfo(eq(admin.getId()), eq(admin.getTenantId()), eq(admin.getLanguage())))
                    .thenReturn(Arrays.asList());
            Assert.assertEquals(false, deviceInfoService.isBirdVip(adminId));
        }
        {
            when(additionalUserTierService.getActiveAdditionalUserTierInfo(eq(admin.getId()), eq(admin.getTenantId()), eq(admin.getLanguage())))
                    .thenReturn(Arrays.asList(new AdditionalUserTierInfo().setType(1)));
            Assert.assertEquals(true, deviceInfoService.isBirdVip(adminId));
        }
    }

    @Test
    public void test_updateHardwareStatus() throws Exception {
        when(deviceStatusService.queryDeviceStatusBySerialNumber(any())).thenReturn(DeviceStatusDO.builder().linkedPlatforms("alexa").codec("h265").build());


        deviceInfoService.updateHardwareStatus(DeviceDO.builder().serialNumber("sn_01").codec("h264").build());

        when(deviceStatusService.queryDeviceStatusBySerialNumber(any())).thenReturn(DeviceStatusDO.builder().codec("h265").build());
        deviceInfoService.updateHardwareStatus(DeviceDO.builder().serialNumber("sn_01").codec("h264").build());
    }

    @Test
    public void test_getVipTierType() {
        Assert.assertEquals(VipAiAnalyzeType.NONE, VipAiAnalyzeType.getVipTierType(false, false));
        Assert.assertEquals(VipAiAnalyzeType.NONE, VipAiAnalyzeType.getVipTierType(false, true));
        Assert.assertEquals(VipAiAnalyzeType.NORMAL, VipAiAnalyzeType.getVipTierType(true, false));
        Assert.assertEquals(VipAiAnalyzeType.NORMAL_WITH_BIRD, VipAiAnalyzeType.getVipTierType(true, true));
    }

    @Test
    public void test_initDeviceSupport() {
        when(userRoleService.queryAllAdminUserRoleIterator(anyString(), anyInt())).thenAnswer(it -> {
            final UserRoleDO userRoleDO = new UserRoleDO();
            userRoleDO.setSerialNumber("sn0322");
            return Arrays.asList(userRoleDO).iterator();
        });
        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        when(deviceSupportService.queryDeviceSupportBySn(any())).thenReturn(cloudDeviceSupport);
        CloudDeviceSupport[] results = new CloudDeviceSupport[1];
        when(deviceSupportService.insertDeviceSupport(eq("sn0322"), any())).thenAnswer(it -> {
            results[0] = it.getArgument(1);
            return 1;
        });
        {
            deviceInfoService.initDeviceSupport();

            Assert.assertNull(results[0].getSupportLocalVideoLookBack());
            Assert.assertNull(results[0].getLocalVideoStorageType());
            Assert.assertNull(results[0].getSupportSdCardFormat());
            Assert.assertNull(results[0].getSupportNightVisionSwitch());
            Assert.assertNull(results[0].getSupportWhiteLight());
            Assert.assertNull(results[0].getSupportAlarmFlashLight());
            Assert.assertNull(results[0].getSupportStarlightSensor());
        }
        {
            cloudDeviceSupport.setSupportLocalVideoLookBack(false);
            cloudDeviceSupport.setLocalVideoStorageType(0);
            cloudDeviceSupport.setSupportSdCardFormat(false);
            cloudDeviceSupport.setSupportNightVisionSwitch(false);
            cloudDeviceSupport.setSupportWhiteLight(false);
            cloudDeviceSupport.setSupportAlarmFlashLight(false);
            cloudDeviceSupport.setSupportStarlightSensor(false);
            cloudDeviceSupport.setSupportUnlimitedWebsocket(false);

            deviceInfoService.initDeviceSupport();

            Assert.assertNull(results[0].getSupportLocalVideoLookBack());
            Assert.assertNull(results[0].getLocalVideoStorageType());
            Assert.assertNull(results[0].getSupportSdCardFormat());
            Assert.assertNull(results[0].getSupportNightVisionSwitch());
            Assert.assertNull(results[0].getSupportWhiteLight());
            Assert.assertNull(results[0].getSupportAlarmFlashLight());
            Assert.assertNull(results[0].getSupportStarlightSensor());
        }
        {
            cloudDeviceSupport.setSupportLocalVideoLookBack(true);
            cloudDeviceSupport.setLocalVideoStorageType(1);
            cloudDeviceSupport.setSupportSdCardFormat(true);
            cloudDeviceSupport.setSupportNightVisionSwitch(true);
            cloudDeviceSupport.setSupportWhiteLight(true);
            cloudDeviceSupport.setSupportAlarmFlashLight(true);
            cloudDeviceSupport.setSupportStarlightSensor(true);
            cloudDeviceSupport.setSupportUnlimitedWebsocket(true);

            deviceInfoService.initDeviceSupport();

            Assert.assertEquals(true, results[0].getSupportLocalVideoLookBack());
            Assert.assertEquals(new Integer(1), results[0].getLocalVideoStorageType());
            Assert.assertEquals(true, results[0].getSupportSdCardFormat());
            Assert.assertEquals(true, results[0].getSupportNightVisionSwitch());
            Assert.assertEquals(true, results[0].getSupportWhiteLight());
            Assert.assertEquals(true, results[0].getSupportAlarmFlashLight());
            Assert.assertEquals(true, results[0].getSupportStarlightSensor());
        }

    }

    public List<String> pushAiList(VipAiAnalyzeType vipTierType, String serialNumber, Integer userId) {
        if (vipTierType == null) return Collections.emptyList();
        return deviceInfoService.pushAiList(serialNumber, userId, vipTierType.getSupportEventObjectNames());
    }

    @Test
    public void test_queryEnabledObjectCategories() {
        final String sn = OpenApiUtil.shortUUID();
        final String modelNo = OpenApiUtil.shortUUID();
        final int userId = RandomUtils.nextInt(0, 10000_0000);

        when(deviceAiSettingsService.queryEnableEventObjects(userId, sn)).thenReturn(VipAiAnalyzeType.NORMAL_WITH_BIRD.getSupportEventObjects());
        when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(modelNo);
        when(deviceModelEventService.queryRowDeviceModelEvent(modelNo)).thenReturn(VipAiAnalyzeType.NORMAL_WITH_BIRD.getSupportEventObjectNames());

        {
            when(vipService.isVipDevice(userId, sn)).thenReturn(true);
            when(vipService.isBirdVipDevice(userId, sn)).thenReturn(true);
            final List<String> categories = deviceInfoService.queryEnabledObjectCategories(sn, userId);
            Assert.assertEquals(VipAiAnalyzeType.NORMAL_WITH_BIRD.getSupportEventObjectNames(), new HashSet<>(categories));
        }
        {
            when(vipService.isVipDevice(userId, sn)).thenReturn(true);
            when(vipService.isBirdVipDevice(userId, sn)).thenReturn(false);
            final List<String> categories = deviceInfoService.queryEnabledObjectCategories(sn, userId);
            Assert.assertEquals(VipAiAnalyzeType.NORMAL.getSupportEventObjectNames(), new HashSet<>(categories));
        }
        {
            when(vipService.isVipDevice(userId, sn)).thenReturn(false);
            when(vipService.isBirdVipDevice(userId, sn)).thenReturn(true);
            final List<String> categories = deviceInfoService.queryEnabledObjectCategories(sn, userId);
            Assert.assertEquals(VipAiAnalyzeType.NONE.getSupportEventObjectNames(), new HashSet<>(categories));
        }
        {
            when(vipService.isVipDevice(userId, sn)).thenReturn(false);
            when(vipService.isBirdVipDevice(userId, sn)).thenReturn(false);
            final List<String> categories = deviceInfoService.queryEnabledObjectCategories(sn, userId);
            Assert.assertEquals(VipAiAnalyzeType.NONE.getSupportEventObjectNames(), new HashSet<>(categories));
        }
    }


    @Test
    public void test_hasServiceTypeDevice() {
        Integer userId = 1;
        Boolean actualResult = false;
        {
            //无绑定设备
            when(userRoleService.getUserRoleByUserId(any(), any())).thenReturn(Lists.newArrayList());
            actualResult = deviceInfoService.hasServiceTypeDevice(userId, TierServiceTypeEnums.TIER_4G);
            Assert.assertFalse(actualResult);
        }
        {
            // 绑定设备中有4G设备
            List<UserRoleDO> userRoleDOList = Arrays.asList(
                    UserRoleDO.builder().serialNumber("sn1").build()
            );
            when(userRoleService.getUserRoleByUserId(any(), any())).thenReturn(userRoleDOList);
            when(device4GService.queryDevice4GSimDO(any())).thenReturn(new Device4GSimDO());
            actualResult = deviceInfoService.hasServiceTypeDevice(userId, TierServiceTypeEnums.TIER_4G);
            Assert.assertTrue(actualResult);
        }
        {
            // 绑定设备中有4G设备
            List<UserRoleDO> userRoleDOList = Arrays.asList(
                    UserRoleDO.builder().serialNumber("sn1").build()
            );
            when(userRoleService.getUserRoleByUserId(any(), any())).thenReturn(userRoleDOList);
            when(device4GService.queryDevice4GSimDO(any())).thenReturn(null);
            actualResult = deviceInfoService.hasServiceTypeDevice(userId, TierServiceTypeEnums.TIER_CLOID_SERVICE);
            Assert.assertTrue(actualResult);
        }
    }

    @Test
    public void testCheckIfDeviceUse4G_WithExistingDevice() {
        String serialNumber = "1234567890";
        Device4GSimDO mockDevice = new Device4GSimDO(); // 根据实际情况可能需要设置更多属性

        when(device4GService.queryDevice4GSimDO(serialNumber)).thenReturn(mockDevice);

        Boolean result = deviceInfoService.checkIfDeviceUse4G(serialNumber);

        assertTrue("The device should use 4G", result);
        verify(device4GService).queryDevice4GSimDO(serialNumber);
    }

    @Test
    public void testCheckIfDeviceUse4G_WithNonExistingDevice() {
        String serialNumber = "0987654321";

        when(device4GService.queryDevice4GSimDO(serialNumber)).thenReturn(null);

        Boolean result = deviceInfoService.checkIfDeviceUse4G(serialNumber);

        assertFalse("The device should not use 4G", result);
        verify(device4GService).queryDevice4GSimDO(serialNumber);
    }

    @Test
    public void testCheckIf4GDeviceHasOfficialSimCard_WithOfficialSim() {
        String serialNumber = "123456";
        Device4GSimDO mockDevice = new Device4GSimDO();
        mockDevice.setSimThirdParty(0);

        when(device4GService.queryDevice4GSimDO(serialNumber)).thenReturn(mockDevice);

        assertTrue("The device should have an official SIM card",
                deviceInfoService.checkIf4GDeviceHasOfficialSimCard(serialNumber));
    }

    @Test
    public void testCheckIf4GDeviceHasOfficialSimCard_WithThirdPartySim() {
        String serialNumber = "123456";
        Device4GSimDO mockDevice = new Device4GSimDO();
        mockDevice.setSimThirdParty(1);

        when(device4GService.queryDevice4GSimDO(serialNumber)).thenReturn(mockDevice);

        assertFalse("The device should not have an official SIM card",
                deviceInfoService.checkIf4GDeviceHasOfficialSimCard(serialNumber));
    }

    @Test
    public void testCheckIf4GDeviceHasOfficialSimCard_WithNoDevice() {
        String serialNumber = "unknown";

        when(device4GService.queryDevice4GSimDO(serialNumber)).thenReturn(null);

        assertFalse("No device found, should return false",
                deviceInfoService.checkIf4GDeviceHasOfficialSimCard(serialNumber));
    }

    @Test
    public void testContainsOfficialSim_WithAdminHavingOfficialSim() {
        Integer adminId = 1;
        UserRoleDO userRole1 = new UserRoleDO();
        userRole1.setSerialNumber("123456");
        UserRoleDO userRole2 = new UserRoleDO();
        userRole2.setSerialNumber("654321");

        List<UserRoleDO> userRoleDOList = Arrays.asList(userRole1, userRole2);

        Device4GSimDO device1 = new Device4GSimDO();
        device1.setSimThirdParty(0); // 明确设置为官方SIM卡
        Device4GSimDO device2 = new Device4GSimDO();
        device2.setSimThirdParty(1); // 设置为非官方SIM卡

        when(userRoleService.getUserRoleByUserId(adminId, UserRoleEnums.ADMIN.getCode())).thenReturn(userRoleDOList);
        when(device4GService.queryDevice4GSimDO("123456")).thenReturn(device1);
        when(device4GService.queryDevice4GSimDO("654321")).thenReturn(device2);

        assertTrue("Should find an official SIM card",
                deviceInfoService.containsOfficialSim(adminId));
    }

    @Test
    public void testContainsOfficialSim_WithAdminHavingNoOfficialSim() {
        Integer adminId = 1;
        UserRoleDO userRole1 = new UserRoleDO();
        userRole1.setSerialNumber("123456");

        List<UserRoleDO> roles = Collections.singletonList(userRole1);

        Device4GSimDO device1 = new Device4GSimDO();
        device1.setSimThirdParty(1); // 设置为非官方SIM卡

        when(userRoleService.getUserRoleByUserId(adminId, UserRoleEnums.ADMIN.getCode())).thenReturn(roles);
        when(device4GService.queryDevice4GSimDO("123456")).thenReturn(device1);

        assertFalse("Should not find an official SIM card",
                deviceInfoService.containsOfficialSim(adminId));
    }

    @Test
    public void testContainsOfficialSim_WithNoAdminRoles() {
        Integer adminId = 2;

        when(userRoleService.getUserRoleByUserId(adminId, UserRoleEnums.ADMIN.getCode())).thenReturn(Collections.emptyList());

        assertFalse("No admin roles found, should return false",
                deviceInfoService.containsOfficialSim(adminId));
    }

    @Test
    public void test_setDeviceSimInfoIfHasIccid() {
        DeviceDO deviceDO = DeviceDO.builder().serialNumber("sn").build();
        {
            when(device4GService.queryDevice4GSimDO(any())).thenReturn(null);
            deviceInfoService.setDeviceSimInfoIfHasIccid(deviceDO);
            Assert.assertFalse(StringUtils.hasLength(deviceDO.getIccid()));
        }
        {
            when(device4GService.queryDevice4GSimDO(any())).thenReturn(Device4GSimDO.builder().iccid("iccid").simStatus(0).simThirdParty(0).inBlackList(false).build());
            deviceInfoService.setDeviceSimInfoIfHasIccid(deviceDO);
            Assert.assertEquals(deviceDO.getIccid(), "iccid");
        }
    }

    @Test
    public void test_fillDeviceStatusReason() {
        User user = new User().setTenantId("kiwibit");
        {
            when(redisService.getFromLocalCache(REDIS_KEY_FILL_DEVICE_STATUS_REASON_TENANT_IDS)).thenReturn(Optional.empty());
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            onlineInfo.setData("{\"reason\":1}");
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
        }
        {
            when(redisService.getFromLocalCache(REDIS_KEY_FILL_DEVICE_STATUS_REASON_TENANT_IDS)).thenReturn(Optional.of("vicoo"));
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            onlineInfo.setData("{\"reason\":1}");
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
        }
        when(redisService.getFromLocalCache(REDIS_KEY_FILL_DEVICE_STATUS_REASON_TENANT_IDS)).thenReturn(Optional.of(user.getTenantId()));
        {
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            DeviceDO deviceDO = new DeviceDO();
            deviceInfoService.fillDeviceStatusReason(onlineInfo, null, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
            deviceInfoService.fillDeviceStatusReason(null, deviceDO, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
            deviceInfoService.fillDeviceStatusReason(null, null, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
        }
        {
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(1);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
        }
        {
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            onlineInfo.setData("");
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
        }
        {
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            onlineInfo.setData("{}");
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
        }
        {
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            onlineInfo.setData("{\"reason\":\"str\"}");
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
        }
        {
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            onlineInfo.setData("{\"reason\":1}");
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertEquals(Integer.valueOf(11), deviceDO.getDeviceStatus());
        }
        {
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            onlineInfo.setData("{\"reason\":2}");
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertEquals(Integer.valueOf(12), deviceDO.getDeviceStatus());
        }
        {
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            onlineInfo.setData("{\"reason\":4}");
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertEquals(Integer.valueOf(14), deviceDO.getDeviceStatus());
        }
        {
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo();
            onlineInfo.setData("{\"reason\":99}");
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
        }
        {
            DeviceOnlineInfo onlineInfo = new DeviceOnlineInfo() {
                @Override
                public Integer getReasonFromData() {
                    throw new RuntimeException("mock RuntimeException");
                }
            };
            onlineInfo.setData("{\"reason\":4}");
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertNull(deviceDO.getDeviceStatus());
        }
        {
            DeviceOnlineInfo onlineInfo = JSON.parseObject("{\n" +
                            "\t\t\"data\":\"{\\\"reason\\\":\\\"2\\\"}\",\n" +
                            "\t\t\"online\":0,\n" +
                            "\t\t\"offlineTime\":1737885579,\n" +
                            "\t\t\"awake\":0\n" +
                            "\t}"
                    , DeviceOnlineInfo.class);
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setOnline(0);
            deviceDO.setDeviceStatus(0);
            deviceInfoService.fillDeviceStatusReason(onlineInfo, deviceDO, user);
            Assert.assertEquals(Integer.valueOf(12), deviceDO.getDeviceStatus());
        }
    }

    @Test
    public void test_syncTimeZoneOffsetByProto() {
        String sn = OpenApiUtil.shortUUID();
        {
            PbSyncTimeZoneOffset input = PbSyncTimeZoneOffset.newBuilder()
                    .setId(1).setTime(PhosUtils.getUTCStamp()).setName("syncTimeZoneOffset")
                    .setValue(PbSyncTimeZoneOffset.PbValue.newBuilder()
                            .setTriggerDeviceMsgId(OpenApiUtil.shortUUID())
                            .build())
                    .build();
            when(deviceSettingService.getDeviceSettingsBySerialNumber(sn)).thenReturn(null);
            PbSyncTimeZoneOffsetResponse resp = deviceInfoService.syncTimeZoneOffsetByProto(sn, input);
            Assert.assertEquals(0, resp.getResult());
        }
        {
            PbSyncTimeZoneOffset input = PbSyncTimeZoneOffset.newBuilder()
                    .setId(1).setTime(PhosUtils.getUTCStamp()).setName("syncTimeZoneOffset")
                    .setValue(PbSyncTimeZoneOffset.PbValue.newBuilder()
                            .build())
                    .build();
            when(deviceSettingService.getDeviceSettingsBySerialNumber(sn)).thenReturn(new DeviceSettingsDO().setTimeZone("Europe/London"));
            PbSyncTimeZoneOffsetResponse resp = deviceInfoService.syncTimeZoneOffsetByProto(sn, input);
            Assert.assertEquals(0, resp.getResult());
        }
    }
}