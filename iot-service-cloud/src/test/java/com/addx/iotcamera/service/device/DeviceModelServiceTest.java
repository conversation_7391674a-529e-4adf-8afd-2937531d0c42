package com.addx.iotcamera.service.device;

import com.addx.iotcamera.dao.device.IDeviceManualDAO;
import com.addx.iotcamera.dao.factory.DeviceModelDao;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceModelServiceTest {
    @InjectMocks
    @Spy
    private DeviceModelService deviceModelService;
    @Mock
    private DeviceModelDao deviceModelDao;
    @Mock
    private IDeviceManualDAO iDeviceManualDAO;

//    private TestHelper testHelper;
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private RedisService redisService;

    @Before
    public void before() {
        when(applicationContext.getBean(DeviceModelService.class)).thenReturn(deviceModelService);
//        testHelper = TestHelper.getInstanceByLocal();
//        this.deviceModelDao = testHelper.getMapper(DeviceModelDao.class);
//        this.iDeviceManualDAO = testHelper.getMapper(IDeviceManualDAO.class);

        deviceModelService.setDeviceModelDao(this.deviceModelDao);
        deviceModelService.setIDeviceManualDAO(this.iDeviceManualDAO);
        when(deviceModelDao.queryModelCategory(anyString())).thenReturn(1);
    }


    @Test
    @DisplayName("查询型号类别-摄像头")
    public void modelCategoryTest_camera(){
        String modelNo = "CG1";
        when(redisService.get("modelCategory::CG1")).thenReturn("1");
        Integer actualResult = deviceModelService.queryDeviceModelCategory(modelNo);
        Integer expectedResult = 1;
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("查询型号类别-摄像头-无缓存")
    public void modelCategoryTest_camera_noCache(){
        String modelNo = "CG1";
        when(redisService.get("modelCategory::CG1")).thenReturn(null);
        Integer actualResult = deviceModelService.queryDeviceModelCategory(modelNo);
        Integer expectedResult = 1;
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("查询型号类别-摄像头-错误缓存")
    public void modelCategoryTest_camera_errCache(){
        String modelNo = "CG1";
        when(redisService.get("modelCategory::CG1")).thenReturn("null");
        Integer actualResult = deviceModelService.queryDeviceModelCategory(modelNo);
        Integer expectedResult = 1;
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("查询型号类别-门铃")
    public void modelCategoryTest_doorbell(){
        String modelNo = "DB121A-US";
        when(redisService.get("modelCategory::DB121A-US")).thenReturn("2");
        Integer actualResult = deviceModelService.queryDeviceModelCategory(modelNo);
        Integer expectedResult = 2;
        Assert.assertEquals(expectedResult,actualResult);
    }

//    @Test
//    @DisplayName("查询型号安装引导")
//    public void modelDeviceModelInstallBootTest(){
//        String modelNo = "ML-1";
//        String actualResult = deviceModelService.queryDeviceModelInstallBoot(modelNo);
//        String expectedResult = "/hc/en-us/articles/4406080247065";
//        Assert.assertEquals(expectedResult,actualResult);
//    }

    // @Test
    // @DisplayName("查询型号类别-根据序列号")
    // public void modelDeviceModeCategoryBySerialNumberTest(){
    //     String serialNumber = "8a935a5333c9f59b46f55d2bfdaa9b21";
    //     Integer actualResult = deviceModelService.queryDeviceModelCategoryBySerialNumber(serialNumber);
    //     Integer expectedResult = 1;
    //     Assert.assertEquals(expectedResult,actualResult);
    // }

    @Test
    @DisplayName("获取枚举值-摄像机")
    public void getDeviceModelCategoryEnums_camera(){
        Integer categoryId = 1;
        DeviceModelCategoryEnums actualEnums = DeviceModelCategoryEnums.queryByCode(categoryId);
        DeviceModelCategoryEnums expectedEnums = DeviceModelCategoryEnums.CAMERA;
        Assert.assertEquals(actualEnums.getCode(),expectedEnums.getCode());
    }

    @Test
    @DisplayName("获取枚举值-门铃")
    public void getDeviceModelCategoryEnums_doorbell(){
        Integer categoryId = 2;
        DeviceModelCategoryEnums actualEnums = DeviceModelCategoryEnums.queryByCode(categoryId);
        DeviceModelCategoryEnums expectedEnums = DeviceModelCategoryEnums.DOORBELL;
        Assert.assertEquals(actualEnums.getCode(),expectedEnums.getCode());
    }

    @Test
    @DisplayName("获取枚举值-室内机")
    public void getDeviceModelCategoryEnums_sub(){
        Integer categoryId = 3;
        DeviceModelCategoryEnums actualEnums = DeviceModelCategoryEnums.queryByCode(categoryId);
        DeviceModelCategoryEnums expectedEnums = DeviceModelCategoryEnums.INDOOR_UNIT;
        Assert.assertEquals(actualEnums.getCode(),expectedEnums.getCode());
    }
}
