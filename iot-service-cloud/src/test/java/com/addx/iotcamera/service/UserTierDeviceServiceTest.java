package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.UserTierDeviceRequest;
import com.addx.iotcamera.bean.app.additional_tier.AdditionalTierInfo;
import com.addx.iotcamera.bean.app.additional_tier.AdditionalUserTierInfo;
import com.addx.iotcamera.bean.app.payment.ApplePaymentRequest;
import com.addx.iotcamera.bean.app.vip.UserTierDeviceInfo;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.db.UserTierDeviceDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.Device4GSimDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.openapi.DeviceVipLog;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.dao.UserTierDeviceDAO;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.pay.IOrderProductDAO;
import com.addx.iotcamera.enums.PaymentTypeEnums;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.vip.TierActiveConditionService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
@ExtendWith(MockitoExtension.class)
public class UserTierDeviceServiceTest {
    @InjectMocks
    @Spy
    private UserTierDeviceService userTierDeviceServiceUnderTest;
    @Mock
    private TierService mockTierService;
    @Mock
    private AdditionalUserTierService mockAdditionalUserTierService;
    @Mock
    private VipService paasVipService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private TierActiveConditionService mockTierActiveConditionService;
    //    @Mock
//    private RedisService mockRedisService;
    @Mock
    private UserTierDeviceDAO mockUserTierDeviceDAO;
    @Mock
    private CenterNotifyConfig mockCenterNotifyConfig;
    @Mock
    private UserRoleService userRoleService;

    @Mock
    private UserTierDeviceDAO userTierDeviceDAO;

    @Mock
    private AdditionalUserTierService additionalUserTierService;

    @Mock
    private VipService vipService;

    @Mock
    private TierService tierService;

    @Mock
    private UserService userService;

    @Mock
    private UserVipService userVipService;

    @Mock
    private IOrderDAO iOrderDAO;

    @Mock
    private RedisService redisService;
    @Mock
    private Device4GService device4GService;

    @Mock
    private MqSender mqSender;

    @Mock
    private DeviceSettingService deviceSettingService;

    @Mock
    private IOrderProductDAO iOrderProductDAO;

    @Before
    public void init() {
        when(mockCenterNotifyConfig.getMessage()).thenReturn(Collections.singletonMap("tenantId", new HashMap<String, Map<String, String>>() {{
            put("language", Collections.singletonMap("freeMessage", "freeTier"));
            put("en", Collections.singletonMap("freeMessage", "freeTier"));
        }}));

        when(mockTierService.queryTierById(any())).thenReturn(new Tier());
    }

//    @Test
//    public void testGetManageUserTierDeviceInfoList() {
//        // Setup
//        final AppRequestBase request = new AppRequestBase();
//        request.setApp(new AppInfo("appName", 0, "channelId", "appBuild", "bundle", "appType", "tenantId", "apiVersion","", ""));
//        request.setRequestId("requestId");
//        request.setLanguage("language");
//        request.setCountryNo("countryNo");
//
//        when(mockUserVipService.queryUserVipInfo(any(), any(), any())).thenReturn(new UserVipTier());
//        userTierDeviceServiceUnderTest.getManageUserTierDeviceInfoList(request, 0);
//        // Configure TierService.queryTierList(...).
//        final TierInfo tierInfo = new TierInfo();
//        tierInfo.setId(0);
//        tierInfo.setName("name");
//        tierInfo.setStorage("storage");
//        tierInfo.setMaxDeviceNum(1);
//        tierInfo.setCurrency("currency");
//        tierInfo.setPrice("price");
//        tierInfo.setDiscount("discount");
//        tierInfo.setUnit("unit");
//        tierInfo.setSubhead("subhead");
//        tierInfo.setAvailable(false);
//        final TierInfo tierInfo2 = new TierInfo();
//        tierInfo2.setId(1);
//        tierInfo2.setName("name");
//        tierInfo2.setStorage("storage");
//        tierInfo2.setMaxDeviceNum(1);
//        tierInfo2.setCurrency("currency");
//        tierInfo2.setPrice("price");
//        tierInfo2.setDiscount("discount");
//        tierInfo2.setUnit("unit");
//        tierInfo2.setSubhead("subhead");
//        tierInfo2.setAvailable(false);
//        final List<TierInfo> tierInfos = Arrays.asList(tierInfo, tierInfo2);
//        when(mockTierService.queryTierList(any(), any())).thenReturn(tierInfos);
//
//        when(mockUserVipService.queryUserVipInfo(any(), any(), any())).thenReturn(new UserVipTier(){{
//            setTierIdList(Arrays.asList( 1, 2, 0));
//        }});
//        when(mockTierService.queryTierById(any())).thenReturn(new Tier());
//
//        final List<DeviceDO> deviceDOList = Arrays.asList(new DeviceDO(){{
//            setSerialNumber("serialNumber");
//            setUserSn("userSn");
//            setModelNo("modelNo");
//        }},new DeviceDO(){{
//            setSerialNumber("serialNumber1");
//            setUserSn("userSn1");
//            setModelNo("modelNo1");
//        }},new DeviceDO(){{
//            setSerialNumber("serialNumber2");
//            setUserSn("userSn2");
//            setModelNo("modelNo2");
//        }});
//        when(mockDeviceInfoService.listDevicesByUserId(0)).thenReturn(deviceDOList);
//
//        when(mockRedisService.get(eq("deviceTier#0_serialNumber"))).thenReturn("NULL");
//        when(mockRedisService.get(eq("deviceTier#0_serialNumber1"))).thenReturn("null_null");
//        when(mockRedisService.get(eq("deviceTier#0_serialNumber2"))).thenReturn("1_0");
//
//        List<UserTierDeviceInfo> result = userTierDeviceServiceUnderTest.getManageUserTierDeviceInfoList(request, 0);
//        assertTrue(CollectionUtils.isNotEmpty(result) && ObjectUtils.equals(result.get(0).getTierId(), 1) && ObjectUtils.equals(result.get(1).getTierId(), 2));
//    
//        when(mockTierActiveConditionService.getById(any(), eq(0), any())).thenReturn(null);
//        when(mockTierActiveConditionService.getById(any(), eq(1), any())).thenReturn(new TierActiveConditionDO());
//        when(mockTierActiveConditionService.getById(any(), eq(2), any())).thenReturn(new TierActiveConditionDO(){{
//            setSupportModelNo("serialNumber,serialNumber1,serialNumber2");
//        }});
//        result = userTierDeviceServiceUnderTest.getManageUserTierDeviceInfoList(request, 0);
//        assertTrue(CollectionUtils.isNotEmpty(result) && ObjectUtils.equals(result.get(0).getTierId(), 1) && ObjectUtils.equals(result.get(1).getTierId(), 2));
//    }

//    @Test
//    public void testGetCurrentTierDeviceInfoList() {
//        // Setup
//        final AppRequestBase request = new AppRequestBase();
//        request.setApp(new AppInfo("appName", 0, "channelId", "appBuild", "bundle", "appType", "tenantId", "apiVersion","", ""));
//        request.setRequestId("requestId");
//        request.setLanguage("language");
//        request.setCountryNo("countryNo");
//
//        when(mockUserVipService.queryUserVipInfo(any(), any(), any())).thenReturn(new UserVipTier());
//        userTierDeviceServiceUnderTest.getCurrentTierDeviceInfoList(request, 0);
//
//        when(mockUserVipService.queryUserVipInfo(any(), any(), any())).thenReturn(new UserVipTier(){{
//            setTierIdList(Arrays.asList(1, 2));
//        }});
//        userTierDeviceServiceUnderTest.getCurrentTierDeviceInfoList(request, 0);
//
//        // Configure TierService.queryTierList(...).
//        final TierInfo tierInfo = new TierInfo();
//        tierInfo.setId(0);
//        tierInfo.setName("name");
//        tierInfo.setStorage("storage");
//        tierInfo.setMaxDeviceNum(1);
//        tierInfo.setCurrency("currency");
//        tierInfo.setPrice("price");
//        tierInfo.setDiscount("discount");
//        tierInfo.setUnit("unit");
//        tierInfo.setSubhead("subhead");
//        tierInfo.setAvailable(false);
//        final TierInfo tierInfo2 = new TierInfo();
//        tierInfo2.setId(1);
//        tierInfo2.setName("name");
//        tierInfo2.setStorage("storage");
//        tierInfo2.setMaxDeviceNum(1);
//        tierInfo2.setCurrency("currency");
//        tierInfo2.setPrice("price");
//        tierInfo2.setDiscount("discount");
//        tierInfo2.setUnit("unit");
//        tierInfo2.setSubhead("subhead");
//        tierInfo2.setAvailable(false);
//        final List<TierInfo> tierInfos = Arrays.asList(tierInfo, tierInfo2);
//        when(mockTierService.queryTierList(any(), any())).thenReturn(tierInfos);
//
//        when(mockUserVipService.queryUserVipInfo(any(), any(), any())).thenReturn(new UserVipTier(){{
//            setTierIdList(Arrays.asList(1, 2));
//            setAdditionalTierInfo(Collections.singletonMap("activeList", Arrays.asList(new AdditionalUserTierInfo() {{
//                setTierUid("tierUid");
//            }}, new AdditionalUserTierInfo() {{
//                setTierUid("tierUid2");
//            }}, new AdditionalUserTierInfo() {{
//                setTierUid("tierUid3");
//            }})));
//        }});
//        when(mockTierService.queryTierById(any())).thenReturn(new Tier());
//
//        final List<DeviceDO> deviceDOList = Arrays.asList(new DeviceDO(){{
//            setSerialNumber("serialNumber");
//            setUserSn("userSn");
//            setModelNo("modelNo");
//        }},new DeviceDO(){{
//            setSerialNumber("serialNumber1");
//            setUserSn("userSn1");
//            setModelNo("modelNo1");
//        }},new DeviceDO(){{
//            setSerialNumber("serialNumber2");
//            setUserSn("userSn2");
//            setModelNo("modelNo2");
//        }});
//        when(mockDeviceInfoService.listDevicesByUserId(0)).thenReturn(deviceDOList);
//        when(userRoleService.getUserRoleByUserId(any(), any())).thenReturn(Arrays.asList(new UserRoleDO() {{
//            setSerialNumber("serialNumber");
//        }}, new UserRoleDO() {{
//            setSerialNumber("serialNumber1");
//        }}, new UserRoleDO() {{
//            setSerialNumber("serialNumber2");
//        }}));
//
//        when(mockRedisService.get(eq("deviceTier#0_serialNumber"))).thenReturn("NULL");
//        when(mockRedisService.get(eq("deviceTier#0_serialNumber1"))).thenReturn("null_null");
//        when(mockRedisService.get(eq("deviceTier#0_serialNumber2"))).thenReturn("1_0");
//
//        // Configure TierService.queryAdditionalTierList(...).
//        final AdditionalTierInfo additionalTierInfo1 = new AdditionalTierInfo();
//        additionalTierInfo1.setTierUid("tierUid1");
//        final AdditionalTierInfo additionalTierInfo2 = new AdditionalTierInfo();
//        additionalTierInfo2.setTierUid("tierUid2");
//        additionalTierInfo2.setSupportDeviceList(Arrays.asList(new DeviceDO(){{
//            setSerialNumber("serialNumber2");
//        }}));
//        final List<AdditionalTierInfo> additionalTierInfoList = Arrays.asList(additionalTierInfo1, additionalTierInfo2);
//        when(mockTierService.queryAdditionalTierList(any(), eq(0))).thenReturn(additionalTierInfoList);
//
//        // Run the test
//        final List<UserTierDeviceInfo> result = userTierDeviceServiceUnderTest.getCurrentTierDeviceInfoList(request, 0);
//
//        // Verify the results
//        assertEquals(result.size(), 2);
//        assertTrue(CollectionUtils.isNotEmpty(result.get(0).getActiveDeviceList()) && CollectionUtils.isNotEmpty(result.get(1).getActiveDeviceList()));
//    }

    @Test
    public void testUpdateUserTierDeviceInfo() {
        Integer userId = 1;

        // Setup
        final UserTierDeviceRequest userTierDeviceRequest = new UserTierDeviceRequest();
        userTierDeviceRequest.setList(Arrays.asList(
                //UserTierDeviceInfo.builder().tierId(-1).activeDeviceSnList(Arrays.asList("sn1","sn2")).build(),
                UserTierDeviceInfo.builder().tierId(1).activeDeviceSnList(Arrays.asList("sn3","sn4")).build()
        ));

        AppInfo appInfo = AppInfo.builder()
                .appName("appName")
                .appType("iOS")
                .tenantId(TENANTID_VICOO)
                .build();
        userTierDeviceRequest.setApp(appInfo);

        UserVipTier userVipTier = new UserVipTier();
        doNothing().when(userTierDeviceDAO).deleteByUserIdAndSerialNumber(any(),any(),any(),any(),any());
        doNothing().when(redisService).set(any(),any(),any());
        when(userTierDeviceDAO.queryByUserTierIdAndSerialNumber(any(),any(),any())).thenReturn(null);

        when(userTierDeviceDAO.getByUserId(any(),any())).thenReturn(Arrays.asList(
                UserTierDeviceDO.builder().serialNumber("sn1").tierId(1).build(),
                UserTierDeviceDO.builder().serialNumber("sn2").tierId(1).build()
        ));

        when(userService.queryTenantIdById(any())).thenReturn(null);
        {
            userVipTier.setTierIdList(Lists.newArrayList());
            userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
            when(userVipService.queryUserVipInfo(any(),any(),any())).thenReturn(userVipTier);
            when(userVipService.queryTierId4GList(any())).thenReturn(Lists.newArrayList());
            userTierDeviceServiceUnderTest.updateUserTierDeviceInfo(userId,userTierDeviceRequest);
        }
        {
            userVipTier.setTierIdList(Arrays.asList(1,100));
            userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
            // 套餐type = 0,老套餐
            when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(1).tierType(0).level(1).build());


            when(userVipService.queryUserVipInfo(any(),any(),any())).thenReturn(userVipTier);
            when(userVipService.queryTierId4GList(any())).thenReturn(Lists.newArrayList());
            userTierDeviceServiceUnderTest.updateUserTierDeviceInfo(userId,userTierDeviceRequest);
        }
        {
            userVipTier.setTierIdList(Arrays.asList(1,100));
            userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
            // 套餐type = 4,新套餐
            when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(1).maxDeviceNum(2).tierType(4).level(1).tierServiceType(0).build());
            when(deviceInfoService.listDevicesByUserId(any())).thenReturn(Arrays.asList(
                    DeviceDO.builder().serialNumber("sn3").adminId(userId).userId(userId).iccid("iccid").simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn4").adminId(userId).userId(userId).simThirdParty(0).build()
            ));

            when(deviceInfoService.checkIfDeviceUse4G("sn3")).thenReturn(true);
            when(deviceInfoService.checkIfDeviceUse4G("sn4")).thenReturn(false);

            doNothing().when(redisService).set(any(),any(),any());
            when(userTierDeviceDAO.queryByUserTierIdAndSerialNumber(any(),any(),any())).thenReturn(new UserTierDeviceDO());

            when(userVipService.queryUserVipInfo(any(),any(),any())).thenReturn(userVipTier);
            when(userVipService.queryTierId4GList(any())).thenReturn(Lists.newArrayList());
                userTierDeviceServiceUnderTest.updateUserTierDeviceInfo(userId,userTierDeviceRequest);
        }
        {
            userVipTier.setTierIdList(Arrays.asList(1,100));
            userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
            // 套餐type = 4,新套餐
            when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(1).maxDeviceNum(2).tierType(4).level(1).tierServiceType(1).build());
            when(deviceInfoService.listDevicesByUserId(any())).thenReturn(Arrays.asList(
                    DeviceDO.builder().serialNumber("sn3").adminId(userId).userId(userId).iccid("iccid").simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn4").adminId(userId).userId(userId).simThirdParty(0).build()
            ));

            when(deviceInfoService.checkIfDeviceUse4G("sn3")).thenReturn(true);
            when(deviceInfoService.checkIfDeviceUse4G("sn4")).thenReturn(false);

            doNothing().when(redisService).set(any(),any(),any());
            when(userTierDeviceDAO.queryByUserTierIdAndSerialNumber(any(),any(),any())).thenReturn(new UserTierDeviceDO());

            when(userVipService.queryUserVipInfo(any(),any(),any())).thenReturn(userVipTier);
            when(userVipService.queryTierId4GList(any())).thenReturn(Lists.newArrayList());
            userTierDeviceServiceUnderTest.updateUserTierDeviceInfo(userId,userTierDeviceRequest);
        }
    }

    @Test(expected = Exception.class)
    public void test_updateUserTierDeviceInfo(){
        Integer userId = 1;

        // Setup
        final UserTierDeviceRequest userTierDeviceRequest = new UserTierDeviceRequest();
        userTierDeviceRequest.setList(Arrays.asList(
                UserTierDeviceInfo.builder().tierId(-2).activeDeviceSnList(Arrays.asList("sn1","sn2")).build()
        ));


        AppInfo appInfo = AppInfo.builder()
                .appName("appName")
                .appType("iOS")
                .tenantId(TENANTID_VICOO)
                .build();
        userTierDeviceRequest.setApp(appInfo);

        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierIdList(Arrays.asList(1));
        userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
        doNothing().when(userTierDeviceDAO).deleteByUserIdAndSerialNumber(any(),any(),any(),any(),any());
        when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(1).build());
        doNothing().when(redisService).set(any(),any(),any());
        when(userTierDeviceDAO.queryByUserTierIdAndSerialNumber(any(),any(),any())).thenReturn(null);


        when(userService.queryTenantIdById(any())).thenReturn(null);

        when(userVipService.queryUserVipInfo(any(),any(),any())).thenReturn(userVipTier);
        when(userVipService.queryTierId4GList(any())).thenReturn(Lists.newArrayList());
        userTierDeviceServiceUnderTest.updateUserTierDeviceInfo(userId,userTierDeviceRequest);
    }

    @Test(expected = Exception.class)
    public void test_updateUserTierDeviceInfo_deviceNum(){
        Integer userId = 1;

        // Setup
        final UserTierDeviceRequest userTierDeviceRequest = new UserTierDeviceRequest();
        userTierDeviceRequest.setList(Arrays.asList(
                UserTierDeviceInfo.builder().tierId(1).activeDeviceSnList(Arrays.asList("sn3")).build()
        ));

        AppInfo appInfo = AppInfo.builder()
                .appName("appName")
                .appType("iOS")
                .tenantId(TENANTID_VICOO)
                .build();
        userTierDeviceRequest.setApp(appInfo);

        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierIdList(Arrays.asList(1));
        userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
        doNothing().when(userTierDeviceDAO).deleteByUserIdAndSerialNumber(any(),any(),any(),any(),any());
        when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(1).build());
        doNothing().when(redisService).set(any(),any(),any());

        {
            userVipTier.setTierIdList(Arrays.asList(1,100));
            userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
            // 套餐type = 4,新套餐
            when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(1).maxDeviceNum(2).tierType(4).level(1).tierServiceType(1).build());
            when(deviceInfoService.listDevicesByUserId(any())).thenReturn(Arrays.asList(
                    DeviceDO.builder().serialNumber("sn3").adminId(userId).userId(userId).iccid("iccid").simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn4").adminId(userId).userId(userId).iccid("iccid").simThirdParty(0).build()
            ));

            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(true);

            doNothing().when(redisService).set(any(),any(),any());
            when(userTierDeviceDAO.queryByUserTierIdAndSerialNumber(any(),any(),any())).thenReturn(new UserTierDeviceDO());

            when(userVipService.queryUserVipInfo(any(),any(),any())).thenReturn(userVipTier);
            when(userVipService.queryTierId4GList(any())).thenReturn(Lists.newArrayList());
            userTierDeviceServiceUnderTest.updateUserTierDeviceInfo(userId,userTierDeviceRequest);
        }

        {
            // 指定套餐下设备超过套餐限制
            userTierDeviceRequest.setList(Arrays.asList(
                    UserTierDeviceInfo.builder().tierId(1).activeDeviceSnList(Arrays.asList("sn1")).build(),
                    UserTierDeviceInfo.builder().tierId(1).activeDeviceSnList(Arrays.asList("sn2")).build()

            ));

            userVipTier.setTierIdList(Arrays.asList(1,100));
            userVipTier.setTierDeviceInfoMap(Maps.newHashMap());
            // 套餐type = 4,新套餐
            when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(1).maxDeviceNum(1).tierType(4).level(1).tierServiceType(1).build());
            when(deviceInfoService.listDevicesByUserId(any())).thenReturn(Arrays.asList(
                    DeviceDO.builder().serialNumber("sn1").adminId(userId).userId(userId).iccid("iccid").simThirdParty(0).build(),
                    DeviceDO.builder().serialNumber("sn2").adminId(userId).userId(userId).iccid("iccid").simThirdParty(0).build()
            ));

            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(true);

            doNothing().when(redisService).set(any(),any(),any());
            when(userTierDeviceDAO.queryByUserTierIdAndSerialNumber(any(),any(),any())).thenReturn(new UserTierDeviceDO());

            when(userVipService.queryUserVipInfo(any(),any(),any())).thenReturn(userVipTier);
            when(userVipService.queryTierId4GList(any())).thenReturn(Lists.newArrayList());
            userTierDeviceServiceUnderTest.updateUserTierDeviceInfo(userId,userTierDeviceRequest);
        }
    }

    @Test
    public void testFilterOutDeviceVip() {
        // Setup
        final List<DeviceDO> deviceDOList = Arrays.asList(new DeviceDO() {{
            setSerialNumber("serialNumber");
        }});
        {
            when(vipService.queryLastDeviceVip("tenantId", "serialNumber")).thenReturn(new DeviceVipLog(0L, "productId", 0));
            // Run the test
            List<DeviceDO> result = userTierDeviceServiceUnderTest.filterOutDeviceVip("tenantId", deviceDOList);
            // Verify the results
            assertTrue(result.size() == 1);
        }
        {
            when(vipService.queryLastDeviceVip("tenantId", "serialNumber"))
                    .thenReturn(new DeviceVipLog(0L, "productId", 1));
            List<DeviceDO> result = userTierDeviceServiceUnderTest.filterOutDeviceVip("tenantId", deviceDOList);
            // Verify the results
            assertTrue(result.size() == 0);
        }
    }

    @Test
    public void test_refreshUserDeviceTierCache() {
        Integer userId = 1;
        doNothing().when(userTierDeviceServiceUnderTest).refreshUserDeviceTierCache(userId, null);
        userTierDeviceServiceUnderTest.refreshUserDeviceTierCache(userId,false);
    }

    @Test
    public void testRefreshUserDeviceTierCache() {
        Integer userId = 1;
        String tenantId = TENANTID_VICOO;
        Integer tierId = 1;
        // Setup
        when(userService.queryTenantIdById(userId)).thenReturn(tenantId);

        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierId(tierId);
        userVipTier.setTierIdList(Arrays.asList(tierId, 100));
        when(userVipService.queryUserVipInfo(userId, "en", tenantId)).thenReturn(userVipTier);

        when(userTierDeviceDAO.deleteByUserIdAndTierList(any(), any())).thenReturn(1);

        // Configure UserTierDeviceDAO.getByUserId(...).
        final List<UserTierDeviceDO> userTierDeviceDOList = Arrays.asList(
                new UserTierDeviceDO(0L, userId, 100, "tierUid", "serialNumber1", tenantId),
                new UserTierDeviceDO(0L, userId, tierId, "tierUid", "serialNumber2", tenantId)
        );
        when(mockUserTierDeviceDAO.getByUserId(tenantId, userId)).thenReturn(userTierDeviceDOList);

        Gson gson = new Gson();
        final List<DeviceDO> deviceDOList = Arrays.asList(new DeviceDO() {{
                                                              setSerialNumber("serialNumber1");
                                                              setModelNo("modelNo");
                                                              setAdminId(userId);
                                                          }},
                new DeviceDO() {{
                    setSerialNumber("serialNumber2");
                    setModelNo("modelNo");
                    setAdminId(userId);
                }});
        when(deviceInfoService.listDevicesByUserId(userId)).thenReturn(deviceDOList);
        when(paasVipService.queryLastDeviceVip(any(), any())).thenReturn(new DeviceVipLog(0L, "productId", 0));
        when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(false);

        Tier tier1 = new Tier();
        tier1.setLevel(1);
        tier1.setMaxDeviceNum(1);
        tier1.setTierType(2);

        Tier tier100 = new Tier();
        tier100.setLevel(0);
        tier100.setMaxDeviceNum(null);
        when(tierService.queryTierById(100)).thenReturn(tier100);

        when(tierService.queryTierById(tierId)).thenReturn(tier1);
        doReturn(true).when(redisService).delete(anyString());
        doNothing().when(userTierDeviceDAO).deleteByUserIdAndSerialNumber(any(), any(), any(), any(), any());
        {
            when(userVipService.hasUserVipActive(any())).thenReturn(false);
            when(userTierDeviceDAO.deleteByUserIdAndTierList(any(), any())).thenReturn(1);
            userTierDeviceServiceUnderTest.refreshUserDeviceTierCache(userId, userVipTier);
        }
        when(userVipService.hasUserVipActive(any())).thenReturn(true);
        {
            //老的刷新规则
            tier1.setTierType(0);
            when(userVipService.queryTierId4GList(any())).thenReturn(Lists.newArrayList());
            when(userVipService.queryUserVipByActiveTierId(userId, tierId)).thenReturn(null);
            userTierDeviceServiceUnderTest.refreshUserDeviceTierCache(userId, null);
        }

        //新的刷新逻辑
        tier1.setTierType(1);

        {
            //
            when(userVipService.queryTierId4GList(any())).thenReturn(Lists.newArrayList());
            when(userVipService.queryUserVipByActiveTierId(userId, tierId)).thenReturn(null);
            userTierDeviceServiceUnderTest.refreshUserDeviceTierCache(userId, null);
        }
        {
            //无相同套餐等级的 userVip 记录
            when(userVipService.queryUserVipByActiveTierId(userId, tierId)).thenReturn(null);
            userTierDeviceServiceUnderTest.refreshUserDeviceTierCache(userId, null);
        }
        {
            // 订单信息为空
            when(userVipService.queryUserVipByActiveTierId(userId, tierId)).thenReturn(UserVipDO.builder().build());
            when(iOrderDAO.queryByOrderId(0L)).thenReturn(null);
            userTierDeviceServiceUnderTest.refreshUserDeviceTierCache(userId, null);
        }
        {
            when(userVipService.queryUserVipByActiveTierId(userId, tierId)).thenReturn(UserVipDO.builder().orderId(1L).build());
            when(iOrderDAO.queryByOrderId(1L)).thenReturn(null);

            doNothing().when(redisService).set(any(), any(), any());
            when(userTierDeviceDAO.queryByUserTierIdAndSerialNumber(any(), any(), any())).thenReturn(UserTierDeviceDO.builder().build());
            userTierDeviceServiceUnderTest.refreshUserDeviceTierCache(userId, null);
        }
        {
            when(userVipService.queryUserVipByActiveTierId(userId, tierId)).thenReturn(UserVipDO.builder().orderId(1L).tierId(1).build());

            when(userTierDeviceDAO.getByUserId(any(), any())).thenReturn(Arrays.asList(UserTierDeviceDO.builder().tierId(1).serialNumber("serialNumber1").build()));

            ApplePaymentRequest request = new ApplePaymentRequest();
            request.setTierDeviceList(Collections.singletonList("serialNumber1"));
            when(iOrderDAO.queryByOrderId(1L)).thenReturn(OrderDO.builder().tenantId(TENANTID_VICOO).userId(userId).orderType(2).extend(gson.toJson(request)).build());

            doNothing().when(redisService).set(any(), any(), any());
            when(userTierDeviceDAO.queryByUserTierIdAndSerialNumber(any(), any(), any())).thenReturn(UserTierDeviceDO.builder().build());
            userTierDeviceServiceUnderTest.refreshUserDeviceTierCache(userId, null);
        }
    }

//    @Test
//    public void testGetDeviceCurrentTier() {
//        when(paasVipService.isVipDevice(0, "serialNumber")).thenReturn(false);
//        when(mockTierService.queryTierById(any())).thenReturn(new Tier());
//
//        // Setup
//        when(mockRedisService.get(any())).thenReturn("NULL");
//        Integer result = userTierDeviceServiceUnderTest.getDeviceCurrentTier(0, "serialNumber");
//        // Verify the results
//        assertTrue(result == null);
//
//        when(mockUserService.queryTenantIdById(0)).thenReturn("tenantId");
//
//        // Configure UserVipService.queryUserVipInfo(...).
//        final TierTerm tierTerm = new TierTerm();
//        tierTerm.setTitle("title");
//        tierTerm.setDescribe("describe");
//        tierTerm.setUrl("url");
//        final UserVipTier.TierList tierList = new UserVipTier.TierList();
//        tierList.setTierId(0);
//        tierList.setAdditionalTierUid("additionalTierUid");
//        tierList.setTierName("tierName");
//        tierList.setTierDateStart("tierDateStart");
//        tierList.setTierDateEnd("tierDateEnd");
//        tierList.setTierStartTime(0);
//        tierList.setTierEndTime(0);
//        final UserVipTier userVipTier = new UserVipTier();
//        userVipTier.setTierId(1);
//        when(mockUserVipService.queryUserVipInfo(0, "en", "tenantId")).thenReturn(userVipTier);
//
//        // Run the test
//        when(mockRedisService.get(any())).thenReturn("NULL");
//        result = userTierDeviceServiceUnderTest.getDeviceCurrentTier(0, "serialNumber");
//        assertTrue(result == null);
//        assertTrue(userTierDeviceServiceUnderTest.getIsNoVipOrFreeTier2(0, "serialNumber"));
//
//        when(mockRedisService.get(any())).thenReturn(null);
//        result = userTierDeviceServiceUnderTest.getDeviceCurrentTier(0, "serialNumber");
//        assertTrue(result == 1);
//        assertFalse(userTierDeviceServiceUnderTest.getIsNoVipOrFreeTier2(0, "serialNumber"));
//
//        when(mockRedisService.get(any())).thenReturn("2");
//        result = userTierDeviceServiceUnderTest.getDeviceCurrentTier(0, "serialNumber");
//        assertTrue(result == 2);
//        assertFalse(userTierDeviceServiceUnderTest.getIsNoVipOrFreeTier2(0, "serialNumber"));
//
//        when(mockRedisService.get(any())).thenReturn("10");
//        result = userTierDeviceServiceUnderTest.getDeviceCurrentTier(0, "serialNumber");
//        assertTrue(result == 10);
//        assertFalse(userTierDeviceServiceUnderTest.getIsNoVipOrFreeTier2(0, "serialNumber"));
//
//        when(mockRedisService.get(any())).thenReturn("100");
//        result = userTierDeviceServiceUnderTest.getDeviceCurrentTier(0, "serialNumber");
//        assertTrue(result == 100);
//        assertTrue(userTierDeviceServiceUnderTest.getIsNoVipOrFreeTier2(0, "serialNumber"));
//    }

    @Test
    public void testIsDeviceAdditionalTierActive() {
        // Setup
        when(redisService.containsKey("key")).thenReturn(false);

        // Run the test
        final boolean result = userTierDeviceServiceUnderTest.isDeviceAdditionalTierActive(0, "serialNumber", "tierUid");

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void onAddUserDevice_ShouldRefreshUserDeviceTierCache_WhenValidUserIdAndSerialNumber() throws Exception {
        // Arrange
        Integer userId = 1;
        String serialNumber = "123456";
        String tenantId = "tenant123";

        {

            // Act
            doNothing().when(mqSender).send(any(),anyInt(),any());
            userTierDeviceServiceUnderTest.refreshUserDevice(userId, TENANTID_VICOO);

            // Assert
            verify(mqSender,times(1)).send(any(), anyInt(),any());
        }
        {
            // Act
            doNothing().when(mqSender).send(any(),anyInt(),any());
            when(userService.queryTenantIdById(any())).thenReturn(TENANTID_VICOO);
            userTierDeviceServiceUnderTest.refreshUserDevice(userId,null);
        }
    }


    @Test
    public void testOnRemoveUserDevice() {

        when(userService.queryTenantIdById(any())).thenReturn("te");
        doNothing().when(userTierDeviceDAO).deleteByUserIdAndSerialNumber(any(),any(),any(),any(),any());
        doNothing().when(mqSender).send(any(),anyInt(),any());

        userTierDeviceServiceUnderTest.onRemoveUserDevice(0, "sn_01");
    }

    @Test
    public void testFilterOutShareDevice() {
//        userTierDeviceServiceUnderTest.filterOutShareDevice(0, null);

//        List<DeviceDO> deviceDOList = Arrays.asList(new DeviceDO(){{
//            setSerialNumber("sn_01");
//        }}, new DeviceDO(){{
//            setSerialNumber("sn_02");
//        }});
//        when(userRoleService.getUserRoleByUserId(any(), any())).thenReturn(Collections.singletonList(new UserRoleDO (){{
//            setSerialNumber("sn_01");
//        }}));
//        deviceDOList = userTierDeviceServiceUnderTest.filterOutShareDevice(0, deviceDOList);
//        Assert.assertTrue(deviceDOList.size() == 1 && Objects.equal(deviceDOList.get(0).getSerialNumber(), "sn_01"));

        DeviceDO device1 = new DeviceDO();
        device1.setSerialNumber("123456789");
        device1.setAdminId(1);

        DeviceDO device2 = new DeviceDO();
        device2.setSerialNumber("987654321");
        device2.setAdminId(1);

        List<DeviceDO> deviceDOList = Arrays.asList(device1, device2);

        UserRoleDO userRole1 = new UserRoleDO();
        userRole1.setSerialNumber("123456789");

        UserRoleDO userRole2 = new UserRoleDO();
        userRole2.setSerialNumber("987654321");

        List<UserRoleDO> userRoleDOList = Arrays.asList(userRole1, userRole2);
        when(userRoleService.getUserRoleByUserId(1, UserRoleEnums.ADMIN.getCode())).thenReturn(userRoleDOList);

        // Act
        List<DeviceDO> result = userTierDeviceServiceUnderTest.filterOutShareDevice(1, deviceDOList);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.contains(device1));
        assertTrue(result.contains(device2));
    }

    @Test
    @DisplayName("用户未绑定设备")
    public void testFilterOutShareDeviceWithEmptyDeviceList() {
        // Arrange
        List<DeviceDO> deviceDOList = Collections.emptyList();
        // Act
        List<DeviceDO> result = userTierDeviceServiceUnderTest.filterOutShareDevice(1, deviceDOList);
        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("过滤分享设备-根据用户绑定设备过滤")
    public void testFilterOutShareDeviceWithEmptyUserRoleList() {
        // Arrange
        DeviceDO device1 = new DeviceDO();
        device1.setSerialNumber("123456789");
        device1.setAdminId(null);

        DeviceDO device2 = new DeviceDO();
        device2.setSerialNumber("987654321");
        device2.setAdminId(1);

        List<DeviceDO> deviceDOList = Arrays.asList(device1, device2);

        {
            when(userRoleService.getUserRoleByUserId(1, UserRoleEnums.ADMIN.getCode())).thenReturn(Collections.emptyList());

            // Act
            List<DeviceDO> result = userTierDeviceServiceUnderTest.filterOutShareDevice(1, deviceDOList);
            // Assert
            assertTrue(result.isEmpty());
        }
        {
            List<UserRoleDO> userRoleDOList = Arrays.asList(UserRoleDO.builder().serialNumber("123456789").build());
            when(userRoleService.getUserRoleByUserId(1, UserRoleEnums.ADMIN.getCode())).thenReturn(userRoleDOList);
            List<DeviceDO> expectResult = Arrays.asList(device1, device2);
            List<DeviceDO> actualResult = userTierDeviceServiceUnderTest.filterOutShareDevice(1, deviceDOList);
            Assert.assertEquals(expectResult.size(), actualResult.size());
        }
    }

    @Test
    public void test_getIsNoVipOrFreeTier2() {
        Integer userId = 3954734;
        String sn = OpenApiUtil.shortUUID();
        {
            when(vipService.isVipDevice(userId, sn)).thenReturn(true);
            Assert.assertFalse(userTierDeviceServiceUnderTest.getIsNoVipOrFreeTier2(userId, sn));
        }
    }

    @Test
    @DisplayName("查询已制定的套餐下设备")
    public void testGetActiveTierDeviceList_Positive() {
        // Prepare test data
        Integer userId = 1;
        Tier tier = new Tier();
        tier.setMaxDeviceNum(1);

        UserTierDeviceDO userTierDeviceDO1 = new UserTierDeviceDO();
        userTierDeviceDO1.setSerialNumber("123");
        UserTierDeviceDO userTierDeviceDO2 = new UserTierDeviceDO();
        userTierDeviceDO2.setSerialNumber("456");


        List<UserTierDeviceDO> userTierDeviceList = Lists.newArrayList();
        userTierDeviceList.add(userTierDeviceDO1);
        userTierDeviceList.add(userTierDeviceDO2);
        when(userTierDeviceDAO.getByUserTierId(any(), any())).thenReturn(userTierDeviceList);

        // Execute the method
        Set<String> result = userTierDeviceServiceUnderTest.getActiveTierDeviceList(userId, tier);

        // Verify the result
        Set<String> expected = new HashSet<>();
        expected.add("123");
        expected.add("456");
        Assert.assertEquals(expected, result);
    }

    @Test
    @DisplayName("无线设备套餐-指定的设备")
    public void testGetActiveTierDeviceList_MaxDeviceNumNull() {
        // Prepare test data
        Integer userId = 1;
        Tier tier = new Tier();
        tier.setMaxDeviceNum(null);

        // Execute the method
        Set<String> result = userTierDeviceServiceUnderTest.getActiveTierDeviceList(userId, tier);

        // Verify the result
        Set<String> expected = new HashSet<>();
        Assert.assertEquals(expected, result);
    }

    @Test
    @DisplayName("未指定设备")
    public void testGetActiveTierDeviceList_EmptyUserTierDeviceList() {
        // Prepare test data
        Integer userId = 1;
        Tier tier = new Tier();
        tier.setMaxDeviceNum(1);

        when(userTierDeviceDAO.getByUserTierId(any(), any())).thenReturn(Lists.newArrayList());

        // Execute the method
        Set<String> result = userTierDeviceServiceUnderTest.getActiveTierDeviceList(userId, tier);

        // Verify the result
        Set<String> expected = new HashSet<>();
        Assert.assertEquals(expected, result);
    }

    @Test
    @DisplayName("获取用户名下非vip设备")
    public void testQueryUserAdminDevice_PositiveCase() {
        // Mock the behavior of deviceInfoService.listDevicesByUserId()
        DeviceDO deviceDO1 = DeviceDO.builder().serialNumber("sn1").adminId(1).build();
        DeviceDO deviceDO2 = DeviceDO.builder().serialNumber("sn2").adminId(1).build();


        List<DeviceDO> userDevices = Arrays.asList(deviceDO1, deviceDO2);
        when(deviceInfoService.listDevicesByUserId(1)).thenReturn(userDevices);


        when(vipService.queryLastDeviceVip(any(), any())).thenReturn(null);

        // Call the method to test
        List<DeviceDO> result = userTierDeviceServiceUnderTest.queryUserAdminDevice(1, "tenantId");

        // Verify the result
        assertEquals(2, result.size());
    }


    @Test
    public void testInitAdditionalUserTierInfoWithEmptyDeviceList() {
        // Arrange
        AppRequestBase request = new AppRequestBase();
        Integer userId = 123;
        List<DeviceDO> deviceDOList = Collections.emptyList();

        // Act
        List<UserTierDeviceInfo> result = userTierDeviceServiceUnderTest.initAdditionalUserTierInfo(request, userId, deviceDOList);

        // Assert
        assertTrue(result.isEmpty());
    }


    @Test
    @DisplayName("鸟类套餐")
    public void testInitAdditionalUserTierInfoWithNonEmptyDeviceList() {
        // Arrange
        AppRequestBase request = new AppRequestBase();
        Integer userId = 123;
        List<DeviceDO> deviceDOList = Arrays.asList(
                DeviceDO.builder().serialNumber("sn1").build(),
                DeviceDO.builder().serialNumber("sn2").build(),
                DeviceDO.builder().serialNumber("sn3").build()
        );

        //鸟类套餐
        AdditionalUserTierInfo additionalUserTierInfo = new AdditionalUserTierInfo();
        additionalUserTierInfo.setTierUid("b");
        List<AdditionalUserTierInfo> currentAdditionalUserTierList = Arrays.asList(additionalUserTierInfo);

        when(additionalUserTierService.getActiveAdditionalUserTierInfo(userId, request.getApp().getTenantId(), request.getLanguage())).thenReturn(currentAdditionalUserTierList);

        {
            AdditionalTierInfo additionalTierInfo = new AdditionalTierInfo();
            additionalTierInfo.setTierUid("c");
            List<AdditionalTierInfo> additionalTierInfoList = Arrays.asList(additionalTierInfo);

            when(tierService.queryAdditionalTierList(request, userId)).thenReturn(additionalTierInfoList);


            // Act
            List<UserTierDeviceInfo> result = userTierDeviceServiceUnderTest.initAdditionalUserTierInfo(request, userId, deviceDOList);

            // Assert
            assertTrue(result.isEmpty());
        }
        {
            AdditionalTierInfo additionalTierInfo = new AdditionalTierInfo();
            additionalTierInfo.setTierUid("b");
            List<AdditionalTierInfo> additionalTierInfoList = Arrays.asList(additionalTierInfo);

            when(tierService.queryAdditionalTierList(request, userId)).thenReturn(additionalTierInfoList);


            // Act
            List<UserTierDeviceInfo> result = userTierDeviceServiceUnderTest.initAdditionalUserTierInfo(request, userId, deviceDOList);

            // Assert
            assertEquals(1, result.size());
        }
    }


    @Test
    public void testQueryUserTierDeviceDOListByUser_Negative() {
        // Mock data
        Integer userId = 1;
        String tenantId = "exampleTenantId";

        List<UserTierDeviceDO> expectResult = Lists.newArrayList();
        // Mock the behavior of userTierDeviceDAO to return null
        when(userTierDeviceDAO.getByUserId(tenantId, userId)).thenReturn(expectResult);

        // Call the method to be tested
        List<UserTierDeviceDO> actualList = userTierDeviceServiceUnderTest.queryUserTierDeviceDOListByUser(userId, tenantId);

        // Verify the result is null
        Assert.assertEquals(expectResult, actualList);
    }

    //    @Test
    //    public void testSaveUserTierDevice_Positive() {
    //        // Prepare test data
    //        Integer buyTierId = 1;
    //        OrderDO orderDO = new OrderDO();
    //        orderDO.setTenantId("tenant1");
    //        {
    //
    //        }
    //
    //        List<String> serialNumberList = Arrays.asList("serial1", "serial2");
    //
    //        // Mock the behavior of userTierDeviceDAO
    //        when(userTierDeviceDAO.getByUserTierId(orderDO.getUserId(), buyTierId)).thenReturn(new
    // ArrayList<>());
    //
    //        // Call the method to be tested
    //        userTierDeviceServiceUnderTest.saveUserTierDevice(buyTierId, orderDO);
    //
    //        // Verify the interactions with userTierDeviceDAO
    //        verify(userTierDeviceDAO, times(1)).batchUpdateUserTierDevice(anyList());
    //    }

    @Test
    public void testInitUserTierDeviceList() {
        Integer userId = 1;
        Integer bugTierId = 1;


        OrderDO orderDO = new OrderDO();
        orderDO.setTenantId(TENANTID_VICOO);
        orderDO.setUserId(userId);
        orderDO.setOrderType(PaymentTypeEnums.APPLE.getCode());
        orderDO.setExtend("{\"tierDeviceList\":[\"serialNumber1\",\"serialNumber2\"]}");
        orderDO.setUserId(userId);

        Tier tier = new Tier();
        tier.setTierId(bugTierId);
        tier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
        when(tierService.queryTierById(any())).thenReturn(tier);
        List<DeviceDO> deviceDOList = Arrays.asList(new DeviceDO() {{
                                                              setSerialNumber("serialNumber1");
                                                              setModelNo("modelNo");
                                                              setAdminId(userId);
                                                          }},
                new DeviceDO() {{
                    setSerialNumber("serialNumber2");
                    setModelNo("modelNo");
                    setAdminId(userId);
                }});
        when(deviceInfoService.listDevicesByUserId(any())).thenReturn(deviceDOList);
        when(paasVipService.queryLastDeviceVip(any(), any())).thenReturn(new DeviceVipLog(0L, "productId", 0));
        doNothing().when(mockUserTierDeviceDAO).deleteByUserIdAndSerialNumber(any(), any(), any(), any(), any());

        // Configure UserTierDeviceDAO.getByUserId(...).
         List<UserTierDeviceDO> userTierDeviceDOList = Arrays.asList(
                new UserTierDeviceDO(0L, userId, bugTierId, "tierUid", "serialNumber1", TENANTID_VICOO),
                new UserTierDeviceDO(0L, userId, bugTierId, "tierUid", "serialNumber2", TENANTID_VICOO)
        );
        orderDO.setTenantId(TENANTID_VICOO);
//        {
//            List<String> expectResult = Arrays.asList("serialNumber1", "serialNumber2");
//            List<String> actualResult = userTierDeviceServiceUnderTest.initUserTierDeviceList(orderDO, bugTierId, null);
//            assertEquals(expectResult, actualResult);
//        }
//        {
//            tier.setMaxDeviceNum(2);
//            when(userTierDeviceDAO.getByUserId(any(),any())).thenReturn(userTierDeviceDOList);
//            List<String> expectResult = Arrays.asList("serialNumber1","serialNumber2");
//            List<String> actualResult = userTierDeviceServiceUnderTest.initUserTierDeviceList(orderDO,bugTierId,null);
//            Assert.assertEquals(expectResult.size(),actualResult.size());
//        }
//        {
//            tier.setMaxDeviceNum(1);
//
//            when(userTierDeviceDAO.getByUserId(any(),any())).thenReturn(userTierDeviceDOList);
//            when(redisService.delete(anyString())).thenReturn(false);
//            doNothing().when(userTierDeviceDAO).deleteByUserIdAndSerialNumber(any(),any(),any(),any(),any());
//            List<String> expectResult = Arrays.asList("serialNumber2");
//            List<String> actualResult = userTierDeviceServiceUnderTest.initUserTierDeviceList(orderDO, bugTierId, null);
//            Assert.assertEquals(expectResult, actualResult);
//        }
        orderDO.setExtend("{\"tierDeviceList\":[\"serialNumber1\",\"serialNumber2\"]}");

//        {
//            tier.setMaxDeviceNum(2);
//            orderDO.setOrderType(PaymentTypeEnums.GOOGLE.getCode());
//            List<String> expectResult = Arrays.asList("serialNumber1","serialNumber2");
//            List<String> actualResult = userTierDeviceServiceUnderTest.initUserTierDeviceList(orderDO,bugTierId,null);
//            Assert.assertEquals(expectResult.size(),actualResult.size());
//        }
        {
            final List<DeviceDO> requestList = Arrays.asList(new DeviceDO() {{
                                                                 setSerialNumber("serialNumber1");
                                                                 setModelNo("modelNo");
                                                                 setAdminId(userId);
                                                                 setIccid("iccid");
                                                                 setSimThirdParty(0);
                                                             }},
                    new DeviceDO() {{
                        setSerialNumber("serialNumber2");
                        setModelNo("modelNo");
                        setAdminId(userId);
                        setIccid("iccid");
                        setSimThirdParty(0);
                    }}  ,
                    new DeviceDO() {{
                        setSerialNumber("serialNumber3");
                        setModelNo("modelNo");
                        setIccid("iccid");
                        setSimThirdParty(0);
                        setAdminId(userId);
                    }} );

            tier.setTierServiceType(TierServiceTypeEnums.TIER_4G.getCode());
            tier.setMaxDeviceNum(0);
            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(true);
            when(userTierDeviceDAO.getByUserId(any(),any())).thenReturn(Lists.newArrayList());
            orderDO.setOrderType(PaymentTypeEnums.GOOGLE.getCode());
            List<String> expectResult = Arrays.asList("serialNumber1","serialNumber2","serialNumber3");
            List<String> actualResult = userTierDeviceServiceUnderTest.initUserTierDeviceList(orderDO, bugTierId, requestList);
            Assert.assertEquals(expectResult.size(), actualResult.size());
        }
        {
            final List<DeviceDO> requestList = Arrays.asList(new DeviceDO() {{
                                                                 setSerialNumber("serialNumber1");
                                                                 setModelNo("modelNo");
                                                                 setAdminId(userId);
                                                                 setIccid("iccid");
                                                                 setSimThirdParty(0);
                                                             }},
                    new DeviceDO() {{
                        setSerialNumber("serialNumber2");
                        setModelNo("modelNo");
                        setAdminId(userId);
                        setIccid("iccid");
                        setSimThirdParty(0);
                    }}  ,
                    new DeviceDO() {{
                        setSerialNumber("serialNumber3");
                        setModelNo("modelNo");
                        setIccid("iccid");
                        setSimThirdParty(0);
                        setAdminId(userId);
                    }} );

            tier.setTierServiceType(TierServiceTypeEnums.TIER_4G.getCode());
            tier.setMaxDeviceNum(8);
            when(deviceInfoService.checkIfDeviceUse4G(any())).thenReturn(true);
            when(userTierDeviceDAO.getByUserId(any(),any())).thenReturn(Lists.newArrayList());
            orderDO.setOrderType(PaymentTypeEnums.GOOGLE.getCode());
            when(device4GService.queryDevice4GSimDO(any())).thenReturn(Device4GSimDO.builder().iccid("iccid").simThirdParty(0).build());

            List<String> expectResult = Arrays.asList("serialNumber1","serialNumber2","serialNumber3");
            List<String> actualResult = userTierDeviceServiceUnderTest.initUserTierDeviceList(orderDO, bugTierId, requestList);
            Assert.assertEquals(expectResult.size(), actualResult.size());
        }

        {
            List deviceDOList1 = Arrays.asList(new DeviceDO() {{
                                                                  setSerialNumber("serialNumber1");
                                                                  setModelNo("modelNo");
                                                                  setAdminId(userId);
                                                              }},
                    new DeviceDO() {{
                        setSerialNumber("serialNumber2");
                        setModelNo("modelNo");
                        setAdminId(11);
                    }}   );
            when(deviceInfoService.listDevicesByUserId(any())).thenReturn(deviceDOList1);
            tier.setMaxDeviceNum(2);
            List<String> expectResult = Arrays.asList("serialNumber1", "serialNumber2");
            List<String> actualResult = userTierDeviceServiceUnderTest.initUserTierDeviceList(orderDO,bugTierId, deviceDOList1);
            Assert.assertEquals(expectResult,actualResult);
        }
    }


    @Test
    @DisplayName("查询当前生效的套餐-无userVip记录")
    public void testQueryUserDeviceCurrentTier_WithNullUserVipDO_ReturnsNull() {
        Integer userId = 1;
        String serialNumber = "123456";

        {
            when(deviceInfoService.checkIfDeviceUse4G(serialNumber)).thenReturn(false);
            when(userVipService.queryUserCurrentVip(userId, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(null);
            Tier result = userTierDeviceServiceUnderTest.queryUserDeviceCurrentTier(userId, serialNumber);
            assertNull(result);
        }
        {
            when(deviceInfoService.checkIfDeviceUse4G(serialNumber)).thenReturn(true);
            when(userVipService.queryUserCurrentVip(userId, TierServiceTypeEnums.TIER_4G.getCode())).thenReturn(null);
            Tier result = userTierDeviceServiceUnderTest.queryUserDeviceCurrentTier(userId, serialNumber);
            assertNull(result);
        }
    }

    @Test
    @DisplayName("查询当前生效的套餐-无设备数量限制")
    public void testQueryUserDeviceCurrentTier_WithMaxDeviceLimitZero_ReturnsTier() {
        Integer userId = 1;
        String serialNumber = "123456";
        Integer tierId = 1;

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setTierId(tierId);

        Tier expectedTier = new Tier();
        expectedTier.setMaxDeviceNum(0);

        when(userVipService.queryUserCurrentVip(userId, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(userVipDO);
        when(tierService.queryTierById(tierId)).thenReturn(expectedTier);

        Tier result = userTierDeviceServiceUnderTest.queryUserDeviceCurrentTier(userId, serialNumber);

        assertEquals(expectedTier, result);
    }

    @Test
    @DisplayName("查询当前生效的套餐-订单信息为Null")
    public void testQueryUserDeviceCurrentTier_WithNullOrderDO_ReturnsNull() {
        Integer userId = 1;
        String serialNumber = "123456";
        Integer tierId = 1;

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setTierId(tierId);

        Tier expectedTier = new Tier();
        expectedTier.setMaxDeviceNum(5);

        when(userVipService.queryUserCurrentVip(any(), any())).thenReturn(userVipDO);
        when(tierService.queryTierById(tierId)).thenReturn(expectedTier);
        when(iOrderDAO.queryByOrderId(userVipDO.getOrderId())).thenReturn(null);

        Tier result = userTierDeviceServiceUnderTest.queryUserDeviceCurrentTier(userId, serialNumber);

        assertNull(result);
    }

    @Test
    @DisplayName("查询当前生效的套餐-订单扩展信息为空")
    public void testQueryUserDeviceCurrentTier_WithBlankExtend_ReturnsNull() {
        Integer userId = 1;
        String serialNumber = "123456";
        Integer tierId = 1;
        Long orderId = 123L;

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setTierId(tierId);
        userVipDO.setOrderId(orderId);

        Tier expectedTier = new Tier();
        expectedTier.setMaxDeviceNum(5);

        OrderDO orderDO = new OrderDO();
        orderDO.setExtend("");

        when(userVipService.queryUserCurrentVip(any(), any())).thenReturn(userVipDO);
        when(tierService.queryTierById(tierId)).thenReturn(expectedTier);
        when(iOrderDAO.queryByOrderId(orderId)).thenReturn(orderDO);

        Tier result = userTierDeviceServiceUnderTest.queryUserDeviceCurrentTier(userId, serialNumber);

        assertNull(result);
    }

    @Test
    @DisplayName("查询当前生效的套餐-不是指定的是设备")
    public void testQueryUserDeviceCurrentTier_WithMatchingSerialNumber_ReturnsTier() {
        Integer userId = 1;
        String serialNumber = "123456";
        Integer tierId = 1;
        Long orderId = 123L;

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setTierId(tierId);
        userVipDO.setOrderId(orderId);

        Tier expectedTier = new Tier();
        expectedTier.setMaxDeviceNum(5);

        OrderDO orderDO = new OrderDO();
        orderDO.setOrderType(PaymentTypeEnums.APPLE.getCode());
        orderDO.setExtend("{\"tierDeviceList\":[\"device1\",\"device2\"]}");

        when(userVipService.queryUserCurrentVip(any(), any())).thenReturn(userVipDO);
        when(tierService.queryTierById(tierId)).thenReturn(expectedTier);
        when(iOrderDAO.queryByOrderId(orderId)).thenReturn(orderDO);

        Tier result = userTierDeviceServiceUnderTest.queryUserDeviceCurrentTier(userId, serialNumber);

        assertEquals(null, result);
    }

    @Test
    @DisplayName("查询当前生效的套餐")
    public void testQueryUserDeviceCurrentTier_WithNonMatchingSerialNumber_ReturnsNull() {
        Integer userId = 1;
        String serialNumber = "123456";
        Integer tierId = 1;
        Long orderId = 123L;

        UserVipDO userVipDO = new UserVipDO();
        userVipDO.setTierId(tierId);
        userVipDO.setOrderId(orderId);

        Tier expectedTier = new Tier();
        expectedTier.setMaxDeviceNum(5);

        OrderDO orderDO = new OrderDO();
        orderDO.setOrderType(PaymentTypeEnums.GOOGLE.getCode());
        orderDO.setExtend("{\"tierDeviceList\":[\"device1\",\"device2\"]}");

        when(userVipService.queryUserCurrentVip(any(), any())).thenReturn(userVipDO);
        when(tierService.queryTierById(tierId)).thenReturn(expectedTier);
        when(iOrderDAO.queryByOrderId(orderId)).thenReturn(orderDO);

        Tier result = userTierDeviceServiceUnderTest.queryUserDeviceCurrentTier(userId, "654321");

        assertNull(result);
    }


    @Test
    public void test_getDeviceCurrentTierWithTierGroupId() {
        when(redisService.getFromSlave(any())).thenReturn("NULL");
        Tuple2<Integer, Integer> exceptResult = null;
        Tuple2<Integer, Integer> actualResult = userTierDeviceServiceUnderTest.getDeviceCurrentTierWithTierGroupId(1, "1");
        Assert.assertEquals(exceptResult, actualResult);
    }

    @Test
    public void test_setDeviceActiveTier() {
        when(deviceInfoService.checkIf4GDeviceHasOfficialSimCard(any())).thenReturn(true);
        doNothing().when(deviceSettingService).pushSettingMsg(any());
        userTierDeviceServiceUnderTest.setDeviceActiveTier("1", 1, "1", 1, 1);

    }

    @Test
    public void test_checkDevice4GTierServiceType(){
        String serialNumber = "sn";
        {
            when(userRoleService.getDeviceAdminUser(any())).thenReturn(0);
            userTierDeviceServiceUnderTest.checkDevice4GTierServiceType(serialNumber);
        }
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(1);
        {
            when(device4GService.queryDevice4GSimDO(any())).thenReturn(null);
            userTierDeviceServiceUnderTest.checkDevice4GTierServiceType(serialNumber);
        }
        when(device4GService.queryDevice4GSimDO(any())).thenReturn(Device4GSimDO.builder().build());
        {
            when(redisService.getFromSlave(any())).thenReturn("NULL");
            userTierDeviceServiceUnderTest.checkDevice4GTierServiceType(serialNumber);
        }
        when(redisService.getFromSlave(any())).thenReturn("1_1");
        when(userService.queryTenantIdById(any())).thenReturn(TENANTID_VICOO);
        {
            when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode()).build());
            userTierDeviceServiceUnderTest.checkDevice4GTierServiceType(serialNumber);
        }
        when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierServiceType(TierServiceTypeEnums.TIER_4G.getCode()).build());
        when(redisService.delete(anyString())).thenReturn(true);
        doNothing().when(userTierDeviceDAO).deleteByUserIdAndSerialNumber(any(),any(),any(),any(),any());
        {
            userTierDeviceServiceUnderTest.checkDevice4GTierServiceType(serialNumber);
        }
    }


    @Test
    public void test_deleteDeviceTierServiceType(){
        Integer userId = 1;
        String tenantId = TENANTID_VICOO;
        Tier tier = Tier.builder().tierId(1).build();

        {
            when(userTierDeviceDAO.getByUserTierId(any(),any())).thenReturn(Lists.newArrayList());
            userTierDeviceServiceUnderTest.deleteDeviceTierServiceType(userId,tenantId,tier);
        }


        {
            tier.setTierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            List<UserTierDeviceDO> list = Arrays.asList(
                    UserTierDeviceDO.builder().serialNumber("sn1").build(),
                    UserTierDeviceDO.builder().serialNumber("sn2").build(),
                    UserTierDeviceDO.builder().serialNumber("sn3").build(),
                    UserTierDeviceDO.builder().serialNumber("sn4").build()
            );

            when(userTierDeviceDAO.getByUserTierId(any(),any())).thenReturn(list);
            when(device4GService.queryDevice4GSimDO("sn1")).thenReturn(Device4GSimDO.builder().iccid("").build());
            when(device4GService.queryDevice4GSimDO("sn2")).thenReturn(Device4GSimDO.builder().iccid("iccid").simThirdParty(1).build());

            when(device4GService.queryDevice4GSimDO("sn3")).thenReturn(null);
            when(device4GService.queryDevice4GSimDO("sn4")).thenReturn(null);

            when(redisService.delete(anyString())).thenReturn(true);
            doNothing().when(userTierDeviceDAO).deleteByUserIdAndSerialNumber(any(),any(),any(),any(),any());
            userTierDeviceServiceUnderTest.deleteDeviceTierServiceType(userId,tenantId,tier);

        }
        {
            tier.setTierServiceType(TierServiceTypeEnums.TIER_4G
                    .getCode());
            List<UserTierDeviceDO> list = Arrays.asList(
                    UserTierDeviceDO.builder().serialNumber("sn1").build(),
                    UserTierDeviceDO.builder().serialNumber("sn2").build(),
                    UserTierDeviceDO.builder().serialNumber("sn3").build(),
                    UserTierDeviceDO.builder().serialNumber("sn4").build()
            );

            when(userTierDeviceDAO.getByUserTierId(any(),any())).thenReturn(list);
            when(device4GService.queryDevice4GSimDO("sn1")).thenReturn(Device4GSimDO.builder().iccid("").build());
            when(device4GService.queryDevice4GSimDO("sn2")).thenReturn(Device4GSimDO.builder().iccid("iccid").simThirdParty(1).build());

            when(device4GService.queryDevice4GSimDO("sn3")).thenReturn(null);
            when(device4GService.queryDevice4GSimDO("sn4")).thenReturn(null);

            when(redisService.delete(anyString())).thenReturn(true);
            doNothing().when(userTierDeviceDAO).deleteByUserIdAndSerialNumber(any(),any(),any(),any(),any());
            userTierDeviceServiceUnderTest.deleteDeviceTierServiceType(userId,tenantId,tier);
        }
    }


    @Test
    public void test_verifyUserBirdAdditional() throws Exception {
        Integer userId = 1;
        Integer tierId = 1;
        {
            tierId = 10;
            userTierDeviceServiceUnderTest.verifyUserBirdAdditional(userId,tierId);
            verify(additionalUserTierService, times(0)).verifyAdditionalTierFree(any(), any(),any());
        }
        {
            tierId = 100;
            userTierDeviceServiceUnderTest.verifyUserBirdAdditional(userId,tierId);
            verify(additionalUserTierService, times(0)).verifyAdditionalTierFree(any(), any(),any());
        }
        {
            tierId = -1;
            userTierDeviceServiceUnderTest.verifyUserBirdAdditional(userId,tierId);
            verify(additionalUserTierService, times(0)).verifyAdditionalTierFree(any(), any(),any());
        }
        tierId = 1;
        {
            when(userVipService.queryUserCurrentVip(any(),any())).thenReturn(null);
            userTierDeviceServiceUnderTest.verifyUserBirdAdditional(userId,tierId);
            verify(additionalUserTierService, times(0)).verifyAdditionalTierFree(any(), any(),any());
        }
        {
            when(userVipService.queryUserCurrentVip(any(),any())).thenReturn(UserVipDO.builder().build());
            userTierDeviceServiceUnderTest.verifyUserBirdAdditional(userId,tierId);
            verify(additionalUserTierService, times(0)).verifyAdditionalTierFree(any(), any(),any());
        }
        {
            when(userVipService.queryUserCurrentVip(any(),any())).thenReturn(UserVipDO.builder().orderId(1L).build());
            when(iOrderDAO.queryByOrderId(any())).thenReturn(null);
            userTierDeviceServiceUnderTest.verifyUserBirdAdditional(userId,tierId);
            verify(additionalUserTierService, times(0)).verifyAdditionalTierFree(any(), any(),any());
        }
        {
            when(userVipService.queryUserCurrentVip(any(),any())).thenReturn(UserVipDO.builder().orderId(1L).build());
            when(iOrderDAO.queryByOrderId(any())).thenReturn(OrderDO.builder().build());
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(null);
            userTierDeviceServiceUnderTest.verifyUserBirdAdditional(userId,tierId);
            verify(additionalUserTierService, times(0)).verifyAdditionalTierFree(any(), any(),any());
        }
        {
            when(userVipService.queryUserCurrentVip(any(),any())).thenReturn(UserVipDO.builder().orderId(1L).build());
            when(iOrderDAO.queryByOrderId(any())).thenReturn(OrderDO.builder().build());
            when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(OrderProductDo.builder().build());

            when(tierService.queryTierById(any())).thenReturn(Tier.builder().build());

            userTierDeviceServiceUnderTest.verifyUserBirdAdditional(userId,tierId);
            verify(additionalUserTierService, times(1)).verifyAdditionalTierFree(any(), any(),any());
        }
    }
}
