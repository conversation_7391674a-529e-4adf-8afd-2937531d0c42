package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.device_msg.DeviceCmd;
import com.addx.iotcamera.helper.kiss.AccessTokenHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.enums.ReadyState;
import org.java_websocket.handshake.ServerHandshake;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.URI;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceMsgTest {

    @Test
    public void test(){

    }

//    @Test
    @SneakyThrows
    public void test_websocket() {
//        final URI uri = URI.create("wss://p-signal-1363592005.addx.live/f98af5080147855b2fa336b3dc05e28b/master/f98af5080147855b2fa336b3dc05e28b?traceId=webrtc-GowD4eiaq397wFFj&time=1234567890123&sign=987654321");
//        final URI uri = URI.create("wss://p-signal-1363592005.addx.live/iot-channel/connect");
        final URI uri = URI.create("wss://p-signal-1363592005.addx.live/iot-service-channel/connect");
        final String iotServiceNodeId = OpenApiUtil.shortUUID();
        Map<String, DeviceCmd> cmdMap = new ConcurrentHashMap<>();
        final WebSocketClient client = new WebSocketClient(uri) {

            @Override
            public void onOpen(ServerHandshake handshake) {
                log.info("onOpen! handshake httpStatus={},httpStatusMessage={},content={}", handshake.getHttpStatus(), handshake.getHttpStatusMessage(), bytesToString(handshake.getContent()));
                final String accessToken = AccessTokenHelper.createForIotService(iotServiceNodeId);
                send("{\"method\":\"AUTH\",\"accessToken\":\"" + accessToken + "\"}");
            }

            @Override
            public void onMessage(String message) {
                log.info("onMessage text! message={}", message);
            }

            @Override
            public void onMessage(ByteBuffer bytes) {
                log.info("onMessage binary! message={}", bytes.array().length);
            }


            @Override
            public void onClose(int code, String reason, boolean remote) {
                log.info("onClose! code={},reason={},remote={}", code, reason, remote);
            }

            @Override
            public void onError(Exception ex) {
                log.error("onError!", ex);
            }
        };
        client.connectBlocking();
        int count = 0;
        while (client.getReadyState() == ReadyState.OPEN) {
            final DeviceCmd deviceCmd = new DeviceCmd().setRecipientClientId("f98af5080147855b2fa336b3dc05e28b")
                    .setName("otaStart").setId("cmd:" + OpenApiUtil.shortUUID()).setTime(PhosUtils.getUTCStamp())
                    .setValue(new JSONObject().fluentPut("count", count++));
            cmdMap.put(deviceCmd.getId(), deviceCmd);
            client.send(JSON.toJSONString(deviceCmd));
            Thread.sleep(3000);
        }
        Thread.currentThread().join();
    }

    public static String bytesToString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) return "";
        try {
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (Throwable e) {
            return "[base64]" + Base64.getEncoder().encodeToString(bytes);
        }
    }

}
