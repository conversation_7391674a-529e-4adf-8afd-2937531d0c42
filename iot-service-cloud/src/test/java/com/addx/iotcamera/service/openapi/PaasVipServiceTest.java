package com.addx.iotcamera.service.openapi;

import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.openapi.DeviceVipLog;
import com.addx.iotcamera.config.PaasVipConfig;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.UserTierDeviceService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.service.VipService;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.addx.iotcamera.util.TestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PaasVipServiceTest {

    @InjectMocks
    private VipService paasVipService;
    @Mock
    private PaasVipConfig paasVipConfig;
    @Mock
    private DeviceVipService deviceVipService;
    @Mock
    private PaasUserVipService paasUserVipService;
    @Mock
    private UserService userService;
    @Mock
    private UserVipService userVipService;
    @Mock
    private UserTierDeviceService userTierDeviceService;
    @Mock
    private OrderService orderService;

    private static final String TENANT_LONGSE = "longse";
    private static final String TENANT_IKASA = "ikasa";

    private static final List<String> tenantIds = Arrays.asList(TENANT_LONGSE, TENANT_IKASA);

    @Before
    public void before() {
        when(paasVipConfig.tenantIsSupportDeviceVip(TENANT_LONGSE)).thenReturn(true);
        when(paasVipConfig.tenantIsSupportPaasUserVip(TENANT_LONGSE)).thenReturn(false);
        when(paasVipConfig.tenantIsSupportPaasVip(TENANT_LONGSE)).thenReturn(true);
        when(paasVipConfig.tenantIsSupportDeviceVip(TENANT_IKASA)).thenReturn(false);
        when(paasVipConfig.tenantIsSupportPaasUserVip(TENANT_IKASA)).thenReturn(true);
        when(paasVipConfig.tenantIsSupportPaasVip(TENANT_IKASA)).thenReturn(true);
    }

    @After
    public void after() {

    }

    @Test
    public void test_queryLastDeviceVip() {
        for (String tenantId : tenantIds) {
            String sn = OpenApiUtil.shortUUID();
            DeviceVipLog expectResult = new DeviceVipLog();
            when(paasVipConfig.tenantIsSupportDeviceVip(tenantId)).thenReturn(true);
            when(paasVipConfig.tenantIsSupportPaasUserVip(tenantId)).thenReturn(false);
            when(deviceVipService.queryLastDeviceVip(tenantId, sn)).thenReturn(expectResult);
            Assert.assertEquals(expectResult, paasVipService.queryLastDeviceVip(tenantId, sn));
        }
    }

    @Test
    public void test_querySn2VipLevel() {
        int userId = TestUtil.randInt();
        {
            when(userVipService.isVipUser(userId)).thenReturn(true);
            Assert.assertEquals(Collections.EMPTY_MAP, paasVipService.querySn2VipLevel(userId));
        }
        when(userVipService.isVipUser(userId)).thenReturn(false);
        {
            when(userService.queryUserById(userId)).thenReturn(null);
            Assert.assertEquals(Collections.EMPTY_MAP, paasVipService.querySn2VipLevel(userId));
        }
        for (String tenantId : tenantIds) {
            User userIkasa = new User() {{
                setId(userId);
                setTenantId(tenantId);
            }};
            when(paasVipConfig.tenantIsSupportDeviceVip(tenantId)).thenReturn(false);
            when(paasVipConfig.tenantIsSupportPaasUserVip(tenantId)).thenReturn(true);
            when(userService.queryUserById(userId)).thenReturn(userIkasa);
            when(paasUserVipService.querySn2VipLevel(userIkasa)).thenReturn(Collections.singletonMap("key_" + tenantId, 1));
            Assert.assertEquals(Collections.singletonMap("key_" + tenantId, 1), paasVipService.querySn2VipLevel(userId));
        }
    }

    @Test
    public void test_isVipUser() {

    }

    @Test
    public void test_isVipDevice() {

    }

    @Test
    public void test_queryDeviceVipState() {

    }

    @Test
    public void test_queryVipCountType() {

    }

}
