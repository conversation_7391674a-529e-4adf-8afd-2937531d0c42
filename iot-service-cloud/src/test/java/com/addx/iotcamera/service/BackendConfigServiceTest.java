package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.dynamic.BackendConfigItem;
import com.addx.iotcamera.config.StorageAllocateConfig;
import com.addx.iotcamera.dao.BackendConfigDAO;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class BackendConfigServiceTest {

    @InjectMocks
    private BackendConfigService backendConfigService;
    @Mock
    private BackendConfigDAO backendConfigDAO;

    @Test
    public void test_queryStorageAllocateConfig() {
        {
            when(backendConfigDAO.queryByGroup(anyString())).thenReturn(Arrays.asList());
            final StorageAllocateConfig config = backendConfigService.queryStorageAllocateConfig();
            Assert.assertNull(config);
        }
        {
            final List<BackendConfigItem> allItems = Arrays.asList(
                    new BackendConfigItem().setKey(StorageAllocateConfig.VIP_RATIOS)
                            .setValue("{\"min\":0,\"max\":100,\"serviceNames\":[\"s3\"]}"),
                    new BackendConfigItem().setKey(StorageAllocateConfig.NO_VIP_RATIOS)
                            .setValue("{\"min\":0,\"max\":50,\"serviceNames\":[\"cos\",\"s3\"]}"),
                    new BackendConfigItem().setKey(StorageAllocateConfig.NO_VIP_RATIOS)
                            .setValue("{\"min\":50,\"max\":100,\"serviceNames\":[\"s3\"]}"),
                    new BackendConfigItem().setKey(StorageAllocateConfig.WHITE_USER_IDS + "cos")
                            .setValue("[123,456]"),
                    new BackendConfigItem().setKey(StorageAllocateConfig.WHITE_USER_IDS + "s3")
                            .setValue("[789]"),
                    new BackendConfigItem().setKey(StorageAllocateConfig.SAAS_AI_SUPPORT_SERVICE_NAMES)
                            .setValue("[\"s3\"]")
            );
            final StorageAllocateConfig expectConfig = new StorageAllocateConfig()
                    .setVipRatios(Arrays.asList(
                            new StorageAllocateConfig.RatioItem().setMin(0).setMax(100).setServiceNames(Arrays.asList("s3"))
                    ))
                    .setNoVipRatios(Arrays.asList(
                            new StorageAllocateConfig.RatioItem().setMin(0).setMax(50).setServiceNames(Arrays.asList("cos", "s3")),
                            new StorageAllocateConfig.RatioItem().setMin(50).setMax(100).setServiceNames(Arrays.asList("s3"))
                    ))
                    .setWhiteUserIds(ImmutableMap.of(
                            "cos", new LinkedHashSet<>(Arrays.asList(123, 456))
                            , "s3", new LinkedHashSet<>(Arrays.asList(789)))
                    )
                    .setSaasAiSupportServiceNames(new LinkedHashSet<>(Arrays.asList("s3")));

            when(backendConfigDAO.queryByGroup(anyString())).thenReturn(allItems);
            final StorageAllocateConfig config = backendConfigService.queryStorageAllocateConfig();
            Assert.assertNotNull(config);
            Assert.assertEquals(expectConfig, config);
        }

    }
}
