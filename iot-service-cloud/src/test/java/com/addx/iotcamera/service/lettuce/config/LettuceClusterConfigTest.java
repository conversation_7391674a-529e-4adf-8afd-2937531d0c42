package com.addx.iotcamera.service.lettuce.config;

import com.addx.iotcamera.service.lettuce.properties.LettuceConfigProperties;
import io.lettuce.core.RedisURI;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.async.RedisAdvancedClusterAsyncCommands;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import io.lettuce.core.resource.ClientResources;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LettuceClusterConfigTest {

    @InjectMocks
    LettuceClusterConfig lettuceClusterConfig;
    @Mock
    LettuceConfigProperties lettuceConfigProperties;
    @Mock
    LettuceConfigProperties.RedisConnConf redisConnConf;
    @Mock
    StatefulRedisClusterConnection<String, String> connection;
    @Mock
    RedisClusterClient redisClusterClient;
    @Mock
    RedisAdvancedClusterCommands<String, String> syncCommands;
    @Mock
    RedisAdvancedClusterAsyncCommands<String, String> asyncCommands;

    ClientResources clientResources;
    RedisURI redisUri;


    @Before
    public void before() {
        lettuceClusterConfig.lettuceConfigProperties = lettuceConfigProperties;

        when(lettuceConfigProperties.getMaxActive()).thenReturn(1);

        when(redisConnConf.getHost()).thenReturn("127.0.0.1");
        when(redisConnConf.getPort()).thenReturn(6379);
        when(redisConnConf.getMaxRedirect()).thenReturn(5);
        when(redisConnConf.getConnectTimeout()).thenReturn(500);
        when(redisConnConf.getDefaultCommandTimeout()).thenReturn(500);
        when(redisConnConf.getMetaCommandTimeout()).thenReturn(500);
        when(redisConnConf.getShutdownTimeout()).thenReturn(500);
        when(lettuceConfigProperties.getBusinessRedisCluster()).thenReturn(redisConnConf);

        when(connection.sync()).thenReturn(syncCommands);
        when(connection.async()).thenReturn(asyncCommands);

        when(redisClusterClient.connect(lettuceClusterConfig.redisCodec())).thenReturn(connection);
        clientResources = lettuceClusterConfig.clientResources(lettuceConfigProperties);
        redisUri = lettuceClusterConfig.getRedisUri();
    }


    @Test
    public void redisClusterClient() {
        lettuceClusterConfig.redisClusterClient();
    }

    @Test
    public void businessLettuceSyncClient() {
        lettuceClusterConfig.businessLettuceSyncClient(redisClusterClient);
    }

    @Test
    public void businessLettuceAsyncClient() {
        lettuceClusterConfig.businessLettuceAsyncClient(redisClusterClient);
    }

    @Test
    public void redisCodec() {
        lettuceClusterConfig.redisCodec();
    }
}