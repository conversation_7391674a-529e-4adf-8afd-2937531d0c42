package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.param.CosCredentials;
import com.addx.iotcamera.bean.param.CosParams;
import com.addx.iotcamera.config.CosConfig;
import com.addx.iotcamera.constants.VideoConstants;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.BeanUtil;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.addx.iotcamera.service.CosService.COS_BEIJING_DOMAIN_PATTERN;


@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class CosServiceTest {


    private TestHelper testHelper;
    private CosService cosService;

    public CosService createCosService(TestHelper testHelper) {
        final CosConfig cosConfig = testHelper.getConfig().getObject("cos", CosConfig.class);
        cosConfig.getParams()
                .setAllowActions(new String[]{
                        "name/cos:PutObject", // 简单上传
                        "name/cos:GetBucket", // 查询对象列表
                        "name/cos:GetObject", // 下载对象
                        "name/cos:CopyObject", // 复制对象
                })
                .setListObjectMaxKeys(2);
        CosService cosService = new CosService().setConfig(cosConfig);
        cosService.init();
        return cosService;
    }

    @Before
    public void before() {
        testHelper = TestHelper.getInstanceByEnv("local");
        this.cosService = createCosService(testHelper);
    }

    @After
    public void after() {
        this.cosService.close();
    }

    @DisplayName("所有环境的cos参数和api都测一遍")  // 平常不需要运行，注释掉
//    @Test
    public void testAllEnv() {
        for (final String env : Arrays.asList("staging", "pre", "prod")) {
            TestHelper testHelper = TestHelper.getInstanceByEnv(env);
            final CosService cosService = createCosService(testHelper);
            for (final String bucket : cosService.getConfig().getLookBackDays2Bucket().values()) {
                test_cos_api(cosService, bucket);
            }
            String sn = "cos_test_" + OpenApiUtil.shortUUID();
            final CosCredentials tempCredential = cosService.createTempCredential(sn);
            test_cos_api_tempCredential(cosService, tempCredential);
            cosService.close();
        }
    }

    @DisplayName("使用永久秘钥调用cos-api")
    @Test
    public void test_cos_api_server() {
        for (final String bucket : cosService.getConfig().getLookBackDays2Bucket().values()) {
            test_cos_api(cosService, bucket);
        }
    }

    @DisplayName("使用临时秘钥调用cos-api")
    @Test
    public void test_cos_api_tempCredential() {
        String sn = "cos_test_" + OpenApiUtil.shortUUID();
        final CosCredentials tempCredential = cosService.createTempCredential(sn);
        test_cos_api_tempCredential(cosService, tempCredential);
    }

    public static void test_cos_api_tempCredential(CosService cosService, CosCredentials tempCredential) {
        final CosParams tempParams = new CosParams();
        BeanUtil.copy(cosService.getConfig().getParams(), tempParams);
        BeanUtil.copy(tempCredential, tempCredential);
        final CosService tempCosService = new CosService();
        tempCosService.setConfig(new CosConfig().setParams(tempParams).setEnable(true)
                .setBucket(cosService.getConfig().getBucket())
                .setLookBackDays2Bucket(cosService.getConfig().getLookBackDays2Bucket())
                .setLookBackDays2Bucket(cosService.getConfig().getLookBackDays2Bucket())
        );
        tempCosService.init();

        for (final String bucket : cosService.getConfig().getLookBackDays2Bucket().values()) {
            test_cos_api(tempCosService, bucket);
        }
    }

    public static void test_cos_api(CosService cosService, String bucket) {
        String keyPrefix = "test/";
        final List<String> keys = IntStream.range(0, 3).mapToObj(it -> keyPrefix + it).collect(Collectors.toList());
        String content = UUID.randomUUID().toString();
        // putObject
        for (final String key : keys) {
            cosService.putObject(bucket, key, content);
        }
        // listObject
        final List<String> keys2 = cosService.listObject(bucket, keyPrefix);
        Assert.assertTrue(keys2.containsAll(keys));
        // getObject
        for (final String key : keys) {
            final String content2 = cosService.getObject(bucket, key);
            Assert.assertEquals(content, content2);
            // copyObject
            final String desKey = VideoConstants.VIDEO_BACKUP_PREFIX + "/" + key;
            cosService.copyObject(bucket, key, cosService.getConfig().getBucket(), desKey);
        }
        // generatePresignedUrl
        for (final String key : keys) {
            final URL url = cosService.getObjectUrl(bucket, key);
            final URL presignedUrl = cosService.preSignUrl(url.toString(), TimeUnit.MINUTES.toMillis(2880));
            try {
                final String content2 = IOUtils.toString(presignedUrl, StandardCharsets.UTF_8.displayName());
                Assert.assertEquals(content, content2);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Test
    public void test_parseCosUrl() {
        {
            final CosService.CosUrlParts cosUrlParts = CosService.parseCosUrl("https://addx-test-1302606863.cos.ap-beijing.myqcloud.com/test/2");
            Assert.assertNotNull(cosUrlParts);
            Assert.assertEquals("addx-test-1302606863", cosUrlParts.getBucket());
            Assert.assertEquals("ap-beijing", cosUrlParts.getRegion());
            Assert.assertEquals("test/2", cosUrlParts.getKey());
        }
        {
            final CosService.CosUrlParts cosUrlParts = CosService.parseCosUrl("https://storage.googleapis.com/download/storage/v1/b/test-a4x/o/device_video_slice%2Fsn1234%2FtraceId123444%2Fslice_2000_1_0.ts");
            Assert.assertNull(cosUrlParts);
        }
        {
            final CosService.CosUrlParts cosUrlParts = CosService.parseCosUrl("https://addx-device-config.s3.amazonaws.com/battery/2021/1629894634_CW2007_BK5100.txt");
            Assert.assertNull(cosUrlParts);
        }
    }

//    @Test
//    public void test_getObjectUrl() {
//        {
//            final URL url = cosService.getObjectUrl(bucket1, "/");
//            Assert.assertEquals("https://addx-test-1302606863.cos.ap-beijing.myqcloud.com/", url.toString());
//        }
//        {
//            final URL url = cosService.getObjectUrl(bucket1, "");
//            Assert.assertEquals("https://addx-test-1302606863.cos.ap-beijing.myqcloud.com/", url.toString());
//        }
//    }

    @Test
    public void test_FuncUtil_copyArray() {
        Assert.assertArrayEquals(new String[]{}, FuncUtil.copyArray(new String[]{}));
        Assert.assertArrayEquals(new String[]{"1", "2"}, FuncUtil.copyArray(new String[]{"1", "2"}));
        Assert.assertArrayEquals(null, FuncUtil.copyArray(null));
    }

    @Test
    public void test_charset_utf8() {
        final Charset charset = StandardCharsets.UTF_8;
        Assert.assertEquals(charset, Charset.forName(charset.displayName()));
        Assert.assertEquals(charset, Charset.forName(charset.name()));
        Assert.assertEquals(charset, Charset.forName("utf8"));
        Assert.assertEquals(charset, Charset.forName("UTF-8"));
        Assert.assertEquals(charset, Charset.forName("UTF8"));
        Assert.assertEquals(charset, Charset.forName("Utf8"));
        Assert.assertEquals(charset, Charset.forName("UTf8"));
        Assert.assertEquals(charset, Charset.forName("uTf8"));
    }

    @Test
    public void test_init_close() {
        final CosService cosService = new CosService();
        cosService.setConfig(new CosConfig().setEnable(false));
        cosService.init();
        cosService.close();
    }

//    @Test
    public void test_online() {
        final String cosCredentialJson = "{\"secretKey\":\"jxInpiOVryThGfqQQg9lPwBErdKy5+BJu5IUGbpt1YI=\",\"expirationSeconds\":**********,\"sessionToken\":\"lRC8jGwbvPDBYqwmqfTc43g0Tow04vBa9093894b08b99abf5d326f3c32434be5VRwabPMa70bRS2FfMQuyMjZikFvA1PEoxUy7itNdC7mqR_YAM9WxSKG-FFaO7a1bXyDWv5IURdtWn1LXYmk-rdJxmcNuyfipDJyR24Il_E9O5GrZwoTU4bLkShlO2rqYAj5BHbSjZTkeJfTFZE4MWL7rb0OJGIi13tBuqp6IcYWJJ2GeB5ETCUyp-QHGj058AsPPfXjjf9Wd_UxCEDnH3CDr6WKrAePOLLFCDHrqFljbn9bZVViJ21290FCxEFT1IZuksPJBvIcZdXQW_09qK75lSnp0FYT2MfI3v-uDfIA7pUILXyuUZtdCHZjNSPE-4HiRaT6c8sSegoQyw7V4QwfW05PXE6DWz_1SDt57a4FaKDrdjkQtUty5p0IrC1e2oytWp1fTLVHpRsT5FFu4pVOgwhN_qqqebGnaWtd0rNlENfIn6938ZiJ0EjuxZJINwbERNeG1aHo2O_O37CbrqzDf3Whiedi80nS2He8WDyMYigkUerXnP6FNEBbL2IQlNE_Aha0wQ9UHDw966cm5faTFBnHyqbfqG-zaYQZ0bTw0RifS_ZVdP3l_CTwuqLrM2uW4XcVjfFPE4OghK7VfOkRwAxMMeG0UkAcYt1Ov9Oe7Uu7inKWymCv8MLNtjjbApC6XRe184qdpy-2xHpiiMh86TQ27UJdWj3-ZK5BPNZYtKCAofw4ZHkRF6-o6Y72cIg4qEq96m4khw3-Nych-d1FJ31ZbJf9HBl1AMqJnomNbYxZi2jJFsjOWrv5wiMhKyfTB9sX55rAJ3R-x_urP7PRueavjUmp0pDC9sogg54MiSxVQE5QczNegK7IgT7I--HONnTqqx81c4VkBJ4-ToJ8LpqRtXeeesHhaFd6MwJVavt2zOwLZwv5mZ-n9HC-y7NvF8ADnn1iEIc20BB4boM8mPG76uyk8vNV3_dMCvVyD7eYxGaxPkLqSTk_nb9EDCAWrGgD2F7Q6rYvIvHmTlUuZDNE4MQFe8RAm4xFZRNfJJJzSe00zXvqgyS3UMlmI\",\"secretId\":\"AKIDfpPK-5Zq2QgzEDMSGLJCx09JZS563sp5MmqyBGKNdgPAThR8vRKcgb9gR-qwC33L\"}";
        final CosCredentials cosCredentials = JSON.parseObject(cosCredentialJson, CosCredentials.class);

        TestHelper testHelper = TestHelper.getInstanceByEnv("prod");
        final CosService cosService = createCosService(testHelper);

        test_cos_api_tempCredential(cosService, cosCredentials);
    }

    @After
    public void afterTests() {
        try {
            cosService.close();
        } catch (Exception e) {
        }
    }

    @Test
    public void test_preSignUrlV2() {
        final TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        final CosService cosService = createCosService(testHelper);

//        final String url = "https://staging-cn-vip-7d-1302606863.cos.ap-beijing.myqcloud.com/device_video_slice/995a6ae04f85c732b1b7956ee0e37027/0193631699144483S26pPDlZa30L1/image.jpg";
        final String url = "https://staging-cn-vip-7d-1302606863.cos.ap-beijing.myqcloud.com/device_video_slice/cd1a7a93c31986e36d0c3522269c853b/0193651698798690S3gTFUGqGTSdy/image.jpg";
        final long expiredMills = TimeUnit.MINUTES.toMillis(2880);

        final URL signUrl1 = cosService.preSignUrl(url, expiredMills);
        log.info("signUrl1:{}", signUrl1);

        final URL signUrl2 = cosService.preSignUrlV2(url, expiredMills);
        log.info("signUrl2:{}", signUrl2);

        cosService.getConfig().setDomainPattern(COS_BEIJING_DOMAIN_PATTERN);
        final URL signUrl3 = cosService.preSignUrlV2(url, expiredMills);
        log.info("signUrl3:{}", signUrl3);
    }

}
