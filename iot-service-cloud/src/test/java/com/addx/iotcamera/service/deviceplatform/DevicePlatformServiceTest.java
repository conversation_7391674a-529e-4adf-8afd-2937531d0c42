package com.addx.iotcamera.service.deviceplatform;

import com.addx.iotcamera.bean.deviceplatform.PlatformCamera;
import com.addx.iotcamera.bean.deviceplatform.PlatformDevice;
import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.deviceplatform.PlatformLinkedDeviceMapDo;
import com.addx.iotcamera.bean.domain.deviceplatform.PlatformSupportedDeviceMapDo;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.device.DeviceServiceTest;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.deviceplatform.alexa.AlexaService;
import com.addx.iotcamera.service.deviceplatform.googlehome.GoogleHomeService;
import com.addx.iotcamera.util.DeviceCodecUtil;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.collections.MapUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class DevicePlatformServiceTest {

    @Mock
    private DeviceInfoService mockDeviceInfoService;
    @Mock
    private AlexaService mockAlexaService;
    @Mock
    private GoogleHomeService mockGoogleHomeService;
    @Mock
    private DeviceSettingService mockDeviceSettingService;

    @InjectMocks
    private DevicePlatformService devicePlatformServiceUnderTest;

    @Test
    public void testQueryEmptyPlatformLinkedDeviceMapDo() {
        // Setup
        final PlatformLinkedDeviceMapDo expectedResult = new PlatformLinkedDeviceMapDo(new HashMap<>(), new HashMap<>(), Arrays.asList(new HashMap<String, Object>() {{
            put("value", false);
            put("key", "alexa");
        }}, new HashMap<String, Object>() {{
            put("value", false);
            put("key", "googlehome");
        }}));

        // Configure DeviceInfoService.listDevicesByUserId(...).
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(new CloudDeviceSupport(){{
            setSupportChangeCodec(1);
            setSupportAlexaWebrtc(1);
        }});
        when(mockDeviceInfoService.listDevicesByUserId(anyInt(), any(), anyInt())).thenReturn(deviceDOList);

        when(mockAlexaService.canSnSupportUseCache("sn", "userSn")).thenReturn(false);
        when(mockAlexaService.isUserLinked(any(), anyInt())).thenReturn(false);
        when(mockAlexaService.canAccessAlexaUseCache(any())).thenReturn(false);

        when(mockDeviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO() {{
            setDefaultCodec("defaultCodec");
        }});

        when(mockAlexaService.canFirmwareSupport(any())).thenReturn(false);
        when(mockGoogleHomeService.canSnSupportUseCache("sn", "userSn")).thenReturn(false);
        when(mockGoogleHomeService.isUserLinked(any(), anyInt())).thenReturn(false);
        when(mockGoogleHomeService.canAccessGoogleHome(any())).thenReturn(false);
        when(mockGoogleHomeService.canFirmwareSupport(any())).thenReturn(false);
        // Run the test
        final PlatformLinkedDeviceMapDo result = devicePlatformServiceUnderTest.queryPlatformLinkedDeviceMapDo(null, 0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryPlatformLinkedDeviceMapDo() {
        // Configure DeviceInfoService.listDevicesByUserId(...).
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(new CloudDeviceSupport(){{
            setSupportChangeCodec(1);
            setSupportAlexaWebrtc(1);
        }});
        when(mockDeviceInfoService.listDevicesByUserId(anyInt(), any(), anyInt())).thenReturn(deviceDOList);

        when(mockAlexaService.canSnSupportUseCache("sn", "userSn")).thenReturn(false);
        when(mockAlexaService.isUserLinked(any(), anyInt())).thenReturn(true);
        when(mockAlexaService.canAccessAlexaUseCache(any())).thenReturn(true);
        when(mockDeviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO() {{
            setDefaultCodec("defaultCodec");
        }});

        when(mockAlexaService.canFirmwareSupport(any())).thenReturn(true);
        when(mockGoogleHomeService.canSnSupportUseCache("sn", "userSn")).thenReturn(true);
        when(mockGoogleHomeService.isUserLinked(any(), anyInt())).thenReturn(true);
        when(mockGoogleHomeService.canAccessGoogleHome(any())).thenReturn(true);
        when(mockGoogleHomeService.canFirmwareSupport(any())).thenReturn(true);

        // Run the test
        final PlatformLinkedDeviceMapDo result = devicePlatformServiceUnderTest.queryPlatformLinkedDeviceMapDo(null, 0);

        // Verify the results
        assertTrue(MapUtils.isNotEmpty(result.getPlatformLinkedDevicesMap()));
    }

    @Test
    public void testQueryPlatformLinkedDeviceMapDo_DeviceInfoServiceReturnsNoItems() {
        // Setup
        final PlatformLinkedDeviceMapDo expectedResult = new PlatformLinkedDeviceMapDo(new HashMap<>(), new HashMap<>(), Arrays.asList(new HashMap<String, Object>() {{
            put("value", false);
            put("key", "alexa");
        }}, new HashMap<String, Object>() {{
            put("value", false);
            put("key", "googlehome");
        }}));
        when(mockDeviceInfoService.listDevicesByUserId(anyInt(), any(), anyInt())).thenReturn(Collections.emptyList());
        when(mockAlexaService.canSnSupportUseCache("sn", "userSn")).thenReturn(false);
        when(mockAlexaService.isUserLinkedUseCache(any(), anyInt())).thenReturn(false);
        when(mockAlexaService.canAccessAlexaUseCache(any())).thenReturn(false);
        when(mockDeviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO() {{
            setDefaultCodec("defaultCodec");
        }});

        when(mockAlexaService.canFirmwareSupport(any())).thenReturn(false);
        when(mockGoogleHomeService.canSnSupportUseCache("sn", "userSn")).thenReturn(false);
        when(mockGoogleHomeService.isUserLinkedUseCache(any(), anyInt())).thenReturn(false);
        when(mockGoogleHomeService.canAccessGoogleHome(any())).thenReturn(false);
        when(mockGoogleHomeService.canFirmwareSupport(any())).thenReturn(false);
        // Run the test
        final PlatformLinkedDeviceMapDo result = devicePlatformServiceUnderTest.queryPlatformLinkedDeviceMapDo(null, 0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryEmptyPlatformSupportedDeviceMapDo() {
        // Setup
        final PlatformSupportedDeviceMapDo expectedResult = new PlatformSupportedDeviceMapDo(new HashMap<String, List<PlatformDevice>>() {{
            put("googlehome", Collections.emptyList());
            put("alexa", Collections.emptyList());
        }});

        // Configure DeviceInfoService.listDevicesByUserId(...).
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(new CloudDeviceSupport(){{
            setSupportChangeCodec(1);
            setSupportAlexaWebrtc(1);
        }});
        when(mockDeviceInfoService.listDevicesByUserId(anyInt(), any(), anyInt())).thenReturn(deviceDOList);

        when(mockAlexaService.canAccessAlexaUseCache(any())).thenReturn(false);
        when(mockGoogleHomeService.canAccessGoogleHomeUseCache(any())).thenReturn(false);
        // Run the test
        final PlatformSupportedDeviceMapDo result = devicePlatformServiceUnderTest.queryPlatformSupportedDeviceMapDo(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryPlatformSupportedDeviceMapDo() {
        // Configure DeviceInfoService.listDevicesByUserId(...).
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(new CloudDeviceSupport(){{
            setSupportChangeCodec(1);
            setSupportAlexaWebrtc(1);
        }});
        when(mockDeviceInfoService.listDevicesByUserId(anyInt(), any(), anyInt())).thenReturn(deviceDOList);

        when(mockAlexaService.canAccessAlexaUseCache(any())).thenReturn(true);
        when(mockGoogleHomeService.canAccessGoogleHomeUseCache(any())).thenReturn(true);

        // Run the test
        final PlatformSupportedDeviceMapDo result = devicePlatformServiceUnderTest.queryPlatformSupportedDeviceMapDo(0);

        // Verify the results
        assertTrue(MapUtils.isNotEmpty(result.getPlatformSupportedDevicesMap()));
    }

    @Test
    public void testQueryPlatformSupportedDeviceMapDo_DeviceInfoServiceReturnsNoItems() {
        // Setup
        final PlatformSupportedDeviceMapDo expectedResult = new PlatformSupportedDeviceMapDo(new HashMap<String, List<PlatformDevice>>() {{
            put("googlehome", Collections.emptyList());
            put("alexa", Collections.emptyList());
        }});
        when(mockDeviceInfoService.listDevicesByUserId(anyInt(), any(), anyInt())).thenReturn(Collections.emptyList());
        when(mockAlexaService.canAccessAlexaUseCache(any())).thenReturn(false);
        when(mockGoogleHomeService.canAccessGoogleHomeUseCache(any())).thenReturn(false);
        // Run the test
        final PlatformSupportedDeviceMapDo result = devicePlatformServiceUnderTest.queryPlatformSupportedDeviceMapDo(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFilterAlexaSupportedDevices() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        final Map<String, List<PlatformDevice>> platformSupportedDeviceInfoMap = new HashMap<>();
        when(mockAlexaService.canAccessAlexaUseCache(any())).thenReturn(false);

        // Run the test
        devicePlatformServiceUnderTest.filterAlexaSupportedDevices(deviceDOList, platformSupportedDeviceInfoMap);

        // Verify the results
        assertTrue(platformSupportedDeviceInfoMap.size() == 1);
    }

    @Test
    public void testFilterAlexaLinkedDevices() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        final Map<String, List<PlatformDevice>> platformLinkedDeviceInfoMap = new HashMap<>();
        final Map<String, List<PlatformDevice>> firmwareNotSupportDeviceInfoMap = new HashMap<>();
        final List<Map> platformDisplayControlList = new LinkedList<Map>() {{
            add(new HashMap<>());
        }};
        when(mockAlexaService.canSnSupportUseCache("serialNumber", "userSn")).thenReturn(false);
        when(mockAlexaService.isUserLinked(any(), anyInt())).thenReturn(false);
        when(mockAlexaService.canAccessAlexaUseCache(any())).thenReturn(false);

        when(mockDeviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO() {{
            setDefaultCodec("defaultCodec");
        }});

        when(mockAlexaService.canFirmwareSupport(any())).thenReturn(false);

        // Run the test
        devicePlatformServiceUnderTest.filterAlexaLinkedDevices(null, 0, deviceDOList, platformLinkedDeviceInfoMap, firmwareNotSupportDeviceInfoMap, platformDisplayControlList);
        assertTrue(MapUtils.isEmpty(platformLinkedDeviceInfoMap));

        when(mockAlexaService.isUserLinked(null, 0)).thenReturn(true);
        when(mockAlexaService.canAccessAlexaUseCache(any())).thenReturn(true);
        deviceDOList.get(0).setCodec("h265");
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportAlexaWebrtc(1)
                .supportChangeCodec(1)
                .build());
        when(mockAlexaService.canFirmwareSupport(any())).thenReturn(false);
        when(mockAlexaService.canSnSupportUseCache(any(), any())).thenReturn(true);
        devicePlatformServiceUnderTest.filterAlexaLinkedDevices(null, 0, deviceDOList, platformLinkedDeviceInfoMap, firmwareNotSupportDeviceInfoMap, platformDisplayControlList);
        assertTrue(MapUtils.isNotEmpty(platformLinkedDeviceInfoMap));
    }

    @Test
    public void testFilterGoogleHomeSupportedDevices() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportAlexaWebrtc(1)
                .supportChangeCodec(1)
                .build());
        final Map<String, List<PlatformDevice>> platformSupportedDeviceInfoMap = new HashMap<>();
        when(mockGoogleHomeService.canAccessGoogleHomeUseCache(any())).thenReturn(false);
        // Run the test
        devicePlatformServiceUnderTest.filterGoogleHomeSupportedDevices(deviceDOList, platformSupportedDeviceInfoMap);
        assertTrue(MapUtils.isNotEmpty(platformSupportedDeviceInfoMap));
    }

    @Test
    public void testFilterGoogleHomeLinkedDevices() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportAlexaWebrtc(1)
                .supportChangeCodec(1)
                .build());
        final Map<String, List<PlatformDevice>> platformLinkedDeviceInfoMap = new HashMap<>();
        final Map<String, List<PlatformDevice>> firmwareNotSupportDeviceInfoMap = new HashMap<>();
        final List<Map> platformDisplayControlList = new LinkedList<Map>() {{
            add(new HashMap<>());
        }};
        when(mockGoogleHomeService.canSnSupportUseCache("sn", "userSn")).thenReturn(false);
        when(mockGoogleHomeService.isUserLinked(any(), anyInt())).thenReturn(false);
        when(mockGoogleHomeService.canAccessGoogleHome(any())).thenReturn(false);
        when(mockDeviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO() {{
            setDefaultCodec("defaultCodec");
        }});

        when(mockGoogleHomeService.canFirmwareSupport(any())).thenReturn(false);

        // Run the test
        devicePlatformServiceUnderTest.filterGoogleHomeLinkedDevices(null, 0, deviceDOList, platformLinkedDeviceInfoMap, firmwareNotSupportDeviceInfoMap, platformDisplayControlList);
        assertTrue(MapUtils.isEmpty(platformLinkedDeviceInfoMap));

        when(mockGoogleHomeService.isUserLinked(any(), anyInt())).thenReturn(true);
        when(mockGoogleHomeService.canAccessGoogleHomeUseCache(any())).thenReturn(true);
        deviceDOList.get(0).setCodec("h265");
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportAlexaWebrtc(1)
                .supportChangeCodec(1)
                .build());
        when(mockGoogleHomeService.canFirmwareSupport(any())).thenReturn(false);
        when(mockGoogleHomeService.canSnSupportUseCache(any(), any())).thenReturn(true);
        devicePlatformServiceUnderTest.filterGoogleHomeLinkedDevices(null, 0, deviceDOList, platformLinkedDeviceInfoMap, firmwareNotSupportDeviceInfoMap, platformDisplayControlList);
        assertTrue(MapUtils.isNotEmpty(platformLinkedDeviceInfoMap));
    }

    @Test
    public void test_getPlatformDeviceFromDeviceDO() {
        PlatformDevice platformDevice = devicePlatformServiceUnderTest.getPlatformDeviceFromDeviceDO(DeviceDO.builder().serialNumber("sn_01").codec(DeviceCodecUtil.H265).build());
        assertTrue(platformDevice instanceof PlatformCamera && !((PlatformCamera) platformDevice).getCodecSupported() && !((PlatformCamera) platformDevice).getShowCodecChange());

        platformDevice = devicePlatformServiceUnderTest.getPlatformDeviceFromDeviceDO(DeviceDO.builder().serialNumber("sn_01").codec(DeviceCodecUtil.H265).deviceSupport(CloudDeviceSupport.builder().build()).build());
        assertTrue(platformDevice instanceof PlatformCamera && !((PlatformCamera) platformDevice).getCodecSupported() && !((PlatformCamera) platformDevice).getShowCodecChange());

        platformDevice = devicePlatformServiceUnderTest.getPlatformDeviceFromDeviceDO(DeviceDO.builder().serialNumber("sn_01").codec(DeviceCodecUtil.H265).deviceSupport(CloudDeviceSupport.builder().supportChangeCodec(0).build()).build());
        assertTrue(platformDevice instanceof PlatformCamera && !((PlatformCamera) platformDevice).getCodecSupported() && !((PlatformCamera) platformDevice).getShowCodecChange());

        when(mockDeviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO() {{
            setDefaultCodec(DeviceCodecUtil.H264);
        }});
        platformDevice = devicePlatformServiceUnderTest.getPlatformDeviceFromDeviceDO(DeviceDO.builder().serialNumber("sn_01").codec(DeviceCodecUtil.H265).deviceSupport(CloudDeviceSupport.builder().supportChangeCodec(1).build()).build());
        assertTrue(platformDevice instanceof PlatformCamera && !((PlatformCamera) platformDevice).getCodecSupported() && !((PlatformCamera) platformDevice).getShowCodecChange());

        when(mockDeviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO() {{
            setDefaultCodec(null);
        }});
        platformDevice = devicePlatformServiceUnderTest.getPlatformDeviceFromDeviceDO(DeviceDO.builder().serialNumber("sn_01").codec(DeviceCodecUtil.H265).deviceSupport(CloudDeviceSupport.builder().supportChangeCodec(1).build()).build());
        assertTrue(platformDevice instanceof PlatformCamera && !((PlatformCamera) platformDevice).getCodecSupported() && ((PlatformCamera) platformDevice).getShowCodecChange());

        when(mockDeviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO() {{
            setDefaultCodec(DeviceCodecUtil.H265);
        }});
        platformDevice = devicePlatformServiceUnderTest.getPlatformDeviceFromDeviceDO(DeviceDO.builder().serialNumber("sn_01").codec(DeviceCodecUtil.H265).deviceSupport(CloudDeviceSupport.builder().supportChangeCodec(1).build()).build());
        assertTrue(platformDevice instanceof PlatformCamera && !((PlatformCamera) platformDevice).getCodecSupported() && ((PlatformCamera) platformDevice).getShowCodecChange());

        platformDevice = devicePlatformServiceUnderTest.getPlatformDeviceFromDeviceDO(DeviceDO.builder().serialNumber("sn_01").codec(DeviceCodecUtil.H264).build());
        assertTrue(platformDevice instanceof PlatformCamera && ((PlatformCamera) platformDevice).getCodecSupported() && !((PlatformCamera) platformDevice).getShowCodecChange());
    }
}
