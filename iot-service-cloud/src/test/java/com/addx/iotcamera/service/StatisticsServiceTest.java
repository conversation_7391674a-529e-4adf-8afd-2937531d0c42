package com.addx.iotcamera.service;

import com.addx.iotcamera.config.EsConfig;
import com.addx.iotcamera.config.S3;
import com.addx.iotcamera.helper.JwtHelper;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.LinkedHashMap;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class StatisticsServiceTest {

    @Mock
    private EsService esService;
    @Mock
    private S3Service s3Service;
    @Mock
    private S3 s3;
    @Mock
    private EsConfig esConfig;
    @InjectMocks
    private StatisticsService statisticsService;

    @Before
    public void before() {
        final EsService esService = new EsService();
        final EsConfig esConfig = new EsConfig();
        esConfig.setEndpoints(Arrays.asList("https://elasticsearch-cn.addx.live"));
        esService.setEsConfig(esConfig);
//        when(this.esService.searchByTimestampDesc(anyString(), any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(esService));
//        when(this.esService.searchByTimestampDescV2(anyString(), any(), any())).thenAnswer(AdditionalAnswers.delegatesTo(esService));
        when(this.esService.searchByTimestampDesc(anyString(), any())).thenAnswer(AdditionalAnswers.delegatesTo(esService));
    }

    public static String createStatisticsDownloadUrl(String rootUrl) {
        LinkedHashMap<String, Object> claims = new LinkedHashMap<>();
        claims.put("keyPrefix", "video_backup/statistics/");
        String token = new JwtHelper().createToken(claims, 3600 * 24); // 1天有效
        return rootUrl + "/workTemp/downloadQuestionBackVideo?token=" + token;
    }

//    @Test
    public void test_sliceUploadSuccessRate() {
        final JSONObject data = statisticsService.sliceUploadSuccessRate("k8s-staging-cn-iot-service-2022.11.16");
        log.info("test_sliceUploadSuccessRate:{}", data);
    }

    @Test
    public void test_createStatisticsDownloadUrl(){
        String stagingUsRootUrl = "https://api-staging-us.vicohome.io";
        String stagingEuRootUrl = "https://api-staging-eu.vicohome.io";
        String stagingCnRootUrl = "https://api-stage.addx.live";
        log.info("euUrl:{}", createStatisticsDownloadUrl(stagingEuRootUrl));
        log.info("usUrl:{}", createStatisticsDownloadUrl(stagingUsRootUrl));
        log.info("cnUrl:{}", createStatisticsDownloadUrl(stagingCnRootUrl));
    }

}

