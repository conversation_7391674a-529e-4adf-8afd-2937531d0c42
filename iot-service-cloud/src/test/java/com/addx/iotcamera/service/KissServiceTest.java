package com.addx.iotcamera.service;


import com.addx.iotcamera.bean.db.device.DeviceKissFlagDO;
import com.addx.iotcamera.bean.db.device.DeviceServerAllocDO;
import com.addx.iotcamera.bean.device.DeviceCoturnDO;
import com.addx.iotcamera.bean.device_msg.KissDeviceNode;
import com.addx.iotcamera.bean.device_msg.KissDeviceStatus;
import com.addx.iotcamera.config.device.KeepAliveParams;
import com.addx.iotcamera.dao.device.DeviceCoturnDAO;
import com.addx.iotcamera.enums.EProtocol;
import com.addx.iotcamera.kiss.CoturnFinder;
import com.addx.iotcamera.kiss.KissFinder;
import com.addx.iotcamera.kiss.KissNodeType;
import com.addx.iotcamera.kiss.bean.*;
import com.addx.iotcamera.kiss.enums.EHeartbeatReplyMode;
import com.addx.iotcamera.kiss.node.CoturnNode;
import com.addx.iotcamera.kiss.node.KissNode;
import com.addx.iotcamera.kiss.service.IKissAllocationService;
import com.addx.iotcamera.kiss.service.impl.KissServiceImpl;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.device.DeviceServerAllocService;
import com.addx.iotcamera.service.device_msg.DeviceMsgSrcManager;
import com.addx.iotcamera.service.device_msg.KissDeviceNodeManager;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class KissServiceTest {

    @InjectMocks
    private KissServiceImpl kissService;
    @Mock
    private KissWsService kissWsService;
    @Mock
    private DeviceMsgSrcManager deviceMsgSrcManager;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private RedisService redisService;
    @Mock
    private RestTemplate restTemplate;
    @Mock
    private ResponseEntity responseEntity;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private IKissAllocationService kissAllocationService;
    @Mock
    private KissFinder kissFinder;
    @Mock
    private DeviceServerAllocService deviceServerAllocService;
    @Mock
    private CoturnFinder coturnFinder;
    @Mock
    private MqSender mqSender;
    @Mock
    private DeviceCoturnDAO deviceCoturnDAO;
    @Mock
    private KissDeviceNodeManager kissDeviceNodeManager;
    @Mock
    private TenantSettingService tenantSettingService;


    private KissNode kissNode;

    @Before
    public void before() {
        kissNode = new KissNode();
        kissNode.setWebsocketAddr("kiss");
        kissNode.setSignalServerSecret("secret");
        kissNode.setExtranetIp4("127.0.0.1");
        kissFinder.getNodeMapByType(KissNodeType.kiss).put("127.0.0.1", kissNode);

        when(deviceMsgSrcManager.isWsReplaceMqtt(anyString())).thenReturn(false);
        // when(deviceMsgSrcManager.isWsKeepAlive(anyString())).thenReturn(false);
        when(kissDeviceNodeManager.get(anyString())).thenAnswer(it -> {
            return new KissDeviceNode().setKissIp("127.0.0.1").setDeviceSn(it.getArgument(0))
                    .setDeviceStatus(KissDeviceStatus.normal).setLastUpdateTime(System.currentTimeMillis());
        });
        when(restTemplate.postForEntity(anyString(), any(), any())).thenReturn(responseEntity);
        when(responseEntity.getStatusCode()).thenReturn(HttpStatus.OK);
        when(responseEntity.getBody()).thenReturn(JSON.toJSON(new KissApiResult().setCode(0)));
        when(deviceServerAllocService.queryBySn(anyString())).thenAnswer(it -> {
            return new DeviceServerAllocDO().setSn(it.getArgument(0));
        });
    }

    @Test
    public void test_chooseTurnServer_hasDeviceCoturnConfig_whiteList() {
        String sn = OpenApiUtil.shortUUID();
        String countryNo = "cn";
        when(userRoleService.getCountryNoBySerialNumber(sn)).thenReturn(countryNo);
        DeviceCoturnDO coturnNode = new DeviceCoturnDO() {{
            setDomain("p-coturn-1386029440.addx.live");
            setExtranetIp4("*************");
            setType(DeviceCoturnDO.TYPE_WHITE_LIST);
        }};
        when(deviceCoturnDAO.queryEnabledBySn(sn)).thenReturn(ImmutableList.of(coturnNode));
        List<IceServer> iceServers = kissService.chooseTurnServer(sn, sn);
        Assert.assertEquals(1, iceServers.size());
        Assert.assertEquals(coturnNode.getExtranetIp4(), iceServers.get(0).getIpAddress());
        Assert.assertEquals("turn:" + coturnNode.getDomain() + ":5349", iceServers.get(0).getUrl());
        Assert.assertNotNull(iceServers.get(0).getUsername());
        Assert.assertNotNull(iceServers.get(0).getCredential());
    }

    @Test
    public void test_chooseTurnServer_hasDeviceCoturnConfig_blackList() {
        String sn = OpenApiUtil.shortUUID();
        String countryNo = "cn";
        when(userRoleService.getCountryNoBySerialNumber(sn)).thenReturn(countryNo);
        CoturnNode coturnNode = new CoturnNode() {{
            setDomain("p-coturn-1386029440.addx.live");
            setExtranetIp4("*************");
        }};
        when(deviceCoturnDAO.queryEnabledBySn(sn)).thenReturn(ImmutableList.of(new DeviceCoturnDO() {{
            setDomain("p-coturn-1386029440.addx.live222");
            setExtranetIp4("*************");
            setType(DeviceCoturnDO.TYPE_BLACK_LIST);
        }}));
        when(deviceServerAllocService.queryBySn(sn)).thenReturn(new DeviceServerAllocDO().setSn(sn));
        when(coturnFinder.chooseCoturnNodesBySNAndCountryNo(eq(sn), any())).thenReturn(Arrays.asList(
                coturnNode,
                new CoturnNode() {{
                    setDomain("p-coturn-1386029440.addx.live222");
                    setExtranetIp4("*************");
                }}
        ));
        List<IceServer> iceServers = kissService.chooseTurnServer(sn, sn);
        Assert.assertEquals(1, iceServers.size());
        Assert.assertEquals(coturnNode.getExtranetIp4(), iceServers.get(0).getIpAddress());
        Assert.assertEquals("turn:" + coturnNode.getDomain() + ":5349", iceServers.get(0).getUrl());
        Assert.assertNotNull(iceServers.get(0).getUsername());
        Assert.assertNotNull(iceServers.get(0).getCredential());
    }

    @Test
    public void test_chooseTurnServer_notDeviceCoturnConfig() {
        String sn = OpenApiUtil.shortUUID();
        String countryNo = "cn";
        when(userRoleService.getCountryNoBySerialNumber(sn)).thenReturn(countryNo);
        CoturnNode coturnNode = new CoturnNode() {{
            setDomain("p-coturn-1386029440.addx.live");
            setExtranetIp4("*************");
        }};
        when(coturnFinder.chooseCoturnNodesBySNAndCountryNo(eq(sn), any())).thenReturn(Arrays.asList(coturnNode));
        when(deviceCoturnDAO.queryEnabledBySn(sn)).thenReturn(ImmutableList.of());

        List<IceServer> iceServers = kissService.chooseTurnServer(sn, sn);
        Assert.assertEquals(1, iceServers.size());
        Assert.assertEquals(coturnNode.getExtranetIp4(), iceServers.get(0).getIpAddress());
        Assert.assertEquals("turn:" + coturnNode.getDomain() + ":5349", iceServers.get(0).getUrl());
        Assert.assertNotNull(iceServers.get(0).getUsername());
        Assert.assertNotNull(iceServers.get(0).getCredential());
    }

    @Test
    public void test_chooseTurnServer_hasDeviceCoturnConfig_empty() {
        String sn = OpenApiUtil.shortUUID();
        String countryNo = "cn";
        when(userRoleService.getCountryNoBySerialNumber(sn)).thenReturn(countryNo);
        when(deviceCoturnDAO.queryEnabledBySn(sn)).thenReturn(ImmutableList.of(
                new DeviceCoturnDO() {{
                    setDomain("p-coturn-1386029440.addx.live");
                    setExtranetIp4("*************");
                    setType(DeviceCoturnDO.TYPE_WHITE_LIST);
                }},
                new DeviceCoturnDO() {{
                    setDomain("p-coturn-1386029440.addx.live");
                    setExtranetIp4("*************");
                    setType(DeviceCoturnDO.TYPE_BLACK_LIST);
                }}
        ));
        when(coturnFinder.chooseCoturnNodesBySNAndCountryNo(eq(sn), any())).thenReturn(Arrays.asList());
        List<IceServer> iceServers = kissService.chooseTurnServer(sn, sn);
        Assert.assertEquals(0, iceServers.size());
    }

    @Test
    public void test_getSignalServerAddr() {
        final String sn = OpenApiUtil.shortUUID();
        final String countryNo = OpenApiUtil.shortUUID();

        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(true);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);
        deviceInfoService.getDeviceSupport(sn);

        when(userRoleService.getCountryNoBySerialNumber(sn)).thenReturn(countryNo);
        final KissNode kissNode = new KissNode();
        when(deviceServerAllocService.queryBySn(sn)).thenReturn(new DeviceServerAllocDO().setSn(sn));
        when(kissFinder.chooseWebsocketNodeBNAndCountryNo(eq(sn), any())).thenReturn(kissNode);

        final SignalServer signalServer = new SignalServer();
        signalServer.setAddr("kiss");
        signalServer.setIpAddress("127.0.0.1");
        signalServer.setSecret("secret");
        when(redisService.get("device_signal_server:" + sn)).thenReturn(JSON.toJSONString(signalServer));

        kissService.getSignalServerAddr(sn, true);

    }

    @Test
    public void test_getSignalServerAddr_notCache() {
        final String sn = OpenApiUtil.shortUUID();
        final String countryNo = OpenApiUtil.shortUUID();

        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(true);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);
        deviceInfoService.getDeviceSupport(sn);

        when(userRoleService.getCountryNoBySerialNumber(sn)).thenReturn(countryNo);
        final KissNode kissNode = new KissNode();
        when(kissFinder.chooseWebsocketNodeBNAndCountryNo(eq(sn), any())).thenReturn(kissNode);

        when(redisService.get("device_signal_server:" + sn)).thenReturn(null);

        kissService.getSignalServerAddr(sn, true);

    }

    @Test
    public void test_wakeupDevice() {
        final String sn = OpenApiUtil.shortUUID();
        final String traceId = OpenApiUtil.shortUUID();

        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportUnlimitedWebsocket(true);
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(cloudDeviceSupport);
        deviceInfoService.getDeviceSupport(sn);

        final KissServer kissServer = new KissServer();
        kissServer.setSecret("secret");
        kissServer.setRealIp4("127.0.0.1");
        kissServer.setUpdateTime(System.currentTimeMillis());
        when(kissAllocationService.getKissServer(sn)).thenReturn(kissServer);

        when(kissWsService.wakeup(eq(sn), eq(traceId), anyString(), anyBoolean())).thenReturn(new KissApiResult().setCode(0));
        {
            // when(deviceMsgSrcManager.isWsKeepAlive(sn)).thenReturn(true);
            when(deviceMsgSrcManager.queryKissFlag(sn)).thenReturn(new DeviceKissFlagDO()
                    .setDeviceSupportKissReplaceMqtt(true).setDeviceIsWsKeepAlive(true));
            kissService.wakeupDevice(sn, traceId);
            when(kissWsService.wakeup(eq(sn), eq(traceId), anyString(), anyBoolean())).thenReturn(new KissApiResult().setCode(0));
            kissService.wakeupDevice(sn, traceId);
            when(kissWsService.wakeup(eq(sn), eq(traceId), anyString(), anyBoolean())).thenReturn(new KissApiResult().setCode(-1));
            kissService.wakeupDevice(sn, traceId);
        }
    }

    @Test
    public void test_updateKeepAliveParams() {
        final String sn = OpenApiUtil.shortUUID();
        final KeepAliveParams params = new KeepAliveParams();
        {
            when(deviceMsgSrcManager.isWsReplaceMqtt(sn)).thenReturn(true);
            when(kissWsService.updateKeepAliveParams(eq(sn), any())).thenReturn(true);
            kissService.updateKeepAliveParams(sn, params);
            when(kissWsService.updateKeepAliveParams(eq(sn), any())).thenReturn(false);
            kissService.updateKeepAliveParams(sn, params);
        }
    }

    @Test
    public void test_closeOldGroup() {
        final String sn = OpenApiUtil.shortUUID();
        {
            when(deviceMsgSrcManager.isWsReplaceMqtt(sn)).thenReturn(true);
            when(kissWsService.closeOldGroup(eq(sn))).thenReturn(true);
            kissService.closeOldGroup(sn);
            when(kissWsService.closeOldGroup(eq(sn))).thenReturn(false);
            kissService.closeOldGroup(sn);
        }
    }

    /*
    @Test
    public void test_getDeviceStatus() {
        final String sn = OpenApiUtil.shortUUID();

        final SignalServer signalServer = new SignalServer();
        signalServer.setAddr("kiss");
        signalServer.setIpAddress("127.0.0.1");
        signalServer.setSecret("secret");
        when(redisService.get("device_signal_server:" + sn)).thenReturn(JSON.toJSONString(signalServer));

        final KissApiResult kissApiResult = new KissApiResult();
        kissApiResult.setCode(0);
        kissApiResult.setData("normal");
        final ResponseEntity<String> respEntity = new ResponseEntity<>(JSON.toJSONString(kissApiResult), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), (Class) any())).thenReturn(respEntity);

        Assert.assertEquals(KissDeviceStatus.normal, kissService.getDeviceStatus(sn));
    }

    @Test
    public void test_getDeviceStatus_notKissNode() {
        final String sn = OpenApiUtil.shortUUID();

        final SignalServer signalServer = new SignalServer();
        signalServer.setAddr("kiss");
        signalServer.setIpAddress("127.0.0.1");
        signalServer.setSecret("secret");
        when(redisService.get("device_signal_server:" + sn)).thenReturn(null);

        Assert.assertNull(kissService.getDeviceStatus(sn));
    }

    @Test
    public void test_closeOldGroup() {
        final String sn = OpenApiUtil.shortUUID();

        final SignalServer signalServer = new SignalServer();
        signalServer.setAddr("kiss");
        signalServer.setIpAddress("127.0.0.1");
        signalServer.setSecret("secret");
        when(redisService.get("device_signal_server:" + sn)).thenReturn(JSON.toJSONString(signalServer));

        final KissApiResult kissApiResult = new KissApiResult();
        kissApiResult.setCode(0);
        kissApiResult.setData("");
        final ResponseEntity<String> respEntity = new ResponseEntity<>(JSON.toJSONString(kissApiResult), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), (Class) any())).thenReturn(respEntity);

        Assert.assertTrue(kissService.closeOldGroup(sn));
        {
            final String str = "{\"code\":0,\"msg\":\"\",\"data\":{\"closeIds\":[],\"ids\":[]}}";
            final KissApiResult result = JSON.parseObject(str, KissApiResult.class);
            Assert.assertNotNull(result.getData());
        }
    }

    @Test
    public void test_closeOldGroup_notKissNode() {
        final String sn = OpenApiUtil.shortUUID();

        final SignalServer signalServer = new SignalServer();
        signalServer.setAddr("kiss");
        signalServer.setIpAddress("127.0.0.1");
        signalServer.setSecret("secret");
        when(redisService.get("device_signal_server:" + sn)).thenReturn(null);

        Assert.assertFalse(kissService.closeOldGroup(sn));
    }

    @Test
    public void test_updateKeepAliveParams() {
        final String sn = OpenApiUtil.shortUUID();

        final SignalServer signalServer = new SignalServer();
        signalServer.setAddr("kiss");
        signalServer.setIpAddress("127.0.0.1");
        signalServer.setSecret("secret");
        when(redisService.get("device_signal_server:" + sn)).thenReturn(JSON.toJSONString(signalServer));

        final KissApiResult kissApiResult = new KissApiResult();
        kissApiResult.setCode(0);
        final ResponseEntity<String> respEntity = new ResponseEntity<>(JSON.toJSONString(kissApiResult), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), (Class) any())).thenReturn(respEntity);

        Assert.assertTrue(kissService.updateKeepAliveParams(sn, new KeepAliveParams()));
    }

    @Test
    public void test_updateKeepAliveParams_callNotSuccess() {
        final String sn = OpenApiUtil.shortUUID();

        final SignalServer signalServer = new SignalServer();
        signalServer.setAddr("kiss");
        signalServer.setIpAddress("127.0.0.1");
        signalServer.setSecret("secret");
        when(redisService.get("device_signal_server:" + sn)).thenReturn(JSON.toJSONString(signalServer));

        final KissApiResult kissApiResult = new KissApiResult();
        kissApiResult.setCode(-1);
        final ResponseEntity<String> respEntity = new ResponseEntity<>(JSON.toJSONString(kissApiResult), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(), any(), (Class) any())).thenReturn(respEntity);

        Assert.assertFalse(kissService.updateKeepAliveParams(sn, new KeepAliveParams()));
    }

    @Test
    public void test_updateKeepAliveParams_notKissNode() {
        final String sn = OpenApiUtil.shortUUID();

        final SignalServer signalServer = new SignalServer();
        signalServer.setAddr("kiss");
        signalServer.setIpAddress("127.0.0.1");
        signalServer.setSecret("secret");
        when(redisService.get("device_signal_server:" + sn)).thenReturn(null);

        Assert.assertFalse(kissService.updateKeepAliveParams(sn, new KeepAliveParams()));
    }
    */

    @Test
    public void test_sendReportEventResponse() {
        try {
            String response = kissService.sendReportEventResponse("sn_01", "", "http://localhost:9092");
            Assert.assertTrue(StringUtils.isEmpty(response));
        } catch (Throwable e) {
        }
    }

    @Data
    @AllArgsConstructor
    public static class Result2<T> {
        private Integer result;
        private String msg;
        private T data;
    }

    @Test
    public void test_KissApiResult() {
        final KissApiResult r1 = new KissApiResult().setCode(0).setMsg("msg").setData("abc124");
        final KissApiResult r2 = new KissApiResult().setCode(0).setMsg("msg").setData(System.currentTimeMillis());
        Assert.assertEquals(r1, KissApiResult.fromJson(JSON.toJSONString(r1), String.class));
        Assert.assertEquals(r2, KissApiResult.fromJson(JSON.toJSONString(r2), Long.class));
        final KissApiResult r3 = new KissApiResult().setCode(0).setMsg("msg").setData(new Result<>(1, "xyz", null));
        Assert.assertEquals(r3, KissApiResult.fromJson(JSON.toJSONString(r3), Result.class));
    }

//    @Test
//    public void testx(){
//        final TokenInfo parse = AccessTokenHelper.parse(" eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************.ITybphtwvAITsaujpAbYGvNK2pku4LNhNDH-Cx3dqrNm0RPgZkXr53Syv72xY1oL7B6SzTiiX2V-DRAyF__c7w");
//        log.info("");
//    }

    @Test
    public void test_getKissNodeFromSignalServerCache() {
        { // 缓存节点离线
            String sn = OpenApiUtil.shortUUID();
            when(kissDeviceNodeManager.get(sn)).thenAnswer(it -> {
                return new KissDeviceNode().setKissIp("127.0.0.1").setDeviceSn(it.getArgument(0))
                        .setDeviceStatus(KissDeviceStatus.offline).setLastUpdateTime(System.currentTimeMillis());
            });
            KissNode kissNode = kissService.getKissNodeFromSignalServerCache(sn);
            Assert.assertEquals(null, kissNode);
        }
        { // 缓存节点被移除
            KissNode kissNode1 = new KissNode() {{
                setRemoving(1);
            }};
            kissFinder.getNodeMapByType(KissNodeType.kiss).put("127.0.0.1", kissNode1);
            String sn = OpenApiUtil.shortUUID();
            when(kissDeviceNodeManager.get(sn)).thenAnswer(it -> {
                return new KissDeviceNode().setKissIp("127.0.0.1").setDeviceSn(it.getArgument(0))
                        .setDeviceStatus(KissDeviceStatus.normal).setLastUpdateTime(System.currentTimeMillis());
            });
            KissNode kissNode = kissService.getKissNodeFromSignalServerCache(sn);
            Assert.assertEquals(kissNode1, kissNode);
            kissFinder.getNodeMapByType(KissNodeType.kiss).clear();
        }
        { // 缓存节点找不到
            KissNode kissNode1 = new KissNode() {{
                setRemoving(0);
            }};
            kissFinder.getNodeMapByType(KissNodeType.kiss).put("*********", kissNode1);
            String sn = OpenApiUtil.shortUUID();
            when(kissDeviceNodeManager.get(sn)).thenAnswer(it -> {
                return new KissDeviceNode().setKissIp("127.0.0.1").setDeviceSn(it.getArgument(0))
                        .setDeviceStatus(KissDeviceStatus.dormant).setLastUpdateTime(System.currentTimeMillis());
            });
            KissNode kissNode = kissService.getKissNodeFromSignalServerCache(sn);
            Assert.assertEquals(null, kissNode);
            kissFinder.getNodeMapByType(KissNodeType.kiss).clear();
        }
        { // 缓存节点能找到
            KissNode kissNode1 = new KissNode() {{
                setRemoving(0);
            }};
            kissFinder.getNodeMapByType(KissNodeType.kiss).put("127.0.0.1", kissNode1);
            String sn = OpenApiUtil.shortUUID();
            when(kissDeviceNodeManager.get(sn)).thenAnswer(it -> {
                return new KissDeviceNode().setKissIp("127.0.0.1").setDeviceSn(it.getArgument(0))
                        .setDeviceStatus(KissDeviceStatus.dormant).setLastUpdateTime(System.currentTimeMillis());
            });
            KissNode kissNode = kissService.getKissNodeFromSignalServerCache(sn);
            Assert.assertEquals(kissNode1, kissNode);
        }

    }

    @Test
    public void test_wakeupDevice_2() {
        final String sn = OpenApiUtil.shortUUID();
        // when(deviceMsgSrcManager.isWsKeepAlive(sn)).thenReturn(false);
        when(kissWsService.wakeup(anyString(), anyString(), anyString(), anyBoolean())).thenReturn(new KissApiResult().setCode(0));
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(new CloudDeviceSupport() {{
            setSupportUnlimitedWebsocket(false);
        }});
        when(kissAllocationService.getKissServer(sn)).thenReturn(null);
        {
            when(deviceMsgSrcManager.queryKissFlag(sn)).thenReturn(new DeviceKissFlagDO()
                    .setDeviceSupportKissReplaceMqtt(false).setDeviceIsWsKeepAlive(false));
            kissService.wakeupDevice(sn, "");
        }
        {
            when(deviceMsgSrcManager.queryKissFlag(sn)).thenReturn(new DeviceKissFlagDO()
                    .setDeviceSupportKissReplaceMqtt(true).setDeviceIsWsKeepAlive(true));
            kissService.wakeupDevice(sn, "");
        }
        {
            when(deviceMsgSrcManager.queryKissFlag(sn)).thenReturn(new DeviceKissFlagDO()
                    .setDeviceSupportKissReplaceMqtt(true).setDeviceIsWsKeepAlive(false));
            when(kissAllocationService.getKissServer(sn)).thenReturn(null);

            kissService.wakeupDevice(sn, "");
        }
        {
            when(deviceMsgSrcManager.queryKissFlag(sn)).thenReturn(new DeviceKissFlagDO()
                    .setDeviceSupportKissReplaceMqtt(true).setDeviceIsWsKeepAlive(false));
            KissServer kissServer = new KissServer().setRealIp4("************");
            when(kissAllocationService.getKissServer(sn)).thenReturn(kissServer);
            kissFinder.getNodeMapByType(KissNodeType.kiss).put(kissServer.getRealIp4(), new KissNode() {{
                setIntranetIp4(kissServer.getRealIp4());
            }});
            when(kissWsService.wakeupByKissIp(anyString(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(new KissApiResult().setCode(0));

            kissService.wakeupDevice(sn, "");
            kissFinder.getNodeMapByType(KissNodeType.kiss).clear();
        }
        {
            when(deviceMsgSrcManager.queryKissFlag(sn)).thenReturn(new DeviceKissFlagDO()
                    .setDeviceSupportKissReplaceMqtt(true).setDeviceIsWsKeepAlive(false));
            KissServer kissServer = new KissServer().setRealIp4("************");
            when(kissAllocationService.getKissServer(sn)).thenReturn(kissServer);
            kissFinder.getNodeMapByType(KissNodeType.kiss).put(kissServer.getRealIp4(), new KissNode() {{
                setIntranetIp4(kissServer.getRealIp4());
            }});
            when(kissWsService.wakeupByKissIp(anyString(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(new KissApiResult().setCode(500));

            kissService.wakeupDevice(sn, "");
            kissFinder.getNodeMapByType(KissNodeType.kiss).clear();
        }

    }

    @Test
    public void test_chooseTurnServer() {
        String sn = OpenApiUtil.shortUUID();
        String countryNo = OpenApiUtil.shortUUID();
        when(deviceServerAllocService.queryBySn(sn)).thenReturn(new DeviceServerAllocDO().setSn(sn).setCoturnPurpose("tcpdump"));
        when(userRoleService.getCountryNoBySerialNumber(sn)).thenReturn(countryNo);
        List<CoturnNode> coturnNodes = Arrays.asList(new CoturnNode());
        when(coturnFinder.chooseCoturnNodesBySNAndCountryNo(sn, new ChooseNodeCondition().setCountryNo(countryNo).setPurpose("tcpdump"))).thenReturn(coturnNodes);
        Assert.assertEquals(1, kissService.chooseTurnServer(sn, sn).size());
    }

    @Test
    public void test_getKissParams(){
        String sn = OpenApiUtil.shortUUID();
        String countryNo = OpenApiUtil.shortUUID();
        when(userRoleService.getCountryNoBySerialNumber(sn)).thenReturn(countryNo);
        when(deviceServerAllocService.queryBySn(sn)).thenReturn(new DeviceServerAllocDO().setSn(sn).setKissPurpose("tcpdump"));
        KissNode kissNode1 = new KissNode();
        kissNode1.setIntranetIp4("127.0.0.1");
        kissNode1.setExtranetIp4("*********");
        kissNode1.setTcpPort(8888);
        when(kissFinder.chooseHeartbeatNodesBySNAndCountryNo(eq(sn), any(), anyInt())).thenReturn(Arrays.asList(kissNode1));
        KissParams kissParams = kissService.getKissParams(sn, EProtocol.TCP, EHeartbeatReplyMode.NO_REPLY);
        Assert.assertNotNull(kissParams);
    }
}
