package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.additional_tier.AdditionalTierInfo;
import com.addx.iotcamera.bean.app.vip.*;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.additional_tier.AdditionalTierDO;
import com.addx.iotcamera.bean.device.model.DeviceModelIconDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.init.TierProductInit;
import com.addx.iotcamera.bean.response.user.ABTestResult;
import com.addx.iotcamera.bean.response.user.FreeLicenseABTestResult;
import com.addx.iotcamera.bean.response.user.Product4GABTestResult;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.config.app.*;
import com.addx.iotcamera.config.device.FreeUserVipTier2Config;
import com.addx.iotcamera.config.pay.AppTierKeyTranslatedConfig;
import com.addx.iotcamera.dao.IProductDAO;
import com.addx.iotcamera.dao.TierDao;
import com.addx.iotcamera.dao.additional_tier.AdditionalTierDao;
import com.addx.iotcamera.dao.additional_tier.AdditionalTierProductDao;
import com.addx.iotcamera.enums.pay.TierTypeEnums;
import com.addx.iotcamera.service.abtest.AbTestService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import com.addx.iotcamera.service.vip.TierService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.time.Instant;
import java.util.*;

import static com.addx.iotcamera.constants.AbTestConstants.DURATION_HALF_YEAR;
import static com.addx.iotcamera.constants.AbTestConstants.DURATION_ONE_YEAR;
import static com.addx.iotcamera.constants.CopyWriteConstans.*;
import static com.addx.iotcamera.enums.ProductTypeEnums.*;
import static com.addx.iotcamera.enums.pay.TierServiceTypeEnums.*;
import static org.addx.iot.common.constant.AppConstants.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class TierServiceTest {

    @InjectMocks
    private TierService tierService;

    @Mock
    private UserVipService userVipService;
    @Mock
    private AdditionalTierDao additionalTierDao;
    @Mock
    private AdditionalTierProductDao additionalTierProductDao;
    @Mock
    private TierDao tierDao;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private DeviceService deviceService;
    @Mock
    private DeviceModelIconService deviceModelIconService;
    @Mock
    private TierProductInit tierProductInit;

    @Mock
    private DeviceModelEventService deviceModelEventService;

    @Mock
    private AdditionalUserTierService additionalUserTierService;

    @Mock
    private AppRecommendProductConfig appRecommendProductConfig;

    @Mock
    private ProductService productService;

    @Mock
    private TierProductConfig tierProductConfig;

    @Mock
    private SupportProductConfig supportProductConfig;

    @Mock
    private TierProductSubConfig tierProductSubConfig;

    @Mock
    private TierInfoConfig tierInfoConfig;

    @Mock
    private TenantTierConfig tenantTierConfig;

    @Mock
    private CopyWrite copyWrite;

    @Mock
    private AppTierKeyTranslatedConfig appTierKeyTranslatedConfig;

    @Mock
    private VipService vipService;

    @Mock
    private IProductDAO productDAO;

    @Mock
    private AbTestService abTestService;

    @Before
    public void init() {
        when(tierProductInit.getProductMonth(anyInt(), any(), any())).thenReturn("1month");

    }

    private FreeUserVipTier2Config freeUserVipTier2ConfigUnderTest;

    @Before
    public void setUp() throws Exception {
        freeUserVipTier2ConfigUnderTest = new FreeUserVipTier2Config();
        freeUserVipTier2ConfigUnderTest.afterPropertiesSet();

        freeUserVipTier2ConfigUnderTest.setRegisterConfigList(Arrays.asList(new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(110);
            setRegisterStart(Integer.MAX_VALUE - 1);
            setRegisterEnd(Integer.MAX_VALUE);
            setCooldownValue(Collections.singletonMap("cg1", "60"));
        }}, new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(100);
            setRegisterStart(0);
            setRegisterEnd(Integer.MAX_VALUE);
            setCooldownValue(Collections.singletonMap("cg1", "60"));
        }}, new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(100);
            setRegisterStart(0);
            setRegisterEnd(1);
            setCooldownValue(Collections.singletonMap("cg1", "60"));
        }}, new FreeUserVipTier2Config.RegisterConfig() {{
            setTierId(100);
            setRegisterStart(Integer.MAX_VALUE - 1);
            setRegisterEnd(Integer.MAX_VALUE);
            setCooldownValue(Collections.singletonMap("cg1", "60"));
        }}));
    }

    @Test
    public void test_queryAdditionalTierList() throws NoSuchFieldException, IllegalAccessException {
        when(additionalTierDao.getAllTier(any())).thenReturn(null);
        List<AdditionalTierInfo> additionalTierInfoList = tierService.queryAdditionalTierList(new AppRequestBase(), 1);
        assertTrue(CollectionUtils.isEmpty(additionalTierInfoList));

        when(additionalTierDao.getAllTier(any())).thenReturn(Arrays.asList(new AdditionalTierDO() {{
            setTierUid("a");
            setType(1);
            setTenantId("vicoo");
            setNeedVipTier(true);
            setStatus(0);
        }}, new AdditionalTierDO() {{
            setTierUid("b");
            setType(1);
            setTenantId("vicoo");
            setNeedVipTier(false);
            setStatus(0);
        }}, new AdditionalTierDO() {{
            setTierUid("c");
            setType(2);
            setTenantId("vicoo");
            setNeedVipTier(false);
            setStatus(0);
        }}));

        when(userVipService.isVipUser(eq(1))).thenReturn(true);
        when(userRoleService.getUserRoleByUserId(eq(1), anyInt())).thenReturn(Arrays.asList(new UserRoleDO() {{
            setSerialNumber("sn_01");
        }}, new UserRoleDO() {{
            setSerialNumber("sn_02");
        }}));
        when(deviceManualService.getModelNoBySerialNumber(eq("sn_01"))).thenReturn("CG1");
        when(deviceManualService.getModelNoBySerialNumber(eq("sn_02"))).thenReturn("CG2");
        when(deviceModelEventService.queryRowDeviceModelEvent(any())).thenReturn(new HashSet<>(Arrays.asList("bird")));

        when(userVipService.isVipUser(eq(2))).thenReturn(false);
        when(userRoleService.getUserRoleByUserId(eq(2), anyInt())).thenReturn(Arrays.asList(new UserRoleDO() {{
            setSerialNumber("sn_01");
        }}));

        when(userVipService.isVipUser(eq(3))).thenReturn(true);
        when(userRoleService.getUserRoleByUserId(eq(3), anyInt())).thenReturn(null);

        when(userVipService.isVipUser(eq(4))).thenReturn(false);

        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(new DeviceDO());

        when(additionalTierProductDao.getByTierUid(anyString(), anyString())).thenReturn(Arrays.asList(new ProductDO() {{
            setPrice(100);
            setCurrency(0);
        }}, new ProductDO() {{
            setPrice(0);
            setCurrency(1);
        }}));

        when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(new DeviceModelIconDO());

        when(additionalUserTierService.isReceivedFreeAdditionalTier(anyInt(), anyString(), anyInt())).thenReturn(false);

        additionalTierInfoList = tierService.queryAdditionalTierList(new AppRequestBase(), 1);
        assertTrue(additionalTierInfoList.size() == 3);

        when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);
        additionalTierInfoList = tierService.queryAdditionalTierList(new AppRequestBase(), 2);
        assertTrue(CollectionUtils.isNotEmpty(additionalTierInfoList) && additionalTierInfoList.size() == 2 && "b".equals(additionalTierInfoList.get(0).getTierUid()) && "c".equals(additionalTierInfoList.get(1).getTierUid()));

        when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);
        when(additionalTierProductDao.getByTierUid(eq("vicoo"), eq("b"))).thenReturn(null);
        additionalTierInfoList = tierService.queryAdditionalTierList(new AppRequestBase(), 3);
        assertTrue(CollectionUtils.isNotEmpty(additionalTierInfoList) && additionalTierInfoList.size() == 1 && "c".equals(additionalTierInfoList.get(0).getTierUid()));

        when(deviceModelIconService.queryDeviceModelIcon(any())).thenReturn(null);
        additionalTierInfoList = tierService.queryAdditionalTierList(new AppRequestBase(), 4);
        assertTrue(CollectionUtils.isNotEmpty(additionalTierInfoList) && additionalTierInfoList.size() == 1 && "c".equals(additionalTierInfoList.get(0).getTierUid()));
    }

    @Test
    @DisplayName("获取套餐记录-套餐id不存在")
    public void queryTierLevelById_null(){
        int tierId = 0;
        when(tierDao.getTierByTierId(tierId)).thenReturn(null);

        int expectedResult = 0;
        int actualResult = tierService.queryTierLevelById(tierId);
        assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("获取套餐记录")
    public void queryTierLevelById(){
        int tierId = 0;
        Tier tier = new Tier();
        tier.setLevel(1);
        when(tierDao.getTierByTierId(tierId)).thenReturn(tier);

        int expectedResult = 1;
        int actualResult = tierService.queryTierLevelById(tierId);
        assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("已领取")
    public void test_initRecommendProduct_hasReceive(){
        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierReceive(true);

        AppRequestBase request = new AppRequestBase();

        Boolean exceptResult = true;
        Boolean actualResult = tierService.initRecommendProduct(userVipTier,request);
        assertEquals(exceptResult,actualResult);
    }

    @Test
    @DisplayName("未领取-不弹出")
    public void test_initRecommendProduct_no_shouldReminder(){
        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierReceive(false);
        userVipTier.setShouldReminder(false);

        AppRequestBase request = new AppRequestBase();

        Boolean exceptResult = true;
        Boolean actualResult = tierService.initRecommendProduct(userVipTier,request);
        assertEquals(exceptResult,actualResult);
    }

    @Test
    @DisplayName("未领取-需弹出-CN节点")
    public void test_initRecommendProduct_shouldReminder_CN_serve() throws NoSuchFieldException, IllegalAccessException {
        Field field = tierService.getClass().getDeclaredField("servernode");
        field.setAccessible(true);
        field.set(tierService,"CN");

        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierReceive(false);
        userVipTier.setShouldReminder(true);

        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setAppType("Android");
        request.setApp(app);

        Boolean exceptResult = true;
        Boolean actualResult = tierService.initRecommendProduct(userVipTier,request);
        assertEquals(exceptResult,actualResult);
    }

    @Test
    @DisplayName("未领取-需弹出-不包含tenant")
    public void test_initRecommendProduct_shouldReminder_no_tenant(){
        String tenantId = "vicoo";

        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierReceive(false);
        userVipTier.setShouldReminder(true);

        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setAppType("iOS");
        app.setTenantId(tenantId);
        request.setApp(app);

        when(appRecommendProductConfig.getConfig()).thenReturn(Maps.newHashMap());

        Boolean exceptResult = true;
        Boolean actualResult = tierService.initRecommendProduct(userVipTier,request);
        assertEquals(exceptResult,actualResult);
    }

    @Test
    @DisplayName("未领取-需弹出-未配置推荐商品")
    public void test_initRecommendProduct_shouldReminder_no_recommendProductId(){
        String tenantId = "vicoo";
        Integer productId = 20111;
        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierReceive(false);
        userVipTier.setShouldReminder(true);

        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setAppType("iOS");
        app.setTenantId(tenantId);
        request.setApp(app);

        Map<String,AppRecommendProductConfig.TierRecommendProduct> productMap = new HashMap<>();
        AppRecommendProductConfig.TierRecommendProduct product = new AppRecommendProductConfig.TierRecommendProduct();
        product.setRecommendProductId(productId);
        productMap.put(tenantId,product);

        when(appRecommendProductConfig.getConfig()).thenReturn(productMap);

        when(appRecommendProductConfig.queryRecommendProductId(tenantId)).thenReturn(null);

        Boolean exceptResult = true;
        Boolean actualResult = tierService.initRecommendProduct(userVipTier,request);
        assertEquals(exceptResult,actualResult);
    }


    @Test
    @DisplayName("未领取-需弹出-未配置推荐商品")
    public void test_initRecommendProduct_shouldReminder_no_productId(){
        String tenantId = "vicoo";
        Integer productId = 20111;
        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierReceive(false);
        userVipTier.setShouldReminder(true);

        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setAppType("iOS");
        app.setTenantId(tenantId);
        request.setApp(app);

        Map<String,AppRecommendProductConfig.TierRecommendProduct> productMap = new HashMap<>();
        AppRecommendProductConfig.TierRecommendProduct product = new AppRecommendProductConfig.TierRecommendProduct();
        product.setRecommendProductId(productId);
        productMap.put(tenantId,product);
        when(appRecommendProductConfig.getConfig()).thenReturn(productMap);

        when(appRecommendProductConfig.queryRecommendProductId(tenantId)).thenReturn(productId);
        when(productService.queryProductById(productId)).thenReturn(null);

        Boolean exceptResult = true;
        Boolean actualResult = tierService.initRecommendProduct(userVipTier,request);
        assertEquals(exceptResult,actualResult);
    }


    @Test
    @DisplayName("未领取-需弹出")
    public void test_initRecommendProduct(){
        String tenantId = "vicoo";
        Integer productId = 20111;
        UserVipTier userVipTier = new UserVipTier();
        userVipTier.setTierReceive(false);
        userVipTier.setShouldReminder(true);

        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setAppType("iOS");
        app.setTenantId(tenantId);
        request.setApp(app);

        Map<String,AppRecommendProductConfig.TierRecommendProduct> productMap = new HashMap<>();
        AppRecommendProductConfig.TierRecommendProduct product = new AppRecommendProductConfig.TierRecommendProduct();
        product.setRecommendProductId(productId);
        productMap.put(tenantId,product);
        when(appRecommendProductConfig.getConfig()).thenReturn(productMap);

        when(appRecommendProductConfig.queryRecommendProductId(tenantId)).thenReturn(productId);

        ProductDO productDO = new ProductDO();
        productDO.setId(productId);
        productDO.setTierId(2);
        when(productService.queryProductById(productId)).thenReturn(productDO);

        Tier tier = new Tier();
        tier.setMaxDeviceNum(4);

        Boolean exceptResult = true;
        Boolean actualResult = tierService.initRecommendProduct(userVipTier,request);
        assertEquals(exceptResult,actualResult);
    }


    @Test
    public void test_queryRecommendProductId_CN_serve() throws NoSuchFieldException, IllegalAccessException {
        Field field = tierService.getClass().getDeclaredField("servernode");
        field.setAccessible(true);
        field.set(tierService,"CN");

        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setAppType("Android");
        request.setApp(app);


        Integer expectResult = null;
        Integer actualResult = tierService.queryRecommendProductId(1,request);
        assertEquals(expectResult,actualResult);
    }

    @Test
    public void test_queryRecommendProductId_hasReceive() {
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setAppType("iOS");
        app.setTenantId("vicoo");
        request.setApp(app);

        when(userVipService.isTierReceived(any(),any())).thenReturn(true);

        Integer expectResult = null;
        Integer actualResult = tierService.queryRecommendProductId(1,request);
        assertEquals(expectResult,actualResult);
    }

    @Test
    public void test_queryRecommendProductId_no_tenant() {
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setAppType("iOS");
        app.setTenantId("vicoo");
        request.setApp(app);

        when(userVipService.isTierReceived(any(),any())).thenReturn(false);

        when(appRecommendProductConfig.getConfig()).thenReturn(Maps.newHashMap());

        Integer expectResult = null;
        Integer actualResult = tierService.queryRecommendProductId(1,request);
        assertEquals(expectResult,actualResult);
    }


    @Test
    public void test_queryRecommendProductId() {
        Integer productId = 20111;
        String tenantId = "vicoo";
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setAppType("iOS");
        app.setTenantId(tenantId);
        request.setApp(app);

        when(userVipService.isTierReceived(any(),any())).thenReturn(false);

        Map<String,AppRecommendProductConfig.TierRecommendProduct> productMap = new HashMap<>();
        AppRecommendProductConfig.TierRecommendProduct product = new AppRecommendProductConfig.TierRecommendProduct();
        product.setRecommendProductId(productId);
        productMap.put(tenantId,product);

        when(appRecommendProductConfig.getConfig()).thenReturn(productMap);
        when(appRecommendProductConfig.queryRecommendProductId(any())).thenReturn(productId);
        Integer expectResult = productId;
        Integer actualResult = tierService.queryRecommendProductId(1,request);
        assertEquals(expectResult,actualResult);
    }

    @Test
    public void test_filterOutNoProductTier() {
        List<TierInfo> tierInfoList = tierService.filterOutNoProductTier(Arrays.asList(new TierInfo() {{
            setCommomProductList(Collections.singletonList(null));
        }}, new TierInfo() {{
            setSubProductList(Collections.singletonList(null));
        }}, new TierInfo() {{
            setUpgradeProductList(Collections.singletonList(null));
        }}, new TierInfo()));
        assertEquals(tierInfoList.size(), 3);
    }

    @Test
    @DisplayName("免费套餐2")
    public void test_queryTierLookBackDay_freeTier2(){
        Integer tierId = 10;
        Integer registerTime = (int) Instant.now().getEpochSecond()-1000;

        Integer expectedResult = 7;
        Integer actualResult = tierService.queryTierLookBackDay(tierId,registerTime);
        assertEquals(expectedResult,actualResult);
    }


    @Test
    public void test_queryTierLookBackDay(){
        Integer tierId = 100;
        Integer registerTime = 1;

        Tier tier = new Tier();
        tier.setRollingDays(3);
        when(tierDao.getTierByTierId(1)).thenReturn(tier);
        Integer expectedResult = 0;
        Integer actualResult = tierService.queryTierLookBackDay(tierId,registerTime);
        assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("未配置商品列表")
    public void test_queryPurchaseList_productConfig_empty(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        Integer tierId = 1;

        when(tierProductConfig.getProduct()).thenReturn(Maps.newHashMap());
        List<TierProduct> expectedResult = Lists.newArrayList();
        List<TierProduct> actualResult = tierService.queryPurchaseList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("商品列表-未配置tenantId")
    public void test_queryPurchaseList_tenantId(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        app.setAppBuild("bundle");
        request.setApp(app);

        Integer tierId = 1;

        when(tierProductConfig.getProduct()).thenReturn(this.initTierProductList(TENANTID_GUARD,request.getApp().getBundle(),request.getLanguage(),tierId));
        List<TierProduct> expectedResult = Lists.newArrayList();
        List<TierProduct> actualResult = tierService.queryPurchaseList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("商品列表-未配置bundle")
    public void test_queryPurchaseList_bundle(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        app.setAppBuild("bundle");

        request.setApp(app);

        Integer tierId = 1;

        when(tierProductConfig.getProduct()).thenReturn(this.initTierProductList(TENANTID_VICOO,"app_bundle",request.getLanguage(),tierId));
        List<TierProduct> expectedResult = Lists.newArrayList();
        List<TierProduct> actualResult = tierService.queryPurchaseList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("商品列表-未配置language")
    public void test_queryPurchaseList_language(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        app.setAppBuild("bundle");
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setApp(app);

        Integer tierId = 1;

        when(tierProductConfig.getProduct()).thenReturn(this.initTierProductList(TENANTID_VICOO,request.getApp().getAppBuild(),APP_LANGUAGE_EN,tierId));
        List<TierProduct> expectedResult = Lists.newArrayList();
        List<TierProduct> actualResult = tierService.queryPurchaseList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("商品列表-未配置language")
    public void test_queryPurchaseList_tier(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        app.setAppBuild("bundle");
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setApp(app);

        Integer tierId = 1;

        when(tierProductConfig.getProduct()).thenReturn(this.initTierProductList(TENANTID_VICOO,request.getApp().getAppBuild(),APP_LANGUAGE_EN,2));
        List<TierProduct> expectedResult = Lists.newArrayList();
        List<TierProduct> actualResult = tierService.queryPurchaseList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("商品列表")
    public void test_queryPurchaseList_tier_product(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_GUARD);
        app.setAppBuild("bundle");
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setApp(app);

        Integer tierId = 1;

        when(tierProductConfig.getProduct()).thenReturn(this.initTierProductList(TENANTID_GUARD,request.getApp().getAppBuild(),APP_LANGUAGE_ZH,tierId));

        Set<Integer> supportProduct = new HashSet<>(Arrays.asList(0));
        when(supportProductConfig.querySupportProductType(any())).thenReturn(supportProduct);

        List<TierProduct> expectedResult = Lists.newArrayList();
        expectedResult.add(new TierProduct());
        List<TierProduct> actualResult = tierService.queryPurchaseList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("商品列表")
    public void test_queryTierProductList_v1(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_GUARD);
        app.setAppBuild("bundle");
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setApp(app);

        Integer tierId = 1;

        when(tierProductConfig.getProduct()).thenReturn(this.initTierProductList(TENANTID_GUARD,request.getApp().getAppBuild(),APP_LANGUAGE_ZH,tierId));

        Set<Integer> supportProduct = new HashSet<>(Arrays.asList(0));
        when(supportProductConfig.querySupportProductType(any())).thenReturn(supportProduct);

        List<TierProduct> expectedResult = Lists.newArrayList();
        expectedResult.add(new TierProduct());
        List<TierProduct> actualResult = tierService.queryPurchaseList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }

    private Map<String, Map<String, Map<String, Map<Integer, List<TierProduct>>>>> initTierProductList(String tenantId,String bundle,String language,Integer tierId){
        Map<String, Map<String, Map<String, Map<Integer, List<TierProduct>>>>> tenantConfig = new HashMap<>();

        Map<String, Map<String, Map<Integer, List<TierProduct>>>> bundleMap = Maps.newHashMap();

        Map<String, Map<Integer, List<TierProduct>>> languageMap = Maps.newHashMap();

        Map<Integer, List<TierProduct>> tierMap = Maps.newHashMap();

        List<TierProduct> tierProductList = Lists.newArrayList();
        TierProduct tierProduct = new TierProduct();
        tierProductList.add(tierProduct);

        tierMap.put(tierId,tierProductList);
        languageMap.put(language,tierMap);
        bundleMap.put(bundle,languageMap);
        tenantConfig.put(tenantId,bundleMap);

        return tenantConfig;
    }


    @Test
    @DisplayName("未配置套餐商品")
    public void test_querySubList_productConfig_null(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        app.setAppBuild(bundle);
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setApp(app);
        Integer tierId = 1;

        when(tierProductSubConfig.getProduct()).thenReturn(null);

        List<TierProduct> expectedResult = Lists.newArrayList();
        List<TierProduct> actualResult = tierService.querySubList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("未配置套餐商品")
    public void test_querySubList_productConfig_tenant(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_GUARD);
        app.setAppBuild(bundle);
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setApp(app);
        Integer tierId = 1;

        when(tierProductSubConfig.getProduct()).thenReturn(this.initTierProductSubList(TENANTID_VICOO,request.getApp().getAppBuild(),APP_LANGUAGE_EN,tierId));

        List<TierProduct> expectedResult = Lists.newArrayList();
        List<TierProduct> actualResult = tierService.querySubList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("配置套餐商品-bundle error")
    public void test_querySubList_productConfig_bundle(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        app.setAppBuild(bundle);
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setApp(app);
        Integer tierId = 1;

        when(tierProductSubConfig.getProduct()).thenReturn(this.initTierProductSubList(TENANTID_VICOO,request.getApp().getAppBuild()+"test",APP_LANGUAGE_ZH,tierId));

        List<TierProduct> expectedResult = Lists.newArrayList();
        List<TierProduct> actualResult = tierService.querySubList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("配置套餐商品-language error")
    public void test_querySubList_productConfig_language(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        app.setAppBuild(bundle);
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setApp(app);
        Integer tierId = 1;

        when(tierProductSubConfig.getProduct()).thenReturn(this.initTierProductSubList(TENANTID_VICOO,request.getApp().getAppBuild(),APP_LANGUAGE_EN,tierId));

        List<TierProduct> expectedResult = Lists.newArrayList();
        List<TierProduct> actualResult = tierService.querySubList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("配置套餐商品-tier error")
    public void test_querySubList_productConfig_tier(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        app.setAppBuild(bundle);
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setApp(app);
        Integer tierId = 1;

        when(tierProductSubConfig.getProduct()).thenReturn(this.initTierProductSubList(TENANTID_VICOO,request.getApp().getAppBuild(),APP_LANGUAGE_ZH,2));

        List<TierProduct> expectedResult = Lists.newArrayList();
        List<TierProduct> actualResult = tierService.querySubList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("配置套餐商品")
    public void test_querySubList_productConfig(){
        String bundle = "bundle";
        TierListRequest request = new TierListRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_GUARD);
        app.setAppBuild(bundle);
        request.setLanguage(APP_LANGUAGE_ZH);
        request.setApp(app);
        Integer tierId = 1;

        when(tierProductSubConfig.getProduct()).thenReturn(this.initTierProductSubList(TENANTID_GUARD,request.getApp().getAppBuild(),APP_LANGUAGE_ZH,tierId));
        when(supportProductConfig.querySupportProductType(any())).thenReturn(new HashSet<>(Arrays.asList(0,1)));

        List<TierProduct> expectedResult = Lists.newArrayList();
        expectedResult.add(new TierProduct());
        List<TierProduct> actualResult = tierService.querySubList(tierId,request,bundle);
        assertEquals(expectedResult,actualResult);
    }

    private Map<String, Map<String, Map<String, Map<String, List<TierProduct>>>>> initTierProductSubList(String tenantId,String bundle,String language,Integer tierId){
        Map<String, Map<String, Map<String, Map<String, List<TierProduct>>>>> tenantConfig = new HashMap<>();

        Map<String, Map<String, Map<String, List<TierProduct>>>> bundleMap = Maps.newHashMap();

        Map<String, Map<String, List<TierProduct>>> languageMap = Maps.newHashMap();

        Map<String, List<TierProduct>> tierMap = Maps.newHashMap();

        List<TierProduct> tierProductList = Lists.newArrayList();
        TierProduct tierProduct = new TierProduct();
        tierProductList.add(tierProduct);

        tierMap.put(String.valueOf(tierId),tierProductList);
        languageMap.put(language,tierMap);
        bundleMap.put(bundle,languageMap);
        tenantConfig.put(tenantId,bundleMap);

        return tenantConfig;
    }


    @Test
    public void test_queryTierListV2() throws NoSuchFieldException {
        Field noBindCameraAfter48hHtmlBodyMapField = tierService.getClass().getDeclaredField("servernode");



        TierListRequest request = new TierListRequest();
        request.setLanguage(APP_LANGUAGE_ZH);
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        app.setBundle("com.ai.addx.guard");
        app.setAppType("iOS");
        request.setApp(app);

        Map<String, Map<String, List<TierInfo>>> tenantMap = Maps.newHashMap();
        Map<String, List<TierInfo>> languageMap = Maps.newHashMap();
        List<TierInfo> tierInfoList = Lists.newArrayList();
        TierInfo tierInfo = new TierInfo();
        tierInfoList.add(tierInfo);
        languageMap.put(APP_LANGUAGE_ZH,tierInfoList);
        tenantMap.put(TENANTID_VICOO,languageMap);

        when(tierInfoConfig.getTierInfos(any(),any())).thenReturn(tierInfoList);

        List<TierInfo> expectedResult = Lists.newArrayList();
        List<TierInfo> actualResult = tierService.queryTierListV2(request);
        assertEquals(expectedResult,actualResult);
    }

    @Test
    public void testInitTierListV2_productList_empty() {
        when(productService.queryProductListByType(any(),any(), anyBoolean(), any())).thenReturn(Lists.newArrayList());
        TierProductDeviceNumDO expectResult = new TierProductDeviceNumDO();

        TierProductDeviceNumDO actualResult = tierService.initTierListV2(TIER_CLOID_SERVICE, TENANTID_VICOO, false, 0);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    public void testInitTierListV2_TIER_4G_productList_empty() {
        when(productService.queryProductListByType(any(),any(), anyBoolean(), any())).thenReturn(Lists.newArrayList());
        TierProductDeviceNumDO expectResult = new TierProductDeviceNumDO();
        TierProductDeviceNumDO actualResult = tierService.initTierListV2(TIER_4G, TENANTID_VICOO, false, 0);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test(expected = BaseException.class)
    public void testInitTierListV2_TIER_DEFAULT_productList_empty() {
        when(productService.queryProductListByType(any(),any(), anyBoolean(), any())).thenReturn(Lists.newArrayList());
        TierProductDeviceNumDO expectResult = new TierProductDeviceNumDO();
        TierProductDeviceNumDO actualResult = tierService.initTierListV2(DEFAULT, TENANTID_VICOO, false, 0);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    public void testInitTierListV2() {
        // Arrange
        String tenantId = "test-tenant";

        ProductDO product1 = new ProductDO();
        product1.setId(1);
        product1.setType(PRODUCT_DEVICE_NUM.getCode());
        product1.setTierId(1);
        product1.setSubscriptionPeriod(0);
        product1.setShowInTier(1);

        ProductDO product2 = new ProductDO();
        product2.setId(2);
        product2.setType(PRODUCT_DEVICE_NUM.getCode());
        product2.setTierId(2);
        product2.setSubscriptionPeriod(1);
        product2.setShowInTier(1);

        ProductDO product3 = new ProductDO();
        product3.setId(3);
        product3.setType(PRODUCT_DEVICE_NUM.getCode());
        product3.setTierId(3);
        product3.setSubscriptionPeriod(1);
        product3.setShowInTier(1);




        ProductDO product4 = new ProductDO();
        product4.setId(4);
        product4.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product4.setTierId(4);
        product4.setSubscriptionPeriod(0);
        product4.setShowInTier(1);

        ProductDO product5 = new ProductDO();
        product5.setId(5);
        product5.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product5.setTierId(5);
        product5.setSubscriptionPeriod(1);
        product5.setShowInTier(1);


        ProductDO product6 = new ProductDO();
        product6.setId(6);
        product6.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product6.setTierId(6);
        product6.setSubscriptionPeriod(1);
        product6.setShowInTier(1);

        List<ProductDO> productList = Arrays.asList(product1, product2, product3, product4,product5,product6);
        when(productService.queryProductListByType(Arrays.asList(PRODUCT_DEVICE_NUM.getCode(), PRODUCT_LEVEL_DEVICE_NUM.getCode()), tenantId, false, 0)).thenReturn(productList);


        Tier tier1 = new Tier();
        tier1.setTierId(1);
        tier1.setMaxDeviceNum(1);
        tier1.setRollingDays(30);
        tier1.setLevel(1);

        Tier tier2 = new Tier();
        tier2.setTierId(2);
        tier2.setMaxDeviceNum(null);
        tier2.setRollingDays(30);
        tier2.setLevel(2);


        Tier tier4 = new Tier();
        tier4.setTierId(4);
        tier4.setMaxDeviceNum(2);
        tier4.setRollingDays(30);
        tier4.setLevel(1);

        Tier tier5 = new Tier();
        tier5.setTierId(5);
        tier5.setMaxDeviceNum(null);
        tier5.setRollingDays(30);
        tier5.setLevel(2);

        List<Tier> tierList = Arrays.asList(tier1, tier2,tier4,tier5);

        when(tierDao.getTierByTierIds(any())).thenReturn(tierList);

        // Act
        TierProductDeviceNumDO result = tierService.initTierListV2(TIER_CLOID_SERVICE,tenantId, false, 0);

        // Assert
        assertEquals(1, result.getTierV2List().getMonthlyProductList().size());
        assertEquals(1, result.getTierV2List().getYearlyProductList().size());

        assertEquals(2, result.getTierV1List().getMonthlyProductList().size());
        assertEquals(0, result.getTierV1List().getYearlyProductList().size());
    }

    @Test
    public void testInitTierListV2_tier4G() {
        // Arrange
        String tenantId = "test-tenant";

        ProductDO product1 = new ProductDO();
        product1.setId(1);
        product1.setType(PRODUCT_DEVICE_4G.getCode());
        product1.setTierId(1);
        product1.setSubscriptionPeriod(0);
        product1.setShowInTier(1);

        ProductDO product2 = new ProductDO();
        product2.setId(2);
        product2.setType(PRODUCT_DEVICE_4G.getCode());
        product2.setTierId(2);
        product2.setSubscriptionPeriod(1);
        product2.setShowInTier(1);

        ProductDO product3 = new ProductDO();
        product3.setId(3);
        product3.setType(PRODUCT_DEVICE_4G.getCode());
        product3.setTierId(3);
        product3.setSubscriptionPeriod(1);
        product3.setShowInTier(1);

        List<ProductDO> productList = Arrays.asList(product1, product2, product3);
        when(productService.queryProductListByType(Arrays.asList(PRODUCT_DEVICE_4G.getCode()), tenantId, false, 0)).thenReturn(productList);


        Tier tier1 = new Tier();
        tier1.setTierId(1);
        tier1.setMaxDeviceNum(1);
        tier1.setRollingDays(30);
        tier1.setLevel(1);

        Tier tier2 = new Tier();
        tier2.setTierId(2);
        tier2.setMaxDeviceNum(null);
        tier2.setRollingDays(30);
        tier2.setLevel(2);


        Tier tier4 = new Tier();
        tier4.setTierId(4);
        tier4.setMaxDeviceNum(2);
        tier4.setRollingDays(30);
        tier4.setLevel(1);

        Tier tier5 = new Tier();
        tier5.setTierId(5);
        tier5.setMaxDeviceNum(null);
        tier5.setRollingDays(30);
        tier5.setLevel(2);

        List<Tier> tierList = Arrays.asList(tier1, tier2,tier4,tier5);

        when(tierDao.getTierByTierIds(any())).thenReturn(tierList);

        // Act
        TierProductDeviceNumDO result = tierService.initTierListV2(TIER_4G,tenantId, false, 0);

        // Assert
        assertEquals(1, result.getTierV2List().getMonthlyProductList().size());
        assertEquals(1, result.getTierV2List().getYearlyProductList().size());
    }


    @Test
    public void testHasSupportBirdDevice() {
        UserRoleDO userRole1 = new UserRoleDO();
        userRole1.setSerialNumber("123456789");
        UserRoleDO userRole2 = new UserRoleDO();
        userRole2.setSerialNumber("987654321");

        List<UserRoleDO> userRoleDOList = Arrays.asList(userRole1, userRole2);
        when(userRoleService.getUserRoleByUserId(any(),any())).thenReturn(userRoleDOList);

        when(deviceManualService.getModelNoBySerialNumber("123456789")).thenReturn("ABC123");
        when(deviceModelEventService.queryRowDeviceModelEvent("ABC123")).thenReturn(new HashSet<>(Arrays.asList("event1", "event2", AiObjectEnum.BIRD.getObjectName())));

        when(deviceManualService.getModelNoBySerialNumber("987654321")).thenReturn("DEF456");
        when(deviceModelEventService.queryRowDeviceModelEvent("DEF456")).thenReturn(new HashSet<>(Arrays.asList("event3", "event4")));

        // Act
        boolean result = tierService.hasSupportBirdDevice(1,Lists.newArrayList());

        // Assert
        assertTrue(result);
    }

    @Test
    public void testHasSupportBirdDeviceWithEmptyUserRole() {
        when(userRoleService.getUserRoleByUserId(any(),any())).thenReturn(Lists.newArrayList());

        // Act
        boolean result = tierService.hasSupportBirdDevice(1,Lists.newArrayList());

        // Assert
        assertFalse(result);
    }

    @Test
    @DisplayName("推荐商品-月订阅-已领取过")
    public void test_queryRecommendProductV1_hasReceive(){
        List<UserVipTier.RecommendProduct> expectResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = tierService.queryRecommendProductV1(true,new AppRequestBase(),1);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("推荐商品-月订阅-已领取过")
    public void test_queryRecommendProductV1_hasReceive_4G(){
        List<UserVipTier.RecommendProduct> expectResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = tierService.queryRecommendProductV1(true,new AppRequestBase(),1);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("推荐商品-年订阅")
    public void test_queryRecommendProductYearList(){
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        request.setApp(app);
        Integer userId = 1;

        List<UserVipTier.RecommendProduct> expectedResult;
        List<UserVipTier.RecommendProduct> actualResult;

        {
            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            expectedResult = Lists.newArrayList();
            actualResult = tierService.queryRecommendProductYearList(false,request, 1);
            Assert.assertEquals(expectedResult,actualResult);
        }
        {
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            when(appRecommendProductConfig.queryRecommendProductIdYear(any(),any())).thenReturn(Collections.singletonList(new UserVipTier.RecommendProduct()));
            expectedResult = Collections.singletonList(new UserVipTier.RecommendProduct());
            actualResult = tierService.queryRecommendProductYearList(false,request, 1);
            Assert.assertEquals(expectedResult,actualResult);
        }
        {
            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(true).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            when(appRecommendProductConfig.queryRecommendProductIdYear(any(),any())).thenReturn(Collections.singletonList(new UserVipTier.RecommendProduct()));
            expectedResult = new ArrayList<>();
            actualResult = tierService.queryRecommendProductYearList(false,request, 1);
            Assert.assertEquals(expectedResult,actualResult);
        }

        {
            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            when(appRecommendProductConfig.queryRecommendProductIdYear(any(),any())).thenReturn(Collections.singletonList(new UserVipTier.RecommendProduct()));
            expectedResult = new ArrayList<>();
            actualResult = tierService.queryRecommendProductYearList(false,request, 1);
            Assert.assertEquals(expectedResult,actualResult);
        }

        {
            when (abTestService.getFreeLicenseAbResult(userId, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(true)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            when(appRecommendProductConfig.queryRecommendProductIdYear(any(),any())).thenReturn(Collections.singletonList(new UserVipTier.RecommendProduct()));
            expectedResult = new ArrayList<>();
            actualResult = tierService.queryRecommendProductYearList(false,request, 1);
            Assert.assertEquals(expectedResult,actualResult);
        }

    }

    @Test
    @DisplayName("推荐商品-未领取-月订阅")
    public void test_queryRecommendProductV1_noReceive() {
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        app.setAppType("iOS");
        request.setApp(app);

        List<UserVipTier.RecommendProduct> expectedResult = new ArrayList<>();
        expectedResult.add(new UserVipTier.RecommendProduct());
        when(appRecommendProductConfig.queryRecommendProductIdV1(any(), any())).thenReturn(expectedResult);
        when (abTestService.getFreeLicenseAbResult(1, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                .build());
        when (abTestService.getAwarenessFreeTrailDayAbResult(1, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build());
        List<UserVipTier.RecommendProduct> actualResult = tierService.queryRecommendProductV1(false, request, 1);
        Assert.assertEquals(expectedResult, actualResult);
        verify(appRecommendProductConfig).queryRecommendProductIdV1("vicoo", "iOS");
    }

    @Test
    public void test_queryRecommendProductV1_7freetrial() {
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        app.setAppType("iOS");
        request.setApp(app);

        List<UserVipTier.RecommendProduct> expectedResult = new ArrayList<>();
        when(appRecommendProductConfig.queryRecommendProductIdV1(any(), any())).thenReturn(expectedResult);
        {
            when(abTestService.getFreeLicenseAbResult(1, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(true).isFreeTrial(false)
                    .build());
            when(abTestService.getAwarenessFreeTrailDayAbResult(1, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            List<UserVipTier.RecommendProduct> actualResult = tierService.queryRecommendProductV1(false, request, 1);
            Assert.assertEquals(expectedResult, actualResult);
        }

        {
            when(abTestService.getFreeLicenseAbResult(1, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when(abTestService.getAwarenessFreeTrailDayAbResult(1, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit0DayExperimentGroup(false)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            List<UserVipTier.RecommendProduct> actualResult = tierService.queryRecommendProductV1(false, request, 1);
            Assert.assertEquals(expectedResult, actualResult);
        }

        {
            when(abTestService.getFreeLicenseAbResult(1, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when(abTestService.getAwarenessFreeTrailDayAbResult(1, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(true)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            List<UserVipTier.RecommendProduct> actualResult = tierService.queryRecommendProductV1(false, request, 1);
            Assert.assertEquals(expectedResult, actualResult);
        }

        {
            when(abTestService.getFreeLicenseAbResult(1, request)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when(abTestService.getAwarenessFreeTrailDayAbResult(1, request)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(true)
                    .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            List<UserVipTier.RecommendProduct> actualResult = tierService.queryRecommendProductV1(false, request, 1);
            Assert.assertEquals(expectedResult, actualResult);
        }
    }

    @Test
    @DisplayName("推荐商品-4G产品")
    public void test_queryRecommendProduct4G() {
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        app.setAppType("iOS");
        request.setApp(app);

        List<UserVipTier.RecommendProduct> expectedResult = new ArrayList<>();
        expectedResult.add(new UserVipTier.RecommendProduct());
        when(appRecommendProductConfig.queryRecommendProduct4G(any(), any())).thenReturn(expectedResult);

        List<UserVipTier.RecommendProduct> actualResult = tierService.queryRecommendProduct4G(request);
        Assert.assertEquals(expectedResult, actualResult);
        verify(appRecommendProductConfig).queryRecommendProduct4G("vicoo", "iOS");
    }

    @Test
    public void test_queryRecommendProduct4GAndroid() {
        AppRequestBase request = new AppRequestBase();
        AppInfo app = new AppInfo();
        app.setTenantId("guard");
        app.setAppType("android");
        request.setApp(app);

        List<UserVipTier.RecommendProduct> expectedResult = new ArrayList<>();
        expectedResult.add(new UserVipTier.RecommendProduct());
        when(appRecommendProductConfig.queryRecommendProduct4G(any(), any())).thenReturn(expectedResult);
    }

    @Test
    @DisplayName("查询套餐名称key-套餐为空")
    public void testQueryTierNameWithNullTier() {
        ProductDO productDO = new ProductDO();
        productDO.setTierId(1);
        when(tierService.queryTierById(productDO.getTierId())).thenReturn(null);
        String result = tierService.queryTierName(productDO, APP_LANGUAGE_ZH,"");
        Assert.assertEquals("", result);
    }

    @Test
    public void testQueryTierNameWithValidTier() {
        ProductDO productDO = new ProductDO();
        productDO.setTierId(1);

        Tier tier = new Tier();

        when(tierService.queryTierById(productDO.getTierId())).thenReturn(tier);
        when(tenantTierConfig.queryAppNameByTenant(any())).thenReturn(tierNameVicoHome);
        when(copyWrite.getConfig()).thenReturn(this.initCopywriteConfig());
        {
            //v2-单设备
            tier.setTierType(TierTypeEnums.TIER_LEVEL.getCode());
            tier.setLevel(3);
            String actualResult = tierService.queryTierName(productDO, APP_LANGUAGE_ZH, TENANTID_VICOO);
            Assert.assertEquals("plan_pro", actualResult);
        }
        {
            tier.setTierType(TierTypeEnums.TIER_4GDATA.getCode());
            //v2-单设备
            tier.setTierServiceType(TIER_4G.getCode());
            tier.setMaxDeviceNum(1);
            String actualResult = tierService.queryTierName(productDO, APP_LANGUAGE_ZH, TENANTID_VICOO);
            Assert.assertEquals("", actualResult);
        }
        tier.setTierServiceType(TIER_CLOID_SERVICE.getCode());
        {
            //v2-单设备
            tier.setTierType(TierTypeEnums.TIER_DEVICE_LIMIT.getCode());
            tier.setMaxDeviceNum(1);
            when(appTierKeyTranslatedConfig.queryAppTierTranslatedKey(any(),any())).thenReturn("tier_message_name_monkeyvision_one_device");

            String actualResult = tierService.queryTierName(productDO, APP_LANGUAGE_ZH, TENANTID_VICOO);
            Assert.assertEquals(TIER_MONKEY_ONE_DEVICE, actualResult);
        }
//        {
//            //v2-双设备
//            productDO.setType(PRODUCT_DEVICE_NUM.getCode());
//            tier.setMaxDeviceNum(2);
//            String actualResult = tierService.queryTierName(productDO, APP_LANGUAGE_ZH,tenantIdVicoo);
//            Assert.assertEquals(TIER_MONKEY_TWO_DEVICE, actualResult);
//        }
//        {
//            //v2-无限设备
//            productDO.setType(PRODUCT_DEVICE_NUM.getCode());
//            tier.setMaxDeviceNum(null);
//            String actualResult = tierService.queryTierName(productDO, APP_LANGUAGE_ZH,tenantIdVicoo);
//            Assert.assertEquals(TIER_MONKEY_NOLIMIT_DEVICE, actualResult);
//        }
//
//        {
//            //v1-基础版-单设备
//            productDO.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
//            tier.setMaxDeviceNum(1);
//            tier.setLevel(TierLevelEnums.BASIC.getId());
//            String actualResult = tierService.queryTierName(productDO, APP_LANGUAGE_ZH,tenantIdVicoo);
//            Assert.assertEquals(TIER_V1_BASIC_ONE_DEVICE, actualResult);
//        }
//        {
//            //v1-基础版-双设备
//            productDO.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
//            tier.setMaxDeviceNum(2);
//            tier.setLevel(TierLevelEnums.BASIC.getId());
//            String actualResult = tierService.queryTierName(productDO, APP_LANGUAGE_ZH,tenantIdVicoo);
//            Assert.assertEquals(TIER_V1_BASIC_TWO_DEVICE, actualResult);
//        }
//        {
//            //v1-高级版-单设备
//            productDO.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
//            tier.setMaxDeviceNum(1);
//            tier.setLevel(TierLevelEnums.PLUS.getId());
//            String actualResult = tierService.queryTierName(productDO, APP_LANGUAGE_ZH,tenantIdVicoo);
//            Assert.assertEquals(TIER_V1_PLUS_ONE_DEVICE, actualResult);
//        }
//        {
//            //v1-高级版-双设备
//            productDO.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
//            tier.setMaxDeviceNum(2);
//            tier.setLevel(TierLevelEnums.PLUS.getId());
//            String actualResult = tierService.queryTierName(productDO, APP_LANGUAGE_ZH,tenantIdVicoo);
//            Assert.assertEquals(TIER_V1_PLUS_TWO_DEVICE, actualResult);
//        }
    }

    private Map<String, Map<String, String>> initCopywriteConfig(){
        Map<String, Map<String, String>> config = Maps.newHashMap();

        Map<String, String> v2OneDeviceMap = Maps.newHashMap();
        v2OneDeviceMap.put(APP_LANGUAGE_ZH,"tier_message_name_monkeyvision_one_device");
        config.put(TIER_MONKEY_ONE_DEVICE,v2OneDeviceMap);

        Map<String, String> v2TwoDeviceMap = Maps.newHashMap();
        v2TwoDeviceMap.put(APP_LANGUAGE_ZH,"tier_message_name_monkeyvision_two_device");
        config.put(TIER_MONKEY_TWO_DEVICE,v2TwoDeviceMap);

        Map<String, String> v2NoLimitDeviceMap = Maps.newHashMap();
        v2NoLimitDeviceMap.put(APP_LANGUAGE_ZH,"tier_message_name_monkeyvision_nolimit_device");
        config.put(TIER_MONKEY_NOLIMIT_DEVICE,v2NoLimitDeviceMap);

        Map<String, String> v1BasicOneDeviceMap = Maps.newHashMap();
        v1BasicOneDeviceMap.put(APP_LANGUAGE_ZH,"tier_message_name_basic_one_device");
        config.put(TIER_V1_BASIC_ONE_DEVICE,v1BasicOneDeviceMap);

        Map<String, String> v1PlusOneDeviceMap = Maps.newHashMap();
        v1PlusOneDeviceMap.put(APP_LANGUAGE_ZH,"tier_message_name_plus_one_device");
        config.put(TIER_V1_PLUS_ONE_DEVICE,v1PlusOneDeviceMap);

        Map<String, String> v1BasicTwoDeviceMap = Maps.newHashMap();
        v1BasicTwoDeviceMap.put(APP_LANGUAGE_ZH,"tier_message_name_basic_two_device");
        config.put(TIER_V1_BASIC_TWO_DEVICE,v1BasicTwoDeviceMap);

        Map<String, String> v1PlusTwoDeviceMap = Maps.newHashMap();
        v1PlusTwoDeviceMap.put(APP_LANGUAGE_ZH,"tier_message_name_plus_two_device");
        config.put(TIER_V1_PLUS_TWO_DEVICE,v1PlusTwoDeviceMap);


        return config;
    }

    @Test
    public void test_queryAdditionalTierInfoList() {
        Integer userId = 1;
        String tenantId = "homeguard";

        List<AdditionalTierDO> additionalTierDOList = Collections.singletonList(new AdditionalTierDO());
        when(additionalTierDao.getAllTier(tenantId)).thenReturn(additionalTierDOList);

        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setSerialNumber("serial123");
        List<UserRoleDO> userRoleByUserId = Collections.singletonList(userRoleDO);
        when(userRoleService.getUserRoleByUserId(userId)).thenReturn(userRoleByUserId);

        when(vipService.isVipDevice(userId, "serial123")).thenReturn(true);

        when(deviceManualService.getModelNoBySerialNumber("serial123")).thenReturn("model123");

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setSerialNumber("serial123");
        when(deviceService.getAllDeviceInfo("serial123")).thenReturn(deviceDO);

        DeviceModelIconDO deviceModelIconDO = new DeviceModelIconDO();
        deviceModelIconDO.setIconUrl("iconUrl");
        deviceModelIconDO.setSmallIconUrl("smallIconUrl");
        when(deviceModelIconService.queryDeviceModelIcon("model123")).thenReturn(deviceModelIconDO);

        ProductDO productDO = new ProductDO();
        productDO.setId(100);
        when(productDAO.queryAdditionalProductFree(tenantId)).thenReturn(productDO);

        List<AdditionalTierInfo> result = tierService.queryAdditionalTierInfoList(userId, tenantId);

        assertNotNull(result);
        assertEquals(1, result.size());
        AdditionalTierInfo additionalTierInfo = result.get(0);
        assertNotNull(additionalTierInfo.getSupportDeviceList());
    }

    @Test
    public void testProcessProduct4GResult() {
        {
            Integer userId = 1;
            AppRequestBase request = new AppRequestBase();
            Product4GABTestResult result = new Product4GABTestResult();
            result.setExperimentSuccessful(true);
            result.setBillingCycleDuration(DURATION_HALF_YEAR);

            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(result);

            Map<String, Object> tierMap = new HashMap<>();
            tierService.processProduct4GResult(tierMap, userId, request);

            assert tierMap.containsKey("commendProduct4GHalfYearList");
            assert tierMap.containsKey("abFeatureSimpleResultList");
        }

        {
            Integer userId = 1;
            AppRequestBase request = new AppRequestBase();
            Product4GABTestResult result = new Product4GABTestResult();
            result.setExperimentSuccessful(true);
            result.setBillingCycleDuration(DURATION_ONE_YEAR);

            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(result);

            Map<String, Object> tierMap = new HashMap<>();
            tierService.processProduct4GResult(tierMap, userId, request);

            assert tierMap.containsKey("commendProduct4GYearList");
            assert tierMap.containsKey("abFeatureSimpleResultList");
        }

        {
            Integer userId = 1;
            AppRequestBase request = new AppRequestBase();
            Product4GABTestResult result = new Product4GABTestResult();
            result.setExperimentSuccessful(true);

            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(result);

            Map<String, Object> tierMap = new HashMap<>();
            tierService.processProduct4GResult(tierMap, userId, request);

            assert tierMap.containsKey("abFeatureSimpleResultList");
        }

        {
            Integer userId = 1;
            AppRequestBase request = new AppRequestBase();
            Product4GABTestResult result = new Product4GABTestResult();
            result.setExperimentSuccessful(false);

            when(abTestService.getProduct4GABTestResult(userId, request)).thenReturn(result);

            Map<String, Object> tierMap = new HashMap<>();
            tierService.processProduct4GResult(tierMap, userId, request);

            assertFalse( tierMap.containsKey("abFeatureSimpleResultList"));
        }
    }

    @Test
    public void test_initTierNameKey(){
        Tier tier = Tier.builder().build();
        String tenantId = "";
        String expectedResult = "";
        String actualResult = "";
        {
            tier.setTierType(0);
            tier.setLevel(1);
            tier.setMaxDeviceNum(1);
            tenantId = TENANTID_VICOO;
            expectedResult = "plan_basic";
            actualResult = tierService.initTierNameKey(tier,tenantId);
            Assert.assertEquals(expectedResult,actualResult);
        }
    }
}
