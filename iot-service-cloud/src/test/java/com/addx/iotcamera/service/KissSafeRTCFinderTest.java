package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.device.DeviceServerAllocDO;
import com.addx.iotcamera.kiss.KissFinder;
import com.addx.iotcamera.kiss.bean.ChooseNodeCondition;
import com.addx.iotcamera.kiss.helper.NodeChooseHelper;
import com.addx.iotcamera.kiss.KissNodeType;
import com.addx.iotcamera.kiss.node.KissNode;
import com.addx.iotcamera.service.device.DeviceServerAllocService;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.util.Assert.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class KissSafeRTCFinderTest {

    @InjectMocks
    private KissFinder kissFinder;
    @Mock
    private CuratorFramework curatorFramework;
    @Mock
    private NodeChooseHelper nodeChooseHelper;
    @Mock
    private KissWsService kissSafeRTCWsService;
    @Mock
    private DeviceServerAllocService deviceServerAllocService;

    @Test
    public void test_chooseWebsocketNodeBNAndCountryNo() {
        when(deviceServerAllocService.queryBySn(anyString())).thenAnswer(it -> {
            return new DeviceServerAllocDO().setSn(it.getArgument(0));
        });
        KissNode kissNode = new KissNode() {{
            setExtranetIp4("*******");
            setUdpPort(1234);
            setDomain("kiss.a4x.ai");
            setCountryNoSet(Collections.singleton("US"));
            setRemoving(0);
        }};
        final ChooseNodeCondition condition = new ChooseNodeCondition().setCountryNo("US").setPurpose("normal");
        when(nodeChooseHelper.chooseNodes(kissFinder.getNodeMapByType(KissNodeType.kissSafeRtc), condition)).thenReturn(Arrays.asList(kissNode));
        {
            KissNode result = kissFinder.chooseWebsocketNodeBNAndCountryNo("sn1", condition);
            Assert.assertEquals(kissNode, result);
        }
        {
            final ChooseNodeCondition condition2 = new ChooseNodeCondition().setCountryNo("US").setPurpose("tcpdump");
            AssertUtil.assertThrowException(()-> kissFinder.chooseWebsocketNodeBNAndCountryNo("sn1", condition2));
        }

    }

}
