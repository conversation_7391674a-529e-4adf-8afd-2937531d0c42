package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.*;
import com.addx.iotcamera.bean.domain.HttpTokenDO;
import com.addx.iotcamera.bean.domain.LoginResponseDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.config.AppUserConfig;
import com.addx.iotcamera.config.app.EmailPayConfig;
import com.addx.iotcamera.config.app.TenantFreeTierConfig;
import com.addx.iotcamera.enums.LoginTypeEnums;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.user.UserAppScoreService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.aliyuncs.exceptions.ClientException;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;

import static org.addx.iot.common.constant.AppConstants.TENANTID_GUARD;
import static org.addx.iot.common.enums.ResultCollection.NO_CONTACT;
import static org.addx.iot.common.enums.ResultCollection.USER_ACCOUNT_TRANSFER;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class LoginServiceTest {

    @InjectMocks
    private LoginService loginService;
    @Mock
    private MailConfirmService mailConfirmService;
    @Mock
    private ForceChangePwdService forceChangePwdService;
    @Mock
    private MailConfirmRequest confirmRequest;
    @Mock
    private LoginRequest loginRequest;
    @Mock
    private UserService userService;
    @Mock
    private RegisterRequest registerRequest;
    @Mock
    private PushService pushService;
    @Mock
    private User user;
    @Mock
    private TokenService tokenService;
    @Mock
    private MailConfirmRequest mailConfirmRequest;
    @Mock
    private LoginRequest registerOrLoginRequest;
    @Mock
    private HttpTokenDO httpTokenDO;
    @Mock
    private AppRequestBase appRequestBase;
    @Mock
    private JwtHelper jwtHelper;
    @Mock
    private RedisService redisService;
    @Mock
    private AppInfoService appInfoService;
    @Mock
    private TenantFreeTierConfig tenantFreeTierConfig;
    @Mock
    private AppUserConfig appUserConfig;
    @Mock
    private UserVipActivateService userVipActivateService;
    @Mock
    private EmailPayConfig emailPayConfig;
    @Mock
    private MqSender mqSender;
    @Mock
    private UserSettingService userSettingService;
    @Mock
    private UserAppScoreService userAppScoreService;

    @Before
    public void before() {
        when(userService.buildLoginResponseDO(any(), any())).thenAnswer(it -> {
            final User stored = it.getArgument(0);
            final HttpTokenDO httpTokenDO = it.getArgument(1);

            LoginResponseDO responseDO = new LoginResponseDO();
            responseDO.setId(stored.getId());
            responseDO.setName(stored.getName());
            responseDO.setEmail(stored.getEmail());
            responseDO.setPhone(stored.getPhone());
            // 更新masToken
            responseDO.setToken(httpTokenDO);
            responseDO.setNode("prod-us");
            // 给app下发访问其它服务的token
            responseDO.setTrackerToken("trackerToken");
            responseDO.setSafePushToken("safePushToken");
            responseDO.setSafePushEndpoint("http://safepush");
            return responseDO;
        });
        when(forceChangePwdService.getForceResetPwdFlag(any())).thenReturn(false);
        when(forceChangePwdService.newPwdNotChange(any(), any())).thenReturn(false);
        when(forceChangePwdService.sendEmail(any(), any(), any())).thenReturn(false);
        when(forceChangePwdService.deleteForceChangePwdFlag(any())).thenReturn(false);
    }

    @Test
    public void testLoginWithUserNotExist() throws ClientException {
        when(loginRequest.getEmail()).thenReturn("<EMAIL>");
        when(loginRequest.getCode()).thenReturn("");
        when(loginRequest.getPassword()).thenReturn("123456");
        AppInfo appInfo = new AppInfo();
        when(loginRequest.getApp()).thenReturn(appInfo);
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(null);

        Result expectedResult = Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");

        Result actualResult = loginService.login(loginRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testLoginPasswordAndCodeBothNull() throws ClientException {
        when(loginRequest.getCode()).thenReturn("");
        when(loginRequest.getPassword()).thenReturn("");
        AppInfo appInfo = new AppInfo();
        when(loginRequest.getApp()).thenReturn(appInfo);
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(null);

        Result expectedResult = Result.Error(-102, "INVALID_PARAMS");

        Result actualResult = loginService.login(loginRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testLoginPhoneAndEmailBothNull() throws ClientException {
        when(loginRequest.getCode()).thenReturn("");
        when(loginRequest.getPassword()).thenReturn("123456");
        AppInfo appInfo = new AppInfo();
        when(loginRequest.getApp()).thenReturn(appInfo);
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(null);

        Result expectedResult = Result.Error(-102, "INVALID_PARAMS");

        Result actualResult = loginService.login(loginRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testLoginWithWrongPassword() throws ClientException {
        when(loginRequest.getEmail()).thenReturn("<EMAIL>");
        when(loginRequest.getPassword()).thenReturn("123");

        User stored = new User();
        stored.setEmail("<EMAIL>");
        stored.setSalt("salt");
        stored.setHashedPassword("123");

        AppInfo appInfo = new AppInfo();
        when(loginRequest.getApp()).thenReturn(appInfo);
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(stored);

        Result expectedResult = Result.Error(-1021, "WRONG_PASSWORD");

        Result actualResult = loginService.login(loginRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testLoginSuccess() throws Exception {
        String loginRequestPassword = "123";
        AppInfo appInfo = new AppInfo();
        appInfo.setBundle("addx");
        appInfo.setAppType("Android");
        appInfo.setEnv("local");
        when(loginRequest.getEmail()).thenReturn("<EMAIL>");
        when(loginRequest.getPassword()).thenReturn(loginRequestPassword);
        when(loginRequest.getApp()).thenReturn(appInfo);
        when(loginRequest.getLoginType()).thenReturn(0);
        when(redisService.hasDeviceOperationDo(any())).thenReturn(false);

        User stored = new User();
        stored.setEmail("<EMAIL>");
        stored.setSalt("salt");
        stored.setHashedPassword(PhosUtils.CalcSalt(loginRequestPassword, stored.getSalt()));
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(stored);

        doNothing().when(tokenService).setUserToken(any());

        Result actualResult = loginService.login(loginRequest);
        assertEquals(LoginResponseDO.class, actualResult.getData().getClass());

        Field profileField = loginService.getClass().getDeclaredField("activeProfile");
        profileField.setAccessible(true);
        profileField.set(loginService, "local-cn");
        actualResult = loginService.login(loginRequest);
        assertEquals(LoginResponseDO.class, actualResult.getData().getClass());

        appInfo.setEnv("test");
        actualResult = loginService.login(loginRequest);
        assertEquals(LoginResponseDO.class, actualResult.getData().getClass());
    }

    @Test
    public void testRegisterWithAccountInUse() {
        when(registerRequest.getEmail()).thenReturn("<EMAIL>");
        User storedUser = new User();
        storedUser.setEmail("<EMAIL>");
        AppInfo appInfo = new AppInfo();
        when(registerRequest.getApp()).thenReturn(appInfo);
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(storedUser);
        Result expectedResult = Result.Error(-1002, "ACCOUNT_IN_USE");

        Result actualResult = loginService.register(registerRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testRegisterWithInvalidPassword() {
        when(registerRequest.getEmail()).thenReturn("<EMAIL>");
        AppInfo appInfo = new AppInfo();
        when(registerRequest.getApp()).thenReturn(appInfo);
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(null);
        //mock非法密码
        when(registerRequest.getPassword()).thenReturn("");
        Result expectedResult = Result.Error(-1012, "INVALID_PASSWORD");

        Result actualResult = loginService.register(registerRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testRegisterNewUser() {
        when(registerRequest.getEmail()).thenReturn("<EMAIL>");
        AppInfo appInfo = new AppInfo();
        when(registerRequest.getApp()).thenReturn(appInfo);
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(null);
        //mock密码
        when(registerRequest.getPassword()).thenReturn("!qweQWE123");
        when(mailConfirmService.mailConfirm(any(), anyString(), any())).thenReturn(Result.Success());
        Result expectedResult = Result.Success();

        Result actualResult = loginService.register(registerRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testResetPasswordWithUserNotExist() {
        when(registerRequest.getEmail()).thenReturn("<EMAIL>");
        AppInfo appInfo = new AppInfo();
        when(registerRequest.getApp()).thenReturn(appInfo);
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(null);
        Result expectedResult = Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");

        Result actualResult = loginService.resetPassword(registerRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testResetPasswordWithWrongPassword() {
        when(registerRequest.getEmail()).thenReturn("<EMAIL>");
        when(registerRequest.getPassword()).thenReturn("123");

        User stored = new User();
        stored.setEmail("<EMAIL>");
        stored.setSalt("salt");
        stored.setHashedPassword("123");

        AppInfo appInfo = new AppInfo();
        when(registerRequest.getApp()).thenReturn(appInfo);
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(stored);

        Result expectedResult = Result.Error(-1012, "INVALID_PASSWORD");

        Result actualResult = loginService.resetPassword(registerRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testResetPassword() {
        when(registerRequest.getEmail()).thenReturn("<EMAIL>");
        when(registerRequest.getPassword()).thenReturn("!qweQWE123");

        User stored = new User();
        stored.setEmail("<EMAIL>");
        stored.setSalt("salt");
        stored.setHashedPassword("123");

        AppInfo appInfo = new AppInfo();
        when(registerRequest.getApp()).thenReturn(appInfo);
        when(userService.getUserByEmailAndTenantId(any(), any(), any())).thenReturn(stored);
        when(mailConfirmService.mailConfirm(any(), anyString(), any())).thenReturn(Result.Success());

        Result expectedResult = Result.Success();

        Result actualResult = loginService.resetPassword(registerRequest);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testRegisterSuccess() {
        AppInfo appInfo = new AppInfo();
        appInfo.setBundle("addx");
        appInfo.setAppType("Android");
        appInfo.setTenantId("vicoo");
        RegisterRequest request = new RegisterRequest();
        request.setApp(appInfo);
        request.setPassword("password");
        request.setLanguage("zh");
        when(appUserConfig.getNamePreByTenantId(any())).thenReturn("Vi");
        when(userService.insertUser(any())).thenReturn(1);
        when(userVipActivateService.createFreeUserTier(any(), any())).thenReturn(true);
        when(mailConfirmService.setMailConfirmUsed(any())).thenReturn(1);

        when(pushService.setPushInfo(any())).thenReturn(1);
        when(tokenService.generateMsgToken(any(), any())).thenReturn(new HttpTokenDO());

        when(emailPayConfig.needSendPayEmail(any())).thenReturn(true);
        doNothing().when(mqSender).sendToUserEmail(any());
        doNothing().when(tokenService).setUserSeed(any(), any());
        doNothing().when(userSettingService).insertUserSetting(any());
        doNothing().when(userAppScoreService).userAppScoreMoment(any(), any());
        Result actualResult = loginService.registerSuccess(request, confirmRequest, "<EMAIL>");
        assertEquals(LoginResponseDO.class, actualResult.getData().getClass());
    }

    @Test
    public void testResetPasswordSuccess() {
        when(userService.updateUserPassword(any())).thenReturn(1);
        when(mailConfirmService.setMailConfirmUsed(any())).thenReturn(1);
        AppInfo appInfo = new AppInfo();
        when(registerRequest.getApp()).thenReturn(appInfo);

        Result expectedResult = Result.SqlOperationResult(1);

        Result actualResult = loginService.resetPasswordSuccess(registerRequest, mailConfirmRequest, user, "<EMAIL>");

        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("注册发送验证码")
    public void testRegisterConfirm() throws ClientException {
        MailConfirmRequest confirmRequest = new MailConfirmRequest();
        confirmRequest.setEmail("<EMAIL>");
        AppInfo appInfo = new AppInfo();
        confirmRequest.setApp(appInfo);


        when(appInfoService.queryDownloadUrl(any(), any())).thenReturn("");
        when(appInfoService.registerVerify(any())).thenReturn(false);

        when(mailConfirmService.sendConfirmEmail(any(), any())).thenReturn(Result.Success());

        Result actualResult = loginService.registerConfirm(confirmRequest);
        assertEquals(Result.Success(), actualResult);
    }


    @Test
    @DisplayName("注册时邮箱/手机全为空")
    public void testRegisterConfirmEmailAndPhoneNull() throws ClientException {
        Result result = Result.Error(NO_CONTACT, "ACCOUNT_NOT_REGISTERED");
        Result actualResult = loginService.registerConfirm(confirmRequest);
        assertEquals(result, actualResult);
    }

    @Test
    @DisplayName("注册-在迁移期内")
    public void testRegisterConfirmInTransferTime() throws ClientException {
        MailConfirmRequest confirmRequest = new MailConfirmRequest();
        confirmRequest.setEmail("<EMAIL>");
        AppInfo appInfo = new AppInfo();
        confirmRequest.setApp(appInfo);
        Result result = new Result(USER_ACCOUNT_TRANSFER.getCode(), "USER_ACCOUNT_TRANSFER", "");
        when(appInfoService.queryDownloadUrl(any(), any())).thenReturn("");
        when(appInfoService.registerVerify(any())).thenReturn(true);
        Result actualResult = loginService.registerConfirm(confirmRequest);
        assertEquals(result, actualResult);
    }

    @Test
    public void testResetConfirm() throws ClientException {
        when(confirmRequest.getEmail()).thenReturn("<EMAIL>");
        when(mailConfirmService.sendConfirmEmail(any(), any())).thenReturn(Result.Success());

        Result actualResult = loginService.resetConfirm(confirmRequest);
        assertEquals(Result.Success(), actualResult);
    }

    @Test
    public void testLogout() {
        when(jwtHelper.getUserId(any())).thenReturn(1);
        doNothing().when(tokenService).setUserToken(any());
        when(pushService.setPushInfo(any())).thenReturn(1);

        AppInfo appInfo = new AppInfo();
        appInfo.setBundle("addx");
        appInfo.setAppType("Android");
        when(appRequestBase.getApp()).thenReturn(appInfo);

        Result expectedResult = Result.SqlOperationResult(1);
        Result actualResult = loginService.logout(1, appRequestBase);
        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testGenerateLoginOrRegisterResponse() {
        LoginResponseDO responseDO = new LoginResponseDO();
        responseDO.setId(1);
        responseDO.setName("name");
        responseDO.setEmail("<EMAIL>");
        //生成新MsgToken
        responseDO.setToken(httpTokenDO);

        when(user.getId()).thenReturn(1);
        when(user.getName()).thenReturn("name");
        when(user.getEmail()).thenReturn("<EMAIL>");
        when(tokenService.generateMsgToken(any(), any())).thenReturn(httpTokenDO);
        doNothing().when(tokenService).setUserToken(any());

        AppInfo appInfo = new AppInfo();
        appInfo.setBundle("addx");
        appInfo.setAppType("Android");
        when(registerOrLoginRequest.getApp()).thenReturn(appInfo);

        LoginResponseDO loginResponseDO = loginService.generateLoginOrRegisterResponse(user, "seed", registerOrLoginRequest,1);
        assertEquals(java.util.Optional.of(1).get(), loginResponseDO.getId());
        assertEquals(java.util.Optional.of("name").get(), loginResponseDO.getName());
        assertEquals(java.util.Optional.of("<EMAIL>").get(), loginResponseDO.getEmail());
        assertEquals(httpTokenDO, loginResponseDO.getToken());
    }


    @Test
    @DisplayName("历史版本无需验证")
    public void test_virifyLoginTimes_history() {
        LoginRequest loginRequest = this.initLoginRequest(LoginTypeEnums.OLD.getCode());
        boolean expectResult = true;
        boolean actualResult = loginService.virifyLoginTimes(loginRequest);
        assertEquals(expectResult, actualResult);
    }

    @Test
    @DisplayName("无错误记录")
    public void test_virifyLoginTimes_noError() {
        LoginRequest loginRequest = this.initLoginRequest(LoginTypeEnums.NORMAL.getCode());
        when(redisService.hasDeviceOperationDo(any())).thenReturn(false);

        boolean expectResult = true;
        boolean actualResult = loginService.virifyLoginTimes(loginRequest);
        assertEquals(expectResult, actualResult);
    }

    @Test
    @DisplayName("无引导忘记密码")
    public void test_virifyLoginTimes_no_guideForgetPassword() {
        LoginRequest loginRequest = this.initLoginRequest(LoginTypeEnums.NORMAL.getCode());
        when(redisService.hasDeviceOperationDo(any())).thenReturn(true);
        when(redisService.get(any())).thenReturn("1");
        boolean expectResult = true;
        boolean actualResult = loginService.virifyLoginTimes(loginRequest);
        assertEquals(expectResult, actualResult);
    }

    @Test
    @DisplayName("引导忘记密码-失败次数1")
    public void test_virifyLoginTimes_guideForgetPassword_error1() {
        LoginRequest loginRequest = this.initLoginRequest(LoginTypeEnums.NORMAL.getCode(), true);
        when(redisService.hasDeviceOperationDo(any())).thenReturn(true);
        when(redisService.get(any())).thenReturn("1");
        boolean expectResult = true;
        boolean actualResult = loginService.virifyLoginTimes(loginRequest);
        assertEquals(expectResult, actualResult);
    }

    @Test(expected = BaseException.class)
    @DisplayName("引导忘记密码")
    public void test_virifyLoginTimes_guideForgetPassword() {
        LoginRequest loginRequest = this.initLoginRequest(LoginTypeEnums.NORMAL.getCode(), true);
        when(redisService.hasDeviceOperationDo(any())).thenReturn(true);
        when(redisService.get(any())).thenReturn("3");
        loginService.virifyLoginTimes(loginRequest);
    }


    private LoginRequest initLoginRequest(int loginType, Boolean guideForgetPassword) {
        LoginRequest loginRequest = LoginRequest.builder()
                .loginType(loginType)
                .email("email")
                .guideForgetPassword(guideForgetPassword)
                .build();

        AppInfo app = AppInfo.builder()
                .tenantId(TENANTID_GUARD)
                .build();
        loginRequest.setApp(app);
        return loginRequest;
    }

    private LoginRequest initLoginRequest(int loginType) {
        return this.initLoginRequest(loginType, false);
    }

    @Test
    @DisplayName("登录前操作验证码")
    public void test_sendConfirmBeforLogin() throws ClientException {
        MailConfirmRequest request = new MailConfirmRequest();
        Result excepedResult ;
        Result actualResult ;
        {
            excepedResult = Result.Error(NO_CONTACT, "ACCOUNT_NOT_REGISTERED");
            actualResult = loginService.sendConfirmBeforLogin(request);
            Assert.assertEquals(excepedResult,actualResult);
        }
        {
            request.setEmail("email");
            when(mailConfirmService.sendConfirmEmail(any(),any())).thenReturn(Result.Success());
            excepedResult = Result.Success();
            actualResult = loginService.sendConfirmBeforLogin(request);
            Assert.assertEquals(excepedResult,actualResult);
        }
    }

    @Test
    public void test_checkAppEnvMatchServerProfile() {
        {
            loginService.setActiveProfile(null);
            LoginRequest loginRequest2 = LoginRequest.builder().build();
            loginRequest2.getApp().setEnv(null);
            loginService.checkAppEnvMatchServerProfile(loginRequest2);
        }
        {
            loginService.setActiveProfile(null);
            LoginRequest loginRequest2 = LoginRequest.builder().build();
            loginRequest2.getApp().setEnv("");
            loginService.checkAppEnvMatchServerProfile(loginRequest2);
        }
        {
            loginService.setActiveProfile(null);
            LoginRequest loginRequest2 = LoginRequest.builder().build();
            loginRequest2.getApp().setEnv("staging");
            loginService.checkAppEnvMatchServerProfile(loginRequest2);
        }
        {
            loginService.setActiveProfile("staging-us");
            LoginRequest loginRequest2 = LoginRequest.builder().build();
            loginRequest2.getApp().setEnv("test");
            loginService.checkAppEnvMatchServerProfile(loginRequest2);
        }
        {
            loginService.setActiveProfile("staging-us");
            LoginRequest loginRequest2 = LoginRequest.builder().build();
            loginRequest2.getApp().setEnv("staging");
            loginService.checkAppEnvMatchServerProfile(loginRequest2);
        }
    }
}
