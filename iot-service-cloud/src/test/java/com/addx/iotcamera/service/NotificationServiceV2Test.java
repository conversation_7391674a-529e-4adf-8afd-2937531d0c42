package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.bird.BirdName;
import com.addx.iotcamera.bean.cache.DevicePirNotifyFactor;
import com.addx.iotcamera.bean.db.LibraryStatusTb;
import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.PushInfo;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.library.ActivityZone;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.init.CopyWriteInit;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.config.EventDescribeConfig;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.enums.PushTypeEnums;
import com.addx.iotcamera.helper.DeviceDetectHelper;
import com.addx.iotcamera.service.NotificationService.PushContext;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.message.MessagePushManageService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.video.DeviceCache;
import com.addx.iotcamera.service.video.VideoGenerateService;
import com.addx.iotcamera.service.xinge.PushXingeService;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.addx.iot.domain.extension.ai.model.PossibleSubcategory;
import org.addx.iot.domain.extension.ai.vo.AiTaskResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static com.addx.iotcamera.constants.VideoConstants.S3_VIDEO_SLICE_INFO_PREFIX;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class NotificationServiceV2Test {

    @InjectMocks
    private NotificationService notificationService;
    @Mock
    private PushXingeService pushXingeService;
    @Mock
    private DeviceService deviceService;
    @Mock
    private BirdLoversService birdLoversService;
    @Mock
    private AIService aiService;
    @Mock
    private UserService userService;
    @Mock
    private PushInfoService pushInfoService;
    @Mock
    private PushService pushService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private MessagePushManageService messagePushManageService;
    @Mock
    private VideoService videoService;
    @Mock
    private LibraryStatusService libraryStatusService;
    @Mock
    private S3Service s3Service;
    @Mock
    private RedisService redisService;
    @Mock
    private VideoEventRedisService videoEventRedisService;
    @Mock
    private DeviceDetectHelper deviceDetectHelper;
    @Mock
    private LibraryService libraryService;
    @Mock
    private CopyWrite copyWrite;
    @Mock
    private Map<String, Map<String, Map<String, String>>> deep3Map;
    @Mock
    private Map<String, Map<String, String>> deep2Map;
    @Mock
    private Map<String, String> deep1Map;
    @Mock
    private CenterNotifyConfig centerNotifyConfig;
    @Mock
    private EventDescribeConfig eventDescribeConfig;
    @Mock
    private ActivityZoneService activityZoneService;

    @Mock
    private VideoGenerateService videoGenerateService;

    @Mock
    FactoryDataQueryService factoryDataQueryService;

    @Mock
    private DeviceManualService deviceManualService;

    private String traceId = OpenApiUtil.shortUUID();
    private String sn = OpenApiUtil.shortUUID();
    private Integer adminId = (new Random()).nextInt(9_0000_0000) + 1_0000_0000;
    private Integer userId = (new Random()).nextInt(9_0000_0000) + 1_0000_0000;
    private List<Integer> userIds = Arrays.asList(userId, adminId);

    @Before
    @SneakyThrows
    public void init() {
        notificationService.setGson(new Gson());
        when(libraryStatusService.selectLibraryStatusByTraceIdAndUserId(anyString(), any())).thenAnswer(it ->
                LibraryStatusTb.builder().traceId(it.getArgument(0)).userId(it.getArgument(1)).build());
        when(libraryStatusService.selectLibraryStatusByTraceIdAndUserId(anyString(), any())).thenAnswer(it ->
                LibraryStatusTb.builder().traceId(it.getArgument(0)).userId(it.getArgument(1)).build());
        when(s3Service.preSignUrl(anyString())).thenAnswer(it -> it.getArgument(0) + "?token");
        when(deep3Map.get(anyString())).thenReturn(deep2Map);
        when(deep2Map.get(anyString())).thenReturn(deep1Map);
        when(deep1Map.get(anyString())).thenAnswer(it -> "${" + it.getArgument(0) + "}");
        when(copyWrite.getConfig()).thenReturn(deep2Map);
        when(centerNotifyConfig.getMessage()).thenReturn(deep3Map);

    }

    @Test
    public void test_aiTaskNotify() {
        {
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>(){{ add(new ActivityZone() {{ setId(999);}}); }});
            aiTaskNotify();
        }
        {
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>());
            aiTaskNotify();
        }
    }

    public void aiTaskNotify() {
        AiTaskResult result = new AiTaskResult()
                .setTraceId(traceId).setSerialNumber(sn).setOrder(0)
                .setEvents(Arrays.asList());
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(null);
            AssertUtil.assertNotException(() -> notificationService.aiTaskNotify(result, true));
        }
        DeviceDO device = DeviceDO.builder().serialNumber(sn)
                .build();
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(device);
        {
            when(pushService.getDevicePirNotifyFactor(sn)).thenReturn(null);
            AssertUtil.assertNotException(() -> notificationService.aiTaskNotify(result, true));
        }
        {
            DevicePirNotifyFactor factor = new DevicePirNotifyFactor();
            factor.setPir(false);
            AssertUtil.assertNotException(() -> notificationService.aiTaskNotify(result, true));
        }
        DevicePirNotifyFactor factor = new DevicePirNotifyFactor();
        factor.setPir(true);
        factor.setNotify(true);
        factor.setVip(true);
        factor.setAiDetect(true);
        factor.setAiDetectNotifyEvents(Arrays.asList("person,pet,vehicle,package"));
        when(pushService.getDevicePirNotifyFactor(sn)).thenReturn(factor);
        when(userRoleService.getDeviceAdminUser(sn)).thenReturn(adminId);
        {
            when(userRoleService.findAllUsersForDevice(sn)).thenReturn(Arrays.asList());
            AssertUtil.assertNotException(() -> notificationService.aiTaskNotify(result, true));
        }
        when(userRoleService.findAllUsersForDevice(sn)).thenReturn(userIds);
        {
            when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(true);

            AssertUtil.assertNotException(() -> notificationService.aiTaskNotify(result, true));
        }
        when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(false);

        {
            AssertUtil.assertNotException(() -> notificationService.aiTaskNotify(result, true));
        }
        {
            AssertUtil.assertNotException(() -> notificationService.aiTaskNotify(result, false));
        }
    }

    @Test
    public void test_notifyVideoReportEvent() {
        test_notifyVideoReportEvent(EReportEvent.DEVICE_CALL);
        test_notifyVideoReportEvent(EReportEvent.DOORBELL_PRESS);
        test_notifyVideoReportEvent(EReportEvent.DOORBELL_REMOVE);
    }

    public void test_notifyVideoReportEvent(EReportEvent reportEvent) {
        final int notifyType = MsgType.NEW_VIDEO_MSG;
        when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(false);
        {
            when(userRoleService.findAllUsersForDevice(sn)).thenReturn(Arrays.asList());
            AssertUtil.assertNotException(() -> notificationService.notifyVideoReportEvent(traceId, sn, reportEvent, notifyType));
        }
        when(userRoleService.findAllUsersForDevice(sn)).thenReturn(userIds);
        String mapKey = S3_VIDEO_SLICE_INFO_PREFIX + traceId;
        {
            when(redisService.hashGetString(eq(mapKey), anyString())).thenReturn(null);
            AssertUtil.assertNotException(() -> notificationService.notifyVideoReportEvent(traceId, sn, reportEvent, notifyType));
        }
        when(redisService.hashGetString(eq(mapKey), anyString())).thenReturn("http://addx/image.jpeg");
        {
            when(redisService.hashIncrementInt(eq(mapKey), anyString(), eq(1))).thenReturn(2);
            AssertUtil.assertNotException(() -> notificationService.notifyVideoReportEvent(traceId, sn, reportEvent, notifyType));
        }
        when(redisService.hashIncrementInt(eq(mapKey), anyString(), eq(1))).thenReturn(1);
        when(s3Service.preSignUrl(anyString())).thenReturn("http://addx/image.jpeg?token");
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(DeviceDO.builder().deviceName("deviceName1").build());
        when(aiService.getVideoEventKey(sn, traceId)).thenReturn("videoEvent1");
        {
            when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(true);
            AssertUtil.assertNotException(() -> notificationService.notifyVideoReportEvent(traceId, sn, reportEvent, notifyType));
        }
        when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(false);
        when(userService.queryUserById(userId)).thenReturn(new User() {{
            setId(userId);
            setLanguage("en");
        }});
        {
            when(pushInfoService.getPushInfo(userId)).thenReturn(PushInfo.builder()
                    .msgType(PushTypeEnums.PUSH_XINGE.getCode()).msgToken("token1").build());
            AssertUtil.assertNotException(() -> notificationService.notifyVideoReportEvent(traceId, sn, reportEvent, notifyType));
        }
        {
            when(pushInfoService.getPushInfo(userId)).thenReturn(PushInfo.builder()
                    .msgType(PushTypeEnums.PUSH_FCM.getCode()).msgToken("token1").build());
            AssertUtil.assertNotException(() -> notificationService.notifyVideoReportEvent(traceId, sn, reportEvent, notifyType));
        }
    }

    @Test
    public void test_PirNotify() {
        String imageUrl = "http://addx/image.jpeg";
        AssertUtil.assertNotException(() -> notificationService.PirNotify(sn, traceId, "", ""));
        {
            when(redisService.setIfAbsent(anyString(), anyString(), anyLong(), any())).thenReturn(false);
            AssertUtil.assertNotException(() -> notificationService.PirNotify(sn, traceId, imageUrl, ""));
        }
        when(redisService.setIfAbsent(anyString(), anyString(), anyLong(), any())).thenReturn(true);
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(DeviceDO.builder().pushIgnored(true).build());
            AssertUtil.assertNotException(() -> notificationService.PirNotify(sn, traceId, imageUrl, ""));
        }
        DeviceDO device = DeviceDO.builder().serialNumber(sn).pushIgnored(false)
                .deviceName("deviceName1").build();
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(device);
        {
            when(userRoleService.findAllUsersForDevice(anyString())).thenReturn(Arrays.asList());
            AssertUtil.assertNotException(() -> notificationService.PirNotify(sn, traceId, imageUrl, ""));
        }
        when(userRoleService.findAllUsersForDevice(anyString())).thenReturn(Arrays.asList(userId));
        {
            when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(true);
            AssertUtil.assertNotException(() -> notificationService.PirNotify(sn, traceId, imageUrl, ""));
        }
        when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(false);
        when(userService.queryUserById(userId)).thenReturn(new User() {{
            setId(userId);
            setLanguage("en");
            setTenantId("tenant1");
        }});
        {
            when(pushInfoService.getPushInfo(userId)).thenReturn(PushInfo.builder().msgType(PushTypeEnums.PUSH_XINGE.getCode()).msgToken("token1").build());
            when(videoGenerateService.getDeviceCache(any())).thenReturn(new DeviceCache("sn", System.currentTimeMillis()));
            AssertUtil.assertNotException(() -> notificationService.PirNotify(sn, traceId, imageUrl, ""));
        }
        {
            when(pushInfoService.getPushInfo(userId)).thenReturn(PushInfo.builder().msgType(PushTypeEnums.PUSH_FCM.getCode()).msgToken("token1").build());
            AssertUtil.assertNotException(() -> notificationService.PirNotify(sn, traceId, imageUrl, ""));
        }

    }

    @Test
    public void test_getVideoReportEventMsgContent() {
        User user = new User();
        user.setLanguage("zh");
        when(userService.queryUserById(userId)).thenReturn(user);
        {
            when(deep2Map.containsKey(anyString())).thenReturn(true);
            when(deep1Map.containsKey(anyString())).thenReturn(true);
            String content = notificationService.getVideoReportEventMsgContent(userId, EReportEvent.DOORBELL_PRESS);
            Assert.assertNotNull(content);
        }
        {
            when(deep2Map.containsKey(anyString())).thenReturn(true);
            when(deep1Map.containsKey(anyString())).thenReturn(false);
            String content = notificationService.getVideoReportEventMsgContent(userId, EReportEvent.DOORBELL_PRESS);
            Assert.assertNotNull(content);
        }
        {
            when(deep2Map.containsKey(anyString())).thenReturn(false);
            when(deep1Map.containsKey(anyString())).thenReturn(false);
            String content = notificationService.getVideoReportEventMsgContent(userId, EReportEvent.DOORBELL_PRESS);
            Assert.assertNull(content);
        }
        {
            when(deep2Map.containsKey(anyString())).thenReturn(false);
            when(deep1Map.containsKey(anyString())).thenReturn(true);
            String content = notificationService.getVideoReportEventMsgContent(userId, EReportEvent.DOORBELL_PRESS);
            Assert.assertNull(content);
        }
    }

    @Test
    @SneakyThrows
    public void test_getBirdEventDesc() {
        CopyWrite copyWrite = new CopyWrite();
        CopyWriteInit.readStreamCsv("gitconfig/copywrite/iot.csv", copyWrite);

        User user = new User();
        user.setId(384934);
        user.setLanguage("zh");
        NotificationService.PushContext pushContext = new NotificationService.PushContext()
                .setUser(user).setAdminId(12343433).setShowPossibleSubcategoryText(false);
        AiEvent event = new AiEvent();
        {
            String eventDescribe = notificationService.getBirdEventDesc(pushContext, event);
            Assert.assertEquals(null, eventDescribe);
        }
        event.setEventObject(AiObjectEnum.BIRD).setEventType(AiObjectActionEnum.EXIST);
        {
            String eventDescribe = notificationService.getBirdEventDesc(pushContext, event);
            Assert.assertEquals(null, eventDescribe);
        }
        pushContext.setShowPossibleSubcategoryText(true);
        {
            String eventDescribe = notificationService.getBirdEventDesc(pushContext, event);
            Assert.assertEquals(null, eventDescribe);
        }
        event.setPossibleSubcategory(Arrays.asList(new PossibleSubcategory().setName("haha").setConfidence(new BigDecimal("99"))));
        {
            when(birdLoversService.searchBirdNameByStdName(anyString())).thenReturn(Arrays.asList());
            String eventDescribe = notificationService.getBirdEventDesc(pushContext, event);
            Assert.assertEquals(null, eventDescribe);
        }
        BirdName birdName = new BirdName().setIoc_12_1("haha")
                .setLang2Name(ImmutableMap.of("zh", "哈哈"));
        when(birdLoversService.searchBirdNameByStdName(anyString())).thenReturn(Arrays.asList(birdName));
        {
            when(this.copyWrite.getConfig()).thenReturn(null);
            String eventDescribe = notificationService.getBirdEventDesc(pushContext, event);
            Assert.assertEquals(null, eventDescribe);
        }
        when(this.copyWrite.getConfig()).thenReturn(copyWrite.getConfig());
        {
            String eventDescribe = notificationService.getBirdEventDesc(pushContext, event);
            Assert.assertEquals("发现鸟类，看上去像是哈哈！", eventDescribe);
        }

    }

    @Mock
    private MessageNotificationSettingsService messageNotificationSettingsService;
    @Mock
    private UserSettingService userSettingService;

    @Test
    public void test_notificationMessage() {
        Integer adminId = 12347538;
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        List<Integer> azIds = new LinkedList<>();
        AiTaskResult result = new AiTaskResult().setTraceId(traceId).setSerialNumber(sn)
                .setOrder(0).setImageUrl("https://addx/image.jpeg")
                .setEvents(Arrays.asList(new AiEvent()
                        .setEventObject(AiObjectEnum.PERSON)
                        .setEventType(AiObjectActionEnum.EXIST)
                        .setActivatedZones(azIds)
                ));
        {
            azIds.clear();
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>(){{ add(new ActivityZone() {{ setId(999);}}); }});
            notificationService.notificationMessage(result, Arrays.asList(adminId), adminId);
        }
        {
            azIds.add(999);
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>(){{ add(new ActivityZone() {{ setId(999);}}); }});
            notificationService.notificationMessage(result, Arrays.asList(adminId), adminId);
        }
        {
            azIds.clear();
            notificationService.notificationMessage(result, Arrays.asList(adminId), adminId);
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>());
        }
        {
            azIds.add(999);
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>());
            notificationService.notificationMessage(result, Arrays.asList(adminId), adminId);
        }
        MessageNotificationSetting messageNotificationSetting = new MessageNotificationSetting();
        messageNotificationSetting.setUserId(adminId);
        messageNotificationSetting.setSerialNumber(sn);
        messageNotificationSetting.setEventObjects("person,package");
        messageNotificationSetting.setPackageEventType("");
        messageNotificationSetting.setPackageFirstUse(0);
        when(messageNotificationSettingsService.queryMessageNotificationSetting(sn, adminId)).thenReturn(messageNotificationSetting);
        UserSettingsDO userSettingsDO = new UserSettingsDO();
        userSettingsDO.setMessageMergeSwitch(1);
        when(userSettingService.queryUserSetting(adminId)).thenReturn(userSettingsDO);

        when(messagePushManageService.queryUserPushSwitch(any(),anyString())).thenReturn(true);
        {
            azIds.clear();
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>(){{ add(new ActivityZone() {{ setId(999);}}); }});
            notificationService.notificationMessage(result, Arrays.asList(adminId), adminId);
        }
        {
            azIds.add(999);
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>(){{ add(new ActivityZone() {{ setId(999);}}); }});
            notificationService.notificationMessage(result, Arrays.asList(adminId), adminId);
        }
        {
            azIds.clear();
            notificationService.notificationMessage(result, Arrays.asList(adminId), adminId);
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>());
        }
        {
            azIds.add(999);
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>());
            notificationService.notificationMessage(result, Arrays.asList(adminId), adminId);
        }

    }

    @Test
    public void test_aiTaskNotify_aiEventResult() {
        {
            AiTaskResult result = new AiTaskResult()
                    .setTraceId(traceId).setSerialNumber(sn).setOrder(0)
                    .setEvents(Arrays.asList());
            aiTaskNotify_aiEventResult(result);
        }
        {
            AiEvent event = new AiEvent();
            event.setEventObject(AiObjectEnum.BIRD).setEventType(AiObjectActionEnum.EXIST);
            event.setActivatedZones(Arrays.asList(999));
            AiTaskResult result = new AiTaskResult()
                    .setTraceId(traceId).setSerialNumber(sn).setOrder(0)
                    .setEvents(Arrays.asList(event));
            aiTaskNotify_aiEventResult(result);
        }
    }

    public void aiTaskNotify_aiEventResult(AiTaskResult result) {
        DeviceDO device = DeviceDO.builder().serialNumber(sn)
                .build();
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(device);
        DevicePirNotifyFactor factor = new DevicePirNotifyFactor();
        factor.setPir(true);
        factor.setNotify(true);
        factor.setVip(true);
        factor.setAiDetect(true);
        factor.setAiDetectNotifyEvents(Arrays.asList("person,pet,vehicle,package"));
        when(pushService.getDevicePirNotifyFactor(sn)).thenReturn(factor);
        when(userRoleService.getDeviceAdminUser(sn)).thenReturn(adminId);
        when(userRoleService.findAllUsersForDevice(sn)).thenReturn(userIds);
        when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(false);

        {
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>(){{ add(new ActivityZone() {{ setId(999);}}); }});
            notificationService.aiTaskNotify(result, false);
        }
        {
            when(activityZoneService.queryActivityZone(sn)).thenReturn(new ArrayList<ActivityZone>(){{ add(new ActivityZone() {{ setId(999);}}); }});
            notificationService.aiTaskNotify(result, false);
        }
    }

    @Test
    public void test_getEventInfo() {
        List<AiEvent> events = Arrays.asList(
                new AiEvent().setEventObject(AiObjectEnum.PERSON).setSummaryUrl("123"),
                new AiEvent().setEventObject(AiObjectEnum.PET).setSummaryUrl("123"),
                new AiEvent().setEventObject(AiObjectEnum.VEHICLE).setSummaryUrl("123"),
                new AiEvent().setEventObject(AiObjectEnum.PACKAGE).setSummaryUrl("123"),
                new AiEvent().setEventObject(AiObjectEnum.BIRD).setSummaryUrl("123")
        );
        String eventInfo = NotificationService.getEventInfo(events);
        List<AiEvent> events2 = JSON.parseArray(eventInfo, AiEvent.class);
        Assert.assertTrue(events2.stream().filter(it -> it.getEventObject() == AiObjectEnum.BIRD).allMatch(it -> it.getSummaryUrl() != null));
        Assert.assertTrue(events2.stream().filter(it -> it.getEventObject() != AiObjectEnum.BIRD).allMatch(it -> it.getSummaryUrl() == null));
    }


    @Test
    public void test_pushAiMessage() {
        PushContext pushContext = Mockito.mock(PushContext.class);
        AiTaskResult result = Mockito.mock(AiTaskResult.class);

        when(pushContext.getUser()).thenReturn(new User(){{
            setLanguage("en");
        }});
        when(pushContext.getPushInfo()).thenReturn(new PushInfo(){{
            setMsgType(PushTypeEnums.PUSH_FCM.getCode());
        }});
        when(result.getEvents()).thenReturn(Collections.singletonList(new AiEvent(){{
            setActivatedZones(Collections.emptyList());
            setEventObject(AiObjectEnum.PET);
            setEventType(AiObjectActionEnum.EXIST);
        }}));
        when(deviceService.getAllDeviceInfo(any())).thenReturn(new DeviceDO());

        when(eventDescribeConfig.getConfig()).thenReturn(Collections.singletonMap("en", Collections.<String, Map<String, String>>singletonMap("pet", Collections.<String, String>singletonMap("exist", "adasdsadsdadadasd"))));

        notificationService.pushAiMessage(pushContext, result, "1");
        Mockito.verify(pushService, Mockito.only()).pushVideoMessage(any(), any());
    }
}
