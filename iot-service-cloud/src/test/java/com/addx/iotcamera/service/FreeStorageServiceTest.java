package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.openapi.PaasTenantInfo;
import com.addx.iotcamera.bean.openapi.UserFreeStorage;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.vip.TierService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static com.addx.iotcamera.service.UserVipActivateService.FREE_TIER_SECONDS;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class FreeStorageServiceTest {

    @InjectMocks
    private FreeStorageService freeStorageService;
    @Mock
    private PaasTenantConfig paasTenantConfig;
    @Mock
    private UserService userService;
    @Mock
    private TierService tierService;
    @Mock
    private TimeTicker timeTicker;
    @Mock
    private IUserVipDAO iUserVipDAO;
    @Mock
    private UserVipActivateService userVipActivateService;

    @Mock
    private UserSettingService userSettingService;

    @Test
    public void test_getPaasFreeStorage() {
        Assert.assertEquals(new UserFreeStorage(), freeStorageService.getPaasFreeStorage(0));
        Assert.assertEquals(new UserFreeStorage(), freeStorageService.getPaasFreeStorage(-1));
        {
            final int userId = 163428;
            when(userService.queryUserById(userId)).thenReturn(null);
            Assert.assertEquals(new UserFreeStorage(), freeStorageService.getPaasFreeStorage(userId));
        }
        {
            final int userId = 89813099;
            when(userService.queryUserById(userId)).thenThrow(new RuntimeException("mock"));
            Assert.assertEquals(new UserFreeStorage(), freeStorageService.getPaasFreeStorage(userId));
        }
    }

    @Test
    public void test_createFreeUserVip() {
        final Integer curTime = PhosUtils.getUTCStamp();
        when(timeTicker.readSeconds()).thenReturn(curTime);
        final User user = new User() {{
            setId(1232463784);
            setTenantId("haha");
        }};
        Integer tierId = 100;
        UserVipDO expectedResult = null;
//        when(iUserVipDAO.insertUserVip(any())).thenAnswer(it -> results[1] = 1);
//        doAnswer(it -> results[2] = 1).when(userVipActivateService).activateUserVip(eq(user.getId()), eq(tierId));
        {

            expectedResult = freeStorageService.createFreeUserVip(user.getId(), null, curTime);
            Assert.assertNull(expectedResult);
        }
        {

            expectedResult = freeStorageService.createFreeUserVip(user.getId(), tierId, curTime);
            Assert.assertEquals(expectedResult.getTierId(),tierId);
        }
    }

    @Test
    public void test_createPaasFreeUserTier() {
        final Integer curTime = PhosUtils.getUTCStamp();
        when(timeTicker.readSeconds()).thenReturn(curTime);
        final User user = new User() {{
            setId(1232463784);
            setTenantId("haha");
        }};
        Integer tierId = 100;
        int[] results = new int[3];
        UserVipDO exceptedResult;
        when(iUserVipDAO.insertUserVip(any())).thenAnswer(it -> results[1] = 1);
        doAnswer(it -> results[2] = 1).when(userVipActivateService).activateUserVip(eq(user.getId()), eq(tierId));
        {
            when(iUserVipDAO.queryUserVipInfo(eq(user.getId()), anyInt(),any())).thenReturn(Arrays.asList());
            user.setRegistTime(curTime);
            Arrays.fill(results, 0);
            exceptedResult = freeStorageService.getOrCreateFreeUserVip(user.getId(), null, curTime);
            Assert.assertNull(exceptedResult);

        }
        {
            when(iUserVipDAO.queryUserVipInfo(eq(user.getId()), anyInt(),any())).thenReturn(Arrays.asList());
            user.setRegistTime(curTime);
            Arrays.fill(results, 0);
            exceptedResult = freeStorageService.getOrCreateFreeUserVip(user.getId(), tierId, curTime);
            Assert.assertTrue(exceptedResult != null && exceptedResult.getTierId().equals(tierId));
        }
        {
            when(iUserVipDAO.queryUserVipInfo(eq(user.getId()), anyInt(),any())).thenReturn(Arrays.asList());
            user.setRegistTime(curTime - FREE_TIER_SECONDS);
            Arrays.fill(results, 0);
            exceptedResult = freeStorageService.getOrCreateFreeUserVip(user.getId(), tierId, curTime);
            Assert.assertTrue(exceptedResult != null && exceptedResult.getTierId().equals(tierId));
        }
        {
            when(iUserVipDAO.queryUserVipInfo(eq(user.getId()), anyInt(),any())).thenReturn(Arrays.asList());
            user.setRegistTime(curTime - FREE_TIER_SECONDS * 2);
            Arrays.fill(results, 0);
            exceptedResult = freeStorageService.getOrCreateFreeUserVip(user.getId(), tierId, curTime);
            Assert.assertTrue(exceptedResult != null && exceptedResult.getTierId().equals(tierId));
        }
//        final UserVipDO userVipDO = new UserVipDO();
//        userVipDO.setEffectiveTime(curTime - 100);
//        userVipDO.setEndTime(curTime + 100);
//        when(iUserVipDAO.queryUserVipInfo(eq(user.getId()), anyInt(),any())).thenReturn(Arrays.asList(userVipDO));
//        {
//            userVipDO.setTierId(100);
//            userVipDO.setActive(false);
//            user.setRegistTime(curTime);
//            Arrays.fill(results, 0);
//            exceptedResult = freeStorageService.getOrCreateFreeUserVip(user.getId(), tierId, curTime);
//            Assert.assertTrue(exceptedResult != null && exceptedResult.getTierId().equals(tierId));
//        }
//        {
//            userVipDO.setTierId(100);
//            userVipDO.setActive(true);
//            user.setRegistTime(curTime);
//            Arrays.fill(results, 0);
//            exceptedResult = freeStorageService.getOrCreateFreeUserVip(user.getId(), tierId, curTime);
//            Assert.assertTrue(exceptedResult != null && exceptedResult.getTierId().equals(tierId));
//        }
//        {
//            userVipDO.setTierId(10);
//            userVipDO.setActive(false);
//            user.setRegistTime(curTime);
//            Arrays.fill(results, 0);
//            exceptedResult = freeStorageService.getOrCreateFreeUserVip(user.getId(), tierId, curTime);
//            Assert.assertTrue(exceptedResult != null && exceptedResult.getTierId().equals(tierId));
//        }
//        {
//            userVipDO.setTierId(10);
//            userVipDO.setActive(true);
//            user.setRegistTime(curTime);
//            Arrays.fill(results, 0);
//            exceptedResult = freeStorageService.getOrCreateFreeUserVip(user.getId(), tierId, curTime);
//            Assert.assertTrue(exceptedResult != null && exceptedResult.getTierId().equals(tierId));
//        }
//        {
//            userVipDO.setTierId(123);
//            userVipDO.setActive(false);
//            user.setRegistTime(curTime);
//            Arrays.fill(results, 0);
//            exceptedResult = freeStorageService.getOrCreateFreeUserVip(user.getId(), tierId, curTime);
//            Assert.assertTrue(exceptedResult != null && exceptedResult.getTierId().equals(tierId));
//        }
//        {
//            userVipDO.setTierId(123);
//            userVipDO.setActive(true);
//            user.setRegistTime(curTime);
//            Arrays.fill(results, 0);
//            exceptedResult = freeStorageService.getOrCreateFreeUserVip(user.getId(), tierId, curTime);
//            Assert.assertTrue(exceptedResult != null && exceptedResult.getTierId().equals(tierId));
//        }

    }

    @Test
    public void test_getUserFreeStorage() {
        final Integer utcTime = PhosUtils.getUTCStamp();
        Integer tierId = 100;
        final Tier tier = new Tier() {{
            setTierId(tierId);
            setRollingDays(3);
            setStorage(536870912L);
        }};
        when(tierService.queryTierById(tierId)).thenReturn(tier);
        Integer tierId2 = 10;
        final Tier tier2 = new Tier() {{
            setTierId(tierId2);
            setRollingDays(7);
            setStorage(536870912L*2);
        }};
        when(tierService.queryTierById(tierId2)).thenReturn(tier2);
        when(timeTicker.readSeconds()).thenReturn(utcTime);
        final User user = new User() {{
            setId(357834567);
            setTenantId("longse_123");
        }};
        {
            when(userSettingService.queryUserSetting(any())).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
            when(paasTenantConfig.getPaasTenantInfo(user.getTenantId())).thenReturn(new PaasTenantInfo()
                    .setOldUserFreeTierId(null));
            when(iUserVipDAO.queryUserVipInfo(user.getId(), 0, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Arrays.asList());
            final UserFreeStorage userFreeStorage = freeStorageService.getUserFreeStorage(user, utcTime);
            Assert.assertEquals(new UserFreeStorage(), userFreeStorage);
        }
        when(userSettingService.queryUserSetting(any())).thenReturn(UserSettingsDO.builder().supportFreeLicense(0).build());

        {
            when(paasTenantConfig.getPaasTenantInfo(user.getTenantId())).thenReturn(new PaasTenantInfo()
                    .setOldUserFreeTierId(null));
            when(iUserVipDAO.queryUserVipInfo(user.getId(), 0, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Arrays.asList());
            final UserFreeStorage userFreeStorage = freeStorageService.getUserFreeStorage(user, utcTime);
            Assert.assertEquals(new UserFreeStorage(), userFreeStorage);
        }
        {
            when(paasTenantConfig.getPaasTenantInfo(user.getTenantId())).thenReturn(new PaasTenantInfo()
                    .setOldUserFreeTierId(null));
            when(iUserVipDAO.queryUserVipInfo(user.getId(), 0,TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Arrays.asList(new UserVipDO() {{
                setTierId(tierId2);
                setEffectiveTime(utcTime - 100);
                setEndTime(utcTime + 100);
            }}));
            final UserFreeStorage expect = new UserFreeStorage() {{
                setEffectTime(utcTime - 100);
                setEndTime(utcTime + 100);
                setIsValid(true);
                setRollingDays(tier2.getRollingDays());
                setStorage(tier2.getStorage());
            }};
            final UserFreeStorage userFreeStorage = freeStorageService.getUserFreeStorage(user, utcTime);
            Assert.assertEquals(expect, userFreeStorage);
        }
        {
            when(paasTenantConfig.getPaasTenantInfo(user.getTenantId())).thenReturn(new PaasTenantInfo()
                    .setOldUserFreeTierId(null));
            when(iUserVipDAO.queryUserVipInfo(user.getId(), 0,TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Arrays.asList(new UserVipDO() {{
                setTierId(tierId2);
                setEffectiveTime(utcTime - 100);
                setEndTime(utcTime - 50);
            }}));
            final UserFreeStorage expect = new UserFreeStorage() {{
                setEffectTime(utcTime - 100);
                setEndTime(utcTime - 50);
                setIsValid(false);
                setRollingDays(tier2.getRollingDays());
                setStorage(tier2.getStorage());
            }};
            final UserFreeStorage userFreeStorage = freeStorageService.getUserFreeStorage(user, utcTime);
            Assert.assertEquals(expect, userFreeStorage);
        }
        {
            when(iUserVipDAO.queryUserVipInfo(user.getId(), 0,TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Arrays.asList());
            when(paasTenantConfig.getPaasTenantInfo(user.getTenantId())).thenReturn(new PaasTenantInfo()
                    .setOldUserFreeTierId(tierId));
            final UserFreeStorage expect = new UserFreeStorage() {{
                setEffectTime(utcTime);
                setEndTime(utcTime + FREE_TIER_SECONDS);
                setIsValid(true);
                setRollingDays(tier.getRollingDays());
                setStorage(tier.getStorage());
            }};
            final UserFreeStorage userFreeStorage = freeStorageService.getUserFreeStorage(user, utcTime);
            Assert.assertEquals(expect, userFreeStorage);
        }
        {
            when(iUserVipDAO.queryUserVipInfo(user.getId(), 0,TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Arrays.asList(new UserVipDO() {{
                setTierId(tierId2);
                setEffectiveTime(utcTime - FREE_TIER_SECONDS);
                setEndTime(utcTime);
            }}));
            when(paasTenantConfig.getPaasTenantInfo(user.getTenantId())).thenReturn(new PaasTenantInfo()
                    .setOldUserFreeTierId(tierId));
            final UserFreeStorage expect = new UserFreeStorage() {{
                setEffectTime(utcTime - FREE_TIER_SECONDS);
                setEndTime(utcTime);
                setIsValid(false);
                setRollingDays(tier2.getRollingDays());
                setStorage(tier2.getStorage());
            }};
            final UserFreeStorage userFreeStorage = freeStorageService.getUserFreeStorage(user, utcTime);
            Assert.assertEquals(expect, userFreeStorage);
        }
        {
            when(iUserVipDAO.queryUserVipInfo(user.getId(), 0,TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(Arrays.asList(new UserVipDO() {{
                setTierId(tierId2);
                setEffectiveTime(utcTime - FREE_TIER_SECONDS + 1);
                setEndTime(utcTime + 1);
            }}));
            when(paasTenantConfig.getPaasTenantInfo(user.getTenantId())).thenReturn(new PaasTenantInfo()
                    .setOldUserFreeTierId(tierId));
            final UserFreeStorage expect = new UserFreeStorage() {{
                setEffectTime(utcTime - FREE_TIER_SECONDS + 1);
                setEndTime(utcTime + 1);
                setIsValid(true);
                setRollingDays(tier2.getRollingDays());
                setStorage(tier2.getStorage());
            }};
            final UserFreeStorage userFreeStorage = freeStorageService.getUserFreeStorage(user, utcTime);
            Assert.assertEquals(expect, userFreeStorage);
        }
        {
            when(paasTenantConfig.getPaasTenantInfo(user.getTenantId())).thenThrow(new RuntimeException("mock"));
            final UserFreeStorage userFreeStorage = freeStorageService.getUserFreeStorage(user, utcTime);
            Assert.assertEquals(new UserFreeStorage(), userFreeStorage);
        }
    }

}
