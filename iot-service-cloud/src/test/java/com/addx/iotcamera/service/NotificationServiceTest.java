package com.addx.iotcamera.service;


import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.PushInfo;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.msg.DoorbellVideoMsgEntity;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.bean.msg.VideoReportEventMsgEntity;
import com.addx.iotcamera.config.EventDescribeConfig;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.constants.CopyWriteConstans;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.enums.PushTypeEnums;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.message.MessagePushManageService;
import com.addx.iotcamera.service.video.DeviceCache;
import com.addx.iotcamera.service.video.VideoGenerateService;
import com.addx.iotcamera.service.xinge.PushXingeService;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.addx.iot.domain.extension.ai.vo.AiTaskResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class NotificationServiceTest {

    @InjectMocks
    @Spy
    private NotificationService notificationService;


    @Mock
    private RedisService redisService;
    @Mock
    private VideoEventRedisService videoEventRedisService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private DeviceService deviceService;

    @Mock
    private MessagePushManageService messagePushManageService;

    @Mock
    private UserService userService;

    @Mock
    private CopyWrite copyWrite;

    @Mock
    private PushInfoService pushInfoService;

    @Mock
    private PushXingeService pushXingeService;

    @Mock
    private PushService pushService;

    @Mock
    private AIService aiService;

    @Mock
    private CenterNotifyConfig centerNotifyConfig;

    @Mock
    private ReIdService reIdService;

    @Mock
    private EventDescribeConfig eventDescribeConfig;

    @Mock
    private VideoGenerateService videoGenerateService;

    @Mock
    FactoryDataQueryService factoryDataQueryService;

    @Mock
    private DeviceManualService deviceManualService;

    @Before
    public void beforeTest() throws NoSuchFieldException, IllegalAccessException {
        Field gsonField = NotificationService.class.getDeclaredField("gson");
        gsonField.setAccessible(true);
        gsonField.set(notificationService, new Gson());
    }

    @Test
    public void testNoticeDoorbellEvent() {
        when(redisService.setIfAbsent(any(), any(), anyLong(), any())).thenReturn(true);
        when(userRoleService.findAllUsersForDevice(any())).thenReturn(Collections.singletonList(1));
        when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().deviceName("device_01").build());
        when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(false);
        when(userService.queryUserById(any())).thenReturn(new User(){{
            setLanguage("en");
        }});
        when(copyWrite.getConfig()).thenReturn(new HashMap<String, Map<String, String>>(){{
            put("doorbellPressed",  Collections.singletonMap("en",  "someone is pressing doorbell"));
            put("doorbellRemoved",  Collections.singletonMap("en",  "someone is removing doorbell"));
        }});
        when(aiService.getVideoEventKey(any(), any())).thenReturn("");

        when(pushInfoService.getPushInfo(any())).thenReturn(PushInfo.builder().msgType(PushTypeEnums.PUSH_XINGE.getCode()).build());
        notificationService.notifyVideoReportEvent("", "", EReportEvent.DOORBELL_PRESS, MsgType.DEVICE_CALL_MSG);

        when(pushInfoService.getPushInfo(any())).thenReturn(PushInfo.builder().msgType(PushTypeEnums.PUSH_IOS_DEBUG.getCode()).build());
        notificationService.notifyVideoReportEvent("", "", EReportEvent.DOORBELL_REMOVE, MsgType.NEW_VIDEO_MSG);

        new DoorbellVideoMsgEntity().defaultFcmEntity();
        new DoorbellVideoMsgEntity().defaultIosEntity();
        new DoorbellVideoMsgEntity().defaultUmengEntity();
    }

    @Test
    public void testNoticeDeviceCallEvent() {
        when(redisService.setIfAbsent(any(), any(), anyLong(), any())).thenReturn(true);
        when(userRoleService.findAllUsersForDevice(any())).thenReturn(Collections.singletonList(1));
        when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().deviceName("device_01").build());
        when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(false);
        when(userService.queryUserById(any())).thenReturn(new User(){{
            setLanguage("en");
        }});
        when(copyWrite.getConfig()).thenReturn(new HashMap<String, Map<String, String>>(){{
            put("cameraCalling", Collections.singletonMap("en", "someone is calling"));
        }});

        when(pushInfoService.getPushInfo(any())).thenReturn(PushInfo.builder().msgType(PushTypeEnums.PUSH_XINGE.getCode()).build());
        when(aiService.getVideoEventKey(any(), any())).thenReturn("");
        notificationService.notifyVideoReportEvent("", "", EReportEvent.DOORBELL_PRESS, MsgType.NEW_VIDEO_MSG);
        notificationService.notifyVideoReportEvent("", "", EReportEvent.DEVICE_CALL, MsgType.DEVICE_CALL_MSG);

        when(pushInfoService.getPushInfo(any())).thenReturn(PushInfo.builder().msgType(PushTypeEnums.PUSH_IOS_DEBUG.getCode()).build());
        notificationService.notifyVideoReportEvent("", "", EReportEvent.DEVICE_CALL, MsgType.DEVICE_CALL_MSG);

        new VideoReportEventMsgEntity().defaultFcmEntity();
        new VideoReportEventMsgEntity().defaultIosEntity();
        new VideoReportEventMsgEntity().defaultUmengEntity();
    }

    @Test
    public void testPirNotify() {
        when(redisService.setIfAbsent(any(), any(), anyLong(), any())).thenReturn(true);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().deviceName("device_01").pushIgnored(false).build());
        when(userRoleService.findAllUsersForDevice(any())).thenReturn(Collections.singletonList(1));
        when(messagePushManageService.queryUserPushSwitch(any(),(DeviceDO) any())).thenReturn(false);
        when(userService.queryUserById(any())).thenReturn(new User(){{
            setLanguage("en");
            setTenantId("1");
        }});
        when(centerNotifyConfig.getMessage()).thenReturn(new HashMap<String, Map<String, Map<String, String>>>(){{
            put("1",  Collections.singletonMap("en",  Collections.singletonMap(CopyWriteConstans.commonMotionBody, "send pir")));
        }});

        when(pushInfoService.getPushInfo(any())).thenReturn(PushInfo.builder().msgType(PushTypeEnums.PUSH_XINGE.getCode()).build());
        when(videoGenerateService.getDeviceCache(any())).thenReturn(new DeviceCache("sn", System.currentTimeMillis()));
        notificationService.PirNotify("sn_01", UUID.randomUUID().toString(), "http://any.png", null);
    }
//
//    @Test
//    public void getVideoEventDescribeTest_emptyEventKey(){
//        NotificationService.PushContext pushCtx = this.initPushContext();
//        Event event = this.initEvent();
//        String videoEventKey = "";
//
//        when(reIdService.queryValidUserReId(any(), (String) any())).thenReturn(null);
//        when(eventDescribeConfig.getConfig()).thenReturn(initMap());
//        String expectedResult = "发现有人";
//        String actualResult = notificationService.getVideoEventDescribe(pushCtx,event,videoEventKey);
//
//        assertEquals(expectedResult,actualResult);
//    }
//
//    @Test
//    public void getVideoEventDescribeTest_one(){
//        NotificationService.PushContext pushCtx = this.initPushContext();
//        Event event = this.initEvent();
//        String videoEventKey = String.valueOf(System.currentTimeMillis());
//
//        when(reIdService.queryValidUserReId(any(), (String) any())).thenReturn(null);
//        when(eventDescribeConfig.getConfig()).thenReturn(initMap());
//        Set<String> logSet = new HashSet<>();
//        logSet.add("person");
//
//        String videoEventListKey = LIBRARY_VIDEO_EVENT_TO_TAG.replace("{videoEvent}",videoEventKey);
//        when(redisService.reverseRange(videoEventListKey,0L,-1L)).thenReturn(logSet);
//        String expectedResult = "发现有人";
//        String actualResult = notificationService.getVideoEventDescribe(pushCtx,event,videoEventKey);
//
//        assertEquals(expectedResult,actualResult);
//    }
//
//    private NotificationService.PushContext initPushContext(){
//        User user = new User();
//        user.setId(1);
//        user.setTenantId("vicoo");
//        user.setLanguage("zh");
//       return new NotificationService.PushContext().setUser(user).setAdminId(1);
//    }
//
//    private Event initEvent(){
//        return new Event().setEventObject(PERSON).setEventType(EXIST);
//    }
//
//    private Map<String,Map<String, Map<String, String>>> initMap(){
//        Map<String,Map<String, Map<String, String>>> languageMap = Maps.newHashMap();
//        Map<String, Map<String, String>> config = Maps.newHashMap();
//        Map<String,String> eventTypeMap = Maps.newHashMap();
//        eventTypeMap.put(EXIST.getEventTypeName(),"发现有人");
//        eventTypeMap.put(PASS.getEventTypeName(),"有人经过");
//        eventTypeMap.put(HANGING.getEventTypeName(),"有人徘徊");
//        config.put(PERSON.getObjectName(),eventTypeMap);
//
//        languageMap.put("zh",config);
//        return languageMap;
//    }
//
//
    @Test
    public void libraryEventLogTest_videoEvent_empty(){
        AiTaskResult aiTaskResult = new AiTaskResult();
        String traceId = PhosUtils.getUUID();
        aiTaskResult.setTraceId(traceId);

        List<AiEvent> eventList = Arrays.asList(new AiEvent());
        aiTaskResult.setEvents(eventList);

        when(videoEventRedisService.get(any())).thenReturn("");

        String expectedResult = null;
        String actualResult = notificationService.libraryEventLog(aiTaskResult);

        Assert.assertEquals(expectedResult,actualResult);
    }
//
//    @Test
//    @DisplayName("记录ai分析log")
//    public void libraryEventLogTest(){
//        AiTaskResult aiTaskResult = new AiTaskResult();
//        String traceId = PhosUtils.getUUID();
//        aiTaskResult.setTraceId(traceId);
//
//        Event event = this.initEvent();
//        List<Event> eventList = Lists.newArrayList();
//        eventList.add(event);
//        aiTaskResult.setEvents(eventList);
//
//
//        String eventKey = String.valueOf(System.currentTimeMillis());
//        when(redisService.get(any())).thenReturn(eventKey);
//
//        doNothing().when(redisService).zadd(anyString(),anyString(),anyDouble());
//        notificationService.libraryEventLog(aiTaskResult);
//        verify(redisService, times(1)).zadd(anyString(),anyString(),anyDouble());
//    }
}
