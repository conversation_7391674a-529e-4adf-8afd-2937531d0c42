package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.bean.msg.ios.IosMsgEntity;
import com.addx.iotcamera.util.OpenApiUtil;
import com.turo.pushy.apns.ApnsClient;
import com.turo.pushy.apns.ApnsClientBuilder;
import com.turo.pushy.apns.DeliveryPriority;
import com.turo.pushy.apns.PushNotificationResponse;
import com.turo.pushy.apns.util.ApnsPayloadBuilder;
import com.turo.pushy.apns.util.SimpleApnsPushNotification;
import com.turo.pushy.apns.util.TokenUtil;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.GenericFutureListener;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.File;
import java.io.FileInputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.turo.pushy.apns.util.SimpleApnsPushNotification.DEFAULT_EXPIRATION_PERIOD_MILLIS;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class IosPushTest {

    @Test
    public void test() {
    }

    //    @Test
    public void test1() throws Exception {
        String credentialPassword = "addx";
        String credentialPath = "/Users/<USER>/test/ios_push_prod_20211027.p12";
        ApnsClient client = new ApnsClientBuilder()
                .setApnsServer(ApnsClientBuilder.PRODUCTION_APNS_HOST)
                .setClientCredentials(new File(credentialPath), credentialPassword)
                .setConnectionTimeout(10, TimeUnit.SECONDS)
                .setConcurrentConnections(20)
                .build();

        String msgToken = "8df34df7cfc45198980c6167052b9cae3e33dd4a1ae242eaf321213f85805ed1";
        String bundleName = "com.acesee.lsplatform";

        IosMsgEntity entity = new IosMsgEntity();
        entity.setType(MsgType.VIDEO_MSG);
        entity.setMsgId(123);
        entity.setTraceId(OpenApiUtil.shortUUID());
        entity.setVideoEvent("person");
        entity.setTimestamp(PhosUtils.getUTCStamp());
        entity.setSerialNumber("651c8ce4e95510e95b3e4cd7fa5b994c");

        ApnsPayloadBuilder payloadBuilder = new ApnsPayloadBuilder();
        payloadBuilder.setAlertTitle("a4x_test_title");
        payloadBuilder.setAlertBody("a4x_test_title_body_" + System.currentTimeMillis());
        payloadBuilder.setSound("default");
        payloadBuilder.setMutableContent(true);

        payloadBuilder.addCustomProperty("data", entity);
        String payload = payloadBuilder.buildWithDefaultMaximumLength();
        String token = TokenUtil.sanitizeTokenString(msgToken);
        SimpleApnsPushNotification pushNotification = new SimpleApnsPushNotification(token,
                bundleName, payload, new Date(System.currentTimeMillis() + DEFAULT_EXPIRATION_PERIOD_MILLIS),
                DeliveryPriority.IMMEDIATE, "person");

        client.sendNotification(pushNotification)
                .addListener((GenericFutureListener<Future<PushNotificationResponse<SimpleApnsPushNotification>>>) future -> {
                    if (future.isSuccess()) {
                        PushNotificationResponse<SimpleApnsPushNotification> response = future.getNow();
                        if (response.isAccepted()) {
                            log.info("1");
                        } else {
                            log.info("2");
                        }
                    } else {
                        log.info("3");
                    }
                });
        Thread.currentThread().join();
    }

//    @Test
    public void test2() throws Exception {
        String credentialPath1 = "/Users/<USER>/test/ios_push_prod_20211027.p12";
        String credentialPath2 = "/Users/<USER>/test/com_acesee_lsplatform_pro.p12";
        byte[] bytes1 = IOUtils.toByteArray(new FileInputStream(credentialPath1));
        byte[] bytes2 = IOUtils.toByteArray(new FileInputStream(credentialPath2));
        Assert.assertTrue(Arrays.equals(bytes1, bytes2));
    }
}
