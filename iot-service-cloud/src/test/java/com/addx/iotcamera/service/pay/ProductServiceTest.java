package com.addx.iotcamera.service.pay;

import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.dao.IProductDAO;
import com.addx.iotcamera.service.ProductService;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ProductServiceTest {
    @InjectMocks
    private ProductService productService;

    @Mock
    private IProductDAO iProductDAO;

    @Test
    @DisplayName("查询指定类型商品")
    public void test_queryProductListByType() {
        {
            when(iProductDAO.queryProductByType(any(), any(), any(), any())).thenReturn(Lists.newArrayList());

            List<ProductDO> expectResult = Lists.newArrayList();
            List<ProductDO> actualResult = productService.queryProductListByType(Lists.newArrayList(), TENANTID_VICOO, false, 0);
            Assert.assertEquals(expectResult, actualResult);
        }
        {
            when(iProductDAO.queryProductByType(any(), any(), any(), any())).thenReturn(Lists.newArrayList());

            List<ProductDO> expectResult = Lists.newArrayList();
            List<ProductDO> actualResult = productService.queryProductListByType(Lists.newArrayList(), TENANTID_VICOO, false, 1);
            Assert.assertEquals(expectResult, actualResult);
        }
        {
            when(iProductDAO.queryProductByType(any(), any(), any(), any())).thenReturn(Lists.newArrayList());

            List<ProductDO> expectResult = Lists.newArrayList();
            List<ProductDO> actualResult = productService.queryProductListByType(Lists.newArrayList(), TENANTID_VICOO, true, 1);
            Assert.assertEquals(expectResult, actualResult);
        }
    }

    @Test
    public void test_queryTierProductType() {
        when(iProductDAO.queryProductTypeByTier(any())).thenReturn(1);
        int expectResult = 1;
        int actualResult = productService.queryTierProductType(1);
        Assert.assertEquals(expectResult, actualResult);
    }

    @Test
    public void test_queryAdditionalProductFreeList() {
        List<ProductDO> list = Arrays.asList(new ProductDO().setId(RandomUtils.nextInt(1000_0000, 10000_0000)));
        when(iProductDAO.queryAdditionalProductFreeList()).thenReturn(list);
        List<ProductDO> result = productService.queryAdditionalProductFreeList();
        Assert.assertEquals(list, result);
    }
}
