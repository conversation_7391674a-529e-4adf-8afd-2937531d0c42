package com.addx.iotcamera.service;

import com.addx.iotcamera.config.EsConfig;
import com.addx.iotcamera.config.NetTestConfig;
import com.addx.iotcamera.config.S3;
import com.addx.iotcamera.dao.ZoneDictDAO;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.addx.iotcamera.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.device_msg.CloudIperfServer;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class NetTestServiceTest {

    @Mock
    private NetTestConfig netTestConfig;
    @Mock
    private EsService esService;
    @Mock
    private ZoneDictDAO zoneDictDAO;
    @Mock
    private S3 s3;
    @Mock
    private AmazonS3 s3Client;
    @Mock
    private OpenApiConfigService openApiConfigService;
    @Mock
    private MqttSender mqttSender;
    @InjectMocks
    private NetTestService netTestService;

    @Before
    public void init() {
        VernemqPublisher vernemqPublisher = new VernemqPublisher();
        ReflectUtil.setPrivateField(vernemqPublisher, "mqttSender", mqttSender);
        vernemqPublisher.init();
        when(esService.save(any(EsConfig.IndexKey.class), any(), any())).thenReturn(true);

        when(zoneDictDAO.getCountryNameZhByCountryNo(anyString())).thenAnswer(it -> it.getArgument(0) + "_zh");
        when(netTestConfig.getTraceroute()).thenReturn(new NetTestConfig.TracerouteConfig() {{
            setCoturnPort(8888);
        }});
        when(netTestConfig.getIperf3()).thenReturn(new NetTestConfig.Iperf3Config() {{
            setPassword("pwd");
            setUsername("name");
            setServers(new LinkedHashMap<>(ImmutableMap.<String, CloudIperfServer>builder()
                    .put("cn", JSON.parseObject("{ title: '中国', ip: '**************', port: 5201 }", CloudIperfServer.class))
                    .put("us", JSON.parseObject("{ title: '美国', ip: '************', port: 5201 }", CloudIperfServer.class))
                    .put("eu", JSON.parseObject("{ title: '欧洲', ip: '***********', port: 5201 }", CloudIperfServer.class))
                    .build()));
        }});
        when(s3.getLookBackBuckets()).thenReturn(new HashSet<>(Arrays.asList(
                "addx-test-vip-none", "addx-test-vip-basic", "addx-test-vip-plus", "addx-test-vip-pro"
        )));
        when(s3.getBucket()).thenReturn("addx-test");
        when(s3Client.getUrl(anyString(), any())).thenAnswer(it -> {
            String url = "https://" + it.getArgument(0) + ".s3.amazon.com/" + it.getArgument(1);
            return new URL(url);
        });
    }

    @Test
    public void test_buildIperf3Arguments() {
        JSONObject input = new JSONObject().fluentPut("remoteHost", "*************");
        String arguments = NetTestService.buildIperf3Arguments(input);
        Assert.assertEquals("-c ************* -t 5 -b 100M -i 1 -J -V", arguments);
        Map<NetTestService.Iperf3ArgKey, String> map2 = NetTestService.parseIperf3Arguments(arguments);
        String arguments2 = NetTestService.buildIperf3Arguments(map2);
        Assert.assertEquals(arguments, arguments2);
    }

    @Test
    public void test_sendNetTestCommand() {
        JSONObject input = new JSONObject();
        {
            Result result = netTestService.sendNetTestCommand(input);
            Assert.assertEquals(new Integer(102), result.getResult());
        }
        String sn = OpenApiUtil.shortUUID();
        input.put("serialNumber", sn);
        {
            Result result = netTestService.sendNetTestCommand(input);
            Assert.assertEquals(new Integer(102), result.getResult());
        }
        {
            input.put("type", "iperf3");
            Result result = netTestService.sendNetTestCommand(input);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        {
            input.put("type", "wifiscanf");
            Result result = netTestService.sendNetTestCommand(input);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        {
            input.put("type", "traceroute");
            Result result = netTestService.sendNetTestCommand(input);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
    }

    @Test
    public void test_handleNetTestResult() {
        String sn = OpenApiUtil.shortUUID();
        JSONObject payload = new JSONObject();
        AssertUtil.assertNotException(() -> netTestService.handleNetTestResult(sn, payload));
        payload.put("id", OpenApiUtil.shortUUID());
        payload.put("value", new JSONObject().fluentPut("type", "iperf3")
                .fluentPut("arguments", " -c ************* -JV")
                .fluentPut("output", "{}")
        );
        AssertUtil.assertNotException(() -> netTestService.handleNetTestResult(sn, payload));
    }

    @Test
    public void test_queryNetTestResult() {
        {
            String id1 = OpenApiUtil.shortUUID();
            when(esService.queryById(any(EsConfig.IndexKey.class), eq(id1))).thenReturn(null);
            Result<JSONObject> result = netTestService.queryNetTestResult(id1);
            Assert.assertEquals(new Integer(400), result.getResult());
        }
        {
            String id1 = OpenApiUtil.shortUUID();
            when(esService.queryById(any(EsConfig.IndexKey.class), eq(id1))).thenReturn(new JSONObject());
            Result<JSONObject> result = netTestService.queryNetTestResult(id1);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
    }

    @Test
    public void test_queryNetTestCmdByDeviceSn() {
        {
            String sn = OpenApiUtil.shortUUID();
            when(esService.searchByTimestampDesc(any(EsConfig.IndexKey.class), any(), any(), any(), any())).thenReturn(null);
            Result<List<JSONObject>> result = netTestService.queryNetTestCmdByDeviceSn(sn);
            Assert.assertEquals(Result.failureFlag, result.getResult());
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(esService.searchByTimestampDesc(any(EsConfig.IndexKey.class), any(), any(), any(), any())).thenReturn(Arrays.asList());
            Result<List<JSONObject>> result = netTestService.queryNetTestCmdByDeviceSn(sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
    }

    @Test
    public void test_queryNetTestResultByDeviceSn() {
        {
            String sn = OpenApiUtil.shortUUID();
            when(esService.searchByTimestampDesc(any(EsConfig.IndexKey.class), any(), any(), any(), any())).thenReturn(null);
            Result<List<JSONObject>> result = netTestService.queryNetTestResultByDeviceSn(sn);
            Assert.assertEquals(Result.failureFlag, result.getResult());
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(esService.searchByTimestampDesc(any(EsConfig.IndexKey.class), any(), any(), any(), any())).thenReturn(Arrays.asList());
            Result<List<JSONObject>> result = netTestService.queryNetTestResultByDeviceSn(sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
    }


    @Test
    public void test_queryNetTestCmdWithResultByDeviceSn() {
        List<JSONObject> cmds = Arrays.asList(
                new JSONObject().fluentPut("id", 1).fluentPut("@timestamp", 10),
                new JSONObject().fluentPut("id", 2).fluentPut("@timestamp", 20),
                new JSONObject().fluentPut("id", 3).fluentPut("@timestamp", 30),
                new JSONObject().fluentPut("id", 4).fluentPut("@timestamp", 40),
                new JSONObject().fluentPut("id", 5).fluentPut("@timestamp", 50)
        );
        List<JSONObject> results = Arrays.asList(
                new JSONObject().fluentPut("id", 1).fluentPut("@timestamp", 15).fluentPut("output", "output1"),
                new JSONObject().fluentPut("id", 3).fluentPut("@timestamp", 35).fluentPut("output", "output1"),
                new JSONObject().fluentPut("id", 5).fluentPut("@timestamp", 70).fluentPut("output", "output1")
        );
        Map<EsConfig.IndexKey, List<JSONObject>> indexKey2List = new LinkedHashMap<>();
        indexKey2List.put(EsConfig.INDEX_NET_TEST_CMD, cmds);
        indexKey2List.put(EsConfig.INDEX_NET_TEST_RESULT, results);
        when(esService.searchByTimestampDesc(any(), any(), any(), any(), any())).thenAnswer(it -> {
            Long beginTime = it.getArgument(2);
            Long endTime = it.getArgument(3);
            Integer limitNum = it.getArgument(4);
            return indexKey2List.get(it.getArgument(0)).stream().filter(cmd -> {
                Long time = cmd.getLong("@timestamp");
                return (beginTime == null || time >= beginTime) && (endTime == null || time < endTime);
            }).limit(limitNum != null ? limitNum : Integer.MAX_VALUE).collect(Collectors.toList());
        });
        String sn = OpenApiUtil.shortUUID();
        {
            List<JSONObject> list = netTestService.queryNetTestCmdWithResultByDeviceSn(sn, null, null, null);
            Assert.assertEquals(5, list.size());
        }
        {
            List<JSONObject> list = netTestService.queryNetTestCmdWithResultByDeviceSn(sn, null, null, 0);
            Assert.assertEquals(0, list.size());
        }
        {
            List<JSONObject> list = netTestService.queryNetTestCmdWithResultByDeviceSn(sn, null, 15L, 5);
            Assert.assertEquals(1, list.size());
        }
        {
            List<JSONObject> list = netTestService.queryNetTestCmdWithResultByDeviceSn(sn, 20L, 50L, null);
            Assert.assertEquals(3, list.size());
            Assert.assertEquals(1, list.stream().filter(it -> it.get("output") != null).count());
        }
        {
            List<JSONObject> list = netTestService.queryNetTestCmdWithResultByDeviceSn(sn, 20L, 50L, 2);
            Assert.assertEquals(2, list.size());
            Assert.assertEquals(1, list.stream().filter(it -> it.get("output") != null).count());

        }
    }

}
