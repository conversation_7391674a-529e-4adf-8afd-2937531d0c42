package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.msg.MsgEntityBase;
import com.addx.iotcamera.bean.msg.VideoMsgEntity;
import com.addx.iotcamera.enums.PushTypeEnums;
import com.addx.iotcamera.publishers.notification.Firebase.FcmV1Config;
import com.addx.iotcamera.publishers.notification.Firebase.FcmV1Entity;
import com.addx.iotcamera.publishers.notification.PushArgs;
import com.addx.iotcamera.service.message.FcmV1Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class FcmV1ServiceTest {

    @InjectMocks
    private FcmV1Service fcmV1Service;

    @Mock
    private ReportLogService reportLogService;
    @Mock
    private GoogleCredentials credentials;
    @Mock
    private CloseableHttpClient httpClient;
    @Mock
    private CloseableHttpResponse httpResponse;
    @Mock
    private HttpEntity httpEntity;
    @Mock
    private StatusLine statusLine;
    @Mock
    private FcmV1Config fcmV1Config;

    private static final String bundleId = "comcksmartlife"; // tenantId=cantonk,packageName=com.ck.smartlife
    private static final PushTypeEnums putType = PushTypeEnums.PUSH_FCM;
    private AccessToken accessToken = new AccessToken("mockToken", new Date());

    @Before
    @SneakyThrows
    public void init() {
        fcmV1Service.getBundle2Config().put(bundleId, fcmV1Config);
        when(fcmV1Config.getProjectId()).thenReturn("productId_123");
        when(fcmV1Config.getAccessToken()).thenReturn(accessToken);

        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        when(httpEntity.getContent()).thenAnswer(it -> new ByteArrayInputStream("123".getBytes(StandardCharsets.UTF_8)));
        when(httpResponse.getStatusLine()).thenReturn(statusLine);
    }

    @Test
    public void test_FcmV1Entity() {
        FcmV1Entity entity = new FcmV1Entity();
        entity.setAndroid(new FcmV1Entity.Android().setPriority(FcmV1Entity.AndroidMessagePriority.HIGH));
        entity.setAndroid(new FcmV1Entity.Android().setPriority(FcmV1Entity.AndroidMessagePriority.NORMAL));
    }

    @Test
    public void test_FcmV1Config_getAccessToken() {
        FcmV1Config fcmV1Config = new FcmV1Config();
        {
            Assert.assertNull(fcmV1Config.getAccessToken());
        }
        fcmV1Config.setCredentials(credentials);
        {
            Assert.assertNull(fcmV1Config.getAccessToken());
        }
    }

    @Test
    public void test_pushMessage() {
        {
            boolean result = fcmV1Service.pushMessage("comnothing", null, false);
            Assert.assertFalse(result);
        }
        {
            boolean result = fcmV1Service.pushMessage("commock", null, false);
            Assert.assertFalse(result);
        }
    }

    @Test
    @SneakyThrows
    public void test_pushMessageAsync() {
        PushArgs pushArgs = new PushArgs();
        pushArgs.setBundleName("com.ck.smartlife");
        pushArgs.setUserId(123);
        pushArgs.setMsgType(PushTypeEnums.PUSH_FCM_V1.getCode());
        pushArgs.setMsgToken("ciSy1P20Ss6r-vOnBMcmpP:APA91bHq20RMQE9cgVREft345YU4xyZxq-PUXWtj_G9zMRbP-gNLtgeoAh5mAB6iDbBdevDH3WhHg5g3jjV5T8dn1QI_9guXOuz6VhxbmIq7rc0CNzT-VT1GI2OuzVZif-iikxWgFPL0");
        MsgEntityBase msg = new MsgEntityBase();

        {
            when(fcmV1Config.getAccessToken()).thenReturn(null);
            boolean result = fcmV1Service.pushMessage(msg, pushArgs);
            Assert.assertFalse(result);
        }
        when(fcmV1Config.getAccessToken()).thenReturn(accessToken);
        {
            fcmV1Service.setHttpClientFactory(() -> {
                throw new RuntimeException("mock httpClientFactory error!");
            });
            boolean result = fcmV1Service.pushMessage(msg, pushArgs);
            Assert.assertFalse(result);
        }
        fcmV1Service.setHttpClientFactory(() -> httpClient);
        {
            when(statusLine.getStatusCode()).thenReturn(500);
            boolean result = fcmV1Service.pushMessage(msg, pushArgs);
            Assert.assertFalse(result);
        }
        {
            when(statusLine.getStatusCode()).thenReturn(200);
            boolean result = fcmV1Service.pushMessage(msg, pushArgs);
            assertTrue(result);
        }

        VideoMsgEntity msg2 = new VideoMsgEntity();
        fcmV1Service.pushMessage(msg2, pushArgs);
    }

    @Test
    public void test_pushMessage_real() {
        final FcmV1Service service = new FcmV1Service();
        final String bundleId = "comheospeedflux";
        final String reqBodyStr = "{\"validate_only\":false,\"message\":{\"android\":{\"priority\":\"HIGH\"},\"data\":{\"title\":\"Smart Camera\",\"body\":\"相机检测到了运动\",\"msgId\":\"1123\",\"type\":\"2\",\"timestamp\":\"1672301123\",\"serialNumber\":\"48fd067d7f81ddae7e2b947284ca1d00\"},\"token\":\"ecs_04QHTwWAKHoOUBqg1b:APA91bHjMSUtAC1xSnlWarnb-lZ01HOG_LlEqlUHhYDgnASntPt1xFXdcDZlb51Dd-olF14bA-YQI1i4zvuXQVWjd-4_nGkwaNTVTJsi4GDTzoJ05JF2Up1T-qZ6wPFSMXnzGVq_3VlZ\"}}";
        final JSONObject reqBody = JSON.parseObject(reqBodyStr);
//        final FcmV1Entity entity = reqBody.getObject("message", FcmV1Entity.class);
//        final boolean flag = service.pushMessage(bundleId, entity, reqBody.getBooleanValue("validate_only"));
        log.info("test_pushMessage_real: flag={}", JSON.toJSONString(reqBody.getJSONObject("message"),true));
    }
    @Test
    public void testCheckIfConfigExist_WhenConfigExists() {
        FcmV1Config config = new FcmV1Config();
        assertTrue(fcmV1Service.checkIfConfigExist(bundleId));
    }

}
