package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.pay.GoogleCancelOrderDO;
import com.addx.iotcamera.config.apollo.GlobalConfig;
import com.addx.iotcamera.config.pay.GooglePaySandboxConfig;
import com.addx.iotcamera.config.serve.ServeConfig;
import com.addx.iotcamera.dao.pay.IPaymentFlowDAO;
import com.addx.iotcamera.service.pay.GooglePayService;
import com.addx.iotcamera.service.user.UserAppScoreService;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import com.google.api.client.util.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PaymentServiceTest {

    @InjectMocks
    private PaymentService paymentService;

    @Mock
    private GlobalConfig globalConfig;

    @Mock
    private IPaymentFlowDAO iPaymentFlowDAO;

    @Mock
    private UserAppScoreService userAppScoreService;

    @Mock
    private GooglePayService googlePayService;

    @Mock
    private UserService userService;

    @Mock
    private ServeConfig serveConfig;

    @Mock
    private GooglePaySandboxConfig googlePaySandboxConfig;

    @Mock
    private UserVipService userVipService;

    @Test
    @DisplayName("非第一次支付，不需要调用评分引导方法")
    public void verifyPaymentCount_hasPayment(){
        when(iPaymentFlowDAO.queryPaymentFlowCountExclusion(any(),any())).thenReturn(2);
        paymentService.verifyPaymentCount(any(),any());
        verify(userAppScoreService, times(0)).userAppScoreMoment( any(),any());
    }

    @Test
    @DisplayName("第一次支付，需要记录评分引导记录")
    public void verifyPaymentCount_noPayment(){
        when(iPaymentFlowDAO.queryPaymentFlowCountExclusion(any(),any())).thenReturn(0);
        doNothing().when(userAppScoreService).userAppScoreMoment( any(),any());
        paymentService.verifyPaymentCount(any(),any());
        verify(userAppScoreService, times(1)).userAppScoreMoment( any(),any());
    }


    @Test
    public void test_queryGoogleCancelOrder_tenant_empty(){
        when(globalConfig.getTenant()).thenReturn(Lists.newArrayList());
        Boolean exceptedResult = false;
        Boolean actualResult = paymentService.queryGoogleCancelOrder();
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    public void test_queryGoogleCancelOrder_tenant_no_cancel(){
        when(globalConfig.getTenant()).thenReturn(Arrays.asList("vicoo"));
        when(googlePayService.queryCancelOrderList(any())).thenReturn(Lists.newArrayList());

        Boolean exceptedResult = true;
        Boolean actualResult = paymentService.queryGoogleCancelOrder();
        Assert.assertEquals(exceptedResult,actualResult);
    }


    @Test
    public void test_queryGoogleCancelOrder_tenant(){
        when(globalConfig.getTenant()).thenReturn(Arrays.asList("vicoo"));
        GoogleCancelOrderDO.VoidedPurchaseDO purchaseDO = new GoogleCancelOrderDO.VoidedPurchaseDO();
        purchaseDO.setOrderId("tradeNo");
        purchaseDO.setPurchaseTimeMillis(System.currentTimeMillis());
        purchaseDO.setVoidedTimeMillis(System.currentTimeMillis());

        purchaseDO.setVoidedReason(4);
        List<GoogleCancelOrderDO.VoidedPurchaseDO> list = Lists.newArrayList();
        list.add(purchaseDO);
        when(googlePayService.queryCancelOrderList(any())).thenReturn(list);

        PaymentFlow paymentFlow = new PaymentFlow();
        paymentFlow.setTradeNo("tradeNo");
        List<PaymentFlow> paymentFlows = Lists.newArrayList();
        paymentFlows.add(paymentFlow);

        when(iPaymentFlowDAO.queryPaymentFlowNoRefundBatch(any())).thenReturn(paymentFlows);
        when(userService.queryUserById(any())).thenReturn(new User());
        when(serveConfig.getServeReleaseTag()).thenReturn("");

        when(iPaymentFlowDAO.updateRefundOrderInfo(any())).thenReturn(1);

        doNothing().when(userVipService).userVipRefundOrUpgrade(any(),any());

        Boolean exceptedResult = true;
        Boolean actualResult = paymentService.queryGoogleCancelOrder();
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    @DisplayName("是否沙盒账号-非tenantId-非沙盒")
    public void test_isSandboxOrder_not_sandbox(){
        User user = new User();
        user.setEmail("email");
        user.setTenantId(TENANTID_VICOO);
        when(userService.queryUserById(any())).thenReturn(user);
        when(googlePaySandboxConfig.getConfig()).thenReturn(Maps.newHashMap());
        Boolean expectedResult = false;
        Boolean actualResult = paymentService.isSandboxOrder(1);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("是否沙盒账号-非tenantId-非沙盒")
    public void test_isSandboxOrder_not_email_sandbox(){
        User user = new User();
        user.setEmail("email");
        user.setTenantId(TENANTID_VICOO);
        when(userService.queryUserById(any())).thenReturn(user);

        Map<String,Set<String>> map = Maps.newHashMap();
        map.put(TENANTID_VICOO,new HashSet<>());
        when(googlePaySandboxConfig.getConfig()).thenReturn(map);
        Boolean expectedResult = false;
        Boolean actualResult = paymentService.isSandboxOrder(1);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("是否沙盒账号-沙盒")
    public void test_isSandboxOrder_sandbox(){
        String email = "email";

        User user = new User();
        user.setEmail(email);
        user.setTenantId(TENANTID_VICOO);
        when(userService.queryUserById(any())).thenReturn(user);

        Map<String,Set<String>> map = Maps.newHashMap();
        Set<String> sandboxSet = Sets.newHashSet();
        sandboxSet.add(email);
        map.put(TENANTID_VICOO,sandboxSet);

        when(googlePaySandboxConfig.getConfig()).thenReturn(map);
        Boolean expectedResult = true;
        Boolean actualResult = paymentService.isSandboxOrder(1);
        Assert.assertEquals(expectedResult,actualResult);
    }
}
