package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AppAiConfigRequest;
import com.addx.iotcamera.bean.db.AppAiConfigDO;
import com.addx.iotcamera.bean.db.AppAiConfigGlobalDO;
import com.addx.iotcamera.dao.AppAiConfigDAO;
import com.addx.iotcamera.dao.AppAiConfigGlobalDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;
import java.util.Objects;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppAIServiceTest {

    @InjectMocks
    private AppAIService appAIService;
    @Mock
    private AppAiConfigDAO appAiConfigDAO;
    @Mock
    private AppAiConfigGlobalDAO appAiConfigGlobalDAO;

    @Test
    public void test() throws Exception {
        Mockito.when(appAiConfigGlobalDAO.getByDeivceCode(Mockito.any(), Mockito.any())).thenReturn(null);
        Map map = appAIService.queryAiCapability(0, null, null);
        Assert.assertTrue(map == null || map.isEmpty());
        Mockito.when(appAiConfigGlobalDAO.getByDeivceCode(Mockito.any(), Mockito.any())).thenReturn(new AppAiConfigGlobalDO(){{
        }});
        map = appAIService.queryAiCapability(0, null, null);
        Assert.assertTrue(map == null || map.isEmpty());

        Mockito.when(appAiConfigGlobalDAO.getByDeivceCode(Mockito.any(), Mockito.any())).thenReturn(new AppAiConfigGlobalDO(){{
            setTemplate("{\"key1\": \"${key1}\"}");
        }});
        map = appAIService.queryAiCapability(0, null, null);
        Assert.assertTrue(map != null && StringUtils.equals((String)map.get("key1"), "${key1}"));

        Mockito.when(appAiConfigGlobalDAO.getByDeivceCode(Mockito.any(), Mockito.any())).thenReturn(new AppAiConfigGlobalDO(){{
            setTemplate("{\"key1\": \"${key1}\", \"key2\": ${key2}}");
            setParamList("[{\"name\": \"key1\", \"value\": \"val\"}, {\"name\": \"key2\", \"value\": \"1\"}]");
        }});
        map = appAIService.queryAiCapability(0, null, null);
        Assert.assertTrue(map != null && StringUtils.equals((String)map.get("key1"), "val") && Objects.equals(map.get("key2"), 1));

        appAIService.updateAiCapability(0, new AppAiConfigRequest());
        Mockito.verify(appAiConfigDAO, Mockito.atLeastOnce()).insert(Mockito.any());

        Mockito.when(appAiConfigDAO.getByUserAndDeivceCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new AppAiConfigDO(){{
            setId(1L);
        }});
        appAIService.updateAiCapability(0, new AppAiConfigRequest(){{
            setMagicPix(new MagicPixConfig());
        }});
        Mockito.verify(appAiConfigDAO, Mockito.atLeastOnce()).updateById(Mockito.any());
    }

}
