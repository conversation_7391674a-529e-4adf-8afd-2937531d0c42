package com.addx.iotcamera.service.device_msg;

import com.addx.iotcamera.bean.device_msg.*;
import com.addx.iotcamera.bean.domain.DeviceStateDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.device.KeepAliveParams;
import com.addx.iotcamera.controller.safertc.SafeRTCBxController;
import com.addx.iotcamera.enums.DeviceOnlineStatusEnums;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.kiss.KissNodeType;
import com.addx.iotcamera.kiss.bean.KissApiResult;
import com.addx.iotcamera.kiss.node.KissNode;
import com.addx.iotcamera.mqtt.MqttPayload;
import com.addx.iotcamera.mqtt.enums.EOutputTopicType;
import com.addx.iotcamera.mqtt.enums.ERequestAction;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.URI;
import java.util.*;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class KissWsServiceTest {

    @InjectMocks
    private KissWsService kissWsService;

    @Mock
    private DeviceMsgService deviceMsgService;
    // 设备sn -> 设备在所在kiss节点
    @Mock
    private KissDeviceNodeManager sn2KissDeviceNode;
    @Mock
    private DeviceMsgSrcManager deviceMsgSrcManager;
    @Mock
    private KissWsClient wsClient;
    @Mock
    private KissWsClient wsClient2;
//    @Mock
//    private KissWsClient kissWsClient;

    @Mock
    private UserRoleService userRoleService;
    @Mock
    private UserService userService;
    @Mock
    private DeviceInfoService deviceInfoService;

    @Before
    public void before() {
//        kissWsService.setKissWebsocketPort(18888);
        kissWsService.setKissWsClientFactory(uri -> new KissWsClientMock(uri));
    }

    @Getter
    @Setter
    public static class KissWsClientMock extends KissWsClient {

        private boolean connectCalled = false;
        private boolean reconnectCalled = false;
        private boolean isClosed = false;

        public KissWsClientMock(URI serverUri) {
            super(serverUri);
        }

        @Override
        public void connect() {
            connectCalled = true;
        }

        @Override
        public void reconnect() {
            reconnectCalled = true;
        }

        private LinkedList<String> sendTextList = new LinkedList<>();

        @Override
        public void send(String text) {
            sendTextList.add(text);
        }

        @Override
        public boolean isOpen() {
            return !isClosed;
        }
    }

    @Test
    public void test_msgNameOf() {
        final EReportEvent reportEvent = EReportEvent.DETECT_PIR_RESULT;

        Assert.assertEquals("detectPirResult", reportEvent.getMsgName());
        Assert.assertEquals(reportEvent, EReportEvent.msgNameOf("detectPirResult"));
    }

    //    @Test
    public void test_refreshKissWsClient1() {
        {
            Map<String, KissNode> ip2KissNode = new LinkedHashMap<>();
            {
                ip2KissNode.put("127.0.0.1", new KissNode() {{
                    setRemoving(0);
                }});
                ip2KissNode.put("*********", null);
                ip2KissNode.put("*********", new KissNode() {{
                    setRemoving(1);
                }});
            }
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, ip2KissNode);
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, ip2KissNode);
        }
        {
            Map<String, KissNode> ip2KissNode = new LinkedHashMap<>();
            {
                ip2KissNode.put("*********", new KissNode() {{
                    setRemoving(0);
                }});
                ip2KissNode.put("127.0.0.1", new KissNode() {{
                    setRemoving(1);
                }});
            }
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, ip2KissNode);
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, ip2KissNode);
        }

    }

    @Test
    public void test_refreshKissWsClient() {
        {
            Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, kissNodeMap);
            final Map<String, KissWsClient> ip2KissWsClient = kissWsService.getIp2KissWsClient().getMap();
            Assert.assertEquals(0, ip2KissWsClient.size());
        }
        String innerApiAddr = "***********";
        {
            Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
            kissNodeMap.put(innerApiAddr, new KissNode() {{
                setRemoving(0);
                setInnerApiAddr(innerApiAddr);
            }});
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, kissNodeMap);
            final Map<String, KissWsClient> ip2KissWsClient = kissWsService.getIp2KissWsClient().getMap();
            Assert.assertEquals(1, ip2KissWsClient.size());
            final KissWsClientMock kissWsClient = (KissWsClientMock) ip2KissWsClient.get(innerApiAddr);
            Assert.assertNotNull(kissWsClient);
            Assert.assertFalse(kissWsClient.isKissNodeRemoving());

            Assert.assertTrue(kissWsClient.isConnectCalled());
            Assert.assertFalse(kissWsClient.isReconnectCalled());
            kissWsClient.setClosed(true);
        }
        {
            Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
            kissNodeMap.put(innerApiAddr, new KissNode() {{
                setRemoving(0);
                setInnerApiAddr(innerApiAddr);
            }});
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, kissNodeMap);
            final Map<String, KissWsClient> ip2KissWsClient = kissWsService.getIp2KissWsClient().getMap();
            Assert.assertEquals(1, ip2KissWsClient.size());
            final KissWsClientMock kissWsClient = (KissWsClientMock) ip2KissWsClient.get(innerApiAddr);
            Assert.assertNotNull(kissWsClient);
            Assert.assertFalse(kissWsClient.isKissNodeRemoving());

            Assert.assertTrue(kissWsClient.isConnectCalled());
            Assert.assertTrue(kissWsClient.isReconnectCalled());
            kissWsClient.setClosed(false);
        }
        {
            Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
            kissNodeMap.put(innerApiAddr, new KissNode() {{
                setRemoving(1);
                setInnerApiAddr(innerApiAddr);
            }});
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, kissNodeMap);
            final Map<String, KissWsClient> ip2KissWsClient = kissWsService.getIp2KissWsClient().getMap();
            Assert.assertEquals(1, ip2KissWsClient.size());
            final KissWsClientMock kissWsClient = (KissWsClientMock) ip2KissWsClient.get(innerApiAddr);
            Assert.assertNotNull(kissWsClient);
            Assert.assertTrue(kissWsClient.isKissNodeRemoving());
        }
        {
            Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, kissNodeMap);
            final Map<String, KissWsClient> ip2KissWsClient = kissWsService.getIp2KissWsClient().getMap();
            Assert.assertEquals(0, ip2KissWsClient.size());
        }
    }

    @Test
    @SneakyThrows
    public void test_createKissWsClient() {
        final URI uri = URI.create("ws://**************/iot-service-channel/connect/1234");
        final KissWsClient kissWsClient = kissWsService.createKissWsClient(uri);
        Assert.assertNotNull(kissWsClient);
        kissWsClient.getKissDeviceNodesListener().accept(Arrays.asList(new KissDeviceNode()));
        kissWsClient.getRetainedMsgAckListener().accept(new DeviceMsgAck());
        kissWsClient.getCmdAckListener().accept(new DeviceMsgAck());

        {
            kissWsClient.getReqListener().accept(new IotWsReq<>().setName(IotWsReqName.deviceReportEvent.name())
                    .setValue(new JSONObject()));
        }
        {
            String json = "{\"id\":\"76fb19591a4e4b2398e892b9c31ace2b\",\"method\":\"IOT_WS_REQ\",\"name\":\"deviceReportEvent\",\"time\":1708920331463,\"value\":{\"serialNumber\":\"a7581545ba8b66b87b86b8055e4b270a\",\"name\":\"reportEvent\",\"time\":1708920331,\"id\":0,\"value\":{\"traceId\":\"03029381708920331f72e5ID44tTk\",\"event\":18}}}";
            IotWsReq iotWsReq = JSON.parseObject(json, IotWsReq.class);
            kissWsClient.getReqListener().accept(iotWsReq);
        }
        {
            final String clientId = OpenApiUtil.shortUUID();
            when(userRoleService.getCountryNoBySerialNumber(clientId)).thenReturn("CN");
            kissWsClient.getReqListener().accept(new IotWsReq<>().setName(IotWsReqName.queryClientCountryNo.name())
                    .setValue(new JSONObject().fluentPut("clientIds", Arrays.asList(clientId))));

        }
        {
            final String clientId = OpenApiUtil.shortUUID();
            when(userRoleService.getCountryNoBySerialNumber(clientId)).thenReturn(null);
            kissWsClient.getReqListener().accept(new IotWsReq<>().setName(IotWsReqName.queryClientCountryNo.name())
                    .setValue(new JSONObject().fluentPut("clientIds", Arrays.asList(clientId))));
        }
        kissWsClient.getRespListener().accept(new IotWsResp<>().setName(IotWsReqName.queryClientCountryNo.name()));
    }

    @Test
    public void test_getDeviceState() {
        final String sn = OpenApiUtil.shortUUID();
        {
            when(deviceMsgSrcManager.isWsReplaceMqtt(sn)).thenReturn(false);
            final DeviceStateDO deviceState = kissWsService.getDeviceState(sn);
            Assert.assertEquals(null, deviceState, null);
        }
        when(deviceMsgSrcManager.isWsReplaceMqtt(sn)).thenReturn(true);
        {
            when(sn2KissDeviceNode.get(sn)).thenReturn(null);
            final DeviceStateDO deviceState = kissWsService.getDeviceState(sn);
            Assert.assertEquals(null, deviceState, null);
        }
        {
            when(sn2KissDeviceNode.get(sn)).thenReturn(new KissDeviceNode().setDeviceStatus(KissDeviceStatus.normal)
                    .setLastUpdateTime(System.currentTimeMillis()).setDeviceSn(sn));
            final DeviceStateDO deviceState = kissWsService.getDeviceState(sn);
            Assert.assertEquals(DeviceOnlineStatusEnums.WAKE, deviceState.getOnlineStatus());
        }
        {
            when(sn2KissDeviceNode.get(sn)).thenReturn(new KissDeviceNode().setDeviceStatus(KissDeviceStatus.dormant)
                    .setLastUpdateTime(System.currentTimeMillis()).setDeviceSn(sn));
            final DeviceStateDO deviceState = kissWsService.getDeviceState(sn);
            Assert.assertEquals(DeviceOnlineStatusEnums.ONLINE, deviceState.getOnlineStatus());
        }
    }

    @Test
    public void test_wakeup() {
        final String sn = OpenApiUtil.shortUUID();
        final String traceId = OpenApiUtil.shortUUID();
        final String wakeup = OpenApiUtil.shortUUID();

        kissWsService.wakeup(sn, traceId, wakeup, true);

    }

    @Test
    public void test_wakeupByIp() {
        final String sn = OpenApiUtil.shortUUID();
        final String traceId = OpenApiUtil.shortUUID();
        final String wakeup = OpenApiUtil.shortUUID();
        final String kissIp = OpenApiUtil.shortUUID();

        kissWsService.wakeupByKissIp(kissIp, sn, traceId, wakeup, true);

    }

    @Test
    public void test_sendDeviceMsg() {
        final String sn = OpenApiUtil.shortUUID();
        final String msg = OpenApiUtil.shortUUID();
        final String kissIp = OpenApiUtil.shortUUID();
        {
            when(sn2KissDeviceNode.get(sn)).thenReturn(null);
            final KissApiResult result = kissWsService.sendDeviceMsg(sn, msg);
            Assert.assertEquals(-200, result.getCode());
        }
        when(sn2KissDeviceNode.get(sn)).thenReturn(new KissDeviceNode().setKissIp(kissIp));
        {
            final KissApiResult result = kissWsService.sendDeviceMsg(sn, msg);
            Assert.assertEquals(-200, result.getCode());
        }
        kissWsService.getIp2KissWsClient().put(kissIp, wsClient);
        {
            when(wsClient.isOpen()).thenReturn(false);
            final KissApiResult result = kissWsService.sendDeviceMsg(sn, msg);
            Assert.assertEquals(-200, result.getCode());
        }
        when(wsClient.isOpen()).thenReturn(true);
        final KissApiResult result = kissWsService.sendDeviceMsg(sn, msg);
        Assert.assertEquals(result.getMsg(), 0, result.getCode());

    }

    @Test
    public void test_sendDeviceMsgByIp() {
        final String sn = OpenApiUtil.shortUUID();
        final String msg = OpenApiUtil.shortUUID();
        final String kissIp = OpenApiUtil.shortUUID();

        when(sn2KissDeviceNode.get(sn)).thenReturn(new KissDeviceNode().setKissIp(kissIp));
        {
            final KissApiResult result = kissWsService.sendDeviceMsgByKissIp(kissIp, sn, msg);
            Assert.assertEquals(-200, result.getCode());
        }
        kissWsService.getIp2KissWsClient().put(kissIp, wsClient);
        {
            when(wsClient.isOpen()).thenReturn(false);
            final KissApiResult result = kissWsService.sendDeviceMsgByKissIp(kissIp, sn, msg);
            Assert.assertEquals(-200, result.getCode());
        }
        when(wsClient.isOpen()).thenReturn(true);
        final KissApiResult result = kissWsService.sendDeviceMsgByKissIp(kissIp, sn, msg);
        Assert.assertEquals(result.getMsg(), 0, result.getCode());

    }

    @Test
    public void test_sendDeviceMsgToAny() {
        when(wsClient.isOpen()).thenReturn(false);
        when(wsClient2.isOpen()).thenReturn(true);
        final List<KissWsClient> wcClients = Arrays.asList(wsClient, wsClient2);
        final KissApiResult result = kissWsService.sendDeviceMsgToAny(wcClients, "");
        Assert.assertEquals(result.getMsg(), 0, result.getCode());
    }

    @Test
    public void test_sendDeviceMsgToAll() {
        when(wsClient.isOpen()).thenReturn(false);
        when(wsClient2.isOpen()).thenReturn(true);
        final List<KissWsClient> wcClients = Arrays.asList(wsClient, wsClient2);
        final KissApiResult result = kissWsService.sendDeviceMsgToAll(wcClients, "");
        Assert.assertEquals(result.getMsg(), 0, result.getCode());
    }

    @Test
    public void test_sendRetainedMsgToDevice() {
        final String sn = OpenApiUtil.shortUUID();
        final String kissIp = OpenApiUtil.shortUUID();

        when(sn2KissDeviceNode.get(sn)).thenReturn(new KissDeviceNode().setKissIp(kissIp));
        kissWsService.getIp2KissWsClient().put(kissIp, wsClient);

        final JSONObject data = new JSONObject().fluentPut("name", EOutputTopicType.CONFIG.getValue()).fluentPut("id", OpenApiUtil.shortUUID());
        final MqttPayload payload = new MqttPayload(data.toJSONString(), null);
        {
            when(wsClient.isOpen()).thenReturn(false);
            kissWsService.sendRetainedMsgToDevice(EOutputTopicType.CONFIG.getValue(), sn, payload);
        }
        {
            when(wsClient.isOpen()).thenReturn(true);
            kissWsService.sendRetainedMsgToDevice(EOutputTopicType.CONFIG.getValue(), sn, payload);
        }
    }

    @Test
    public void test_sendCmdToDevice() {
        final String sn = OpenApiUtil.shortUUID();
        final String kissIp = OpenApiUtil.shortUUID();

        when(sn2KissDeviceNode.get(sn)).thenReturn(new KissDeviceNode().setKissIp(kissIp));
        kissWsService.getIp2KissWsClient().put(kissIp, wsClient);

        final JSONObject data = new JSONObject().fluentPut("name", ERequestAction.netTest.getValue()).fluentPut("id", OpenApiUtil.shortUUID());
        final MqttPayload payload = new MqttPayload(data.toJSONString(), null);
        {
            when(wsClient.isOpen()).thenReturn(false);
            kissWsService.sendCmdToDevice(sn, payload);
        }
        {
            when(wsClient.isOpen()).thenReturn(true);
            kissWsService.sendCmdToDevice(sn, payload);
        }
    }

    @Test
    public void test_updateKeepAliveParams() {
        final String sn = OpenApiUtil.shortUUID();
        final String kissIp = OpenApiUtil.shortUUID();

        when(sn2KissDeviceNode.get(sn)).thenReturn(new KissDeviceNode().setKissIp(kissIp));
        kissWsService.getIp2KissWsClient().put(kissIp, wsClient);

        final KeepAliveParams params = new KeepAliveParams();
        {
            when(wsClient.isOpen()).thenReturn(false);
            kissWsService.updateKeepAliveParams(sn, params);
        }
        {
            when(wsClient.isOpen()).thenReturn(true);
            kissWsService.updateKeepAliveParams(sn, params);
        }
    }

    @Test
    public void test_closeOldGroup() {
        final String sn = OpenApiUtil.shortUUID();
        final String kissIp = OpenApiUtil.shortUUID();

        when(sn2KissDeviceNode.get(sn)).thenReturn(new KissDeviceNode().setKissIp(kissIp));
        kissWsService.getIp2KissWsClient().put(kissIp, wsClient);

        {
            when(wsClient.isOpen()).thenReturn(false);
            kissWsService.closeOldGroup(sn);
        }
        {
            when(wsClient.isOpen()).thenReturn(true);
            kissWsService.closeOldGroup(sn);
        }
    }

    @Test
    public void test_IotWsReq_IotWsResp() {
        IotWsReq<Object> req1 = new IotWsReq<>();
        IotWsReq<Object> req2 = new IotWsReq<>(IotWsReqName.deviceReportEvent);
        IotWsReq<Object> req3 = new IotWsReq<>(IotWsReqName.queryClientCountryNo);
        Assert.assertNotNull(req3.toString());

        IotWsResp<Object> resp1 = new IotWsResp<>();
        IotWsResp<Object> resp2 = new IotWsResp<>(req2);
        IotWsResp<Object> resp3 = new IotWsResp<>(req3);
        Assert.assertNotNull(resp3.toString());
    }

    @Test
    public void test_getCountryNoByClientIds() {
        {
            final String clientId = OpenApiUtil.shortUUID();
            when(userRoleService.getCountryNoBySerialNumber(clientId)).thenReturn("CN");
            Map<String, String> result = kissWsService.getCountryNoByClientIds(Arrays.asList(clientId));
            Assert.assertEquals(Collections.singletonMap(clientId, "CN"), result);
        }
        {
            final String clientId = OpenApiUtil.shortUUID();
            when(userRoleService.getCountryNoBySerialNumber(clientId)).thenThrow(new RuntimeException("mock"));
            Map<String, String> result = kissWsService.getCountryNoByClientIds(Arrays.asList(clientId));
            Assert.assertEquals(Collections.emptyMap(), result);
        }
        {
            final String clientId = OpenApiUtil.shortUUID();
            when(userRoleService.getCountryNoBySerialNumber(clientId)).thenReturn(null);
            Map<String, String> result = kissWsService.getCountryNoByClientIds(Arrays.asList(clientId));
            Assert.assertEquals(Collections.emptyMap(), result);
        }
        {
            final Integer userId = new Random().nextInt(1000_0000);
            final String clientId = userId + "";
            when(userRoleService.getCountryNoBySerialNumber(clientId)).thenReturn(null);
            when(userService.queryUserById(userId)).thenReturn(null);
            Map<String, String> result = kissWsService.getCountryNoByClientIds(Arrays.asList(clientId));
            Assert.assertEquals(Collections.emptyMap(), result);
        }
        {
            final Integer userId = new Random().nextInt(1000_0000);
            final String clientId = userId + "";
            when(userRoleService.getCountryNoBySerialNumber(clientId)).thenReturn(null);
            when(userService.queryUserById(userId)).thenReturn(new User().setId(userId).setCountryNo(null));
            Map<String, String> result = kissWsService.getCountryNoByClientIds(Arrays.asList(clientId));
            Assert.assertEquals(Collections.emptyMap(), result);
        }
        {
            final Integer userId = new Random().nextInt(1000_0000);
            final String clientId = userId + "";
            when(userRoleService.getCountryNoBySerialNumber(clientId)).thenReturn(null);
            when(userService.queryUserById(userId)).thenReturn(new User().setId(userId).setCountryNo("US"));
            Map<String, String> result = kissWsService.getCountryNoByClientIds(Arrays.asList(clientId));
            Assert.assertEquals(Collections.singletonMap(clientId, "US"), result);
        }

    }

    @Test
    public void test_getUserIdFromLinkToken() {
        {
            String linkToken = "1539369AICLQ7BZH1V00271706534677";
            Integer userId = SafeRTCBxController.getUserIdFromLinkToken(linkToken);
            Assert.assertEquals(new Integer(1539369), userId);
        }
        {
            Integer userId = SafeRTCBxController.getUserIdFromLinkToken("abcd");
            Assert.assertEquals(null, userId);
        }
        {
            Integer userId = SafeRTCBxController.getUserIdFromLinkToken(null);
            Assert.assertEquals(null, userId);
        }
        {
            String linkToken = "1539369999999999999AICLQ7BZH1V00271706534677";
            Integer userId = SafeRTCBxController.getUserIdFromLinkToken(linkToken);
            Assert.assertEquals(null, userId);
        }
    }

    @Test
    public void test_refreshKissWsClient_createKissWsClient_null() {
        KissWsClientManager manager = new KissWsClientManager();
        manager.setKissWsClientFactory(null);

        KissNode kissNode = new KissNode();
        kissNode.setIntranetIp4("127.0.0.1");
        kissNode.setRemoving(0);
        Map<String, KissNode> ip2KissNode = new LinkedHashMap<>();
        ip2KissNode.put(kissNode.getIntranetIp4(), kissNode);

        manager.refreshKissWsClient(KissNodeType.kiss, ip2KissNode);
        Assert.assertTrue(manager.getMap().isEmpty());
        Assert.assertTrue(manager.keySet().isEmpty());
        Assert.assertTrue(manager.entrySet().isEmpty());
    }

    @Test
    public void test_refreshKissWsClient_error() {
        KissWsClientManager manager = new KissWsClientManager();
        manager.setKissWsClientFactory(null);

        manager.refreshKissWsClient(KissNodeType.kiss, null);
        Assert.assertTrue(manager.getMap().isEmpty());
        Assert.assertTrue(manager.keySet().isEmpty());
        Assert.assertTrue(manager.entrySet().isEmpty());
    }

    @Test
    public void test_broadcast() {
        KissNode kissNode1 = new KissNode() {{
            setIntranetIp4("***********");
            setRemoving(0);
        }};
        KissNode kissNode2 = new KissNode() {{
            setIntranetIp4("***********");
            setRemoving(0);
        }};
        KissNode kissNode3 = new KissNode() {{
            setIntranetIp4("***********");
            setRemoving(0);
        }};
        List<KissNode> kissNodes = Arrays.asList(kissNode1, kissNode2, kissNode3);

        kissWsService.setKissWsClientFactory(it -> {
            KissWsClientMock wsClient;
            if (kissNode2.getIntranetIp4().equals(it.getHost())) {
                wsClient = new KissWsClientMock(it);
                wsClient.setClosed(true);
            } else if (kissNode3.getIntranetIp4().equals(it.getHost())) {
                wsClient = new KissWsClientMock(it) {
                    @Override
                    public void send(String text) {
                        throw new RuntimeException("mock");
                    }
                };
                wsClient.setClosed(false);
            } else {
                wsClient = new KissWsClientMock(it);
                wsClient.setClosed(false);
            }
            return wsClient;
        });
        Map<String, KissNode> ip2KissNode = new LinkedHashMap<>();
        {
            ip2KissNode.clear();
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, ip2KissNode);
            JSONObject value = new JSONObject();
            Result<Map<String, String>> result = kissWsService.broadcast(value);
            Assert.assertTrue(Result.isSuccess(result));
        }
        {
            kissNodes.forEach(it -> ip2KissNode.put(it.getIntranetIp4(), it));
            kissWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kiss, ip2KissNode);
            JSONObject value = new JSONObject();
            Result<Map<String, String>> result = kissWsService.broadcast(value);
            Assert.assertTrue(Result.isSuccess(result));
            Assert.assertEquals(kissNode1 + "", "success", result.getData().get(kissNode1.getIntranetIp4()));
            Assert.assertEquals(kissNode2 + "", "notOpen", result.getData().get(kissNode2.getIntranetIp4()));
            Assert.assertEquals(kissNode3 + "", "error", result.getData().get(kissNode3.getIntranetIp4()));
        }
    }
}
