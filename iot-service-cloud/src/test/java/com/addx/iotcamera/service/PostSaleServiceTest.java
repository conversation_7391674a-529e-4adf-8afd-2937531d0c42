package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.device.KxDeviceSettingsDO;
import com.addx.iotcamera.bean.device_msg.KissDeviceNode;
import com.addx.iotcamera.bean.device_msg.RejectCloudRequest;
import com.addx.iotcamera.bean.device_msg.ReportDeviceInfoToCloud;
import com.addx.iotcamera.bean.domain.DeviceManualDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.dao.ZoneDictDAO;
import com.addx.iotcamera.dao.factory.DeviceManufactureDao;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device_msg.IotLocalService;
import com.addx.iotcamera.service.device_msg.KissDeviceNodeManager;
import com.addx.iotcamera.service.device_msg.KxDeviceSettingService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PostSaleServiceTest {

    @InjectMocks
    private PostSaleService postSaleService;
    @Mock
    private DeviceStatusService deviceStatusService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private DeviceManufactureDao deviceManufactureDao;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private ZoneDictDAO zoneDictDAO;
    @Mock
    private DeviceModelConfigService deviceModelConfigService;
    @Mock
    private FactoryDataQueryService factoryDataQueryService;
    @Mock
    private IotLocalService iotLocalService;
    @Mock
    private UserService userService;
    @Mock
    private KxDeviceSettingService kxDeviceSettingService;
    @Mock
    private KissDeviceNodeManager kissDeviceNodeManager;

    @Before
    @SneakyThrows
    public void before() {
        when(deviceSettingService.updateUserConfig(any(), any(), anyLong(), anyBoolean(), anyBoolean())).thenReturn(Result.Success());
        when(iotLocalService.getSupportCloudDeviceConfig(anyString())).thenReturn(true);
    }

    @Test
    public void test_queryWifiPowerLevel() {
        String userSn = OpenApiUtil.shortUUID();
        String sn = OpenApiUtil.shortUUID();
        {
            Result result = postSaleService.queryWifiPowerLevel("");
            Assert.assertEquals(new Integer(INVALID_PARAMS.getCode()), result.getResult());
        }
        {
            when(deviceManufactureDao.selectByUserSnOrSn(sn)).thenReturn(null);
            Result result = postSaleService.queryWifiPowerLevel(sn);
            Assert.assertEquals(new Integer(INVALID_PARAMS.getCode()), result.getResult());
        }
        when(deviceManufactureDao.selectByUserSnOrSn(sn)).thenReturn(new DeviceManufactureTableDO() {{
            setSerialNumber(sn);
            setUserSn(userSn);
        }});
        {
            when(userRoleService.getAdminUserBySn(sn)).thenReturn(Result.Error(INVALID_PARAMS));
            Result result = postSaleService.queryWifiPowerLevel(sn);
            Assert.assertEquals(new Integer(INVALID_PARAMS.getCode()), result.getResult());
        }
        when(userRoleService.getAdminUserBySn(sn)).thenReturn(new Result(new User() {{
            setId(123);
            setTenantId("guard");
        }}));
        {
            when(deviceManualService.getDeviceManualBySerialNumber(sn)).thenReturn(null);
            Result result = postSaleService.queryWifiPowerLevel(sn);
            Assert.assertEquals(Result.failureFlag, result.getResult());
        }
        when(deviceManualService.getDeviceManualBySerialNumber(sn)).thenReturn(new DeviceManualDO() {{
            setModelNo("modelNo");
            setUserSn(userSn);
            setOriginalModelNo("cg1");
            setModelNo("cg1x");
            setDisplayModelNo("cg1xy");
        }});
        {
            Result result = postSaleService.queryWifiPowerLevel(sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        when(userRoleService.getAdminUserBySn(sn)).thenReturn(new Result(new User() {{
            setId(123);
            setTenantId("guard");
            setCountryNo("cn");
        }}));
        when(zoneDictDAO.getCountryNameZhByCountryNo("cn")).thenReturn("中国");
        {
            Result result = postSaleService.queryWifiPowerLevel(sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        when(deviceStatusService.queryDeviceStatusBySerialNumber(sn)).thenReturn(new DeviceStatusDO() {{
            setSignalStrength(-59);
            setWifiPowerLevel(6);
        }});
        when(deviceInfoService.getDeviceSupport(sn)).thenReturn(new CloudDeviceSupport() {{
            setSupportWifiPowerLevel(true);
            setIsShield(false);
        }});
        when(deviceModelConfigService.queryDeviceModelConfig(sn)).thenReturn(new DeviceModel() {{
            setIsExternalAntenna(true);
        }});
        {
            Result result = postSaleService.queryWifiPowerLevel(sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
    }

    @Test
    public void test_updateWifiPowerLevel() {
        String userSn = OpenApiUtil.shortUUID();
        String sn = OpenApiUtil.shortUUID();
        {
            Result result = postSaleService.updateWifiPowerLevel(sn, null, null);
            Assert.assertEquals(new Integer(INVALID_PARAMS.getCode()), result.getResult());
        }
        {
            Result result = postSaleService.updateWifiPowerLevel(sn, null, -1);
            Assert.assertEquals(new Integer(INVALID_PARAMS.getCode()), result.getResult());
        }
        {
            Result result = postSaleService.updateWifiPowerLevel(sn, null, 11);
            Assert.assertEquals(new Integer(INVALID_PARAMS.getCode()), result.getResult());
        }
        {
            Result result = postSaleService.updateWifiPowerLevel(sn, null, 8);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
        {
            Result result = postSaleService.updateWifiPowerLevel(null, null, 8);
            Assert.assertEquals(new Integer(INVALID_PARAMS.getCode()), result.getResult());
        }
        {
            when(deviceManufactureDao.selectByUserSn(userSn)).thenReturn(null);
            Result result = postSaleService.updateWifiPowerLevel(null, userSn, 8);
            Assert.assertEquals(new Integer(INVALID_PARAMS.getCode()), result.getResult());
        }
        when(deviceManufactureDao.selectByUserSn(userSn)).thenReturn(null).thenReturn(new DeviceManufactureTableDO() {{
            setSerialNumber(sn);
            setUserSn(userSn);
        }});
        {
            Result result = postSaleService.updateWifiPowerLevel(sn, null, 8);
            Assert.assertEquals(Result.successFlag, result.getResult());
        }
    }

    @Test
    public void test_updateKxWifiPowerLevel() {
        String json = "{\n" +
                "    \"bxSn\": \"80101db92908202cffc193b69155175a\",\n" +
                "    \"devices\": [\n" +
                "        {\n" +
                "            \"sn\": \"d8162f29f37d0c44188c184fc91096ba\",\n" +
                "            \"wifiPowerLevel\": 7\n" +
                "        },\n" +
                "        {\n" +
                "            \"sn\": \"c3f3b51962e88e970daab72353a5786c\",\n" +
                "            \"wifiPowerLevel\": 8\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        JSONObject input = JSON.parseObject(json);

        when(kxDeviceSettingService.save(any())).thenReturn(1);
        Result result = postSaleService.updateKxWifiPowerLevel(input);
        Assert.assertEquals(Result.successFlag, result.getResult());
    }

    @Test
    public void test_checkInputBxSn() {
        {
            JSONObject input = new JSONObject().fluentPut("bxSn", OpenApiUtil.shortUUID());
            Assert.assertEquals(true, postSaleService.checkInputBxSn(input));
        }
        {
            JSONObject input = new JSONObject();
            Assert.assertEquals(false, postSaleService.checkInputBxSn(input));
        }
        {
            String userSnOrSn = OpenApiUtil.shortUUID();
            JSONObject input = new JSONObject().fluentPut("userSnOrSn", userSnOrSn);
            when(deviceManufactureDao.selectByUserSnOrSn(userSnOrSn)).thenReturn(null);
            Assert.assertEquals(false, postSaleService.checkInputBxSn(input));
        }
        {
            String userSnOrSn = OpenApiUtil.shortUUID();
            JSONObject input = new JSONObject().fluentPut("userSnOrSn", userSnOrSn);
            when(deviceManufactureDao.selectByUserSnOrSn(userSnOrSn)).thenReturn(new DeviceManufactureTableDO() {{
                setSerialNumber(null);
            }});
            Assert.assertEquals(false, postSaleService.checkInputBxSn(input));
        }
        {
            String userSnOrSn = OpenApiUtil.shortUUID();
            JSONObject input = new JSONObject().fluentPut("userSnOrSn", userSnOrSn);
            when(deviceManufactureDao.selectByUserSnOrSn(userSnOrSn)).thenReturn(new DeviceManufactureTableDO() {{
                setSerialNumber(OpenApiUtil.shortUUID());
            }});
            Assert.assertEquals(true, postSaleService.checkInputBxSn(input));
        }
    }

    @Test
    public void test_queryKxWifiPowerLevel() {
        {
            String bxSn = OpenApiUtil.shortUUID();
            when(iotLocalService.getKxDeviceInfo(bxSn)).thenReturn(null);
            when(iotLocalService.getBxRejectCloudRequest(bxSn)).thenReturn(new RejectCloudRequest().setBxSn(bxSn));
            Result result = postSaleService.queryKxWifiPowerLevel(bxSn);
            Assert.assertEquals(new Integer(404), result.getResult());
            Assert.assertEquals(false, ((JSONObject) result.getData()).getBoolean("isEnableCloudRequest"));
        }
        {
            String bxSn = OpenApiUtil.shortUUID();
            when(iotLocalService.getKxDeviceInfo(bxSn)).thenReturn(null);
            when(iotLocalService.getBxRejectCloudRequest(bxSn)).thenReturn(null);
            Result result = postSaleService.queryKxWifiPowerLevel(bxSn);
            Assert.assertEquals(new Integer(404), result.getResult());
            Assert.assertEquals(true, ((JSONObject) result.getData()).getBoolean("isEnableCloudRequest"));
        }
        {
            String bxSn = OpenApiUtil.shortUUID();
            when(iotLocalService.getKxDeviceInfo(bxSn)).thenReturn(new ReportDeviceInfoToCloud().setBxSn(bxSn)
                    .setTimestamp(System.currentTimeMillis() - 10000));
            when(iotLocalService.getBxRejectCloudRequest(bxSn)).thenReturn(new RejectCloudRequest().setBxSn(bxSn)
                    .setTimestamp(System.currentTimeMillis() - 20000));
            Result result = postSaleService.queryKxWifiPowerLevel(bxSn);
            Assert.assertEquals(new Integer(0), result.getResult());
            Assert.assertEquals(true, ((JSONObject) result.getData()).getBoolean("isEnableCloudRequest"));
        }
        {
            String bxSn = OpenApiUtil.shortUUID();
            when(iotLocalService.getKxDeviceInfo(bxSn)).thenReturn(new ReportDeviceInfoToCloud().setBxSn(bxSn)
                    .setTimestamp(System.currentTimeMillis() - 20000));
            when(iotLocalService.getBxRejectCloudRequest(bxSn)).thenReturn(new RejectCloudRequest().setBxSn(bxSn)
                    .setTimestamp(System.currentTimeMillis() - 10000));
            Result result = postSaleService.queryKxWifiPowerLevel(bxSn);
            Assert.assertEquals(new Integer(0), result.getResult());
            Assert.assertEquals(false, ((JSONObject) result.getData()).getBoolean("isEnableCloudRequest"));
        }
        {
            String bxSn = OpenApiUtil.shortUUID();
            when(iotLocalService.getKxDeviceInfo(bxSn)).thenReturn(new ReportDeviceInfoToCloud().setBxSn(bxSn)
                    .setTimestamp(System.currentTimeMillis() - 20000));
            when(iotLocalService.getBxRejectCloudRequest(bxSn)).thenReturn(null);
            Result result = postSaleService.queryKxWifiPowerLevel(bxSn);
            Assert.assertEquals(new Integer(0), result.getResult());
            Assert.assertEquals(true, ((JSONObject) result.getData()).getBoolean("isEnableCloudRequest"));
        }
        {
            String bxSn = OpenApiUtil.shortUUID();
            String cxSn = OpenApiUtil.shortUUID();
            String cxModelNo = OpenApiUtil.shortUUID();
            ReportDeviceInfoToCloud.DeviceInfo cxDevice = new ReportDeviceInfoToCloud.DeviceInfo()
                    .setSn(cxSn).setModelNo(cxModelNo).setBaseStation(0)
                    .setKissDeviceNode(new KissDeviceNode())
                    .setSupport(new ReportDeviceInfoToCloud.DeviceSupport())
                    .setSettings(new ReportDeviceInfoToCloud.DeviceSettings())
                    .setStatus(new ReportDeviceInfoToCloud.DeviceStatus());
            when(iotLocalService.getKxDeviceInfo(bxSn)).thenReturn(new ReportDeviceInfoToCloud().setBxSn(bxSn)
                    .setCloudUserId(4283948)
                    .setTimestamp(System.currentTimeMillis())
                    .setDevices(Arrays.asList(cxDevice))
            );
            when(iotLocalService.getBxRejectCloudRequest(bxSn)).thenReturn(null);

            when(deviceModelConfigService.queryRowDeviceModelByModelNo(cxModelNo)).thenReturn(new DeviceModel() {{

            }});
            when(factoryDataQueryService.queryDeviceManufactureBySn(cxSn)).thenReturn(new DeviceManufactureTableDO() {{

            }});
            when(kxDeviceSettingService.queryBySn(cxSn)).thenReturn(new KxDeviceSettingsDO() {{

            }});
            when(kissDeviceNodeManager.get(bxSn)).thenReturn(new KissDeviceNode() {{

            }});
            Result result;
            try {
                result = postSaleService.queryKxWifiPowerLevel(bxSn);
            } catch (Throwable e) {
                log.error("queryKxWifiPowerLevel", e);
                throw e;
            }
            Assert.assertEquals(new Integer(0), result.getResult());
            Assert.assertEquals(true, ((JSONObject) result.getData()).getBoolean("isEnableCloudRequest"));
        }
    }

}

