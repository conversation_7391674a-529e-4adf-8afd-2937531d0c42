package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.msg.MsgEntityBase;
import com.addx.iotcamera.bean.msg.VideoMsgEntity;
import com.addx.iotcamera.bean.msg.fcm.FcmMsgEntity;
import com.addx.iotcamera.bean.msg.xinge.CustomerContentBase;
import com.addx.iotcamera.publishers.notification.Firebase.FirebaseMessage;
import com.addx.iotcamera.publishers.notification.Firebase.FirebasePublisher;
import com.addx.iotcamera.publishers.notification.PushArgs;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tencent.xinge.XingeApp;
import com.tencent.xinge.bean.*;
import com.tencent.xinge.push.app.PushAppRequest;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppPushTest {

    @InjectMocks
    private FirebasePublisher firebasePublisher;
    @Mock
    private ReportLogService reportLogService;

    public static String sign(String appSecret, String stringToSign) {
        try {
//            String stringToSign = "********************{\"audience_type\": \"account\",\"platform\": \"android\",\"message\": {\"title\": \"test title\",\"content\": \"test content\",\"android\": { \"action\": {\"action_type\": 3,\"intent\": \"xgscheme://com.xg.push/notify_detail?param1=xg\"}}},\"message_type\": \"notify\",\"account_list\": [\"5822f0eee44c3625ef0000bb\"] }";
//            String appSecret = "1452fcebae9f3115ba794fb0fff2fd73";
            Mac mac;
            mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(appSecret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signatureBytes = mac.doFinal(stringToSign.getBytes("UTF-8"));

            String hexStr = Hex.encodeHexString(signatureBytes);
            String signature = Base64.getEncoder().encodeToString(hexStr.getBytes());

            return signature;
        } catch (NoSuchAlgorithmException | InvalidKeyException | UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Test
    public void test_xinge_sign() throws Exception {
        String stringToSign = "********************{\"audience_type\": \"account\",\"platform\": \"android\",\"message\": {\"title\": \"test title\",\"content\": \"test content\",\"android\": { \"action\": {\"action_type\": 3,\"intent\": \"xgscheme://com.xg.push/notify_detail?param1=xg\"}}},\"message_type\": \"notify\",\"account_list\": [\"5822f0eee44c3625ef0000bb\"] }";
        String appSecret = "1452fcebae9f3115ba794fb0fff2fd73";
        String sign = sign(appSecret, stringToSign);
        Assert.assertEquals("Y2QyMDc3NDY4MmJmNzhiZmRiNDNlMTdkMWQ1ZDU2YjNlNWI3ODlhMTY3MGZjMTUyN2VmNTRjNjVkMmQ3Yjc2ZA==", sign);
    }

    private static String longse_push_app(String body) {
        //        通过请求时间戳 + AccessId + 请求 body 进行字符拼接，得到原始的待签名字符串：
//        待签名字符串 = ${TimeStamp} + ${AccessId} + ${请求body}
//        通过 SecretKey 作为密钥，对原始待签名字符串进行签名，生成得到签名：
//        Sign = Base64(HMAC_SHA256(SecretKey, 待签名字符串))
        String accessId = "**********";
        String secretKey = "dc09c723b9e93ea24eeae11fbdc917df";
        /*
        POST /v3/push/app HTTP/1.1
        Host: api.tpns.tencent.com
        Content-Type: application/json
        AccessId: **********
        TimeStamp: **********
        Sign: Y2QyMDc3NDY4MmJmNzhiZmRiNDNlMTdkMWQ1ZDU2YjNlNWI3ODlhMTY3MGZjMTUyN2VmNTRjNjVkMmQ3Yjc2ZA==
        {"audience_type": "account","message": {"title": "test title","content": "test content","android": { "action": {"action_type": 3,"intent": "xgscheme://com.xg.push/notify_detail?param1=xg"}}},"message_type": "notify","account_list": ["5822f0eee44c3625ef0000bb"] }
         */
//        String body = "";
        String timestamp = PhosUtils.getUTCStamp() + "";
        String unsignedStr = timestamp + accessId + body;
        String sign = sign(secretKey, unsignedStr);
        StringBuilder curlCmd = new StringBuilder("curl")
                .append(" -X POST 'https://api.tpns.tencent.com/v3/push/app'\\\n")
                .append(" -H 'Content-Type: application/json'\\\n")
                .append(" -H 'AccessId: ").append(accessId).append("'\\\n")
                .append(" -H 'TimeStamp: ").append(timestamp).append("'\\\n")
                .append(" -H 'Sign: ").append(sign).append("'\\\n")
                .append(" -d '").append(body).append("'");
        return curlCmd.toString();
    }

    private final static String defaultIntent = "xgscheme://{package}/notify_detail?";

    private PushAppRequest buildPushAppRequest(String postfix) {
        Message message = new Message();
        message.setTitle("test_longse_title" + postfix);
        message.setContent("test_longse_body" + postfix);
        MessageAndroid messageAndroid = new MessageAndroid();
        CustomerContentBase customerContentBase = new CustomerContentBase();
        customerContentBase.setType(9999);
        customerContentBase.setTraceId(RandomStringUtils.randomAlphanumeric(16));
        messageAndroid.setCustom_content("test_longse_custom_content" + postfix);
        message.setAndroid(messageAndroid);
        return initPushAppRequest(null, message);
    }

    private PushAppRequest initPushAppRequest(Integer userId, Message message) {
        PushAppRequest pushAppRequest = new PushAppRequest();
        pushAppRequest.setAudience_type(AudienceType.account);
        ArrayList<String> tokenList = new ArrayList();
//        PushInfo pushInfo = pushInfoService.getPushInfo(userId);
//        tokenList.add(pushInfo.getMsgToken());
        tokenList.add("ali-cf10ac5ab0d91c4cbbcad0f7f021a19a");
        pushAppRequest.setAccount_list(tokenList);

        pushAppRequest.setMessage_type(MessageType.notify);
        pushAppRequest.setMulti_pkg(true);


        message.getAndroid().setShowType(1);
        ClickAction clickAction = new ClickAction();
        clickAction.setAction_type(3);
        StringBuilder intentSb = new StringBuilder();
        intentSb.append(defaultIntent.replace("{package}", "com.gzch.lsapp.bitdogbro"));
        intentSb.append("customerContent").append("=").append(message.getAndroid().getCustom_content());
        clickAction.setIntent(intentSb.toString());
        message.getAndroid().setAction(clickAction);
        message.getAndroid().setCustom_content("");

        // 国外节点控制消息大小
        //message.getAndroid().setIcon_res(servernode.equals(ServerConstants.SERVER_NODE_CN) ? message.getAndroid().getIcon_res() : null);
        //message.getAndroid().setIcon_type(servernode.equals(ServerConstants.SERVER_NODE_CN) ? 1 : 0);
        message.getAndroid().setIcon_type(1);
        message.getAndroid().setIcon_res(message.getAndroid().getIcon_res());
        // 角标，只华为有效
        message.getAndroid().setBadgeType(-2);
        pushAppRequest.setMessage(message);
        return pushAppRequest;
    }

    @Test
    public void test_longse_push_app() throws Exception {

        String curlCmd = longse_push_app(JSONObject.toJSONString(buildPushAppRequest("-1")));

//        String curlCmd = longse_push_app(new JSONObject()
//                .fluentPut("audience_type", "token")
//                .fluentPut("message_type", "notify")
//                .fluentPut("environment", "product")
//                .fluentPut("upload_id", "")
//                .fluentPut("message", new JSONObject()
//                ).toJSONString());

        System.out.println(curlCmd);
//        Process process = Runtime.getRuntime().exec(curlCmd);
//        System.out.println(IOUtils.toString(process.getInputStream()));
    }

    //    @Test
    public void test_longse_sdk() {
        String accessId = "**********";
        String secretKey = "dc09c723b9e93ea24eeae11fbdc917df";

        XingeApp xingeApp = new XingeApp.Builder()
                .appId(accessId)
                .secretKey(secretKey)
                .connectTimeOut(10)
                .readTimeOut(10)
                .domainUrl("https://api.tpns.tencent.com")
                .build();
        org.json.JSONObject result = xingeApp.pushApp(buildPushAppRequest("--33"));
        System.out.println(result.toString());
    }

    // fcm longse
    private String androidKey = "AAAAMW3kInU:APA91bFtWkXK4xOVo1ZZScDsfSZeQ7QqGPmX7s49Tb1HvtOA_OSUxzkecw6ABnO7gXZ1zvuq9BGqHHPENovQSt0Jg-9reRdGKpyAAz9R98gATvUenm91ivi9gleAW3KeHY2mBtPTahY0";

//    @Test
    public void test_longse_fcm() {
        doNothing().doAnswer(invocation -> {
            System.out.println("reportPushMessageLog:" + Arrays.toString(invocation.getArguments()));
            return null;
        }).when(reportLogService).reportPushMessageLog(any(), any(), anyString(), any(), anyString(), anyString());

        JSONObject obj = JSON.parseObject("{\n" +
                "            \"app\": {\n" +
                "                \"appName\": \"桔子柚子\",\n" +
                "                \"appType\": \"Android\",\n" +
                "                \"bundle\": \"com.gzch.lsapp.bitdogbro\",\n" +
                "                \"channelId\": 0,\n" +
                "                \"tenantId\": \"longse\",\n" +
                "                \"version\": 16,\n" +
                "                \"versionName\": \"21.1.33\"\n" +
                "            },\n" +
                "            \"countryNo\": \"CN\",\n" +
                "            \"language\": \"zh\",\n" +
                "            \"msgToken\": \"eOT4ERWBSoGg4G4MTHSCK8:APA91bHOXTR9iGesjOuXswj38TdBULTmmxVpV7XUPf5r3iuQooNYAndYBScp-Teug4cl4LeaV8Buv35joqEKvbyNUG-0ug9vdDSr1HbSGq6qGX9h3oVPOzYfzxQBnSaQNIIuFxdQZetY\",\n" +
                "            \"msgType\": \"101\"\n" +
                "        }");

        PushArgs pushArgs = PushArgs.builder()
                .msgType(obj.getInteger("msgType"))
                .msgToken(obj.getString("msgToken"))
                .bundleName(obj.getJSONObject("app").getString("bundle"))
                .userId(967)
                .serialNumber("b533db20f9e1a8e00fccfbea80c62d83")
                .build();
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setDeviceName("deviceName");
        deviceDO.setSerialNumber(pushArgs.getSerialNumber());
        VideoMsgEntity message = buildPirVideoMessage(0, deviceDO, "traceId1234", "http://imageUrl", null);
        SendFCM(message, pushArgs);
    }

    // com.addx.iotcamera.helper.MsgHelper#SendFCM
    private void SendFCM(MsgEntityBase msg, PushArgs pushArgs) {
        FirebaseMessage fbm = new FirebaseMessage();
        fbm.setTo(pushArgs.getMsgToken());

        FcmMsgEntity msgEntity = msg.defaultFcmEntity();
        msgEntity.setTitle(msg.getTitle());
        msgEntity.setBody(msg.getBody());
        msgEntity.ParseFrom(msg);
        msgEntity.setTimestamp(msg.getTime());
        msgEntity.setSerialNumber(pushArgs.getSerialNumber());
        msgEntity.setVideoEvent(msg.getVideoEvent());
        fbm.setData(msgEntity);
//        String bundId = getFcmBundId(pushArgs.getBundleName());

        fbm.setCollapse_key(msg.getVideoEvent());
//        String androidKey = config.getAndroidConfig().get(bundId);
        firebasePublisher.publish(fbm, androidKey, pushArgs);
    }

    public VideoMsgEntity buildPirVideoMessage(int userId, DeviceDO device, String traceId, String imageUrl, String eventObject) {
        VideoMsgEntity entity = new VideoMsgEntity();
//        User user = userService.queryUserById(userId);
//        String body = this.getUserNotifyMessage(user, CopyWriteConstans.commonMotionBody);
        String body = "mock_pir_video_longse";

        // 这两个本来就没值，预留字段?
        entity.getLocation().setLocationId(0);
        entity.getLocation().setLocationName("");

        entity.getDevice().setDeviceName(device.getDeviceName());
        entity.getDevice().setSerialNumber(device.getSerialNumber());
        entity.setTraceId(traceId);
        entity.setTitle(device.getDeviceName());
        entity.setBody(body);
        entity.setThumbnailUrl(imageUrl);
        return entity;
    }

}
