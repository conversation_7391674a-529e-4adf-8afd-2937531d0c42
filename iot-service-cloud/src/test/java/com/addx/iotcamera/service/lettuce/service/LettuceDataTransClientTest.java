package com.addx.iotcamera.service.lettuce.service;

import com.alibaba.fastjson.JSON;
import io.lettuce.core.*;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LettuceDataTransClientTest {

    @InjectMocks
    LettuceDataTransClient lettuceDataTransClient;
    @Mock
    RedisAdvancedClusterCommands<String, String> businessRedisClusterSyncClient;
    @Mock
    List<ScoredValue<String>> scoredValueList;
    @Mock
    ScoredValue<String> stringScoredValue;
    @Mock
    KeyValue<String, String> keyValue;
    @Mock
    ValueScanCursor<String> valueScanCursor;
    @Mock
    List<KeyValue<String, String>> keyValues;
    @Mock
    KeyScanCursor<String> cursor;
    @Mock
    ScanArgs scanArgs;

    String key = "a";

    String field = "a";

    String value = "a";

    int timeout = 1;

    List<String> valueList = Collections.singletonList(value);
    List<String> fields = Collections.singletonList(field);
    String[] keysArray = new String[]{key};
    String[] filesArray = new String[]{field};
    String[] valuesArray = new String[]{value};

    Map<Object, Object> map = new HashMap<>();
    Map<String, Object> map2 = new HashMap<>();
    Map<String, String> map3 = new HashMap<>();

    @Before
    public void before() {
        String[] sliceJsonArray = valueList.stream().map(JSON::toJSONString).toArray(String[]::new);
        when(businessRedisClusterSyncClient.hget(key, field)).thenReturn(value);
        when(businessRedisClusterSyncClient.hgetall(key)).thenReturn(map3);
        when(businessRedisClusterSyncClient.hincrby(key, field, 1)).thenReturn(1L);
        when(businessRedisClusterSyncClient.hincrbyfloat(key, field, 1d)).thenReturn(1d);
        when(businessRedisClusterSyncClient.zrangebyscoreWithScores(key, 1, 1, 1, 1)).thenReturn(scoredValueList);
        when(businessRedisClusterSyncClient.hdel(key, filesArray)).thenReturn(1L);
        when(businessRedisClusterSyncClient.hexists(key, field)).thenReturn(true);
        when(businessRedisClusterSyncClient.incrby(key, 1)).thenReturn(1L);
        when(businessRedisClusterSyncClient.hlen(key)).thenReturn(1L);
        when(businessRedisClusterSyncClient.zrem(key, value)).thenReturn(1L);
        when(businessRedisClusterSyncClient.zremrangebyscore(key, 1, 1)).thenReturn(1L);
        when(businessRedisClusterSyncClient.zcard(key)).thenReturn(1L);
        when(businessRedisClusterSyncClient.zscore(key, value)).thenReturn(1d);
        when(businessRedisClusterSyncClient.sscan(key, ScanArgs.Builder.limit(1).match(""))).thenReturn(valueScanCursor);
        when(businessRedisClusterSyncClient.smembers(key)).thenReturn(new HashSet<>());
        when(businessRedisClusterSyncClient.setnx(key, value)).thenReturn(true);
        when(businessRedisClusterSyncClient.getset(key, value)).thenReturn(value);
        when(businessRedisClusterSyncClient.mget(keysArray)).thenReturn(new ArrayList<>());
        when(businessRedisClusterSyncClient.lrange(key, 0, -1)).thenReturn(new ArrayList<>());
        when(businessRedisClusterSyncClient.del(keysArray)).thenReturn(1L);
        when(businessRedisClusterSyncClient.sadd(key, valuesArray)).thenReturn(1L);
        when(businessRedisClusterSyncClient.ttl(key)).thenReturn(1L);
        when(businessRedisClusterSyncClient.rpush(key, sliceJsonArray)).thenReturn(1L);
        when(businessRedisClusterSyncClient.hmget(key, filesArray)).thenReturn(keyValues);
        when(businessRedisClusterSyncClient.scan(ScanArgs.Builder.matches(key + "::*").limit(100))).thenReturn(cursor);
        when(businessRedisClusterSyncClient.scan(cursor)).thenReturn(cursor);
        when(lettuceDataTransClient.scanSetInner(key, key + "::*", 100)).thenReturn(valueScanCursor);
    }


    @Test
    public void set() {
        lettuceDataTransClient.set(key, value);
        lettuceDataTransClient.set(key, value, 1);
    }

    @Test
    public void testSet() {
        lettuceDataTransClient.set(key, value, timeout);
    }

    @Test
    public void saveList() {
        lettuceDataTransClient.saveList(key, value, timeout);
    }

    @Test
    public void setHashFieldValue() {
        lettuceDataTransClient.setHashFieldValue(key, field, value);
    }

    @Test
    public void setHashFieldValueMap() {
        lettuceDataTransClient.setHashFieldValueMap(key, map);
        lettuceDataTransClient.setHashFieldValueMap(key, map, 1L);
    }

    @Test
    public void getHashFieldValue() {
        lettuceDataTransClient.getHashFieldValue(key, field);
    }

    @Test
    public void getHashEntries() {
        lettuceDataTransClient.getHashEntries(key);
    }

    @Test
    public void incrHashFieldValue() {
        lettuceDataTransClient.incrHashFieldValue(key, field, 1d);
        lettuceDataTransClient.incrHashFieldValue(key, field, 1L);
    }

    @Test
    public void deleteHashField() {
        lettuceDataTransClient.deleteHashField(key, field);
    }

    @Test
    public void deleteHashFields() {
        lettuceDataTransClient.deleteHashFields(key, Collections.singletonList(field));
        lettuceDataTransClient.deleteHashFields(key, Collections.emptyList());
    }

    @Test
    public void hashIncrementInt() {
        lettuceDataTransClient.hashIncrementInt(key, field, 1);
        lettuceDataTransClient.hashIncrementInt(key, field, 1, timeout);
    }

    @Test
    public void hashIncrementBigDecimal() {
        lettuceDataTransClient.hashIncrementBigDecimal(key, field, new BigDecimal(1));
    }

    @Test
    public void hasFieldKey() {
        lettuceDataTransClient.hasFieldKey(key, field);
    }

    @Test
    public void delete() {
        lettuceDataTransClient.delete(key);
    }

    @Test
    public void deleteListValue() {
        lettuceDataTransClient.deleteListValue(key, value);
    }

    @Test
    public void setExpired() {
        lettuceDataTransClient.setExpired(key, timeout);
    }

    @Test
    public void incrNum() {
        lettuceDataTransClient.incrNum(key);
    }

    @Test
    public void incrBy() {
        lettuceDataTransClient.incrBy(key, 1);
    }

    @Test
    public void hashGetAll() {
        lettuceDataTransClient.hashGetAll(key);
    }

    @Test
    public void hashEntries() {
        lettuceDataTransClient.hashEntries(key);
    }

    @Test
    public void hashPut() {
        lettuceDataTransClient.hashPut(key, field, value);
    }

    @Test
    public void hashPutAll() {
        lettuceDataTransClient.hashPutAll(key, map2);

        lettuceDataTransClient.hashPutAll(key, new HashMap<>());

        HashMap<String, Object> map3 = new HashMap<>();
        map3.put("a", "a");
        lettuceDataTransClient.hashPutAll(key, map3);
    }

    @Test
    public void hashMembersCount() {
        lettuceDataTransClient.hashMembersCount(key);
    }

    @Test
    public void zAdd() {
        lettuceDataTransClient.zAdd(key, value, 1);
    }

    @Test
    public void zRemove() {
        lettuceDataTransClient.zRemove(key, value);
        lettuceDataTransClient.zRemove(key, new String[]{value});
    }

    @Test
    public void zRangeByScore() {
        lettuceDataTransClient.zRangeByScore(key, 1, 1, 1, 1);
    }

    @Test
    public void zRemoveByScore() {
        lettuceDataTransClient.zRemoveByScore(key, 1, 1);
    }

    @Test
    public void zCard() {
        lettuceDataTransClient.zCard(key);
    }

    @Test
    public void zScore() {
        lettuceDataTransClient.zScore(key, 1);
    }

    @Test
    public void addSetValue() {
        lettuceDataTransClient.addSetValue(key, new String[]{"1"});
    }

    @Test
    public void remove() {
        lettuceDataTransClient.remove(key, Collections.singletonList("1"));
    }

    @Test
    public void scanSet() {
        // lettuceDataTransClient.scanSet(key, "", 1);
        lettuceDataTransClient.scan(key, 1);
    }

    @Test
    public void getSetValue() {
        lettuceDataTransClient.getSetValue(key);
    }

    @Test
    public void setIfAbsent() {
        lettuceDataTransClient.setIfAbsent(key, value, timeout);
    }

    @Test
    public void getAndSet() {
        lettuceDataTransClient.getAndSet(key, value);
    }

    @Test
    public void multiGet() {
        lettuceDataTransClient.multiGet(Collections.singletonList(key));
    }

    @Test
    public void rangeList() {
        lettuceDataTransClient.rangeList(key);
    }

    @Test
    public void setMembers() {
        lettuceDataTransClient.setMembers(key);
    }

    @Test
    public void setAdd() {
        lettuceDataTransClient.setAdd(key, Collections.singletonList(value));
    }

    @Test
    public void ttl() {
        lettuceDataTransClient.ttl(key);
    }

    @Test
    public void listRightPushAll() {
        lettuceDataTransClient.listRightPushAll(key, Collections.singletonList(value), timeout);
    }

    @Test
    public void hashMultiGet() {
        lettuceDataTransClient.hashMultiGet(key, null);
        lettuceDataTransClient.hashMultiGet(key, Collections.singletonList(field));
    }

    @Test
    public void clearRedisCache() {
        // when(ScanArgs.Builder.matches(key).limit(100)).thenReturn(scanArgs);
        // when(businessRedisClusterSyncClient.scan(scanArgs)).thenReturn(cursor);
        // lettuceDataTransClient.clearRedisCache(Collections.singletonList(key));
    }

    @Test
    public void getScoredValue() {
        lettuceDataTransClient.getScoredValue(null);
        lettuceDataTransClient.getScoredValue(stringScoredValue);
    }

    @Test
    public void getKVValue() {
        lettuceDataTransClient.getKVValue(null);
        lettuceDataTransClient.getKVValue(keyValue);
    }

}