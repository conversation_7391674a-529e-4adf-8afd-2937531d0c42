package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.LoginRequest;
import com.addx.iotcamera.bean.db.ApkInfoDO;
import com.addx.iotcamera.bean.db.app.IosVersionReleaseDO;
import com.addx.iotcamera.bean.response.ServerNode;
import com.addx.iotcamera.bean.response.ZendeskItem;
import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.config.CountlyConfig;
import com.addx.iotcamera.config.EuCountryConfig;
import com.addx.iotcamera.config.apollo.CountryNodeConfig;
import com.addx.iotcamera.config.apollo.CountryNodeV1Config;
import com.addx.iotcamera.config.app.*;
import com.addx.iotcamera.controller.manage.AppManagementController;
import com.addx.iotcamera.dao.AppInfoDAO;
import com.addx.iotcamera.dao.user.IEuUserDao;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.PropertySource;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;

import static org.addx.iot.common.constant.AppConstants.ZENDESK_NODE_EU;
import static org.addx.iot.common.constant.AppConstants.ZENDESK_NODE_US;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

//@RunWith(PowerMockRunner.class)
@RunWith(MockitoJUnitRunner.Silent.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@EnableConfigurationProperties({AndroidReleaseConfig.class, AppCopyrightConfig.class, AppMenuConfig.class})
@PropertySource(value = {
        "classpath:/gitconfig/app-release/android/androidrelease.yml",
        "classpath:/app/app_copyright.yml",
        "classpath:/app/app_menu.yml",
        
},
        encoding = "utf-8", factory = MixPropertySourceFactory.class)
@Slf4j
public class AppInfoServiceTest {

    @Spy
    EuCountryConfig euCountryConfig;

    @Mock
    CountryNodeConfig countryNodeConfig;

    @InjectMocks
    private AppInfoService appInfoService;
    @Mock
    private TenantSettingService tenantSettingService;
    @Mock
    private AppInfoDAO appInfoDAO;

    @Mock
    CountlyConfig countlyConfig;


    @Autowired
    @Spy
    private AppMenuConfig appMenuConfig;

    @Mock
    private IEuUserDao iEuUserDao;

    @Mock
    private CountryNodeV1Config countryNodeV1Config;
    @Mock
    private ZendeskConfig zendeskConfig;

    @Mock
    private TenantServerNodeConfig tenantServerNodeConfig;

    @Before
    public void before() throws Exception {
        TestHelper.injectSpyPropertySource(this);
    }

    @Test
    public void testConfig() {
        // LocalDate currentDate = LocalDateTime.ofEpochSecond(Instant.now().getEpochSecond(), 0, ZoneOffset.ofHours(8))

        log.info(Instant.now().atZone(ZoneId.systemDefault()).getDayOfWeek().toString());
        when(tenantSettingService.getTenantSettingByTenantId(anyString())).thenAnswer(it -> new TenantSetting().setTenantId(it.getArgument(0)));
    }

//    @Test
//    public void getLatestApk() {
//        ApkInfoDO apkInfoDO = new ApkInfoDO();
//
//        when(appInfoDAO.selectLatestApk(any())).thenReturn(apkInfoDO);
//        when(appInfoDAO.getApkDescriptions(any(), any())).thenReturn(null);
//
//        AppRequestBase requestBase = new AppRequestBase();
//        requestBase.setLanguage("zh");
//        requestBase.getApp().setBundle("com.smartaddx.vicohome");
//        ReflectionTestUtils.setField(appInfoService, "reeleaseEvn", "release");
//        assertEquals(ApkInfoDO.class, appInfoService.getLatestApk(requestBase).getClass());
//    }

    //@Test
//    public void getLatestIosVersion() {
//        IosVersionDO iosVersionDO = new IosVersionDO();
//
//        when(appInfoDAO.selectLatestIosVersion(any())).thenReturn(iosVersionDO);
//        when(appInfoDAO.getIosVersionDescriptions(any(), any())).thenReturn(null);
//        when(appUpgradeConfig.getConfig()).thenReturn(new HashMap<>());
//        AppRequestBase requestBase = new AppRequestBase();
//        requestBase.setLanguage("zh");
//        ReflectionTestUtils.setField(appInfoService, "reeleaseEvn", "release");
//        assertEquals(IosVersionDO.class, appInfoService.getLatestIosVersion(requestBase).getClass());
//    }

    @Test
    public void setLatestApkFailure() {
        ApkInfoDO apkInfoDO = new ApkInfoDO();

        Result expectedResult = ResultCollection.INVALID_PARAMS.getResult();

        Result actualResult = appInfoService.setLatestApk(apkInfoDO);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void setLatestApkSuccess() {
        List<String> releaseNote = new ArrayList<>();
        releaseNote.add("1");
        releaseNote.add("2");
        releaseNote.add("3");
        ApkInfoDO apkInfoDO = ApkInfoDO.builder()
                .apkId(1)
                .apkName("testApkName")
                .apkUrl("testApkName")
                .minSupport(0)
                .minFirmware("testFirmwareId")
                .releaseNote(releaseNote)
                .build();

        when(appInfoDAO.insertApk(any())).thenReturn(1);
        when(appInfoDAO.insertApkDescription(any(), any())).thenReturn(1);

        Result expectedResult = Result.Success();

        Result actualResult = appInfoService.setLatestApk(apkInfoDO);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void setLatestIosVersionReleaseNoteEmpty() {
        IosVersionReleaseDO iosVersionDO = new IosVersionReleaseDO();

        when(appInfoDAO.selectLatestIosVersion(any(),any())).thenReturn(null);
        when(appInfoDAO.insertIosVersion(any())).thenReturn(1);

        when(appInfoDAO.insertIosVersionDescription(any(),any(),any())).thenReturn(1);

        Result expectedResult = Result.Success();

        Result actualResult = appInfoService.setLatestIosVersion(iosVersionDO);

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void setLatestIosVersionReleaseNote() {
        IosVersionReleaseDO iosVersionDO = new IosVersionReleaseDO();

        when(appInfoDAO.selectLatestIosVersion(any(),any())).thenReturn(null);
        when(appInfoDAO.insertIosVersion(any())).thenReturn(1);

        when(appInfoDAO.insertIosVersionDescription(any(),any(),any())).thenReturn(1);

        Map<String,List<String>> releaseNote = Maps.newHashMap();
        releaseNote.put("zh", Arrays.asList("test1","test2"));
        iosVersionDO.setReleaseNote(releaseNote);

        Result expectedResult = Result.Success();

        Result actualResult = appInfoService.setLatestIosVersion(iosVersionDO);

        assertEquals(expectedResult, actualResult);
    }

//    @Test
//    public void setLatestIosVersionSuccess() {
//        List<String> releaseNote = new ArrayList<>();
//        releaseNote.add("1");
//        releaseNote.add("2");
//        releaseNote.add("3");
//        IosVersionDO iosVersionDO = IosVersionDO.builder()
//                .version(1)
//                .versionName("testIosName")
//                .minSupport(0)
//                .minFirmware("testFirmwareId")
//                .releaseNote(releaseNote)
//                .downloadUrl("downloadUrl")
//                .tenantId("vicoo")
//                .build();
//
//        when(appInfoDAO.insertIosVersion(any())).thenReturn(1);
//        when(appInfoDAO.insertIosVersionDescription(any(), any())).thenReturn(1);
//
//        Result expectedResult = Result.Success();
//
//        Result actualResult = appInfoService.setLatestIosVersion(iosVersionDO);
//
//        assertEquals(expectedResult, actualResult);
//    }

    @Test
    public void verifyDzeesMenu() {
        String menuUrl = "https://www.dzees.net";
        String language = "en";
        String tenantId = "dzees";
        String url = appMenuConfig.getConfig().get(tenantId).get(language).get(0);
        assertEquals(menuUrl, url);
    }

//    @Test
//    public void testGetOrDownloadAppFile() throws Exception {
//        String tenantId = "test_tenantId";
//        String templateCode = "test_templateCode";
//        String templateLang = "en";
//
//        Assert.assertThrows(Exception.class, () -> {
//            appInfoService.getOrDownloadAppFile("key", tenantId, templateCode, templateLang, "index.html", true, false);
//        });
//
//        AmazonS3 amazonS3 = Mockito.mock(AmazonS3.class);
//        Field appFileS3Field = appInfoService.getClass().getDeclaredField("appFileS3");
//        appFileS3Field.setAccessible(true);
//        appFileS3Field.set(appInfoService, amazonS3);
//
//        when(amazonS3.listObjectsV2(any(), any())).thenReturn(new ListObjectsV2Result() {{
//            setKeyCount(0);
//        }});
//
//        ResponseEntity responseEntity = appInfoService.getOrDownloadAppFile("key", tenantId, templateCode, templateLang, "index.html", true, false);
//        Assert.assertTrue(responseEntity.getStatusCode() == HttpStatus.NOT_FOUND);
//
//        when(amazonS3.listObjectsV2(any(), any())).thenReturn(new ListObjectsV2Result() {
//            @Override
//            public List<S3ObjectSummary> getObjectSummaries() {
//                return Collections.singletonList(new S3ObjectSummary() {{
//                    setKey("test_key");
//                }});
//            }
//            {
//            setKeyCount(1);
//            }
//        });
//
//        when(amazonS3.generatePresignedUrl(any(), any(), any(), any())).thenReturn(new URL("https://any/index.html"));
//
//        responseEntity = appInfoService.getOrDownloadAppFile("key", tenantId, templateCode, templateLang, "index.html", true, false);
//        Assert.assertTrue(responseEntity.getStatusCode() == HttpStatus.OK);
//
//        when(amazonS3.getObject(any())).thenReturn(new S3Object(){{
//            setObjectContent(new ByteArrayInputStream("asdsadasdasd".getBytes()));
//        }});
//        responseEntity = appInfoService.getOrDownloadAppFile("key2", tenantId, templateCode, templateLang, "index.html", true, false);
//        Assert.assertTrue(responseEntity.getStatusCode() == HttpStatus.OK);
//
//        responseEntity = appInfoService.getOrDownloadAppFile("key", tenantId, templateCode, templateLang, "index.html", false, false);
//        Assert.assertTrue(responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getHeaders().get(HttpHeaders.CONTENT_TYPE).contains(MediaType.TEXT_HTML.toString()));
//
//        responseEntity = appInfoService.getOrDownloadAppFile("key", tenantId, templateCode, templateLang, "index.html", false, true);
//        Assert.assertTrue(responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getHeaders().containsKey(HttpHeaders.CONTENT_DISPOSITION));
//    }

    @Test
    public void testGetRequestDomain() {
        AppManagementController appManagementController = new AppManagementController();
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        when(request.getHeader(any())).thenReturn("api.any.live");
        String requestDomain = appManagementController.getRequestHostFromRequest(request);
        Assert.assertEquals(requestDomain, "any.live");

        when(request.getHeader(any())).thenReturn("api-cn.any.live");
        requestDomain = appManagementController.getRequestHostFromRequest(request);
        Assert.assertEquals(requestDomain, "any.live");

        when(request.getHeader(any())).thenReturn("any.live");
        requestDomain = appManagementController.getRequestHostFromRequest(request);
        Assert.assertEquals(requestDomain, "any.live");
    }

    @Test
    public void testQueryNodeV1() throws NoSuchFieldException, IllegalAccessException {
        LoginRequest request = new LoginRequest();
        AppInfo app = new AppInfo();
        app.setTenantId("vicoo");
        app.setAppType("iOS");
        app.setApiVersion("1024");
        request.setCountryNo("BR");
        request.setApp(app);
        request.setUserName("userName");

        String iosUrl = "https://api-staging-us.vicoo.tech";
        String euUrl = "https://api-staging-eu.vicoo.tech";
        String androidUrl = "https://api-staging-us.vicohome.io";

        Map<String, String> defaultNode = new HashMap<>();
        defaultNode.put("vicoo-iOS", iosUrl);

        initContryNodeV1Config(iosUrl, androidUrl, defaultNode);

        when(zendeskConfig.queryZendeskItem(any(),any())).thenReturn(new ZendeskItem());

        Map<String, Map<String, String>> config2 = getConfig(iosUrl,euUrl);
        when(countryNodeConfig.getDefaultNode()).thenReturn(defaultNode);
        when(countryNodeConfig.getConfig()).thenReturn(config2);
        ServerNode result;
        {
            //判断欧洲用户在美国节点

            Map<String, String> euMap = new HashMap<>();
            euMap.put("BR","BR");
            when(euCountryConfig.getConfig()).thenReturn(euMap);
            when(iEuUserDao.getEuUser(any())).thenReturn("test");

            result = appInfoService.queryNodeV1(request);
            Assert.assertEquals(result.getNodeUrl(), iosUrl);
        }

        // 安卓v0
        app.setAppType("Android");
        app.setApiVersion("v0");
        request.setCountryNo("US");
        defaultNode.put("vicoo",androidUrl);
        result = appInfoService.queryNodeV1(request);
        Assert.assertEquals(result.getNodeUrl(), androidUrl);

        when(euCountryConfig.getConfig()).thenReturn(Maps.newHashMap());
        {
            request.getApp().setTenantId("test");
            result = appInfoService.queryNodeV1(request);
            Assert.assertNull(result.getNodeUrl());
        }


        request.getApp().setTenantId("vicoo");
        {
            when(tenantServerNodeConfig.queryTenantServerNode(any(),any())).thenReturn(ZENDESK_NODE_EU);
            result = appInfoService.queryNodeV1(request);
            Assert.assertEquals(result.getNodeUrl(),euUrl);
        }

        when(tenantServerNodeConfig.queryTenantServerNode(any(),any())).thenReturn(ZENDESK_NODE_US);
        when(countryNodeV1Config.getAppTypeNodeUrlConfig()).thenReturn(null);
        result = appInfoService.queryNodeV1(request);
        Assert.assertEquals(result.getNodeUrl(), iosUrl);

        when(countryNodeV1Config.getAppTypeNodeUrlConfig()).thenReturn(new HashMap<>());
        result = appInfoService.queryNodeV1(request);
        Assert.assertEquals(result.getNodeUrl(), iosUrl);

        when(countryNodeV1Config.getAppTypeNodeUrlConfig()).thenReturn(null);
        app.setAppType("iOS");
        app.setApiVersion("1024");
        request.setCountryNo("BR");
        appInfoService.queryNodeV1(request);
        Assert.assertEquals(result.getNodeUrl(), iosUrl);
    }

    private void initContryNodeV1Config(String iosUrl, String androidUrl, Map<String, String> defaultNode) throws NoSuchFieldException, IllegalAccessException {
        countryNodeV1Config = Mockito.mock(CountryNodeV1Config.class);
        Field appFileS3Field = appInfoService.getClass().getDeclaredField("countryNodeV1Config");
        appFileS3Field.setAccessible(true);
        appFileS3Field.set(appInfoService, countryNodeV1Config);


        when(countryNodeV1Config.getDefaultNode()).thenReturn(defaultNode);

        Map<String, Map<String, Map<String, String>>> appTypeConfig = getAppTypeConfig(iosUrl, androidUrl);
        when(countryNodeV1Config.getAppTypeNodeUrlConfig()).thenReturn(appTypeConfig);

        Map<String, Map<String, String>> config = getConfig(iosUrl);

        when(countryNodeV1Config.getConfig()).thenReturn(config);
    }

    @NotNull
    private static Map<String, ZendeskItem> getItem() {
        Map<String, ZendeskItem> item = new HashMap<>();
        ZendeskItem zendeskItem = new ZendeskItem();
        item.put("vicoo",zendeskItem);
        return item;
    }

    @NotNull
    private static Map<String, Map<String, String>> getConfig(String iosUrl) {
        Map<String, Map<String, String>> config = new HashMap<>();
        Map<String, String> cMap = new HashMap<>();
        cMap.put("US", iosUrl);
        config.put("vicoo", cMap);
        return config;
    }

    @NotNull
    private static Map<String, Map<String, String>> getConfig(String iosUrl,String euUrl) {
        Map<String, Map<String, String>> config = new HashMap<>();
        Map<String, String> cMap = new HashMap<>();
        cMap.put("US", iosUrl);
        cMap.put("EU", euUrl);
        config.put("vicoo", cMap);
        return config;
    }

    @NotNull
    private static Map<String, Map<String, Map<String, String>>> getAppTypeConfig(String iosUrl, String androidUrl) {
        Map<String, Map<String, Map<String, String>>> appTypeConfig = new HashMap<>();
        Map<String, String> osMap = new HashMap<>();
        osMap.put("iOS", iosUrl);
        osMap.put("Android", androidUrl);
        Map<String, Map<String, String>> countryMap = new HashMap<>();
        countryMap.put("US", osMap);
        appTypeConfig.put("vicoo", countryMap);
        return appTypeConfig;
    }

    @Test
    public void test_buildServerNode(){
        AppInfo appInfo = new AppInfo();
        appInfo.setTenantId("vicoo");
        {
            when(zendeskConfig.queryZendeskItem(any(),any())).thenReturn(null);
            Result excepteResult = Result.Failure("zendeskItem配置缺失");
            Result actualResult = appInfoService.buildServerNode(appInfo,ZENDESK_NODE_US,"");
            Assert.assertEquals(excepteResult,actualResult);
        }
        {
            ZendeskItem zendeskItem = new ZendeskItem();
            zendeskItem.setAppId("appid");
            when(zendeskConfig.queryZendeskItem(any(),any())).thenReturn(zendeskItem);

            TenantSetting tenantSetting = new TenantSetting();
            when(tenantSettingService.getTenantSettingByTenantId(any())).thenReturn(tenantSetting);
            when(countlyConfig.getParamsByAppInfo(appInfo)).thenReturn(Maps.newHashMap());
            appInfoService.buildServerNode(appInfo,ZENDESK_NODE_US,"");
        }
    }
}
