package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.FirmwareBuildInfoTableDO;
import com.addx.iotcamera.bean.db.FirmwareTableDO;
import com.addx.iotcamera.bean.db.device.FirmwareSubVersionTableDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.DeviceOTADO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.manage.*;
import com.addx.iotcamera.bean.response.device.ModelFirmwareUpgradeCount;
import com.addx.iotcamera.config.OtaConfig;
import com.addx.iotcamera.config.OtaStartConfig;
import com.addx.iotcamera.config.device.DeviceOTADebugConfig;
import com.addx.iotcamera.config.firmware.FirmwareBuildGroupConfig;
import com.addx.iotcamera.dao.IFirmwareBuildDAO;
import com.addx.iotcamera.dao.IFirmwareDAO;
import com.addx.iotcamera.dao.IFirmwareSubVersionDAO;
import com.addx.iotcamera.dao.device.IDeviceManualDAO;
import com.addx.iotcamera.enums.FirmwareBuildEnums;
import com.addx.iotcamera.helper.DeviceOperationHelper;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.service.device.DeviceDormancyPlanService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.FirmwareSubVersionService;
import com.addx.iotcamera.service.device.model.DeviceModelTenantService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.firmware.FirmwareCacheService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.firmware.KernelFirmwareService;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.PageResult;
import org.addx.iot.common.vo.Result;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.*;

import static com.addx.iotcamera.constants.FirmwareConstants.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class KernelFirmwareServiceTest {
    @InjectMocks
    private KernelFirmwareService kernelFirmwareService;
    @Mock
    private FirmwareService firmwareServiceMock;

    @InjectMocks
    private FirmwareService firmwareService;
    @Mock
    private IFirmwareDAO firmwareDAO;

    @Mock
    private IFirmwareBuildDAO iFirmwareBuildDAO;
    @Mock
    private DeviceService deviceService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private FirmwareCacheService firmwareCacheService;
    @Mock
    private DeviceOTADebugConfig deviceOTADebugConfig;
    @Mock
    private DeviceDormancyPlanService deviceDormancyPlanService;
    @Mock
    private OtaConfig otaConfig;
    @Mock
    private OtaStartConfig otaStartConfig;
    @Mock
    private RedisService redisService;
    @Mock
    private MqttSender mqttSender;
    @Mock
    private DeviceOperationHelper deviceOperationHelper;
    @Mock
    private ReportLogService reportLogService;
    @Mock
    private CloudfrontService cloudfrontService;
    @Mock
    private FirmwareSubVersionService firmwareSubVersionService;

    @Mock
    private IFirmwareBuildDAO firmwareBuildDAO;
    @Mock
    private FactoryDataQueryService factoryDataQueryService;
    @Mock
    private DeviceModelTenantService deviceModelTenantService;
    @Mock
    private IDeviceManualDAO deviceManualDAO;

    @Mock
    private IFirmwareSubVersionDAO deviceSubVersionDAO;

    @Mock
    private FirmwareBuildGroupConfig firmwareBuildGroupConfig;

    @Mock
    private S3Service s3Service;

    @BeforeEach
    public void setUp() {

        // Mock the method getOtaUnlimitedDevicesSet in your service class
        Set<String> whiteDevices = new HashSet<>();
        whiteDevices.add("123456");

        when(firmwareService.getOtaUnlimitedDevicesSet()).thenReturn(whiteDevices);
    }

    @Test
    public void testAllowBstationDeviceOTANoLimit() {
        boolean result = firmwareService.allowBstationDeviceOTA(1, "", "123456");
        assertTrue(result);
    }

    @Test
    public void testAllowBstationDeviceOTAWhiteListed() {
        boolean result = firmwareService.allowBstationDeviceOTA(1, "2:0", "123456");
        assertTrue(result);
    }

    @Test
    public void testAllowBstationDeviceOTAShardMatches() {
        boolean result = firmwareService.allowBstationDeviceOTA(1, "2:0", "000002"); // id % 2 == 0 -> shard is 0, which is allowed.
        assertTrue(result);

        result = firmwareService.allowBstationDeviceOTA(1, "2:1", "000003"); // id % 2 == 1 -> shard is 1, which is allowed.
        assertTrue(result);
    }

    @Test
    public void testAllowBstationDeviceOTAShardDoesNotMatch() {
        boolean result = firmwareService.allowBstationDeviceOTA(1, "2:0", "000003"); // id % 2 == 1 -> shard is not allowed.
        assertFalse(result);

        result = firmwareService.allowBstationDeviceOTA(1, "3:0,2", "000004"); // id % 3 == 1 -> shard is not allowed.
        assertFalse(result);
    }

    @Test
    public void testAllowBstationDeviceOTAMalformedLimit() {
        boolean result = firmwareService.allowBstationDeviceOTA(1, "invalid_limit", "123456");
        assertFalse(result);


        result = firmwareService.allowBstationDeviceOTA(1,"10:","123456");// Test empty shards list

        assertFalse(result);

    }

    @Test
    public void testGetOtaUnlimitedDevicesSetEmptyString() {
        firmwareService.setOtaUnlimitedDevices(""); // 设置空字符串情景下的 otaUnlimitedDevices 字段值。

        Set<String> result = firmwareService.getOtaUnlimitedDevicesSet();
        assertTrue(result.contains(""));
    }

    @Test
    public void testGetOtaUnlimitedDevicesSetNullString() {
        firmwareService.setOtaUnlimitedDevices(null); // 设置 null 情景下的 otaUnlimitedDevices 字段值。

        Set<String> result = firmwareService.getOtaUnlimitedDevicesSet();
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetOtaUnlimitedDevicesSetValidInput() {
        String validInput = "device1,device2,device3";
        firmwareService.setOtaUnlimitedDevices(validInput); // 设置有效输入情景下的 otaUnlimitedDevices 字段值。

        Set<String> expectedResult = new HashSet<>();
        expectedResult.add("device1");
        expectedResult.add("device2");
        expectedResult.add("device3");

        Set<String> result = firmwareService.getOtaUnlimitedDevicesSet();
        assertEquals(expectedResult, result);
    }



    @Test
    @DisplayName("获取最新一次已激活的版本")
    public void getLastActiveFirmwareMinSupport(){
        String modelNo = "CG1";
        List<FirmwareTableDO> firmwareTableDOList = Lists.newArrayList();
        FirmwareTableDO firmwareTableDO1 = new FirmwareTableDO();
        firmwareTableDO1.setFirmwareId("0.3.1");
        firmwareTableDO1.setActivated(1);
        firmwareTableDO1.setMinSupport("0.2.1");
        firmwareTableDO1.setModelNo(modelNo);
        firmwareTableDOList.add(firmwareTableDO1);

        FirmwareTableDO firmwareTableDO2 = new FirmwareTableDO();
        firmwareTableDO2.setFirmwareId("0.3.1");
        firmwareTableDO2.setActivated(2);
        firmwareTableDO2.setMinSupport("0.2.5");
        firmwareTableDO2.setModelNo(modelNo);
        firmwareTableDOList.add(firmwareTableDO2);

        when(firmwareDAO.getAllFirmwareForModel(eq(modelNo), any())).thenReturn(firmwareTableDOList);

        FirmwareTableDO firmwareTableDOResult = firmwareService.getLatestActivatedFirmwareByModelNo(modelNo);

        assertEquals(firmwareTableDO1.getMinSupport(), firmwareTableDOResult.getMinSupport());
    }


    @Test(expected = BaseException.class)
    @DisplayName("获取最新可升级固件-传入参数设备序列号为空，抛出异常")
    public void queryFirmwareLSerialNumberEmptyTest(){
        DeviceDO deviceInfo = new DeviceDO();
        DeviceOTADO deviceOTADO = new DeviceOTADO();
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView( deviceInfo,  deviceOTADO);
    }

    @Test(expected = BaseException.class)
    @DisplayName("获取最新可升级固件-传入参数错误-型号不存在，抛出异常")
    public void queryFirmwareLModelEmptyTest(){
        DeviceDO deviceInfo = new DeviceDO();
        DeviceOTADO deviceOTADO = new DeviceOTADO();
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceInfo);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(null);
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView( deviceInfo,  deviceOTADO);
    }

    @Test
    @DisplayName("获取最新可升级固件-升级状态-仅提示需升级")
    public void firmwareStatusBehindLatestFirmwareTest(){
        String modelNo = "CG1";
        Integer adminId = 1;
        String serialNumber = "a";
        String currentVersion = "0.1.1";

        DeviceDO deviceInfo = new DeviceDO();
        deviceInfo.setSerialNumber(serialNumber);
        deviceInfo.setFirmwareId(currentVersion);
        deviceInfo.setOtaIgnored(0);

        DeviceOTADO deviceOTADO = new DeviceOTADO();
        deviceOTADO.setSerialNumber(serialNumber);
        deviceOTADO.setInProgress(0);


        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceInfo);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(modelNo);
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(adminId);
        when(firmwareDAO.getAllFirmwareForModel(any(), any())).thenReturn(this.queryIgnoreFirmwareList(modelNo));
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView( deviceInfo,  deviceOTADO);
        log.info("获取到的可升级固件 {}",firmwareViewDO);
        // 目标升级版本
        int actualResult = firmwareViewDO.getFirmwareStatus();
        int expectedResult = 1;
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取最新可升级固件-升级状态-需升级且升级中")
    public void firmwareStatus_BehindLatestAndInProgressTest(){
        String modelNo = "CG1";
        Integer adminId = 1;
        String serialNumber = "a";
        String currentVersion = "0.1.1";

        DeviceDO deviceInfo = new DeviceDO();
        deviceInfo.setSerialNumber(serialNumber);
        deviceInfo.setFirmwareId(currentVersion);
        deviceInfo.setOtaIgnored(0);

        DeviceOTADO deviceOTADO = new DeviceOTADO();
        deviceOTADO.setSerialNumber(serialNumber);
        deviceOTADO.setInProgress(1);
        deviceOTADO.setOtaStartTime(PhosUtils.getUTCStamp() - 100);
        deviceOTADO.setLastAct(PhosUtils.getUTCStamp() - 100);

        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceInfo);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(modelNo);
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(adminId);
        when(firmwareDAO.getAllFirmwareForModel(any(), any())).thenReturn(this.queryIgnoreFirmwareList(modelNo));
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView( deviceInfo,  deviceOTADO);
        log.info("获取到的可升级固件 {}",firmwareViewDO);
        // 目标升级版本
        int actualResult = firmwareViewDO.getFirmwareStatus();
        // (1+4) 需升级 + ota升级中
        int expectedResult = 5;
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取最新可升级固件-升级状态-需升级且升级中且强制升级")
    public void firmwareStatus_BehindLatestAndInProgressAndForceUpgradeTest(){
        String modelNo = "CG1";
        Integer adminId = 1;
        String serialNumber = "a";
        String currentVersion = "0.1.1";

        DeviceDO deviceInfo = new DeviceDO();
        deviceInfo.setSerialNumber(serialNumber);
        deviceInfo.setFirmwareId(currentVersion);
        deviceInfo.setOtaIgnored(0);

        DeviceOTADO deviceOTADO = new DeviceOTADO();
        deviceOTADO.setSerialNumber(serialNumber);
        deviceOTADO.setInProgress(1);
        deviceOTADO.setOtaStartTime(PhosUtils.getUTCStamp() - 100);
        deviceOTADO.setLastAct(PhosUtils.getUTCStamp() - 100);

        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceInfo);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(modelNo);
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(adminId);
        when(firmwareDAO.getAllFirmwareForModel(any(), any())).thenReturn(this.queryForceUpgradeFirmwareList(modelNo));
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView( deviceInfo,  deviceOTADO);
        log.info("获取到的可升级固件 {}",firmwareViewDO);
        // 目标升级版本
        int actualResult = firmwareViewDO.getFirmwareStatus();
        // (1+4+8+16) 需升级、升级中、强制升级
        int expectedResult = 13;
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取最新可升级固件-升级状态-需升级且升级中且需升级提醒")
    public void firmwareStatus_BehindLatestAndInProgressAndRecommendTest(){
        String modelNo = "CG1";
        Integer adminId = 1;
        String serialNumber = "a";
        String currentVersion = "0.1.1";

        DeviceDO deviceInfo = new DeviceDO();
        deviceInfo.setSerialNumber(serialNumber);
        deviceInfo.setFirmwareId(currentVersion);
        deviceInfo.setOtaIgnored(0);

        DeviceOTADO deviceOTADO = new DeviceOTADO();
        deviceOTADO.setSerialNumber(serialNumber);
        deviceOTADO.setInProgress(1);
        deviceOTADO.setOtaStartTime(PhosUtils.getUTCStamp() - 100);
        deviceOTADO.setLastAct(PhosUtils.getUTCStamp() - 100);

        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceInfo);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(modelNo);
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(adminId);
        when(firmwareDAO.getAllFirmwareForModel(any(), any())).thenReturn(this.queryRecommendFirmwareList(modelNo));
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView( deviceInfo,  deviceOTADO);
        log.info("获取到的可升级固件 {}",firmwareViewDO);
        // 目标升级版本
        int actualResult = firmwareViewDO.getFirmwareStatus();
        // (1+4+16) 需升级、升级中、需提醒
        int expectedResult = 21;
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取最新可升级固件-升级状态-需升级且需升级提醒")
    public void firmwareStatus_BehindLatestAndRecommendTest(){
        String modelNo = "CG1";
        Integer adminId = 1;
        String serialNumber = "a";
        String currentVersion = "0.1.1";

        DeviceDO deviceInfo = new DeviceDO();
        deviceInfo.setSerialNumber(serialNumber);
        deviceInfo.setFirmwareId(currentVersion);
        deviceInfo.setOtaIgnored(0);

        DeviceOTADO deviceOTADO = new DeviceOTADO();
        deviceOTADO.setSerialNumber(serialNumber);
        deviceOTADO.setInProgress(0);
        deviceOTADO.setOtaStartTime(PhosUtils.getUTCStamp() - 1000);
        deviceOTADO.setLastAct(PhosUtils.getUTCStamp() - 1000);

        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceInfo);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(modelNo);
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(adminId);
        when(firmwareDAO.getAllFirmwareForModel(any(), any())).thenReturn(this.queryRecommendFirmwareList(modelNo));
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView( deviceInfo,  deviceOTADO);
        log.info("获取到的可升级固件 {}",firmwareViewDO);
        // 目标升级版本
        int actualResult = firmwareViewDO.getFirmwareStatus();
        // (1+16) 需升级、需提醒
        int expectedResult = 17;
        assertEquals(expectedResult, actualResult);


        // 验证ota firmwareId
        String expectedFirmwareId = "0.1.2";
        String actualFirmwareId = firmwareViewDO.getTargetFirmware();
        assertEquals(expectedFirmwareId, actualFirmwareId);
    }


    /**
     * 提醒升级
     * @param modelNo
     * @return
     */
    private List<FirmwareTableDO> queryRecommendFirmwareList(String modelNo){
        List<FirmwareTableDO> list = Lists.newArrayList();
        FirmwareTableDO firmwareTableDO1 = new FirmwareTableDO();
        firmwareTableDO1.setModelNo(modelNo);
        firmwareTableDO1.setFirmwareId("0.1.1");
        firmwareTableDO1.setMinReachable("0.0.0");
        firmwareTableDO1.setMinSupport("0.0.0");
        firmwareTableDO1.setUpgradeType(0);
        firmwareTableDO1.setActivated(1);
        firmwareTableDO1.setDeprecated(0);
        list.add(firmwareTableDO1);

        FirmwareTableDO firmwareTableDO2 = new FirmwareTableDO();
        firmwareTableDO2.setModelNo(modelNo);
        firmwareTableDO2.setFirmwareId("0.1.2");
        firmwareTableDO2.setMinReachable("0.0.0");
        firmwareTableDO2.setMinSupport("0.0.0");
        firmwareTableDO2.setUpgradeType(1);
        firmwareTableDO2.setActivated(1);
        firmwareTableDO2.setDeprecated(0);
        firmwareTableDO2.setRecommendUpgradeVersion("0.1.1");
        list.add(firmwareTableDO2);
        return list;
    }

    /**
     * 可忽略
     * @param modelNo
     * @return
     */
    private List<FirmwareTableDO> queryIgnoreFirmwareList(String modelNo){
        List<FirmwareTableDO> list = Lists.newArrayList();
        FirmwareTableDO firmwareTableDO1 = new FirmwareTableDO();
        firmwareTableDO1.setModelNo(modelNo);
        firmwareTableDO1.setFirmwareId("0.1.1");
        firmwareTableDO1.setMinReachable("0.0.0");
        firmwareTableDO1.setMinSupport("0.0.0");
        firmwareTableDO1.setUpgradeType(0);
        firmwareTableDO1.setActivated(1);
        firmwareTableDO1.setDeprecated(0);
        list.add(firmwareTableDO1);

        FirmwareTableDO firmwareTableDO2 = new FirmwareTableDO();
        firmwareTableDO2.setModelNo(modelNo);
        firmwareTableDO2.setFirmwareId("0.1.2");
        firmwareTableDO2.setMinReachable("0.0.0");
        firmwareTableDO2.setMinSupport("0.0.0");
        firmwareTableDO2.setUpgradeType(0);
        firmwareTableDO2.setActivated(1);
        firmwareTableDO2.setDeprecated(0);
        list.add(firmwareTableDO2);

        FirmwareTableDO firmwareTableDO3 = new FirmwareTableDO();
        firmwareTableDO3.setModelNo(modelNo);
        firmwareTableDO3.setFirmwareId("0.1.3");
        firmwareTableDO3.setMinReachable("0.0.0");
        firmwareTableDO3.setMinSupport("0.0.0");
        firmwareTableDO3.setUpgradeType(0);
        firmwareTableDO3.setActivated(1);
        firmwareTableDO3.setDeprecated(0);
        list.add(firmwareTableDO3);
        return list;
    }

    /**
     * 最新版本为强制升级
     * @param modelNo
     * @return
     */
    private List<FirmwareTableDO> queryForceUpgradeFirmwareList(String modelNo){
        List<FirmwareTableDO> list = Lists.newArrayList();
        FirmwareTableDO firmwareTableDO1 = new FirmwareTableDO();
        firmwareTableDO1.setModelNo(modelNo);
        firmwareTableDO1.setFirmwareId("0.1.1");
        firmwareTableDO1.setMinReachable("0.0.0");
        firmwareTableDO1.setMinSupport("0.0.0");
        firmwareTableDO1.setRecommendUpgradeVersion("0.1.4");
        firmwareTableDO1.setUpgradeType(0);
        firmwareTableDO1.setActivated(1);
        firmwareTableDO1.setDeprecated(0);
        list.add(firmwareTableDO1);

        FirmwareTableDO firmwareTableDO2 = new FirmwareTableDO();
        firmwareTableDO2.setModelNo(modelNo);
        firmwareTableDO2.setFirmwareId("0.1.2");
        firmwareTableDO2.setMinReachable("0.0.0");
        firmwareTableDO2.setMinSupport("0.0.0");
        firmwareTableDO2.setRecommendUpgradeVersion("0.1.4");
        firmwareTableDO2.setUpgradeType(0);
        firmwareTableDO2.setActivated(1);
        firmwareTableDO2.setDeprecated(0);
        list.add(firmwareTableDO2);

        FirmwareTableDO firmwareTableDO3 = new FirmwareTableDO();
        firmwareTableDO3.setModelNo(modelNo);
        firmwareTableDO3.setFirmwareId("0.1.3");
        firmwareTableDO3.setMinReachable("0.0.0");
        firmwareTableDO3.setMinSupport("0.0.0");
        firmwareTableDO3.setRecommendUpgradeVersion("0.1.4");
        firmwareTableDO3.setUpgradeType(0);
        firmwareTableDO3.setActivated(1);
        firmwareTableDO3.setDeprecated(0);
        list.add(firmwareTableDO3);

        FirmwareTableDO firmwareTableDO4 = new FirmwareTableDO();
        firmwareTableDO4.setModelNo(modelNo);
        firmwareTableDO4.setFirmwareId("0.1.4");
        firmwareTableDO4.setMinReachable("0.0.0");
        firmwareTableDO4.setMinSupport("0.1.4");
        firmwareTableDO4.setRecommendUpgradeVersion("0.1.4");
        firmwareTableDO4.setUpgradeType(2);
        firmwareTableDO4.setActivated(1);
        firmwareTableDO4.setDeprecated(0);
        list.add(firmwareTableDO4);
        return list;
    }

    @Test
    public void test_getAllFirmwareForModel() {
        when(firmwareDAO.getAllFirmwareForModel(any(), isNull())).thenReturn(Arrays.asList(FirmwareTableDO.builder().firmwareId("0.0.1").modelNo("CG1").useDebug(false).build(), FirmwareTableDO.builder().firmwareId("0.0.2").modelNo("CG1").useDebug(true).build(), FirmwareTableDO.builder().firmwareId("0.0.3").modelNo("CG1").useDebug(false).build()));
        when(firmwareDAO.getAllFirmwareForModel(any(), eq(false))).thenReturn(Arrays.asList(FirmwareTableDO.builder().firmwareId("0.0.1").modelNo("CG1").build(), FirmwareTableDO.builder().firmwareId("0.0.3").modelNo("CG1").build()));

        when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().firmwareId("0.0.1").build());

        List<FirmwareTableDO> firmwareTableDOList = firmwareService.getAllFirmwareForModel("CG1", null);
        Assert.assertTrue(CollectionUtils.isNotEmpty(firmwareTableDOList));

        when(deviceOTADebugConfig.getOtaDebugWhiteSnSet()).thenReturn(Collections.emptySet());
        when(deviceOTADebugConfig.getOtaDebugBlackSnSet()).thenReturn(Collections.emptySet());
        firmwareTableDOList = firmwareService.getAllFirmwareForModel("CG1", "sn_01");
        Assert.assertTrue(CollectionUtils.size(firmwareTableDOList) == 2);

        when(deviceOTADebugConfig.getOtaDebugWhiteSnSet()).thenReturn(Collections.singleton("sn_01"));
        when(deviceOTADebugConfig.getOtaDebugBlackSnSet()).thenReturn(Collections.emptySet());
        firmwareTableDOList = firmwareService.getAllFirmwareForModel("CG1", "sn_01");
        Assert.assertTrue(CollectionUtils.size(firmwareTableDOList) == 3);

        when(deviceOTADebugConfig.getOtaDebugWhiteSnSet()).thenReturn(Collections.emptySet());
        when(deviceOTADebugConfig.getOtaDebugBlackSnSet()).thenReturn(Collections.singleton("sn_01"));
        firmwareTableDOList = firmwareService.getAllFirmwareForModel("CG1", "sn_01");
        Assert.assertTrue(CollectionUtils.size(firmwareTableDOList) == 1);

        when(deviceOTADebugConfig.getOtaDebugWhiteSnSet()).thenReturn(Collections.singleton("sn_01"));
        when(deviceOTADebugConfig.getOtaDebugBlackSnSet()).thenReturn(Collections.singleton("sn_01"));
        firmwareTableDOList = firmwareService.getAllFirmwareForModel("CG1", "sn_01");
        Assert.assertTrue(CollectionUtils.size(firmwareTableDOList) == 1);


    }

    @Test
    public void test_getAllOtaInfoForDevice() {
        when(firmwareDAO.selectOTAInfoByModelNo(any(), isNull())).thenReturn(Arrays.asList(new OTADBValue() {{
            setFirmwareId("0.0.1");
            setModelNo("CG1");
        }},new OTADBValue() {{
            setFirmwareId("0.0.2");
            setModelNo("CG1");
        }},new OTADBValue() {{
            setFirmwareId("0.0.3");
            setModelNo("CG1");
        }}));
        when(firmwareDAO.selectOTAInfoByModelNo(any(), eq(false))).thenReturn(Arrays.asList(new OTADBValue() {{
            setFirmwareId("0.0.1");
            setModelNo("CG1");
        }}, new OTADBValue() {{
            setFirmwareId("0.0.3");
            setModelNo("CG1");
        }}));

        when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().firmwareId("0.0.1").build());

        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");

        when(deviceOTADebugConfig.getOtaDebugWhiteSnSet()).thenReturn(Collections.emptySet());
        when(deviceOTADebugConfig.getOtaDebugBlackSnSet()).thenReturn(Collections.emptySet());
        List<OTADBValue> otadbValueList = firmwareService.getAllOtaInfoForDevice("sn_01", null);
        Assert.assertTrue(CollectionUtils.size(otadbValueList) == 2);

        when(deviceOTADebugConfig.getOtaDebugWhiteSnSet()).thenReturn(Collections.singleton("sn_01"));
        when(deviceOTADebugConfig.getOtaDebugBlackSnSet()).thenReturn(Collections.emptySet());
        otadbValueList = firmwareService.getAllOtaInfoForDevice("sn_01", null);
        Assert.assertTrue(CollectionUtils.size(otadbValueList) == 3);

        when(deviceOTADebugConfig.getOtaDebugWhiteSnSet()).thenReturn(Collections.emptySet());
        when(deviceOTADebugConfig.getOtaDebugBlackSnSet()).thenReturn(Collections.singleton("sn_01"));
        otadbValueList = firmwareService.getAllOtaInfoForDevice("sn_01", null);
        Assert.assertTrue(CollectionUtils.size(otadbValueList) == 1);

        when(deviceOTADebugConfig.getOtaDebugWhiteSnSet()).thenReturn(Collections.singleton("sn_01"));
        when(deviceOTADebugConfig.getOtaDebugBlackSnSet()).thenReturn(Collections.singleton("sn_01"));
        otadbValueList = firmwareService.getAllOtaInfoForDevice("sn_01",null);
        Assert.assertTrue(CollectionUtils.size(otadbValueList) == 1);
    }


    @Test
    @DisplayName("otaList 倒排序")
    public void otaListSort(){
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("CG1");
        when(deviceOTADebugConfig.getOtaDebugWhiteSnSet()).thenReturn(Collections.emptySet());
        when(deviceOTADebugConfig.getOtaDebugBlackSnSet()).thenReturn(Collections.emptySet());

        when(firmwareDAO.selectOTAInfoByModelNo(any(), any())).thenReturn(Arrays.asList(new OTADBValue() {{
            setFirmwareId("0.0.1");
            setModelNo("CG1");
        }},new OTADBValue() {{
            setFirmwareId("0.0.2");
            setModelNo("CG2");
        }},new OTADBValue() {{
            setFirmwareId("0.0.3");
            setModelNo("CG3");
        }}));
        List<OTADBValue> otadbValueList = firmwareService.getAllOtaInfoForDevice("sn_01", null);
        String expectedResult = "0.0.3";
        String actualResult = otadbValueList.get(0).getFirmwareId();
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("firmwareOta test")
    public void test_sendDeviceOtaStart() throws Exception  {
        Mockito.when(firmwareDAO.selectFirmwareByIdAndModelNo(Mockito.any(), Mockito.any())).thenReturn(new OTADBValue() {{
        }});
        Mockito.when(deviceInfoService.waitDeviceWakeUp(Mockito.any())).thenReturn(true);
        Mockito.when(otaConfig.getPacket()).thenReturn(Collections.emptyMap());
        Mockito.when(otaStartConfig.getOtaStartCheckLowBatterWhiteSnSet()).thenReturn(Collections.emptySet());
        
        Mockito.when(deviceOperationHelper.waitOperation(Mockito.any(), Mockito.any())).thenReturn(Result.Success());
        Mockito.when(deviceOperationHelper.waitOperation(Mockito.any())).thenReturn(Result.Failure(""));
        Mockito.when(deviceService.getAllDeviceInfo(Mockito.any())).thenReturn(new DeviceDO());
        
        VernemqPublisher vernemqPublisher = new VernemqPublisher();
        Field mqttSenderField = VernemqPublisher.class.getDeclaredField("mqttSender");
        mqttSenderField.setAccessible(true);
        mqttSenderField.set(vernemqPublisher, mqttSender);
        vernemqPublisher.init();
        
        Result result = firmwareService.startDeviceOTAWithVersion(null, "sn_01", "0.1", true, false, false);
        Assert.assertTrue(Result.failureFlag.equals(result.getResult()));

        Mockito.when(deviceOperationHelper.waitOperation(Mockito.any(), Mockito.any())).thenReturn(Result.Failure(""));
        result = firmwareService.startDeviceOTAWithVersion(null, "sn_01", "0.1", true, false, false);
        Assert.assertTrue(Result.failureFlag.equals(result.getResult()));

        mqttSenderField.set(vernemqPublisher, null);
        vernemqPublisher.init();
    }


    @Test
    public void test_startOtaFromApp() {
        when(deviceDormancyPlanService.checkDeviceDormancyStatus(any())).thenReturn(true);

        Assert.assertThrows(Exception.class, () -> {
            firmwareService.startOtaFromApp("sn_01", FirmwareViewDO.builder().build(), false);
        });

        Assert.assertThrows(Exception.class, () -> {
            firmwareService.startOtaFromDevice("sn_01", FirmwareViewDO.builder().build(), "aaaa");
        });

        Mockito.verify(deviceDormancyPlanService, Mockito.atLeastOnce()).checkDeviceDormancyStatus(any());
    }

    @Test
    public void test_startOtaFromDevice() {
        when(deviceDormancyPlanService.checkDeviceDormancyStatus(any())).thenReturn(true);

        Assert.assertThrows(Exception.class, () -> {
            firmwareService.startOtaFromDevice("sn_01", FirmwareViewDO.builder().build(), null);
        });

        Assert.assertThrows(Exception.class, () -> {
            firmwareService.startOtaFromDevice("sn_01", FirmwareViewDO.builder().build(), "aaa");
        });

        Mockito.verify(deviceDormancyPlanService, Mockito.atLeastOnce()).checkDeviceDormancyStatus(any());
    }

    @Test
    @DisplayName("获取最新可升级固件-升级状态-需升级且升级中且需升级提醒")
    public void testGetLatestVersion(){
        String modelNo = "CG1";
        Integer adminId = 1;
        String serialNumber = "a";
        String currentVersion = "0.1.1";

        DeviceDO deviceInfo = new DeviceDO();
        deviceInfo.setSerialNumber(serialNumber);
        deviceInfo.setFirmwareId(currentVersion);
        deviceInfo.setOtaIgnored(0);

        DeviceOTADO deviceOTADO = new DeviceOTADO();
        deviceOTADO.setSerialNumber(serialNumber);
        deviceOTADO.setInProgress(1);
        deviceOTADO.setOtaStartTime(PhosUtils.getUTCStamp() - 100);
        deviceOTADO.setLastAct(PhosUtils.getUTCStamp() - 100);

        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceInfo);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn(modelNo);
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(adminId);
        when(cloudfrontService.createCloudfrontUrl(any(),any(),any())).thenReturn("path");
        List<FirmwareSubVersionTableDO> subVersions = new ArrayList<>();
        FirmwareSubVersionTableDO deviceSubVersionTableDO = new FirmwareSubVersionTableDO();
        deviceSubVersionTableDO.setFirmwareId("0.0.2");
        deviceSubVersionTableDO.setRootFirmwareId(currentVersion);
        deviceSubVersionTableDO.setApplication("iot-local");
        deviceSubVersionTableDO.setModelNo(modelNo);
        subVersions.add(deviceSubVersionTableDO);

        when(firmwareSubVersionService.getSubversions(any(),any())).thenReturn(subVersions);
        when(firmwareDAO.getAllFirmwareForModel(any(), any())).thenReturn(this.queryRecommendFirmwareList(modelNo));
        when(firmwareDAO.getFirmwareByPkey(any(), any(),any(),any())).thenReturn(new FirmwareTableDO());

        Integer userId = 123;

        OtaLatestVersionViewDO version = firmwareService.getLatestVersion(userId, serialNumber, currentVersion, modelNo, 4,null);
        log.info("获取到的可升级固件 {}",version);


         version = firmwareService.getLatestVersion(userId, serialNumber, "0.1.3", modelNo, 4,null);
        log.info("获取到的可升级固件 {}",version);

        when(firmwareDAO.getAllFirmwareForModel(any(), any())).thenReturn(new ArrayList<>());
        version = firmwareService.getLatestVersion(userId, serialNumber, currentVersion, modelNo, 4,null);
        log.info("获取到的可升级固件 {}",version);

//        // 目标升级版本
//        int actualResult = firmwareViewDO.getFirmwareStatus();
//        // (1+4+16) 需升级、升级中、需提醒
//        int expectedResult = 21;
//        Assert.assertEquals(expectedResult, actualResult);
    }


    @Test
    public void testBuildBxVersionWithValidRequest() {
        // Arrange
        FirmwareLinkRequest request = new FirmwareLinkRequest();
        request.setRootFirmwareId("0.0.1");
        HashMap<String, String> map = new HashMap<>();
        map.put("iot-local","0.0.1");
        request.setAppFirmwareIdMap(map);
        request.setPlatform("BXSoft");

        // Mock the firmwareDAO to return a firmware with activated status
        FirmwareTableDO firmwareTableDO = new FirmwareTableDO();
        firmwareTableDO.setActivated(0);
        when(firmwareDAO.getFirmwareByPkey(null, "0.0.1", "BXSoft", FIRMWARE_ACTIVATED)).thenReturn(null);

        // Mock the firmwareBuildDAO to return a new build info
        FirmwareBuildInfoTableDO buildInfo = new FirmwareBuildInfoTableDO();
        buildInfo.setFirmwareId("0.0.1");
        buildInfo.setPlatform("BXSoft");
        when(firmwareBuildDAO.insertNewFirmwareBuildInfo(buildInfo)).thenReturn(1);

        when(factoryDataQueryService.queryPlatformModelNo(any())).thenReturn(Collections.singletonList("model1"));

        // Act
        System.setProperty("unit-test","true");
        request.setUseDebug(0);
        Object result = firmwareService.buildBxVersion(request);

        // Assert
        assertNotNull(result);
        verify(firmwareDAO).getFirmwareByPkey(null, "0.0.1", "BXSoft", FIRMWARE_ACTIVATED);

        when(firmwareDAO.getFirmwareByPkey(null, "0.0.1", "BXSoft", FIRMWARE_ACTIVATED)).thenReturn(firmwareTableDO);
        try{
            result = firmwareService.buildBxVersion(request);
        }catch (Exception e){
            assertEquals(BaseException.class, e.getClass());
        }

        // Act
        System.setProperty("unit-test","true");
        request.setUseDebug(1);
        request.setRootFirmwareId("0.0.3");
        result = firmwareService.buildBxVersion(request);


    }

    @Test(expected = BaseException.class)
    public void testBuildBxVersionWithInvalidRequest() {
        // Arrange
        FirmwareLinkRequest request = new FirmwareLinkRequest();
        request.setRootFirmwareId(null);
        request.setAppFirmwareIdMap(null);
        request.setRequestTime(1701327359L);
        request.setAuth("2b63d2b3ee25ee23003365d8bfc47651");

        // Act
        firmwareService.buildBxVersion(request);
        // Assert
        // Expect a BaseException to be thrown
    }

    @Test
    public void testBuildBxVersionWithAlreadyPublishedFirmware() {
        // Arrange
        FirmwareLinkRequest request = new FirmwareLinkRequest();
        request.setRootFirmwareId("123");
        request.setAppFirmwareIdMap(new HashMap<>());
        request.setPlatform("android");

        // Mock the firmwareDAO to return a firmware with activated status
        FirmwareTableDO firmwareTableDO = new FirmwareTableDO();
        firmwareTableDO.setActivated(1);
        when(firmwareDAO.getFirmwareByPkey(null, "123", "BXSoft", 1)).thenReturn(firmwareTableDO);

        // Act
        try {
            firmwareService.buildBxVersion(request);
        }catch (Exception e){
            assertEquals("版本不能为空", e.getMessage());
        }


        // Act
        try {
            HashMap<String, String> map = new HashMap<>();
            map.put("iot-local","0.0.1");
            request.setAppFirmwareIdMap(map);
            firmwareService.buildBxVersion(request);
        }catch (Exception e){
            assertEquals("固件版本已发布，请重新输入版本", e.getMessage());
        }

        // Assert
        // Expect a BaseException to be thrown
    }

    @Test
    public void testGetAllBxOtaInfoForDevice() {
        // Arrange
        String version = "1.0.0";
        String modelNo = "ABC123";
        String serialNumber = "123456789";
        List<OTADBValue> expectedList = new ArrayList<>();
        OTADBValue otaDBValue1 = new OTADBValue();
        otaDBValue1.setFirmwareId("1.0.0");
        otaDBValue1.setVersion("1.0.0");
        expectedList.add(otaDBValue1);

        OTADBValue otaDBValue2 = new OTADBValue();
        otaDBValue2.setFirmwareId("2.0.0");
        otaDBValue2.setVersion("2.0.0");
        expectedList.add(otaDBValue2);

        when(firmwareDAO.selectBxOTAInfoByModelNo(modelNo, null, 0 , 50)).thenReturn(expectedList);

        // Act
        List<OTADBValue> actualList = firmwareService.getAllBxOtaInfoForDevice(version, modelNo, serialNumber, 1 , 50);

        // Assert
        assertEquals(expectedList, actualList);
        verify(firmwareDAO, times(1)).selectBxOTAInfoByModelNo(modelNo, null, 0, 50);
    }

    @Test
    public void testGetPubList() {
        // Arrange
        PubListRequest request = new PubListRequest();
        request.setGroup("组1");
        request.setPage(1);
        request.setPageSize(10);
        List<String> platforms = new ArrayList<>();
        platforms.add("platform1");
        List<String> platFormModelNos = new ArrayList<>();
        platFormModelNos.add("model1");
        platFormModelNos.add("model2");
        List<String> firmwareIds = new ArrayList<>();
        firmwareIds.add("firmware1");
        firmwareIds.add("firmware2");
        List<FirmwareTableDO> firmwareDoList = new ArrayList<>();
        FirmwareTableDO firmware1 = new FirmwareTableDO();
        firmware1.setModelNo("model1");
        firmware1.setFirmwareId("firmware1");
        firmwareDoList.add(firmware1);
        FirmwareTableDO firmware2 = new FirmwareTableDO();
        firmware2.setModelNo("model2");
        firmware2.setFirmwareId("firmware2");
        firmwareDoList.add(firmware2);
        Map<String, List<String>> modelTenantMap = new HashMap<>();
        List<String> tenantList1 = new ArrayList<>();
        tenantList1.add("tenant1");
        tenantList1.add("tenant2");
        modelTenantMap.put("model1", tenantList1);
        List<ModelFirmwareUpgradeCount> upgradeCountList = new ArrayList<>();
        ModelFirmwareUpgradeCount count1 = new ModelFirmwareUpgradeCount();
        count1.setModelNo("model1");
        count1.setCount(10);
        count1.setTotal(100);
        upgradeCountList.add(count1);
        ModelFirmwareUpgradeCount count2 = new ModelFirmwareUpgradeCount();
        count2.setModelNo("model2");
        count2.setCount(20);
        count2.setTotal(100);
        upgradeCountList.add(count2);
        when(factoryDataQueryService.queryAllPlatforms()).thenReturn(platforms);
        when(factoryDataQueryService.queryPlatformModelNo("platform1")).thenReturn(platFormModelNos);
        when(firmwareDAO.getAllFirmwareIdsForModels(platFormModelNos, null, null)).thenReturn(firmwareIds);
        when(firmwareDAO.getAllFirmwareForModels(platFormModelNos, "firmware1", 0, 10)).thenReturn(firmwareDoList);
        when(deviceModelTenantService.queryDeviceModelTenantDOByModel(platFormModelNos)).thenReturn(modelTenantMap);
        when(deviceManualDAO.queryModelFirmwareUpgradeCount(platFormModelNos, "firmware1")).thenReturn(upgradeCountList);
        when(firmwareDAO.getAllFirmwareCountForModels(platFormModelNos, "firmware1")).thenReturn(2L);
        Map<String, List<String>> map = new HashMap<>();
        map.put("组1",platFormModelNos);
        when(firmwareBuildGroupConfig.getConfig()).thenReturn(map);

        // Act
        PageResult<List<FirmwareTableDO>> actualResult = firmwareService.getPubList(request);

        // Assert
        assertEquals(2, actualResult.getList().size());
        assertEquals("firmware1", actualResult.getList().get(0).getFirmwareId());
        assertEquals("firmware2", actualResult.getList().get(1).getFirmwareId());
        assertEquals(new Long(2), actualResult.getTotal());

        assertEquals(platforms, actualResult.getExtMap().get("platforms"));
        assertEquals("platform1", actualResult.getExtMap().get("defaultPlatform"));
        assertEquals(Arrays.asList( "all", "other","组1"), actualResult.getExtMap().get("groups"));
        assertEquals("firmware1", actualResult.getExtMap().get("defaultFirmwareId"));
        verify(factoryDataQueryService, times(1)).queryAllPlatforms();
        verify(factoryDataQueryService, times(1)).queryPlatformModelNo("platform1");
        verify(firmwareDAO, times(1)).getAllFirmwareIdsForModels(platFormModelNos, null, null);
        verify(firmwareDAO, times(1)).getAllFirmwareForModels(platFormModelNos, "firmware1", 0, 10);
        verify(deviceModelTenantService, times(1)).queryDeviceModelTenantDOByModel(platFormModelNos);
        verify(deviceManualDAO, times(1)).queryModelFirmwareUpgradeCount(platFormModelNos, "firmware1");

        request.setGroup("other");
        request.setFirmwareId("0.0.1");
        actualResult = firmwareService.getPubList(request);


    }

    @Test
    public void testUpdateBxBuildInfo() {
        // Arrange
        KernelFirmwareRequest request = new KernelFirmwareRequest();
        request.setId(1L);
        request.setGitsha("gitsha1");
        request.setMd5("md51");
        request.setDownloadUrl("downloadUrl1");
        request.setBuildSuccess(true);
        FirmwareBuildInfoTableDO firmwareBuildInfoTableDO = new FirmwareBuildInfoTableDO();
        firmwareBuildInfoTableDO.setId(1L);
        firmwareBuildInfoTableDO.setGitsha("gitsha2");
        firmwareBuildInfoTableDO.setMd5("md52");
        firmwareBuildInfoTableDO.setPath("path2");
        firmwareBuildInfoTableDO.setStatus(FirmwareBuildEnums.FAILED.getCode());
        firmwareBuildInfoTableDO.setUpdateTime(1000);
        when(firmwareBuildDAO.getFirmware(1L)).thenReturn(firmwareBuildInfoTableDO);
        when(firmwareBuildDAO.updateFirmwareDO(firmwareBuildInfoTableDO)).thenReturn(1);

        // Act
        boolean actualResult = firmwareService.updateBxBuildInfo(request);

        // Assert
        assertTrue(actualResult);
        assertEquals("gitsha1", firmwareBuildInfoTableDO.getGitsha());
        assertEquals("md51", firmwareBuildInfoTableDO.getMd5());
        assertEquals("downloadUrl1", firmwareBuildInfoTableDO.getPath());
        assertEquals(FirmwareBuildEnums.SUCCESS.getCode(), firmwareBuildInfoTableDO.getStatus());
        verify(firmwareBuildDAO, times(1)).getFirmware(1L);
        verify(firmwareBuildDAO, times(1)).updateFirmwareDO(firmwareBuildInfoTableDO);
    }

    @Test
    public void testUpdateBxBuildInfoNotFound() {
        // Arrange
        KernelFirmwareRequest request = new KernelFirmwareRequest();
        request.setId(1L);
        when(firmwareBuildDAO.getFirmware(1L)).thenReturn(null);

        // Act
        boolean actualResult = firmwareService.updateBxBuildInfo(request);

        // Assert
        assertFalse(actualResult);
        verify(firmwareBuildDAO, times(1)).getFirmware(1L);
        verify(firmwareBuildDAO, never()).updateFirmwareDO(any(FirmwareBuildInfoTableDO.class));
    }

    @Test
    public void testUpdateBxBuildInfoUpdateFailed() {
        // Arrange
        KernelFirmwareRequest request = new KernelFirmwareRequest();
        request.setId(1L);
        request.setGitsha("gitsha1");
        request.setMd5("md51");
        request.setDownloadUrl("downloadUrl1");
        request.setBuildSuccess(true);
        FirmwareBuildInfoTableDO firmwareBuildInfoTableDO = new FirmwareBuildInfoTableDO();
        firmwareBuildInfoTableDO.setId(1L);
        firmwareBuildInfoTableDO.setGitsha("gitsha2");
        firmwareBuildInfoTableDO.setMd5("md52");
        firmwareBuildInfoTableDO.setPath("path2");
        firmwareBuildInfoTableDO.setStatus(FirmwareBuildEnums.FAILED.getCode());
        firmwareBuildInfoTableDO.setUpdateTime(1000);
        when(firmwareBuildDAO.getFirmware(1L)).thenReturn(firmwareBuildInfoTableDO);
        when(firmwareBuildDAO.updateFirmwareDO(firmwareBuildInfoTableDO)).thenReturn(0);

        // Act
        boolean actualResult = firmwareService.updateBxBuildInfo(request);

        // Assert
        assertFalse(actualResult);
        assertEquals("gitsha1", firmwareBuildInfoTableDO.getGitsha());
        assertEquals("md51", firmwareBuildInfoTableDO.getMd5());
        assertEquals("downloadUrl1", firmwareBuildInfoTableDO.getPath());
        //更新为成功
        assertEquals(FirmwareBuildEnums.SUCCESS.getCode(), firmwareBuildInfoTableDO.getStatus());
        verify(firmwareBuildDAO, times(1)).getFirmware(1L);
        verify(firmwareBuildDAO, times(1)).updateFirmwareDO(firmwareBuildInfoTableDO);
    }

    @Test
    public void testGetBxBuildList() {
        // Arrange
        BXBuildListRequest request = new BXBuildListRequest();
        request.setFirmwareId("firmware1");
        request.setStartTime(1000L);
        request.setEndTime(2000L);
        request.setStatus(FirmwareBuildEnums.SUCCESS.getCode());
        request.setPage(1);
        request.setPageSize(10);
        List<FirmwareBuildInfoTableDO> expectedList = new ArrayList<>();
        FirmwareBuildInfoTableDO buildInfo1 = new FirmwareBuildInfoTableDO();
        buildInfo1.setId(1L);
        buildInfo1.setFirmwareId("firmware1");
        buildInfo1.setGitsha("gitsha1");
        buildInfo1.setMd5("md51");
        buildInfo1.setPath("path1");
        buildInfo1.setStatus(FirmwareBuildEnums.SUCCESS.getCode());
        buildInfo1.setCreateTime(1000);
        buildInfo1.setUpdateTime(1000);
        expectedList.add(buildInfo1);
        FirmwareBuildInfoTableDO buildInfo2 = new FirmwareBuildInfoTableDO();
        buildInfo2.setId(2L);
        buildInfo2.setFirmwareId("firmware1");
        buildInfo2.setGitsha("gitsha2");
        buildInfo2.setMd5("md52");
        buildInfo2.setPath("path2");
        buildInfo2.setStatus(FirmwareBuildEnums.FAILED.getCode());
        buildInfo2.setCreateTime(2000);
        buildInfo2.setUpdateTime(2000);
        expectedList.add(buildInfo2);
        when(firmwareBuildDAO.queryBuildInfoList("firmware1", 1000L, 2000L, FirmwareBuildEnums.SUCCESS.getCode(), 0, 10)).thenReturn(expectedList);
        when(firmwareBuildDAO.queryBuildInfoCount("firmware1", 1000L, 2000L, FirmwareBuildEnums.SUCCESS.getCode())).thenReturn(2L);

        // Act
        PageResult actualResult = firmwareService.getBxBuildList(request);

        // Assert
        assertEquals(expectedList, actualResult.getList());
        assertEquals(new Long(2), actualResult.getTotal());
        verify(firmwareBuildDAO, times(1)).queryBuildInfoList("firmware1", 1000L, 2000L, FirmwareBuildEnums.SUCCESS.getCode(), 0, 10);
        verify(firmwareBuildDAO, times(1)).queryBuildInfoCount("firmware1", 1000L, 2000L, FirmwareBuildEnums.SUCCESS.getCode());

        when(firmwareBuildDAO.getFirmwareIds(any())).thenReturn(Collections.singletonList("0.0.1"));
        assertEquals(Collections.singletonList("0.0.1"), firmwareService.queryBxBuildVersions());

    }


    @Test
    public void testGetBxBuildInfo() {
        // Arrange
        FirmwareBuildInfoTableDO buildInfo1 = new FirmwareBuildInfoTableDO();
        buildInfo1.setId(1L);
        buildInfo1.setFirmwareId("firmware1");
        buildInfo1.setGitsha("gitsha1");
        buildInfo1.setMd5("md51");
        buildInfo1.setPath("path1");
        buildInfo1.setStatus(FirmwareBuildEnums.SUCCESS.getCode());
        buildInfo1.setCreateTime(1000);
        buildInfo1.setUpdateTime(1000);

        when(firmwareBuildDAO.getFirmware(any())).thenReturn(buildInfo1);
        when(factoryDataQueryService.queryPlatformModelNo(any())).thenReturn(Collections.singletonList("model1"));

        // Act
        FirmwareBuildInfoTableDO buildInfo = firmwareService.getBxBuildInfo(1L);

        buildInfo1.setApplications(buildInfo.getApplications());
        // Assert
        assertEquals(buildInfo, buildInfo1);

    }


    @Test
    public void testKernelFirmwareRequestToTableDO() {

        // Act
        KernelFirmwareRequest request = new KernelFirmwareRequest() ;
        request.setBucketName("addx-firmware-cn");
        request.setObjectKey("debug/BX/iot-local/0.0.26.2/345299/c848d4c140859bc0e5d1f04caa319a40/bx_iot-local_0.0.26.2_345299_c848d4c140859bc0e5d1f04caa319a40.fw");


        FirmwareTableDO firmwareTableDO = kernelFirmwareService.kernelFirmwareRequestToTableDO(request);
        assertEquals(firmwareTableDO.getType(),"kernel");

        when(s3Service.getObjectMetadata(any(),any())).thenReturn(new ObjectMetadata());

        firmwareTableDO = kernelFirmwareService.kernelFirmwareRequestToTableDO(request);
        assertEquals(new Integer(0), firmwareTableDO.getSize());

    }

    @Test
    public void testPublishKernelFirmwarePlatform() {

        // Act
        KernelFirmwareRequest request = new KernelFirmwareRequest() ;
        request.setBucketName("addx-firmware-cn");
        request.setObjectKey("debug/BX/iot-local/0.0.26.2/345299/c848d4c140859bc0e5d1f04caa319a40/bx_iot-local_0.0.26.2_345299_c848d4c140859bc0e5d1f04caa319a40.fw");
        request.setPlatform(DEFAULT_CX_PLATFORMS.get(0));
        request.setApplication(SUB_SOFTWARE_TYPE);
        request.setFirmwareId("0.0.1");
        when(factoryDataQueryService.queryPlatformModelNo(any())).thenReturn(Collections.singletonList("model1"));

        Result result = kernelFirmwareService.publishKernelFirmwarePlatform(request);
        assertEquals(result.getResult(),Result.successFlag);

        request.setPlatform("new_p");
        request.setApplication("BXSoft");
        result = kernelFirmwareService.publishKernelFirmwarePlatform(request);
        assertEquals(result.getResult(),Result.successFlag);

        request.setPlatform("new_p");
        request.setApplication("iot-local");
        result = kernelFirmwareService.publishKernelFirmwarePlatform(request);
        assertEquals(result.getResult(),Result.successFlag);


        FirmwareTableDO activatedVersion = new FirmwareTableDO();
        when(firmwareDAO.getFirmwareByPkey(any(),any(),any(),any())).thenReturn(activatedVersion);
        try{
            result = kernelFirmwareService.publishKernelFirmwarePlatform(request);
        }catch (Exception e){
            assertEquals(BaseException.class, e.getClass());
        }

    }

    @Test
    public void testQqeryModelFirmware() {
        List<FirmwareTableDO> result = kernelFirmwareService.queryModelFirmware("");
        assertEquals(new ArrayList<>(),result);
    }

    @Test
    public void testBuildCallback() {
        KernelFirmwareRequest requst = new KernelFirmwareRequest();
        requst.setBuildSuccess(true);
        requst.setSize(100);
        requst.setPlatform("p1");
        requst.setFirmwareId("0.0.1");

        when(factoryDataQueryService.queryPlatformModelNo(any())).thenReturn(Collections.singletonList("model1"));
        when(firmwareServiceMock.updateBxBuildInfo(requst)).thenReturn(true);

        Result result = kernelFirmwareService.buildCallback(requst);
        assertEquals(result.getResult(),Result.successFlag);
    }

    @Test
    public void testLinkFirmware() {
        FirmwareLinkRequest requst = new FirmwareLinkRequest();
        Map<String, String> map = new HashMap<>();
        map.put("iot-local","0.0.1");
        map.put("bstationd","0.0.1");

        requst.setAppFirmwareIdMap(map);
        requst.setPlatform("p1");
        requst.setRootFirmwareId("0.0.1");

        when(factoryDataQueryService.queryPlatformModelNo(any())).thenReturn(Collections.singletonList("model1"));
        Result result = kernelFirmwareService.linkFirmware(requst);
        assertEquals(result.getResult(),Result.successFlag);
    }

    @Test
    public void testQuerySubFirmwareVersions() {
        Map<String, List<String>> result = kernelFirmwareService.querySubFirmwareVersions();
        assertNotNull(result);
    }

    @Test
    public void testUpdateSubSoftwarePubStatus() {
        FirmwareStatusRequest request = new FirmwareStatusRequest();

        ModelFirmwareStatus status = new ModelFirmwareStatus();
        List<ApplicationViewDo> list = new ArrayList<>();
        ApplicationViewDo vo = new ApplicationViewDo();
        vo.setName("iot-local");
        when(firmwareServiceMock.getBxApplications(any(),any())).thenReturn(list);
         kernelFirmwareService.updateSubSoftwarePubStatus(request, status);
    }

    @Test
    public void testQuerySuggestVersions() {
        List<String> result = kernelFirmwareService.querySuggestVersions("0.0.1", "model1", "iot-local");
        assertNotNull(result);
    }

    @Test
    public void testQueryBxActivatedVersions() {
        List<String> result = kernelFirmwareService.queryBxActivatedVersions();
        assertNotNull(result);
    }
}