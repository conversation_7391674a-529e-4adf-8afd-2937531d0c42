package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.device.DeviceKissFlagDO;
import com.addx.iotcamera.bean.device_msg.DeviceMsgSrc;
import com.addx.iotcamera.dao.device.DeviceKissFlagDAO;
import com.addx.iotcamera.util.OpenApiUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.addx.iotcamera.service.device_msg.DeviceMsgSrcManager.REDIS_KEY_IS_WS_DEVICE_MSG;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceKissFlagServiceTest {

    @Mock
    private DeviceKissFlagDAO deviceKissFlagDAO;
    @Mock
    private RedisService redisService;
    @InjectMocks
    private DeviceKissFlagService deviceKissFlagService;

    @Test
    public void test_save() {
        when(deviceKissFlagDAO.save(any())).thenReturn(1);

        Assert.assertEquals(0, deviceKissFlagService.save(null));
        Assert.assertEquals(0, deviceKissFlagService.save(new DeviceKissFlagDO()));
        Assert.assertEquals(1, deviceKissFlagService.save(new DeviceKissFlagDO().setSn("sn1")));
    }

    @Test
    public void test_queryBySn() {
        when(deviceKissFlagDAO.save(any())).thenReturn(1);
        when(redisService.delete(anyString())).thenReturn(true);
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceKissFlagDAO.queryBySn(sn)).thenReturn(null);
            when(redisService.get(REDIS_KEY_IS_WS_DEVICE_MSG.replace("{sn}", sn))).thenReturn(null);

            DeviceKissFlagDO expect = new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(false).setDeviceIsWsKeepAlive(false).setAppSupportUnlimitedWebsocket(false);
            DeviceKissFlagDO actual = deviceKissFlagService.queryBySn(sn);
            Assert.assertEquals(expect, actual);
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceKissFlagDAO.queryBySn(sn)).thenReturn(null);
            when(redisService.get(REDIS_KEY_IS_WS_DEVICE_MSG.replace("{sn}", sn))).thenReturn("any_value");

            DeviceKissFlagDO expect = new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(false).setDeviceIsWsKeepAlive(false).setAppSupportUnlimitedWebsocket(false);
            DeviceKissFlagDO actual = deviceKissFlagService.queryBySn(sn);
            Assert.assertEquals(expect, actual);
        }
        for (DeviceMsgSrc deviceMsgSrc : DeviceMsgSrc.values()) {
            String sn = OpenApiUtil.shortUUID();
            when(deviceKissFlagDAO.queryBySn(sn)).thenReturn(null);
            when(redisService.get(REDIS_KEY_IS_WS_DEVICE_MSG.replace("{sn}", sn))).thenReturn(deviceMsgSrc.name());

            DeviceKissFlagDO expect = new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(deviceMsgSrc.isWsReplaceMqtt())
                    .setDeviceIsWsKeepAlive(deviceMsgSrc.isWsReplaceMqtt()).setAppSupportUnlimitedWebsocket(false);
            DeviceKissFlagDO actual = deviceKissFlagService.queryBySn(sn);
            Assert.assertEquals(expect, actual);
        }
        for (DeviceMsgSrc deviceMsgSrc : DeviceMsgSrc.values()) {
            String sn = OpenApiUtil.shortUUID();
            when(deviceKissFlagDAO.queryBySn(sn)).thenReturn(new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(null).setDeviceIsWsKeepAlive(null).setAppSupportUnlimitedWebsocket(true));
            when(redisService.get(REDIS_KEY_IS_WS_DEVICE_MSG.replace("{sn}", sn))).thenReturn(deviceMsgSrc.name());

            DeviceKissFlagDO expect = new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(deviceMsgSrc.isWsReplaceMqtt())
                    .setDeviceIsWsKeepAlive(deviceMsgSrc.isWsReplaceMqtt()).setAppSupportUnlimitedWebsocket(true);
            DeviceKissFlagDO actual = deviceKissFlagService.queryBySn(sn);
            Assert.assertEquals(expect, actual);
        }
        for (DeviceMsgSrc deviceMsgSrc : DeviceMsgSrc.values()) {
            String sn = OpenApiUtil.shortUUID();
            when(deviceKissFlagDAO.queryBySn(sn)).thenThrow(new RuntimeException("mock_sql_error!"));
            when(redisService.get(REDIS_KEY_IS_WS_DEVICE_MSG.replace("{sn}", sn))).thenReturn(deviceMsgSrc.name());

            DeviceKissFlagDO expect = new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(deviceMsgSrc.isWsReplaceMqtt())
                    .setDeviceIsWsKeepAlive(deviceMsgSrc.isWsReplaceMqtt()).setAppSupportUnlimitedWebsocket(false);
            DeviceKissFlagDO actual = deviceKissFlagService.queryBySn(sn);
            Assert.assertEquals(expect, actual);
        }
        {
            String sn = OpenApiUtil.shortUUID();

            when(deviceKissFlagDAO.queryBySn(sn)).thenReturn(new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(true).setDeviceIsWsKeepAlive(true).setAppSupportUnlimitedWebsocket(false));
            when(redisService.get(REDIS_KEY_IS_WS_DEVICE_MSG.replace("{sn}", sn))).thenReturn(null);

            DeviceKissFlagDO expect = new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(true).setDeviceIsWsKeepAlive(true).setAppSupportUnlimitedWebsocket(false);
            DeviceKissFlagDO actual = deviceKissFlagService.queryBySn(sn);
            Assert.assertEquals(expect, actual);
        }
        {
            String sn = OpenApiUtil.shortUUID();

            when(deviceKissFlagDAO.queryBySn(sn)).thenReturn(new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(true).setDeviceIsWsKeepAlive(false).setAppSupportUnlimitedWebsocket(false));
            when(redisService.get(REDIS_KEY_IS_WS_DEVICE_MSG.replace("{sn}", sn))).thenReturn(null);

            DeviceKissFlagDO expect = new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(true).setDeviceIsWsKeepAlive(false).setAppSupportUnlimitedWebsocket(false);
            DeviceKissFlagDO actual = deviceKissFlagService.queryBySn(sn);
            Assert.assertEquals(expect, actual);
        }

    }

}
