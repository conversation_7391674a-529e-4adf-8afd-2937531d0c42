package com.addx.iotcamera.service.deviceplatform.alexa;

import com.addx.iotcamera.bean.app.device.DeviceConfigRequest;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.config.AlexaWhiteConfig;
import com.addx.iotcamera.service.DeviceConfigService;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.device.DeviceServiceTest;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.addx.iotcamera.service.deviceplatform.alexa.safemo.AlexaSafemoRequestService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.util.RedisUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class AlexaServiceTest {

    @Mock
    private AlexaWhiteConfig mockAlexaWhiteSnConfig;
    @Mock
    private JdbcTemplate mockJdbcTemplate;
    @Mock
    private RedisUtil mockRedisUtil;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private DeviceInfoService mockDeviceInfoService;
    @Mock
    private DeviceConfigService mockDeviceConfigService;
    @Mock
    private DeviceStatusService mockDeviceStatusService;
    @Mock
    private FactoryDataQueryService mockFactoryDataQueryService;
    @Mock
    private AlexaSafemoRequestService alexaSafemoRequestService;
    @Mock
    private UserService userService;

    @InjectMocks
    private AlexaService alexaServiceUnderTest;

    @Test
    public void testCanAccessAlexa() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final DeviceDO deviceDO = DeviceServiceTest.createDeviceDO(deviceModel);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(0)
                .supportAlexaWebrtc(0)
                .build());
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();
        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("serialNumber", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        boolean result = alexaServiceUnderTest.canAccessAlexa(deviceDO);
        assertTrue(!result);

        deviceManufactureTableDO.setSupportAlexa(0);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(0)
                .supportAlexaWebrtc(1)
                .build());
        result = alexaServiceUnderTest.canAccessAlexa(deviceDO);
        assertTrue(!result);

        deviceManufactureTableDO.setSupportAlexa(1);
        result = alexaServiceUnderTest.canAccessAlexa(deviceDO);
        assertTrue(result);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("serialNumber")));
        result = alexaServiceUnderTest.canAccessAlexa(deviceDO);
        assertTrue(result);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("userSn")));
        result = alexaServiceUnderTest.canAccessAlexa(deviceDO);
        assertTrue(result);
    }

    @Test
    public void testCanAccessAlexa_AlexaWhiteSnConfigReturnsNoItems() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final DeviceDO deviceDO = DeviceServiceTest.createDeviceDO(deviceModel);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("sn", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = alexaServiceUnderTest.canAccessAlexa(deviceDO);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanAccessAlexaUseCache() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final DeviceDO deviceDO = DeviceServiceTest.createDeviceDO(deviceModel);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(0)
                .supportAlexaWebrtc(0)
                .build());
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("serialNumber", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = alexaServiceUnderTest.canAccessAlexaUseCache(deviceDO);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanAccessAlexaUseCache_AlexaWhiteSnConfigReturnsNoItems() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final DeviceDO deviceDO = DeviceServiceTest.createDeviceDO(deviceModel);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(0)
                .supportAlexaWebrtc(0)
                .build());
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("serialNumber", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = alexaServiceUnderTest.canAccessAlexaUseCache(deviceDO);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanFirmwareSupport() {
        // Setup
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final DeviceDO deviceDO = DeviceServiceTest.createDeviceDO(deviceModel);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(0)
                .supportAlexaWebrtc(0)
                .build());
        boolean result = alexaServiceUnderTest.canFirmwareSupport(deviceDO);
        assertTrue(!result);

        deviceDO.setDeviceSupport(null);
        result = alexaServiceUnderTest.canFirmwareSupport(deviceDO);
        assertTrue(!result);
    }

    @Test
    public void testCanSnSupport() {
        // Setup
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("serialNumber", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = alexaServiceUnderTest.canSnSupport("sn", "userSn");

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanSnSupport_AlexaWhiteSnConfigReturnsNoItems() {
        // Setup
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("serialNumber", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = alexaServiceUnderTest.canSnSupport("sn", "userSn");

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testCanSnSupportUseCache() {
        // Setup
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("sn", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        deviceManufactureTableDO.setSupportAlexa(0);
        boolean result = alexaServiceUnderTest.canSnSupportUseCache("sn", "userSn");
        assertTrue(!result);

        deviceManufactureTableDO.setSupportAlexa(1);
        result = alexaServiceUnderTest.canSnSupportUseCache("sn", "userSn");
        assertTrue(result);

        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("sn", "userSn")).thenReturn(null);
        result = alexaServiceUnderTest.canSnSupportUseCache("sn", "userSn");
        assertTrue(!result);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("sn")));
        result = alexaServiceUnderTest.canSnSupportUseCache("sn", "userSn");
        assertTrue(result);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("userSn")));
        result = alexaServiceUnderTest.canSnSupportUseCache("sn", "userSn");
        assertTrue(result);

        when(mockRedisService.containsKey(anyString())).thenReturn(true);
        when(mockRedisService.get(anyString())).thenReturn("0");
        result = alexaServiceUnderTest.canSnSupportUseCache("sn", "userSn");
        assertTrue(!result);

        when(mockRedisService.containsKey(anyString())).thenReturn(true);
        when(mockRedisService.get(anyString())).thenReturn("1");
        result = alexaServiceUnderTest.canSnSupportUseCache("sn", "userSn");
        assertTrue(result);
    }

    @Test
    public void testCanSnSupportUseCache_AlexaWhiteSnConfigReturnsNoItems() {
        // Setup
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("serialNumber", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = alexaServiceUnderTest.canSnSupportUseCache("sn", "userSn");

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testIsUserLinked() {
        // Setup
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockJdbcTemplate.queryForList(any(), any(Object[].class), eq(String.class))).thenReturn(Arrays.asList(JSON.toJSONString(new JSONObject() {{
            put(AlexaService.KEY_AMAZON_TOKEN, new JSONObject().fluentPut(AlexaService.KEY_EXPIRES, System.currentTimeMillis() + 10000).fluentPut(AlexaService.KEY_ACCESS_TOKEN, "access_token"));
            put(AlexaService.KEY_EVENT_GATEWAY_ENDPOINT, "http://");
        }})));
        when(mockRedisService.setIfAbsent("key", "value", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);
        when(mockJdbcTemplate.getDataSource()).thenReturn(null);
        when(mockJdbcTemplate.update(eq("sql"), any(Object.class))).thenReturn(0);
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        when(mockAlexaWhiteSnConfig.getWhiteTenantIdSet()).thenReturn(Collections.singleton("vicoo"));

        // Run the test
        boolean result = alexaServiceUnderTest.isUserLinked(null,0);
        assertTrue(result);

        result = alexaServiceUnderTest.isUserLinked("vicoo", 0);
        // Verify the results
        assertTrue(result);

        result = alexaServiceUnderTest.isUserLinked("guard", 0);
        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testIsUserLinked_JdbcTemplateQueryForListReturnsNoItems() {
        // Setup
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Collections.emptyList());
        when(mockRedisService.setIfAbsent("key", "value", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);
        when(mockJdbcTemplate.getDataSource()).thenReturn(null);
        when(mockJdbcTemplate.update(eq("sql"), any(Object.class))).thenReturn(0);
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = alexaServiceUnderTest.isUserLinked(null,0);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testIsUserLinked_JdbcTemplateGetDataSourceReturnsNull() {
        // Setup
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Arrays.asList("value"));
        when(mockRedisService.setIfAbsent("key", "value", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);
        when(mockJdbcTemplate.getDataSource()).thenReturn(null);
        when(mockJdbcTemplate.update(eq("sql"), any(Object.class))).thenReturn(0);
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = alexaServiceUnderTest.isUserLinked(null,0);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testIsUserLinkedUseCache() {
        // Setup
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockJdbcTemplate.queryForList(any(), any(Object[].class), eq(String.class))).thenReturn(Arrays.asList(JSON.toJSONString(new JSONObject() {{
            put(AlexaService.KEY_AMAZON_TOKEN, new JSONObject().fluentPut(AlexaService.KEY_EXPIRES, System.currentTimeMillis() + 10000).fluentPut(AlexaService.KEY_ACCESS_TOKEN, "access_token"));
            put(AlexaService.KEY_EVENT_GATEWAY_ENDPOINT, "http://");
        }})));
        when(mockRedisService.setIfAbsent("key", "value", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);
        when(mockJdbcTemplate.getDataSource()).thenReturn(null);
        when(mockJdbcTemplate.update(eq("sql"), any(Object.class))).thenReturn(0);
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        when(mockAlexaWhiteSnConfig.getWhiteTenantIdSet()).thenReturn(Collections.singleton("vicoo"));

        boolean result = alexaServiceUnderTest.isUserLinkedUseCache(null, 0);
        assertTrue(result);

        // Run the test
        result = alexaServiceUnderTest.isUserLinkedUseCache(null,0);
        // Verify the results
        assertTrue(result);

        result = alexaServiceUnderTest.isUserLinkedUseCache("vicoo", 0);
        // Verify the results
        assertTrue(result);

        result = alexaServiceUnderTest.isUserLinkedUseCache("guard", 0);
        // Verify the results
        assertTrue(!result);

        when(mockRedisService.containsKey(anyString())).thenReturn(true);
        when(mockRedisService.get(anyString())).thenReturn("0");
        result = alexaServiceUnderTest.canSnSupportUseCache("sn", "userSn");
        assertTrue(!result);

        when(mockRedisService.containsKey(anyString())).thenReturn(true);
        when(mockRedisService.get(anyString())).thenReturn("1");
        result = alexaServiceUnderTest.canSnSupportUseCache("sn", "userSn");
        assertTrue(result);
    }

    @Test
    public void testIsUserLinkedUseCache_JdbcTemplateQueryForListReturnsNoItems() {
        // Setup
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Collections.emptyList());
        when(mockRedisService.setIfAbsent("key", "value", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);
        when(mockJdbcTemplate.getDataSource()).thenReturn(null);
        when(mockJdbcTemplate.update(eq("sql"), any(Object.class))).thenReturn(0);
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = alexaServiceUnderTest.isUserLinkedUseCache(null,0);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testIsUserLinkedUseCache_JdbcTemplateGetDataSourceReturnsNull() {
        // Setup
        when(mockRedisService.containsKey("key")).thenReturn(false);
        when(mockRedisService.get("key")).thenReturn("result");
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Arrays.asList("value"));
        when(mockRedisService.setIfAbsent("key", "value", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);
        when(mockJdbcTemplate.getDataSource()).thenReturn(null);
        when(mockJdbcTemplate.update(eq("sql"), any(Object.class))).thenReturn(0);
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);

        // Run the test
        final boolean result = alexaServiceUnderTest.isUserLinkedUseCache(null,0);

        // Verify the results
        assertTrue(!result);
    }

    @Test
    public void testGetAmazonTokenAndEventGateway() {
        // Setup
        when(mockJdbcTemplate.queryForList(any(), any(Object[].class), eq(String.class))).thenReturn(Arrays.asList(JSON.toJSONString(new JSONObject() {{
            put(AlexaService.KEY_AMAZON_TOKEN, new JSONObject().fluentPut(AlexaService.KEY_EXPIRES, System.currentTimeMillis() + 10000).fluentPut(AlexaService.KEY_ACCESS_TOKEN, "access_token"));
            put(AlexaService.KEY_EVENT_GATEWAY_ENDPOINT, "http://");
        }})));
        when(mockRedisService.setIfAbsent("key", "value", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);
        when(mockJdbcTemplate.getDataSource()).thenReturn(null);
        when(mockJdbcTemplate.update(eq("sql"), any(Object.class))).thenReturn(0);

        // Run the test
        Map<String, Object> result = alexaServiceUnderTest.getAmazonTokenAndEventGateway("oauthClientId");
        assertTrue(result != null);

        when(mockJdbcTemplate.queryForList(any(), any(Object[].class), eq(String.class))).thenReturn(Arrays.asList(JSON.toJSONString(new JSONObject() {{
            put(AlexaService.KEY_AMAZON_TOKEN, new JSONObject().fluentPut(AlexaService.KEY_EXPIRES, System.currentTimeMillis() - 10000).fluentPut(AlexaService.KEY_TOKEN_SERVER, "httpa://amazonToken"));
            put(AlexaService.KEY_EVENT_GATEWAY_ENDPOINT, "http://");
        }})));

        when(mockRedisService.setIfAbsent(any(), any(), anyLong(), any())).thenReturn(true);
        result = alexaServiceUnderTest.getAmazonTokenAndEventGateway("oauthClientId");
        assertTrue(result == null);
    }

    @Test
    public void testGetAmazonTokenAndEventGateway_JdbcTemplateQueryForListReturnsNoItems() {
        // Setup
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Collections.emptyList());
        when(mockRedisService.setIfAbsent("key", "value", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);
        when(mockJdbcTemplate.getDataSource()).thenReturn(null);
        when(mockJdbcTemplate.update(eq("sql"), any(Object.class))).thenReturn(0);

        // Run the test
        final Map<String, Object> result = alexaServiceUnderTest.getAmazonTokenAndEventGateway("oauthClientId");
        assertTrue(result == null);

        // Verify the results
        verify(mockJdbcTemplate).queryForList(eq("SELECT additional_information FROM `oauth2`.`oauth_client_details` WHERE `client_id`=?"), eq(new String[]{"oauthClientId"}), eq(String.class));
    }

    @Test
    public void testGetAmazonTokenAndEventGateway_JdbcTemplateGetDataSourceReturnsNull() {
        // Setup
        when(mockJdbcTemplate.queryForList(eq("sql"), any(Object[].class), eq(String.class))).thenReturn(Arrays.asList("value"));
        when(mockRedisService.setIfAbsent("key", "value", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);
        when(mockJdbcTemplate.getDataSource()).thenReturn(null);
        when(mockJdbcTemplate.update(eq("sql"), any(Object.class))).thenReturn(0);

        // Run the test
        final Map<String, Object> result = alexaServiceUnderTest.getAmazonTokenAndEventGateway("oauthClientId");
        assertTrue(result == null);

        // Verify the results
        verify(mockJdbcTemplate).queryForList(eq("SELECT additional_information FROM `oauth2`.`oauth_client_details` WHERE `client_id`=?"), eq(new String[]{"oauthClientId"}), eq(String.class));
    }

    @Test
    public void testAccountLinkSuccess() {
        // Setup
        // Configure DeviceInfoService.listDevicesByUserId(...).
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());

        when(mockDeviceInfoService.listDevicesByUserId(0)).thenReturn(deviceDOList);

        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("serialNumber", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);
        when(mockDeviceConfigService.updateDefaultCodec(new DeviceConfigRequest())).thenReturn(new Result<>("data"));

        // Configure DeviceStatusService.queryDeviceStatusBySerialNumber(...).
        final DeviceStatusDO deviceStatusDO = DeviceServiceTest.createDeviceStatusDO();
        when(mockDeviceStatusService.queryDeviceStatusBySerialNumber("serialNumber")).thenReturn(deviceStatusDO);

        // Run the test
        alexaServiceUnderTest.accountLinkSuccess(0);

        // Verify the results
        verify(mockRedisService).set("user_linked_alexa_0", "1", 600);

        when(userService.queryTenantIdById(0)).thenReturn("safemo");
        alexaServiceUnderTest.accountLinkSuccess(0);
    }

    @Test
    public void testAccountLinkSuccess_DeviceInfoServiceReturnsNoItems() {
        // Setup
        when(mockDeviceInfoService.listDevicesByUserId(0)).thenReturn(Collections.emptyList());
        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("serialNumber", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);
        when(mockDeviceConfigService.updateDefaultCodec(new DeviceConfigRequest())).thenReturn(new Result<>("data"));

        // Configure DeviceStatusService.queryDeviceStatusBySerialNumber(...).
        final DeviceStatusDO deviceStatusDO = DeviceServiceTest.createDeviceStatusDO();
        when(mockDeviceStatusService.queryDeviceStatusBySerialNumber("serialNumber")).thenReturn(deviceStatusDO);

        // Run the test
        alexaServiceUnderTest.accountLinkSuccess(0);

        // Verify the results
        verify(mockRedisService).set("user_linked_alexa_0", "1", 600);
    }

    @Test
    public void testAccountLinkSuccess_AlexaWhiteSnConfigReturnsNoItems() {
        // Setup
        // Configure DeviceInfoService.listDevicesByUserId(...).
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        when(mockDeviceInfoService.listDevicesByUserId(0)).thenReturn(deviceDOList);

        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("serialNumber", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(Collections.emptySet());
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);
        when(mockDeviceConfigService.updateDefaultCodec(new DeviceConfigRequest())).thenReturn(new Result<>("data"));

        // Configure DeviceStatusService.queryDeviceStatusBySerialNumber(...).
        final DeviceStatusDO deviceStatusDO = DeviceServiceTest.createDeviceStatusDO();
        when(mockDeviceStatusService.queryDeviceStatusBySerialNumber("serialNumber")).thenReturn(deviceStatusDO);

        // Run the test
        alexaServiceUnderTest.accountLinkSuccess(0);

        // Verify the results
        verify(mockRedisService).set("user_linked_alexa_0", "1", 600);
    }

    @Test
    public void testAccountLinkSuccess_DeviceConfigServiceReturnsFailure() {
        // Setup
        // Configure DeviceInfoService.listDevicesByUserId(...).
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setModelName("modelName");
        deviceModel.setStreamProtocol("streamProtocol");
        deviceModel.setLiveApp("liveApp");
        deviceModel.setRecApp("recApp");
        deviceModel.setCanStandby(false);
        deviceModel.setKeepAliveProtocol("keepAliveProtocol");
        deviceModel.setCanRotate(false);
        deviceModel.setAudioCodectype("audioCodectype");
        deviceModel.setWhiteLight(false);
        deviceModel.setDevicePersonDetect(false);
        final List<DeviceDO> deviceDOList = Arrays.asList(DeviceServiceTest.createDeviceDO(deviceModel));
        deviceDOList.get(0).setDeviceSupport(CloudDeviceSupport.builder()
                .supportChangeCodec(1)
                .supportAlexaWebrtc(1)
                .build());
        when(mockDeviceInfoService.listDevicesByUserId(0)).thenReturn(deviceDOList);

        when(mockRedisUtil.tryLock("key")).thenReturn(0L);

        // Configure FactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(...).
        final DeviceManufactureTableDO deviceManufactureTableDO =
                DeviceManufactureTableDO.builder()
                        .userSn("userSn")
                        .batch( "batch")
                        .manufacturerId(0)
                        .workerId( 0)
                        .macAddress("macAddress")
                        .serialNumber( "serialNumber")
                        .encryptType( "encryptType")
                        .macAddress( "mcuNumber")
                        .certificate( "certificate")
                        .modelNo( "modelNo")
                        .firmwareId( "firmwareId")
                        .manufactureTime(0)
                        .activated( 0)
                        .activatedTime( 0)
                        .displayModelNo( "displayModelNo")
                        .registerModelNo( "registerModelNo")
                        .supportAlexa( 1)
                        .supportGoogleHome( 1)
                        .apInfo( "apInfo")
                        .customerId( "cuid")
                        .build();
        when(mockFactoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn("serialNumber", "userSn")).thenReturn(deviceManufactureTableDO);

        when(mockAlexaWhiteSnConfig.getWhiteSnSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtil.unlock("key", 0L)).thenReturn(false);
        when(mockDeviceConfigService.updateDefaultCodec(new DeviceConfigRequest())).thenReturn(Result.Exception(new Exception("message")));

        // Configure DeviceStatusService.queryDeviceStatusBySerialNumber(...).
        final DeviceStatusDO deviceStatusDO = DeviceServiceTest.createDeviceStatusDO();
        when(mockDeviceStatusService.queryDeviceStatusBySerialNumber("serialNumber")).thenReturn(deviceStatusDO);

        // Run the test
        alexaServiceUnderTest.accountLinkSuccess(0);

        // Verify the results
        verify(mockRedisService).set("user_linked_alexa_0", "1", 600);
    }
}
