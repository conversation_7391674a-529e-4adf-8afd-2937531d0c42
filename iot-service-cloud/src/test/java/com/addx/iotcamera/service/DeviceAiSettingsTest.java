package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.DeviceAiSettingsDO;
import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.device.UserDeviceSelection;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.config.device.ModelAiEventConfig;
import com.addx.iotcamera.controller.device.info.AiAssistController;
import com.addx.iotcamera.dao.DeviceAiSettingsDAO;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.enums.utils.AiObjectEnumUtil;
import org.addx.iot.domain.extension.ai.vo.AiTaskResult;
import org.addx.iot.domain.extension.entity.DeviceAiSwitch;
import org.addx.iot.domain.extension.entity.EventObjectSwitch;
import org.addx.iot.domain.extension.entity.EventObjectSwitchUpdate;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceAiSettingsTest {

    private TestHelper testHelper;
    private DeviceAiSettingsDAO realDeviceAiSettingsDAO;
    @InjectMocks
    private AiAssistController aiAssistController;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private AiAssistService aiAssistService;
    @Mock
    private DeviceAiSettingsService deviceAiSettingsService;
    @Mock
    private ModelAiEventConfig modelAiEventConfig;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private DeviceService deviceService;
    @Mock
    private DeviceAiSettingsDAO deviceAiSettingsDAO;
    @Mock
    private MessageNotificationSettingsService messageNotificationSettingsService;
    //    @Mock
//    private UserVipService userVipService;
    @Mock
    private VipService paasVipService;
    @Mock
    private DeviceModelEventService deviceModelEventService;

    private final Integer defaultUserId = 123;
    private final String defaultSn = "sn_123";
    private final String defaultSn2 = "sn_123456";
    private final String defaultModelNo = "CG1";

    @Before
    public void before() throws Exception {
        this.testHelper = TestHelper.getInstanceByEnv("test");
//        this.testHelper = TestHelper.getInstanceByEnv("local");
//        this.testHelper = TestHelper.getInstanceByEnv("staging");
        this.realDeviceAiSettingsDAO = testHelper.getMapper(DeviceAiSettingsDAO.class);

        Answer<Object> daoAnswer = AdditionalAnswers.delegatesTo(realDeviceAiSettingsDAO);
        when(deviceAiSettingsDAO.queryBySerialNumber(any(), anyString())).thenAnswer(daoAnswer);
        when(deviceAiSettingsDAO.save(any())).thenAnswer(daoAnswer);
        when(deviceAiSettingsDAO.updateEventObjectsSwitch(any(), anyString(), anyInt())).thenAnswer(daoAnswer);
        when(deviceAiSettingsDAO.updateEventObjectsSwitchPartly(any(), anyString(), anyInt(), anyInt())).thenAnswer(daoAnswer);

        Answer<Object> answer = AdditionalAnswers.delegatesTo(DeviceAiSettingsService.builder()
//                .messageNotificationSettingsService(messageNotificationSettingsService)
                .vipService(paasVipService)
                .deviceAiSettingsDAO(deviceAiSettingsDAO).build());
        when(deviceAiSettingsService.saveDeviceAiSettings(any(), anyString(), any())).thenAnswer(answer);
        when(deviceAiSettingsService.queryEnableEventObjects(any(), anyString())).thenAnswer(answer);
        when(deviceAiSettingsService.updateEnableEventObjectsPartly(any(), anyString(), any(), any())).thenAnswer(answer);
        when(deviceAiSettingsService.updateEnableEventObjects(any(), anyString(), any())).thenAnswer(answer);
        when(deviceAiSettingsService.initDeviceAiSettings(any(), anyString())).thenAnswer(answer);

        Answer<Object> answer2 = AdditionalAnswers.delegatesTo(AiAssistService.builder()
                .messageNotificationSettingsService(messageNotificationSettingsService)
                .deviceAiSettingsService(deviceAiSettingsService).modelAiEventConfig(modelAiEventConfig)
                .deviceService(deviceService).deviceManualService(deviceManualService).build());
        when(aiAssistService.queryEventObjectSwitch(any(), anyString())).thenAnswer(answer2);
        when(aiAssistService.updateEventObjectSwitch(any(), anyString(), anyList())).thenAnswer(answer2);

        when(deviceManualService.getModelNoBySerialNumber(anyString())).thenReturn(defaultModelNo);
        when(userRoleService.getDeviceAdminUser(anyString())).thenReturn(defaultUserId);
        Set<String> eventObjectNames = ImmutableSet.of("person", "pet", "vehicle", "package");
        when(deviceModelEventService.queryDeviceModelEvent(anyString())).thenReturn(new LinkedHashSet<>(eventObjectNames));

        when(messageNotificationSettingsService.updateMessageNotificationSettings(any())).thenReturn(1);
        when(paasVipService.isVipDevice(any(), anyString())).thenReturn(true);
//        when(deviceVipService.isVipDevice(any())).thenReturn(true);
    }

    @After
    public void after() {
//        testHelper.commitAndClose();
        testHelper.commit();
    }

    @Test
    public void test_eventObjects_allCombine() {
        FuncUtil.foreachCombine(AiObjectEnum.values(), eventObjects -> {
            int bits = AiObjectEnumUtil.bitsOfEventObjects(eventObjects);
            Set<AiObjectEnum> eventObjectNames2 = AiObjectEnumUtil.eventObjectsOfBits(bits);
            System.out.printf("%02d,%s:%s ---- %s\n", bits, Integer.toBinaryString(bits), JSON.toJSONString(eventObjects), JSON.toJSONString(eventObjectNames2));
            Assert.assertEquals(new HashSet<>(eventObjects), eventObjectNames2);
        });
    }

    //    @Test
    public void test_deviceAiSettingsService_save_query_update() {
        AtomicInteger count = new AtomicInteger(0);
        FuncUtil.foreachCombine(AiObjectEnum.values(), eventObjects -> {
            String serialNumber = String.format("sn_unit_test_%03d", count.getAndIncrement());
            deviceAiSettingsService.saveDeviceAiSettings(defaultUserId, serialNumber, eventObjects);
//            Assert.assertTrue("save=" + save, save == 1 || save == 2);
            Set<AiObjectEnum> eventObjects2 = deviceAiSettingsService.queryEnableEventObjects(defaultUserId, serialNumber);
            Assert.assertEquals(new HashSet<>(eventObjects), eventObjects2);
        });
        FuncUtil.foreachCombine(AiObjectEnum.values(), eventObjects -> {
            String serialNumber = String.format("sn_unit_test_%03d", count.decrementAndGet());
            deviceAiSettingsService.updateEnableEventObjects(defaultUserId, serialNumber, eventObjects);
            Set<AiObjectEnum> eventObjects2 = deviceAiSettingsService.queryEnableEventObjects(defaultUserId, serialNumber);
            Assert.assertEquals(new HashSet<>(eventObjects), eventObjects2);
        });
    }

    //    @Test
    public void test_queryEventObjectSwitch() {
        ImmutableSet<AiObjectEnum> eventObjects = ImmutableSet.of(AiObjectEnum.PERSON, AiObjectEnum.VEHICLE);
        ImmutableSet<AiObjectEnum> eventObjects2 = ImmutableSet.of(AiObjectEnum.PERSON, AiObjectEnum.PET);
        when(deviceAiSettingsService.queryEnableEventObjects(defaultUserId, defaultSn)).thenReturn(eventObjects);
        when(deviceAiSettingsService.queryEnableEventObjects(defaultUserId, defaultSn2)).thenReturn(eventObjects2);
        when(userRoleService.getUserRoleByUserId(anyInt(), anyInt())).thenReturn(Arrays.asList(
                UserRoleDO.builder().userId(defaultUserId).serialNumber(defaultSn).build(),
                UserRoleDO.builder().userId(defaultUserId).serialNumber(defaultSn2).build()
        ));
        when(deviceService.getAllDeviceInfo(defaultSn)).thenReturn(DeviceDO.builder().deviceName("我家的摄像机").build());
        when(deviceService.getAllDeviceInfo(defaultSn2)).thenReturn(DeviceDO.builder().deviceName("摄像机2").build());
        UserDeviceSelection input = UserDeviceSelection.builder().isAll(true).build();
        Result<List<DeviceAiSwitch>> result = aiAssistController.queryEventObjectSwitch(defaultUserId, input);
        log.info("test_queryEventObjectSwitch responseBody={}", JSON.toJSONString(result, true));
        Assert.assertEquals(Result.successFlag, result.getResult());

        UserDeviceSelection input2 = UserDeviceSelection.builder().isAll(false).serialNumbers(Arrays.asList(defaultSn, defaultSn2)).build();
        Result<List<DeviceAiSwitch>> result2 = aiAssistController.queryEventObjectSwitch(defaultUserId, input2);
        Assert.assertEquals(JSON.toJSON(result.getData()), JSON.toJSON(result2.getData()));
    }

    //    @Test
    public void test_updateEventObjectsSwitch() throws Exception {
        ImmutableSet<AiObjectEnum> eventObjects = ImmutableSet.of(AiObjectEnum.PERSON, AiObjectEnum.VEHICLE);
        deviceAiSettingsService.saveDeviceAiSettings(defaultUserId, defaultSn, eventObjects);
        Set<AiObjectEnum> eventObjects2 = deviceAiSettingsService.queryEnableEventObjects(defaultUserId, defaultSn);
        Assert.assertEquals(eventObjects, eventObjects2);

        MessageNotificationSetting notifySetting = MessageNotificationSetting.builder()
                .eventObjects(eventObjects.stream().map(AiObjectEnum::getName).collect(Collectors.joining(",")))
                .packageEventType("vehicle_enter,vehicle_held_up").enableOther(0)
                .build();
        when(messageNotificationSettingsService.queryMessageNotificationSetting(anyString(), any())).thenReturn(notifySetting);

        EventObjectSwitchUpdate update = new EventObjectSwitchUpdate().setSerialNumber(defaultSn)
                .setList(ImmutableList.<EventObjectSwitch>builder()
                        .add(new EventObjectSwitch().setEventObject("person").setChecked(true))
                        .add(new EventObjectSwitch().setEventObject("pet").setChecked(true))
                        .add(new EventObjectSwitch().setEventObject("vehicle").setChecked(false))
                        .add(new EventObjectSwitch().setEventObject("package").setChecked(true))
                        .build());
        log.info("test_queryEventObjectSwitch requestBody={}", JSON.toJSONString(update, true));
        Result result = aiAssistController.updateEventObjectSwitch(defaultUserId, update);
        Assert.assertEquals(Result.successFlag, result.getResult());

        Set<AiObjectEnum> eventObjects3 = deviceAiSettingsService.queryEnableEventObjects(defaultUserId, defaultSn);
        ImmutableSet<AiObjectEnum> expectEventObjects = ImmutableSet.of(AiObjectEnum.PERSON, AiObjectEnum.PET, AiObjectEnum.PACKAGE);
        Assert.assertEquals(expectEventObjects, eventObjects3);

    }

    //    @Test
    public void test_initDeviceAiSettings() {
        DeviceAiSettingsDO model = deviceAiSettingsService.initDeviceAiSettings(999, "sn_999");
        Assert.assertEquals(DeviceAiSettingsService.INIT_ENABLE_EVENT_OBJECTS, AiObjectEnumUtil.eventObjectsOfBits(model.getEventObjectsSwitch()));
    }

    //    @Test
    public void test_updateEventObjectsSwitchPartly() {
        // updateEnableEventObjectsPartly userId=794,sn=403dacb875ea5df9284f3d0455196e5f,enableList=[PACKAGE, PET, PERSON],disableList=[VEHICLE]
        Integer userId = 794;
        String sn = "403dacb875ea5df9284f3d0455196e5f";
        int enableBits = AiObjectEnumUtil.bitsOfEventObjects(Arrays.asList(AiObjectEnum.PACKAGE, AiObjectEnum.PET, AiObjectEnum.PERSON));
        int disableBits = AiObjectEnumUtil.bitsOfEventObjects(Arrays.asList(AiObjectEnum.VEHICLE));
        deviceAiSettingsDAO.updateEventObjectsSwitchPartly(userId, sn, enableBits, disableBits);
    }

    //    @Test
    public void test_queryEventObjectSwitch2() {
        Result<DeviceAiSwitch> result = aiAssistService.queryEventObjectSwitch(933, "412e6b9f9badc7dbca0ce94e3b81b09a");
        Assert.assertTrue(true);
    }

    @Test
    public void test() {
        String json = "{\"serialNumber\":\"412e6b9f9badc7dbca0ce94e3b81b09a\",\"traceId\":\"0QdZ0yST036I0FHK0ICFkwlpfIKO5\",\"order\":1,\"imageUrl\":\"https://addx-staging.s3.cn-north-1.amazonaws.com.cn/app_push_img/0QdZ0yST036I0FHK0ICFkwlpfIKO5_notify_vehicle.jpg\",\"events\":[{\"eventObject\":\"VEHICLE\",\"eventType\":\"EXIST\",\"ids\":[],\"occur_location\":\"0,0\",\"occur_frame\":\"0\",\"activatedZones\":[]}]}";
        AiTaskResult aiTaskResult = JSON.parseObject(json, AiTaskResult.class);
        String eventObjectName = aiTaskResult.getEvents().get(0).getEventObject().getName();
        String str = "package_drop_off,package_exist,package_pick_up,vehicle_enter,vehicle_held_up,vehicle_out";
        Set<String> enableEventTypes = TextUtil.splitToNotBlankSet(str, ',');
        boolean isVehicleHeldUp = AiObjectEnum.VEHICLE.getObjectName().equals(eventObjectName)
                && enableEventTypes.contains(AiObjectActionEnum.VEHICLE_HELD_UP.getEventTypeName());
        boolean isPackageExist = AiObjectEnum.PACKAGE.getObjectName().equals(eventObjectName)
                && enableEventTypes.contains(AiObjectActionEnum.PACKAGE_EXIST.getEventTypeName());
        log.info("isVehicleHeldUp={},isPackageExist={}", isVehicleHeldUp, isPackageExist);
    }
}
