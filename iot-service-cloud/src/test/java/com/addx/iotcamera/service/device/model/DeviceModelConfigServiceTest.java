package com.addx.iotcamera.service.device.model;

import com.addx.iotcamera.bean.device.DeviceModelIssuedFileDO;
import com.addx.iotcamera.bean.device.FileIssuedSaveVO;
import com.addx.iotcamera.bean.device.FileIssuedVO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.dao.model.DeviceModelIssuedFileDAO;
import com.addx.iotcamera.dao.model.IDeviceModelConfigDAO;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceModelConfigServiceTest {

    @InjectMocks
    @Spy
    private DeviceModelConfigService deviceModelConfigService;

    @Mock
    private IDeviceModelConfigDAO iDeviceModelConfigDAO;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private DeviceModelIssuedFileDAO deviceModelIssuedFileDAO;

    @Test
    public void test() {
        Mockito.when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("cg1");
        Mockito.when(iDeviceModelConfigDAO.queryDeviceModelConfig(any())).thenReturn(new DeviceModel());
        Mockito.when(deviceInfoService.getDeviceSupport(any())).thenReturn(CloudDeviceSupport.builder()
                .supportStreamProtocol("h264")
                .supportAudioCodectype("mp3")
                .supportKeepAliveProtocol("tcp")
                .supportCanStandby(1)
                .supportDevicePersonDetect(1)
                .build());

        DeviceModel deviceModel = deviceModelConfigService.queryDeviceModelConfig("sn_01");
        Assert.assertTrue(deviceModel != null);

        deviceModelConfigService.saveDeviceModelConfig(null);
    }

    @Test
    public void test_saveDeviceModelIssuedFiles() {
        FileIssuedSaveVO saveVO = new FileIssuedSaveVO().setModelNo("modelNo").setIssuedFiles(Arrays.asList());
        deviceModelConfigService.saveDeviceModelIssuedFiles(saveVO);
    }

    @Test
    public void test_queryIssuedFilesByModelNo() {
        {
            List<FileIssuedVO> list = deviceModelConfigService.queryIssuedFilesByModelNo(null);
            Assert.assertEquals(Collections.emptyList(), list);
        }
        {
            List<FileIssuedVO> list = deviceModelConfigService.queryIssuedFilesByModelNo("");
            Assert.assertEquals(Collections.emptyList(), list);
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelIssuedFileDAO.queryByModelNo(modelNo)).thenReturn(null);
            List<FileIssuedVO> list = deviceModelConfigService.queryIssuedFilesByModelNo(modelNo);
            Assert.assertEquals(Collections.emptyList(), list);
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            String filesStr = "[{\"fileId\":\"3kW8fPy990so4U9FSYdCD2\",\"fileTypeCode\":\"iqFile\",\"url\":\"https://addx-device-config.s3.amazonaws.com/fileCenter/iqFile/3kW8fPy990so4U9FSYdCD2-V5\",\"version\":\"V5\"}]";
            when(deviceModelIssuedFileDAO.queryByModelNo(modelNo)).thenReturn(new DeviceModelIssuedFileDO().setModelNo(modelNo).setIssuedFiles(filesStr));
            List<FileIssuedVO> list = deviceModelConfigService.queryIssuedFilesByModelNo(modelNo);
            Assert.assertEquals(JSON.parseArray(filesStr, FileIssuedVO.class), list);
        }
    }

}
