package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.live.TwilioTokenResult;
import com.addx.iotcamera.kiss.bean.IceServer;
import com.addx.iotcamera.service.live.TwilioService;
import com.addx.iotcamera.util.HttpUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.apache.commons.io.IOUtils;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.io.ClassPathResource;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class TwilioTest {

    @Test
    public void test_parseTokens() throws Exception {
        InputStream is = new ClassPathResource("example/twilio_getTokens.json").getInputStream();
        String json = IOUtils.toString(is);
        TwilioTokenResult result = JSON.parseObject(json, TwilioTokenResult.class);
        Assert.assertNotNull(result);
    }

//    @Test
    public void test_getTokens() throws Exception {
        // $TWILIO_ACCOUNT_SID:$TWILIO_AUTH_TOKEN
        /*
        curl -X POST 'https://api.twilio.com/2010-04-01/Accounts/**********************************/Tokens.json \
    -u **********************************:ade6ebb9d889d241882bd3e571313975
         */
//        String TWILIO_ACCOUNT_SID = "**********************************";
//        String TWILIO_AUTH_TOKEN = "ade6ebb9d889d241882bd3e571313975";
        String TWILIO_ACCOUNT_SID = "**********************************";
        String TWILIO_AUTH_TOKEN = "bda34c151c9333bbe37a89918a2c5485";

        String urlPtn = "https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}/Tokens.json";

        String url = urlPtn.replace("${TWILIO_ACCOUNT_SID}", TWILIO_ACCOUNT_SID);

//        String twilioBaseAuth = "Basic " + Base64.getUrlEncoder().encodeToString((TWILIO_ACCOUNT_SID + ":" + TWILIO_AUTH_TOKEN).getBytes());
//        String respBody = HttpUtils.httpPostWithBaseAuth(url, twilioBaseAuth);
        Result <String> bodyResult = httpPostWithBaseAuth(url, TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);
        Assert.assertEquals(Integer.valueOf(0), bodyResult.getResult());
        TwilioTokenResult result = JSON.parseObject(bodyResult.getData(), TwilioTokenResult.class);
        Assert.assertNotNull(result);
        List<IceServer> iceServerList = TwilioService.getIceServerList(result.getIceServers());
        Assert.assertTrue(iceServerList.size() > 0);
    }

    private Result <String> httpPostWithBaseAuth(String url, String TWILIO_ACCOUNT_SID, String TWILIO_AUTH_TOKEN) {
        return HttpUtils.httpRequestWithBaseAuth(new HttpPost(url), TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);
    }

    //    @Test
    public void buildMqttMessageGt2000byte() {
        String json = "{\"time\":**********,\"statusList\":[{\"name\":\"deviceDormancySupport\",\"value\":1},{\"name\":\"killKeepAlive\",\"value\":1},{\"name\":\"supportAlarmVolume\",\"value\":1},{\"name\":\"supportVoiceVolume\",\"value\":1},{\"name\":\"supportRecLamp\",\"value\":1},{\"name\":\"deviceSupportMirrorFlip\",\"value\":0},{\"name\":\"antiflickerSupport\",\"value\":1},{\"name\":\"deviceSupportLanguage\",\"value\":\"cn,en,ja,de,ru,fr,it,es\"},{\"name\":\"supportWebrtc\",\"value\":1},{\"name\":\"quantityCharge\",\"value\":1},{\"name\":\"sdCardStatus\",\"value\":{\"formatStatus\":1,\"total\":0,\"free\":0}},{\"name\":\"gsensor\",\"value\":0},{\"name\":\"mapCapChangeCnt\",\"value\":0},{\"name\":\"mapCapacity\",\"value\":0},{\"name\":\"voltaTerminalVol\",\"value\":3000},{\"name\":\"voltameter\",\"value\":0},{\"name\":\"por\",\"value\":2},{\"name\":\"whiteLight\",\"value\":0},{\"name\":\"wifiRssi\",\"value\":-44},{\"name\":\"live\",\"value\":0},{\"name\":\"buildTime\",\"value\":\"2021-05-27-09-00-39\"},{\"name\":\"mcuVersion\",\"value\":\"0.0.55\"},{\"name\":\"displayGitSha\",\"value\":\"dc0585\"},{\"name\":\"version\",\"value\":\"1.0.0\"},{\"name\":\"ip\",\"value\":\"**************\"},{\"name\":\"MAC\",\"value\":\"ac:64:cf:e4:9b:fa\"},{\"name\":\"userSn\",\"value\":\"AIC9L8LPYPM0346\"},{\"name\":\"uid\",\"value\":\"d49d92e78fa7e3b2e60f07a1fcec79b3\"},{\"name\":\"originModel\",\"value\":\"CG1\"},{\"name\":\"deviceType\",\"value\":\"CG1\"},{\"name\":\"customModel\",\"value\":\"CG1\"},{\"name\":\"ap\",\"value\":\"addx\"},{\"name\":\"charge\",\"value\":2},{\"name\":\"batteryEvent\",\"value\":0},{\"name\":\"meterVoltage\",\"value\":4119},{\"name\":\"battery\",\"value\":100}]}";
        int num = (2000 / json.getBytes(StandardCharsets.UTF_8).length);
        JSONObject obj = JSON.parseObject(json);
        JSONObject obj2 = JSON.parseObject(json);
        for (int i = 0; i < num; i++) {
            for (String key : obj.keySet()) {
                obj2.put(key + "_" + i, obj.get(key));
            }
        }
        System.out.println(JSON.toJSONString(obj2, true));
    }
}
