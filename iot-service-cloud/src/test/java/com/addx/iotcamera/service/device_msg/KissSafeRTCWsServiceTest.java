package com.addx.iotcamera.service.device_msg;

import com.addx.iotcamera.bean.device_msg.*;
import com.addx.iotcamera.controller.safertc.SafeRTCBxController;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.kiss.KissNodeType;
import com.addx.iotcamera.kiss.node.KissNode;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class KissSafeRTCWsServiceTest {

    @InjectMocks
    private KissWsService kissSafeRTCWsService;

    @Mock
    private KissDeviceNodeManager sn2KissDeviceNode;
    @Mock
    private KissWsService kissWsService;

    @Before
    public void before() {
//        kissWsService.setKissWebsocketPort(18888);
        kissSafeRTCWsService.setKissWsClientFactory(uri -> new KissWsClientMock(uri));
    }

    @Getter
    @Setter
    public static class KissWsClientMock extends KissWsClient {

        private boolean connectCalled = false;
        private boolean reconnectCalled = false;
        private boolean isClosed = false;

        public KissWsClientMock(URI serverUri) {
            super(serverUri);
        }

        @Override
        public void connect() {
            connectCalled = true;
        }

        @Override
        public void reconnect() {
            reconnectCalled = true;
        }

        private LinkedList<String> sendTextList = new LinkedList<>();

        @Override
        public void send(String text) {
            sendTextList.add(text);
        }
    }

    @Test
    public void test_msgNameOf() {
        final EReportEvent reportEvent = EReportEvent.DETECT_PIR_RESULT;

        Assert.assertEquals("detectPirResult", reportEvent.getMsgName());
        Assert.assertEquals(reportEvent, EReportEvent.msgNameOf("detectPirResult"));
    }

    //    @Test
    public void test_refreshKissWsClient1() {
        {
            Map<String, KissNode> ip2KissNode = new LinkedHashMap<>();
            {
                ip2KissNode.put("127.0.0.1", new KissNode() {{
                    setRemoving(0);
                }});
                ip2KissNode.put("*********", null);
                ip2KissNode.put("*********", new KissNode() {{
                    setRemoving(1);
                }});
            }
            kissSafeRTCWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kissSafeRtc, ip2KissNode);
            kissSafeRTCWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kissSafeRtc, ip2KissNode);
        }
        {
            Map<String, KissNode> ip2KissNode = new LinkedHashMap<>();
            {
                ip2KissNode.put("*********", new KissNode() {{
                    setRemoving(0);
                }});
                ip2KissNode.put("127.0.0.1", new KissNode() {{
                    setRemoving(1);
                }});
            }
            kissSafeRTCWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kissSafeRtc, ip2KissNode);
            kissSafeRTCWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kissSafeRtc, ip2KissNode);
        }

    }

    @Test
    public void test_refreshKissWsClient() {
        {
            Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
            kissSafeRTCWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kissSafeRtc, kissNodeMap);
            final Map<String, KissWsClient> ip2KissWsClient = kissSafeRTCWsService.getIp2KissWsClient().getMap();
            Assert.assertEquals(0, ip2KissWsClient.size());
        }
        String innerApiAddr = "***********";
        {
            Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
            kissNodeMap.put(innerApiAddr, new KissNode() {{
                setRemoving(0);
                setInnerApiAddr(innerApiAddr);
            }});
            kissSafeRTCWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kissSafeRtc, kissNodeMap);
            final Map<String, KissWsClient> ip2KissWsClient = kissSafeRTCWsService.getIp2KissWsClient().getMap();
            Assert.assertEquals(1, ip2KissWsClient.size());
            final KissWsClientMock kissWsClient = (KissWsClientMock) ip2KissWsClient.get(innerApiAddr);
            Assert.assertNotNull(kissWsClient);
            Assert.assertFalse(kissWsClient.isKissNodeRemoving());

            Assert.assertTrue(kissWsClient.isConnectCalled());
            Assert.assertFalse(kissWsClient.isReconnectCalled());
            kissWsClient.setClosed(true);
        }
        {
            Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
            kissNodeMap.put(innerApiAddr, new KissNode() {{
                setRemoving(0);
                setInnerApiAddr(innerApiAddr);
            }});
            kissSafeRTCWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kissSafeRtc, kissNodeMap);
            final Map<String, KissWsClient> ip2KissWsClient = kissSafeRTCWsService.getIp2KissWsClient().getMap();
            Assert.assertEquals(1, ip2KissWsClient.size());
            final KissWsClientMock kissWsClient = (KissWsClientMock) ip2KissWsClient.get(innerApiAddr);
            Assert.assertNotNull(kissWsClient);
            Assert.assertFalse(kissWsClient.isKissNodeRemoving());

            Assert.assertTrue(kissWsClient.isConnectCalled());
            Assert.assertTrue(kissWsClient.isReconnectCalled());
            kissWsClient.setClosed(false);
        }
        {
            Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
            kissNodeMap.put(innerApiAddr, new KissNode() {{
                setRemoving(1);
                setInnerApiAddr(innerApiAddr);
            }});
            kissSafeRTCWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kissSafeRtc, kissNodeMap);
            final Map<String, KissWsClient> ip2KissWsClient = kissSafeRTCWsService.getIp2KissWsClient().getMap();
            Assert.assertEquals(1, ip2KissWsClient.size());
            final KissWsClientMock kissWsClient = (KissWsClientMock) ip2KissWsClient.get(innerApiAddr);
            Assert.assertNotNull(kissWsClient);
            Assert.assertTrue(kissWsClient.isKissNodeRemoving());
        }
        {
            Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
            kissSafeRTCWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kissSafeRtc, kissNodeMap);
            final Map<String, KissWsClient> ip2KissWsClient = kissSafeRTCWsService.getIp2KissWsClient().getMap();
            Assert.assertEquals(0, ip2KissWsClient.size());
        }
    }

    @Test
    @SneakyThrows
    public void test_createKissWsClient() {
        final URI uri = URI.create("ws://**************/iot-service-channel/connect/1234");
        final KissWsClient kissWsClient = kissSafeRTCWsService.createKissWsClient(uri);
        Assert.assertNotNull(kissWsClient);
        kissWsClient.getKissDeviceNodesListener().accept(Arrays.asList(new KissDeviceNode()));
        kissWsClient.getRetainedMsgAckListener().accept(new DeviceMsgAck());
        kissWsClient.getCmdAckListener().accept(new DeviceMsgAck());

        {
            kissWsClient.getReqListener().accept(new IotWsReq<>().setName(IotWsReqName.deviceReportEvent.name())
                    .setValue(new JSONObject()));
        }
        {
            String json = "{\"id\":\"76fb19591a4e4b2398e892b9c31ace2b\",\"method\":\"IOT_WS_REQ\",\"name\":\"deviceReportEvent\",\"time\":1708920331463,\"value\":{\"serialNumber\":\"a7581545ba8b66b87b86b8055e4b270a\",\"name\":\"reportEvent\",\"time\":1708920331,\"id\":0,\"value\":{\"traceId\":\"03029381708920331f72e5ID44tTk\",\"event\":18}}}";
            IotWsReq iotWsReq = JSON.parseObject(json, IotWsReq.class);
            kissWsClient.getReqListener().accept(iotWsReq);
        }
        {
            final String clientId = OpenApiUtil.shortUUID();
            Map<String, String> clientId2CountryNo = new TreeMap<>();
            when(kissWsService.getCountryNoByClientIds(any())).thenReturn(clientId2CountryNo);
            kissWsClient.getReqListener().accept(new IotWsReq<>().setName(IotWsReqName.queryClientCountryNo.name())
                    .setValue(new JSONObject().fluentPut("clientIds", Arrays.asList(clientId))));

        }
        {
            final String clientId = OpenApiUtil.shortUUID();
            Map<String, String> clientId2CountryNo = new TreeMap<>();
            clientId2CountryNo.put(clientId, "CN");
            when(kissWsService.getCountryNoByClientIds(any())).thenReturn(clientId2CountryNo);
            kissWsClient.getReqListener().accept(new IotWsReq<>().setName(IotWsReqName.queryClientCountryNo.name())
                    .setValue(new JSONObject().fluentPut("clientIds", Arrays.asList(clientId))));
        }
        kissWsClient.getRespListener().accept(new IotWsResp<>().setName(IotWsReqName.queryClientCountryNo.name()));
    }

    @Test
    public void test_IotWsReq_IotWsResp() {
        IotWsReq<Object> req1 = new IotWsReq<>();
        IotWsReq<Object> req3 = new IotWsReq<>(IotWsReqName.queryClientCountryNo);
        Assert.assertNotNull(req3.toString());

        IotWsResp<Object> resp1 = new IotWsResp<>();
        IotWsResp<Object> resp3 = new IotWsResp<>(req3);
        Assert.assertNotNull(resp3.toString());
    }

    @Test
    public void test_getUserIdFromLinkToken() {
        {
            String linkToken = "1539369AICLQ7BZH1V00271706534677";
            Integer userId = SafeRTCBxController.getUserIdFromLinkToken(linkToken);
            Assert.assertEquals(new Integer(1539369), userId);
        }
        {
            Integer userId = SafeRTCBxController.getUserIdFromLinkToken("abcd");
            Assert.assertEquals(null, userId);
        }
        {
            Integer userId = SafeRTCBxController.getUserIdFromLinkToken(null);
            Assert.assertEquals(null, userId);
        }
        {
            String linkToken = "1539369999999999999AICLQ7BZH1V00271706534677";
            Integer userId = SafeRTCBxController.getUserIdFromLinkToken(linkToken);
            Assert.assertEquals(null, userId);
        }
    }

    @Test
    public void getKissWsClientByKissIp() {
        kissSafeRTCWsService.getKissWsClientByKissIp(new ArrayList<>());
    }

    @Test
    public void testCloseGroupByUserId() throws URISyntaxException {
        Map<String, KissNode> kissNodeMap = new LinkedHashMap<>();
        String innerApiAddr = "***********";
        kissNodeMap.put(innerApiAddr, new KissNode() {{
            setRemoving(0);
            setInnerApiAddr(innerApiAddr);
        }});
        kissSafeRTCWsService.getIp2KissWsClient().refreshKissWsClient(KissNodeType.kissSafeRtc, kissNodeMap);
        KissWsClient kissWsClient = mock(KissWsClient.class);
        kissSafeRTCWsService.getIp2KissWsClient().put(innerApiAddr,kissWsClient );

        when(kissWsClient.isOpen()).thenReturn(true);
        boolean result =kissSafeRTCWsService.closeGroupByUserId(1);
        Assert.assertEquals(result, true);
        when(kissWsClient.isOpen()).thenThrow(new RuntimeException());
        result = kissSafeRTCWsService.closeGroupByUserId(1);
        Assert.assertEquals(result, false);
    }

}
