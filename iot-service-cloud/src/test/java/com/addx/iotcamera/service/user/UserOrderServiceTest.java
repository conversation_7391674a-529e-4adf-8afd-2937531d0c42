package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.additional_tier.AdditionalTierInfo;
import com.addx.iotcamera.bean.app.payment.ApplePaymentRequest;
import com.addx.iotcamera.bean.app.payment.GooglePaymentRequest;
import com.addx.iotcamera.bean.app.userorder.BoughtProductInfo;
import com.addx.iotcamera.bean.app.vip.TierInfo;
import com.addx.iotcamera.bean.app.vip.TierServiceInfoRequest;
import com.addx.iotcamera.bean.app.vip.UserOrderSubscriptionCancelRequest;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.init.TierProductInit;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.dao.user.IUserOrderCancelDAO;
import com.addx.iotcamera.enums.PaymentTypeEnums;
import com.addx.iotcamera.enums.ProductTypeEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.PaymentService;
import com.addx.iotcamera.service.ProductService;
import com.addx.iotcamera.service.UserOrderService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.service.pay.ApplePayService;
import com.addx.iotcamera.service.pay.GooglePayService;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.DateUtils;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class UserOrderServiceTest {
    @InjectMocks
    private UserOrderService userOrderService;

    @Mock
    private TierService tierService;

    @Mock
    private ProductService productService;

    @Mock
    private TierProductInit tierProductInit;

    @Mock
    private CopyWrite copyWrite;

    @Mock
    private OrderService orderService;

    @Mock
    private TenantTierConfig tenantTierConfig;

    @Mock
    private UserVipService userVipService;

    @Mock
    private PaymentService paymentService;

    @Mock
    private ApplePayService applePayService;
    @Mock
    private GooglePayService googlePayService;

    @Mock
    private IUserOrderCancelDAO iUserOrderCancelDAO;

    @Test
    @DisplayName("用户套餐购买记录为空")
    public void getBoughtProductList_empty(){
        int userId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();

        when(orderService.queryUserOrderList(any())).thenReturn(Lists.newArrayList());

        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());
        expectResult.setBoughtProductList(Lists.newArrayList());
        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("用户套餐购买记录-订阅未支付")
    public void getBoughtProductList_subType_Unpaid(){
        int userId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);

        when(orderService.queryUserOrderList(any())).thenReturn(this.initOrderDOListUnpaid());

        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());
        expectResult.setBoughtProductList(Lists.newArrayList());
        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("用户套餐购买记录-订阅订单-已支付-已过期")
    public void getBoughtProductList_subType_expire(){
        int userId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);

        when(orderService.queryUserOrderList(any())).thenReturn(this.initOrderDOListSubTypeExpire());

        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());
        expectResult.setBoughtProductList(Lists.newArrayList());
        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("用户套餐购买记录-历史订单_商品映射空")
    public void getBoughtProductList_productEmpty(){
        int userId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);

        when(orderService.queryUserOrderList(any())).thenReturn(this.initOrderDOListHistory());

        when(orderService.queryProductBatch(any())).thenReturn(Maps.newHashMap());
        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());
        expectResult.setBoughtProductList(Lists.newArrayList());


        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("用户套餐购买记录-历史订单_套餐信息列表为空")
    public void getBoughtProductList_tierInfoListEmpty(){
        int userId = 1;
        int tierId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);

        when(orderService.queryUserOrderList(any())).thenReturn(this.initOrderDOListHistory());

        Map<Long,ProductDO> orderProductMap = Maps.newHashMap();
        ProductDO productDO = new ProductDO();
        productDO.setTierId(tierId);
        when(orderService.queryProductBatch(any())).thenReturn(orderProductMap);

        when(tierService.queryTierListV2(any())).thenReturn(Lists.newArrayList());

        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());
        expectResult.setBoughtProductList(Lists.newArrayList());


        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("用户套餐购买记录-历史订单")
    public void getBoughtProductList_tierInfoList(){
        int userId = 1;
        int tierId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);


        List<OrderDO> orderDOList = Lists.newArrayList();
        OrderDO orderDO = OrderDO.builder()
                .id(1L)
                .status(1)
                .subType(0)
                .orderType(2)
                .timeEnd(((int)(System.currentTimeMillis()/1000)) - 14*24*60*60)
                .cdate(((int)(System.currentTimeMillis()/1000)) - 14*24*60*60)
                .build();
        orderDOList.add(orderDO);
        when(orderService.queryUserOrderList(any())).thenReturn(orderDOList);

        Map<Long,ProductDO> orderProductMap = Maps.newHashMap();
        ProductDO productDO = new ProductDO();
        productDO.setTierId(tierId);
        orderProductMap.put(orderDO.getId(),productDO);
        when(orderService.queryProductBatch(any())).thenReturn(orderProductMap);

        List<TierInfo> tenantIdTierInfoList = Lists.newArrayList();
        TierInfo tierInfo = new TierInfo();
        tierInfo.setId(tierId).setName("tierName");
        tenantIdTierInfoList.add(tierInfo);
        when(tierService.queryTierListV2(any())).thenReturn(tenantIdTierInfoList);
        when(tierProductInit.getProductMonth(any(),any(),any())).thenReturn("月份");

        when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode()).build());


        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());

        List<BoughtProductInfo.BoughtProduct> boughtProductList = new LinkedList<>();

        BoughtProductInfo.BoughtProduct boughtProduct = new BoughtProductInfo.BoughtProduct();
        boughtProduct.setTierName(tierInfo.getName());
        boughtProduct.setMonth("月份");
        boughtProduct.setBoughtFrom(userOrderService.getBoughtFromByOrderType(orderDO.getOrderType()));
        boughtProduct.setBoughtTime(orderDO.getCdate());
        boughtProduct.setOrderStatus(orderDO.getStatus());
        boughtProductList.add(boughtProduct);
        expectResult.setBoughtProductList(boughtProductList);


        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("用户套餐购买记录-历史订单-叠加包套餐-empty")
    public void getBoughtProductList_additionalTierInfo_empty(){
        int userId = 1;
        int tierId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);
        List<OrderDO> orderDOList = Lists.newArrayList();
        OrderDO orderDO = OrderDO.builder()
                .id(1L)
                .status(1)
                .subType(0)
                .orderType(2)
                .timeEnd(((int)(System.currentTimeMillis()/1000)) - 14*24*60*60)
                .cdate(((int)(System.currentTimeMillis()/1000)) - 14*24*60*60)
                .build();
        orderDOList.add(orderDO);
        when(orderService.queryUserOrderList(any())).thenReturn(orderDOList);

        Map<Long,ProductDO> orderProductMap = Maps.newHashMap();
        ProductDO productDO = new ProductDO();
        productDO.setAdditionalTierUid("add").setTierId(-1);
        orderProductMap.put(orderDO.getId(),productDO);
        when(orderService.queryProductBatch(any())).thenReturn(orderProductMap);

        when(tierService.queryTierListV2(any())).thenReturn(Lists.newArrayList());

        when(tierService.queryAdditionalTierList(any(),any())).thenReturn(Lists.newArrayList());

        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());
        expectResult.setBoughtProductList(Lists.newArrayList());

        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("用户套餐购买记录-历史订单-叠加包套餐-empty")
    public void getBoughtProductList_additionalTierInfo(){
        int userId = 1;
        int tierId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);
        List<OrderDO> orderDOList = Lists.newArrayList();
        OrderDO orderDO = OrderDO.builder()
                .id(1L)
                .status(1)
                .subType(0)
                .orderType(2)
                .timeEnd(((int)(System.currentTimeMillis()/1000)) - 14*24*60*60)
                .cdate(((int)(System.currentTimeMillis()/1000)) - 14*24*60*60)
                .build();
        orderDOList.add(orderDO);
        when(orderService.queryUserOrderList(any())).thenReturn(orderDOList);

        when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode()).build());

        Map<Long,ProductDO> orderProductMap = Maps.newHashMap();
        ProductDO productDO = new ProductDO();
        productDO.setAdditionalTierUid("add").setTierId(-1);
        orderProductMap.put(orderDO.getId(),productDO);
        when(orderService.queryProductBatch(any())).thenReturn(orderProductMap);

        when(tierService.queryTierListV2(any())).thenReturn(Lists.newArrayList());

        List<AdditionalTierInfo> additionalTierInfoList = Lists.newArrayList();
        AdditionalTierInfo additionalTierInfo = new AdditionalTierInfo();
        additionalTierInfo.setTierUid("add");
        additionalTierInfoList.add(additionalTierInfo);
        when(tierService.queryAdditionalTierList(any(),any())).thenReturn(additionalTierInfoList);
        when(tierProductInit.getProductMonth(any(),any(),any())).thenReturn("月份");

        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());

        List<BoughtProductInfo.BoughtProduct> boughtProductList = new LinkedList<>();

        BoughtProductInfo.BoughtProduct boughtProduct = new BoughtProductInfo.BoughtProduct();
        boughtProduct.setAdditionalTierUid(additionalTierInfo.getTierUid());
        boughtProduct.setMonth("月份");
        boughtProduct.setBoughtFrom(userOrderService.getBoughtFromByOrderType(orderDO.getOrderType()));
        boughtProduct.setBoughtTime(orderDO.getCdate());
        boughtProduct.setOrderStatus(orderDO.getStatus());
        boughtProductList.add(boughtProduct);
        expectResult.setBoughtProductList(boughtProductList);


        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("用户套餐购买记录-订阅订单_订单商品部存在")
    public void getBoughtProductList_subTypeOrder_noOrderProduct(){
        int userId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);

        when(orderService.queryUserOrderList(any())).thenReturn(this.initOrderDOListSubTypeOrder());
        when(orderService.queryProductBatch(any())).thenReturn(Maps.newHashMap());
        when(orderService.queryOrderProductDO(any())).thenReturn(null);

        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());
        expectResult.setBoughtProductList(Lists.newArrayList());
        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("用户套餐购买记录-订阅订单_订单商品部存在")
    public void getBoughtProductList_subTypeOrder_noProduct(){
        int userId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);

        when(orderService.queryUserOrderList(any())).thenReturn(this.initOrderDOListSubTypeOrder());
        when(orderService.queryProductBatch(any())).thenReturn(Maps.newHashMap());
        when(orderService.queryOrderProductDO(any())).thenReturn(this.initOrderProduct());
        when(productService.queryProductById(any())).thenReturn(null);

        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());
        expectResult.setBoughtProductList(Lists.newArrayList());
        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("用户套餐购买记录-订阅订单_套餐信息描述Null")
    public void getBoughtProductList_subTypeOrder_tierInfo_null(){
        int userId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);

        when(orderService.queryUserOrderList(any())).thenReturn(this.initOrderDOListSubTypeOrder());
        when(orderService.queryProductBatch(any())).thenReturn(Maps.newHashMap());
        when(orderService.queryOrderProductDO(any())).thenReturn(this.initOrderProduct());

        ProductDO productDO = new ProductDO();
        productDO.setId(20000);
        productDO.setTierId(1);
        productDO.setType(1);
        when(productService.queryProductById(any())).thenReturn(productDO);
        when(tierService.queryTierListV2(any())).thenReturn(null);

        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());
        expectResult.setBoughtProductList(Lists.newArrayList());
        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("用户套餐购买记录-订阅订单_套餐信息描述Null")
    public void getBoughtProductList_subTypeOrder_tierNotEqual(){
        int userId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);

        when(orderService.queryUserOrderList(any())).thenReturn(this.initOrderDOListSubTypeOrder());
        when(orderService.queryProductBatch(any())).thenReturn(Maps.newHashMap());
        when(orderService.queryOrderProductDO(any())).thenReturn(this.initOrderProduct());

        ProductDO productDO = new ProductDO();
        productDO.setId(20000);
        productDO.setTierId(1);
        productDO.setType(0);
        when(productService.queryProductById(any())).thenReturn(productDO);


        List<TierInfo> tenantIdTierInfoList = Lists.newArrayList();
        TierInfo tierInfo = new TierInfo();
        tierInfo.setId(2).setName("tierName");
        tenantIdTierInfoList.add(tierInfo);
        when(tierService.queryTierListV2(any())).thenReturn(tenantIdTierInfoList);


        BoughtProductInfo expectResult = new BoughtProductInfo();
        expectResult.setCurrentSubscriptionProductList(Lists.newArrayList());
        expectResult.setBoughtProductList(Lists.newArrayList());
        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("用户套餐购买记录-订阅订单")
    public void getBoughtProductList_subTypeOrder(){
        int userId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        AppInfo app = new AppInfo();
        request.setApp(app);


        List<OrderDO> orderDOList = Lists.newArrayList();
        Integer time = (int)(System.currentTimeMillis()/1000);
        OrderDO orderDO = OrderDO.builder()
                .id(1L)
                .status(1)
                .subType(1)
                .orderType(2)
                .timeStart(time)
                .timeEnd(time + 14*24*60*60)
                .build();
        orderDOList.add(orderDO);


        when(orderService.queryUserOrderList(any())).thenReturn(orderDOList);
        when(orderService.queryProductBatch(any())).thenReturn(Maps.newHashMap());
        when(orderService.queryOrderProductDO(any())).thenReturn(this.initOrderProduct());

        ProductDO productDO = new ProductDO();
        productDO.setId(20000);
        productDO.setTierId(1);
        when(productService.queryProductById(any())).thenReturn(productDO);


        List<TierInfo> tenantIdTierInfoList = Lists.newArrayList();
        TierInfo tierInfo = new TierInfo();
        tierInfo.setId(1).setName("tierName");
        tenantIdTierInfoList.add(tierInfo);
        when(tierService.queryTierListV2(any())).thenReturn(tenantIdTierInfoList);
        when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierServiceType(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode()).build());


        BoughtProductInfo expectResult = new BoughtProductInfo();

        List<BoughtProductInfo.BoughtSubscriptionProduct> currentSubscriptionProductList = new LinkedList<>();
        BoughtProductInfo.BoughtSubscriptionProduct boughtSubscriptionProduct = new BoughtProductInfo.BoughtSubscriptionProduct();
        boughtSubscriptionProduct.setTierName(tierInfo.getName());
        boughtSubscriptionProduct.setBoughtFrom(userOrderService.getBoughtFromByOrderType(orderDO.getOrderType()));
        boughtSubscriptionProduct.setNextSubscriptionTime(orderDO.getTimeEnd());
        boughtSubscriptionProduct.setOrderStatus(orderDO.getStatus());
        currentSubscriptionProductList.add(boughtSubscriptionProduct);

        expectResult.setCurrentSubscriptionProductList(currentSubscriptionProductList);
        expectResult.setBoughtProductList(Lists.newArrayList());
        BoughtProductInfo actualResult = userOrderService.getBoughtProductList(request,userId);
        Assert.assertEquals(expectResult,actualResult);
    }


    private OrderProductDo initOrderProduct(){
        return OrderProductDo.builder()
                .id(1L)
                .orderId(1L)
                .productId(20000)
                .build();
    }

    private List<OrderDO> initOrderDOListSubTypeOrder(){
        List<OrderDO> orderDOList = Lists.newArrayList();

        OrderDO orderDO = OrderDO.builder()
                .id(1L)
                .status(1)
                .subType(1)
                .orderType(2)
                .timeStart(((int)(System.currentTimeMillis()/1000)) - 14*24*60*60)
                .timeEnd(((int)(System.currentTimeMillis()/1000)) + 14*24*60*60)
                .build();
        orderDOList.add(orderDO);
        return orderDOList;
    }

    private List<OrderDO> initOrderDOListUnpaid(){
        List<OrderDO> orderDOList = Lists.newArrayList();
        OrderDO orderDO = OrderDO.builder()
                .status(0)
                .subType(0)
                .timeEnd(0)
                .build();
        orderDOList.add(orderDO);
        return orderDOList;
    }

    private List<OrderDO> initOrderDOListSubTypeExpire(){
        List<OrderDO> orderDOList = Lists.newArrayList();

        OrderDO orderDO = OrderDO.builder()
                .status(1)
                .subType(1)
                .timeEnd(((int)(System.currentTimeMillis()/1000)) - 14*24*60*60)
                .build();
        orderDOList.add(orderDO);
        return orderDOList;
    }

    private List<OrderDO> initOrderDOListHistory(){
        List<OrderDO> orderDOList = Lists.newArrayList();

        OrderDO orderDO = OrderDO.builder()
                .id(1L)
                .status(1)
                .subType(0)
                .orderType(2)
                .timeEnd(((int)(System.currentTimeMillis()/1000)) - 14*24*60*60)
                .cdate(((int)(System.currentTimeMillis()/1000)) - 14*24*60*60)
                .build();
        orderDOList.add(orderDO);
        return orderDOList;
    }

    @Test
    @DisplayName("订阅首月")
    public void nextSubOrderPaymentTime(){
        Integer time = (int)(System.currentTimeMillis()/1000);
        OrderDO orderDO = OrderDO.builder()
                .id(1L)
                .status(1)
                .subType(1)
                .orderType(2)
                .timeStart(time)
                .timeEnd(time)
                .build();

        Integer expectedResult = DateUtils.getDateAfterMonthSecond(time,1);
        Integer actualResult = userOrderService.nextSubOrderPaymentTime(orderDO);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("订阅非首月")
    public void nextSubOrderPaymentTime_notFirstMonth(){
        Integer time = (int)(System.currentTimeMillis()/1000);
        OrderDO orderDO = OrderDO.builder()
                .id(1L)
                .status(1)
                .subType(1)
                .orderType(2)
                .timeStart(time)
                .timeEnd(time + 1000)
                .build();

        Integer expectedResult = time + 1000;
        Integer actualResult = userOrderService.nextSubOrderPaymentTime(orderDO);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("查询用户当前生效的订阅订单")
    public void test_queryUserCurrentSubscriptionOrder(){
        Integer userId = 1;
        TierServiceInfoRequest request = new TierServiceInfoRequest();
        Gson gson = new Gson();
        BoughtProductInfo exceptedResult;
        BoughtProductInfo actualResult;
        ProductDO productDO = new ProductDO();
        productDO.setTierId(1);
        productDO.setType(ProductTypeEnums.SUBSCRIBE.getCode());
        when(productService.queryProductById(any())).thenReturn(productDO);
        when(tierService.queryTierListV2(any())).thenReturn(Lists.newArrayList());

        when(tierService.queryTierById(any())).thenReturn(Tier.builder().tierId(1).rollingDays(30).build());
        {
            //没有购买套餐记录
            when(userVipService.queryUserVipList(any(),any(),any())).thenReturn(Collections.singletonList(UserVipDO.builder().orderId(0L).tierId(10).build()));
            exceptedResult = new BoughtProductInfo();
            actualResult = userOrderService.queryUserCurrentSubscriptionOrder(userId,request);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            //非订阅订单
            when(userVipService.queryUserVipList(any(),any(),any())).thenReturn(Arrays.asList(
                    UserVipDO.builder().orderId(1L).tierId(1).tradeNo("tradeNo1").build(),
                    UserVipDO.builder().orderId(2L).tierId(2).tradeNo("tradeNo2").build()
            ));
            when(orderService.queryOrderBatch(any())).thenReturn(Lists.newArrayList());

            exceptedResult = new BoughtProductInfo();
            actualResult = userOrderService.queryUserCurrentSubscriptionOrder(userId,request);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            //非订阅订单
            when(userVipService.queryUserVipList(any(),any(),any())).thenReturn(Arrays.asList(
                    UserVipDO.builder().orderId(1L).tierId(1).tradeNo("tradeNo1").build(),
                    UserVipDO.builder().orderId(2L).tierId(1).tradeNo("tradeNo2").build(),
                    UserVipDO.builder().orderId(3L).tierId(1).tradeNo("tradeNo3").build(),
                    UserVipDO.builder().orderId(4L).tierId(1).tradeNo("tradeNo4").build()
            ));
            when(orderService.queryOrderBatch(any())).thenReturn(
                    Arrays.asList(
                            OrderDO.builder().id(1L).orderSn("orderSn1").status(1).orderCancel(0).build(),
                            OrderDO.builder().id(2L).orderSn("orderSn2").status(1).orderCancel(0).build(),
                            OrderDO.builder().id(3L).orderSn("orderSn3").status(1).orderCancel(0).build(),
                            OrderDO.builder().id(4L).orderSn("orderSn4").status(1).orderCancel(1).build(),
                            OrderDO.builder().id(5L).orderSn("orderSn5").status(1).orderCancel(1).build()
                    )
            );
            when(paymentService.queryPaymentBatch(any())).thenReturn(
                    Arrays.asList(
                            PaymentFlow.builder().outTradeNo("orderSn2").productId(1).type(2).freeTrial(1).expireTime(1).build(),
                            PaymentFlow.builder().outTradeNo("orderSn2").productId(1).type(2).freeTrial(0).build(),
                            PaymentFlow.builder().outTradeNo("orderSn3").productId(1).type(2).freeTrial(1).expireTime(0).extend(gson.toJson(new ApplePaymentRequest())).build(),
                            PaymentFlow.builder().outTradeNo("orderSn4").productId(1).type(3).freeTrial(1).expireTime(0).extend(gson.toJson(new GooglePaymentRequest())).build(),
                            PaymentFlow.builder().outTradeNo("orderSn5").productId(1).type(2).freeTrial(0).build()
                    )
            );

            when(applePayService.queryAppleOrderExpireTime(any(),any())).thenReturn(1);
            when(googlePayService.queryGoogleOrderExpireTime(any())).thenReturn(1);
            doNothing().when(paymentService).updatePaymentFlowExpire(any(),any());

            actualResult = userOrderService.queryUserCurrentSubscriptionOrder(userId,request);
            Assert.assertNotEquals(exceptedResult,actualResult);
        }
    }

    @Test(expected = BaseException.class)
    @DisplayName("取消验证不符合条件")
    public void test_userSubscriptionOrderCancel_order_null(){
        Integer userid = 1;
        UserOrderSubscriptionCancelRequest request = new UserOrderSubscriptionCancelRequest();
        request.setOrderId(1L);
        {
            //订单不存在
            when(orderService.queryOrderInfoById(any())).thenReturn(null);
            userOrderService.userSubscriptionOrderCancel(userid,request);
        }
        {
            //用户不匹配
            when(orderService.queryOrderInfoById(any())).thenReturn(OrderDO.builder().userId(2).build());
            userOrderService.userSubscriptionOrderCancel(userid,request);
        }
        {
            //不是订阅订单
            when(orderService.queryOrderInfoById(any())).thenReturn(OrderDO.builder().userId(userid).subType(0).build());
            userOrderService.userSubscriptionOrderCancel(userid,request);
        }
        {
            //取消原因为空
            when(orderService.queryOrderInfoById(any())).thenReturn(OrderDO.builder().userId(userid).subType(1).build());
            userOrderService.userSubscriptionOrderCancel(userid,request);
        }
        {
            List<UserOrderSubscriptionCancelRequest.OrderSubscriptionCancel> cancelList = Collections.singletonList(new UserOrderSubscriptionCancelRequest.OrderSubscriptionCancel());
            request.setCancelList(cancelList);
            //订单已取消
            when(orderService.queryOrderInfoById(any())).thenReturn(OrderDO.builder().userId(userid).orderCancel(1).subType(1).build());
            userOrderService.userSubscriptionOrderCancel(userid,request);
        }
    }


    @Test
    @DisplayName("不是订阅订单")
    public void test_userSubscriptionOrderCancel(){
        Integer userId = 1;
        UserOrderSubscriptionCancelRequest request = new UserOrderSubscriptionCancelRequest();
        request.setOrderId(1L);

        AppInfo app = new AppInfo();
        app.setBundle("test");
        request.setApp(app);

        List<UserOrderSubscriptionCancelRequest.OrderSubscriptionCancel> cancelList =Arrays.asList(new UserOrderSubscriptionCancelRequest.OrderSubscriptionCancel());
        request.setCancelList(cancelList);

        Gson gson = new Gson();

        GooglePaymentRequest param = new GooglePaymentRequest();
        param.setProductId(1);
        when(orderService.queryOrderInfoById(any())).thenReturn(OrderDO.builder().userId(userId).subType(1).orderCancel(0).orderType(PaymentTypeEnums.GOOGLE.getCode()).extend(gson.toJson(param)).build());

        when(iUserOrderCancelDAO.insertUserOrderCancel(any())).thenReturn(1);
        {
            when(googlePayService.subscriptionOrderCancel(any(),any(),any())).thenReturn(true);
            userOrderService.userSubscriptionOrderCancel(userId,request);
        }
        {
            when(googlePayService.subscriptionOrderCancel(any(),any(),any())).thenReturn(false);
            try{
                userOrderService.userSubscriptionOrderCancel(userId,request);
            }catch (Exception e){

            }

        }
    }
}
