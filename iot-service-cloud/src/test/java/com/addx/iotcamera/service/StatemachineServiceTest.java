package com.addx.iotcamera.service;

import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Map;

import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.util.OpenApiUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.addx.iotcamera.bean.domain.DeviceStateDO;
import com.addx.iotcamera.dao.statemachine.StateMachineDao;
import com.addx.iotcamera.enums.DeviceOnlineStatusEnums;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class StatemachineServiceTest {

    @InjectMocks
    private StateMachineService stateMachineService;
    @Mock
    private KissWsService kissWsService;
    @Mock
    private StateMachineDao stateMachineDao;

    @Test
    public void test_batchGetDeviceState() {
        when(stateMachineDao.batchGetDeviceState(any())).thenReturn(Arrays.asList(
                DeviceStateDO.builder().sn("sn_01").stateId(1).build(),
                DeviceStateDO.builder().sn("sn_02").stateId(9).build(),
                DeviceStateDO.builder().sn("sn_03").stateId(1).data("unstable").build())
        );
        when(kissWsService.getDeviceState(anyString())).thenAnswer(it -> {
            return new DeviceStateDO() {{
                setSn(it.getArgument(0));
                setOnlineStatus(DeviceOnlineStatusEnums.OFFLINE);
            }};
        });

        Map<String, DeviceStateDO> deviceStateMap = stateMachineService.batchGetDeviceState(Arrays.asList("sn_01", "sn_02", "sn_03"));
        Assert.assertTrue(deviceStateMap.get("sn_01").getOnlineStatus() == DeviceOnlineStatusEnums.ONLINE);
        Assert.assertTrue(deviceStateMap.get("sn_02").getOnlineStatus() == DeviceOnlineStatusEnums.OFFLINE);
        Assert.assertTrue(deviceStateMap.get("sn_03").getOnlineStatus() == DeviceOnlineStatusEnums.OFFLINE);
    }

    @Test
    public void test_batchGetDeviceState_kissWs_null() {
        when(stateMachineDao.batchGetDeviceState(any())).thenReturn(Arrays.asList(
                DeviceStateDO.builder().sn("sn_01").stateId(1).build(),
                DeviceStateDO.builder().sn("sn_02").stateId(9).build(),
                DeviceStateDO.builder().sn("sn_03").stateId(1).data("unstable").build())
        );
        when(kissWsService.getDeviceState(anyString())).thenReturn(null);

        Map<String, DeviceStateDO> deviceStateMap = stateMachineService.batchGetDeviceState(Arrays.asList("sn_01", "sn_02", "sn_03"));
        Assert.assertTrue(deviceStateMap.get("sn_01").getOnlineStatus() == DeviceOnlineStatusEnums.ONLINE);
        Assert.assertTrue(deviceStateMap.get("sn_02").getOnlineStatus() == DeviceOnlineStatusEnums.OFFLINE);
        Assert.assertTrue(deviceStateMap.get("sn_03").getOnlineStatus() == DeviceOnlineStatusEnums.OFFLINE);
    }

    @Test
    public void test_batchGetDeviceState_kissWs_online() {
        when(stateMachineDao.batchGetDeviceState(any())).thenReturn(Arrays.asList(
                DeviceStateDO.builder().sn("sn_01").stateId(1).build(),
                DeviceStateDO.builder().sn("sn_02").stateId(9).build(),
                DeviceStateDO.builder().sn("sn_03").stateId(1).data("unstable").build())
        );
        when(kissWsService.getDeviceState(anyString())).thenAnswer(it -> {
            return new DeviceStateDO() {{
                setSn(it.getArgument(0));
                setOnlineStatus(DeviceOnlineStatusEnums.ONLINE);
            }};
        });
        Map<String, DeviceStateDO> deviceStateMap = stateMachineService.batchGetDeviceState(Arrays.asList("sn_01", "sn_02", "sn_03"));
        Assert.assertTrue(deviceStateMap.get("sn_01").getOnlineStatus() == DeviceOnlineStatusEnums.ONLINE);
        Assert.assertTrue(deviceStateMap.get("sn_02").getOnlineStatus() == DeviceOnlineStatusEnums.ONLINE);
        Assert.assertTrue(deviceStateMap.get("sn_03").getOnlineStatus() == DeviceOnlineStatusEnums.ONLINE);
    }

    @Test
    public void test_getOnlineTimeByModelNo() {
        String modelNo = OpenApiUtil.shortUUID();
        {
            when(stateMachineDao.getV2RuleTimeoutToByModelNo(modelNo)).thenReturn(Arrays.asList("310,state_connection_lost"));
            Integer onlineTime = stateMachineService.getOnlineTimeByModelNo(modelNo);
            Assert.assertEquals(new Integer(310), onlineTime);
        }
        {
            when(stateMachineDao.getV2RuleTimeoutToByModelNo(modelNo)).thenReturn(Arrays.asList());
            Integer onlineTime = stateMachineService.getOnlineTimeByModelNo(modelNo);
            Assert.assertEquals(new Integer(-1), onlineTime);
        }
        {
            when(stateMachineDao.getV2RuleTimeoutToByModelNo(modelNo)).thenReturn(Arrays.asList("310,xyz"));
            Integer onlineTime = stateMachineService.getOnlineTimeByModelNo(modelNo);
            Assert.assertEquals(new Integer(-1), onlineTime);
        }
        {
            when(stateMachineDao.getV2RuleTimeoutToByModelNo(modelNo)).thenReturn(Arrays.asList("999999999999999,state_connection_lost"));
            Integer onlineTime = stateMachineService.getOnlineTimeByModelNo(modelNo);
            Assert.assertEquals(new Integer(-1), onlineTime);
        }
        {
            when(stateMachineDao.getV2RuleTimeoutToByModelNo(modelNo)).thenThrow(new RuntimeException("mock error!"));
            Integer onlineTime = stateMachineService.getOnlineTimeByModelNo(modelNo);
            Assert.assertEquals(new Integer(-1), onlineTime);
        }
    }

}
