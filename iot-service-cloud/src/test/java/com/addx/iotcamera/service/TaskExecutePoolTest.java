package com.addx.iotcamera.service;

import com.addx.iotcamera.config.ThreadPoolsConfig;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.thread.TaskExecutePool;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class TaskExecutePoolTest {

    private ReportLogService reportLogService = new ReportLogService();

    private ThreadPoolsConfig threadPoolsConfig;
    private TaskExecutePool taskExecutePool;

    @Before
    public void init() {
        JSONObject config = ConfigHelper.loadYmlConfig("classpath:application.yml");
        this.threadPoolsConfig = config.getObject("thread-pools", ThreadPoolsConfig.class);
        this.taskExecutePool = new TaskExecutePool().setThreadPoolsConfig(threadPoolsConfig)
                .setReportLogService(reportLogService);
    }

    @Test
    public void test() throws InterruptedException {
        Executor commonPool = taskExecutePool.commonPool();
        Executor killKeepAlivePool = taskExecutePool.killKeepAliveAsyncPool();
        Executor mqttConsumePool = taskExecutePool.mqttConsumeAsyncPool();
        ForkJoinPool forkJoinPool = taskExecutePool.videoUpdateForkJoinPool();
        Executor executor = taskExecutePool.videoConsumerAsyncPool();
        Executor executor1 = taskExecutePool.heartbeatConnectedAsyncPool();

        commonPool.execute(() -> System.out.println("task1 running..."));
        Thread.sleep(100L);
        commonPool.execute(() -> System.out.println("task2 running..."));
        Thread.sleep(100L);
        commonPool.execute(() -> System.out.println("task2 running..."));
        Thread.currentThread().join(1000);
    }
}
