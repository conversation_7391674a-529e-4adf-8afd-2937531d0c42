package com.addx.iotcamera.service.tenant;


import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.dao.tenant.TenantSettingDAO;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.LinkedHashSet;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class TenantSettingServiceTest {

    @InjectMocks
    private TenantSettingService tenantSettingService;

    @Mock
    private TenantSettingDAO tenantSettingDAO;

    @Test
    public void test_getTenantSettingByTenantId() {
        {
            final String tenantId = OpenApiUtil.shortUUID();
            final TenantSetting defaultSetting = tenantSettingService.buildDefault(tenantId);
            when(tenantSettingDAO.queryByTenantId(tenantId)).thenReturn(null);
            Assert.assertEquals(defaultSetting, tenantSettingService.getTenantSettingByTenantId(tenantId));
        }
        {
            final String tenantId = OpenApiUtil.shortUUID();
            final TenantSetting defaultSetting = tenantSettingService.buildDefault(tenantId);
            when(tenantSettingDAO.queryByTenantId(tenantId)).thenThrow(new RuntimeException());
            Assert.assertEquals(defaultSetting, tenantSettingService.getTenantSettingByTenantId(tenantId));
        }
        {
            final String tenantId = null;
            when(tenantSettingDAO.queryByTenantId(tenantId)).thenThrow(new RuntimeException());
            Assert.assertEquals(new TenantSetting().setTenantId(null), tenantSettingService.getTenantSettingByTenantId(tenantId));
        }
        {
            final String tenantId = OpenApiUtil.shortUUID();
            final TenantSetting defaultSetting = tenantSettingService.buildDefault(tenantId);
            final TenantSetting setting = new TenantSetting().setTenantId(tenantId).setEnableLiveChat(true);
            when(tenantSettingDAO.queryByTenantId(tenantId)).thenReturn(setting);
            Assert.assertEquals(setting, tenantSettingService.getTenantSettingByTenantId(tenantId));

            when(tenantSettingDAO.queryByTenantId(tenantId)).thenReturn(null);
            Assert.assertEquals(setting, tenantSettingService.getTenantSettingByTenantId(tenantId));

            tenantSettingService.getCache().invalidateAll();
            Assert.assertEquals(defaultSetting, tenantSettingService.getTenantSettingByTenantId(tenantId));

            when(tenantSettingDAO.queryByTenantId(tenantId)).thenReturn(setting);
            Assert.assertEquals(defaultSetting, tenantSettingService.getTenantSettingByTenantId(tenantId));

            tenantSettingService.getCache().invalidateAll();
            Assert.assertEquals(setting, tenantSettingService.getTenantSettingByTenantId(tenantId));
        }
        tenantSettingService.invalidateTenantSettingCache();
    }

    @Test
    public void test_saveTenantSetting() {
        final TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        final TenantSettingDAO dao = testHelper.getMapper(TenantSettingDAO.class);
        when(tenantSettingDAO.save(any())).thenAnswer(AdditionalAnswers.delegatesTo(dao));
        when(tenantSettingDAO.queryByTenantId(any())).thenAnswer(AdditionalAnswers.delegatesTo(dao));
        {
            final String tenantId = OpenApiUtil.shortUUID();
            final TenantSetting setting1 = new TenantSetting().setTenantId(tenantId).setEnableLiveChat(true)
                    .setLiveChatCustomerIds(new LinkedHashSet<>(Arrays.asList("a1", "b2")));
            dao.save(setting1);
            final TenantSetting querySetting1 = tenantSettingService.getTenantSettingByTenantId(tenantId);
            Assert.assertEquals(querySetting1, setting1);
            log.info("tenantSetting:{}", JSON.toJSONString(setting1));
        }
        {
            final String tenantId = OpenApiUtil.shortUUID();
            final TenantSetting setting2 = new TenantSetting().setTenantId(tenantId).setEnableLiveChat(false);
            dao.save(setting2);
            final TenantSetting querySetting2 = tenantSettingService.getTenantSettingByTenantId(tenantId);
            Assert.assertEquals(querySetting2, setting2);
            log.info("tenantSetting2:{}", JSON.toJSONString(setting2));
        }
        {
            final String tenantId = OpenApiUtil.shortUUID();
            final TenantSetting setting2 = new TenantSetting().setTenantId(tenantId).setEnableLiveChat(false)
                    .setPirRecordTimeDisabledOptions(new LinkedHashSet<>(Arrays.asList("60s", "120s")))
                    .setPirCooldownTimeDisabledOptions(new LinkedHashSet<>(Arrays.asList("600s", "1200s", "1800s")));
            dao.save(setting2);
            final TenantSetting querySetting2 = tenantSettingService.getTenantSettingByTenantId(tenantId);
            Assert.assertEquals(querySetting2, setting2);
            log.info("tenantSetting2:{}", JSON.toJSONString(setting2));
        }
        testHelper.commitAndClose();
    }


}
