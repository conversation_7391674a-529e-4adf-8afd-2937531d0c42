package com.addx.iotcamera.service.deviceplatform.alexa.safemo;

import com.addx.iotcamera.bean.app.alexa.request.AlexaSafemoMsg;
import com.addx.iotcamera.constants.AlexaSafemoEventType;
import com.addx.iotcamera.util.AlexaSafemoUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AlexaSafemoOnMessageServiceTest {

    @InjectMocks
    AlexaSafemoOnMessageService alexaSafemoOnMessageService;
    @Mock
    private AlexaSafemoMsgProcessService alexaSafemoMsgProcessService;
    @Mock
    private AlexaSafemoSyncService alexaSafemoSyncService;

    @Test
    public void onMessage() {
        String messageId = "1";
        String messageType = "safemo_alexa_local_to_service";
        String eventType = null;
        JSONObject data = new JSONObject();
        data.put("adminUserId", 1);
        data.put("cxSerialNumber", "1");
        AlexaSafemoMsg alexaSafemoMsg = new AlexaSafemoMsg();
        alexaSafemoMsg.setMessageId(messageId)
                .setMessageType(messageType)
                .setEventType(eventType)
                .setData(data);

        // 条件1
        String rawMsg = JSON.toJSONString(alexaSafemoMsg);
        alexaSafemoOnMessageService.onMessage(rawMsg);

        // 条件2
        alexaSafemoMsg.setEventType(AlexaSafemoEventType.CX_DEVICE_BIND);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        alexaSafemoOnMessageService.onMessage(rawMsg);

        // 条件3
        alexaSafemoMsg.setEventType(AlexaSafemoEventType.CX_DEVICE_DEACTIVATE);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        alexaSafemoOnMessageService.onMessage(rawMsg);

        // 条件4
        alexaSafemoMsg.setEventType(AlexaSafemoEventType.CX_DEVICE_INFO_AND_STATUS_CHANGE);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        alexaSafemoOnMessageService.onMessage(rawMsg);

        // 条件5
        alexaSafemoMsg.setEventType(AlexaSafemoEventType.USER_BX_CONNECT_KISS);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        alexaSafemoOnMessageService.onMessage(rawMsg);

        // 条件6
        alexaSafemoMsg.setEventType(AlexaSafemoEventType.CX_DEVICE_LIST_DATA);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        alexaSafemoOnMessageService.onMessage(rawMsg);

        // 条件7
        alexaSafemoMsg.setEventType(AlexaSafemoEventType.CX_DEVICE_INFO_AND_STATUS_DATA);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        alexaSafemoOnMessageService.onMessage(rawMsg);

        // 条件8
        alexaSafemoMsg.setEventType(AlexaSafemoEventType.IS_USER_LINK_TO_ALEXA);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        alexaSafemoOnMessageService.onMessage(rawMsg);

        // 条件9
        alexaSafemoMsg.setEventType("");
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        alexaSafemoOnMessageService.onMessage(rawMsg);
    }

    @Test
    public void isAlexaMsg() {
        AlexaSafemoMsg alexaSafemoMsg = new AlexaSafemoMsg();
        String rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertFalse(AlexaSafemoUtil.isAlexaMsg(rawMsg));

        alexaSafemoMsg.setMessageType("safemo_alexa_local_to_service");
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertTrue(AlexaSafemoUtil.isAlexaMsg(rawMsg));
    }

    @Test
    public void getEventType() {
        Assert.assertNull(AlexaSafemoUtil.getEventType(null));

        AlexaSafemoMsg alexaSafemoMsg = new AlexaSafemoMsg();
        alexaSafemoMsg.setEventType("");
        String rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNotNull(AlexaSafemoUtil.getEventType(rawMsg));
    }

    @Test
    public void getAdminUserId() {
        AlexaSafemoMsg alexaSafemoMsg = new AlexaSafemoMsg();
        String rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNull(AlexaSafemoUtil.getAdminUserId(rawMsg));

        JSONObject data = new JSONObject();
        data.put("adminUserId", 1);
        alexaSafemoMsg.setData(data);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNotNull(AlexaSafemoUtil.getAdminUserId(rawMsg));
    }

    @Test
    public void getCxSerialNumber() {
        AlexaSafemoMsg alexaSafemoMsg = new AlexaSafemoMsg();
        String rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNull(AlexaSafemoUtil.getCxSerialNumber(rawMsg));

        JSONObject data = new JSONObject();
        data.put("cxSerialNumber", "1");
        alexaSafemoMsg.setData(data);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNotNull(AlexaSafemoUtil.getCxSerialNumber(rawMsg));
    }

    @Test
    public void getKissIp() {
        AlexaSafemoMsg alexaSafemoMsg = new AlexaSafemoMsg();
        String rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNull(AlexaSafemoUtil.getKissIp(rawMsg));

        JSONObject data = new JSONObject();
        data.put("kissIp", "1");
        alexaSafemoMsg.setData(data);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNotNull(AlexaSafemoUtil.getKissIp(rawMsg));
    }

    @Test
    public void getDeviceEvent() {
        AlexaSafemoMsg alexaSafemoMsg = new AlexaSafemoMsg();
        String rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNull(AlexaSafemoUtil.getDeviceEvent(rawMsg));

        JSONObject data = new JSONObject();
        data.put("adminUserId", 1);
        alexaSafemoMsg.setData(data);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNotNull(AlexaSafemoUtil.getDeviceEvent(rawMsg));
    }

    @Test
    public void getMessageId() {
        AlexaSafemoMsg alexaSafemoMsg = new AlexaSafemoMsg();
        String rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNull(AlexaSafemoUtil.getMessageId(rawMsg));

        Assert.assertNull(AlexaSafemoUtil.getMessageId(null));
    }

    @Test
    public void getDeviceInfoAndStatus() {
        AlexaSafemoMsg alexaSafemoMsg = new AlexaSafemoMsg();
        String rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNull(AlexaSafemoUtil.getDeviceInfoAndStatus(rawMsg));

        JSONObject data = new JSONObject();
        data.put("deviceInfoAndStatus", new JSONObject());
        alexaSafemoMsg.setData(data);
        rawMsg = JSON.toJSONString(alexaSafemoMsg);
        Assert.assertNotNull(AlexaSafemoUtil.getDeviceInfoAndStatus(rawMsg));
    }
}