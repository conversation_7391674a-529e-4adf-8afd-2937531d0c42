package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.LibraryTb;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.util.Assert.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LibraryUpdateReceiveAllServiceTest {

    @InjectMocks
    @Spy
    private LibraryUpdateReceiveAllService libraryUpdateReceiveAllService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private IShardingLibraryDAO iShardingLibraryDAO;

    @Mock
    private MqSender mqSender;
    @Mock
    private IShardingLibraryDAO shardingLibraryDAO;

    @Test
    public void test() {
        libraryUpdateReceiveAllService.updateReceiveAllSlice(LibraryTb.builder().build());
    }

    @Test
    public void test_updateReceiveAllSliceAsync() {
        LibraryTb libraryTb = new LibraryTb();
        libraryTb.setSerialNumber("sn1");
        libraryTb.setTraceId("trace1");
        {
            LibraryUpdateReceiveAllService service = new LibraryUpdateReceiveAllService();
            service.setShardingLibraryDAO(shardingLibraryDAO);
            AssertUtil.assertNotException(() -> service.updateReceiveAllSliceAsync(libraryTb));
        }
        when(shardingLibraryDAO.updateLibraryByUserIdAndTraceId(any())).thenReturn(1);
        {
            when(userRoleService.getDeviceAdminUser(libraryTb.getSerialNumber())).thenReturn(0);
            AssertUtil.assertNotException(() -> libraryUpdateReceiveAllService.updateReceiveAllSliceAsync(libraryTb));
        }
        when(userRoleService.getDeviceAdminUser(libraryTb.getSerialNumber())).thenReturn(54321);
        AssertUtil.assertNotException(() -> libraryUpdateReceiveAllService.updateReceiveAllSliceAsync(libraryTb));
    }
}
