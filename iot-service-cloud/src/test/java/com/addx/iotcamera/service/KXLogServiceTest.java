package com.addx.iotcamera.service;


import com.addx.iotcamera.bean.param.OciCredentials;
import com.addx.iotcamera.bean.param.OciPar;
import com.addx.iotcamera.bean.response.KXLogUploadResponse;
import com.addx.iotcamera.bean.video.StoreBucket;
import com.addx.iotcamera.config.OciConfig;
import com.addx.iotcamera.helper.TimeTicker;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;


@RunWith(PowerMockRunner.class)
public class KXLogServiceTest {

    @InjectMocks
    private KXLogService kxLogService;

    @Mock
    private OciService ociService;

    @Mock
    private OciConfig ociConfig;

    @Mock
    private TimeTicker timeTicker;

    @Mock
    private RedisService redisService;


    @Test
    public void appUploadLog() {
        long l = System.currentTimeMillis()+1000000L;
        when(ociConfig.getEnable()).thenReturn(true);
        when(ociConfig.getEnableCreateCredential()).thenReturn(true);
        when(timeTicker.readMillis()).thenReturn(System.currentTimeMillis());
        String redisKey = "kx_log::app_par:123";

        OciPar ociPar = new OciPar("namespace","bucket", "us-oci-region","parId","accessUri",1L,l,l);
        doNothing().when(redisService).set(any(), any());
        Mockito.when(redisService.get(redisKey)).thenReturn(JSON.toJSONString(ociPar));
        ReflectionTestUtils.setField(kxLogService,"kxLogBucket","bucket" );
        Integer userId = 123;
        Result result = kxLogService.appUploadLog(userId);
        JSONObject data =((JSONObject) result.getData()).getJSONObject("credentials");
        Assert.assertEquals("accessUri", data.getJSONArray("accessUri").get(0));
    }

    @Test
    public void deviceUploadLog() {
        long l = System.currentTimeMillis()+1000000L;
        when(ociConfig.getEnable()).thenReturn(true);
        when(ociConfig.getEnableCreateCredential()).thenReturn(true);
        when(timeTicker.readMillis()).thenReturn(System.currentTimeMillis());
        String redisKey = "kx_log::device_par:serialNumber";

        OciPar ociPar = new OciPar("namespace","bucket", "us-oci-region","parId","accessUri",1L,l,l);
        doNothing().when(redisService).set(any(), any());
        Mockito.when(redisService.get(redisKey)).thenReturn(JSON.toJSONString(ociPar));
        ReflectionTestUtils.setField(kxLogService,"kxLogBucket","bucket" );
        String serialNumber = "serialNumber";
        Result result = kxLogService.deviceUploadLog(serialNumber);
        JSONObject data =((JSONObject) result.getData()).getJSONObject("credentials");
        Assert.assertEquals("accessUri", data.getJSONArray("accessUri").get(0));
    }

    @Test
    public void createKxLogUploadOciPar_oci() {
        long l = System.currentTimeMillis()+1000000L;
        when(ociConfig.getEnable()).thenReturn(true);
        when(ociConfig.getEnableCreateCredential()).thenReturn(true);
        when(timeTicker.readMillis()).thenReturn(System.currentTimeMillis());
        String redisKey = "redisKey";
        String bucket = "bucket";
        String prefix = "prefix";

        OciPar ociPar = new OciPar("namespace","bucket", "us-oci-region","parId","accessUri",1L,l,l);
        doNothing().when(redisService).set(any(), any());
        Mockito.when(redisService.get(redisKey)).thenReturn(JSON.toJSONString(ociPar));
        ReflectionTestUtils.setField(kxLogService,"kxLogBucket","bucket" );
        KXLogUploadResponse kxLogUploadResponse = kxLogService.createKxLogUploadOciPar(prefix, redisKey);
        Assert.assertEquals("accessUri", kxLogUploadResponse.getCredentials().getAccessUri().get(0));
    }


    @Test
    public void createKXLogUploadOciCredentials_null() {
        when(ociConfig.getEnableCreateCredential()).thenReturn(true);
        when(timeTicker.readMillis()).thenReturn(System.currentTimeMillis());
        String redisKey = "redisKey";
        String bucket = "bucket";
        String prefix = "prefix";

        doNothing().when(redisService).set(any(), any());
        Mockito.when(redisService.get(redisKey)).thenReturn(null);
        OciPar ociPar = new OciPar("namespace","bucket", "us-oci-region","parId","accessUri",1L,2000L,2000L);
        Mockito.when(ociService.createPar(any(StoreBucket.class), any())).thenReturn(ociPar);
        OciCredentials kxLogUploadOciCredentials = kxLogService.createKXLogUploadOciCredentials(bucket, prefix, redisKey);
        Assert.assertEquals("accessUri", kxLogUploadOciCredentials.getAccessUri().get(0));
        Assert.assertTrue(2==kxLogUploadOciCredentials.getExpirationSeconds());
    }

    @Test
    public void createKXLogUploadOciCredentials_expired() {
        long l = System.currentTimeMillis();
        when(ociConfig.getEnableCreateCredential()).thenReturn(true);
        when(timeTicker.readMillis()).thenReturn(System.currentTimeMillis());
        String redisKey = "redisKey";
        String bucket = "bucket";
        String prefix = "prefix";

        OciPar ociPar = new OciPar("namespace","bucket", "us-oci-region","parId","accessUri",1L,l,l);
        doNothing().when(redisService).set(any(), any());
        doNothing().when(ociService).deleteExpiredPar(any(), any());
        Mockito.when(redisService.get(redisKey)).thenReturn(JSON.toJSONString(ociPar));
        long l1 = System.currentTimeMillis();
        OciPar newOciPar = new OciPar("namespace","bucket","us-oci-region","parId","accessUri",1L,l1,l1);
        Mockito.when(ociService.createPar(any(), any())).thenReturn(newOciPar);
        OciCredentials kxLogUploadOciCredentials = kxLogService.createKXLogUploadOciCredentials(bucket, prefix, redisKey);
        Assert.assertEquals("accessUri", kxLogUploadOciCredentials.getAccessUri().get(0));
        Assert.assertTrue((int)(l1/1000)==kxLogUploadOciCredentials.getExpirationSeconds());
    }

    @Test
    @SneakyThrows
    public void createKXLogUploadOciCredentials_not_expired() {
        long l = System.currentTimeMillis()+1000000L;
        when(ociConfig.getEnableCreateCredential()).thenReturn(true);
        when(timeTicker.readMillis()).thenReturn(System.currentTimeMillis());
        String redisKey = "redisKey";
        String bucket = "bucket";
        String prefix = "prefix";

        OciPar ociPar = new OciPar("namespace","bucket","us-oci-region","parId","accessUri",1L,l,l);
        doNothing().when(redisService).set(any(), any());
        Mockito.when(redisService.get(redisKey)).thenReturn(JSON.toJSONString(ociPar));
        OciCredentials kxLogUploadOciCredentials = kxLogService.createKXLogUploadOciCredentials(bucket, prefix, redisKey);
        Assert.assertEquals("accessUri", kxLogUploadOciCredentials.getAccessUri().get(0));
        Assert.assertTrue((int)(l/1000)==kxLogUploadOciCredentials.getExpirationSeconds());
    }
}