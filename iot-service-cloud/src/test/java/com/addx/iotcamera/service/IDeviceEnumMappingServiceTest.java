package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.DeviceModelEnumMappingDO;
import com.addx.iotcamera.bean.device.attributes.OptionEnumMapping;
import com.addx.iotcamera.dao.DeviceModelEnumMappingDAO;
import com.addx.iotcamera.service.device.DeviceEnumMappingService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class IDeviceEnumMappingServiceTest {

    @InjectMocks
    private DeviceEnumMappingService deviceEnumMappingService;

    @Before
    public void before() {

    }

    /* 统计 设备型号-分辨率-数量
    select t.model_no,group_concat(t.rec_resolution),group_concat(t.num) from (
    select dm.model_no,d.rec_resolution,count(1) num
    from camera.device d join camera.device_manual dm on d.serial_number=dm.serial_number
    group by dm.model_no,d.rec_resolution ) t group by t.model_no;
     */
    @Test
    public void test_modelNo_resolution_num() {

    }

    /*
    select concat(dm.model_no,';',ds.`device_support_resolution`,';',count(1))
    from `camera`.`device_support` ds join `camera`.`device_manual` dm on dm.serial_number=ds.serial_number
    group by dm.`model_no`,ds.`device_support_resolution`;
     */
    private static final String modelNo2Resolution = "" +
            "CB0;1080P,360P;2\n" +
            "CB0-JS;1080P,360P;3717\n" +
            "CB040B;1080P,360P;8\n" +
            "CB040B-LT;1080P,360P;2\n" +
            "CB040B-TN2;1080P,360P;1\n" +
            "CB040C;1080P,360P;9\n" +
            "CB040C-JS1;1080P,360P;8\n" +
            "CB040C-JS2;1080P,360P;1\n" +
            "CB1;1080P,360P;1\n" +
            "CB140;1080P,360P;3\n" +
            "CB140-A;1080P,360P;2\n" +
            "CB140-A-JS;1080P,360P;443\n" +
            "CB140-JS;1080P,360P;3\n" +
            "CB140-JS-S1L;1080P,360P;374\n" +
            "CB140B;1080P,360P;2\n" +
            "CB140B-JS;1080P,360P;71\n" +
            "CB1C;1080P,360P;2808\n" +
            "CB1C-JS;1080P,360P;27\n" +
            "CB1C-JS-S1;1080P,360P;1509\n" +
            "CB2;1080P,360P;5\n" +
            "CB320-JS;1080P,360P;254\n" +
            "CB340-JS;1080P,360P;33\n" +
            "CB340-JS2;1080P,360P;4\n" +
            "CG1;720P,1080P;1\n" +
            "CG122;2560x1440,1280x720;2\n" +
            "CG122-DC;2560x1440,1280x720;4751\n" +
            "CG122-DCR;2560x1440,1280x720;566\n" +
            "CG122-FTK;2560x1440,1280x720;546\n" +
            "CG122-JA;2560x1440,1280x720;1744\n" +
            "CG122-JS;2560x1440,1280x720;234\n" +
            "CG122-ST;2560x1440,1280x720;1170\n" +
            "CG122-ST-ITRON;2560x1440,1280x720;97\n" +
            "CG122-VT;2560x1440,1280x720;34\n" +
            "CG122-WX;2560x1440,1280x720;207\n" +
            "CG522;2560x1440,1280x720;1\n" +
            "CG522-JS;2560x1440,1280x720;21\n" +
            "CG522-JS-BTN;2560x1440,1280x720;1\n" +
            "CG522-JS-ZKT;2560x1440,1280x720;66\n" +
            "CG522-JS2;2560x1440,1280x720;315\n" +
            "CG522-MHK;2560x1440,1280x720;1\n" +
            "CG522-ST;2560x1440,1280x720;7\n" +
            "CG522-TN;2560x1440,1280x720;116\n" +
            "CG522-TN-YSY;2560x1440,1280x720;81\n" +
            "CG621;1920x1080,1280x720;782\n" +
            "CG621-DC;1920x1080,1280x720;72\n" +
            "CG621-JA;1920x1080,1280x720;1106\n" +
            "CG621-JS;1920x1080,1280x720;1039\n" +
            "CG621-RQ;1920x1080,1280x720;670\n" +
            "CG621-W;1920x1080,1280x720;10\n" +
            "CG621-W-A4X;1920x1080,1280x720;1484\n" +
            "CG621-W-BTN;1920x1080,1280x720;1\n" +
            "CG621-W-CA;1920x1080,1280x720;3699\n" +
            "CG621-W-CS;1920x1080,1280x720;12\n" +
            "CG621-W-DC;1920x1080,1280x720;2556\n" +
            "CG621-W-DCR;1920x1080,1280x720;45\n" +
            "CG621-W-FTK;1920x1080,1280x720;3645\n" +
            "CG621-W-FTK2;1920x1080,1280x720;320\n" +
            "CG621-W-GSGL;1920x1080,1280x720;2165\n" +
            "CG621-W-HDG;1920x1080,1280x720;1243\n" +
            "CG621-W-JA;1280x720,640x480;1\n" +
            "CG621-W-JA;1920x1080,1280x720;13114\n" +
            "CG621-W-JS;1920x1080,1280x720;14980\n" +
            "CG621-W-JS2;1920x1080,1280x720;36\n" +
            "CG621-W-JXJ;1920x1080,1280x720;1619\n" +
            "CG621-W-KMY;1920x1080,1280x720;717\n" +
            "CG621-W-MZ;1920x1080,1280x720;117\n" +
            "CG621-W-ST;1920x1080,1280x720;5810\n" +
            "CG621-W-TN;1920x1080,1280x720;7298\n" +
            "CG621-W-TN2;1920x1080,1280x720;1\n" +
            "CG621-W-VT;1920x1080,1280x720;14\n" +
            "CG621-W-WX;1920x1080,1280x720;4\n" +
            "CG623-W;1920x1080,1280x720;6\n" +
            "CG623-W-8102;1920x1080,1280x720;1\n" +
            "CG623-W-8202;1920x1080,1280x720;1\n" +
            "CG623-W-CA;1920x1080,1280x720;541\n" +
            "CG623-W-CS;1920x1080,1280x720;228\n" +
            "CG623-W-DC;1920x1080,1280x720;2031\n" +
            "CG623-W-JA;1920x1080,1280x720;7496\n" +
            "CG623-W-JS;1920x1080,1280x720;233\n" +
            "CG623-W-JS2;1920x1080,1280x720;864\n" +
            "CG623-W-ST;1920x1080,1280x720;45\n" +
            "CG623-W-THR;1920x1080,1280x720;8\n" +
            "CG623-W-TN;1920x1080,1280x720;16201\n" +
            "CG623-W-TN2;1920x1080,1280x720;2521\n" +
            "CG623-W-VT;1920x1080,1280x720;28\n" +
            "CG623-W-WSK2;1920x1080,1280x720;5\n" +
            "CG623-W-WX;1920x1080,1280x720;1366\n" +
            "CG623B-W;1920x1080,1280x720;3\n" +
            "CG623B-W-8102;1920x1080,1280x720;47\n" +
            "CG623B-W-CA;1920x1080,1280x720;66\n" +
            "CG623B-W-CJJ;1920x1080,1280x720;1\n" +
            "CG623B-W-DC;1920x1080,1280x720;438\n" +
            "CG623B-W-HG;1920x1080,1280x720;1\n" +
            "CG623B-W-JS;1920x1080,1280x720;13\n" +
            "CG623B-W-JS2;1920x1080,1280x720;700\n" +
            "CG623B-W-MG;1920x1080,1280x720;42\n" +
            "CG623B-W-SS;1920x1080,1280x720;5\n" +
            "CG623B-W-ST;1920x1080,1280x720;114\n" +
            "CG623B-W-ST1;1920x1080,1280x720;1\n" +
            "CG623B-W-THR;1920x1080,1280x720;20\n" +
            "CG623B-W-TN;1920x1080,1280x720;19076\n" +
            "CG623B-W-TN2;1920x1080,1280x720;9934\n" +
            "CG623B-W-TN3;1920x1080,1280x720;73\n" +
            "CG623B-W-WSK1;1920x1080,1280x720;13\n" +
            "CG623B-W-WSK2;1920x1080,1280x720;3\n" +
            "CG623B-W-WX;1920x1080,1280x720;10985\n" +
            "CG623B-W-YFWL;1920x1080,1280x720;2\n" +
            "CG630-W;1920x1080,1280x720;1\n" +
            "CG721;1920x1080,1280x720;1\n" +
            "CG721-A-JS;1920x1080,1280x720;615\n" +
            "CG721-A-ZKT;1920x1080,1280x720;98\n" +
            "CG721-DC;1920x1080,1280x720;164\n" +
            "CG721-FTK;1920x1080,1280x720;769\n" +
            "CG721-FTK-HW;1920x1080,1280x720;429\n" +
            "CG721-FTK-THR;1920x1080,1280x720;9\n" +
            "CG721-JA;1920x1080,1280x720;8075\n" +
            "CG721-ST;1920x1080,1280x720;3427\n" +
            "CG721-ST-CHJZ;1920x1080,1280x720;87\n" +
            "CG721-THR;1920x1080,1280x720;6\n" +
            "CG721-TN;1920x1080,1280x720;103\n" +
            "CG721-VT;1920x1080,1280x720;10\n" +
            "CG721C;2304x1296,640x360;9\n" +
            "CG721C-JA;2304x1296,640x360;243\n" +
            "CG721C-JS;2304x1296,640x360;4\n" +
            "CG721C-WX;2304x1296,640x360;373\n" +
            "CG721C1-ST;2304x1296,640x360;1\n" +
            "CG721C1-ST-HG;2304x1296,640x360;1\n" +
            "CQ121;1920x1080,1280x720;12\n" +
            "CQ121B;1920x1080,1280x720;4497\n" +
            "CQ121B-BTJ;1920x1080,1280x720;180\n" +
            "CQ121B-JS;1920x1080,1280x720;1309\n" +
            "CQ121B-JS-JA;1920x1080,1280x720;5071\n" +
            "CQ121B-ST;1920x1080,1280x720;2582\n" +
            "CQ121B-TN;1920x1080,1280x720;3241\n" +
            "CQ121B-TN-YTY;1920x1080,1280x720;512\n" +
            "CQ121B-WSK;1920x1080,1280x720;187\n" +
            "CQ121B-WSK-THR;1920x1080,1280x720;4\n" +
            "CQ121B-ZKT;1920x1080,1280x720;312\n" +
            "CQ121C;2304x1296,640x360;9\n" +
            "CQ121C-AN;2304x1296,640x360;1\n" +
            "CQ121C-BTJ;2304x1296,640x360;15\n" +
            "CQ121C-JS;2304x1296,640x360;2832\n" +
            "CQ121C-JS-JA;2304x1296,640x360;6907\n" +
            "CQ121C-JS-LWS;2304x1296,640x360;277\n" +
            "CQ121C-JS1;2304x1296,640x360;2\n" +
            "CQ121C-PSD;2304x1296,640x360;1\n" +
            "CQ121C-ST;2304x1296,640x360;321\n" +
            "CQ121C-TN;2304x1296,640x360;13483\n" +
            "CQ121C-TN-YTY;2304x1296,640x360;1500\n" +
            "CQ121C-TN2;2304x1296,640x360;2786\n" +
            "CQ121C-TN3;2304x1296,640x360;2033\n" +
            "CQ121C-WSK;2304x1296,640x360;508\n" +
            "CQ121C-WSK-THR;2304x1296,640x360;324\n" +
            "CQ121C-WT;2304x1296,640x360;288\n" +
            "CQ121C-WX;2304x1296,640x360;582\n" +
            "CQ121C1-ST;2304x1296,640x360;3\n" +
            "CQ121C1-ST-HG;2304x1296,640x360;3\n" +
            "CQ240-TN;1080P,360P;1\n" +
            "CQ240B;1080P,360P;1\n" +
            "CQ240B-JS1;1080P,360P;10\n" +
            "CQ240B-JS2;1080P,360P;15\n" +
            "CQ240B-JS3;1080P,360P;5\n" +
            "CQ240B-TN1;1080P,360P;4\n" +
            "CQ240B-XF;1080P,360P;1\n" +
            "DB121A;2048x1536,640x480;562\n" +
            "DB121A-JS;2048x1536,640x480;5\n" +
            "DB121A-US;2048x1536,640x480;9\n" +
            "DB121A-WSK;2048x1536,640x480;46\n" +
            "DB121A-YRJ;2048x1536,640x480;2\n" +
            "DB121A1-JS;2048x1536,640x480;12\n" +
            "DB121A1-WSK;2048x1536,640x480;2\n" +
            "DB121B;2048x1536,640x480;8\n" +
            "DB121B-JS;2048x1536,640x480;59\n" +
            "DB121B-TN;2048x1536,640x480;11\n" +
            "DB121B1;2048x1536,640x480;1\n" +
            "DB121C;2048x1536,640x480;3\n" +
            "DB121C-JS;2048x1536,640x480;4\n" +
            "DB121C-YRJ;2048x1536,640x480;7\n" +
            "DB223A;1920x1080,1280x720;3\n" +
            "DB223A1;1920x1080,1280x720;1\n" +
            "DB223A1-A4X;1920x1080,1280x720;1\n" +
            "DB223C;1920x1080,1280x720;11\n" +
            "DB223C-A4X;1920x1080,1280x720;21";

    //  当前分辨率格式 -> 枚举名称
    public static ImmutableMap<String, String> value2EnumName = ImmutableMap.<String, String>builder()
            .put("2048x1536", "2k")
            .put("2560x1440", "2k")
            .put("2304x1296", "2k")
            .put("1920x1080", "high")
            .put("1280x720", "mid")
            .put("640x480", "mid")
            .put("640x360", "mid")
            .build();

    public static String createModelNo2Options(String modelNo2Resolution) {
        Map<String, Set<Set<String>>> modelNo2OptionsList = new LinkedHashMap<>();
        Map<String, Map<Set<String>, AtomicInteger>> modelNo2Options2Num = new LinkedHashMap<>();
        for (String line : TextUtil.splitToNotBlankList(modelNo2Resolution, '\n')) {
            String[] strs = line.split(";");
            String modelNo = strs[0];
            Set<String> options = TextUtil.splitToNotBlankStream(strs[1], ',')
                    .map(it -> DeviceEnumMappingService.oldEnumName2Value.getOrDefault(it, it)).collect(Collectors.toSet());
            Integer num = Integer.valueOf(strs[2]);
            modelNo2Options2Num.computeIfAbsent(modelNo, it -> new LinkedHashMap<>())
                    .computeIfAbsent(options, k -> new AtomicInteger(0)).addAndGet(num);
            modelNo2OptionsList.computeIfAbsent(modelNo, k -> new LinkedHashSet<>()).add(options);
        }
        int i = 0;
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, Set<Set<String>>> entry : modelNo2OptionsList.entrySet()) {
            log.info("i={} , modelNo={} , num={}", i++, entry.getKey(), entry.getValue().size());
            Map<Set<String>, AtomicInteger> options2Num = modelNo2Options2Num.get(entry.getKey());
            log.info("lines:");
            for (Set<String> options : entry.getValue()) {
                int num = options2Num.get(options).get();
                log.info("------option:{},{}", JSON.toJSONString(options), num);
                for (String option : options) {
                    builder.append(String.format("('%s','%s','%s','%s'),", entry.getKey()
                            , "videoResolution", value2EnumName.get(option), option));
                }
                builder.append("\n");
            }
        }
        return builder.toString();
    }

    @Test
    public void test_createModelNo2Options() {
        String str = createModelNo2Options(modelNo2Resolution);
        System.out.println("\n" + str);
    }

    public static String getDefaultEnumMappingJson() {
        return "{\n" +
                "    \"pirSensitivityOptions\": [\n" +
                "        { \"enumName\": \"auto\", \"value\": -1 },\n" +
                "        { \"enumName\": \"high\", \"value\": 1 },\n" +
                "        { \"enumName\": \"mid\", \"value\": 2 },\n" +
                "        { \"enumName\": \"low\", \"value\": 3 }\n" +
                "    ],\n" +
                "    \"pirRecordTimeOptions\": [\n" +
                "        { \"enumName\": \"auto\", \"value\": -1 }, \n" +
                "        { \"enumName\": \"10s\", \"value\": 10 },\n" +
                "        { \"enumName\": \"15s\", \"value\": 15 },\n" +
                "        { \"enumName\": \"20s\", \"value\": 20 }\n" +
                "    ],\n" +
                "    \"pirCooldownTimeOptions\": [\n" +
                "        { \"enumName\": \"auto\", \"value\": 0 },\n" +
                "        { \"enumName\": \"10s\", \"value\": 10 },\n" +
                "        { \"enumName\": \"30s\", \"value\": 30 },\n" +
                "        { \"enumName\": \"60s\", \"value\": 60 },\n" +
                "        { \"enumName\": \"180s\", \"value\": 180 },\n" +
                "        { \"enumName\": \"300s\", \"value\": 300 }\n" +
                "        { \"enumName\": \"600s\", \"value\": 600 }\n" +
                "        { \"enumName\": \"1800s\", \"value\": 1800 }\n" +
                "        { \"enumName\": \"3600s\", \"value\": 3600 }\n" +
                "    ],\n" +
                "    \"videoResolutionOptions\": [\n" +
                "        { \"enumName\": \"2k\", \"value\": \"2560x1440\" },\n" +
                "        { \"enumName\": \"high\", \"value\": \"1920x1080\" },\n" +
                "        { \"enumName\": \"mid\", \"value\": \"1280x720\" }\n" +
                "    ],\n" +
                "    \"videoAntiFlickerFrequencyOptions\": [\n" +
                "        { \"enumName\": \"50Hz\", \"value\": 50 },\n" +
                "        { \"enumName\": \"60Hz\", \"value\": 60 }\n" +
                "    ],\n" +
                "    \"liveStreamCodecOptions\": [\n" +
                "        { \"enumName\": \"h265\", \"value\": \"h265\" },\n" +
                "        { \"enumName\": \"h264\", \"value\": \"h264\" }\n" +
                "    ],\n" +
                "    \"powerSourceOptions\": [\n" + // ["solar_panel", "plug_in", "battery_power_only", "wired"] */
                "        { \"enumName\": \"solar_panel\", \"value\": \"\" },\n" +
                "        { \"enumName\": \"plug_in\", \"value\": \"\" },\n" +
                "        { \"enumName\": \"battery_power_only\", \"value\": \"\" },\n" +
                "        { \"enumName\": \"wired\", \"value\": \"\" }\n" +
                "    ],\n" +
                "    \"chargeAutoPowerOnCapacityOptions\": [\n" +
                "        { \"enumName\": \"10pct\", \"value\": \"10\" }\n" +
                "    ]\n" +
                "}";
    }

    @Test
    public void test_buildEnumMapping() {
        final JSONObject obj = JSON.parseObject(getDefaultEnumMappingJson());
        List<DeviceModelEnumMappingDO> list = new LinkedList<>();
        for (final String key : obj.keySet()) {
            final String attrName = key.substring(0, key.length() - 7);
            for (final JSONObject option : obj.getJSONArray(key).toJavaList(JSONObject.class)) {
                list.add(new DeviceModelEnumMappingDO().setModelNo("modelNo").setAttributeName(attrName)
                        .setEnumName(option.getString("enumName")).setValue(option.getString("value")));
            }
        }
        final OptionEnumMapping enumMapping = deviceEnumMappingService.buildEnumMapping(list);
        Assert.assertEquals(getDefaultEnumMapping(), enumMapping);
    }

    public static OptionEnumMapping getDefaultEnumMapping() {
        return JSON.parseObject(getDefaultEnumMappingJson(), OptionEnumMapping.class);
    }

    // "deviceSupportResolution" and "receive mqtt" and not
    // ("1080P" or "360P" or "1920x1080,1280x720" or "1440P,720P" or
    //  "2560x1440,1280x720" or "2304x1296,640x360" or "2048x1536,640x480")
    /*
    '1280x720','223104'
    '1920x1080','61023'
    '2048x1536','9'
    '2304x1296','1187'
    '2560x1440','2340'
    '640x360','296'
    '640x480','1'
     */
     /*
    1080P,360P, 493
    1080P,480P, 1
    1080P,720P, 643
    1296P,360P, 7
    1440P,720P, 160
    1536P,480P, 23
    1536P,720P, 1
    1920x1080,1280x720, 126
    1920x1080,640x360, 3
    2048x1536,640x480, 60
    2304x1296,640x360, 79
    2560x1440,1280x720, 11
    720P,1080P, 695
     */
//    @Test
    public void test_buildInsert() {
        String str = getDefaultEnumMappingJson();
        String modelNo = "*";
        JSONObject json = JSON.parseObject(str);
        for (String optionsName : json.keySet()) {
            JSONArray options = json.getJSONArray(optionsName);
            String attributeName = optionsName.replace("Options", "");
            for (int i = 0; i < options.size(); i++) {
                JSONObject option = options.getJSONObject(i);
                String enumName = option.getString("enumName");
                String value = option.getString("value");
                System.out.printf("('%s','%s','%s','%s'),\n", modelNo, attributeName, enumName, value);
            }
        }
    }

    @Mock
    private DeviceModelEnumMappingDAO deviceModelEnumMappingDAO;
    @Mock
    private DeviceManualService deviceManualService;

    @Test
    public void test_getEnumMapping() {
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        final DeviceModelEnumMappingDAO enumMappingDAO = testHelper.getMapper(DeviceModelEnumMappingDAO.class);
        when(deviceModelEnumMappingDAO.queryByModelNo(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(enumMappingDAO));
        when(deviceModelEnumMappingDAO.queryByModelNos(anyCollection())).thenAnswer(AdditionalAnswers.delegatesTo(enumMappingDAO));
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn("CG99999");
            OptionEnumMapping mapping = deviceEnumMappingService.getEnumMapping(sn);
            Assert.assertNotNull(mapping);
//            TestHelper.assertDeepEquals(getDefaultEnumMapping(), mapping);
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn("CB140-JS");
            OptionEnumMapping mapping = deviceEnumMappingService.getEnumMapping(sn);
            Assert.assertNotNull(mapping);
            OptionEnumMapping expectEnumMapping = getDefaultEnumMapping();
            expectEnumMapping.setVideoResolutionOptions(Arrays.asList(
                    new OptionEnumMapping.Entry<String>().setEnumName("high").setValue("1920x1080"),
                    new OptionEnumMapping.Entry<String>().setEnumName("mid").setValue("640x360")
            ));
//            TestHelper.assertDeepEquals(expectEnumMapping, mapping);
        }
    }

    @Test
    public void test_OptionEnumMapping_getEnumName() {
        List<OptionEnumMapping.Entry<Integer>> alarmDurationOptions = new LinkedList<>();
        alarmDurationOptions.add(new OptionEnumMapping.Entry<>("5s", 5));
        alarmDurationOptions.add(new OptionEnumMapping.Entry<>("10s", 10));
        alarmDurationOptions.add(new OptionEnumMapping.Entry<>("15s", 15));

        Assert.assertEquals("5s", OptionEnumMapping.getEnumName(alarmDurationOptions, 5, () -> "5s"));
        Assert.assertEquals("10s", OptionEnumMapping.getEnumName(alarmDurationOptions, 10, () -> "5s"));
        Assert.assertEquals("15s", OptionEnumMapping.getEnumName(alarmDurationOptions, 15, () -> "5s"));
    }

}
