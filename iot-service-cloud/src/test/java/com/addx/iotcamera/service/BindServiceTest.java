package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.BindByApOperationRequest;
import com.addx.iotcamera.bean.app.BindOperationRequest;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.mqtt.Request.MqttDeviceBindRequest;
import com.addx.iotcamera.config.DeviceAntiflickerConfig;
import com.addx.iotcamera.config.PaasTenantConfigTest;
import com.addx.iotcamera.config.apollo.DeviceLanguageConfig;
import com.addx.iotcamera.config.device.DeviceCameraNameConfig;
import com.addx.iotcamera.config.device.DeviceNameConfig;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.enums.BindContentSrc;
import com.addx.iotcamera.enums.DeviceNetType;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.addx.iotcamera.service.device.model.DeviceModelTenantService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.openapi.NodeMatcherAgency;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.addx.iotcamera.service.user.UserAppScoreService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.zxing.WriterException;
import io.prometheus.client.CollectorRegistry;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.assertj.core.util.Sets;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.PropertySource;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.addx.iot.common.enums.ResultCollection.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@PrepareForTest({VernemqPublisher.class, PhosUtils.class})
@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@PowerMockIgnore({"javax.net.ssl.*"})
@EnableConfigurationProperties({ DeviceAntiflickerConfig.class})
@PropertySource(value = {
        "classpath:/notification/deviceAntiflicker.yml"},
        encoding = "utf-8", factory = MixPropertySourceFactory.class)
@Slf4j
public class BindServiceTest {

    @InjectMocks
    private BindService bindService;

    @Mock
    private PaasTenantConfig paasTenantConfig;

    @Mock
    private IDeviceDAO iDeviceDAO;

    @Mock
    private FirmwareService firmwareService;

    @Mock
    private FactoryDataQueryService factoryDataQueryService;

    @Mock
    private  DeviceLanguageConfig deviceLanguageConfig;
    @Mock
    private NotificationService notificationService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private VipService paasVipService;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private ReportLogService reportLogService;

    @Mock
    private RedisService redisService;

    @Mock
    private DeviceSettingService deviceSettingService;
    @Mock
    private OpenApiWebhookService openApiWebhookService;

    @Mock
    private RotationPointService rotationPointService;

    @Mock
    private GeoIpService geoIpService;

    @Mock
    private UserService userService;

    @Mock
    private DeviceService deviceService;
    @Mock
    private DeviceStatusService deviceStatusService;

    @Mock
    private DeviceModelTenantService deviceModelTenantService;

    @Mock
    private DeviceAiSettingsService deviceAiSettingsService;

    @Mock
    private NodeMatcherAgency nodeMatcherAgency;

    @Mock
    private DeviceCameraNameConfig deviceCameraNameConfig;

    @Mock
    private DeviceModelService deviceModelService;

    @Mock
    private DeviceNameConfig deviceNameConfig;

    @Mock
    private UserVipService userVipService;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private UserAppScoreService userAppScoreService;

    @Mock ActivityZoneService activityZoneService;

    @Mock
    private VideoSearchService videoSearchService;

    @Mock
    private UserTierDeviceService userTierDeviceService;
    @Before
    public void setUp() throws Exception {
        TestHelper.injectSpyPropertySource(this);

        PowerMockito.mockStatic(VernemqPublisher.class);
        PowerMockito.mockStatic(PhosUtils.class);
        when(openApiWebhookService.setWebhooksForDeviceBindBegin(anyString(), anyInt(), any())).thenReturn(true);
        when(openApiWebhookService.callWebhookForDeviceBind(anyInt(), anyString(), any(), any())).thenReturn(true);
        when(openApiWebhookService.callWebhookForDevicePir(any(), anyString(), anyString(), any(), anyLong(), anyString())).thenReturn(true);
        when(openApiWebhookService.callWebhookForDeviceUnbind(anyInt(), anyString(), any())).thenReturn(true);
        when(openApiWebhookService.callWebhookForDeviceShareBegin(anyInt(), anyString(), anyInt())).thenReturn(true);

        when(openApiWebhookService.callWebhookForDeviceShareHandle(anyInt(), anyString(), anyInt(), anyInt())).thenReturn(true);
        when(openApiWebhookService.callWebhookForDeviceShareCancel(anyString(), anyInt(), any())).thenReturn(true);

        when(userRoleService.queryUserRolesBySn(anyString())).thenReturn(new UserRoleService.UserRoles(1, Collections.emptyList()));
        when(deviceAiSettingsService.initDeviceAiSettings(any(),anyString())).thenReturn(null);

        JSONObject responseJsonObject = new JSONObject();
        establishTestPostResponse(responseJsonObject);
        DeviceManufactureTableDO deviceManu = responseJsonObject.toJavaObject(DeviceManufactureTableDO.class);
        when(factoryDataQueryService.queryDeviceManufactureBySn(anyString())).thenReturn(deviceManu);
        when(nodeMatcherAgency.setBindContent(any(), any(), any(), anyInt())).thenReturn(true);
        when(nodeMatcherAgency.deleteBindContent(any(), any())).thenReturn(true);

        when(userVipService.queryUserVipInfo(anyInt(), anyString(), any())).thenReturn(UserVipTier.builder().tierId(-1).build());
        PaasTenantConfigTest.mockPaasTenantConfig(this.paasTenantConfig);
        when(deviceLanguageConfig.getConfig()).thenReturn(Maps.newHashMap());
    }

    //    @Test
    @DisplayName("设备解绑-设备已解绑")
    public void deactivateDeviceBySerialNumber_NoBind() {
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(0);
        Result actualResult = Result.Error(DEVICE_UNACTIVATED, "DEVICE_UNACTIVATED");
        Result result = bindService.deactivateDeviceBySerialNumber(any());
        assertEquals(actualResult.getResult(), result.getResult());
    }

    @Test
    @DisplayName("设备解绑")
    public void deactivateDeviceBySerialNumber() {
        when(userRoleService.getDeviceAdminUser(any())).thenReturn(1);

        when(userRoleService.cleanUserRole(any(),any())).thenReturn(1);
        doNothing().when(videoSearchService).clearSearchOptionCache(anyCollection());
        doNothing().when(userTierDeviceService).onRemoveUserDevice(any(),any());
        when(deviceInfoService.deactivateDevice(any())).thenReturn(1);
        when(activityZoneService.deleteActivityZoneBySerialNumber(any())).thenReturn(Result.Success());
        when(userService.queryUserById(any())).thenReturn(new User(){{
            setTenantId("vicoo");
        }});
        when(deviceService.getAllDeviceInfo(any())).thenReturn(null);

        when(userRoleService.queryUserRolesBySn(any(), anyBoolean())).thenReturn(new UserRoleService.UserRoles(1, Collections.emptyList()));
        Result result = bindService.deactivateDeviceBySerialNumber("aaa");
        assertTrue(result.getResult().intValue() == 0);
    }

//    @Test
//    @DisplayName("设备解绑-查询厂测库信息异常")
//    public void deactivateDeviceBySerialNumber_querysn() {
//        when(userRoleService.getDeviceAdminUser(any())).thenReturn(1);
//
//        JSONObject responseJsonObject = new JSONObject();
//        establishTestPostResponse(responseJsonObject);
//        when(postResultService.getPostResult(any(), any(), any())).thenReturn(new Result(0, "Success", responseJsonObject));
//
//        Result actualResult = Result.Error(DEVICE_UNACTIVATED, "DEVICE_UNACTIVATED");
//        Result result = bindService.deactivateDeviceBySerialNumber(any());
//        assertEquals(actualResult.getResult(), result.getResult());
//    }

    @Test
    public void testBinding_whenVicooAndCG1_thenPass() {

    }

    @Test
    public void testBindService_getLatestBindModelNo() {
        int userId = 1;
        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setSerialNumber("123");

        when(iDeviceDAO.queryBindHistoryCompleted(userId)).thenReturn(bindOperationTb);
        when(deviceManualService.getModelNoBySerialNumber(bindOperationTb.getSerialNumber())).thenReturn("1");

        String res = bindService.getLatestBindModelNo(userId);

        assertEquals("1", res);
    }

    @Test
    public void testBindService_getLatestBindModelNo_when_nil() {


        String res = bindService.getLatestBindModelNo(0);
        assertEquals("", res);

        String res1 = bindService.getLatestBindModelNo(1);
        assertEquals("", res1);

    }

    @Test
    public void bindDeviceFromApp() {
        when(iDeviceDAO.initBindOperation(any())).thenReturn(1);
        doNothing().when(reportLogService).sysReportBind(any(), any());

        PowerMockito.when(PhosUtils.getUUID()).thenReturn("123456789");

        Result expectedResult = Result.KVResult("operationId", 0);

        BindOperationRequest request = new BindOperationRequest();
        request.setLanguage("cn");
        request.setLocationId(1);
        request.setTimeZone("Asia/Shanghai");
        AppInfo appInfo = new AppInfo();
        appInfo.setVersion(1);
        request.setApp(appInfo);
        Result actualResult = bindService.bindDeviceFromApp(1, request, new IpInfo("testIp", "testCountryCode"));

        assertEquals(expectedResult.getData().getClass(), actualResult.getData().getClass());
    }

    @Test
    public void checkBindOperationWithRequestNotHandled() {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .answered(0)
                .deviceNetType(DeviceNetType.WIFI.getCode()).bindContentSrc(BindContentSrc.QRCODE.getCode())
                .build();
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Result expectedResult = Result.Error(REQUEST_NOT_HANDLED, "REQUEST_NOT_HANDLED");

        Result actualResult = bindService.checkBindOperation("testId", 1, new IpInfo("testIp", "testCountry"));

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void checkBindOperationWithStatusCodeDEVICE_AUTH_LIMITATION() {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .answered(1)
                .statusCode(DEVICE_AUTH_LIMITATION.getCode())
                .deviceNetType(DeviceNetType.WIFI.getCode()).bindContentSrc(BindContentSrc.QRCODE.getCode())
                .build();
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Result expectedResult = Result.Error(DEVICE_AUTH_LIMITATION, "DEVICE_AUTH_LIMITATION");

        Result actualResult = bindService.checkBindOperation("testId", 1, new IpInfo("testIp", "testCountry"));

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void checkBindOperationWithStatusCodeDEVICE_NO_ACCESS() {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .answered(1)
                .statusCode(DEVICE_NO_ACCESS.getCode())
                .deviceNetType(DeviceNetType.WIFI.getCode()).bindContentSrc(BindContentSrc.QRCODE.getCode())
                .build();
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Result expectedResult = Result.Error(DEVICE_NO_ACCESS, "DEVICE_NO_ACCESS");

        Result actualResult = bindService.checkBindOperation("testId", 1, new IpInfo("testIp", "testCountry"));

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void checkBindOperationWithStatusCodeDEVICE_ACTIVATED() {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .answered(1)
                .statusCode(DEVICE_ACTIVATED.getCode())
                .deviceNetType(DeviceNetType.WIFI.getCode()).bindContentSrc(BindContentSrc.QRCODE.getCode())
                .build();
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Result expectedResult = Result.Error(DEVICE_ACTIVATED, "DEVICE_ACTIVATED");

        Result actualResult = bindService.checkBindOperation("testId", 1, new IpInfo("testIp", "testCountry"));

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void checkBindOperationWithStatusCodeDEVICE_UNACTIVATED() {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .answered(1)
                .statusCode(DEVICE_UNACTIVATED.getCode())
                .deviceNetType(DeviceNetType.WIFI.getCode()).bindContentSrc(BindContentSrc.QRCODE.getCode())
                .build();
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Result expectedResult = Result.Error(DEVICE_UNACTIVATED, "DEVICE_UNACTIVATED");

        Result actualResult = bindService.checkBindOperation("testId", 1, new IpInfo("testIp", "testCountry"));

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void checkBindOperationWithStatusCodeDEVICE_OFFLINE() {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .answered(1)
                .statusCode(DEVICE_OFFLINE.getCode())
                .deviceNetType(DeviceNetType.WIFI.getCode()).bindContentSrc(BindContentSrc.QRCODE.getCode())
                .build();
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Result expectedResult = Result.Error(DEVICE_OFFLINE, "DEVICE_OFFLINE");

        Result actualResult = bindService.checkBindOperation("testId", 1, new IpInfo("testIp", "testCountry"));

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void checkBindOperationWithStatusCodeINVALID_REQUEST_ID() {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .answered(1)
                .statusCode(INVALID_REQUEST_ID.getCode())
                .deviceNetType(DeviceNetType.WIFI.getCode()).bindContentSrc(BindContentSrc.QRCODE.getCode())
                .build();
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Result expectedResult = Result.Error(INVALID_REQUEST_ID, "INVALID_REQUEST_ID");

        Result actualResult = bindService.checkBindOperation("testId", 1, new IpInfo("testIp", "testCountry"));

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void checkBindOperationWithStatusCodeREQUEST_EXPIRED() {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .answered(1)
                .statusCode(REQUEST_EXPIRED.getCode())
                .deviceNetType(DeviceNetType.WIFI.getCode()).bindContentSrc(BindContentSrc.QRCODE.getCode())
                .build();
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Result expectedResult = Result.Error(REQUEST_EXPIRED, "REQUEST_EXPIRED");

        Result actualResult = bindService.checkBindOperation("testId", 1, new IpInfo("testIp", "testCountry"));

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void checkBindOperationWithStatusCodeUNKNOWN_ERROR() {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .answered(1)
                .statusCode(-1)
                .deviceNetType(DeviceNetType.WIFI.getCode()).bindContentSrc(BindContentSrc.QRCODE.getCode())
                .build();
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Result expectedResult = Result.Failure("Unknown Error");

        Result actualResult = bindService.checkBindOperation("testId", 1, new IpInfo("testIp", "testCountry"));

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void checkBindOperationWithStatusCodeSUCCESS() {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .answered(1)
                .statusCode(SUCCESS.getCode())
                .serialNumber("testSerialNumber")
                .deviceNetType(DeviceNetType.WIFI.getCode()).bindContentSrc(BindContentSrc.QRCODE.getCode())
                .build();
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Result expectedResult = Result.KVResult("serialNumber", "testSerialNumber");

        Result actualResult = bindService.checkBindOperation("testId", 1, new IpInfo("testIp", "testCountry"));

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void bindDeviceFromCameraWithINVALID_REQUEST_ID() {

        MqttDeviceBindRequest request = new MqttDeviceBindRequest();
        int testId = 123456;
        request.setId(testId);
        request.getValue().setRid("testRid");
        request.setSerialNumber("testSerialNumber");
        when(iDeviceDAO.selectOperation(any())).thenReturn(null);

        Result expectedResult = Result.Error(INVALID_REQUEST_ID, "INVALID_REQUEST_ID");

        Result actualResult = null;
        try {
            actualResult = bindService.bindDeviceFromCamera(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void bindDeviceFromCameraWithStoredDeviceExist() {

        MqttDeviceBindRequest request = new MqttDeviceBindRequest();
        int testId = 123456;
        String modelNo = "modelNo";
        request.setId(testId);
        request.getValue().setRid("testRid");
        request.setSerialNumber("testSerialNumber");
        request.getValue().setModelNo(modelNo);

        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setRequestTime(1);
        bindOperationTb.setUserId(1);
        bindOperationTb.setDeviceNetType(DeviceNetType.WIFI.getCode());
        bindOperationTb.setBindContentSrc(BindContentSrc.QRCODE.getCode());
        bindOperationTb.setTenantId(TENANTID_VICOO);
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setActivated(0);
        PowerMockito.when(PhosUtils.getUTCStamp()).thenReturn(300);

        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setAdminId(1);
        when(userRoleService.getDeviceAdminUserRole(any())).thenReturn(userRoleDO);

        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("modelNo");
        when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(new HashSet<>(Arrays.asList(modelNo)));

        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        when(iDeviceDAO.updateBindOperation(any())).thenReturn(1);

        when(iDeviceDAO.activateDevice(any())).thenReturn(1);

        doNothing().when(redisService).set(any(), any());

        doNothing().when(deviceStatusService).initDeviceStatusForBind(any(), any());

        when(userRoleService.saveUserRole(any())).thenReturn(1);

        when(firmwareService.insertDeviceOTA(any())).thenReturn(1);

        try {
            PowerMockito.when(VernemqPublisher.bindOperationResponse(any(), any())).thenReturn(true);
        } catch (IdNotSetException e) {
            e.printStackTrace();
        }

        //
        doNothing().when(userAppScoreService).userAppScoreMomentDeviceBind(any(),any());

        Result expectedResult = Result.Success();

        Result actualResult = null;
        try {
            actualResult = bindService.bindDeviceFromCamera(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    private void establishTestPostResponse(JSONObject responseJsonObject) {
        try {
            responseJsonObject.put("userSn", "AIC20190319001");
            responseJsonObject.put("serialNumber", "AIC20190319001");
            responseJsonObject.put("activated", 0);
            responseJsonObject.put("modelNo", "CG1");
            responseJsonObject.put("originModelNo", "CG1");
            responseJsonObject.put("firmwareId", "0.1.1");
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    @Test
    public void bindDeviceFromCameraWithDEVICE_ACTIVATED() throws Exception {
        PrometheusMetricsUtil prometheusMetricsUtil = new PrometheusMetricsUtil();

        Field collectorRegistryField = PrometheusMetricsUtil.class.getDeclaredField("collectorRegistry");
        collectorRegistryField.setAccessible(true);
        collectorRegistryField.set(prometheusMetricsUtil, CollectorRegistry.defaultRegistry);

        prometheusMetricsUtil.initMetrics();

        MqttDeviceBindRequest request = new MqttDeviceBindRequest();
        int testId = 123456;
        String serialNumber = "testSerialNumber";
        String userSn = "userSn";
        request.setId(testId);
        request.getValue().setRid("testRid");
        request.getValue().setModelNo("CG1");
        request.getValue().setUserSn(userSn);
        request.getValue().setSerialNumber(serialNumber);
        request.setSerialNumber(serialNumber);

        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setRequestTime(PhosUtils.getUTCStamp() - 10);
        bindOperationTb.setUserId(0);
        bindOperationTb.setTenantId("vicoo");
        bindOperationTb.setDeviceNetType(DeviceNetType.WIFI.getCode());
        bindOperationTb.setBindContentSrc(BindContentSrc.QRCODE.getCode());
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setAdminId(0);
        deviceDO.setActivated(1);

        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        DeviceManufactureTableDO deviceManufactureTableDO = new DeviceManufactureTableDO();
        deviceManufactureTableDO.setUserSn(userSn);
        deviceManufactureTableDO.setSerialNumber(serialNumber);
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(any())).thenReturn(deviceManufactureTableDO);

        when(iDeviceDAO.updateBindOperation(any())).thenReturn(1);
        Set<String> bindSet = Sets.newHashSet();
        bindSet.add("CG1");
        when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(bindSet);

        when(iDeviceDAO.updateBindOperation(any())).thenReturn(1);
        doNothing().when(rotationPointService).deleteRotationPointBySerialNumber(any());
        doReturn(true).when(notificationService).initMessageNotificationSettings(anyString(), anyInt());

        when(paasVipService.isVipDevice(any(), anyString())).thenReturn(false);
//        when(deviceVipService.isVipDevice(any())).thenReturn(false);
        try {
            PowerMockito.when(VernemqPublisher.bindOperationResponse(any(), any())).thenReturn(true);
        } catch (IdNotSetException e) {
            e.printStackTrace();
        }

        doNothing().when(deviceSettingService).updateDeviceSetting(any());
        //when(bindingRules.getRules()).thenReturn(bindingRulesTest.getRules());
        when(geoIpService.getCountryCode(any())).thenReturn("CN");
        //when(deviceAntiflickerConfig.getConfig()).thenReturn(deviceAntiflickerConfigTest.getConfig());
        when(userService.queryUserById(any())).thenReturn(null);
        Result expectedResult = Result.Success();

        Result actualResult = null;
        try {
            actualResult = bindService.bindDeviceFromCamera(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void bindDeviceFromCameraWithREQUEST_EXPIRED() {

        MqttDeviceBindRequest request = new MqttDeviceBindRequest();
        int testId = 123456;
        request.setId(testId);
        request.getValue().setRid("testRid");
        request.getValue().setUserSn("1");
        request.getValue().setFirmwareId("1");
        request.getValue().setModelNo("CG1");
        request.setSerialNumber("testSerialNumber");

        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setRequestTime(0);
        bindOperationTb.setDeviceNetType(DeviceNetType.WIFI.getCode());
        bindOperationTb.setBindContentSrc(BindContentSrc.QRCODE.getCode());
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setActivated(0);

        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        PowerMockito.when(PhosUtils.getUTCStamp()).thenReturn(3601);

        when(iDeviceDAO.updateBindOperation(any())).thenReturn(1);

        Result expectedResult = Result.Error(REQUEST_EXPIRED, "REQUEST_EXPIRED");

        Result actualResult = null;
        try {
            actualResult = bindService.bindDeviceFromCamera(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void bindDeviceFromCameraWithIdNotSetException() {

        MqttDeviceBindRequest request = new MqttDeviceBindRequest();
        int testId = 123456;
        request.setId(testId);
        String userSn = "userSn";
        String serialNumber = "testSerialNumber";
        request.getValue().setRid("testRid");
        request.getValue().setModelNo("CG1");
        request.getValue().setUserSn(userSn);
        request.getValue().setSerialNumber(serialNumber);
        request.setSerialNumber(serialNumber);

        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setRequestTime(1);
        bindOperationTb.setTenantId("vicoo");
        bindOperationTb.setUserId(1);
        bindOperationTb.setDeviceNetType(DeviceNetType.WIFI.getCode());
        bindOperationTb.setBindContentSrc(BindContentSrc.QRCODE.getCode());
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setActivated(0);
        PowerMockito.when(PhosUtils.getUTCStamp()).thenReturn(300);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        when(iDeviceDAO.updateBindOperation(any())).thenReturn(1);

        when(iDeviceDAO.activateDevice(any())).thenReturn(1);

        DeviceManufactureTableDO deviceManufactureTableDO = new DeviceManufactureTableDO();
        deviceManufactureTableDO.setUserSn(userSn);
        deviceManufactureTableDO.setSerialNumber(serialNumber);
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(any())).thenReturn(deviceManufactureTableDO);

        Set<String> bindSet = Sets.newHashSet();
        bindSet.add("CG1");
        when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(bindSet);

        doNothing().when(redisService).set(any(), any());

        doNothing().when(deviceStatusService).initDeviceStatusForBind(any(), any());

        when(userRoleService.saveUserRole(any())).thenReturn(1);

        when(firmwareService.insertDeviceOTA(any())).thenReturn(1);

        //when(bindingRules.getRules()).thenReturn(bindingRulesTest.getRules());
        when(geoIpService.getCountryCode(any())).thenReturn("CN");
//        when(deviceAntiflickerConfig.getConfig()).thenReturn(deviceAntiflickerConfigTest.getConfig());

        try {
            PowerMockito.doThrow(new IdNotSetException()).when(VernemqPublisher.class);
            VernemqPublisher.bindOperationResponse(Mockito.anyString(), Mockito.any());
        } catch (IdNotSetException e) {
            e.printStackTrace();
        }

        Result expectedResult = Result.Success();

        Result actualResult = null;
        try {
            actualResult = bindService.bindDeviceFromCamera(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void bindDeviceFromCameraWithMqttException() {

        MqttDeviceBindRequest request = new MqttDeviceBindRequest();
        int testId = 123456;
        request.setId(testId);
        String userSn = "userSn";
        String serialNumber = "serialNumber";
        request.getValue().setRid("testRid");
        request.getValue().setModelNo("CG1");
        request.getValue().setSerialNumber(serialNumber);
        request.setSerialNumber(serialNumber);

        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setRequestTime(1);
        bindOperationTb.setTenantId("vicoo");
        bindOperationTb.setUserId(1);
        bindOperationTb.setDeviceNetType(DeviceNetType.WIFI.getCode());
        bindOperationTb.setBindContentSrc(BindContentSrc.QRCODE.getCode());
        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setActivated(0);
        PowerMockito.when(PhosUtils.getUTCStamp()).thenReturn(300);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        DeviceManufactureTableDO deviceManufactureTableDO = new DeviceManufactureTableDO();
        deviceManufactureTableDO.setUserSn(userSn);
        deviceManufactureTableDO.setSerialNumber(serialNumber);
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(any())).thenReturn(deviceManufactureTableDO);

        when(iDeviceDAO.updateBindOperation(any())).thenReturn(1);

        when(iDeviceDAO.activateDevice(any())).thenReturn(1);

        doNothing().when(redisService).set(any(), any());

        Set<String> bindSet = Sets.newHashSet();
        bindSet.add("CG1");
        when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(bindSet);

        doNothing().when(deviceStatusService).initDeviceStatusForBind(any(), any());

        when(userRoleService.saveUserRole(any())).thenReturn(1);

        when(firmwareService.insertDeviceOTA(any())).thenReturn(1);

        //when(bindingRules.getRules()).thenReturn(bindingRulesTest.getRules());
        when(geoIpService.getCountryCode(any())).thenReturn("CN");
        //when(deviceAntiflickerConfig.getConfig()).thenReturn(deviceAntiflickerConfigTest.getConfig());

        try {
            PowerMockito.doThrow(new Exception("")).when(VernemqPublisher.class);
            VernemqPublisher.bindOperationResponse(Mockito.anyString(), Mockito.any());
        } catch (Exception e) {
            e.printStackTrace();
        }

        Result expectedResult = Result.Success();

        Result actualResult = null;
        try {
            actualResult = bindService.bindDeviceFromCamera(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void bindDeviceFromCameraWithStoredDeviceNotExist() {

        MqttDeviceBindRequest request = new MqttDeviceBindRequest();
        int testId = 123456;
        String userSn = "userSn";
        String serialNumber = "testSerialNumber";
        request.setId(testId);
        request.getValue().setRid("testRid");
        request.getValue().setModelNo("CG1");
        request.getValue().setUserSn(userSn);
        request.getValue().setSerialNumber(serialNumber);
        request.setSerialNumber(serialNumber);

        BindOperationTb bindOperationTb = new BindOperationTb();
        bindOperationTb.setRequestTime(PhosUtils.getUTCStamp() - 10);
        bindOperationTb.setTenantId("vicoo");
        bindOperationTb.setUserId(1);
        bindOperationTb.setDeviceNetType(DeviceNetType.WIFI.getCode());
        bindOperationTb.setBindContentSrc(BindContentSrc.QRCODE.getCode());

        when(iDeviceDAO.selectOperation(any())).thenReturn(bindOperationTb);

        Set<String> bindSet = Sets.newHashSet();
        bindSet.add("CG1");
        when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(bindSet);

        when(deviceService.getAllDeviceInfo(any())).thenReturn(null);

        when(iDeviceDAO.updateBindOperation(any())).thenReturn(1);

        when(iDeviceDAO.activateDevice(any())).thenReturn(1);

        DeviceManufactureTableDO deviceManufactureTableDO = new DeviceManufactureTableDO();
        deviceManufactureTableDO.setUserSn(userSn);
        deviceManufactureTableDO.setSerialNumber(serialNumber);
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(any())).thenReturn(deviceManufactureTableDO);

        doNothing().when(redisService).set(any(), any());

        doNothing().when(deviceStatusService).initDeviceStatusForBind(any(), any());

        when(userRoleService.saveUserRole(any())).thenReturn(1);

        when(firmwareService.insertDeviceOTA(any())).thenReturn(1);

        //when(bindingRules.getRules()).thenReturn(bindingRulesTest.getRules());
        when(geoIpService.getCountryCode(any())).thenReturn("CN");
        // when(deviceAntiflickerConfig.getConfig()).thenReturn(deviceAntiflickerConfigTest.getConfig());

        try {
            PowerMockito.when(VernemqPublisher.bindOperationResponse(any(), any())).thenReturn(true);
        } catch (IdNotSetException e) {
            e.printStackTrace();
        }



        Result expectedResult = Result.Success();

        Result actualResult = null;
        try {
            actualResult = bindService.bindDeviceFromCamera(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

        assertEquals(expectedResult.getResult(), actualResult.getResult());
    }

    @Test
    public void queryDeviceBindByApTextResultTest() throws IOException, WriterException {
        when(iDeviceDAO.initBindOperation(any())).thenReturn(1);
        doNothing().when(reportLogService).sysReportBind(any(), any());

        PowerMockito.when(PhosUtils.getUUID()).thenReturn("123456789");

        BindByApOperationRequest bindByApOperationRequest = new BindByApOperationRequest();
        bindByApOperationRequest.setDeviceLanguage("ZH");
        bindByApOperationRequest.setNetworkName("addx");
        bindByApOperationRequest.setPassword("");
        bindByApOperationRequest.setLocationId(1);

        AppInfo appInfo = new AppInfo();
        appInfo.setVersion(1);
        bindByApOperationRequest.setApp(appInfo);

        IpInfo ipInfo = new IpInfo();
        ipInfo.setIp("127.0.0.1");
        ipInfo.setCountryCode("CN");
        Result result = bindService.queryDeviceBindByApTextResult(1, bindByApOperationRequest, ipInfo);

        assertNotNull(result.getData());
        assertNotNull(((Map)result.getData()).get("operationId"));
        assertNotNull(((Map)result.getData()).get("bindText"));
    }

    @Test
    @DisplayName("绑定后设备初始化名称-用户不存在")
    public void initBindDeviceName_no_user_Test(){
        Integer userId = 1;
        String modelNo = "CG1";
        when(userService.queryUserById(any())).thenReturn(null);
        String expectedResult = "";
        String actualResult = bindService.initDeviceName(userId,modelNo);

        assertEquals(expectedResult,actualResult);
    }

    private String modelNoCg621whdg = "G-Homa Smart Camera";
    @Test
    @DisplayName("绑定后设备初始化名称-CG621-W-HDG")
    public void initBindDeviceName_DisplayModel_Test(){
        Integer userId = 1;
        String modelNo = "CG621-W-HDG";

        User user = new User();
        user.setLanguage("zh");
        Map<String, String> config = Maps.newHashMap();
        config.put(modelNo,modelNoCg621whdg);

        when(userService.queryUserById(any())).thenReturn(user);
        when(deviceCameraNameConfig.getConfig()).thenReturn(config);

        String expectedResult = modelNoCg621whdg;
        String actualResult = bindService.initDeviceName(userId,modelNo);

        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("绑定后设备初始化名称-智能摄像头")
    public void initBindDeviceName_camera(){
        Integer userId = 1;
        String modelNo = "CG621-W";

        User user = new User();
        user.setLanguage("zh");

        Map<String, String> config = Maps.newHashMap();
        config.put("CG621-W-HDG",modelNoCg621whdg);

        when(userService.queryUserById(any())).thenReturn(user);
        when(deviceCameraNameConfig.getConfig()).thenReturn(config);
        when(deviceModelService.queryDeviceModelCategory(modelNo)).thenReturn(DeviceModelCategoryEnums.CAMERA.getCode());

        // 这个方法的测试用例在 DeviceNameConfigTest 中
        when(deviceNameConfig.queryDeviceName(any(),anyInt())).thenReturn("智能摄像机");

        String expectedResult = "智能摄像机";
        String actualResult = bindService.initDeviceName(userId,modelNo);
        assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("绑定时设备校验_设备不在厂测")
    public void initBindDeviceVerify_DeviceNoExist(){
        String userSn = "userSn";
        String serialNumber = "serialNumber";

        String modelNo = "modelNo";
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(userSn)).thenReturn(null);
        Integer expectedResult = Result.failureFlag;
        Integer actualResult = bindService.verifyDeviceModelInfo(userSn,serialNumber, TENANTID_VICOO,modelNo).getResult();
        assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("绑定时设备校验-UUID 为空")
    public void initBindDeviceVerify_DeviceSerialNumberNull(){
        String userSn = "userSn";
        String serialNumber = "serialNumber";
        String modelNo = "modelNo";
        DeviceManufactureTableDO deviceManufactureTableDO = new DeviceManufactureTableDO();

        when(factoryDataQueryService.queryDeviceManufactureByUserSn(userSn)).thenReturn(deviceManufactureTableDO);
        Integer expectedResult = Result.failureFlag;
        Integer actualResult = bindService.verifyDeviceModelInfo(userSn,serialNumber, TENANTID_VICOO,modelNo).getResult();
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("绑定时设备校验-uuid不相等")
    public void initBindDeviceVerify_DeviceUserEqual_tenantNotContains(){
        String userSn = "userSn";
        String serialNumber = "serialNumber";
        String modelNo = "modelNo";

        DeviceManufactureTableDO deviceManufactureTableDO = new DeviceManufactureTableDO();
        deviceManufactureTableDO.setSerialNumber(serialNumber);
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(userSn)).thenReturn(deviceManufactureTableDO);

        when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(Sets.newHashSet());

        Integer expectedResult = Result.failureFlag;
        Integer actualResult = bindService.verifyDeviceModelInfo(userSn,serialNumber, TENANTID_VICOO,modelNo).getResult();
        assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("绑定时设备校验-uuid不相等")
    public void test_initBindDeviceVerify(){
        String userSn = "userSn";
        String serialNumber = "serialNumber";
        String modelNo = "modelNo";

        DeviceManufactureTableDO deviceManufactureTableDO = new DeviceManufactureTableDO();
        deviceManufactureTableDO.setSerialNumber(serialNumber);
        when(factoryDataQueryService.queryDeviceManufactureByUserSn(userSn)).thenReturn(deviceManufactureTableDO);

        when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(Sets.newHashSet(Arrays.asList(modelNo)));

        Integer expectedResult = Result.successFlag;
        Integer actualResult = bindService.verifyDeviceModelInfo(userSn,serialNumber, TENANTID_VICOO,modelNo).getResult();
        assertEquals(expectedResult,actualResult);
    }

    @Test
    public void testDeactivateOwnDeviceBySerialNumber() {
        String sn = "test";
        Integer userId = 1;
        when(userRoleService.queryUserRolesBySn(any(), anyBoolean())).thenReturn(new UserRoleService.UserRoles(null,null));
        Result actualResult = Result.Error(DEVICE_UNACTIVATED, "DEVICE_UNACTIVATED");
        Result result = bindService.deactivateOwnDeviceBySerialNumber(sn, userId);
        assertEquals(actualResult.getResult(), result.getResult());

        when(userRoleService.queryUserRolesBySn(any(), anyBoolean())).thenReturn(new UserRoleService.UserRoles(1,new ArrayList<>()));
        bindService.deactivateOwnDeviceBySerialNumber(sn, userId);
    }

    @Test
    public void test_verifyDeviceSnInfo(){
        String userSn = "userSn";
        String serialNumber = "serialNumber";
        String macAddress = "";
        DeviceManualDO deviceManualDO = null;
        boolean actualResult = false;
        {
            when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(null);
            actualResult = bindService.verifyDeviceSnInfo(userSn,serialNumber,macAddress);
            Assert.assertFalse(actualResult);
        }
        deviceManualDO = new DeviceManualDO();
        deviceManualDO.setSerialNumber(serialNumber);
        {
            deviceManualDO.setUserSn(userSn + "test");
            when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(deviceManualDO);
            actualResult = bindService.verifyDeviceSnInfo(userSn,serialNumber,macAddress);
            Assert.assertFalse(actualResult);
        }
        deviceManualDO.setUserSn(userSn);
        {
            when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(deviceManualDO);
            actualResult = bindService.verifyDeviceSnInfo(userSn,serialNumber,macAddress);
            Assert.assertTrue(actualResult);
        }
        {

            deviceManualDO.setMacAddress(macAddress + "test");
            when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(deviceManualDO);
            actualResult = bindService.verifyDeviceSnInfo(userSn,serialNumber,macAddress);
            Assert.assertFalse(actualResult);
        }
        {
            macAddress = "macAddress";
            deviceManualDO.setMacAddress(macAddress + "test");
            when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(deviceManualDO);
            actualResult = bindService.verifyDeviceSnInfo(userSn,serialNumber,macAddress);
            Assert.assertFalse(actualResult);
        }
        {
            macAddress = "macAddress";
            deviceManualDO.setMacAddress(macAddress);
            when(deviceManualService.getDeviceManualBySerialNumber(any())).thenReturn(deviceManualDO);
            actualResult = bindService.verifyDeviceSnInfo(userSn,serialNumber,macAddress);
            Assert.assertTrue(actualResult);
        }
    }
}
