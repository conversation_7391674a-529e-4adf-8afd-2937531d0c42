package com.addx.iotcamera.service.deviceplatform.alexa.safemo;

import com.addx.iotcamera.service.RedisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.integration.redis.util.RedisLockRegistry;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest(RedisLockRegistry.class)
public class AlexaSafemoRelationServiceTest {

    @InjectMocks
    AlexaSafemoRelationService alexaSafemoRelationService;
    @Mock
    private RedisService redisService;
    @Mock
    private RedisLockRegistry redisLockRegistry;
    @Mock
    Lock lock;

    @Before
    public void init() throws Exception {
        PowerMockito.whenNew(RedisLockRegistry.class).withAnyArguments().thenReturn(redisLockRegistry);
    }

    @Test
    public void syncUserAndKissRelation() throws InterruptedException {
        Integer adminUserId = 1;
        String kissIp = "1";
        String redisKey = "alexa:safemo:user:kiss:relation:" + adminUserId;
        List<ValueExpirationTime> kissIpList = new ArrayList<>();
        kissIpList.add(new ValueExpirationTime().setExpirationTimeMillis(100).setValue("127.0.0.1"));

        // 条件1
        alexaSafemoRelationService.syncUserAndKissRelation(null, null);

        // 条件2
        when(redisLockRegistry.obtain(redisKey + ":lockKey")).thenReturn(lock);
        when(lock.tryLock(10, TimeUnit.SECONDS)).thenReturn(false);
        alexaSafemoRelationService.syncUserAndKissRelation(adminUserId, kissIp);

        // 条件3
        when(redisLockRegistry.obtain(redisKey + ":lockKey")).thenReturn(lock);
        when(lock.tryLock(10, TimeUnit.SECONDS)).thenReturn(true);
        when(redisService.get(redisKey)).thenReturn(JSON.toJSONString(kissIpList));
        alexaSafemoRelationService.syncUserAndKissRelation(adminUserId, kissIp);
    }

    @Test
    public void getKissIpListByAdminUserId() {
        Integer adminUserId = 1;
        String redisKey = "alexa:safemo:user:kiss:relation:" + adminUserId;
        List<ValueExpirationTime> kissIpList = new ArrayList<>();
        kissIpList.add(new ValueExpirationTime().setExpirationTimeMillis(100).setValue("127.0.0.1"));

        // 条件1
        Assert.assertTrue(alexaSafemoRelationService.getKissIpListByAdminUserId(null).isEmpty());

        // 条件2
        when(redisService.get(redisKey)).thenReturn(null);
        Assert.assertTrue(alexaSafemoRelationService.getKissIpListByAdminUserId(adminUserId).isEmpty());

        // 条件3
        when(redisService.get(redisKey)).thenReturn(JSON.toJSONString(kissIpList));
        Assert.assertNotNull(alexaSafemoRelationService.getKissIpListByAdminUserId(adminUserId));
    }
}