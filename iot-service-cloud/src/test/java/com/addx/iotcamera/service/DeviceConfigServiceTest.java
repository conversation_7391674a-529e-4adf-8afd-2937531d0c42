package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.device.DeviceConfigRequest;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.device.model.DeviceModelBatteryDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.openapi.*;
import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.bean.video.StorageDest;
import com.addx.iotcamera.config.*;
import com.addx.iotcamera.config.apollo.DnsServerConfig;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.dynamo.dao.DeviceConfigDAO;
import com.addx.iotcamera.dynamo.dao.TenantAwsConfigDAO;
import com.addx.iotcamera.enums.UserType;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.model.DeviceModelBatteryService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.openapi.PaasVipProductService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.service.video.StorageAllocateService;
import com.addx.iotcamera.service.video.StorageParamService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.lang3.ObjectUtils;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.integration.support.locks.ExpirableLockRegistry;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

import static com.addx.iotcamera.service.openapi.OpenApiConfigService.PAAS_OWNED_TENANT_ID;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceConfigServiceTest {

    @InjectMocks
    private DeviceConfigService deviceConfigService;

    @Mock
    private StorageParamService storageParamService;
    @Mock
    private StorageAllocateService storageAllocateService;
    @Mock
    private PaasTenantConfig paasTenantConfig;
    @Mock
    private FreeStorageService freeStorageService;

    @Mock
    private UserVipService userVipService;
    @Mock
    private PlanSupportService planSupportService;

    @Mock
    private VipService paasVipService;

    @Mock
    private PaasVipProductService paasVipProductService;

    @Mock
    private UserService userService;

    @Mock
    private UserRoleService userRoleService;

    @InjectMocks
    private OpenApiConfigService deviceThirdConfigService;

    @Mock
    private DnsServerConfig dnsServerConfig;

    @Mock
    private RedisService redisService;

    @Mock
    private DeviceSettingService deviceSettingService;

    @Mock
    private DeviceConfigDAO deviceConfigDAO;

    @Mock
    private MqttSender mqttSender;

    @Mock
    private TenantAwsConfigDAO tenantAwsConfigDAO;

    @Mock
    private DeviceService deviceService;

    @Mock
    private VideoSliceConfig videoSliceConfig;

    @Mock
    private S3 s3;

    @Mock
    private S3Config s3Config;

    @Mock
    private GlobalDeviceConfig globalDeviceConfig;

    @Mock
    private ExpirableLockRegistry redisLockRegistry;

    @Mock
    private DeviceModelConfigService deviceModelConfigService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private DeviceModelBatteryService deviceModelBatteryService;
    @Mock
    private TenantSettingService tenantSettingService;
    @Mock
    private ForkJoinPool pool;

    @Before
    public void init() throws Exception {
        final ForkJoinPool forkJoinPool = new ForkJoinPool(1);
        when(pool.submit((Callable) any())).thenAnswer(AdditionalAnswers.delegatesTo(forkJoinPool));
        when(deviceInfoService.getDeviceSupport(any())).thenReturn(new CloudDeviceSupport());
        when(deviceModelBatteryService.queryDeviceModelBattery(any(),any())).thenReturn(new DeviceModelBatteryDO());
        when(tenantSettingService.getTenantSettingByTenantId(any())).thenReturn(new TenantSetting());
        when(dnsServerConfig.getConfig()).thenReturn(Arrays.asList("114.114.114.114"));
        PaasTenantConfigTest.mockPaasTenantConfig(this.paasTenantConfig);
        Field deviceThirdConfigServiceField = deviceConfigService.getClass().getDeclaredField("deviceThirdConfigService");
        deviceThirdConfigServiceField.setAccessible(true);
        deviceThirdConfigServiceField.set(deviceConfigService, deviceThirdConfigService);

        Field deviceConfigServiceField = deviceThirdConfigService.getClass().getDeclaredField("deviceConfigService");
        deviceConfigServiceField.setAccessible(true);
        deviceConfigServiceField.set(deviceThirdConfigService, deviceConfigService);
        deviceThirdConfigService.init();

        when(s3.getBucket()).thenReturn("bucket");
        when(s3Config.getClientRegion()).thenReturn("cn-test");
        when(tenantAwsConfigDAO.queryByTenantId(any())).thenReturn(TenantAwsConfig.builder().build());

        when(storageAllocateService.getVipStorageDest(any(), anyString(), any(), any())).thenReturn(new StorageDest());
        when(storageAllocateService.getNoVipStorageDest(any(), anyString(), any(), any())).thenReturn(new StorageDest());

        when(redisLockRegistry.obtain(any())).thenAnswer(it -> new ReentrantLock(false));
        when(freeStorageService.getPaasFreeStorage(any())).thenReturn(new UserFreeStorage());

        //
        when(deviceModelConfigService.queryIssuedFilesByModelNo(anyString())).thenReturn(Arrays.asList());
        when(deviceManualService.getModelNoBySerialNumber(anyString())).thenReturn("modelNo");
        when(storageParamService.deviceNeedCloudStorageParams(any())).thenReturn(true);

        when(planSupportService.isNoPlanAndDeviceSupportImageEvent(any(), any())).thenReturn(new Tuple2<>(false, false));
    }

    @Mock
    private UserTierDeviceService userTierDeviceService;

    @Mock
    private TierService tierService;

    @Test
    public void testRefreshS3VideoStorageDays() {
        String serialNumber = "sn_01";

        when(userRoleService.getDeviceAdminUser(any())).thenReturn(1);
        when(userService.queryUserById(anyInt())).thenReturn(new User(){{
            setTenantId("testapp");
            setType(UserType.REGISTER.getCode());
        }});

        when(userService.queryTenantIdById(anyInt())).thenReturn("testapp");

        when(userVipService.queryCurrentTierTime(anyInt(), anyLong())).thenReturn(Long.valueOf(System.currentTimeMillis() / 1000 - 2 * 24 * 60 * 60).intValue());
        when(userVipService.queryUserVipEndTime(anyInt(), any(),any())).thenReturn(Long.valueOf(System.currentTimeMillis() / 1000 + 60 * 60).intValue());

        when(deviceConfigDAO.queryBySnAndTenantIdOrPaasOwned(any(), any())).thenReturn(OpenApiDeviceConfig.fromJson("{\"tenantId\":\"testapp\", \"iotService\":{\"baseUrl\":\"http://any\"}, \"mqtt\":{\"domain\": \"addx.live\"}}"));
        when(tenantAwsConfigDAO.queryByTenantId(any())).thenReturn(new TenantAwsConfig());

        when(deviceService.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().build());

        Result result = deviceConfigService.refreshS3VideoStorageDays(serialNumber);
        Assert.assertTrue(result.getResult().intValue() == 0);
        log.info("refreshS3VideoStorageDays by userVip {}", JsonUtil.toJson(result));

        when(userRoleService.getDeviceAdminUser(any())).thenAnswer((Answer<Integer>) invocation -> invocation.getArgument(0) == null?0:1);
        when(paasVipService.queryLastDeviceVip(any(), any())).then((invocation) -> {
            return new DeviceVipLog() {{
                setProductId("vip2");
                setVipLevel(1);
            }};
        });
        when(paasVipProductService.queryAllPaasVipProduct()).thenReturn(Collections.singletonList(new PaasVipProduct() {{
            setLookBackDays(7);
            setId("vip2");
        }}));
        result = deviceConfigService.refreshS3VideoStorageDays(serialNumber);
        Assert.assertTrue(result.getResult().intValue() == 0);
        log.info("refreshS3VideoStorageDays by deviceVip {}", JsonUtil.toJson(result));
    }

    @Test
    public void testDeviceConfig() throws InterruptedException, IdNotSetException, MqttException {
        when(deviceSettingService.getDeviceSettingsBySerialNumber(any())).thenReturn(DeviceSettingsDO.builder().build());
        when(deviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO());
        when(deviceSettingService.updateUserConfig(any(), any(), anyLong(), anyBoolean())).thenReturn(Result.Success());

        DeviceConfigRequest.DeviceAudio deviceAudio = deviceConfigService.queryDeviceAudio(1, "sn_01");
        Assert.assertTrue(deviceAudio != null);

        DeviceConfigRequest.DoorbellConfig doorbellConfig = deviceConfigService.queryDoorbellConfig(1, "sn_01");
        Assert.assertTrue(doorbellConfig != null);

        DeviceConfigRequest deviceConfigRequest = new DeviceConfigRequest();
        deviceConfigRequest.setDeviceAudio(new DeviceConfigRequest.DeviceAudio());
        deviceConfigRequest.setDoorbellConfig(new DeviceConfigRequest.DoorbellConfig());
        Result result = deviceConfigService.updateDeviceAudio(deviceConfigRequest);
        Assert.assertTrue(result.getResult().intValue() == 0);

        result = deviceConfigService.updateDoorbellConfig(deviceConfigRequest);
        Assert.assertTrue(result.getResult().intValue() == 0);
    }

    @Test
    public void test_getVideoRollingDays() {
        String tenantId = "tenant54321";
        Integer userId = 54321;
        String sn = "sn54321";
        Integer lookBackDays = 7;
        Integer endTime = ((int) System.currentTimeMillis() / 1000) + 24 * 3600 * 3;

        when(userVipService.queryCurrentTierLookBackDays(eq(userId), anyString())).thenReturn(lookBackDays);
        when(userVipService.queryUserVipEndTime(eq(userId), any(),any())).thenReturn(endTime);
        when(tierService.queryTierById(any())).thenReturn(new Tier() {{
            setRollingDays(lookBackDays);
        }});

        PaasVipProduct vipProduct1 = new PaasVipProduct().setId("1").setVipLevel(PaasVipLevel.BASIC.getCode()).setStorage(100L).setLookBackDays(15);
        PaasVipProduct vipProduct2 = new PaasVipProduct().setId("2").setVipLevel(PaasVipLevel.PLUS.getCode()).setStorage(200L).setLookBackDays(30);
        PaasVipProduct vipProduct3 = new PaasVipProduct().setId("3").setVipLevel(PaasVipLevel.PRO.getCode()).setStorage(200L).setLookBackDays(60);
        List<PaasVipProduct> vipProducts = Arrays.asList(vipProduct1, vipProduct2, vipProduct3);
        when(paasVipProductService.queryAllPaasVipProduct()).thenReturn(vipProducts);
        {
            when(paasVipService.queryLastDeviceVip(tenantId, sn)).thenReturn(null);
            Tuple2<Integer, Long> tuple = deviceConfigService.getVideoRollingDays(tenantId, userId, sn);
            Assert.assertEquals(lookBackDays, tuple.v0());

            when(userTierDeviceService.getDeviceCurrentTier(any(), any())).thenReturn(null);
            when( userVipService.queryUserVipInfo(any(), any(), any())).thenReturn(new UserVipTier() {{
                setTierId(1);
            }});
            tuple = deviceConfigService.getVideoRollingDays(tenantId, userId, sn);
            Assert.assertEquals(lookBackDays, tuple.v0());
        }
        {
            DeviceVipLog vipLog1 = new DeviceVipLog().setProductId("4").setVipLevel(4);
            when(paasVipService.queryLastDeviceVip(tenantId, sn)).thenReturn(vipLog1);
            Tuple2<Integer, Long> tuple = deviceConfigService.getVideoRollingDays(tenantId, userId, sn);
            Assert.assertEquals(lookBackDays, tuple.v0());
        }
        DeviceVipLog vipLog2 = new DeviceVipLog().setProductId("2").setVipLevel(2);
        when(paasVipService.queryLastDeviceVip(tenantId, sn)).thenReturn(vipLog2);
        Tuple2<Integer, Long> tuple = deviceConfigService.getVideoRollingDays(tenantId, userId, sn);
        Assert.assertEquals(vipProduct2.getLookBackDays(), tuple.v0());
    }

    @Test
    public void test_publishDeviceUnbindConfig() {
        Result result = deviceThirdConfigService.publishDeviceUnbindConfig("sn_01", PAAS_OWNED_TENANT_ID, "vicoo");
        Assert.assertTrue(ObjectUtils.equals(result.getResult(), 0));

        result = deviceThirdConfigService.publishDeviceUnbindConfig(null, PAAS_OWNED_TENANT_ID, "vicoo");
        Assert.assertTrue(ObjectUtils.equals(result.getResult(), 102));

        when(s3.getBucket()).thenReturn(null);
        result = deviceThirdConfigService.publishDeviceUnbindConfig("sn_01", PAAS_OWNED_TENANT_ID, "vicoo");
        Assert.assertTrue(ObjectUtils.equals(result.getResult(), -1));
    }

    @Test
    public void test_refreshS3VideoStorageDaysByDay() throws Exception {
        when(redisService.containsKey(any())).thenReturn(true);
        deviceConfigService.refreshS3VideoStorageDaysByDay("sn_01");

        when(redisService.containsKey(any())).thenReturn(false);
        Field executorField = deviceConfigService.getClass().getDeclaredField("executor");
        executorField.setAccessible(true);
        executorField.set(deviceConfigService, Executors.newSingleThreadScheduledExecutor());

        deviceConfigService.refreshS3VideoStorageDaysByDay("sn_01");

        ((ExecutorService)executorField.get(deviceConfigService)).awaitTermination(1, TimeUnit.SECONDS);

        verify(redisService, Mockito.atLeastOnce()).set(any(), any(), any());
    }
}
