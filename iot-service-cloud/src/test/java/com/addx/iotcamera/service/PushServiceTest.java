package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.MsgResponseDO;
import com.addx.iotcamera.bean.domain.PushInfo;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.msg.DoorbellVideoMsgEntity;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.config.apollo.PushConfig;
import com.addx.iotcamera.constants.CopyWriteConstans;
import com.addx.iotcamera.helper.MsgHelper;
import com.addx.iotcamera.publishers.notification.Firebase.FirebasePublisher;
import com.addx.iotcamera.publishers.notification.PushArgs;
import com.addx.iotcamera.publishers.notification.UMeng.UMengPublisher;
import com.addx.iotcamera.publishers.notification.iOS.IosPublisher;
import com.addx.iotcamera.service.message.FcmV1Service;
import com.addx.iotcamera.service.xinge.PushXingeService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.addx.iot.common.constant.AppConstants.APP_LANGUAGE_ZH;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class PushServiceTest {

    @InjectMocks
    @Spy
    private PushService pushService;

    @Mock
    private UserService userService;

    @Mock
    private RedisService redisService;

    @Mock
    private CenterNotifyConfig centerNotifyConfig;
    @Mock
    private PushInfoService pushInfoService;
    @Mock
    private PushXingeService pushXingeService;

    @InjectMocks
    private MsgHelper msgHelper;

    @Mock
    private ReportLogService reportLogService;

    @Mock
    private IosPublisher iosPublisher;

    @Mock
    private FirebasePublisher firebasePublisher;

    @Mock
    private UMengPublisher uMengPublisher;

    @Mock
    private PushConfig config;

    @Mock
    private FcmV1Service fcmV1Service;

    @Test
    public void test() {
        Integer result = pushService.setPushInfo(PushInfo.builder().build());
        Assert.assertTrue(result == 0);

        result = pushService.setPushInfo(PushInfo.builder().iosVoipToken(UUID.randomUUID().toString()).build());
        Assert.assertTrue(result == 0);

        result = pushService.setPushInfo(PushInfo.builder().msgType(201).msgToken(UUID.randomUUID().toString()).iosVoipToken(UUID.randomUUID().toString()).build());
        Assert.assertTrue(result == 0);

        when(redisService.get(any())).thenReturn("{}");
        result = pushService.setPushInfo(PushInfo.builder().iosVoipToken(UUID.randomUUID().toString()).build());
        Assert.assertTrue(result == 0);
    }


    @Test
    public void test_pushLowBatteryXinge(){
        User user = new User();
        user.setId(1);
        user.setLanguage(APP_LANGUAGE_ZH);
        user.setTenantId(TENANTID_VICOO);
        when(userService.queryUserById(any())).thenReturn(user);
        when(centerNotifyConfig.getMessage()).thenReturn(this.initMessage());
        PushInfo pushInfo = PushInfo.builder().bundleName("").build();
        when(pushInfoService.getPushInfo(any())).thenReturn(pushInfo);

        doNothing().when(pushXingeService).pushMessage(any(),any());

        MsgResponseDO response = new MsgResponseDO();
        response.setUserId(1);
        response.setDeviceName("device");
        String serialNumber = "serialNumber";
        pushService.pushLowBatteryXinge(response,serialNumber);
        verify(pushService, times(1)).pushLowBatteryXinge(response,serialNumber);
    }

    @Test
    public void test_push_entity() throws Exception {
        PushInfo pushInfo = PushInfo.builder().bundleName("").msgType(201).build();
        PushArgs pushArgs = PushArgs.builder()
                .msgType(pushInfo.getMsgType())
                .msgToken(pushInfo.getMsgToken())
                .iosVoipToken(pushInfo.getIosVoipToken())
                .bundleName(pushInfo.getBundleName())
                .userId(0)
                .build();

        DoorbellVideoMsgEntity doorbellVideoMsgEntity = new DoorbellVideoMsgEntity();
        msgHelper.Send(pushArgs, doorbellVideoMsgEntity);

        Mockito.verify(iosPublisher, Mockito.times(1)).publish(any(), anyBoolean(), any());
    
        pushInfo = PushInfo.builder().bundleName("").msgType(101).build();
        pushArgs = PushArgs.builder()
                .msgType(pushInfo.getMsgType())
                .msgToken(pushInfo.getMsgToken())
                .iosVoipToken(pushInfo.getIosVoipToken())
                .bundleName(pushInfo.getBundleName())
                .userId(0)
                .build();

        when(config.getAndroidConfig()).thenReturn(Collections.emptyMap());

        msgHelper.Send(pushArgs, doorbellVideoMsgEntity);
        Mockito.verify(firebasePublisher, Mockito.times(1)).publish(any(), any(), any());
    
        pushInfo = PushInfo.builder().bundleName("").msgType(102).build();
        pushArgs = PushArgs.builder()
                .msgType(pushInfo.getMsgType())
                .msgToken(pushInfo.getMsgToken())
                .iosVoipToken(pushInfo.getIosVoipToken())
                .bundleName(pushInfo.getBundleName())
                .userId(0)
                .build();

        msgHelper.Send(pushArgs, doorbellVideoMsgEntity);
        Mockito.verify(uMengPublisher, Mockito.times(1)).publish(any(), any());
    }


    private Map<String, Map<String, Map<String, String>>> initMessage(){
        Map<String, Map<String, Map<String, String>>> message = new HashMap<>();
        Map<String, Map<String, String>> languageMap = new HashMap<>();
        Map<String, String> notifyMap = new HashMap<>();
        notifyMap.put(CopyWriteConstans.lowBatteryTitle,"title");
        notifyMap.put(CopyWriteConstans.lowBatteryBody,"body");
        languageMap.put(APP_LANGUAGE_ZH,notifyMap);
        message.put(TENANTID_VICOO,languageMap);
        return message;
    }
}
