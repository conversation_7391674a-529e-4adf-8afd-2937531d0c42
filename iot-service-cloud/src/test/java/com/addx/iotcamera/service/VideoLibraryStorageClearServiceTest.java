package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.db.LibraryStatusTb;
import com.addx.iotcamera.bean.db.UserLibraryViewDO;
import com.addx.iotcamera.bean.db.VideoLibraryDO;
import com.addx.iotcamera.bean.domain.InsertLibraryRequest;
import com.addx.iotcamera.bean.domain.ShareStatusDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.library.TierFreeUserExpireDO;
import com.addx.iotcamera.bean.openapi.DeviceVipLog;
import com.addx.iotcamera.bean.openapi.PaasVipProduct;
import com.addx.iotcamera.bean.response.user.ABTestResult;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.dao.IShareDAO;
import com.addx.iotcamera.enums.VideoLibraryEventEnums;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.service.xxl_check.VideoLibraryXxlJobCheckService;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryStatusDAO;
import com.addx.iotcamera.util.JsonUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
//@Ignore
public class VideoLibraryStorageClearServiceTest {

    @InjectMocks
    @Spy
    private VideoLibraryStorageClearService videoLibraryStorageClearService;

    @Mock
    private UserVipService userVipService;

    @Mock
    private VipService paasVipService;

    @Mock
    private UserService userService;

    @Mock
    private IShardingLibraryStatusDAO shardingLibraryStatusDAO;

    @Mock
    private IShardingLibraryDAO shardingLibraryDAO;

    @Mock
    private IShareDAO shareDAO;

    @Mock
    private UserTierDeviceService userTierDeviceService;

    @Mock
    private TierService tierService;

    @Mock
    private DeviceService deviceService;

    private RedisService redisService;

    private Long userStorageCapacity = 10 * 1024 * 1024L;
    private Long deviceVipStorageCapacity = 5 * 1024 * 1024L;

    @Before
    public void init() {
        try {
            redisService = new MockRedisService();
            Field redisServiceField = VideoLibraryStorageClearService.class.getDeclaredField("redisService");
            redisServiceField.setAccessible(true);
            redisServiceField.set(videoLibraryStorageClearService, redisService);

            Field deviceVipProductMapField = VideoLibraryStorageClearService.class.getDeclaredField("deviceVipProductMap");
            deviceVipProductMapField.setAccessible(true);
            deviceVipProductMapField.set(videoLibraryStorageClearService, Collections.singletonMap("vip2", new PaasVipProduct() {{
                setStorage(deviceVipStorageCapacity);
            }}));

            Field executorField = VideoLibraryStorageClearService.class.getDeclaredField("executor");
            executorField.setAccessible(true);
            executorField.set(videoLibraryStorageClearService, Executors.newSingleThreadScheduledExecutor());

            VideoLibraryXxlJobCheckService videoLibraryXxlJobCheckService = Mockito.mock(VideoLibraryXxlJobCheckService.class);
            when(videoLibraryXxlJobCheckService.needCheck(anyInt())).thenReturn(true);

            Field videoLibraryXxlJobCheckServiceInstanceField = VideoLibraryXxlJobCheckService.class.getDeclaredField("INSTANCE");
            videoLibraryXxlJobCheckServiceInstanceField.setAccessible(true);
            videoLibraryXxlJobCheckServiceInstanceField.set(null, videoLibraryXxlJobCheckService);

            when(userTierDeviceService.getDeviceCurrentTier(any(), any())).thenReturn(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testVideoLibraryKafkaMsgListener() throws InterruptedException {
        Integer userId = 1;
        String serialNumber = "sn_01";
        Integer userId2 = 2;
        String serialNumber2 = "sn_02";

        List<VideoLibraryDO> videoLibraryDOList = new LinkedList<>();
        for (int i = 0; i < 20; i++) {
            videoLibraryDOList.add(VideoLibraryDO.builder().userId(userId).serialNumber(serialNumber).type(0).timestamp(Long.valueOf(System.currentTimeMillis() / 1000).intValue()).fileSize(512 * 1024L).build());
        }
        for (int i = 0; i < 20; i++) {
            videoLibraryDOList.add(VideoLibraryDO.builder().userId(userId2).serialNumber(serialNumber2).type(0).timestamp(Long.valueOf(System.currentTimeMillis() / 1000).intValue()).fileSize(512 * 1024L).build());
        }

        when(userVipService.queryCurrentTierSize(anyInt(), anyInt())).thenReturn(userStorageCapacity);

        when(userTierDeviceService.getDeviceCurrentTierWithTierGroupId(any(), any())).thenReturn(new Tuple2(Integer.valueOf(1), null));

        when(tierService.queryTierById(any())).thenReturn(new Tier(){{
            setStorage(Long.valueOf(userStorageCapacity.intValue()));
        }});

        when(userService.queryTenantIdById(anyInt())).then((invocation) -> {
            Integer userIdI = invocation.getArgument(0);
            return userIdI.intValue() == userId2.intValue() ? "mock_tenantId" : null;
        });

        when(paasVipService.queryLastDeviceVip(any(), any())).then((invocation) -> {
            String tenantId = invocation.getArgument(0);
            if (StringUtils.isEmpty(tenantId)) {
                return null;
            }
            return new DeviceVipLog() {{
                setProductId("vip2");
                setVipLevel(1);
            }};
        });

        videoLibraryDOList.forEach(videoLibraryDO -> {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
            }

            InsertLibraryRequest insertLibraryRequest = new InsertLibraryRequest();
            insertLibraryRequest.setTraceId(videoLibraryDO.getTraceId());
            insertLibraryRequest.setAdminId(videoLibraryDO.getUserId());
            insertLibraryRequest.setSerialNumber(videoLibraryDO.getSerialNumber());
            insertLibraryRequest.setFileSize(videoLibraryDO.getFileSize());
            insertLibraryRequest.setType(0);
            JSONObject insertLibraryInfoObj = new JSONObject();
            insertLibraryInfoObj.put("type", VideoLibraryEventEnums.INSERT);
            insertLibraryInfoObj.put("insertLibraryRequest", insertLibraryRequest);
            videoLibraryStorageClearService.videoLibraryKafkaMsgListener(new ConsumerRecord<String, String>("video-library-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(insertLibraryInfoObj)));
        });

        if (redisService instanceof MockRedisService) {
            log.info("redisValueMap {}", JsonUtil.toJson(((MockRedisService) redisService).redisValueMap));
        }
    }

    @Test
    public void testTriggerVideoLibraryClear() throws InterruptedException {
        when(deviceService.queryBindHistory(anyInt())).thenReturn(Collections.singletonList(new BindOperationTb(){{
            setAnswered(1);
            setRequestTime(0);
            setSerialNumber("sn_01");
        }}));
        when(shareDAO.getDeviceShareSuccessHistoryByAdminId(anyInt())).thenReturn(Collections.singletonList(new ShareStatusDO() {{
            setUserId(2);
        }}));
        when(shareDAO.getDeviceShareSuccessHistoryBySerialNumber(anyString())).thenReturn(Collections.singletonList(new ShareStatusDO() {{
            setUserId(2);
        }}));

        redisService.hashPut("userLibraryInfoV3#1", "userLibraryTriggerStorageClearTime#1", "1");
        redisService.hashPut("deviceLibraryInfoV3#sn_01", "deviceVipLibraryTriggerStorageClearTime#sn_01", "1");
        videoLibraryStorageClearService.triggerUserStorageClear(1, 1);
        videoLibraryStorageClearService.triggerDeviceVipStorageClear(1, "sn_01", 1);

        redisService.hashPut("userLibraryInfoV3#1", "userLibraryTriggerStorageClearTime#1", "1,2,3,4,5,6,7,8,9,10,11");
        redisService.hashPut("deviceLibraryInfoV3#sn_01", "deviceVipLibraryTriggerStorageClearTime#sn_01", "1,2,3,4,5,6,7,8,9,10,11");
        videoLibraryStorageClearService.triggerUserStorageClear(1, 1);
        videoLibraryStorageClearService.triggerDeviceVipStorageClear(1, "sn_01", 1);

        Thread.sleep(2000);
    }

    @Test
    public void testClearVideoLibrary() {
        when(shardingLibraryStatusDAO.selectLibraryStatusList(any())).thenReturn(Collections.singletonList(LibraryStatusTb.builder().traceId("trace_id_01").timestamp(1).build()));
        when(shardingLibraryDAO.selectLibraryStorageClearInfoList(Collections.singleton(anyInt()), any())).thenReturn(new LinkedList() {{
            add(new UserLibraryViewDO() {{
                setUserId(1);
                setTraceId(UUID.randomUUID().toString());
                setShareUserIds("3");
                setFileSize(1);
            }});
            add(new UserLibraryViewDO() {{
                setUserId(1);
                setAdminId(1);
                setTraceId(UUID.randomUUID().toString());
            }});
            add(new UserLibraryViewDO() {{
                setUserId(1);
                setAdminId(2);
                setTraceId(UUID.randomUUID().toString());
            }});
        }});
        videoLibraryStorageClearService.clearVideoLibrary(1, Collections.singletonList("sn_01"), 1L, 0, null);

        when(shardingLibraryDAO.selectLibraryStorageClearInfoList(Collections.singleton(anyInt()), any())).thenReturn(Collections.emptyList());
        videoLibraryStorageClearService.clearVideoLibrary(1, Collections.singletonList("sn_01"), 1L, 0, Collections.singleton("trace_id_01"));

        when(shardingLibraryStatusDAO.selectLibraryStatusList(any())).thenReturn(Collections.singletonList(LibraryStatusTb.builder().traceId("trace_id_01").build()));
        videoLibraryStorageClearService.clearVideoLibrary(1, Collections.singletonList("sn_01"), 1L, 0, Collections.singleton("trace_id_01"));

        when(shardingLibraryStatusDAO.selectLibraryStatusList(any())).thenReturn(Collections.emptyList());
        videoLibraryStorageClearService.clearVideoLibrary(1, Collections.singletonList("sn_01"), 1L, 0, Collections.singleton("trace_id_01"));
    }

    public static class MockRedisService extends RedisService {

        private Map<String, String> redisValueMap = new HashMap<>();

        @Override
        public int hashIncrementInt(String key, String hkey, int num) {
            String redisKey = key + "_" + hkey;
            redisValueMap.put(redisKey, String.valueOf((redisValueMap.containsKey(redisKey) ? Integer.valueOf(redisValueMap.get(redisKey)).intValue() : 0) + num));
            return Integer.valueOf(redisValueMap.get(redisKey));
        }

        @Override
        public BigDecimal hashIncrementBigDecimal(String key, String hKey, BigDecimal num) {
            String redisKey = key + "_" + hKey;
            redisValueMap.put(redisKey, (redisValueMap.containsKey(redisKey) ? new BigDecimal(redisValueMap.get(redisKey)) : BigDecimal.ZERO).add(num).toPlainString());
            return new BigDecimal(redisValueMap.get(redisKey));
        }

        @Override
        public Integer hashGetInt(String key, String hKey, Integer defaultValue) {
            String redisKey = key + "_" + hKey;
            return Integer.valueOf(redisValueMap.get(redisKey));
        }

        @Override
        public String hashGetString(String key, String hKey) {
            String redisKey = key + "_" + hKey;
            return redisValueMap.get(redisKey);
        }

        @Override
        public boolean setIfAbsent(String key, String value, long timeout, TimeUnit timeUnit) {
            return true;
        }

        @Override
        public void hashPut(String key, String hKey, Object value) {
            String redisKey = key + "_" + hKey;
            redisValueMap.put(redisKey, String.valueOf(value));
        }

        @Override
        public boolean setExpired(String key, Integer timeOut) {
            return true;
        }

        @Override
        public boolean delete(String key) {
            return true;
        }

        @Override
        public boolean containsKey(String key) {
            return redisValueMap.keySet().stream().anyMatch(keyObj ->  ArrayUtils.getLength(keyObj.replace(key, "").split("_")) == 2);
        }

        @Override
        public void hashPutAll(String key, Map<String, Object> map) {
            if(MapUtils.isEmpty(map)) {
                return;
            }

            map.entrySet().forEach(entry -> hashPut(key, entry.getKey(), entry.getValue()));
        }
    }


    @Test
    @DisplayName("套餐信息不存在")
    public void test_freeUserStorageNotify_noTier(){
        Integer tierId = 1;
        User user = new User();
        user.setId(1);

        when(tierService.queryTierById(any())).thenReturn(null);
        TierFreeUserExpireDO expectResult = TierFreeUserExpireDO.builder()
                .notify(false)
                .build();
        ABTestResult awarenessFreeTrailDayAbResult = ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build();
        TierFreeUserExpireDO actualResult = videoLibraryStorageClearService.freeUserStorageNotify(user,tierId, awarenessFreeTrailDayAbResult);
        Assert.assertEquals(expectResult,actualResult);
    }
}
