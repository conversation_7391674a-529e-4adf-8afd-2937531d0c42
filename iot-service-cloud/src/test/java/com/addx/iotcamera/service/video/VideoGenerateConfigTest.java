package com.addx.iotcamera.service.video;

import com.addx.iotcamera.config.VideoGenerateConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashSet;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoGenerateConfigTest {

    @InjectMocks
    private VideoGenerateConfig videoGenerateConfig;

    @Before
    public void init() {

    }

    @After
    public void after() {

    }

    @Test
    public void test_setWhiteSnList_isEnableSn() {
        VideoGenerateConfig config = new VideoGenerateConfig();
        config.setEnable(true);
        Assert.assertTrue(config.isEnableSn(null));
        config.setEnable(false);
        config.setWhiteSnList(null);
        Assert.assertEquals(new HashSet<>(Arrays.asList()), config.getWhiteSnSet());
        config.setWhiteSnList(Arrays.asList());
        Assert.assertEquals(new HashSet<>(Arrays.asList()), config.getWhiteSnSet());
        config.setWhiteSnList(Arrays.asList(""));
        Assert.assertEquals(new HashSet<>(Arrays.asList()), config.getWhiteSnSet());
        config.setWhiteSnList(Arrays.asList("sn1", null));
        Assert.assertTrue(config.isEnableSn("sn1"));
        config.setWhiteSnList(Arrays.asList("sn1,sn2", "sn3"));
        Assert.assertTrue(config.isEnableSn("sn1"));
        Assert.assertTrue(config.isEnableSn("sn2"));
        Assert.assertTrue(config.isEnableSn("sn3"));
    }

    @Test
    public void test_setWhiteUserIdList_isEnableUserId() {
        VideoGenerateConfig config = new VideoGenerateConfig();
        config.setEnable(true);
        Assert.assertTrue(config.isEnableUserId(null));
        config.setEnable(false);
        config.setWhiteUserIdList(null);
        Assert.assertEquals(new HashSet<>(Arrays.asList()), config.getWhiteSnSet());
        config.setWhiteUserIdList(Arrays.asList());
        Assert.assertEquals(new HashSet<>(Arrays.asList()), config.getWhiteSnSet());
        config.setWhiteUserIdList(Arrays.asList(""));
        Assert.assertEquals(new HashSet<>(Arrays.asList()), config.getWhiteSnSet());
        config.setWhiteUserIdList(Arrays.asList("111", null));
        Assert.assertTrue(config.isEnableUserId(111));
        config.setWhiteUserIdList(Arrays.asList("111,222", "333"));
        Assert.assertTrue(config.isEnableUserId(111));
        Assert.assertTrue(config.isEnableUserId(222));
        Assert.assertTrue(config.isEnableUserId(333));
    }

    @Test
    public void test_setWhiteUserPercent_isEnableUserId(){
        VideoGenerateConfig config = new VideoGenerateConfig();
        config.setEnable(false);
        config.setWhiteUserPercent(50);
        Assert.assertTrue(config.isEnableUserId(100));
        Assert.assertTrue(config.isEnableUserId(149));
        Assert.assertFalse(config.isEnableUserId(150));
        Assert.assertFalse(config.isEnableUserId(199));
        config.setWhiteUserPercent(100);
        Assert.assertTrue(config.isEnableUserId(Integer.MAX_VALUE));
        config.setWhiteUserPercent(0);
        Assert.assertFalse(config.isEnableUserId(0));
    }

}
