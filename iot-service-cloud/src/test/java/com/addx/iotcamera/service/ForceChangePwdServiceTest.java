package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.config.S3;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.Mail;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;

import static com.addx.iotcamera.service.ForceChangePwdService.REDIS_KEY_MOCK_USER;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ForceChangePwdServiceTest {

    @InjectMocks
    private ForceChangePwdService forceChangePwdService;
    @Mock
    private RedisService redisService;
    @Mock
    private MailConfirmService mailConfirmService;
    @Mock
    private LoginService loginService;
    @Mock
    private AppAccountConfig appAccountConfig;
    @Mock
    private UserService userService;
    @Mock
    private ScheduledExecutorService executor;

    private Map<String, Object> mockRedis = new LinkedHashMap<>();
    private Map<String, Map<String, Object>> mockRedisHash = new LinkedHashMap<>();
    private List<String> mockUsers = new LinkedList<>();

    TestHelper testHelper = TestHelper.getInstanceByEnv("test");

    @Before
    public void init() {
        when(mailConfirmService.queryTenantName(any(), any())).thenAnswer(it -> it.getArgument(0));
        when(loginService.logout(any(), any())).thenAnswer(it -> Result.Success());
        when(appAccountConfig.queryEmailAccount(any())).thenAnswer(it -> new EmailAccount() {{
            setAccount("<EMAIL>");
            setPassword("AddxBeijing123");
            setSendChannel("ali");
        }});
        IUserDAO userDAO = testHelper.getMapper(IUserDAO.class);
        when(userService.queryUserById(any())).thenAnswer(it -> {
            User user = new User();
            user.setId(it.getArgument(0));
            return userDAO.getUserById(user);
        });
//        when(userService.queryUserById(any())).thenAnswer(it -> new User() {{
//            setId(it.getArgument(0));
//            setStatus(UserStatus.NORMAL.getCode());
//        }});

        doAnswer(it -> {
            ((Runnable) it.getArgument(0)).run();
            return null;
        }).when(executor).scheduleWithFixedDelay(any(), anyLong(), anyLong(), any());

        when(redisService.getFromLocalCache(anyString())).thenAnswer(it -> {
            return Optional.ofNullable(mockRedis.get(it.getArgument(0)));
        });
        when(redisService.get(anyString())).thenAnswer(it -> {
            return mockRedis.get(it.getArgument(0));
        });
        when(redisService.delete(anyString())).thenAnswer(it -> {
            return mockRedis.remove(it.getArgument(0)) != null;
        });
        when(redisService.setIfAbsent(anyString(), anyString(), anyLong(), any())).thenAnswer(it -> {
            return mockRedis.putIfAbsent(it.getArgument(0), it.getArgument(1)) != null;
        });
        doAnswer(it -> {
            mockRedisHash.computeIfAbsent(it.getArgument(0), k -> new LinkedHashMap<>())
                    .put(it.getArgument(1), it.getArgument(2));
            return null;
        }).when(redisService).hashPut(anyString(), anyString(), any());
        doAnswer(it -> {
            mockRedisHash.computeIfAbsent(it.getArgument(0), k -> new LinkedHashMap<>())
                    .putAll(it.getArgument(1));
            return null;
        }).when(redisService).hashPutAll(anyString(), anyMap());
        when(redisService.hashEntries(anyString())).thenAnswer(it -> {
            Map<String, Object> map = mockRedisHash.computeIfAbsent(it.getArgument(0), k -> new LinkedHashMap<>());
            return JSON.parseObject(JSON.toJSONString(map));
        });
        mockUsers = new LinkedList<>();
        mockUsers.add("device_id,user_id,latest_dh,email");
        mockRedis.put(REDIS_KEY_MOCK_USER, mockUsers);

        when(redisService.rangeList(anyString())).thenAnswer(it -> {
            return mockRedis.get(it.getArgument(0));
        });

        forceChangePwdService.setEnable(true);
        forceChangePwdService.setReadS3(true);
        forceChangePwdService.setReadRedis(true);
        forceChangePwdService.setS3Config(new S3() {{
            setBucket("addx-test");
            setClientRegion("cn-north-1");
            setAccessKey("********************");
            setSecretKey("SXycRbyidSaPSVuL3ORry98tKCx2dwXlcRo8lBlr");
        }});
        forceChangePwdService.setS3FileKey("security/affected_user_list/affected_user_list.csv");
        forceChangePwdService.setScheduleDelay(1L);
        forceChangePwdService.setSchedulePeriod(1L);
        forceChangePwdService.setMail(new Mail() {
            @Override
            public boolean send(String to, String title, String content, EmailAccount emailAccount) {
                return true;
            }
        });
    }

    @After
    public void close() {
        testHelper.close();
    }

    @Test
    public void test_pullAbnormalUserList() {
        mockUsers.add("c188e7ae-6a35-4c9e-b34a-50f80f6ca8bf,1148,2025-01-27 03:00:00,<EMAIL>");
        mockUsers.add("c188e7ae-6a35-4c9e-b34a-50f80f121212,1149,2025-01-27 03:00:00,<EMAIL>");
        mockUsers.add("c188e7ae-6a35-4c9e-b34a-50f80f121213,1150,2025-01-27 03:00:00,<EMAIL>");
        forceChangePwdService.setEnable(true);
        forceChangePwdService.init();
        Map<String, JSONObject> userList = forceChangePwdService.pullAbnormalUserList();
        forceChangePwdService.handleAbnormalUserList(userList);
        log.info("");
    }

    @Test
    public void test_pullAbnormalUserList_notEnable() {
        forceChangePwdService.setEnable(false);
        forceChangePwdService.init();
        Map<String, JSONObject> userList = forceChangePwdService.pullAbnormalUserList();
        forceChangePwdService.handleAbnormalUserList(userList);
        log.info("");
    }

    @Test
    public void test_deleteForceChangePwdFlag() {
        Integer userId = (new Random()).nextInt();
        {
            forceChangePwdService.setEnable(false);
            Assert.assertFalse(forceChangePwdService.deleteForceChangePwdFlag(userId));
        }
        forceChangePwdService.setEnable(true);
        String key = ForceChangePwdService.REDIS_KEY_PREFIX + "user_flag:" + userId;
        {
            when(redisService.delete(key)).thenReturn(true);
            Assert.assertTrue(forceChangePwdService.deleteForceChangePwdFlag(userId));
        }
        {
            when(redisService.delete(key)).thenReturn(false);
            Assert.assertFalse(forceChangePwdService.deleteForceChangePwdFlag(userId));
        }
        {
            when(redisService.delete(key)).thenThrow(new RuntimeException());
            Assert.assertFalse(forceChangePwdService.deleteForceChangePwdFlag(userId));
        }
    }

}
