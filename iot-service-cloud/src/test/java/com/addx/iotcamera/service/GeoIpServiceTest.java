package com.addx.iotcamera.service;

import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.Location;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.InetAddress;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * description:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2024/2/1 11:51
 */
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)

public class GeoIpServiceTest {
    @InjectMocks
    GeoIpService geoIpService;
    @Test
    public void testGetLocationReal() throws Exception {
        geoIpService.cityReader = null;
        // Mock HttpServletRequest object
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getRemoteAddr()).thenReturn("***********");

        // Call the getLocation method
        Location location = geoIpService.getLocation(request);

        // Verify that the correct location object was returned
        //澳大利亚纬度为负数
        assertTrue(-33.8591== location.getLatitude());
        assertTrue(151.2002==location.getLongitude());

        // Call the getLocation method
        when(request.getRemoteAddr()).thenReturn("**************");
        location = geoIpService.getLocation(request);
        assertTrue(location.getLatitude() >0);
    }


    @Test
    public void testGetLocation() throws Exception {
        // Mock HttpServletRequest object
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getRemoteAddr()).thenReturn("***********");

        // Mock CityReader object
        DatabaseReader cityReader = mock(DatabaseReader.class);
        geoIpService.cityReader = cityReader;
        CityResponse response = new CityResponse(null,null,null,
                new Location(null,null,40.7128,-74.0060,null,null,null),
                null,null,null,null,null,null);

        when(cityReader.city(any(InetAddress.class))).thenReturn(response);

        // Call the getLocation method
        Location location = geoIpService.getLocation(request);

        // Verify that the correct location object was returned
        assertTrue(40.7128== location.getLatitude());
        assertTrue(-74.0060==location.getLongitude());
    }

    @Test
    public void testGetLocationWithException() throws Exception {
        // Mock HttpServletRequest object
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");

        // Mock CityReader object to throw an exception
        DatabaseReader cityReader = mock(DatabaseReader.class);
        geoIpService.cityReader = cityReader;
        when(cityReader.city(any(InetAddress.class))).thenThrow(new IOException("Test exception"));

        // Call the getLocation method
        Location location = geoIpService.getLocation(request);

        // Verify that the method returned null
        assertNull(location);
    }
}
