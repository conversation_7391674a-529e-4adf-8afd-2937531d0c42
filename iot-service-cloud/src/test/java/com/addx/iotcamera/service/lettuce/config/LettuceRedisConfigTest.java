package com.addx.iotcamera.service.lettuce.config;

import com.addx.iotcamera.service.lettuce.properties.LettuceConfigProperties;
import io.lettuce.core.resource.ClientResources;
import lombok.extern.slf4j.Slf4j;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

import static org.mockito.Mockito.when;

@Slf4j
//@RunWith(MockitoJUnitRunner.Silent.class)
public class LettuceRedisConfigTest {
    @InjectMocks
    LettuceRedisConfig lettuceRedisConfig;
    @Mock
    LettuceConfigProperties lettuceConfigProperties;
    @Mock
    LettuceConfigProperties.RedisConnConf redisConnConf;
    @Mock
    LettuceConnectionFactory lettuceConnectionFactory;

    @Mock
    LettuceRedisReadFrom lettuceRedisReadFrom;

    ClientResources clientResources;


//    @Before
    public void before() {
        when(lettuceConfigProperties.getMaxActive()).thenReturn(1);

        when(redisConnConf.getHost()).thenReturn("127.0.0.1");
        when(redisConnConf.getPort()).thenReturn(6379);
        when(redisConnConf.getMaxRedirect()).thenReturn(5);
        when(redisConnConf.getConnectTimeout()).thenReturn(500);
        when(redisConnConf.getDefaultCommandTimeout()).thenReturn(500);
        when(redisConnConf.getMetaCommandTimeout()).thenReturn(500);
        when(redisConnConf.getShutdownTimeout()).thenReturn(500);
        when(lettuceConfigProperties.getBusinessRedisCluster()).thenReturn(redisConnConf);

        clientResources = lettuceRedisConfig.clientResources(lettuceConfigProperties);
    }

//    @Test
    public void redisTemplate() {
//        lettuceRedisConfig.lettuceConnectionFactory();
//        lettuceRedisConfig.redisTemplate(lettuceConnectionFactory);
    }
}