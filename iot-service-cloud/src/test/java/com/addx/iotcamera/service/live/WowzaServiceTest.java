package com.addx.iotcamera.service.live;

import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.StreamServerDO;
import com.addx.iotcamera.dao.IStreamServerDAO;
import com.addx.iotcamera.service.WowzaService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.google.api.client.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WowzaServiceTest {

    @InjectMocks
    private WowzaService wowzaService;
    @Mock
    private IStreamServerDAO streamServerDAO;

    @Mock
    private DeviceModelConfigService deviceModelConfigService;

    @Mock
    private DeviceService deviceService;

    @Test
    public void testgetLiveWowzaServerWithLeastLoad() {
        String ip = "123";

        StreamServerDO streamServerDO = new StreamServerDO();
        streamServerDO.setIp(ip);
        List<StreamServerDO> list = Lists.newArrayList();
        list.add(streamServerDO);
        when(streamServerDAO.getLiveSetting()).thenReturn(list);

        DeviceModel deviceModel = new DeviceModel();
        deviceModel.setLiveApp("app");
        deviceModel.setStreamProtocol("rtsp");
        when(deviceModelConfigService.queryDeviceModelConfig(any())).thenReturn(deviceModel);

        StreamServerDO expectedResult = this.initResultStreamServerDO();

        StreamServerDO actualResult = wowzaService.getLiveWowzaServerWithLeastLoad("");

        assertEquals(expectedResult, actualResult);
    }
    private StreamServerDO initResultStreamServerDO(){
        return StreamServerDO.builder()
                .ip("123")
                .appName("app")
                .protocol("rtsp")
                .build();
    }

    @Test
    public void testgetRecWowzaServerWithLeastLoad() {
        String ip = "123";
        String appName = "app";
        String streamProtocol = "rtsp";
        String size = "360P";

        StreamServerDO streamServerDO = new StreamServerDO();
        streamServerDO.setIp(ip);
        List<StreamServerDO> list = Lists.newArrayList();
        list.add(streamServerDO);
        when(streamServerDAO.getLiveSetting()).thenReturn(list);

        DeviceModel deviceModel = new DeviceModel();
        deviceModel.setStreamProtocol(streamProtocol);
        deviceModel.setRecApp(appName);
        when(deviceModelConfigService.queryDeviceModelConfig(any())).thenReturn(deviceModel);

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setRecResolution(size);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        StreamServerDO expectedResult =  StreamServerDO.builder()
                .ip(ip)
                .appName(appName)
                .protocol(streamProtocol)
                .size(size)
                .duration(0)
                .build();

        StreamServerDO actualResult = wowzaService.getRecWowzaServerWithLeastLoad("");

        assertEquals(expectedResult, actualResult);
    }


}