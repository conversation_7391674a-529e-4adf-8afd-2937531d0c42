package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.init.CopyWriteInit;
import com.addx.iotcamera.config.device.DeviceNameConfig;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.Map;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceNameConfigTest {
    @Mock
    private DeviceNameConfig deviceNameConfig;

    @Before
    public void setUp() throws Exception {
        String resourcePath = "gitconfig/copywrite/iot.csv";
        CopyWrite config = new CopyWrite();
        CopyWriteInit.readStreamCsv(resourcePath, config);
        deviceNameConfig = new DeviceNameConfig(config);
    }

    @Test
    @DisplayName("设备类别不存在")
    public void queryDeviceNameTest_NoSupportCategory(){
        String language = "zh";
        Integer categoryId = 0;
        String expectedResult = "";
        String actualResult = deviceNameConfig.queryDeviceName(language,categoryId);
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("设备类别-智能摄像机")
    public void queryDeviceNameTest_CategoryCamera(){
        String language = "zh";
        Integer categoryId = 1;
        String expectedResult = "智能摄像机";
        String actualResult = deviceNameConfig.queryDeviceName(language,categoryId);
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("设备类别-智能门铃")
    public void queryDeviceNameTest_CategoryDoorBell(){
        String language = "zh";
        Integer categoryId = 2;
        String expectedResult = "智能门铃";
        String actualResult = deviceNameConfig.queryDeviceName(language,categoryId);
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("设备类别-室内机")
    public void queryDeviceNameTest_CategorySub_g_chime(){
        String language = "zh";
        Integer categoryId = 3;
        String expectedResult = "室内机";
        String actualResult = deviceNameConfig.queryDeviceName(language,categoryId);
        assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("config init test")
    public void initTest() throws IOException {
        String resourcePath = "gitconfig/copywrite/iot.csv";
        CopyWrite config = new CopyWrite();
        CopyWriteInit.readStreamCsv(resourcePath, config);
        deviceNameConfig = new DeviceNameConfig(config);

        Map<String, String> map = this.initConfigDeviceTypeCamera();
        Assert.assertTrue(deviceNameConfig.getConfigDeviceTypeCamera().keySet().containsAll(map.keySet()));
        map.forEach((k, v) -> {
            Assert.assertEquals(v.trim(), deviceNameConfig.getConfigDeviceTypeCamera().get(k).trim());
        });
    }

    private Map<String,String> initConfigDeviceTypeCamera(){
        Map<String,String> configDeviceTypeCamera = Maps.newHashMap();
        configDeviceTypeCamera.put("de"," Smart-Kamera");
        configDeviceTypeCamera.put("ru","Смарт-камера");
        configDeviceTypeCamera.put("fi"," älykamera");
        configDeviceTypeCamera.put("pt","Câmera inteligente ");
        configDeviceTypeCamera.put("en","Smart Camera");
        configDeviceTypeCamera.put("it","Smart Camera");
        configDeviceTypeCamera.put("fr","Caméra intelligente ");
        configDeviceTypeCamera.put("zh","智能摄像机");
        configDeviceTypeCamera.put("es"," Cámara inteligente");
        configDeviceTypeCamera.put("iw","מצלמה חכמה של ");
        configDeviceTypeCamera.put("ar","كاميرا ذكية ");
        configDeviceTypeCamera.put("vi","Máy ảnh thông minh");
        configDeviceTypeCamera.put("ja","スマートカメラ");
        configDeviceTypeCamera.put("pl","Inteligentna kamera");
        configDeviceTypeCamera.put("he","מצלמה חכמה של ");
        return configDeviceTypeCamera;
    }

}
