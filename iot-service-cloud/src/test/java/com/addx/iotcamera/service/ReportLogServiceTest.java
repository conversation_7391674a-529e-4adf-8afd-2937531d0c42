package com.addx.iotcamera.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import static com.addx.iotcamera.constants.ReportLogConstants.REPORT_LOG_NEED_COLLECT;
import static com.addx.iotcamera.service.ReportLogService.markLogCollectByFieldKey;
import static com.addx.iotcamera.util.Assert.AssertUtil.assertEquals;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class ReportLogServiceTest {

    @InjectMocks
    private ReportLogService reportLogService;

    @Test
    public void test_debugSysReportEvent() {
        reportLogService.debugSysReportEvent("reportType1", new LinkedHashMap<>());
    }


    @Test
    @DisplayName("需要标记日志收集")
    public void testMarkLogCollectByFieldKeyWithValidKey() {
        // Arrange
        Map<String, Object> obj = new HashMap<>();
        obj.put("key", "subscription_guide_page_show");
        boolean expectedNeedCollect = true;

        // Act
        markLogCollectByFieldKey(obj);

        // Assert
        assertEquals(expectedNeedCollect, obj.get(REPORT_LOG_NEED_COLLECT));
    }

    @Test
    @DisplayName("不需要标记日志收集-不包含指定key")
    public void testMarkLogCollectByFieldKeyWithInvalidKey() {
        // Arrange
        Map<String, Object> obj = new HashMap<>();
        obj.put("key", "invalidKey");
        boolean expectedNeedCollect = false;

        // Act
        markLogCollectByFieldKey(obj);

        // Assert
        assertEquals(expectedNeedCollect, obj.get(REPORT_LOG_NEED_COLLECT));
    }

    @Test
    @DisplayName("不需要标记日志收集-无key")
    public void testMarkLogCollectByFieldKeyWithNullKey() {
        // Arrange
        Map<String, Object> obj = new HashMap<>();
        Boolean expectedNeedCollect = null;

        // Act
        markLogCollectByFieldKey(obj);

        // Assert
        assertEquals(expectedNeedCollect, obj.get(REPORT_LOG_NEED_COLLECT));
    }
}
