package com.addx.iotcamera.service.template;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.library.TierFreeNotifyPeriodDO;
import com.addx.iotcamera.bean.domain.library.TierFreeUserExpireDO;
import com.addx.iotcamera.bean.domain.library.TierFreeUserVipDO;
import com.addx.iotcamera.bean.response.user.ABTestResult;
import com.addx.iotcamera.config.template.GrayscaleTemplateConfig;
import com.addx.iotcamera.enums.template.FunctionType;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.service.VideoLibraryStorageClearService;
import com.addx.iotcamera.service.abtest.AbTestService;
import com.addx.iotcamera.service.user.FunctionOperateLogService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class TierFreeUserNotifyServiceTest {
    @InjectMocks
    private TierFreeUserNotifyService tierFreeUserNotifyService;

    @Mock
    private GrayscaleTemplateConfig grayscaleTemplateConfig;

    @Mock
    private FunctionOperateLogService functionOperateLogService;

    @Mock
    private UserVipService userVipService;

    @Mock
    private AbTestService abTestService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private UserService userService;

    @Mock
    private VideoStoreService videoStoreService;

    @Mock
    private VideoLibraryStorageClearService videoLibraryStorageClearService;

    @Test
    @DisplayName("获取功能名称")
    public void test_queryFunctionName(){
        String exceptedResult = FunctionType.FREE_USER_NOTIFY.getName();
        String actualResult = tierFreeUserNotifyService.queryFunctionName();
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    @DisplayName("功能是否在保护期")
    public void test_inProtectionPeriod_null(){
        Integer userId = 1;

        when(grayscaleTemplateConfig.queryOperateProtect(any())).thenReturn(null);
        TierFreeNotifyPeriodDO exceptedResult = TierFreeNotifyPeriodDO.builder()
                .inPeriod(false)
                .notifyCount(1)
                .build();

        TierFreeNotifyPeriodDO actualResult = tierFreeUserNotifyService.inProtectionPeriod(userId);
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    @DisplayName("验证满足保护期限制")
    public void test_inProtectionPeriod(){
        Integer userId = 1;

        Map<Integer,Integer> periodMap = new HashMap<>();
        periodMap.put(1,1);
        when(grayscaleTemplateConfig.queryOperateProtect(any())).thenReturn(periodMap);

        TierFreeNotifyPeriodDO tierFreeNotifyPeriodDO = TierFreeNotifyPeriodDO.builder()
                .inPeriod(false)
                .notifyCount(0)
                .build();
        when(functionOperateLogService.inPeriod(any(),any(),any())).thenReturn(tierFreeNotifyPeriodDO);

        TierFreeNotifyPeriodDO actualResult = tierFreeUserNotifyService.inProtectionPeriod(userId);
        Assert.assertEquals(tierFreeNotifyPeriodDO,actualResult);
    }

    @Test
    @DisplayName("更新")
    public void test_updateProtectionPeriod_null(){
        Integer userId = 1;
        when(grayscaleTemplateConfig.queryOperateProtect(any())).thenReturn(null);
        tierFreeUserNotifyService.updateProtectionPeriod(userId);

        verify(functionOperateLogService, times(0)).updateFunctionOperate(any(),any());
    }

    @Test
    @DisplayName("更新")
    public void test_updateProtectionPeriod(){
        Integer userId = 1;
        Map<Integer,Integer> periodMap = new HashMap<>();
        periodMap.put(1,1);
        when(grayscaleTemplateConfig.queryOperateProtect(any())).thenReturn(periodMap);

        doNothing().when(functionOperateLogService).updateFunctionOperate(any(),any());

        tierFreeUserNotifyService.updateProtectionPeriod(userId);
        verify(functionOperateLogService, times(1)).updateFunctionOperate(any(),any());
    }


    @Test
    @DisplayName("非免费用户")
    public void test_eligible_notFreeUser(){
        AppRequestBase requestBase = new AppRequestBase();
        Integer userId = 1;

        TierFreeUserVipDO freeUserVipDO = TierFreeUserVipDO.builder()
                .freeVip(false)
                .build();
        when(userVipService.initFreeUserVip(any(),any())).thenReturn(freeUserVipDO);

        TierFreeUserExpireDO expectedResult = TierFreeUserExpireDO.builder()
                .notify(false)
                .build();
        TierFreeUserExpireDO actualResult = tierFreeUserNotifyService.eligible(userId,requestBase);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("无绑定设备")
    public void test_eligible_notDeviceBind(){
        AppRequestBase requestBase = new AppRequestBase();
        Integer userId = 1;

        TierFreeUserVipDO freeUserVipDO = TierFreeUserVipDO.builder()
                .freeVip(true)
                .build();
        when(userVipService.initFreeUserVip(any(),any())).thenReturn(freeUserVipDO);

        when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(Lists.newArrayList());

        TierFreeUserExpireDO expectedResult = TierFreeUserExpireDO.builder()
                .notify(false)
                .build();
        TierFreeUserExpireDO actualResult = tierFreeUserNotifyService.eligible(userId,requestBase);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("视频存储空间已满")
    public void test_eligible_library_expire(){
        AppRequestBase requestBase = new AppRequestBase();
        Integer userId = 1;

        UserVipTier.RecommendProduct recommendProduct = new UserVipTier.RecommendProduct();
        recommendProduct.setProductId(1);

        TierFreeUserVipDO freeUserVipDO = TierFreeUserVipDO.builder()
                .freeVip(true)
                .recommendProduct(recommendProduct)
                .build();
        when(userVipService.initFreeUserVip(any(),any())).thenReturn(freeUserVipDO);

        when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(Arrays.asList("a"));

        when(userService.queryUserById(any())).thenReturn(new User());

        TierFreeUserExpireDO tierFreeUserExpireDO = TierFreeUserExpireDO.builder()
                .notify(true)
                .notifyType(0)
                .lookBackDay(3)
                .build();
        when(videoStoreService.userLibraryExpire(any(),any(), any())).thenReturn(tierFreeUserExpireDO);

        when (abTestService.getAwarenessFreeTrailDayAbResult(userId, requestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build());
        TierFreeUserExpireDO actualResult = tierFreeUserNotifyService.eligible(userId,requestBase);
        Assert.assertEquals(tierFreeUserExpireDO,actualResult);
    }

    @Test
    @DisplayName("视频存储空间已满")
    public void test_eligible(){
        AppRequestBase requestBase = new AppRequestBase();
        Integer userId = 1;

        UserVipTier.RecommendProduct recommendProduct = new UserVipTier.RecommendProduct();
        recommendProduct.setProductId(1);

        TierFreeUserVipDO freeUserVipDO = TierFreeUserVipDO.builder()
                .freeVip(true)
                .recommendProduct(recommendProduct)
                .build();
        when(userVipService.initFreeUserVip(any(),any())).thenReturn(freeUserVipDO);

        when(userRoleService.getUserSerialNumberByUserId(userId)).thenReturn(Arrays.asList("a"));

        when(userService.queryUserById(any())).thenReturn(new User());

        TierFreeUserExpireDO tierFreeUserExpireDO = TierFreeUserExpireDO.builder()
                .notify(false)
                .notifyType(0)
                .lookBackDay(3)
                .build();
        when(videoStoreService.userLibraryExpire(any(),any(), any())).thenReturn(tierFreeUserExpireDO);

        when (abTestService.getAwarenessFreeTrailDayAbResult(userId, requestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                .build());
        tierFreeUserExpireDO = TierFreeUserExpireDO.builder()
                .notify(true)
                .notifyType(1)
                .storege(0.5)
                .build();
        when(videoLibraryStorageClearService.freeUserStorageNotify(any(),any(), any())).thenReturn(tierFreeUserExpireDO);

        TierFreeUserExpireDO actualResult = tierFreeUserNotifyService.eligible(userId,requestBase);
        Assert.assertEquals(tierFreeUserExpireDO,actualResult);
    }
}
