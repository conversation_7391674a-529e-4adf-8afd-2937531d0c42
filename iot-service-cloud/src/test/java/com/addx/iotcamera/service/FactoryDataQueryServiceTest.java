package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.factory.FactoryDeviceModelDO;
import com.addx.iotcamera.bean.response.device.DeviceManufactureInfoResponse;
import com.addx.iotcamera.dao.factory.CrmDAO;
import com.addx.iotcamera.dao.factory.DeviceManufactureDao;
import com.addx.iotcamera.dao.factory.DeviceModelDao;
import com.addx.iotcamera.dao.factory.DevicePreBindDAO;
import com.addx.iotcamera.service.device.model.DeviceModelTenantService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Connection;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class FactoryDataQueryServiceTest {

    @InjectMocks
    private FactoryDataQueryService factoryDataQueryService;
    @Mock
    private DeviceManufactureDao deviceManufactureDao;
    @Mock
    private RedisService redisService;
    @Mock
    private CrmDAO crmDAO;

    @Mock
    private DevicePreBindDAO devicePreBindDAO;

    @Mock
    private DeviceModelDao deviceModelDao;
    
    @Mock
    private DeviceModelTenantService deviceModelTenantService;

    private static final String TEST_SN = "sn_unit_test_20220425_abcdijkmn";
    private static final String TEST_USER_SN = "AIC_UNIT_TEST_20220426";
    // 准备数据，避免人为修改导致单元测试失败
    private static final String SQL_PREPARE_DATA = "REPLACE INTO `factory`.`device_manufacture` (`user_sn`,`batch`,`manufacturer_id`,`worker_id`,`mac_address`,`serial_number`,`encrypt_type`,`mcu_number`,`certificate`,`model_no`,`batch_time`,`firmwareId`,`manufacture_confirmed`,`manufacture_time`,`activated`,`activated_time`,`display_model_no`,`version_time`,`register_model_no`,`nameplate_label`,`support_alexa`,`customer_id`,`out_stock_customer`,`out_stock_status`,`sub_customer_id1`,`sub_customer_id2`,`ap_info`) VALUES ('AIC_UNIT_TEST_20220426','AIC_UNIT_TEST',1,1,'78787986656786','sn_unit_test_20220425_abcdijkmn','hash','0.0.34','CERT_NOT_SET','CG4',1597218609,'0.1.13',1,1597219300,0,NULL,NULL,1597219300465,'','',0,'0',0,0,'0','0',NULL);";

    @SneakyThrows
    @Before
    public void before() {
        TestHelper.HelperConfig helperConfig = TestHelper.HelperConfig.builder().dbConfigName("factory").build();
        TestHelper testHelper = TestHelper.getInstanceByEnv("test", helperConfig);
        DeviceManufactureDao deviceManufactureDao = testHelper.getMapper(DeviceManufactureDao.class);
        when(this.deviceManufactureDao.selectBySn(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(deviceManufactureDao));
        when(this.deviceManufactureDao.selectByUserSn(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(deviceManufactureDao));
        when(this.deviceManufactureDao.selectByUserSns(any())).thenAnswer(AdditionalAnswers.delegatesTo(deviceManufactureDao));
        when(this.deviceManufactureDao.queryModelNoByPlatform(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(deviceManufactureDao));
        when(this.redisService.get(anyString())).thenReturn(null);
        doNothing().when(this.redisService).set(anyString(), anyString(), any());

        try (Connection conn = testHelper.getDataSource().getConnection()) {
            int updateNum = conn.prepareStatement(SQL_PREPARE_DATA).executeUpdate();
            log.info("execute SQL_PREPARE_DATA:{}", updateNum);
        }

    }

    @Test
    public void test_queryDeviceManufactureTableDOBySnOrUserSn() {
        DeviceManufactureTableDO deviceManu = factoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(TEST_SN, TEST_USER_SN);
        Assert.assertNotNull(deviceManu);
        DeviceManufactureTableDO deviceManu2 = factoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(TEST_SN, null);
        Assert.assertNotNull(deviceManu2);
        DeviceManufactureTableDO deviceManu3 = factoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(null, TEST_USER_SN);
        Assert.assertNotNull(deviceManu3);
    }

    @Test
    public void test_queryDeviceManufactureBySn() {
        DeviceManufactureTableDO deviceManu = factoryDataQueryService.queryDeviceManufactureBySn(TEST_SN);
        Assert.assertNotNull(deviceManu);
    }

    @Test
    public void test_queryDeviceManufactureByUserSn() {
        DeviceManufactureTableDO deviceManu = factoryDataQueryService.queryDeviceManufactureByUserSn(TEST_USER_SN);
        Assert.assertNotNull(deviceManu);
    }

    @Test
    public void test_queryDeviceManufactureByUserSns() {
        List<String> userSns = Arrays.asList("ACIADDX20200001", "ACIADDX20200004");
        List<DeviceManufactureTableDO> list = factoryDataQueryService.queryDeviceManufactureByUserSns(userSns);
        Assert.assertEquals(userSns.size(), list.size());

        List<String> userSns2 = Collections.emptyList();
        List<DeviceManufactureTableDO> list2 = factoryDataQueryService.queryDeviceManufactureByUserSns(userSns2);
        Assert.assertEquals(userSns2.size(), list2.size());

        List<DeviceManufactureTableDO> list3 = factoryDataQueryService.queryDeviceManufactureByUserSns(null);
        Assert.assertEquals(0, list3.size());
    }

    @Test
    public void test_testQueryDeviceManufactureInfoDO_null(){
        when(deviceManufactureDao.selectByUserSn(any())).thenReturn(null);
        DeviceManufactureInfoResponse expectResult = DeviceManufactureInfoResponse.builder()
                .serialNumber("")
                .modelNo("")
                .imageUrl("")
                .matterAuth(false)
                .produced(false)
                .build();

        DeviceManufactureInfoResponse actualResult = factoryDataQueryService.queryDeviceManufactureInfoDO("userSn", TENANTID_VICOO);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test(expected = BaseException.class)
    public void testQueryDeviceManufactureInfoDO_exception() {
        String serialNumber = "bx1";
        String modelNo = "bx-model";
        String cxModelNo = "cx1-model";
        when(deviceManufactureDao.selectByUserSn(any())).thenReturn(DeviceManufactureTableDO.builder()
                .serialNumber(serialNumber)
                .registerModelNo(modelNo)
                .build());

        {
            when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(Sets.newHashSet());
            DeviceManufactureInfoResponse expectResult = DeviceManufactureInfoResponse.builder()
                    .serialNumber("")
                    .modelNo("")
                    .imageUrl("")
                    .matterAuth(false)
                    .produced(false)
                    .build();

            DeviceManufactureInfoResponse actualResult = factoryDataQueryService.queryDeviceManufactureInfoDO("userSn", TENANTID_VICOO);
            Assert.assertEquals(expectResult,actualResult);
        }
    }
    @Test
    public void testQueryDeviceManufactureInfoDO() {
        // 正向测试用例
        String serialNumber = "bx1";
        String modelNo = "bx-model";
        String cxModelNo = "cx1-model";
        when(deviceManufactureDao.selectByUserSn(any())).thenReturn(DeviceManufactureTableDO.builder()
                        .serialNumber(serialNumber)
                        .registerModelNo(modelNo)
                .build());

        {
            when(deviceModelTenantService.queryDeviceModelTenantModelList(any())).thenReturn(new HashSet(Arrays.asList(modelNo,cxModelNo)));

            when(devicePreBindDAO.queryPreBindDeviceSn(any())).thenReturn(Arrays.asList("cx1"));
            when(deviceManufactureDao.selectBySerialNumbers(any()))
                .thenReturn(Arrays.asList(DeviceManufactureTableDO.builder()
                                .serialNumber("cx1")
                                .registerModelNo(cxModelNo)
                                .userSn("cx-sn1")
                        .build()));
    
            FactoryDeviceModelDO factoryDeviceModelDO1 = new FactoryDeviceModelDO();
            factoryDeviceModelDO1.setModelNo(modelNo);
            factoryDeviceModelDO1.setCategoryId(4);
            factoryDeviceModelDO1.setIconUrl("icon1");
    
            FactoryDeviceModelDO factoryDeviceModelDO2 = new FactoryDeviceModelDO();
            factoryDeviceModelDO2.setModelNo(cxModelNo);
            factoryDeviceModelDO2.setCategoryId(1);
            factoryDeviceModelDO2.setIconUrl("icon2");
    
            when(deviceModelDao.queryDeviceModelBatch(any())).thenReturn(Arrays.asList(factoryDeviceModelDO1,factoryDeviceModelDO2));
    
    
            DeviceManufactureInfoResponse expectResponse = DeviceManufactureInfoResponse.builder()
                    .serialNumber(serialNumber)
                    .modelCategory(4)
                    .modelNo(modelNo)
                    .imageUrl("icon1")
                    .matterAuth(false)
                    .produced(true)
                    .build();
    
            DeviceManufactureInfoResponse.DeviceManufactureInfoDO cx = new DeviceManufactureInfoResponse.DeviceManufactureInfoDO();
            cx.setSerialNumber("cx1");
            cx.setUserSn("cx-sn1");
            cx.setModelNo("cx1-model");
            cx.setModelCategory(1);
            cx.setImageUrl("icon2");
            expectResponse.setPreBindDeviceList(Arrays.asList(cx));
            DeviceManufactureInfoResponse actualResponse = factoryDataQueryService.queryDeviceManufactureInfoDO("userSn", TENANTID_VICOO);
            Assert.assertEquals(expectResponse.getSerialNumber(),actualResponse.getSerialNumber());
        }
    }

    @Test
    public void test_queryDeviceCustomerIdByUserSn() {
        {
            final String result = factoryDataQueryService.queryDeviceCustomerIdByUserSn(null);
            Assert.assertEquals(null, result);
        }
        {
            final String result = factoryDataQueryService.queryDeviceCustomerIdByUserSn("");
            Assert.assertEquals(null, result);
        }
        {
            final String userSn = OpenApiUtil.shortUUID();
            when(deviceManufactureDao.selectByUserSns(Arrays.asList(userSn))).thenReturn(Arrays.asList());
            final String result = factoryDataQueryService.queryDeviceCustomerIdByUserSn(userSn);
            Assert.assertEquals(null, result);
        }
        {
            final String userSn = OpenApiUtil.shortUUID();
            when(deviceManufactureDao.selectByUserSns(Arrays.asList(userSn))).thenReturn(Arrays.asList(new DeviceManufactureTableDO(){{
                setCustomerId(null);
            }}));
            final String result = factoryDataQueryService.queryDeviceCustomerIdByUserSn(userSn);
            Assert.assertEquals(null, result);
        }
        {
            final String userSn = OpenApiUtil.shortUUID();
            final String cuid = OpenApiUtil.shortUUID();
            when(deviceManufactureDao.selectByUserSns(Arrays.asList(userSn))).thenReturn(Arrays.asList(new DeviceManufactureTableDO(){{
                setCustomerId(cuid);
            }}));
            final String result = factoryDataQueryService.queryDeviceCustomerIdByUserSn(userSn);
            Assert.assertEquals(cuid, result);
        }
        {
            final String userSn = OpenApiUtil.shortUUID();
            when(deviceManufactureDao.selectByUserSns(Arrays.asList(userSn))).thenThrow(new RuntimeException("mock"));
            final String result = factoryDataQueryService.queryDeviceCustomerIdByUserSn(userSn);
            Assert.assertEquals(null, result);
        }
    }
    
}
