package com.addx.iotcamera.service;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoEventRedisServiceTest {

    @InjectMocks
    private VideoEventRedisService videoEventRedisService;
    
    @Mock
    private StringRedisTemplate businessRedisTemplateClusterClient;
    @Mock
    private ValueOperations<String, String> valueOperations;

    @Mock
    private ListOperations<String, String> listOperations;

    @Mock
    private HashOperations<String, Object, Object> hashOperations;

    @Test
    public void get() {
        String key = "key";
        String result = "result";
        when(businessRedisTemplateClusterClient.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get(key)).thenReturn(result);
        String s = videoEventRedisService.get(key);
        Assert.assertTrue(result.equals(s));
    }

    @Test
    public void rangeList() {
        String key = "key";
        String result = "result";
        List<String> list = Arrays.asList( result);
        when(businessRedisTemplateClusterClient.opsForList()).thenReturn(listOperations);
        when(listOperations.range(key, 0, -1)).thenReturn(list);
        List<String> resList = videoEventRedisService.rangeList(key);
        Assert.assertTrue(resList.contains(result));
    }

    @Test
    public void getHashEntries() {
        String key = "key";
        Map<Object, Object> map= new HashMap<>();
        String hashKey = "hashKey";
        String hashValue = "hashValue";
        map.put(hashKey, hashValue);
        when(businessRedisTemplateClusterClient.opsForHash()).thenReturn(hashOperations);
        when(hashOperations.entries(key)).thenReturn(map);
        Map<Object, Object> hashEntries = videoEventRedisService.getHashEntries(key);
        Assert.assertTrue(hashEntries.containsKey(hashKey));
        Assert.assertTrue(hashValue.equals(hashEntries.get(hashKey)));
    }

    @Test
    public void set() {
        String key = "key";
        String value = "value";
        Integer timeOut = 1000;
        when(businessRedisTemplateClusterClient.opsForValue()).thenReturn(valueOperations);
        Mockito.doNothing().when(valueOperations).set(key, value, timeOut, TimeUnit.SECONDS);
        videoEventRedisService.set(key, value, timeOut);
    }

    @Test
    public void saveList() {
        String key = "key";
        String value = "value";
        long expireTime = 1000L;
        Long result = 1L;
        when(businessRedisTemplateClusterClient.opsForList()).thenReturn(listOperations);
        when(listOperations.leftPush(key, value)).thenReturn(result);
        when(businessRedisTemplateClusterClient.expire(key, expireTime, TimeUnit.SECONDS)).thenReturn(Boolean.TRUE);
        videoEventRedisService.saveList(key, value, expireTime);
    }

    @Test
    public void setHashFieldValue() {
        String key = "key";
        String field = "field";
        String value = "value";
        when(businessRedisTemplateClusterClient.opsForHash()).thenReturn(hashOperations);
        Mockito.doNothing().when(hashOperations).put(key, field, value);
        videoEventRedisService.setHashFieldValue(key, field, value);
    }

    @Test
    public void setHashFieldValueMap() {
        String key = "key";
        Map<Object, Object> param = new HashMap<>();
        long expire = 1000L;
        when(businessRedisTemplateClusterClient.opsForHash()).thenReturn(hashOperations);
        Mockito.doNothing().when(hashOperations).putAll(key, param);
        when(businessRedisTemplateClusterClient.expire(key, expire, TimeUnit.SECONDS)).thenReturn(Boolean.TRUE);
        videoEventRedisService.setHashFieldValueMap(key, param, expire);
    }

    @Test
    public void deleteListValue() {
        String key = "key";
        String value = "value";
        when(businessRedisTemplateClusterClient.opsForList()).thenReturn(listOperations);
        when(listOperations.remove(key, 0, value)).thenReturn(1L);
        videoEventRedisService.deleteListValue(key, value);
    }
}