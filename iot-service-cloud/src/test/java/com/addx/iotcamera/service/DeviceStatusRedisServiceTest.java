package com.addx.iotcamera.service;

import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.SpringContextUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.LinkedHashMap;

import static com.addx.iotcamera.service.PersistentRedisService.KEY_POSTFIX_OLD_DATA;
import static com.addx.iotcamera.service.PersistentRedisService.TIMEOUT_OLD_DATA;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest(SpringContextUtil.class)
public class DeviceStatusRedisServiceTest {

    @InjectMocks
    private PersistentRedisService persistentRedisService;
    @Mock
    private StringRedisTemplate oldRedisTemplate;
    @Mock
    private StringRedisTemplate redisTemplate;
    @Mock
    private StringRedisTemplate businessRedisTemplateClusterClient;
    @Mock
    private ValueOperations oldValueOperations;
    @Mock
    private ValueOperations valueOperations;


    private LinkedHashMap<Object, Object> oldMap = new LinkedHashMap<>();
    private LinkedHashMap<Object, Object> map = new LinkedHashMap<>();

    @Before
    public void before() {
        when(oldRedisTemplate.opsForValue()).thenReturn(oldValueOperations);
        when(businessRedisTemplateClusterClient.opsForValue()).thenReturn(valueOperations);

        doAnswer(it -> {
            map.put(it.getArgument(0), it.getArgument(1));
            return null;
        }).when(valueOperations).set(any(), any());
        doAnswer(it -> {
            map.put(it.getArgument(0), it.getArgument(1));
            return null;
        }).when(valueOperations).set(any(), any(), any());
        when(valueOperations.get(any())).thenAnswer(it -> map.get(it.getArgument(0)));
        when(oldValueOperations.get(any())).thenAnswer(it -> oldMap.get(it.getArgument(0)));
    }

    @Test
    public void test_set() {
        String key = OpenApiUtil.shortUUID();
        String value = OpenApiUtil.shortUUID();
        map.put(key, value);
        Assert.assertEquals(value, map.get(key));
    }

    @Test
    public void test_get() {
        PowerMockito.mockStatic(SpringContextUtil.class);
        when(SpringContextUtil.getBean(PersistentRedisService.class)).thenReturn(persistentRedisService);
        when(businessRedisTemplateClusterClient.opsForValue()).thenReturn(valueOperations);
        persistentRedisService.businessRedisTemplateClusterClient = businessRedisTemplateClusterClient;
        {
            String key = OpenApiUtil.shortUUID();
            String value = OpenApiUtil.shortUUID();
            map.put(key, value);
            String result = persistentRedisService.get(key);
            Assert.assertEquals(value, result);
        }
        {
            String key = OpenApiUtil.shortUUID();
            String value = OpenApiUtil.shortUUID();
            map.put(key + KEY_POSTFIX_OLD_DATA, value);
            String result = persistentRedisService.get(key);
            Assert.assertEquals(value, result);
        }
        {
            String key = OpenApiUtil.shortUUID();
            String result = persistentRedisService.get(key);
            Assert.assertEquals(null, result);
        }
        {
            String key = OpenApiUtil.shortUUID();
            String result = persistentRedisService.get(key);
            Assert.assertEquals(null, result);
        }
        {
            String key = OpenApiUtil.shortUUID();
            String result = persistentRedisService.get(key);
            Assert.assertEquals(null, result);
        }
        {
            String key = OpenApiUtil.shortUUID();
            String value = OpenApiUtil.shortUUID();
            oldMap.put(key, value);
            String result = persistentRedisService.get(key);
            Assert.assertEquals(null, result);
        }
        {
            String key = OpenApiUtil.shortUUID();
            String value = OpenApiUtil.shortUUID();
            oldMap.put(key, value);
            doThrow(new RuntimeException()).when(valueOperations).set(key + KEY_POSTFIX_OLD_DATA, value, TIMEOUT_OLD_DATA);
            String result = persistentRedisService.get(key);
            Assert.assertEquals(null, result);
        }
        {
            String key = OpenApiUtil.shortUUID();
            String value = OpenApiUtil.shortUUID();
            oldMap.put(key, value);
            String result = persistentRedisService.get(key);
            Assert.assertEquals(null, result);
        }
        {
            String key = OpenApiUtil.shortUUID();
            String value = OpenApiUtil.shortUUID();
            oldMap.put(key, value);
            String result = persistentRedisService.get(key);
            Assert.assertEquals(null, result);
        }
    }

}
