package com.addx.iotcamera.service;

import com.addx.iotcamera.config.GcsOptions;
import com.addx.iotcamera.constants.VideoConstants;
import com.addx.iotcamera.enums.GcsCredentials;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.helper.GoogleStorageService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.api.gax.paging.Page;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.CredentialAccessBoundary;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.storage.Blob;
import com.google.cloud.storage.BlobInfo;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.StorageOptions;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.addx.iotcamera.config.GcsOptions.SERVICE_ACCOUNT_CREATE_TOKEN;
import static com.addx.iotcamera.config.GcsOptions.SERVICE_ACCOUNT_STORAGE;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class GoogleStorageServiceTest {

    @InjectMocks
    private GoogleStorageService googleStorageService;
    @Mock
    private Storage gStorage;
    @Mock
    private GcsOptions gcsOptions;
    @Mock
    private GoogleCredentials credentialsForCreateToken;
//    @Mock
    private GoogleCredentials credentials;

    private static final String gcsUrl = "https://storage.googleapis.com/download/storage/v1/b/test-a4x/o/device_video_slice%2Fsn1234%2FtraceId123444%2Fslice_2000_1_0.ts";
    private static final String expectBucket = "test-a4x";
    private static final String expectName = "device_video_slice/sn1234/traceId123444/slice_2000_1_0.ts";

    @Before
    @SneakyThrows
    public void init() {
        when(gcsOptions.getEnable()).thenReturn(true);
        credentials = new GoogleCredentials(new AccessToken("token1", new Date())){
            @Override
            public void refresh() throws IOException {
                log.info("gcs credentials refresh!");
            }
        };
        when(credentialsForCreateToken.createScoped(anyString())).thenReturn(credentials);
        when(gStorage.signUrl(any(), anyLong(), any(), any())).thenReturn(new URL("https://a4x-host/" + OpenApiUtil.shortUUID()));
    }

    @Test
    @SneakyThrows
    public void test_gcsOptions() {
        JSONObject config = TestHelper.getInstanceByLocal().getConfig();
        JSONObject obj = config.getJSONObject("google-storage");
        Assert.assertNotNull(obj);
        GcsOptions gcsOptions = obj.toJavaObject(GcsOptions.class);
        Assert.assertNotNull(gcsOptions.getEnable());
        Assert.assertNotNull(gcsOptions.getServiceAccounts());
        Assert.assertNotNull(gcsOptions.getServiceAccounts().get(SERVICE_ACCOUNT_STORAGE));
        Assert.assertNotNull(gcsOptions.getServiceAccounts().get(SERVICE_ACCOUNT_CREATE_TOKEN));
        Assert.assertNotNull(gcsOptions.getLookBackDays2Bucket());
        Assert.assertNotNull(gcsOptions.getLookBackDays2Bucket().get(3));
        Assert.assertNotNull(gcsOptions.getLookBackDays2Bucket().get(7));
    }

    @Test
    @SneakyThrows
    public void test_parseGcsUrl() {
        {
            BlobInfo blobInfo = GoogleStorageService.parseGcsUrl("https://xyz");
            Assert.assertNull(blobInfo);
        }
        {
            BlobInfo blobInfo = GoogleStorageService.parseGcsUrl(gcsUrl);
            Assert.assertNotNull(blobInfo);
            Assert.assertEquals(expectBucket, blobInfo.getBucket());
            Assert.assertEquals(expectName, blobInfo.getName());
        }
    }

//    @Test
    @SneakyThrows
    public void test_parseGcsUrl2() {
        {
            String traceId = "010007801661849499kJwYiyZsWhq";
            System.out.println(Math.abs(traceId.hashCode()) % 64);
            String key = "device_video_slice/98ead8babc1271f1870c9f58b5a1cc7a/010007801661849499kJwYiyZsWhq/image.jpg";
            String encodedKey = URLEncoder.encode(key, "utf-8");
            BlobInfo blobInfo = GoogleStorageService.parseGcsUrl("https://storage.googleapis.com/storage/v1/b/a4x-staging-us-vip-none/o/" + encodedKey);
            System.out.println("https://storage.googleapis.com/storage/v1/b/a4x-staging-us-vip-none/o/" + encodedKey);
        }
        {
            BlobInfo blobInfo = GoogleStorageService.parseGcsUrl("https://www.googleapis.com/storage/v1/b/a4x-staging-us-vip-none/o/device_video_slice%2Fsn1235%2Fhuanjinglong4%2Fslice_2000_1_0.ts");
            Assert.assertNotNull(blobInfo);
        }
    }

    @Test
    @SneakyThrows
    public void test_preSignGcsUrl() {
        {
            String signedUrl = googleStorageService.preSignGcsUrl("https://xyz", 60, TimeUnit.MINUTES);
            Assert.assertNull(signedUrl);
        }
        {
            String url = "https://storage.googleapis.com/download/storage/v1/b/test-a4x";
            String signedUrl = googleStorageService.preSignGcsUrl(url, 60, TimeUnit.MINUTES);
            Assert.assertEquals("", signedUrl);
        }
        {
            when(gStorage.signUrl(any(), anyLong(), any(), any())).thenReturn(new URL(gcsUrl + "?token"));
            String signedUrl = googleStorageService.preSignGcsUrl(gcsUrl, 60, TimeUnit.MINUTES);
            Assert.assertNotNull(signedUrl);
            Assert.assertTrue(signedUrl.startsWith(gcsUrl));
        }
    }

    @Test
    public void test_buildGscUrlForUploadObject() {
        String url = googleStorageService.buildGscUrlForUploadObject("bucket1");
        String expectUrl = "https://storage.googleapis.com/upload/storage/v1/b/bucket1/o/";
        Assert.assertEquals(expectUrl, url);
    }

    @Test
    public void test_buildGscUrlForDownloadObject() {
        String url = googleStorageService.buildGscUrlForDownloadObject("bucket1", "name1");
        String expectUrl = "https://storage.googleapis.com/storage/v1/b/bucket1/o/name1";
        Assert.assertEquals(expectUrl, url);
    }

    @Test
    public void test_createGcsCredentials() {
//        {
//            when(gcsOptions.getEnable()).thenReturn(false);
//            GcsCredentials result = googleStorageService.createGcsCredentials();
//            Assert.assertNull(result);
//        }
        when(gcsOptions.getEnable()).thenReturn(true);
        {
            when(credentialsForCreateToken.createScoped(anyString())).thenThrow(new RuntimeException());
            GcsCredentials result = googleStorageService.createGcsCredentials();
            Assert.assertNull(result);
        }
        when(credentialsForCreateToken.createScoped((String[]) any())).thenReturn(credentials);
        {
            GcsCredentials result = googleStorageService.createGcsCredentials();
            Assert.assertNotNull(result);
        }
    }

    @Test
    public void test_copyObject() {
        final int result = googleStorageService.copyObject("b1", "o1", "b2", "o2");
        Assert.assertEquals(result, result);
    }

    @Test
    public void test_putObject() {
        final int result = googleStorageService.putObject("b1", "o1", "content1");
        Assert.assertEquals(result, result);
    }

    @Mock
    private Page listBlobPage;
    @Mock
    private Blob listBlob;

    @Test
    public void test_listBlob() {
        when(gStorage.list(eq("b1"), any())).thenReturn(listBlobPage);
        when(listBlobPage.getValues()).thenReturn(Arrays.asList(listBlob));
        when(listBlob.toString()).thenReturn("aabb");
        final List<String> strings = googleStorageService.listBlob("b1", "p1");
        Assert.assertEquals(Arrays.asList("aabb"), strings);
    }

//    @Test
    @SneakyThrows
    public void test_createTempCredentials() {
        final InputStream is1 = ConfigHelper.loadConfig("classpath:google/a4xpaas-000001-7cac2ea0258c.json");
        final GoogleCredentials storageCred = GoogleCredentials.fromStream(is1);
//        final Storage gStorage = StorageOptions.newBuilder()
//                .setCredentials(GoogleCredentials.fromStream(is1))
//                .build().getService();

        final InputStream is2 = ConfigHelper.loadConfig("classpath:google/a4xpaas-000001-259859bd6400.json");
        final GoogleCredentials tokenCreatorCred = GoogleCredentials.fromStream(is2);

        final GoogleStorageService gcsService = new GoogleStorageService();
        gcsService.setCredentialsForCreateToken(tokenCreatorCred);
        gcsService.setCredentialsForStorage(storageCred);

        final List<String> buckets = Arrays.asList("a4x-staging-us-vip-3d", "a4x-staging-us-vip-7d");
        final String sn = "sn_test_createTempCredentials";

        final String allowPrefix = VideoConstants.UPLOAD_KEY_HEAD + "/" + sn + "/";
        final CredentialAccessBoundary boundary = GoogleStorageService.createCredentialAccessBoundary(buckets, allowPrefix);
        log.info("CredentialAccessBoundary:{}", JSON.toJSONString(boundary, true));

        /*** 1.新方法 */
//        final AccessToken accessToken = gcsService.createTempCredentials(buckets, sn);

        /*** 2.旧方法 */
//        final GcsCredentials gcsCredentials = gcsService.createGcsCredentials();
//        final AccessToken accessToken = new AccessToken(gcsCredentials.getAccessToken(), new Date(gcsCredentials.getExpiration()));

        /*** 3.旧方法-日志token */
//        String json = "{\"expirationSeconds\":1677057823,\"expiration\":1677057823753,\"accessToken\":\"ya29.c.b0Aaekm1Jthax-04VKf_ibznX2SwH-RyyviI27CfRCXyhr-093Z4QORoplYxRzclmiGTgNxco5kEwt0_AlodRnIW3AqNazQvkgKFGOftSodDICthyf5IhrwG9mDLlqJytMXcjO9bNuFuQ6QXovZxTibupzMBnS6Q79FkBFbkITAdlw2lc-C3jdLUh5cy_UpzrD6MUf82bMe1YszYtwuncROgylmmowR4kN232DIn-uoyZvQ9lUfjiv4BnhWnp-wX_j_VR0hjeWf-Z__U-_RaluV64R9WWcpu8RWqWVl91yVd3_pegjshh2WMuQbxXV1X5W8OUVmOy4Oki0doi7RlkogBWx8rgMF3udxnZSI0QjijQMkktgF6cp_vakbuMn3cpUXO-WdM4hXy5SiqkdgycMO0sfcrm7eVXml16Z2j_4lU53kz_-UjMMXmQB_-MxlY8o2J-QQplWqY_1Sblx39h6cw6YWak3dbll2cyIYW8dIpa5nq2R4cVMz60p8op_neu5O2u5jOW6BiYqncxibuYxeYB0lQtsV_F8fbepgdluI2mnY6FxuO9cjWilIuj7FrhuMc2cMgYZkvVMRu96M60Mt8Rwwu2fXimFZ-sUmn62WFIo-RpU4qkSd6VSQ75k-tm3Ik7ghaki8pJrIylFkUqIjUykhu95qnpaUJYelMxSM3jUQt3jmOdM4iBS6yVoYqBWo_knycxaQyp92oSUtQxeriX6vtde0Rqhwbra4IZIFSpyJVIo2vk5403SJri-B7qcnaxJSkj-t9z8XqpbxJcb6ZBQykRim68gjvVyfgd8FWRWx3bm428q1yXoI_xrlpbll0VW9ZyVcnXQ5tm4a6ysBQgqOFfbZbYhY_cMbj5eyW2tZw1ulycW7etg1MM_1o_-0BkZ5yQYMa3R_nVR8ko0Fp5Uh5et2s-aImV9yeqjY1jq64ptXm8i5145pRxdkU_S1mq_xpjO1xgqQmM4luceOfZ8VxB0RdByZY88Jh6ppyyoUsgI7UntuFO\"}";
//        final JSONObject gcsCredentials = JSON.parseObject(json);
//        final AccessToken accessToken = new AccessToken(gcsCredentials.getString("accessToken"), new Date(gcsCredentials.getLong("expiration")));

        /*** 4.新方法-日志源token */
//        String json = "{\"expirationSeconds\":1677057823,\"expiration\":1677057823753,\"accessToken\":\"ya29.c.b0Aaekm1Jthax-04VKf_ibznX2SwH-RyyviI27CfRCXyhr-093Z4QORoplYxRzclmiGTgNxco5kEwt0_AlodRnIW3AqNazQvkgKFGOftSodDICthyf5IhrwG9mDLlqJytMXcjO9bNuFuQ6QXovZxTibupzMBnS6Q79FkBFbkITAdlw2lc-C3jdLUh5cy_UpzrD6MUf82bMe1YszYtwuncROgylmmowR4kN232DIn-uoyZvQ9lUfjiv4BnhWnp-wX_j_VR0hjeWf-Z__U-_RaluV64R9WWcpu8RWqWVl91yVd3_pegjshh2WMuQbxXV1X5W8OUVmOy4Oki0doi7RlkogBWx8rgMF3udxnZSI0QjijQMkktgF6cp_vakbuMn3cpUXO-WdM4hXy5SiqkdgycMO0sfcrm7eVXml16Z2j_4lU53kz_-UjMMXmQB_-MxlY8o2J-QQplWqY_1Sblx39h6cw6YWak3dbll2cyIYW8dIpa5nq2R4cVMz60p8op_neu5O2u5jOW6BiYqncxibuYxeYB0lQtsV_F8fbepgdluI2mnY6FxuO9cjWilIuj7FrhuMc2cMgYZkvVMRu96M60Mt8Rwwu2fXimFZ-sUmn62WFIo-RpU4qkSd6VSQ75k-tm3Ik7ghaki8pJrIylFkUqIjUykhu95qnpaUJYelMxSM3jUQt3jmOdM4iBS6yVoYqBWo_knycxaQyp92oSUtQxeriX6vtde0Rqhwbra4IZIFSpyJVIo2vk5403SJri-B7qcnaxJSkj-t9z8XqpbxJcb6ZBQykRim68gjvVyfgd8FWRWx3bm428q1yXoI_xrlpbll0VW9ZyVcnXQ5tm4a6ysBQgqOFfbZbYhY_cMbj5eyW2tZw1ulycW7etg1MM_1o_-0BkZ5yQYMa3R_nVR8ko0Fp5Uh5et2s-aImV9yeqjY1jq64ptXm8i5145pRxdkU_S1mq_xpjO1xgqQmM4luceOfZ8VxB0RdByZY88Jh6ppyyoUsgI7UntuFO\"}";
//        final JSONObject gcsCredentials = JSON.parseObject(json);
//        final AccessToken srcAccessToken = new AccessToken(gcsCredentials.getString("accessToken"), new Date(gcsCredentials.getLong("expiration")));
//        final GoogleCredentials srcCred = GoogleCredentials.newBuilder().setAccessToken(srcAccessToken).build();
//        gcsService.setCredentialsForCreateToken(srcCred);
//        final AccessToken accessToken = gcsService.createTempCredentials(buckets, allowPrefix);

        /*** 5.新方法-日志token */
        String json = "{\"expirationSeconds\":1677066263,\"expiration\":1677066263489,\"accessToken\":\"ya29.dr.AfMbl1i74ulPCkWCri2FaqtivgkXTXyza8OWSQQM5CeWHBs99WhZz9FgtN4B9mcx8UaUfLdbloCnEX-TwpfqDuYtvOrmaqGsYbZfrxHkMhpsaol6UUMA00ieeU2yPNnfLwMtdp-F0mjAFOU8UuK7-qBRV54IqE3mKPkom6ZX4UXvu33nu7bVbGfQF0zVCJdxiLoRXymR1TkLcFpaetuOewrah8FgdhyeEz-DizJJlD3DarIXm3MqTJlYv1enaDxiJw1qm8Az8VY1Spskg08Xx98U68-tZK_WiiFwhWtpnMd-T3MVga7fnhaIh3QlbgyHC8dDQ_1onuovbAw1temNK0xGH0jFDf1I05im2G6bjWzOQGusJZeQowTi8lUjcxebet2muCxIg3bWbPQGpzy09WJYiZg47HtjyttoxQlC1mbLywPOSGpyKmBlaPEY7JdozpWkBFrU3E8nVQL62FFbt4owohMBJtrUm2lohF7h424RCwaZqVfSivTqChBRVD1jCPmDal_382vJNHRffdNH5xNR0_4nn9lZiGgP4Gm8gLmx_W_aom5Bfcl6IdtsME_L4U6Pg6OyWSgSRhm9UFisBR6f2q8Ty_hfCnqR1rlwSIt92fAcEZxG_kl2Z256vWIgAthTRu4OKeB4lCx2bCUBjfQYUA0JP-43ruvvZIVwvguj7cMT3HG_qgjmWZ3eJVBB1Ff8SH-5deMShOO5YrJ9Ntyud92IPLKdg9a2qvjtzbqWW7N_MSTbRQww6d4aWWUufsp-E62nV5zyI7gBa1TPC_d67msRsAv-iVudj1uV7RxjRXLzNjPjv5pqQwa8pAAl01KZOf4QHv82ohOHP2be15S13KfUO19WHBdXZntkBelkZjMCF7F6kQHOca0u5TwZgLuzmFvWBuQiffbVTjfkFolEuFDj8F1SKDb2coj5C0alHmf1OksIyUyF6R1RSDnnjejLbP-URy-GTk7W74t_BcSdDVZS0EsG3xHpef_AjQyF15IUxCcKb9FF8258QJmPc5np-EJLjw6jOmC1KfJfMoPUBzJ8Xf-P1qSMowY5hmGoqkkZ6h8_f0ojfLGG3XA-vzDrvxNgxXv3nhSBfq1hqXMkzHQripQ6IOAFGjl-I_nZ6vcf5zL01I0X5AcMWEzK0LRw2HHtKJM9wCTo6rlGGThedqTucxl7Eh9R4OowWNca3p2NCA2eP1SzppY3B6Hga6iRSj-LEd4DAte0aC6i0tP0SyxC36tFenr1qJTInhCyNl2DX1i2LgyYf9tG3zgDY2lmhZbCQ1impt4ujpDgy9ebwB61IsurK5y9xbjumMhYRMxUGF5VBARMUJZdKqaBNc8WWg5U90Gthk3oy3OZiWO4p_XBCb6MlYEq18vy79X5KY6AKWj-Umc7fF_EQtBEDoui6SMa7RmEJKS3KIzH2vuTgqFm4P-qGS3QngP-Bs07s5NUVkVjuruh1W88VWoS5LNr_6phqA-g2wNwYq2AInLiGknDEp7MCAy9v9sBjB-eR5Cu9_ptHUbbAGOa-RRTPKbRgAKddVEbOZ2WxGoLclNoeBYB1h5-IkmdOvFVu-TtFFiQEOx6bj2hUEHlf7Zbi0YA8IKcO_J7EaAZxTBf_gMxl0CrEMQ8OSKWlY_T-zP4oDFXYc8S2O-xCEVfFKvh2dQ7t-bJ940_H79AKtfKp4VcBAqS3DPnXqB2RWJ6j_bSkEXghoPPl5KQnSCxwW2NIkXeCnjOaWbqe95BqEo-rUHHFjKecxB5ocP2jKokLB2Sa_4tXJsk1dpSmhskVwHcHPWzAkpILpzdU8rn7IOuSOsv4olHlPWL8jZ-q_7PImFHOqM-8aYrdT5ksNzo99Ex8MAmp88ImZV80BL3Vyfkt9z4eopT7OPmdK-VMkOipZYouT5mQGW8UF4iSXhZtK14L_QJpGTIJ-9bpm6mN88PDepPtAeQKGCp26_BZ7cWLKkjsoXkhgdRBz24-EDXJRN2nygq3EIwINcnLmJfJ-F1qm9AAvwlGugI89BXNAmpiiGKb9hqKJsGVy3v4OK7o5GmQskra-52pR1ZbbqUG7meq-huv5uxXnF0HORmdFk9Na85DRv38SU-VNWa4QjvFIB02zjX0SR3aNs6A09PA-nzuyP-EbRCQfntGWKU6pJZcuW9RnQoOg4--yNRSRoLl_opaq6_zVtPVuj-hSnjRkqfQQmhnjhzgwgQVyBgsEIy8yz9s0si-MkeWVLBtuf2rpBCMRNqzSJkoYzecc0neeKVXWEKdglc7ZRmrm-w-QGLnZ-vfo-uE26Up4zsW_Bqc3wYtWLGXX6svyB54PHbk2KdxdN3gKNyprESAJihNulBxXNoRR_Cr7IqQSHxCpQ_WzDDfaiujXPSSz0A55XITALpfL0AZJXSz_cAXBlCbkIiRtZFYr62pvwTVtDWzRz_ZHvWJP3leFdG-npb-8785l15OOWBdPP9e_Tc5ziKIoxpUYEcGAGOoy7uLu58ix2hwkJxa0wyV4RT0yUX2MUXwYXZsMmtB-G8OMsqlMBzPfBUe24hszR9baPSKAQ\"}";
        final JSONObject gcsCredentials = JSON.parseObject(json);
        final AccessToken accessToken = new AccessToken(gcsCredentials.getString("accessToken"), new Date(gcsCredentials.getLong("expiration")));

        Assert.assertNotNull(accessToken);
        final Storage gStorage = StorageOptions.newBuilder()
                .setCredentials(GoogleCredentials.newBuilder().setAccessToken(accessToken).build())
                .build().getService();
        gcsService.setGStorage(gStorage);

        final String content = UUID.randomUUID().toString();
        for (final String bucket : buckets) {
            final String key = VideoConstants.UPLOAD_KEY_HEAD + "/" + sn + "/key";
            AssertUtil.assertNotException(() -> gcsService.putObject(bucket, key, content));
        }
        for (final String bucket : buckets) {
            final String key = VideoConstants.UPLOAD_KEY_HEAD + "/" + sn + "/key";
            Assert.assertEquals(content, gcsService.getObject(bucket, key));
        }
    }

}
