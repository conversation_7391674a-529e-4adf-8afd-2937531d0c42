package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.library.TierFreeUserExpireDO;
import com.addx.iotcamera.bean.response.user.ABTestResult;
import com.addx.iotcamera.config.ShardingVideoSliceConfig;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.dynamo.dao.VideoSliceDAO;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.abtest.AbTestService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.video.VideoCacheService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryStatusDAO;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingVideoSliceDAO;
import com.addx.iotcamera.util.OpenApiUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.addx.iotcamera.constants.CopyWriteConstans.LIBRARY_BANNER_EXPIRE;
import static com.addx.iotcamera.constants.CopyWriteConstans.LIBRARY_BANNER_UPGRADE_FREE;
import static org.addx.iot.common.constant.AppConstants.APP_LANGUAGE_ZH;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoStoreServiceTest {
    @Mock
    private ShardingVideoSliceConfig shardingVideoSliceConfig;
    @Mock
    private VideoSliceDAO videoSliceDAO;
    @Mock
    private DeviceService deviceService;
    @Mock
    private IShardingVideoSliceDAO shardingVideoSliceDAO;
    @Mock
    private VideoCacheService videoCacheService;
    @Mock
    private IShardingLibraryDAO shardingLibraryDAO;
    @Mock
    private IShardingLibraryStatusDAO shardingLibraryStatusDAO;
    @Mock
    private RedisService redisService;
    @Mock
    private MqSender mqSender;
    @Mock
    private VideoSearchService videoSearchService;
    @Mock
    private UserRoleService userRoleService;
    @InjectMocks
    private VideoStoreService videoStoreService;

    @Mock
    private TierService tierService;

    @Mock
    private AbTestService abTestService;
    @Mock
    private CenterNotifyConfig centerNotifyConfig;

    @Before
    public void before() {

    }

    @Test
    public void test_saveVideoSlice() {
        String sn = OpenApiUtil.shortUUID();
        VideoSliceDO slice = new VideoSliceDO();
        slice.setSerialNumber(sn);
        when(shardingVideoSliceConfig.isQuerySharding()).thenReturn(true);
        {   /*** 1. 用新库，不用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            when(shardingVideoSliceConfig.isSaveDynamoDB()).thenReturn(false);
            {
                when(shardingVideoSliceDAO.insertSlice(slice)).thenReturn(1);
                Assert.assertEquals(true, videoStoreService.saveVideoSlice(slice));
            }
            {
                when(shardingVideoSliceDAO.insertSlice(slice)).thenReturn(0);
                Assert.assertEquals(false, videoStoreService.saveVideoSlice(slice));
            }
//            {
//                VideoSliceDO slice2 = new VideoSliceDO();
//                slice2.setSerialNumber(sn);
//                when(shardingVideoSliceDAO.insertSlice(slice2)).thenThrow(new RuntimeException("mock error!"));
//                Assert.assertEquals(false, videoStoreService.saveVideoSlice(slice2));
//            }
        }
        {   /*** 2. 用新库，也用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            when(shardingVideoSliceConfig.isSaveDynamoDB()).thenReturn(true);
            {
                when(videoSliceDAO.batchSaveSlice(Arrays.asList(slice))).thenReturn(new HashSet<>(Arrays.asList()));
                when(shardingVideoSliceDAO.insertSlice(slice)).thenReturn(1);
                Assert.assertEquals(true, videoStoreService.saveVideoSlice(slice));
            }
            {
                when(videoSliceDAO.batchSaveSlice(Arrays.asList(slice))).thenReturn(new HashSet<>(Arrays.asList(99)));
                when(shardingVideoSliceDAO.insertSlice(slice)).thenReturn(1);
                Assert.assertEquals(true, videoStoreService.saveVideoSlice(slice));
            }
            {
                when(videoSliceDAO.batchSaveSlice(Arrays.asList(slice))).thenReturn(new HashSet<>(Arrays.asList()));
                when(shardingVideoSliceDAO.insertSlice(slice)).thenReturn(0);
                Assert.assertEquals(true, videoStoreService.saveVideoSlice(slice));
            }
            {
                when(videoSliceDAO.batchSaveSlice(Arrays.asList(slice))).thenReturn(new HashSet<>(Arrays.asList(99)));
                when(shardingVideoSliceDAO.insertSlice(slice)).thenReturn(0);
                Assert.assertEquals(false, videoStoreService.saveVideoSlice(slice));
            }
        }
        {   /*** 3. 不用新库，用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(false);
            {
                when(videoSliceDAO.batchSaveSlice(Arrays.asList(slice))).thenReturn(new HashSet<>(Arrays.asList()));
                Assert.assertEquals(true, videoStoreService.saveVideoSlice(slice));
            }
            {
                when(videoSliceDAO.batchSaveSlice(Arrays.asList(slice))).thenReturn(new HashSet<>(Arrays.asList(99)));
                Assert.assertEquals(false, videoStoreService.saveVideoSlice(slice));
            }
        }
        {   /*** 4. 不用新库，用老库 */
            when(shardingVideoSliceConfig.isQuerySharding()).thenReturn(false);
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            {
                when(videoSliceDAO.batchSaveSlice(Arrays.asList(slice))).thenReturn(new HashSet<>(Arrays.asList()));
                Assert.assertEquals(true, videoStoreService.saveVideoSlice(slice));
            }
            {
                when(videoSliceDAO.batchSaveSlice(Arrays.asList(slice))).thenReturn(new HashSet<>(Arrays.asList(99)));
                Assert.assertEquals(false, videoStoreService.saveVideoSlice(slice));
            }
        }
    }

    @Test
    public void test_querySliceByTraceId() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoSliceDO slice0 = new VideoSliceDO();
        slice0.setSerialNumber(sn);
        slice0.setTraceId(traceId);
        slice0.setOrder(0);
        slice0.setIsLast(false);
        VideoSliceDO slice1 = new VideoSliceDO();
        slice1.setSerialNumber(sn);
        slice1.setTraceId(traceId);
        slice1.setOrder(1);
        slice1.setIsLast(true);
        Integer userId = userRoleService.getDeviceAdminUser(sn);
        when(shardingVideoSliceConfig.isQuerySharding()).thenReturn(true);
        {   /*** 1. 用新库，不用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            when(shardingVideoSliceConfig.isQueryDynamoDB()).thenReturn(false);
            {
                final List<VideoSliceDO> sliceList = Arrays.asList(slice0, slice1);
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceId(userId, traceId)).thenReturn(sliceList);
                Assert.assertEquals(sliceList, videoStoreService.querySliceByAdminUserIdAndTraceId(userId, traceId));
            }
            {
                final List<VideoSliceDO> sliceList = Arrays.asList(slice1, slice0);
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceId(userId, traceId)).thenReturn(sliceList);
                Assert.assertEquals(sliceList, videoStoreService.querySliceByAdminUserIdAndTraceId(userId, traceId));
            }
            {
                final List<VideoSliceDO> sliceList = Arrays.asList(slice1);
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceId(userId, traceId)).thenReturn(sliceList);
                Assert.assertEquals(sliceList, videoStoreService.querySliceByAdminUserIdAndTraceId(userId, traceId));
            }
//            {
//                when(shardingVideoSliceDAO.querySliceByTraceId(traceId)).thenThrow(new RuntimeException(""));
//                Assert.assertEquals(Arrays.asList(), videoStoreService.querySliceByTraceId(traceId));
//            }
        }
        {   /*** 2. 用新库，也用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            when(shardingVideoSliceConfig.isQueryDynamoDB()).thenReturn(true);
            {
                final List<VideoSliceDO> sliceList = Arrays.asList(slice0, slice1);
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceId(userId, traceId)).thenReturn(sliceList);
                Assert.assertEquals(sliceList, videoStoreService.querySliceByAdminUserIdAndTraceId(userId, traceId));
            }
            {
                final List<VideoSliceDO> sliceList = Arrays.asList(slice0, slice1);
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceId(userId, traceId)).thenReturn(Arrays.asList(slice0));
                when(videoSliceDAO.querySliceByTraceId(traceId)).thenReturn(Arrays.asList(slice1));
                Assert.assertEquals(sliceList, videoStoreService.querySliceByAdminUserIdAndTraceId(userId, traceId));
            }
            {
                final List<VideoSliceDO> sliceList = Arrays.asList(slice0, slice1);
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceId(userId, traceId)).thenReturn(Arrays.asList(slice1));
                when(videoSliceDAO.querySliceByTraceId(traceId)).thenReturn(Arrays.asList(slice0));
                Assert.assertEquals(sliceList, videoStoreService.querySliceByAdminUserIdAndTraceId(userId, traceId));
            }
        }
        {   /*** 3. 不用新库，用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(false);
            {
                when(videoSliceDAO.querySliceByTraceId(traceId)).thenReturn(Arrays.asList(slice0, slice1));
                Assert.assertEquals(Arrays.asList(slice0, slice1), videoStoreService.querySliceByAdminUserIdAndTraceId(userId, traceId));
            }
            {
                when(videoSliceDAO.querySliceByTraceId(traceId)).thenReturn(Arrays.asList(slice1, slice0));
                Assert.assertEquals(Arrays.asList(slice0, slice1), videoStoreService.querySliceByAdminUserIdAndTraceId(userId, traceId));
            }
        }
        {   /*** 4. 不用新库，用老库 */
            when(shardingVideoSliceConfig.isQuerySharding()).thenReturn(false);
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            {
                when(videoSliceDAO.querySliceByTraceId(traceId)).thenReturn(Arrays.asList(slice0, slice1));
                Assert.assertEquals(Arrays.asList(slice0, slice1), videoStoreService.querySliceByAdminUserIdAndTraceId(userId, traceId));
            }
//            {
//                when(videoSliceDAO.querySliceByTraceId(traceId)).thenReturn(Arrays.asList(slice1, slice0));
//                Assert.assertEquals(Arrays.asList(slice0, slice1), videoStoreService.querySliceByTraceId(traceId));
//            }
        }
    }

    @Test
    public void test_queryVideoUrlByTraceIds() {
        String sn = OpenApiUtil.shortUUID();
        VideoSliceDO slice0 = new VideoSliceDO();
        slice0.setSerialNumber(sn);
        slice0.setTraceId(OpenApiUtil.shortUUID());
        slice0.setOrder(0);
        slice0.setIsLast(true);
        slice0.setVideoUrl("videoUrl/0");
        VideoSliceDO slice1 = new VideoSliceDO();
        slice1.setSerialNumber(sn);
        slice1.setTraceId(OpenApiUtil.shortUUID());
        slice1.setOrder(0);
        slice1.setIsLast(true);
        slice1.setVideoUrl("videoUrl/1");
        when(shardingVideoSliceConfig.isQuerySharding()).thenReturn(true);
        {   /*** 1. 用新库，不用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            when(shardingVideoSliceConfig.isQueryDynamoDB()).thenReturn(false);
            final List<String> traceIds = Arrays.asList(slice0.getTraceId(), slice1.getTraceId());
            {
                final List<VideoSliceDO> sliceList = Arrays.asList(slice0, slice1);
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceIds(1, traceIds)).thenReturn(sliceList);
                Assert.assertEquals(Arrays.asList("videoUrl/0", "videoUrl/1"), videoStoreService.queryVideoUrlByAdminUserIdAndTraceIds(1, traceIds));
            }
            {
                final List<VideoSliceDO> sliceList = Arrays.asList(slice0);
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceIds(1, traceIds)).thenReturn(sliceList);
                Assert.assertEquals(Arrays.asList("videoUrl/0"), videoStoreService.queryVideoUrlByAdminUserIdAndTraceIds(1, traceIds));
            }
        }
        {   /*** 2. 用新库，也用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            when(shardingVideoSliceConfig.isQueryDynamoDB()).thenReturn(true);
            {
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceIds(1, Arrays.asList(slice0.getTraceId()))).thenReturn(Arrays.asList(slice0));
                Assert.assertEquals(Arrays.asList("videoUrl/0"), videoStoreService.queryVideoUrlByAdminUserIdAndTraceIds(1, Arrays.asList(slice0.getTraceId())));
            }
            {
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceIds(1, Arrays.asList(slice0.getTraceId(), slice0.getTraceId()))).thenReturn(Arrays.asList(slice0));
                when(videoSliceDAO.queryVideoUrlByTraceIds(Arrays.asList(slice1.getTraceId()))).thenReturn(Arrays.asList(slice1.getVideoUrl()));
                Assert.assertEquals(Arrays.asList("videoUrl/0", "videoUrl/1"), videoStoreService.queryVideoUrlByAdminUserIdAndTraceIds(1, Arrays.asList(slice0.getTraceId(), slice1.getTraceId())));
            }
        }
        {   /*** 3. 不用新库，用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(false);
            {
                when(videoSliceDAO.queryVideoUrlByTraceIds(Arrays.asList(slice1.getTraceId()))).thenReturn(Arrays.asList(slice1.getVideoUrl()));
                Assert.assertEquals(Arrays.asList("videoUrl/1"), videoStoreService.queryVideoUrlByAdminUserIdAndTraceIds(1, Arrays.asList(slice1.getTraceId())));
            }
            {
                when(videoSliceDAO.queryVideoUrlByTraceIds(Arrays.asList(slice0.getTraceId(), slice1.getTraceId()))).thenReturn(Arrays.asList(slice1.getVideoUrl()));
                Assert.assertEquals(Arrays.asList("videoUrl/0", "videoUrl/1"), videoStoreService.queryVideoUrlByAdminUserIdAndTraceIds(1, Arrays.asList(slice0.getTraceId(), slice1.getTraceId())));
            }
        }
        {   /*** 4. 不用新库，用老库 */
            when(shardingVideoSliceConfig.isQuerySharding()).thenReturn(false);
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            {
                when(videoSliceDAO.queryVideoUrlByTraceIds(Arrays.asList(slice1.getTraceId()))).thenReturn(Arrays.asList(slice1.getVideoUrl()));
                Assert.assertEquals(Arrays.asList("videoUrl/1"), videoStoreService.queryVideoUrlByAdminUserIdAndTraceIds(1, Arrays.asList(slice1.getTraceId())));
            }
//            {
//                when(videoSliceDAO.queryVideoUrlByTraceIds(Arrays.asList(slice0.getTraceId(), slice1.getTraceId()))).thenReturn(Arrays.asList(slice1.getVideoUrl()));
//                Assert.assertEquals(Arrays.asList("videoUrl/1"), videoStoreService.queryVideoUrlByTraceIds(Arrays.asList(slice0.getTraceId(), slice1.getTraceId())));
//            }
        }
    }

    @Test
    public void test_querySliceByTraceIdAndOrder() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        Integer order = 0;
        VideoSliceDO slice = new VideoSliceDO();
        slice.setSerialNumber(sn);
        slice.setTraceId(traceId);
        slice.setOrder(order);
        slice.setIsLast(true);
        when(shardingVideoSliceConfig.isQuerySharding()).thenReturn(true);
        {   /*** 1. 用新库，不用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            when(shardingVideoSliceConfig.isQueryDynamoDB()).thenReturn(false);
            {
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order)).thenReturn(slice);
                Assert.assertEquals(slice, videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order));
            }
            {
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order)).thenReturn(null);
                Assert.assertEquals(null, videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order));
            }
//            {
//                when(shardingVideoSliceDAO.querySliceByTraceId(traceId)).thenThrow(new RuntimeException(""));
//                Assert.assertEquals(Arrays.asList(), videoStoreService.querySliceByTraceId(traceId));
//            }
        }
        {   /*** 2. 用新库，也用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            when(shardingVideoSliceConfig.isQueryDynamoDB()).thenReturn(true);
            {
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order)).thenReturn(slice);
                Assert.assertEquals(slice, videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order));
            }
            {
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order)).thenReturn(null);
                when(videoSliceDAO.querySliceByTraceIdAndOrder(traceId, order)).thenReturn(null);
                Assert.assertEquals(null, videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order));
            }
            {
                when(shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order)).thenReturn(null);
                when(videoSliceDAO.querySliceByTraceIdAndOrder(traceId, order)).thenReturn(slice);
                Assert.assertEquals(slice, videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order));
            }
        }
        {   /*** 3. 不用新库，用老库 */
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(false);
            {
                when(videoSliceDAO.querySliceByTraceIdAndOrder(traceId, order)).thenReturn(slice);
                Assert.assertEquals(slice, videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order));
            }
            {
                when(videoSliceDAO.querySliceByTraceIdAndOrder(traceId, order)).thenReturn(null);
                Assert.assertEquals(null, videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order));
            }
        }
        {   /*** 4. 不用新库，用老库 */
            when(shardingVideoSliceConfig.isQuerySharding()).thenReturn(false);
            when(shardingVideoSliceConfig.isEnable(sn)).thenReturn(true);
            {
                when(videoSliceDAO.querySliceByTraceIdAndOrder(traceId, order)).thenReturn(slice);
                Assert.assertEquals(slice, videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order));
            }
            {
                when(videoSliceDAO.querySliceByTraceIdAndOrder(traceId, order)).thenReturn(null);
                Assert.assertEquals(null, videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(1, traceId, order));
            }
        }
    }

    @Test
    @DisplayName("免费用户会看到期判断")
    public void test_TierFreeUserExpireDO(){

        {
            User user = new User();
            user.setId(1);
            user.setRegistTime(1);
            user.setTenantId(TENANTID_VICOO);
            user.setLanguage(APP_LANGUAGE_ZH);


            when(tierService.queryTierLookBackDay(any(), any())).thenReturn(3);
            when(shardingLibraryStatusDAO.getUserShardingLibraryExpireCount(any(), any(), any())).thenReturn(1);

            Map<String, Map<String, Map<String, String>>> message = new HashMap<>();
            Map<String, Map<String, String>> languageMassage = Maps.newHashMap();
            Map<String, String> keyMassage = Maps.newHashMap();
            keyMassage.put(LIBRARY_BANNER_EXPIRE, "expire message");
            keyMassage.put(LIBRARY_BANNER_UPGRADE_FREE, "免费试用");
            languageMassage.put(APP_LANGUAGE_ZH, keyMassage);
            message.put(TENANTID_VICOO, languageMassage);
            when(centerNotifyConfig.getMessage()).thenReturn(message);


            TierFreeUserExpireDO expectedResult = TierFreeUserExpireDO.builder()
                    .notify(true)
                    .notifyType(0)
                    .lookBackDay(3)
                    .notifyMessage("expire message")
                    .upgradeFreeBtn("免费试用")
                    .build();

            ABTestResult awarenessFreeTrailDayAbResult = ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build();
            TierFreeUserExpireDO actualResult = videoStoreService.userLibraryExpire(user, 1, awarenessFreeTrailDayAbResult);
            Assert.assertEquals(expectedResult, actualResult);
        }

        {
            User user = new User();
            user.setId(1);
            user.setRegistTime(1);
            user.setTenantId(TENANTID_VICOO);
            user.setLanguage(APP_LANGUAGE_ZH);


            when(tierService.queryTierLookBackDay(any(), any())).thenReturn(3);
            when(shardingLibraryStatusDAO.getUserShardingLibraryExpireCount(any(), any(), any())).thenReturn(1);

            Map<String, Map<String, Map<String, String>>> message = new HashMap<>();
            Map<String, Map<String, String>> languageMassage = Maps.newHashMap();
            Map<String, String> keyMassage = Maps.newHashMap();
            keyMassage.put(LIBRARY_BANNER_EXPIRE, "expire message");
            keyMassage.put(LIBRARY_BANNER_UPGRADE_FREE, "免费试用");
            languageMassage.put(APP_LANGUAGE_ZH, keyMassage);
            message.put(TENANTID_VICOO, languageMassage);
            when(centerNotifyConfig.getMessage()).thenReturn(message);


            TierFreeUserExpireDO expectedResult = TierFreeUserExpireDO.builder()
                    .notify(true)
                    .notifyType(0)
                    .lookBackDay(3)
                    .notifyMessage("expire message")
                    .build();

            ABTestResult awarenessFreeTrailDayAbResult = ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build();
            TierFreeUserExpireDO actualResult = videoStoreService.userLibraryExpire(user, 1, awarenessFreeTrailDayAbResult);
            Assert.assertEquals(expectedResult, actualResult);
        }
    }
}
