package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.VideoReportEvent;
import com.addx.iotcamera.bean.db.VideoImageDO;
import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.domain.device.DeviceReportEventDO;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.bean.openapi.RecognitionObjectCategory;
import com.addx.iotcamera.bean.openapi.SaasAITaskIM;
import com.addx.iotcamera.bean.openapi.SaasAITaskOM;
import com.addx.iotcamera.bean.video.UploadVideoCompleteRequest;
import com.addx.iotcamera.bean.video.VideoMsgType;
import com.addx.iotcamera.config.VideoGenerateConfig;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.service.BirdLoversService;
import com.addx.iotcamera.service.NewRedisService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.VipService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.VideoConstants.S3_VIDEO_SLICE_INFO_NOTIFY_ALEXA;
import static com.addx.iotcamera.service.VideoServiceTest.createUploadVideoCompleteRequest;
import static com.addx.iotcamera.service.video.DeviceCache.LIBRARY_UPDATE_TIMEOUT_MILLIS;
import static com.addx.iotcamera.service.video.DeviceCache.VIDEO_CACHE_EXPIRATION_MILLIS;
import static com.addx.iotcamera.service.video.VideoCache.Flag.*;
import static com.addx.iotcamera.service.video.VideoCacheServiceTest.createMockVideoCache;
import static com.addx.iotcamera.service.video.VideoGenerateService.MSG_SRC;
import static com.addx.iotcamera.service.video.VideoGenerateService.MSG_SRC_IOT_SERVICE;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoGenerateServiceTest {

    @InjectMocks
    private VideoGenerateService videoGenerateService;

    @Mock
    private VideoStoreService videoStoreService;
    @Mock
    private VideoAIService videoAIService;
    @Mock
    private VideoNotifyService videoNotifyService;
    @Mock
    private VideoCacheService videoCacheService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private BirdLoversService birdLoversService;
    @Mock
    private DevicePlatformEventPublisher devicePlatformEventPublisher;
    @Mock
    private Executor executor;
    @Mock
    private MqSender mqSender;
    @Mock
    private VideoGenerateConfig videoGenerateConfig;
    @Mock
    private TimeTicker timeTicker;
    @Mock
    private VideoReportLogService videoReportLogService;
    @Mock
    private VipService vipService;
    @Mock
    private NewRedisService redisService;

    private AtomicLong time = new AtomicLong(0);

    private void incrTime(long delta) {
        time.addAndGet(delta);
    }

    private ForkJoinPool forkJoinPool = new ForkJoinPool(2);
    private List<Runnable> taskBeforeExecute = new LinkedList<>();
    private List<Runnable> taskAfterExecute = new LinkedList<>();

    @Before
    public void init() {
        when(timeTicker.read()).thenAnswer(it -> {
            log.info("timeTicker read : {}", time.get());
            return time.get();
        });
        videoGenerateService.init();
        doNothing().doAnswer(it -> {
            Runnable run = it.getArgument(0);
            log.info("asyncPool execute ");
            taskBeforeExecute.forEach(Runnable::run);
            forkJoinPool.submit(run).get(); // 异步任务阻塞执行
            taskAfterExecute.forEach(Runnable::run);
            return null;
        }).when(executor).execute(any());
    }

    @After
    public void after() {
        forkJoinPool.shutdown();
    }

//    private void assertIsEnable(String sn, String traceId, Boolean expectCacheValue, Boolean expectRealValue) {
//        Assert.assertEquals(expectCacheValue, videoGenerateService.isEnable(sn, traceId)); // 使用缓存
//        videoGenerateService.getTraceId2EnableCache().invalidateAll();
//        Assert.assertEquals(expectRealValue, videoGenerateService.isEnable(sn, traceId)); // 实时求值
//    }

    @Test
    public void test_isEnable() {
        Assert.assertTrue(videoGenerateService.isEnable("", ""));
    }

//    @Test
//    public void test_isEnable() {
//        String sn = OpenApiUtil.shortUUID();
//        String traceId = OpenApiUtil.shortUUID();
//        Integer userId = new Random().nextInt(1000_0000);
//        {
//            when(videoGenerateConfig.isEnableSn(sn)).thenReturn(true);
//            Assert.assertEquals(true, videoGenerateService.isEnable(sn, traceId));
//            videoGenerateService.getTraceId2EnableCache().invalidateAll();
//        }
//        when(videoGenerateConfig.isEnableSn(sn)).thenReturn(false);
//        {
//            when(userRoleService.getDeviceAdminUserRole(sn)).thenReturn(null);
//            Assert.assertEquals(false, videoGenerateService.isEnable(sn, traceId));
//            videoGenerateService.getTraceId2EnableCache().invalidateAll();
//        }
//        when(userRoleService.getDeviceAdminUserRole(sn)).thenReturn(new UserRoleDO() {{
//            setAdminId(userId);
//        }});
//        {
//            when(videoGenerateConfig.isEnableUserId(userId)).thenReturn(false);
//            Assert.assertEquals(false, videoGenerateService.isEnable(sn, traceId));
//            videoGenerateService.getTraceId2EnableCache().invalidateAll();
//        }
//        {
//            when(videoGenerateConfig.isEnableUserId(userId)).thenReturn(true);
//            Assert.assertEquals(true, videoGenerateService.isEnable(sn, traceId));
//            videoGenerateService.getTraceId2EnableCache().invalidateAll();
//        }
//    }

    @Test
    public void test_sendVideoMsg() {
        mqSender.send("", 1, new Object());
        List<String> realTypes = new LinkedList<>();
        doNothing().doAnswer(it -> {
            JSONObject obj = it.getArgument(2);
            realTypes.add(obj.getString("type"));
            return null;
        }).when(mqSender).send(any(), anyString(), any());
        doNothing().doAnswer(it -> {
            JSONObject obj = it.getArgument(2);
            realTypes.add(obj.getString("type"));
            return null;
        }).when(mqSender).send(any(), anyInt(), any());
        videoGenerateService.sendVideoMsg(1, VideoMsgType.IMAGE_REPORT, "traceId", new JSONObject());
        List<String> expectTypes = new LinkedList<>();
        for (VideoMsgType type : VideoMsgType.values()) {
            expectTypes.add(type.getCode());
            videoGenerateService.sendVideoMsg(1, type, "traceId", new JSONObject());
        }
        Assert.assertEquals(expectTypes, realTypes);
        log.info("");
    }

    public List<DeviceTask> createMockDeviceTasks(String sn, String traceId) {
        List<JSONObject> list = Arrays.asList(
                new JSONObject().fluentPut("type", VideoMsgType.REPORT_EVENT.getCode()).fluentPut("serialNumber", sn),
                new JSONObject().fluentPut("type", VideoMsgType.IMAGE_REPORT.getCode()).fluentPut("serialNumber", sn),
                new JSONObject().fluentPut("type", VideoMsgType.SLICE_REPORT.getCode()).fluentPut("serialNumber", sn),
                new JSONObject().fluentPut("type", VideoMsgType.UPLOAD_COMPLETE.getCode()).fluentPut("serialNumber", sn),
                new JSONObject().fluentPut("type", VideoMsgType.AI_RECOGNITION_RESULT.getCode()).fluentPut("deviceSn", sn).fluentPut(MSG_SRC, MSG_SRC_IOT_SERVICE),
                new JSONObject().fluentPut("type", VideoMsgType.AI_EVENT_RESULT.getCode()).fluentPut("deviceSn", sn).fluentPut(MSG_SRC, MSG_SRC_IOT_SERVICE),
                new JSONObject().fluentPut("type", VideoMsgType.REPORT_EVENT.getCode())
        );
        return list.stream().map(it -> {
            VideoMsgType type = VideoMsgType.codeOf(it.getString("type"));
            return new DeviceTask("", sn, traceId, type, it.toJSONString(), timeTicker.readMillis());
        }).collect(Collectors.toList());
    }

    @Test
    public void test_handleVideoMsg() {
        String traceId = OpenApiUtil.shortUUID();
        String sn = OpenApiUtil.shortUUID();
        videoGenerateService.handleVideoMsg(new ConsumerRecord<>("", 0, 0, traceId, ""));
        videoGenerateService.handleVideoMsg(new ConsumerRecord<>("", 0, 0, traceId, "{}"));
        videoGenerateService.handleVideoMsg(new ConsumerRecord<>("", 0, 0, traceId,
                new JSONObject().fluentPut("type", VideoMsgType.SLICE_REPORT.getCode()).toJSONString()));
        List<DeviceTask> mockDeviceTasks = createMockDeviceTasks(sn, traceId);
        videoGenerateService.handleVideoMsg(new ConsumerRecord<>("", 0, 0, traceId, mockDeviceTasks.get(0).getValue()));
        DeviceCache deviceCache = videoGenerateService.getSn2DeviceCache().asMap().get(sn);
        deviceCache.tryLock();
        try {
            videoGenerateService.handleVideoMsg(new ConsumerRecord<>("", 0, 0, traceId, mockDeviceTasks.get(0).getValue()));
        } finally {
            deviceCache.unlock();
        }
    }

    @Test
    public void test_handleVideoMsgByUserId() {
        String traceId = OpenApiUtil.shortUUID();
        String sn = OpenApiUtil.shortUUID();
        videoGenerateService.handleVideoMsgByUserId(new ConsumerRecord<>("", 0, 0, traceId, ""));
        videoGenerateService.handleVideoMsgByUserId(new ConsumerRecord<>("", 0, 0, traceId, "{}"));
        videoGenerateService.handleVideoMsgByUserId(new ConsumerRecord<>("", 0, 0, traceId,
                new JSONObject().fluentPut("type", VideoMsgType.SLICE_REPORT.getCode()).toJSONString()));
        List<DeviceTask> mockDeviceTasks = createMockDeviceTasks(sn, traceId);
        videoGenerateService.handleVideoMsgByUserId(new ConsumerRecord<>("", 0, 0, traceId, mockDeviceTasks.get(0).getValue()));
        DeviceCache deviceCache = new DeviceCache(sn, new Date().getTime());
        deviceCache.tryLock();
        try {
            videoGenerateService.handleVideoMsgByUserId(new ConsumerRecord<>("", 0, 0, traceId, mockDeviceTasks.get(0).getValue()));
        } finally {
            deviceCache.unlock();
        }
    }

    @Test
    public void test_executeDeviceTasks() {
        String traceId = OpenApiUtil.shortUUID();
        String sn = OpenApiUtil.shortUUID();
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(sn);
        /*** 测试最大循环时跳出的 场景 */
        {
            createMockDeviceTasks(sn, traceId).forEach(deviceCache::offerTask);
            videoGenerateService.setExecuteDeviceTaskMaxLoopNum(0);
            taskBeforeExecute.add(() -> videoGenerateService.setExecuteDeviceTaskMaxLoopNum(100));
            videoGenerateService.executeDeviceTasks(deviceCache);
        }
        videoGenerateService.setExecuteDeviceTaskMaxLoopNum(100);
        /*** 测试 加锁失败时跳出循环 */
        {
            createMockDeviceTasks(sn, traceId).forEach(deviceCache::offerTask);
            deviceCache.tryLock();
            try {
                executor.execute(() -> videoGenerateService.executeDeviceTasks(deviceCache));
            } finally {
                deviceCache.unlock();
            }
        }
    }

    @Test
    @SneakyThrows
    public void test_lockingExecuteDeviceTasks() {
        String traceId = OpenApiUtil.shortUUID();
        String sn = OpenApiUtil.shortUUID();
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(sn);
        createMockDeviceTasks(sn, traceId).forEach(deviceCache::offerTask);
        /*** 有锁 */
        {
            deviceCache.tryLock();
            try {
                // 异步执行才能测锁
                Boolean locked = forkJoinPool.submit(() -> videoGenerateService.lockingExecuteDeviceTasks(deviceCache, 0)).get();
                Assert.assertEquals(false, locked);
            } finally {
                deviceCache.unlock();
            }
        }
        /*** 无锁 */
        when(videoCacheService.initVideoCache(any(), any())).thenReturn(true);
        {
            createMockDeviceTasks(sn, traceId).forEach(deviceCache::offerTask);
            Boolean locked = videoGenerateService.lockingExecuteDeviceTasks(deviceCache, 0);
            Assert.assertEquals(true, locked);
        }
    }

    @Test
    public void test_clearDeviceCache() {
        String sn = OpenApiUtil.shortUUID();
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(sn);
        {
            String traceId = OpenApiUtil.shortUUID();
            deviceCache.getTraceId2VideoCache().put(traceId, new VideoCache(sn, traceId, timeTicker.readMillis()));
        }
        {
            String traceId = OpenApiUtil.shortUUID();
            long time = timeTicker.readMillis() - LIBRARY_UPDATE_TIMEOUT_MILLIS - 1;
            deviceCache.getTraceId2VideoCache().put(traceId, new VideoCache(sn, traceId, time));
        }
        {
            String traceId = OpenApiUtil.shortUUID();
            long time = timeTicker.readMillis() - VIDEO_CACHE_EXPIRATION_MILLIS - 1;
            deviceCache.getTraceId2VideoCache().put(traceId, new VideoCache(sn, traceId, time));
        }
        {
            String traceId = OpenApiUtil.shortUUID();
            long time = timeTicker.readMillis() - LIBRARY_UPDATE_TIMEOUT_MILLIS;
            VideoCache videoCache = new VideoCache(sn, traceId, time);
            videoCache.setFlag(LIBRARY_UPDATED_ON_SLICE_FULL);
            videoCache.setFlag(LIBRARY_UPDATED_ON_UPLOAD_COMPLETE);
            videoCache.setFlag(LIBRARY_UPDATED_ON_AI_EVENT);
            deviceCache.getTraceId2VideoCache().put(traceId, videoCache);
        }
        videoGenerateService.clearDeviceCache(deviceCache);
    }

    @Test
    public void createVideo() {
        VideoCache video = new VideoCache(OpenApiUtil.shortUUID(), OpenApiUtil.shortUUID(), timeTicker.readMillis());
        {
            video.setFlag(LIBRARY_CREATED);
            Assert.assertEquals(true, videoGenerateService.createVideo(video));
        }
        video.clearFlag(LIBRARY_CREATED);
        {
            when(videoStoreService.createVideo(video)).thenReturn(VideoStoreService.CreateResult.ERROR);
            Assert.assertEquals(false, videoGenerateService.createVideo(video));
        }
        {
            when(videoStoreService.createVideo(video)).thenReturn(VideoStoreService.CreateResult.DUPLICATE_KEY);
            Assert.assertEquals(true, videoGenerateService.createVideo(video));
        }
        {
            when(videoStoreService.createVideo(video)).thenReturn(VideoStoreService.CreateResult.CREATED);
            video.setReceiveAllSlice(false);
            Assert.assertEquals(true, videoGenerateService.createVideo(video));
            video.setReceiveAllSlice(true);
            Assert.assertEquals(true, videoGenerateService.createVideo(video));
        }
    }

    @Test
    public void test_updateVideo() {
        VideoCache video = new VideoCache(OpenApiUtil.shortUUID(), OpenApiUtil.shortUUID(), timeTicker.readMillis());
        VideoCache.Flag flag = LIBRARY_UPDATED_ON_AI_EVENT;
        {
            video.setFlag(flag);
            Assert.assertEquals(true, videoGenerateService.updateVideo(video, flag));
        }
        video.clearFlag(flag);
        {
            when(videoCacheService.resumeVideoData(video)).thenReturn(false);
            Assert.assertEquals(false, videoGenerateService.updateVideo(video, flag));
        }
        when(videoCacheService.resumeVideoData(video)).thenReturn(true);
        {
            video.clearFlag(LIBRARY_CREATED);
            Assert.assertEquals(false, videoGenerateService.updateVideo(video, flag));
        }
        video.setFlag(LIBRARY_CREATED);
        {
            when(videoStoreService.updateVideo(video, flag.name())).thenReturn(false);
            Assert.assertEquals(true, videoGenerateService.updateVideo(video, flag));
        }
        {
            when(videoStoreService.updateVideo(video, flag.name())).thenReturn(true);
            Assert.assertEquals(true, videoGenerateService.updateVideo(video, flag));
        }
    }

    @Test
    public void test_handleImageReport() {
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(OpenApiUtil.shortUUID());
        VideoCache video = createMockVideoCache(deviceCache.getSerialNumber(), OpenApiUtil.shortUUID());
        videoGenerateService.handleImageReport(deviceCache, video, new VideoImageDO().setImageUrl("imageUrl"), false);
    }

    public static List<VideoSliceDO> createMockSliceList(VideoCache video, int n) {
        List<VideoSliceDO> sliceList = new LinkedList<>();
        for (int i = 0; i < n; i++) {
            VideoSliceDO videoSlice = VideoSliceDO.builder()
                    .serialNumber(video.getSerialNumber())
                    .traceId(video.getTraceId())
                    .period(new BigDecimal(2000).divide(new BigDecimal(1000))) // 存秒值
                    .order(i)
                    .isLast(i == n - 1)
                    .videoUrl("https://a4x-staging-us-vip-pro.s3.amazonaws.com/device_video_slice/" +
                            video.getSerialNumber() + "/" + video.getTraceId() + "/slice_2333_" + i + "_" + (i == n - 1 ? 1 : 0) + ".ts")
                    .fileSize(12345)
                    .s3EventTime(System.currentTimeMillis())
                    .timeZone("")
                    .imageUrl("imageUrl" + i)
                    .build();
            sliceList.add(videoSlice);
        }
        return sliceList;
    }

    public void test_handleSliceReport(DeviceCache deviceCache, VideoCache video) {
        when(videoStoreService.saveVideoSlice(any())).thenAnswer(it -> {
            VideoSliceDO slice = it.getArgument(0);
            if (slice.getTraceId().equals(video.getTraceId())) {
                return slice.getOrder() % 2 == 0;
            }
            return true;
        });
        for (VideoSliceDO slice : createMockSliceList(video, 5)) {
            videoGenerateService.handleSliceReport(deviceCache, video, slice);
        }
        for (VideoSliceDO slice : createMockSliceList(video, 2)) { // 重复收到
            videoGenerateService.handleSliceReport(deviceCache, video, slice);
        }
    }

    @Test
    public void test_handleSliceReport_noVip() {
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(OpenApiUtil.shortUUID());
        VideoCache video = createMockVideoCache(deviceCache.getSerialNumber(), OpenApiUtil.shortUUID());
        video.setHasAiAbility(false);
        test_handleSliceReport(deviceCache, video);
    }

    @Test
    public void test_handleSliceReport_hasVip() {
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(OpenApiUtil.shortUUID());
        VideoCache video = createMockVideoCache(deviceCache.getSerialNumber(), OpenApiUtil.shortUUID());
        video.setHasAiAbility(true);
        test_handleSliceReport(deviceCache, video);
    }

    @Test
    public void test_handleUploadComplete() {
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(OpenApiUtil.shortUUID());
        VideoCache video = createMockVideoCache(deviceCache.getSerialNumber(), OpenApiUtil.shortUUID());
        for (VideoSliceDO slice : createMockSliceList(video, 5)) {
            videoGenerateService.handleSliceReport(deviceCache, video, slice);
        }
        UploadVideoCompleteRequest request = createUploadVideoCompleteRequest(null, null);
        videoGenerateService.handleUploadComplete(deviceCache, video, JSON.toJSONString(request));
    }

    @Test
    public void test_handleUploadComplete2() {
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(OpenApiUtil.shortUUID());
        VideoCache video = createMockVideoCache(deviceCache.getSerialNumber(), OpenApiUtil.shortUUID());
        for (VideoSliceDO slice : createMockSliceList(video, 5)) {
            videoGenerateService.handleSliceReport(deviceCache, video, slice);
        }
        final String content = "{\"s3AddressReceivedTimestamp\":1672226459641,\"traceId\":\"012833116722264594ZQz8tikrR15\",\"firmwareType\":\"IN1B\",\"serialNumber\":\"234430580b6b08c090361a3733fb27ec\",\"videoFormat\":\"ts\",\"imageKey\":\"device_video_slice/234430580b6b08c090361a3733fb27ec/012833116722264594ZQz8tikrR15/image.jpg\",\"modelNo\":\"CG621-W-TN\",\"type\":\"UPLOAD_COMPLETE\",\"version\":\"0.11.0\",\"resolution\":\"1280x720\",\"totalEndRecordingTimestamp\":1672226468058,\"totalStartRecordingTimestamp\":373,\"gitSha\":\"1d27cf\",\"sliceList\":[{\"startRecordingTimestamp\":373,\"endRecordingTimestamp\":1672226459591,\"startUploadingTimestamp\":1672226460246,\"endUploadingTimestamp\":1672226460520,\"fileSize\":161680,\"uploadSuccess\":1,\"tryNum\":1,\"videoKey\":\"device_video_slice/234430580b6b08c090361a3733fb27ec/012833116722264594ZQz8tikrR15/slice_1797_0_0.ts\"},{\"startRecordingTimestamp\":1672226459658,\"endRecordingTimestamp\":1672226461590,\"startUploadingTimestamp\":1672226461660,\"endUploadingTimestamp\":1672226461896,\"fileSize\":179916,\"uploadSuccess\":1,\"tryNum\":1,\"videoKey\":\"device_video_slice/234430580b6b08c090361a3733fb27ec/012833116722264594ZQz8tikrR15/slice_2000_1_0.ts\"},{\"startRecordingTimestamp\":1672226461658,\"endRecordingTimestamp\":1672226463590,\"startUploadingTimestamp\":1672226463664,\"endUploadingTimestamp\":1672226463991,\"fileSize\":353252,\"uploadSuccess\":1,\"tryNum\":1,\"videoKey\":\"device_video_slice/234430580b6b08c090361a3733fb27ec/012833116722264594ZQz8tikrR15/slice_1999_2_0.ts\"},{\"startRecordingTimestamp\":1672226463658,\"endRecordingTimestamp\":1672226465590,\"startUploadingTimestamp\":1672226465666,\"endUploadingTimestamp\":1672226465943,\"fileSize\":329000,\"uploadSuccess\":1,\"tryNum\":1,\"videoKey\":\"device_video_slice/234430580b6b08c090361a3733fb27ec/012833116722264594ZQz8tikrR15/slice_2000_3_0.ts\"},{\"startRecordingTimestamp\":1672226465657,\"endRecordingTimestamp\":1672226468058,\"startUploadingTimestamp\":1672226468125,\"endUploadingTimestamp\":1672226468516,\"fileSize\":398936,\"uploadSuccess\":1,\"tryNum\":1,\"videoKey\":\"device_video_slice/234430580b6b08c090361a3733fb27ec/012833116722264594ZQz8tikrR15/slice_2466_4_1.ts\"}]}";
        videoGenerateService.handleUploadComplete(deviceCache, video, content);
    }

    @Test
    public void test_handleAIRecognitionResult() {
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(OpenApiUtil.shortUUID());
        VideoCache video = createMockVideoCache(deviceCache.getSerialNumber(), OpenApiUtil.shortUUID());
        video.setIsWowza(false);
        for (VideoSliceDO slice : createMockSliceList(video, 5)) {
            videoGenerateService.handleSliceReport(deviceCache, video, slice);
        }
        String str = "{\"type\":\"AI_RECOGNITION_RESULT\",\"deviceSn\":\"ddab6b8341458650d384b4c43362f7ff\",\"traceId\":\"03901667538294jlTsgPqPAkJvEX3\",\"order\":2,\"ownerId\":\"390\",\"taskId\":\"GTCiazUuPGYWgpTh1dW6e3\",\"outParams\":\"{\\\"outEncodeType\\\":0}\",\"images\":[{\"imageOrder\":0,\"imageUrl\":\"https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667538294jlTsgPqPAkJvEX3_notify_recognitionGTCiazUuPGYWgpTh1dW6e3_2_0.jpg\",\"timeInVideo\":0,\"objects\":[{\"boxId\":\"20000\",\"category\":\"PERSON\",\"confidence\":\"94.590393\",\"id\":{\"enable\":false,\"labelId\":\"\"},\"position\":{\"left\":\"0.361719\",\"top\":\"0.000000\",\"width\":\"0.579687\",\"height\":\"0.833333\"},\"activityZoneIds\":[],\"subCategories\":[]},{\"boxId\":\"20001\",\"category\":\"PERSON\",\"confidence\":\"91.983551\",\"id\":{\"enable\":false,\"labelId\":\"\"},\"position\":{\"left\":\"0.253125\",\"top\":\"0.109722\",\"width\":\"0.166406\",\"height\":\"0.633333\"},\"activityZoneIds\":[],\"subCategories\":[]},{\"boxId\":\"20002\",\"category\":\"PERSON\",\"confidence\":\"75.297012\",\"id\":{\"enable\":false,\"labelId\":\"\"},\"position\":{\"left\":\"0.176563\",\"top\":\"0.359722\",\"width\":\"0.118750\",\"height\":\"0.336111\"},\"activityZoneIds\":[],\"subCategories\":[]}]}]}";
        SaasAITaskOM input = JSON.parseObject(str, SaasAITaskOM.class);
        input.setTraceId(video.getTraceId());
        videoGenerateService.handleAIRecognitionResult(deviceCache, video, input);
    }

    @Test
    public void test_handleAIEventResult() {
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(OpenApiUtil.shortUUID());
        VideoCache video = createMockVideoCache(deviceCache.getSerialNumber(), OpenApiUtil.shortUUID());
        video.setIsWowza(false);
        video.setDefaultSaasAITask(new SaasAITaskIM());
        video.setEnableOther(true);
        for (VideoSliceDO slice : createMockSliceList(video, 5)) {
            videoGenerateService.handleSliceReport(deviceCache, video, slice);
        }
        String str = "{\"type\":\"AI_EVENT_RESULT\",\"deviceSn\":\"ddab6b8341458650d384b4c43362f7ff\",\"traceId\":\"03901667538294jlTsgPqPAkJvEX3\",\"outParams\":\"{\\\"outEncodeType\\\":0}\",\"ownerId\":\"390\",\"coverImageUrl\":\"https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667538294jlTsgPqPAkJvEX3_gallery.jpg\",\"events\":[{\"name\":\"EXIST\",\"objectCategory\":\"PERSON\",\"timeRanges\":[{\"begin\":0,\"end\":0}],\"boxIds\":[],\"summaryUrl\":\"https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667538294jlTsgPqPAkJvEX3_gallery.jpg\",\"activityZoneIds\":[],\"filterResult\":0,\"filterReason\":\"\"},{\"name\":\"VEHICLE_HELD_UP\",\"objectCategory\":\"VEHICLE\",\"timeRanges\":[{\"begin\":0,\"end\":0}],\"boxIds\":[],\"summaryUrl\":\"https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667538294jlTsgPqPAkJvEX3_gallery.jpg\",\"activityZoneIds\":[],\"filterResult\":0,\"filterReason\":\"\"}],\"reIds\":[{\"labelId\":\"unknown\",\"eventIndexes\":[1]}]}";
        SaasAITaskOM input = JSON.parseObject(str, SaasAITaskOM.class);
        input.setTraceId(video.getTraceId());
        videoGenerateService.handleAIEventResult(deviceCache, video, input);
        str = "{\"type\":\"AI_EVENT_RESULT\",\"deviceSn\":\"ddab6b8341458650d384b4c43362f7ff\",\"traceId\":\"03901667538294jlTsgPqPAkJvEX3\",\"outParams\":\"{\\\"outEncodeType\\\":0}\",\"ownerId\":\"390\",\"coverImageUrl\":\"https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667538294jlTsgPqPAkJvEX3_gallery.jpg\",\"events\":[{\"name\":\"EXIST\",\"objectCategory\":\"PERSON\",\"timeRanges\":[{\"begin\":0,\"end\":0}],\"boxIds\":[],\"summaryUrl\":\"https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667538294jlTsgPqPAkJvEX3_gallery.jpg\",\"activityZoneIds\":[],\"filterResult\":0,\"filterReason\":\"\"},{\"name\":\"VEHICLE_HELD_UP\",\"objectCategory\":\"VEHICLE\",\"timeRanges\":[{\"begin\":0,\"end\":0}],\"boxIds\":[],\"summaryUrl\":\"https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667538294jlTsgPqPAkJvEX3_gallery.jpg\",\"activityZoneIds\":[],\"filterResult\":0,\"filterReason\":\"\"}],\"reIds\":[{\"labelId\":\"unknown\",\"eventIndexes\":[1]}]}";
        input = JSON.parseObject(str, SaasAITaskOM.class);
        input.setTraceId(video.getTraceId());
        input.getEvents().get(0).setSummaryUrl("").setObjectCategory(RecognitionObjectCategory.BIRD);
        videoGenerateService.handleAIEventResult(deviceCache, video, input);

    }

    @Test
    public void test_handleReportEvent() {
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(OpenApiUtil.shortUUID());
        VideoCache video = createMockVideoCache(deviceCache.getSerialNumber(), OpenApiUtil.shortUUID());
        String str = "{\"serialNumber\":\"IS_uAUN6qkExlnamhV3GPqFF\",\"attachment\":{\"pirStartTime\":1667538308911,\"pirEndTime\":1667538308928,\"mqttEventTime\":1667538308929},\"name\":\"reportEvent\",\"id\":0,\"time\":1667538308906,\"type\":\"REPORT_EVENT\",\"value\":{\"traceId\":\"QQqwyrRkeRn93skIKzsgk6\",\"data\":\"\",\"videoUploadType\":1,\"battery\":98,\"event\":1}}";
        DeviceReportEventDO reportEventDO = JSON.parseObject(str, DeviceReportEventDO.class);
        reportEventDO.setAttachment(new JSONObject());
        reportEventDO.getValue().setTraceId(video.getTraceId());
        {
            video.setDoorbellPressNotifySwitch(false);
            reportEventDO.getValue().setEvent(EReportEvent.PIR.getEventId());
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);
            reportEventDO.getValue().setEvent(EReportEvent.DOORBELL_PRESS.getEventId());
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);
            reportEventDO.getValue().setEvent(EReportEvent.DOORBELL_REMOVE.getEventId());
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);
            reportEventDO.getValue().setEvent(EReportEvent.DEVICE_CALL.getEventId());
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);
        }
        for (VideoSliceDO slice : createMockSliceList(video, 5)) {
            videoGenerateService.handleSliceReport(deviceCache, video, slice);
        }
        {
            video.setDoorbellPressNotifySwitch(true);
            reportEventDO.getValue().setEvent(EReportEvent.PIR.getEventId());
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);
            reportEventDO.getValue().setEvent(EReportEvent.DOORBELL_PRESS.getEventId());
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);
            reportEventDO.getValue().setEvent(EReportEvent.DOORBELL_REMOVE.getEventId());
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);
            reportEventDO.getValue().setEvent(EReportEvent.DEVICE_CALL.getEventId());
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);

            reportEventDO.getValue().setEvent(EReportEvent.AI_EDGE_EVENT.getEventId());
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);
            reportEventDO.getValue().setEvents(Arrays.asList(new AiEvent()));
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);
            when(vipService.isBirdVipDevice(anyInt(), any())).thenReturn(true);
            videoGenerateService.handleReportEvent(deviceCache, video, reportEventDO);
        }

    }

    @Test
    public void test_resendVideoMsg() {
        Assert.assertFalse(videoGenerateService.resendVideoMsg(VideoMsgType.IMAGE_REPORT, "", new JSONObject()));
        Assert.assertFalse(videoGenerateService.resendVideoMsg(VideoMsgType.SLICE_REPORT, "", new JSONObject()));
        Assert.assertFalse(videoGenerateService.resendVideoMsg(VideoMsgType.UPLOAD_COMPLETE, "", new JSONObject()));
        Assert.assertTrue(videoGenerateService.resendVideoMsg(VideoMsgType.AI_RECOGNITION_RESULT, "", new JSONObject()));
        Assert.assertTrue(videoGenerateService.resendVideoMsg(VideoMsgType.AI_EVENT_RESULT, "", new JSONObject()));
        Assert.assertFalse(videoGenerateService.resendVideoMsg(VideoMsgType.REPORT_EVENT, "", new JSONObject()));

        Assert.assertFalse(videoGenerateService.resendVideoMsg(VideoMsgType.AI_RECOGNITION_RESULT, "", new JSONObject().fluentPut(MSG_SRC, MSG_SRC_IOT_SERVICE)));
        Assert.assertFalse(videoGenerateService.resendVideoMsg(VideoMsgType.AI_EVENT_RESULT, "", new JSONObject().fluentPut(MSG_SRC, MSG_SRC_IOT_SERVICE)));
    }

    @Test
    public void test_handleReportEvent_notifyAlexa() {
        DeviceCache deviceCache = videoGenerateService.getDeviceCache(OpenApiUtil.shortUUID());
        VideoCache video = createMockVideoCache(deviceCache.getSerialNumber(), OpenApiUtil.shortUUID());

        final DeviceReportEventDO reportEvent = new DeviceReportEventDO();
        reportEvent.setValue(reportEvent.new ReportEventRequestValue());
        reportEvent.getValue().setEvent(EReportEvent.DOORBELL_PRESS.getEventId());
        video.setDoorbellPressNotifySwitch(true);
        video.setDoorbellPressNotifyMsgType(MsgType.DEVICE_CALL_MSG);
        {
            reportEvent.setAttachment(null);
            videoGenerateService.handleReportEvent(null, video, reportEvent);
        }
        {
            reportEvent.setAttachment(new JSONObject());
            videoGenerateService.handleReportEvent(null, video, reportEvent);
        }
        {
            reportEvent.setAttachment(new JSONObject().fluentPut(S3_VIDEO_SLICE_INFO_NOTIFY_ALEXA, true));
            videoGenerateService.handleReportEvent(null, video, reportEvent);
        }

    }

    @Test
    public void test_videoCache() {
        VideoCache videoCache = new VideoCache("sn_01", "xxx", System.currentTimeMillis());
        videoCache.setAiEdgeEvents(Arrays.asList(new AiEvent() {{
            setEventObject(AiObjectEnum.PERSON);
            setEventType(AiObjectActionEnum.EXIST);
        }}, new AiEvent() {{
            setEventObject(AiObjectEnum.PET);
            setEventType(AiObjectActionEnum.EXIST);
        }}));
        String aiEventTag = videoCache.getAIEdgeTags();
        Assert.assertTrue(aiEventTag.equals(AiObjectEnum.PERSON.getObjectName() + "," + AiObjectEnum.PET.getObjectName()));

        videoCache.setDefaultSaasAITask(new SaasAITaskIM() {{
        }});
        aiEventTag = videoCache.getAIEdgeTags();
        Assert.assertTrue(aiEventTag.equals(AiObjectEnum.PERSON.getObjectName() + "," + AiObjectEnum.PET.getObjectName()));

        videoCache.setDefaultSaasAITask(new SaasAITaskIM() {{
            setRecognitionObjects(Arrays.asList(new RecognitionObject() {{
                setCategory(RecognitionObjectCategory.PERSON);
            }}));
        }});
        aiEventTag = videoCache.getAIEdgeTags();
        Assert.assertTrue(aiEventTag.equals(AiObjectEnum.PET.getObjectName()));
    }

    @Test
    public void test_otaRequest() {

    }

    @Test
    public void test_VideoCache_setIsInActivityZone() {
        {
            VideoCache.setIsInActivityZone(null);
        }
        {
            SaasAITaskOM result = new SaasAITaskOM();
            VideoCache.setIsInActivityZone(result);
        }
        {
            SaasAITaskOM result = new SaasAITaskOM();
            result.setOutParams(null);
            result.setEvents(new LinkedList<>());
            result.getEvents().add(null);
            result.getEvents().add(new SaasAITaskOM.EventOM().setActivityZoneIds(null));
            VideoCache.setIsInActivityZone(result);
            Assert.assertEquals(true, result.getEvents().get(1).getIsInActivityZone());
        }
        {
            SaasAITaskOM result = new SaasAITaskOM();
            result.setOutParams("");
            result.setEvents(new LinkedList<>());
            result.getEvents().add(null);
            result.getEvents().add(new SaasAITaskOM.EventOM().setActivityZoneIds(null));
            result.getEvents().add(new SaasAITaskOM.EventOM().setActivityZoneIds(Arrays.asList()));
            result.getEvents().add(new SaasAITaskOM.EventOM().setActivityZoneIds(Arrays.asList(123)));

            VideoCache.setIsInActivityZone(result);

            Assert.assertEquals(true, result.getEvents().get(1).getIsInActivityZone());
            Assert.assertEquals(true, result.getEvents().get(2).getIsInActivityZone());
        }
        {
            SaasAITaskOM result = new SaasAITaskOM();
            result.setOutParams("{\"userSetAz\":false}");

            result.setImages(new LinkedList<>());
            result.getImages().add(new SaasAITaskOM.ImageOM().setObjects(new LinkedList<>()));
            List<SaasAITaskOM.RecognisedObject> objects = result.getImages().get(0).getObjects();
            objects.add(null);
            objects.add(new SaasAITaskOM.RecognisedObject().setActivityZoneIds(null));
            objects.add(new SaasAITaskOM.RecognisedObject().setActivityZoneIds(Arrays.asList()));
            objects.add(new SaasAITaskOM.RecognisedObject().setActivityZoneIds(Arrays.asList(123)));

            VideoCache.setIsInActivityZone(result);

            Assert.assertEquals(true, objects.get(1).getIsInActivityZone());
            Assert.assertEquals(true, objects.get(2).getIsInActivityZone());
            Assert.assertEquals(true, objects.get(3).getIsInActivityZone());
        }
        {
            SaasAITaskOM result = new SaasAITaskOM();
            result.setOutParams("{\"userSetAz\":true}");
            result.setEvents(new LinkedList<>());
            result.getEvents().add(null);
            result.getEvents().add(new SaasAITaskOM.EventOM().setActivityZoneIds(null));
            result.getEvents().add(new SaasAITaskOM.EventOM().setActivityZoneIds(Arrays.asList()));
            result.getEvents().add(new SaasAITaskOM.EventOM().setActivityZoneIds(Arrays.asList(123)));

            VideoCache.setIsInActivityZone(result);

            Assert.assertEquals(false, result.getEvents().get(1).getIsInActivityZone());
            Assert.assertEquals(false, result.getEvents().get(2).getIsInActivityZone());
            Assert.assertEquals(true, result.getEvents().get(3).getIsInActivityZone());
        }
        {
            SaasAITaskOM result = new SaasAITaskOM();
            result.setOutParams("{\"userSetAz\":true}");

            result.setImages(new LinkedList<>());
            result.getImages().add(new SaasAITaskOM.ImageOM().setObjects(new LinkedList<>()));
            List<SaasAITaskOM.RecognisedObject> objects = result.getImages().get(0).getObjects();
            objects.add(null);
            objects.add(new SaasAITaskOM.RecognisedObject().setActivityZoneIds(null));
            objects.add(new SaasAITaskOM.RecognisedObject().setActivityZoneIds(Arrays.asList()));
            objects.add(new SaasAITaskOM.RecognisedObject().setActivityZoneIds(Arrays.asList(123)));

            VideoCache.setIsInActivityZone(result);

            Assert.assertEquals(false, objects.get(1).getIsInActivityZone());
            Assert.assertEquals(false, objects.get(2).getIsInActivityZone());
            Assert.assertEquals(true, objects.get(3).getIsInActivityZone());
        }
    }

    @Test
    public void test_retryNotifyVideoReportEvent() {
        Object[] lastArgs = new Object[2];
        doAnswer(it -> {
            VideoCache videoCache = (VideoCache) it.getArguments()[0];
            EReportEvent reportEvent = (EReportEvent) it.getArguments()[1];
            lastArgs[0] = videoCache.getTraceId();
            lastArgs[1] = reportEvent;
            return null;
        }).when(videoNotifyService).notifyVideoReportEvent(any(), any(), any());

        {
            VideoCache videoCache = new VideoCache(OpenApiUtil.shortUUID(), OpenApiUtil.shortUUID(), 1L);
            Arrays.fill(lastArgs, null);
            videoGenerateService.retryNotifyVideoReportEvent(videoCache);
            Assert.assertArrayEquals(new Object[]{null, null}, lastArgs);
        }
        {
            VideoCache videoCache = new VideoCache(OpenApiUtil.shortUUID(), OpenApiUtil.shortUUID(), 1L);
            videoCache.getDoorbellEvents().add(new VideoReportEvent().setEvent(EReportEvent.DOORBELL_PRESS.getEventId()));
            videoCache.setDoorbellPressNotifySwitch(true);
            Arrays.fill(lastArgs, null);
            videoGenerateService.retryNotifyVideoReportEvent(videoCache);
            Assert.assertArrayEquals(new Object[]{videoCache.getTraceId(), EReportEvent.DOORBELL_PRESS}, lastArgs);
        }
        {
            VideoCache videoCache = new VideoCache(OpenApiUtil.shortUUID(), OpenApiUtil.shortUUID(), 1L);
            videoCache.getDoorbellEvents().add(new VideoReportEvent().setEvent(EReportEvent.DOORBELL_PRESS.getEventId()));
            videoCache.setDoorbellPressNotifySwitch(false);
            Arrays.fill(lastArgs, null);
            videoGenerateService.retryNotifyVideoReportEvent(videoCache);
            Assert.assertArrayEquals(new Object[]{null, null}, lastArgs);
        }
        {
            VideoCache videoCache = new VideoCache(OpenApiUtil.shortUUID(), OpenApiUtil.shortUUID(), 1L);
            videoCache.getDoorbellEvents().add(new VideoReportEvent().setEvent(EReportEvent.DOORBELL_REMOVE.getEventId()));
            Arrays.fill(lastArgs, null);
            videoGenerateService.retryNotifyVideoReportEvent(videoCache);
            Assert.assertArrayEquals(new Object[]{videoCache.getTraceId(), EReportEvent.DOORBELL_REMOVE}, lastArgs);
        }
        {
            VideoCache videoCache = new VideoCache(OpenApiUtil.shortUUID(), OpenApiUtil.shortUUID(), 1L);
            videoCache.getDeviceCallEvents().add(new VideoReportEvent().setEvent(EReportEvent.DEVICE_CALL.getEventId()));
            Arrays.fill(lastArgs, null);
            videoGenerateService.retryNotifyVideoReportEvent(videoCache);
            Assert.assertArrayEquals(new Object[]{videoCache.getTraceId(), EReportEvent.DEVICE_CALL}, lastArgs);
        }
        {
            VideoCache videoCache = new VideoCache(OpenApiUtil.shortUUID(), OpenApiUtil.shortUUID(), 1L);
            videoCache.setDoorbellEvents(null);
            Arrays.fill(lastArgs, null);
            videoGenerateService.retryNotifyVideoReportEvent(videoCache);
            Assert.assertArrayEquals(new Object[]{null, null}, lastArgs);
        }
    }
}
