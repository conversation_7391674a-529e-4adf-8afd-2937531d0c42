package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.apollo.AppUser;
import com.addx.iotcamera.bean.domain.HttpTokenDO;
import com.addx.iotcamera.bean.init.AmazonInit;
import com.addx.iotcamera.bean.openapi.*;
import com.addx.iotcamera.config.AppUserConfig;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.dao.openapi.DeviceThirdConfigDAO;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.service.openapi.OpenApiAuthService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.openapi.OpenApiUserService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.HttpUtils;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.apache.commons.io.IOUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.Arrays;
import java.util.Random;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class OpenApiAuthServiceTest {

    @InjectMocks
    private OpenApiUserService openApiUserService;
    @Mock
    private UserService userService;
    @Mock
    private RedisService redisService;
    @Mock
    private AppUserConfig appUserConfig;
    @Mock
    private TokenService tokenService;
    @InjectMocks
    private OpenApiConfigService deviceThirdConfigService;

    @Before
    public void init() {
        userService.setUserDAO(TestHelper.getInstanceByLocal().getMapper(IUserDAO.class));
        when(redisService.setIfAbsent(any(), any(), anyLong(), any())).thenReturn(true);
        when(appUserConfig.getConfig()).thenReturn(ImmutableMap.<String, AppUser>builder()
                .put("guard", new AppUser().setNamepre("guard_").setProductid(123))
                .build());
        HttpTokenDO httpTokenDO = new HttpTokenDO().setToken("123").setTokenType("Bearer");
        when(tokenService.generateMsgToken(any(), any())).thenReturn(httpTokenDO);
        log.info("");
    }

    @After
    public void after() {
        TestHelper.getInstanceByLocal().commitAndClose();
    }

    @Test
    public void test() {
    }

//    @Test
    public void generate_ak_sk() throws Exception {
        Random random = new Random();
        String accountId = "testAccount_" + (random.nextInt(90000) + 10000);
        String accessKey = OpenApiUtil.createAccessKey();
        String accessSecret = OpenApiUtil.createAccessSecret();
        log.info("\naccessKey   : {}\naccessSecret: {}", accessKey, accessSecret);
        OpenApiAuthService openApiAuthService = new OpenApiAuthService() {
            @Override
            public Result<OpenApiAccount> validateSign(String rowRequestUrl, String rowQueryString) {
                OpenApiAccount account = new OpenApiAccount().setAccountId(accountId)
                        .setCurrentAccess(new OpenApiAccess()
                                .setAccessKey(accessKey).setAccessSecret(accessSecret))
                        .setTenantIds(Arrays.asList("guard"));
                return new Result<>(account);
            }
        };

        String rowUrl = "https://api-staging-us.vicohome.io/open-api/auth/token";
        String signedUrl = OpenApiAuthService.createSignedUrl(rowUrl, accessKey, accessSecret);
        String[] strs = signedUrl.split("\\?");

        openApiAuthService.setAuthUrlForValidateRequest("http://10.0.4.9:9101/auth/validate-request");
        Result<OpenApiAccount> result = openApiAuthService.validateSign(strs[0], strs[1]);
        log.info(JSON.toJSONString(result, true));
        Assert.assertEquals(new Integer(0), result.getResult());
    }

    private String rootPath_test = "https://api-test.addx.live";
    private String rootPath_staging_cn = "https://api-stage.addx.live";
    private String rootPath_staging_eu = "https://api-staging-eu.vicohome.io";
    private String rootPath_staging_us = "https://api-staging-us.vicohome.io";
    private String rootPath_prod_us = "https://api-us.addx.live";

    private String rootPath = rootPath_prod_us;
//    private String accessKey = "6eTq8z8ca2qhEJFLvBP6f7";
//    private String accessSecret = "8ArRXDLBS+yLr/guEyUE9Q==";
//    private String tenantId = "netvue";

//    private String accessKey = "FXjWGFS4NmVxt8nyvHnOZ5";
//    private String accessSecret = "7pfSn46KeBVgkLzoToREm6";
//    private String tenantId = "xsense";

//    private String accessKey = "bs5JVb7noHL8X1mnOcUy12";
//    private String accessSecret = "+APZbPeMTCKmUY8goJkQ2w==";
//    private String tenantId = "netvue";

    private String accessKey = "Rzu19VTfl99VEuKS3RS5S3";
    private String accessSecret = "JxzFFNJ0TBSfgWKUJE886g==";
    private String tenantId = "longse";

    private String authTokenUrl = rootPath + "/open-api/auth/token";
    private Random random = new Random();

//    @Test
    public void test_authToken_create() {
        String signedUrl = OpenApiAuthService.createSignedUrl(authTokenUrl, accessKey, accessSecret);

        AppUserTokenCreate input = new AppUserTokenCreate().setTenantId(tenantId).setUserId(OpenApiUtil.createAccessKey())
                .setCountryNo("cn").setLanguage("zh")
                .setEmail("<EMAIL>").setPhone("***********");
        String curlCmd = "curl -X POST '" + signedUrl + "' -H 'Content-Type:application/json' -d '" + JSON.toJSONString(input) + "'";
        log.info("curlCmd:\n{}", curlCmd);
        String respBody = HttpUtils.httpPostPojo(input, signedUrl);
        log.info("test_authToken_create respBody:{}", respBody);
//        Result<AppUserToken> result = openApiUserService.registerOrLogin("testAccount_18613", input);
//        log.info("result:{}", JSON.toJSONString(result));
    }

    //    @Test
    public void test_authToken_update() {
        String signedUrl = OpenApiAuthService.createSignedUrl(authTokenUrl, accessKey + "123", accessSecret);

        AppUserTokenCreate input = new AppUserTokenCreate()
                .setTenantId("guard").setUserId("Y2jcyLNVl1RiF4NFNZYPo")
                .setCountryNo("CN").setLanguage("zh")
//                .setEmail("").setPhone("138" + (random.nextInt(9000_0000) + 1000_0000));
                .setEmail("123").setPhone("************");
        String respBody = HttpUtils.httpPostPojo(input, signedUrl);
        log.info("test_authToken_update respBody:{}", respBody);
//        Result<AppUserToken> result = openApiUserService.registerOrLogin("testAccount_18613", input);
//        log.info("result:{}", JSON.toJSONString(result));
    }

    //    @Test
    public void test_deviceThirdConfigDAO() {
        DeviceThirdConfigDAO deviceThirdConfigDAO = TestHelper.getInstanceByLocal().getMapper(DeviceThirdConfigDAO.class);
        DeviceThirdConfig config = new DeviceThirdConfig().setId(OpenApiUtil.shortUUID())
                .setTenantId("guard").setSerialNumber(OpenApiUtil.shortUUID())
                .setContent("{\"a\":1,\"b\":2,\"c\":\"abc\"}");
        Assert.assertEquals(1, deviceThirdConfigDAO.create(config));
        Assert.assertNotNull(deviceThirdConfigDAO.queryById(config.getId()));
        Assert.assertEquals(1, deviceThirdConfigDAO.updateContentById("{\"a\":1,\"b\":2,\"c\":\"abc\"}", config.getId()));
        Assert.assertNotNull(deviceThirdConfigDAO.queryIdByTenantIdAndSn(config.getTenantId(), config.getSerialNumber()));
    }

    private String createConfigUrlPtn = rootPath + "/open-api/tenants/${tenantId}/devices/${sn}/configs";

    //    @Test
    public void test_createConfig() {
        String createConfigUrl = createConfigUrlPtn.replace("${tenantId}", "guard")
                .replace("${sn}", OpenApiUtil.shortUUID());
        String signedUrl = OpenApiAuthService.createSignedUrl(createConfigUrl, accessKey, accessSecret);
        JSONObject input = new JSONObject()
                .fluentPut("key1", 123)
                .fluentPut("key2", "abc")
                .fluentPut("key3", Arrays.asList("openapi/device_config", "y", "z"))
                .fluentPut("key4", true);
        String respBody = HttpUtils.httpPostPojo(input, signedUrl);
        log.info("test_createConfig respBody:{}", respBody);
    }

    private String updateConfigUrlPtn = rootPath + "/open-api/tenants/${tenantId}/devices/${sn}/configs/${configId}";

    //    @Test
    public void test_updateConfig() throws IOException {
        String createConfigUrl = updateConfigUrlPtn.replace("${tenantId}", "guard")
                .replace("${sn}", "ZUABw47676L0KARS53pDi3")
                .replace("${configId}", "xvSX7Rw5HZRBopYVhechg1");
        String signedUrl = OpenApiAuthService.createSignedUrl(createConfigUrl, accessKey, accessSecret);
//        JSONObject input = new JSONObject()
//                .fluentPut("key1", random.nextInt())
//                .fluentPut("key2", "abcdef")
//                .fluentPut("key3", Arrays.asList("x", "y", "z", "i", "j", "k"))
//                .fluentPut("key4", false);
        String json = IOUtils.toString(ConfigHelper.loadConfig("classpath:example/tian_he_rong.json"));
        JSONObject input = JSON.parseObject(json);
        String respBody = HttpUtils.httpPutPojo(input, signedUrl);
        log.info("test_updateConfig respBody:{}", respBody);
    }

//    @Test
    public void test_parseOpenApiConfig() throws IOException {
        String json = IOUtils.toString(ConfigHelper.loadConfig("classpath:example/tian_he_rong.json"));
        JSONObject jsonObject = JSON.parseObject(json);
//        OpenApiConfigService.handleDeviceConfigFields(jsonObject);
        OpenApiDeviceConfig config = OpenApiDeviceConfig.fromJson(json);
//        String str = OpenApiConfigService.buildConfigMessage(config);
//        Assert.assertEquals(json, config.toString());

        AmazonInit amazonInit = TestHelper.getInstanceByLocal().getAmazonInit();
        DynamoDBMapper dynamoDBMapper = amazonInit.deviceConfigMapper();
        config.setConfigId(OpenApiUtil.shortUUID());
//        config.setSerialNumber(OpenApiUtil.shortUUID());
        config.setSerialNumber("288eb1ea8971fef2115b9adf41021d3b");
        dynamoDBMapper.save(config);
    }

    @Test
    public void test_validate_request_resp_body() throws IOException {
        String respBody = "{\"result\":0,\"message\":\"Success\",\"data\":{\"accountId\":null,\"currentAccess\":{\"accessKey\":\"6eTq8z8ca2qhEJFLvBP6f7\"},\"tenantIds\":[\"6eTq8z8ca2qhEJFLvBP6f7\"]}}";
        Result<OpenApiAccount> result = JSON.parseObject(respBody, new TypeReference<Result<OpenApiAccount>>() {
        }.getType());
        log.info("result:{}", JSON.toJSONString(result));
    }
}
