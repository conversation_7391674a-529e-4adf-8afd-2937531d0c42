package com.addx.iotcamera.service.pay;

import com.addx.iotcamera.bean.app.vip.GoogleHistoryRequest;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.serve.ServeConfig;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.pay.IOrderProductDAO;
import com.addx.iotcamera.dao.pay.IPaymentFlowDAO;
import com.addx.iotcamera.service.ProductService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.vip.OrderService;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class OrderServiceTest {
    @InjectMocks
    private OrderService orderService;

    @Mock
    private IOrderProductDAO iOrderProductDAO;

    @Mock
    private ProductService productService;

    @Mock
    private IOrderDAO iOrderDAO;

    @Mock
    private ApplePayService applePayService;

    @Mock
    private IPaymentFlowDAO iPaymentFlowDAO;

    @Mock
    private UserService userService;

    @Mock
    private ServeConfig serveConfig;

    @Test
    @DisplayName("参数orderIds 为空")
    public void queryProductBatch_orderId_empty(){
        Map<Long, ProductDO> expectResult = Maps.newHashMap();
        Map<Long, ProductDO> actualResult = orderService.queryProductBatch(Lists.newArrayList());

        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("订单商品关系为空")
    public void queryProductBatch_order_empty(){
        Map<Long, ProductDO> expectResult = Maps.newHashMap();
        when(iOrderProductDAO.selectOrderProductByOrderIdBatch(any())).thenReturn(Lists.newArrayList());
        Map<Long, ProductDO> actualResult = orderService.queryProductBatch(Lists.newArrayList());
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("查询订单商品关系")
    public void queryProductBatch(){
        Long orderId1 = 1L;
        Long orderId2 = 2L;
        Integer productId1 = 20000;
        Integer productId2 = 20000;
        List<OrderProductDo> orderProductDoList = Lists.newArrayList();
        OrderProductDo orderProductDo1 = OrderProductDo.builder()
                .orderId(orderId1)
                .productId(productId1)
                .build();
        orderProductDoList.add(orderProductDo1);
        OrderProductDo orderProductDo2 = OrderProductDo.builder()
                .orderId(orderId2)
                .productId(productId2)
                .build();
        orderProductDoList.add(orderProductDo2);

        when(iOrderProductDAO.selectOrderProductByOrderIdBatch(any())).thenReturn(orderProductDoList);

        ProductDO productDO1 = new ProductDO();
        productDO1.setId(productId1);
        when(productService.queryProductById(productId1)).thenReturn(productDO1);

        ProductDO productDO2 = new ProductDO();
        productDO2.setId(productId2);
        when(productService.queryProductById(productId2)).thenReturn(productDO2);
        Map<Long, ProductDO> expectResult = Maps.newHashMap();
        expectResult.put(orderId1,productDO1);
        expectResult.put(orderId2,productDO1);

        List<Long> orderIds = Lists.newArrayList();
        orderIds.add(orderId1);
        orderIds.add(orderId2);
        Map<Long, ProductDO> actualResult = orderService.queryProductBatch(orderIds);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("查询订单与商品关系")
    public void queryOrderProduct(){
        OrderProductDo orderProductDo = new OrderProductDo();
        orderProductDo.setOrderId(1L);
        orderProductDo.setProductId(20000);

        when(iOrderProductDAO.selectOrderProductByOrderId(orderProductDo.getOrderId())).thenReturn(orderProductDo);

        OrderProductDo actualResult = orderService.queryOrderProductDO(orderProductDo.getOrderId());
        Assert.assertEquals(orderProductDo,actualResult);
    }



    @Test
    @DisplayName("查询订单与商品关系")
    public void queryUserOrderList(){
        OrderDO order1 = new OrderDO();
        order1.setId(1L);

        OrderDO orderDO2 = new OrderDO();
        orderDO2.setId(2L);

        List<OrderDO> orderDOList = new ArrayList<>();
        orderDOList.add(order1);
        orderDOList.add(orderDO2);
        when(iOrderDAO.queryUserOrderList(any())).thenReturn(orderDOList);

        List<OrderDO> actualResult = orderService.queryUserOrderList(1);
        Assert.assertEquals(orderDOList,actualResult);
    }

    @Test
    @DisplayName("查询用户实际支付次数-无支付")
    public void userOrderPaymentByUser_test_empty(){
        int expectedResult = 0;
        int actualResult = orderService.userOrderPaymentByUser(Lists.newArrayList());
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("查询用户实际支付次数")
    public void userOrderPaymentByUser_test(){
        int expectedResult = 1;
        when(iOrderDAO.queryUserPaymentOrderCount(any())).thenReturn(1);
        int actualResult = orderService.userOrderPaymentByUser(Arrays.asList(1L));
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("交易订单号空")
    public void test_querySubOrder_tradeNo_null(){
        String tradeNo = null;
        OrderDO expectedResult = null;
        OrderDO actualResult = orderService.querySubOrder(tradeNo);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("交易订单号空")
    public void test_querySubOrder(){
        String tradeNo = "tradeNo";
        OrderDO orderDO = new OrderDO();
        orderDO.setId(1L);
        orderDO.setTradeNo(tradeNo);
        
        OrderDO expectedResult = orderDO;
        when(iOrderDAO.queryBytradeNo(any())).thenReturn(orderDO);
        OrderDO actualResult = orderService.querySubOrder(tradeNo);

        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    public void test_updateOrderCancel(){
        orderService.updateOrderCancel(new OrderDO());
        verify(iOrderDAO, times(1)).updateOrderCancelTime(any());
    }

    @Test
    @DisplayName("查询订单信息")
    public void test_queryOrderDOByOrderSn(){
        when(iOrderDAO.queryByOrderSn(any())).thenReturn(null);
        orderService.queryOrderDOByOrderSn("");
        verify(iOrderDAO, times(1)).queryByOrderSn(any());
    }

    @Test
    @DisplayName("找不到订单流水-原订单号")
    public void test_notifyGoogleOrder_notcancel_paymentFlow_null(){
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId("t");
        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = Lists.newArrayList();
        GoogleHistoryRequest.GoogleOrderNotifyEven notifyEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
        notifyEven.setNotifyType(0);
        notifyTypeList.add(notifyEven);
        request.setNotifyTypeList(notifyTypeList);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(null);

        boolean expectResult = false;
        boolean actualResult = orderService.notifyGoogleOrder(request);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("找不到订单流水-找不到order")
    public void test_notifyGoogleOrder_order_null(){
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId("t..0");
        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = Lists.newArrayList();
        GoogleHistoryRequest.GoogleOrderNotifyEven notifyEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
        notifyEven.setNotifyType(2);
        notifyTypeList.add(notifyEven);
        request.setNotifyTypeList(notifyTypeList);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(null);

        when(iOrderDAO.queryBytradeNo(any())).thenReturn(null);

        boolean expectResult = false;
        boolean actualResult = orderService.notifyGoogleOrder(request);
        Assert.assertEquals(expectResult,actualResult);
    }
    @Test
    @DisplayName("找不到订单流水-无支付流水-找不到源订单号支付流水")
    public void test_notifyGoogleOrder_order_payment_null(){
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId("t..0");
        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = Lists.newArrayList();
        GoogleHistoryRequest.GoogleOrderNotifyEven notifyEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
        notifyEven.setNotifyType(2);
        notifyTypeList.add(notifyEven);
        request.setNotifyTypeList(notifyTypeList);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(null);

        when(iOrderDAO.queryBytradeNo(any())).thenReturn(new OrderDO());

        when(iPaymentFlowDAO.queryPaymentFlowLast(any())).thenReturn(null);

        boolean expectResult = false;
        boolean actualResult = orderService.notifyGoogleOrder(request);
        Assert.assertEquals(expectResult,actualResult);
    }




    @Test
    @DisplayName("有订单流水-无订单信息")
    public void test_notifyGoogleOrder_noorder(){
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId("t..0");
        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = Lists.newArrayList();
        GoogleHistoryRequest.GoogleOrderNotifyEven notifyEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
        notifyEven.setNotifyType(2);
        notifyEven.setNotifyReason(0);
        notifyTypeList.add(notifyEven);
        request.setNotifyTypeList(notifyTypeList);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(new PaymentFlow());

        when(iOrderDAO.queryBytradeNo(any())).thenReturn(null);

        when(iOrderDAO.updateOrderCancelTime(any())).thenReturn(1);
        boolean expectResult = false;
        boolean actualResult = orderService.notifyGoogleOrder(request);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("有订单流水-freeTrial")
    public void test_notifyGoogleOrder_freeTrial(){
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId("t..0");
        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = Lists.newArrayList();
        GoogleHistoryRequest.GoogleOrderNotifyEven notifyEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
        notifyEven.setNotifyType(0);
        notifyEven.setNotifyReason(0);
        notifyTypeList.add(notifyEven);
        request.setNotifyTypeList(notifyTypeList);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(new PaymentFlow());

        when(iOrderDAO.queryByOrderSn(any())).thenReturn(new OrderDO());

        when(iPaymentFlowDAO.updateRefundOrderInfo(any())).thenReturn(1);

        boolean expectResult = true;
        boolean actualResult = orderService.notifyGoogleOrder(request);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("找不到订单流水-退款")
    public void test_notifyGoogleOrder_refund(){
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId("t..0");
        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = Lists.newArrayList();
        GoogleHistoryRequest.GoogleOrderNotifyEven notifyEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
        notifyEven.setNotifyType(1);
        notifyEven.setNotifyReason(0);
        notifyTypeList.add(notifyEven);
        request.setNotifyTypeList(notifyTypeList);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(new PaymentFlow());

        when(iOrderDAO.queryByOrderSn(any())).thenReturn(new OrderDO());

        when(iPaymentFlowDAO.updateRefundOrderInfo(any())).thenReturn(1);


        boolean expectResult = true;
        boolean actualResult = orderService.notifyGoogleOrder(request);
        Assert.assertEquals(expectResult,actualResult);
    }



    @Test
    @DisplayName("找不到订单流水-其他原因")
    public void test_notifyGoogleOrder_refund_reason(){
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId("t..0");
        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = Lists.newArrayList();
        GoogleHistoryRequest.GoogleOrderNotifyEven notifyEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
        notifyEven.setNotifyType(1);
        notifyEven.setNotifyReason(1);
        notifyTypeList.add(notifyEven);
        request.setNotifyTypeList(notifyTypeList);

        PaymentFlow paymentFlow = new PaymentFlow();
        paymentFlow.setTimeStart(1);
        paymentFlow.setTimeEnd(2);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(paymentFlow);
        when(iOrderDAO.queryByOrderSn(any())).thenReturn(new OrderDO());
        when(iPaymentFlowDAO.updateRefundOrderInfo(any())).thenReturn(1);
        ProductDO productDO = new ProductDO();
        productDO.setMonth(1);
        when(productService.queryProductById(any())).thenReturn(productDO);

        when(userService.queryUserById(any())).thenReturn(new User());
        when(serveConfig.getServeReleaseTag()).thenReturn("");
        boolean expectResult = true;
        boolean actualResult = orderService.notifyGoogleOrder(request);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("找不到订单流水-其他原因")
    public void test_notifyGoogleOrder_refund_reason_usernull(){
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId("t..0");
        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = Lists.newArrayList();
        GoogleHistoryRequest.GoogleOrderNotifyEven notifyEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
        notifyEven.setNotifyType(1);
        notifyEven.setNotifyReason(1);
        notifyTypeList.add(notifyEven);
        request.setNotifyTypeList(notifyTypeList);

        PaymentFlow paymentFlow = new PaymentFlow();
        paymentFlow.setTimeStart(1);
        paymentFlow.setTimeEnd(2);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(paymentFlow);
        when(iOrderDAO.queryByOrderSn(any())).thenReturn(new OrderDO());
        when(iPaymentFlowDAO.updateRefundOrderInfo(any())).thenReturn(1);
        ProductDO productDO = new ProductDO();
        productDO.setMonth(1);
        when(productService.queryProductById(any())).thenReturn(productDO);

        when(userService.queryUserById(any())).thenReturn(null);
        when(serveConfig.getServeReleaseTag()).thenReturn("");
        boolean expectResult = true;
        boolean actualResult = orderService.notifyGoogleOrder(request);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("找不到订单流水-取消续订")
    public void test_notifyGoogleOrder_cancel(){
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId("t..0");
        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = Lists.newArrayList();
        GoogleHistoryRequest.GoogleOrderNotifyEven notifyEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
        notifyEven.setNotifyType(2);
        notifyEven.setNotifyReason(0);
        notifyTypeList.add(notifyEven);
        request.setNotifyTypeList(notifyTypeList);

        PaymentFlow paymentFlow = new PaymentFlow();
        paymentFlow.setTimeStart(1);
        paymentFlow.setTimeEnd(2);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(paymentFlow);
        when(iOrderDAO.queryByOrderSn(any())).thenReturn(new OrderDO());

        when(iOrderDAO.updateOrderCancelTime(any())).thenReturn(1);
        when(iPaymentFlowDAO.updateRefundOrderInfo(any())).thenReturn(1);


        boolean expectResult = true;
        boolean actualResult = orderService.notifyGoogleOrder(request);
        Assert.assertEquals(expectResult,actualResult);
    }



    @Test
    @DisplayName("找不到订单流水-其他原因")
    public void test_notifyGoogleOrder_cancel_reason(){
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId("t..0");
        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = Lists.newArrayList();
        GoogleHistoryRequest.GoogleOrderNotifyEven notifyEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
        notifyEven.setNotifyType(2);
        notifyEven.setNotifyReason(1);
        notifyTypeList.add(notifyEven);
        request.setNotifyTypeList(notifyTypeList);

        PaymentFlow paymentFlow = new PaymentFlow();
        paymentFlow.setTimeStart(1);
        paymentFlow.setTimeEnd(2);

        when(iPaymentFlowDAO.queryPaymentFlow(any())).thenReturn(paymentFlow);
        when(iOrderDAO.queryByOrderSn(any())).thenReturn(new OrderDO());
        when(iPaymentFlowDAO.updateRefundOrderInfo(any())).thenReturn(1);
        ProductDO productDO = new ProductDO();
        productDO.setMonth(1);
        when(productService.queryProductById(any())).thenReturn(productDO);

        boolean expectResult = true;
        boolean actualResult = orderService.notifyGoogleOrder(request);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    public void test_queryBytradeNoBatch_null(){
        List<OrderDO> expectedResult = Lists.newArrayList();
        List<OrderDO> actualResult = orderService.queryBytradeNoBatch("");
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    public void test_queryBytradeNoBatch(){
        OrderDO orderDO = new OrderDO();

        List<OrderDO> expectedResult = Lists.newArrayList();
        expectedResult.add(orderDO);
        when(iOrderDAO.queryBytradeNoBatch(any())).thenReturn(expectedResult);
        List<OrderDO> actualResult = orderService.queryBytradeNoBatch("test");
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("批量查询订单信息")
    public void test_queryOrderBatch(){
        List<OrderDO> exceptedResult ;
        List<OrderDO> actualResult;

        {
            exceptedResult = Lists.newArrayList();
            actualResult = orderService.queryOrderBatch(Lists.newArrayList());
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            exceptedResult = Collections.singletonList(new OrderDO());
            when(iOrderDAO.queryOrderBatch(any())).thenReturn(Collections.singletonList(new OrderDO()));
            actualResult = orderService.queryOrderBatch(Collections.singletonList(1L));
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }

    @Test
    @DisplayName("查询订单信息")
    public void test_queryOrderInfoById(){
        when(iOrderDAO.queryByOrderId(any())).thenReturn(new OrderDO());
        Assert.assertEquals(orderService.queryOrderInfoById(1L),new OrderDO());
    }

    @Test
    @DisplayName("查询订单订阅是否处于订阅中")
    public void test_verifyOrderSubStatus(){
        Long orderId ;
        Boolean actualResult;
        {
            orderId = null;
            actualResult = orderService.verifyOrderSubStatus(orderId);
            Assert.assertFalse(actualResult);
        }
        {
            orderId = 1L;
            when(iOrderDAO.queryByOrderId(any())).thenReturn(null);
            actualResult = orderService.verifyOrderSubStatus(orderId);
            Assert.assertFalse(actualResult);
        }
        {
            orderId = 1L;
            OrderDO orderDO = OrderDO.builder()
                    .subType(0)
                    .orderCancel(0)
                    .build();
            when(iOrderDAO.queryByOrderId(any())).thenReturn(orderDO);
            actualResult = orderService.verifyOrderSubStatus(orderId);
            Assert.assertFalse(actualResult);
        }
        {
            orderId = 1L;
            OrderDO orderDO = OrderDO.builder()
                    .subType(0)
                    .orderCancel(1)
                    .build();
            when(iOrderDAO.queryByOrderId(any())).thenReturn(orderDO);
            actualResult = orderService.verifyOrderSubStatus(orderId);
            Assert.assertFalse(actualResult);
        }
        {
            orderId = 1L;
            OrderDO orderDO = OrderDO.builder()
                    .subType(1)
                    .orderCancel(1)
                    .build();
            when(iOrderDAO.queryByOrderId(any())).thenReturn(orderDO);
            actualResult = orderService.verifyOrderSubStatus(orderId);
            Assert.assertFalse(actualResult);
        }
        {
            orderId = 1L;
            OrderDO orderDO = OrderDO.builder()
                    .subType(1)
                    .orderCancel(0)
                    .build();
            when(iOrderDAO.queryByOrderId(any())).thenReturn(orderDO);
            actualResult = orderService.verifyOrderSubStatus(orderId);
            Assert.assertTrue(actualResult);
        }
    }
}
