package com.addx.iotcamera.service.xxl_check;

import com.addx.iotcamera.bean.db.LibraryTb;
import com.addx.iotcamera.config.PaasVipConfig;
import com.addx.iotcamera.dao.xxl_check.VideoLibraryXxlJobCheckDAO;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryStatusDAO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.Callable;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoLibraryXxlJobCheckServiceTest {

    @Mock
    private UserVipService mockUserVipService;
    @Mock
    private PaasVipConfig mockPaasVipConfig;
    @Mock
    private UserService mockUserService;
    @Mock
    private JdbcTemplate mockJdbcTemplate;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private VideoLibraryXxlJobCheckDAO videoLibraryXxlJobCheckDAO;
    @Mock
    private IShardingLibraryDAO shardingLibraryDAO;
    @Mock
    private IShardingLibraryStatusDAO shardingLibraryStatusDAO;

    @InjectMocks
    private VideoLibraryXxlJobCheckService videoLibraryXxlJobCheckServiceUnderTest;

    private ThreadLocal<Map> jobInfoThreadLocal = new ThreadLocal<>();

    @Before
    public void init() throws Exception {
        Field jobInfoThreadLocalField = videoLibraryXxlJobCheckServiceUnderTest.getClass().getDeclaredField("jobInfoThreadLocal");
        jobInfoThreadLocalField.setAccessible(true);
        jobInfoThreadLocalField.set(videoLibraryXxlJobCheckServiceUnderTest, jobInfoThreadLocal);
        jobInfoThreadLocal.set(new HashMap());
    }

    @Test
    public void testNeedCheck() {
        // Setup
//        when(mockUserService.queryTenantIdById(anyInt())).thenReturn("result");
//        when(mockPaasVipConfig.tenantIsSupportDeviceVip(anyString())).thenReturn(false);
//        when(videoLibraryXxlJobCheckDAO.countTenantUser(any())).thenReturn(10);
//        when(videoLibraryXxlJobCheckDAO.getTenantUserIdByOffset(any(), eq(0))).thenReturn(1);
//        when(videoLibraryXxlJobCheckDAO.getTenantUserIdByOffset(any(), eq(5))).thenReturn(3);
//
//        when(mockJdbcTemplate.queryForList(anyString(), anyString(), anyInt())).thenReturn(Arrays.asList(new HashMap<String, Object>(){{
//            put("id", "1");
//        }}, new HashMap<String, Object>(){{
//            put("id", "3");
//        }}));
//
//        // Run the test
//        Boolean needCheck = videoLibraryXxlJobCheckServiceUnderTest.needCheck(0);
//        assertTrue(!needCheck);
//
//        needCheck = videoLibraryXxlJobCheckServiceUnderTest.needCheck(2);
//        assertTrue(needCheck);
//
//        needCheck = videoLibraryXxlJobCheckServiceUnderTest.needCheck(3);
//        assertTrue(needCheck);
//
//        needCheck = videoLibraryXxlJobCheckServiceUnderTest.needCheck(5);
//        assertTrue(!needCheck);
//
//        needCheck = videoLibraryXxlJobCheckServiceUnderTest.needCheck(6);
//        assertTrue(!needCheck);
        assertTrue(true);
    }

    @Test
    public void testNeedCheck_JdbcTemplateReturnsNoItems() {
        // Setup
//        when(mockUserService.queryTenantIdById(anyInt())).thenReturn("result");
//        when(mockPaasVipConfig.tenantIsSupportDeviceVip(anyString())).thenReturn(false);
//
//        // Run the test
//        final Boolean needCheck = videoLibraryXxlJobCheckServiceUnderTest.needCheck(0);
//        // Verify the results
//        assertTrue(needCheck);

        assertTrue(true);
    }

    @Test
    public void testBeforeMarkExpiredVideoLibraryByAdminIdAndTimestamp() {
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryInfo(anyInt())).thenReturn(Collections.singletonMap("key", "value"));

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.beforeMarkExpiredVideoLibraryByAdminIdAndTimestamp(0);

        // Verify the results
    }

    @Test
    public void testBeforeMarkExpiredVideoLibraryByAdminIdAndTimestamp_JdbcTemplateReturnsNoItems() {
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryInfo(anyInt())).thenReturn(Collections.emptyMap());

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.beforeMarkExpiredVideoLibraryByAdminIdAndTimestamp(0);

        // Verify the results
    }

    @Test
    public void testBeforeDeleteExpiredVideoLibraryByAdminIdAndTimestamp() {
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryList(any(), any())).thenReturn(Collections.singletonList(LibraryTb.builder().adminId(0).timestamp(0).build()));
        when(mockUserVipService.queryCurrentTierTime(anyInt(), anyLong())).thenReturn(0);

        jobInfoThreadLocal.get().put("minTimestamp", 0);
        jobInfoThreadLocal.get().put("maxTimestamp", 0);

        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteExpiredVideoLibraryByAdminIdAndTimestamp(0, Arrays.asList(1));
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteExpiredVideoLibraryByAdminIdAndTimestamp(1, Arrays.asList(1));
        Assert.assertTrue(Objects.equals(jobInfoThreadLocal.get().get("deleteLibraryTimestampMin"), 0));

        when(videoLibraryXxlJobCheckDAO.getAdminLibraryList(any(), any())).thenReturn(Collections.singletonList(LibraryTb.builder().adminId(0).timestamp(1).build()));
        jobInfoThreadLocal.get().remove("deleteLibraryTimestampMin");
        jobInfoThreadLocal.get().remove("deleteLibraryTimestampMax");
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteExpiredVideoLibraryByAdminIdAndTimestamp(0, Arrays.asList(1));
        Assert.assertTrue(Objects.equals(jobInfoThreadLocal.get().get("deleteLibraryTimestampMin"), 1));
    }

    @Test
    public void testBeforeDeleteExpiredVideoLibraryByAdminIdAndTimestamp_JdbcTemplateReturnsNoItems() {
        // Setup
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryList(any(), any())).thenReturn(Collections.emptyList());
        when(mockUserVipService.queryCurrentTierTime(anyInt(), anyLong())).thenReturn(0);

        jobInfoThreadLocal.get().put("minTimestamp", 0);
        jobInfoThreadLocal.get().put("maxTimestamp", 0);

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteExpiredVideoLibraryByAdminIdAndTimestamp(0, Arrays.asList(1));

        // Verify the results
        Assert.assertTrue(Objects.equals(jobInfoThreadLocal.get().get("storageStartTime"), 0));

        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteExpiredVideoLibraryByAdminIdAndTimestamp(0, Collections.emptyList());
    }

    @Test
    public void testAfterMarkExpiredVideoLibraryByAdminIdAndTimestamp() {
        jobInfoThreadLocal.get().put("minTimestamp", 0);
        jobInfoThreadLocal.get().put("maxTimestamp", 0);

        jobInfoThreadLocal.get().put("storageStartTime", 0);
        jobInfoThreadLocal.get().put("deleteLibraryTimestampMin", 1);

        // Setup
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryInfo(anyInt())).thenReturn(new HashMap<String, Object>(){{
            put("minTimestamp", -1);
        }});
        when(mockUserVipService.queryCurrentTierTime(anyInt(), anyLong())).thenReturn(0);

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.afterMarkExpiredVideoLibraryByAdminIdAndTimestamp(0);
        Assert.assertTrue(MapUtils.isEmpty(jobInfoThreadLocal.get()));

    }

    @Test
    public void testAfterMarkExpiredVideoLibraryByAdminIdAndTimestamp2() {
        jobInfoThreadLocal.get().put("minTimestamp", 0);
        jobInfoThreadLocal.get().put("maxTimestamp", 0);

        jobInfoThreadLocal.get().put("storageStartTime", 0);
        jobInfoThreadLocal.get().put("deleteLibraryTimestampMin", 0);

        when(videoLibraryXxlJobCheckDAO.getAdminLibraryInfo(anyInt())).thenReturn(new HashMap<String, Object>(){{
            put("minTimestamp", 1);
        }});
        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.afterMarkExpiredVideoLibraryByAdminIdAndTimestamp(0);
        Assert.assertTrue(MapUtils.isEmpty(jobInfoThreadLocal.get()));
    }

    @Test
    public void testAfterMarkExpiredVideoLibraryByAdminIdAndTimestamp_JdbcTemplateReturnsNoItems() {
        jobInfoThreadLocal.get().put("minTimestamp", 0);
        jobInfoThreadLocal.get().put("maxTimestamp", 0);

        jobInfoThreadLocal.get().put("storageStartTime", 0);
        jobInfoThreadLocal.get().put("deleteLibraryTimestampMin", 0);

        // Setup
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryInfo(anyInt())).thenReturn(null);
        when(mockUserVipService.queryCurrentTierTime(anyInt(), anyLong())).thenReturn(0);

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.afterMarkExpiredVideoLibraryByAdminIdAndTimestamp(0);
        Assert.assertTrue(MapUtils.isEmpty(jobInfoThreadLocal.get()));
        // Verify the results
    }

    @Test
    public void testBeforeMarkOutOfSizeVideoLibraryByAdminAndStorage() {
        // Setup
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryInfo(anyInt())).thenReturn(new HashMap<String, Object>(){{
            put("minTimestamp", 0);
        }});

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.beforeMarkOutOfSizeVideoLibraryByAdminAndStorage(0);
        Assert.assertTrue(ObjectUtils.equals(jobInfoThreadLocal.get().get("minTimestamp"), 0));
    }

    @Test
    public void testBeforeMarkOutOfSizeVideoLibraryByAdminAndStorage_JdbcTemplateReturnsNoItems() {
        // Setup
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryInfo(anyInt())).thenReturn(null);

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.beforeMarkOutOfSizeVideoLibraryByAdminAndStorage(0);
        Assert.assertTrue(!jobInfoThreadLocal.get().containsKey("minTimestamp"));

        // Verify the results
    }

    @Test
    public void testBeforeDeleteOutOfSizeVideoLibraryByAdminAndStorage() {
        // Setup
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryList(any(), any())).thenReturn(Collections.singletonList(LibraryTb.builder().adminId(0).timestamp(0).fileSize(1024L).build()));

        jobInfoThreadLocal.get().put("minTimestamp", 0);
        jobInfoThreadLocal.get().put("maxTimestamp", 0);

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteOutOfSizeVideoLibraryByAdminAndStorage(0, Arrays.asList(1));
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteOutOfSizeVideoLibraryByAdminAndStorage(1, Arrays.asList(1));
        Assert.assertTrue(jobInfoThreadLocal.get().containsKey("deleteLibraryFileSizeSum"));

        // Setup
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryList(any(), any())).thenReturn(Collections.singletonList(LibraryTb.builder().adminId(0).timestamp(1).fileSize(1024L).build()));

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteOutOfSizeVideoLibraryByAdminAndStorage(0, Arrays.asList(1));
        Assert.assertTrue(jobInfoThreadLocal.get().containsKey("deleteLibraryFileSizeSum"));
    }

    @Test
    public void testBeforeDeleteOutOfSizeVideoLibraryByAdminAndStorage_JdbcTemplateReturnsNoItems() {
        // Setup
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryList(any(), any())).thenReturn(Collections.emptyList());

        jobInfoThreadLocal.get().put("minTimestamp", 0);
        jobInfoThreadLocal.get().put("maxTimestamp", 0);

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteOutOfSizeVideoLibraryByAdminAndStorage(0, Arrays.asList(1));
        Assert.assertTrue(jobInfoThreadLocal.get().containsKey("deleteLibraryFileSizeSum"));
        // Verify the results

        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteOutOfSizeVideoLibraryByAdminAndStorage(0, Collections.emptyList());
    }

    @Test
    public void testAfterMarkOutOfSizeVideoLibraryByAdminAndStorage() {
        jobInfoThreadLocal.get().put("minTimestamp", 0);
        jobInfoThreadLocal.get().put("maxTimestamp", 0);

        jobInfoThreadLocal.get().put("storageStartTime", 0);
        jobInfoThreadLocal.get().put("deleteLibraryTimestampMin", 1);

        // Setup
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryInfo(anyInt())).thenReturn(new HashMap<String, Object>(){{
            put("libraryUseStorage", 512);
        }});
        when(mockUserVipService.queryCurrentTierSize(anyInt(), anyInt())).thenReturn(10240L);

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.afterMarkOutOfSizeVideoLibraryByAdminAndStorage(0);
        Assert.assertTrue(MapUtils.isEmpty(jobInfoThreadLocal.get()));
    }

    @Test
    public void testAfterMarkOutOfSizeVideoLibraryByAdminAndStorage_JdbcTemplateReturnsNoItems() {
        jobInfoThreadLocal.get().put("minTimestamp", 0);
        jobInfoThreadLocal.get().put("maxTimestamp", 0);

        jobInfoThreadLocal.get().put("storageStartTime", 0);
        jobInfoThreadLocal.get().put("deleteLibraryTimestampMin", 0);

        // Setup
        when(videoLibraryXxlJobCheckDAO.getAdminLibraryInfo(anyInt())).thenReturn(null);
        when(mockUserVipService.queryCurrentTierSize(eq(0), eq(0))).thenReturn(0L);

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.afterMarkOutOfSizeVideoLibraryByAdminAndStorage(0);
        Assert.assertTrue(MapUtils.isEmpty(jobInfoThreadLocal.get()));
        // Verify the results
    }

    @Test
    public void testBeforeClearVideoLibrary() {
        // Setup
        when(shardingLibraryStatusDAO.getAdminShardingLibraryInfo(anyInt(), any())).thenReturn(new HashMap<String, Object>(){{
            put("minTimestamp", 0);
            put("maxTimestamp", 0);
        }});

        when(mockRedisService.hashGetString(anyString(), anyString())).thenReturn("1,2,3,4,5,6,7,8");
        videoLibraryXxlJobCheckServiceUnderTest.beforeClearVideoLibrary(0);
        Assert.assertTrue(!jobInfoThreadLocal.get().containsKey("userLibraryStorageClearStartTime"));

        // Run the test
        when(mockRedisService.hashGetString(anyString(), anyString())).thenReturn("1,2,3,4,5,6,7,8,9,10,11");
        videoLibraryXxlJobCheckServiceUnderTest.beforeClearVideoLibrary(0);
        Assert.assertTrue(jobInfoThreadLocal.get().containsKey("userLibraryStorageClearStartTime"));
    }

    @Test
    public void testBeforeDeleteNeedClearVideoLibrary() {
        // Setup
        when(mockUserVipService.queryCurrentTierTime(0, 0L)).thenReturn(0);

        // Setup
        when(shardingLibraryDAO.getAdminShardingLibraryByTraceId(anyInt(), any())).thenReturn(LibraryTb.builder().timestamp(0).adminId(0).fileSize(1024L).build());

        jobInfoThreadLocal.get().put("minTimestamp", 0);
        jobInfoThreadLocal.get().put("maxTimestamp", 0);

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteNeedClearVideoLibrary(0, new HashSet<>(Arrays.asList("value")));
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteNeedClearVideoLibrary(1, new HashSet<>(Arrays.asList("value")));

        // Setup
        jobInfoThreadLocal.get().put("minTimestamp", 1);
        jobInfoThreadLocal.get().put("maxTimestamp", 1);

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteNeedClearVideoLibrary(0, new HashSet<>(Arrays.asList("value")));

        // Verify the results
        videoLibraryXxlJobCheckServiceUnderTest.beforeDeleteNeedClearVideoLibrary(0, new HashSet<>());
    }

    @Test
    public void testAfterClearVideoLibrary() {
        when(shardingLibraryStatusDAO.getAdminShardingLibraryInfo(any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("minTimestamp", 0);
            put("maxTimestamp", 1);
        }});

        // Setup
        when(mockRedisService.hashGetString(anyString(), anyString())).thenReturn("1,2,3,4,5,6,7,8,9,10");
        videoLibraryXxlJobCheckServiceUnderTest.afterClearVideoLibrary(0);
        Assert.assertTrue(MapUtils.isEmpty(jobInfoThreadLocal.get()));
    }

    @Test
    public void testAfterClearVideoLibrary2() {
        when(shardingLibraryStatusDAO.getAdminShardingLibraryInfo(any(), any())).thenReturn(new HashMap<String, Object>(){{
            put("minTimestamp", 0);
            put("maxTimestamp", 1);
        }});

        // Setup
        when(mockRedisService.hashGetString(anyString(), anyString())).thenReturn("1,2,3,4,5,6,7,8,9");

        jobInfoThreadLocal.get().put("shareUserIds", Collections.singleton("111"));
        jobInfoThreadLocal.get().put("deleteUserLibraryTraceIdSet", Collections.singleton("trace_01"));

        // Run the test
        videoLibraryXxlJobCheckServiceUnderTest.afterClearVideoLibrary(0);
        Assert.assertTrue(MapUtils.isEmpty(jobInfoThreadLocal.get()));
    }

    @Test
    public void testCheckNotExceptionally() {
        // Setup
        final Callable callable = null;

        // Run the test
        Object result = VideoLibraryXxlJobCheckService.checkNotExceptionally(callable);
        Assert.assertTrue(result == null);
        // Verify the results

        result = VideoLibraryXxlJobCheckService.checkNotExceptionally(() -> 1);
        Assert.assertTrue((int) result == 1);
    }
}
