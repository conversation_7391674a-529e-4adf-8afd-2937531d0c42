package com.addx.iotcamera.service.device_msg;

import com.addx.iotcamera.bean.db.device.DeviceKissFlagDO;
import com.addx.iotcamera.bean.device_msg.DeviceMsgSrc;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.service.DeviceKissFlagService;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceMsgSrcManagerTest {

    @Mock
    private MqttSender mqttSender;
    @Mock
    private DeviceMsgService deviceMsgService;
    @Mock
    private DeviceKissFlagService deviceKissFlagService;

    @InjectMocks
    private DeviceMsgSrcManager deviceMsgSrcManager;

    private Map<String, DeviceKissFlagDO> map = new LinkedHashMap<>();
    private AtomicLong time = new AtomicLong(0);

    @Before
    public void before() {
        when(deviceKissFlagService.save(any())).thenAnswer(it -> {
            DeviceKissFlagDO flagDO = it.getArgument(0);
            if (flagDO == null || flagDO.getSn() == null) return 0;
            map.put(flagDO.getSn(), flagDO);
            return 1;
        });
        when(deviceKissFlagService.queryBySn(any())).thenAnswer(it -> {
            Object sn = it.getArgument(0);
            if (sn != null && sn instanceof String) {
                return Optional.ofNullable(map.get(sn)).orElseGet(DeviceKissFlagDO::new).initEmptyValueField((String) sn);
            } else {
                return new DeviceKissFlagDO().initEmptyValueField(null);
            }
        });
    }

    @Test
    public void test_isWsReplaceMqtt() {
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceKissFlagService.queryBySn(sn)).thenReturn(new DeviceKissFlagDO().setDeviceSupportKissReplaceMqtt(true));
            Assert.assertEquals(true, deviceMsgSrcManager.isWsReplaceMqtt(sn));
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceKissFlagService.queryBySn(sn)).thenReturn(new DeviceKissFlagDO().setDeviceSupportKissReplaceMqtt(false));
            Assert.assertEquals(false, deviceMsgSrcManager.isWsReplaceMqtt(sn));
        }
    }

    //
    @Test
    public void test_getDeviceMsgSrc() {
        {
            String sn = OpenApiUtil.shortUUID();
            deviceMsgSrcManager.putDeviceMsgSrc(sn, null);
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceKissFlagService.queryBySn(sn)).thenReturn(new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(true).setAppSupportUnlimitedWebsocket(false));
            deviceMsgSrcManager.putDeviceMsgSrc(sn, DeviceMsgSrc.KISS_WS);
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceKissFlagService.queryBySn(sn)).thenReturn(new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(false).setAppSupportUnlimitedWebsocket(false));
            deviceMsgSrcManager.putDeviceMsgSrc(sn, DeviceMsgSrc.KISS_WS);
        }
        {
            String sn = OpenApiUtil.shortUUID();
            when(deviceKissFlagService.queryBySn(sn)).thenReturn(new DeviceKissFlagDO().setSn(sn)
                    .setDeviceSupportKissReplaceMqtt(true).setAppSupportUnlimitedWebsocket(false));
            deviceMsgSrcManager.putDeviceMsgSrc(sn, DeviceMsgSrc.IOT_MQTT);
        }
    }

    @Test
    public void test_putDeviceKissFlag() {
        {
            deviceMsgSrcManager.putDeviceKissFlag(null);
        }
        {
            DeviceKissFlagDO newFlag = new DeviceKissFlagDO();
            deviceMsgSrcManager.putDeviceKissFlag(newFlag);
        }
        {
            DeviceKissFlagDO newFlag = new DeviceKissFlagDO();
            newFlag.setSn(OpenApiUtil.shortUUID());
            deviceMsgSrcManager.putDeviceKissFlag(newFlag);
            Assert.assertNotNull(deviceMsgSrcManager.queryKissFlag(newFlag.getSn()));
        }
        {
            String sn = OpenApiUtil.shortUUID();
            DeviceKissFlagDO oldFlag = new DeviceKissFlagDO();
            oldFlag.setSn(sn);
            oldFlag.setAppSupportUnlimitedWebsocket(true);
            oldFlag.setDeviceSupportKissReplaceMqtt(true);
            oldFlag.setDeviceIsWsKeepAlive(true);
            deviceMsgSrcManager.putDeviceKissFlag(oldFlag);
            Assert.assertEquals(oldFlag, deviceMsgSrcManager.queryKissFlag(sn));

            DeviceKissFlagDO newFlag = new DeviceKissFlagDO();
            newFlag.setSn(sn);
            newFlag.setAppSupportUnlimitedWebsocket(true);
            newFlag.setDeviceSupportKissReplaceMqtt(true);
            newFlag.setDeviceIsWsKeepAlive(false);
            deviceMsgSrcManager.putDeviceKissFlag(newFlag);
            Assert.assertNotEquals(oldFlag, deviceMsgSrcManager.queryKissFlag(sn));
            Assert.assertEquals(newFlag, deviceMsgSrcManager.queryKissFlag(sn));
        }
        {
            DeviceKissFlagDO newFlag = new DeviceKissFlagDO();
            newFlag.setSn(OpenApiUtil.shortUUID());
            newFlag.setDeviceIsWsKeepAlive(false);
            when(deviceKissFlagService.save(eq(newFlag))).thenThrow(new RuntimeException());

            deviceMsgSrcManager.putDeviceKissFlag(newFlag);
            DeviceKissFlagDO result = deviceMsgSrcManager.queryKissFlag(newFlag.getSn());
            Assert.assertNotEquals(newFlag, result);
        }

    }

}
