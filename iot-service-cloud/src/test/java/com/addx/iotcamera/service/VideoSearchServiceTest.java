package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.ActivityZoneDO;
import com.addx.iotcamera.bean.db.DeviceLibraryViewDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.video.VideoSearchOption;
import com.addx.iotcamera.bean.video.VideoTagSummary;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.service.VideoSearchService.REDIS_KEY_VIDEO_SEARCH_OPTION;
import static com.addx.iotcamera.service.VideoSearchService.REDIS_SET_VIDEO_SEARCH_TAGS;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoSearchServiceTest {

    @Mock
    private UserRoleService userRoleService;
    @Mock
    private LibraryStatusService libraryStatusService;
    @Mock
    private RedisService redisService;
    @Mock
    private DeviceService deviceService;
    @Mock
    private DeviceModelService deviceModelService;
    @Mock
    private ActivityZoneService activityZoneService;
    @Mock
    private LibraryService libraryService;

    @InjectMocks
    private VideoSearchService videoSearchService;

    @Before
    public void init() {
        when(deviceService.getAllDeviceInfo(anyString())).thenAnswer(it ->
                DeviceDO.builder().deviceName("device_name_" + it.getArgument(0)).build());
        when(deviceModelService.queryDeviceModelCategoryBySerialNumber(anyString())).thenReturn(DeviceModelCategoryEnums.CAMERA.getCode());
    }

    @Test
    public void test_queryVideoSearchOptionFromDB() {
        VideoTagSummary summary1 = new VideoTagSummary()
                .setSn("sn1").setTags("person,bird")
                .setDoorbellTags("DOORBELL_REMOVE").setDeviceCallEventTag("DEVICE_CALL");
        VideoTagSummary summary2 = new VideoTagSummary()
                .setSn("sn2").setTags("bird,vehicle_enter,vehicle_held_up")
                .setDoorbellTags("DOORBELL_PRESS").setDeviceCallEventTag(null);
        final ImmutableMap<String, VideoTagSummary> sn2Summary = ImmutableMap.<String, VideoTagSummary>builder()
                .put(summary1.getSn(), summary1).put(summary2.getSn(), summary2).build();
        when(libraryStatusService.querySn2VideoTagSummaryByUserId(any())).thenReturn(sn2Summary);
        when(userRoleService.getUserRoleByUserId(any())).thenReturn(Arrays.asList(
                UserRoleDO.builder().serialNumber("sn1").roleId("1").build(),
                UserRoleDO.builder().serialNumber("sn3").roleId("0").build()
        ));
        final List<VideoSearchOption> searchOptions = videoSearchService.queryVideoSearchOptionFromDB(234823094);
        log.info("test_queryVideoSearchOptionFromDB:{}", JSON.toJSONString(searchOptions, true));
        Assert.assertEquals(ImmutableSet.of("sn1", "sn2", "sn3"), searchOptions.stream().map(it->it.getSn().get()).collect(Collectors.toSet()));

        final VideoSearchOption option1 = searchOptions.stream().filter(it -> "sn1".equals(it.getSn().get())).findFirst().get();
        Assert.assertEquals(Arrays.asList("bird", "person"), option1.getAiEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
        Assert.assertEquals(Arrays.asList(), option1.getAiEventTags().stream().flatMap(it -> it.getSubTags().stream().map(it2 -> it2.getName())).collect(Collectors.toList()));
        Assert.assertEquals(Arrays.asList("DEVICE_CALL", "DOORBELL_REMOVE"), option1.getDeviceEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));

        final VideoSearchOption option2 = searchOptions.stream().filter(it -> "sn2".equals(it.getSn().get())).findFirst().get();
        Assert.assertEquals(Arrays.asList("bird", "vehicle"), option2.getAiEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
        Assert.assertEquals(Arrays.asList("vehicle_enter", "vehicle_held_up"), option2.getAiEventTags().stream().flatMap(it -> it.getSubTags().stream().map(it2 -> it2.getName())).collect(Collectors.toList()));
        Assert.assertEquals(Arrays.asList("DOORBELL_PRESS"), option2.getDeviceEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
    }

    private void mockRedis(Map<String, Object> mockRedis) {
        when(redisService.get(anyString())).thenAnswer(it -> mockRedis.get(it.getArgument(0)));
        doAnswer(it -> mockRedis.put(it.getArgument(0), it.getArgument(1)))
                .when(redisService).set(anyString(), anyString(), any());
        when(redisService.setAdd(anyString(), anyCollection())).thenAnswer(it -> {
            Set<String> set = (Set<String>) mockRedis.computeIfAbsent(it.getArgument(0), k -> new LinkedHashSet<>());
            for (String value : (Collection<String>) it.getArgument(1)) set.add(value);
            return (long) set.size();
        });
        when(redisService.setMembers(anyString())).thenAnswer(it -> {
            Set<String> set = (Set<String>) mockRedis.computeIfAbsent(it.getArgument(0), k -> new LinkedHashSet<>());
            return new HashSet<>(set);
        });
        when(redisService.delete(anyCollection())).thenAnswer(it -> {
            long count = 1L;
            for (String key : ((Collection<String>) it.getArgument(0))) {
                count += (mockRedis.remove(key) != null ? 1L : 0L);
            }
            return count;
        });
    }

    @Test
    public void test_queryVideoSearchOptionFromCache() {
        final Integer userId = 234823094;
        final Map<String, Object> mockRedisMap = new LinkedHashMap<>();
        mockRedis(mockRedisMap);

        VideoTagSummary summary1 = new VideoTagSummary()
                .setSn("sn1").setTags("vehicle_enter")
                .setDoorbellTags("DOORBELL_REMOVE").setDeviceCallEventTag("");
        VideoTagSummary summary2 = new VideoTagSummary()
                .setSn("sn2").setTags("bird,vehicle_enter,vehicle_held_up")
                .setDoorbellTags(null).setDeviceCallEventTag(null);
        final ImmutableMap<String, VideoTagSummary> sn2Summary = ImmutableMap.<String, VideoTagSummary>builder()
                .put(summary1.getSn(), summary1).put(summary2.getSn(), summary2).build();
        when(libraryStatusService.querySn2VideoTagSummaryByUserId(any())).thenReturn(sn2Summary);
        when(userRoleService.getUserRoleByUserId(any())).thenReturn(Arrays.asList(
                UserRoleDO.builder().serialNumber("sn1").roleId("1").build(),
                UserRoleDO.builder().serialNumber("sn3").roleId("0").build()
        ));
        VideoSearchOption videoSearchOption = null;
        {
            // 首次查询，没有缓存
            final Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId);
            log.info("test_queryVideoSearchOptionFromCache:{}", JSON.toJSONString(result, true));
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(Arrays.asList("sn1", "sn3", "sn2"), result.getData().getDevices().stream().map(it -> it.getSerialNumber()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList("bird", "vehicle"), result.getData().getAiEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList("vehicle_enter", "vehicle_held_up"), result.getData().getAiEventTags().stream().flatMap(it -> it.getSubTags().stream().map(it2 -> it2.getName())).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList("DOORBELL_REMOVE"), result.getData().getDeviceEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
            videoSearchOption = result.getData();
        }
        {
            // 第2次查询，有主缓存，没有tags缓存
            Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(videoSearchOption, result.getData());
        }
        {
            // 追加tags缓存
            videoSearchService.appendTagsCache("sn1", Arrays.asList(userId), "package_exist", "DOORBELL_PRESS");
            Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(Arrays.asList("sn1", "sn3", "sn2"), result.getData().getDevices().stream().map(it -> it.getSerialNumber()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList("bird", "package", "vehicle"), result.getData().getAiEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList("package_exist", "vehicle_enter", "vehicle_held_up"), result.getData().getAiEventTags().stream().flatMap(it -> it.getSubTags().stream().map(it2 -> it2.getName())).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList("DOORBELL_PRESS", "DOORBELL_REMOVE"), result.getData().getDeviceEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
        }
//        {
//            // 追加tags报错
//            when(redisService.setAdd(anyString(), anyCollection())).thenThrow(new RuntimeException());
//            videoSearchService.appendTagsCache(Arrays.asList(userId), "package_exist", "DOORBELL_PRESS");
//            Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId);
//            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals(Arrays.asList("sn1", "sn3", "sn2"), result.getData().getDevices().stream().map(it -> it.getSerialNumber()).collect(Collectors.toList()));
//            Assert.assertEquals(Arrays.asList("bird", "package", "vehicle"), result.getData().getAiEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
//            Assert.assertEquals(Arrays.asList("package_exist", "vehicle_enter", "vehicle_held_up"), result.getData().getAiEventTags().stream().flatMap(it -> it.getSubTags().stream().map(it2 -> it2.getName())).collect(Collectors.toList()));
//            Assert.assertEquals(Arrays.asList("DOORBELL_PRESS", "DOORBELL_REMOVE"), result.getData().getDeviceEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
//            mockRedis(mockRedisMap); // 重新mock
//        }
        {
            // 追加tags缓存
            videoSearchService.appendTagsCache("sn1", Arrays.asList(userId), "DEVICE_CALL");
            Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(Arrays.asList("sn1", "sn3", "sn2"), result.getData().getDevices().stream().map(it -> it.getSerialNumber()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList("bird", "package", "vehicle"), result.getData().getAiEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList("package_exist", "vehicle_enter", "vehicle_held_up"), result.getData().getAiEventTags().stream().flatMap(it -> it.getSubTags().stream().map(it2 -> it2.getName())).collect(Collectors.toList()));
            Assert.assertEquals(Arrays.asList("DEVICE_CALL","DOORBELL_PRESS", "DOORBELL_REMOVE"), result.getData().getDeviceEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
        }
//        {
//            // 清空主缓存报错
//            when(redisService.delete(anyCollection())).thenThrow(new RuntimeException());
//            videoSearchService.appendTagsCache(Arrays.asList(userId), "DEVICE_CALL");
//            Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId);
//            Assert.assertEquals(Result.successFlag, result.getResult());
//            Assert.assertEquals(Arrays.asList("sn1", "sn3", "sn2"), result.getData().getDevices().stream().map(it -> it.getSerialNumber()).collect(Collectors.toList()));
//            Assert.assertEquals(Arrays.asList("bird", "package", "vehicle"), result.getData().getAiEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
//            Assert.assertEquals(Arrays.asList("package_exist", "vehicle_enter", "vehicle_held_up"), result.getData().getAiEventTags().stream().flatMap(it -> it.getSubTags().stream().map(it2 -> it2.getName())).collect(Collectors.toList()));
//            Assert.assertEquals(Arrays.asList("DOORBELL_PRESS", "DOORBELL_REMOVE", "DEVICE_CALL"), result.getData().getDeviceEventTags().stream().map(it -> it.getName()).collect(Collectors.toList()));
//            mockRedis(mockRedisMap); // 重新mock
//        }
        {
            // 清空主缓存
            videoSearchService.clearSearchOptionCache(Arrays.asList(userId));
            Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(videoSearchOption, result.getData());
        }
    }

    @Test
    public void test_UserAllSnsAndTags_merge() {
        VideoTagSummary summary = new VideoTagSummary();
        VideoTagSummary summary2 = new VideoTagSummary().setSn("sn1")
                .setTags("tag1").setDoorbellTags("tag2").setDeviceCallEventTag("tag3");
        summary.merge(null);
        Assert.assertEquals(new VideoTagSummary(), summary);
        summary.merge(summary2).setSn("sn1");
        Assert.assertEquals(summary2, summary);
    }

    @Test
    public void test_clearSearchOptionCache_byUserIds() {
        List<Integer> userIds = Arrays.asList(123, 456);
        when(redisService.delete(anyCollection())).thenThrow(new RuntimeException());
        videoSearchService.clearSearchOptionCache(userIds);
    }

    @Test
    public void test_clearSearchOptionCache_bySn() {
        List<Integer> userIds = Arrays.asList(123, 456);
        when(userRoleService.findAllUsersForDevice("sn1")).thenReturn(userIds);
        doThrow(new RuntimeException()).when(redisService)
                .delete(Arrays.asList(REDIS_KEY_VIDEO_SEARCH_OPTION + "123", REDIS_KEY_VIDEO_SEARCH_OPTION + "456"));
        videoSearchService.clearSearchOptionCache("sn1");
    }

    @Test
    public void test_appendTagsCache() {
        List<Integer> userIds = Arrays.asList(123, 456);
        when(userRoleService.findAllUsersForDevice("sn1")).thenReturn(userIds);
        when(redisService.setAdd(anyString(), anyCollection())).thenThrow(new RuntimeException());
        videoSearchService.appendTagsCache("sn1", userIds, "tag1");
    }

    @Test
    public void test_queryLastDeviceNameByUserIdAndSn() {
        final int userId = new Random().nextInt(1000_0000);
        final String sn = OpenApiUtil.shortUUID();
        final String deviceName = OpenApiUtil.shortUUID();
        final String deviceName2 = OpenApiUtil.shortUUID();
        final String userSn = OpenApiUtil.shortUUID();
        final String traceId = OpenApiUtil.shortUUID();

        final DeviceDO deviceDO = new DeviceDO();
        deviceDO.setDeviceName(deviceName);
        deviceDO.setUserSn(userSn);
        final DeviceLibraryViewDO libraryViewDO = new DeviceLibraryViewDO();
        libraryViewDO.setDeviceName(deviceName2);

        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(null);
            Assert.assertEquals("", videoSearchService.queryLastDeviceNameByUserIdAndSn(userId, sn, true));
        }
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(deviceDO);
            Assert.assertEquals(deviceName, videoSearchService.queryLastDeviceNameByUserIdAndSn(userId, sn, true));
        }
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(null);
            when(libraryStatusService.queryLastTraceIdByUserIdAndSn(userId, sn)).thenReturn(null);
            Assert.assertEquals("", videoSearchService.queryLastDeviceNameByUserIdAndSn(userId, sn, false));
        }
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(deviceDO);
            when(libraryStatusService.queryLastTraceIdByUserIdAndSn(userId, sn)).thenReturn(null);
            Assert.assertEquals(userSn, videoSearchService.queryLastDeviceNameByUserIdAndSn(userId, sn, false));
        }
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(null);
            when(libraryStatusService.queryLastTraceIdByUserIdAndSn(userId, sn)).thenReturn(traceId);
            when(libraryService.selectLibraryViewByTraceId(userId, traceId)).thenReturn(null);
            Assert.assertEquals("", videoSearchService.queryLastDeviceNameByUserIdAndSn(userId, sn, false));
        }
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(deviceDO);
            when(libraryStatusService.queryLastTraceIdByUserIdAndSn(userId, sn)).thenReturn(traceId);
            when(libraryService.selectLibraryViewByTraceId(userId, traceId)).thenReturn(null);
            Assert.assertEquals(userSn, videoSearchService.queryLastDeviceNameByUserIdAndSn(userId, sn, false));
        }
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(null);
            when(libraryStatusService.queryLastTraceIdByUserIdAndSn(userId, sn)).thenReturn(traceId);
            when(libraryService.selectLibraryViewByTraceId(userId, traceId)).thenReturn(libraryViewDO);
            Assert.assertEquals(deviceName2, videoSearchService.queryLastDeviceNameByUserIdAndSn(userId, sn, false));
        }
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(deviceDO);
            when(libraryStatusService.queryLastTraceIdByUserIdAndSn(userId, sn)).thenReturn(traceId);
            when(libraryService.selectLibraryViewByTraceId(userId, traceId)).thenReturn(libraryViewDO);
            Assert.assertEquals(deviceName2, videoSearchService.queryLastDeviceNameByUserIdAndSn(userId, sn, false));
        }
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(null);
            when(libraryStatusService.queryLastTraceIdByUserIdAndSn(userId, sn)).thenReturn(traceId);
            when(libraryService.selectLibraryViewByTraceId(userId, traceId)).thenReturn(new DeviceLibraryViewDO());
            Assert.assertEquals("", videoSearchService.queryLastDeviceNameByUserIdAndSn(userId, sn, false));
        }
        {
            when(deviceService.getAllDeviceInfo(sn)).thenReturn(deviceDO);
            when(libraryStatusService.queryLastTraceIdByUserIdAndSn(userId, sn)).thenReturn(traceId);
            when(libraryService.selectLibraryViewByTraceId(userId, traceId)).thenReturn(new DeviceLibraryViewDO());
            Assert.assertEquals(userSn, videoSearchService.queryLastDeviceNameByUserIdAndSn(userId, sn, false));
        }

    }

    @Test
    public void test_queryVideoSearchOption_by_userId_sn() {
        final String sn2 = OpenApiUtil.shortUUID();
        final String sn3 = OpenApiUtil.shortUUID();
        final String sn = OpenApiUtil.shortUUID();
        final int userId = (new Random()).nextInt(9999999);
        final String dataCacheKey = REDIS_KEY_VIDEO_SEARCH_OPTION + userId;
        final String tagsCacheKey = REDIS_SET_VIDEO_SEARCH_TAGS + userId;
        {
            when(redisService.get(dataCacheKey)).thenReturn("[]");
            final Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId, sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
            final VideoSearchOption searchOption = new VideoSearchOption() {{
                getDevices().add(DeviceOption.createUnbind(sn));
            }};
            Assert.assertEquals(searchOption, result.getData());
        }
        final VideoSearchOption searchOption = new VideoSearchOption() {{
            getDevices().add(new VideoSearchOption.DeviceOption().setSerialNumber(sn).setDeviceName("name_" + sn)
                    .setModelCategory(DeviceModelCategoryEnums.CAMERA.getCode())
                    .setRoleId(1).setIsBind(true));
            getAiEventTags().add(new TagOption().setName("person"));
            getAiEventTags().add(new TagOption() {{
                setName("vehicle");
                getSubTags().add(new TagOption().setName("vehicle_enter"));
            }});
        }};
        when(redisService.get(dataCacheKey)).thenReturn(new JSONArray().fluentAdd(searchOption).toJSONString());
        {
            final Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId, sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(searchOption, result.getData());
        }
        final Set<String> hashSet = new HashSet<String>() {{
            add(sn + ":" + "pet");
            add(sn + ":" + "vehicle_held_up");
            add(sn + ":" + "person");
            add(sn2 + ":" + "bird");
            add(sn3 + ":" + "person");
            add(sn3 + ":" + "vehicle_out");
        }};
        when(redisService.setMembers(tagsCacheKey)).thenReturn(hashSet);
        {
            final VideoSearchOption expectSearchOption = ((JSONObject) JSON.toJSON(searchOption)).toJavaObject(VideoSearchOption.class);
            {
                expectSearchOption.getAiEventTags().stream().filter(it -> it.getName().equals("vehicle"))
                        .findFirst().ifPresent(it -> it.getSubTags().add(new VideoSearchOption.TagOption().setName("vehicle_held_up")));
                expectSearchOption.getAiEventTags().add(new VideoSearchOption.TagOption().setName("pet"));
                expectSearchOption.getAiEventTags().sort(videoSearchService.createVideoTagComparator(VideoSearchOption.TagOption::getName));
            }
            final Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId, sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(expectSearchOption, result.getData());
        }
        when(redisService.get(dataCacheKey)).thenReturn("");
        {
            final VideoSearchOption expectSearchOption = VideoSearchOption.createUnbind(sn);
            final Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId, sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(expectSearchOption, result.getData());
        }
        final VideoTagSummary summary = new VideoTagSummary() {{
            setSn(sn);
            setTags("bird,vehicle,vehicle_out");
            setDoorbellTags("DOORBELL_PRESS");
        }};
        final VideoTagSummary summary3 = new VideoTagSummary() {{
            setSn(sn3);
            setTags("person,vehicle_enter");
            setDeviceCallEventTag("DEVICE_CALL");
        }};
        when(libraryStatusService.querySn2VideoTagSummaryByUserId(userId)).thenReturn(ImmutableMap.of(sn, summary, sn3, summary3));
        when(userRoleService.getUserRoleByUserId(userId)).thenReturn(Arrays.asList(
                new UserRoleDO() {{
                    setSerialNumber(sn);
                    setRoleId("1");
                    setUserId(userId);
                }},
                new UserRoleDO() {{
                    setSerialNumber(sn2);
                    setRoleId("0");
                    setUserId(userId);
                }}
        ));
        when(deviceModelService.queryDeviceModelCategoryBySerialNumber(sn)).thenReturn(DeviceModelCategoryEnums.DOORBELL.getCode());
        when(activityZoneService.getActivityZones(sn)).thenReturn(Arrays.asList());
        {
            final VideoSearchOption expectSearchOption = new VideoSearchOption();
            {
                expectSearchOption.getDevices().add(new VideoSearchOption.DeviceOption().setSerialNumber(sn).setDeviceName("device_name_" + sn)
                        .setModelCategory(DeviceModelCategoryEnums.DOORBELL.getCode()).setRoleId(1).setIsBind(true));
                expectSearchOption.getAiEventTags().add(new VideoSearchOption.TagOption() {{
                    setName("vehicle");
                    getSubTags().add(new VideoSearchOption.TagOption().setName("vehicle_out"));
                }});
                expectSearchOption.getAiEventTags().add(new VideoSearchOption.TagOption().setName("bird"));
                expectSearchOption.getDeviceEventTags().add(new VideoSearchOption.TagOption().setName("DOORBELL_PRESS"));
                expectSearchOption.getAiEventTags().sort(videoSearchService.createVideoTagComparator(VideoSearchOption.TagOption::getName));
                expectSearchOption.getOperateOptions().add(new VideoSearchOption.OperateOption().setName("missing"));
                expectSearchOption.getOperateOptions().add(new VideoSearchOption.OperateOption().setName("marked"));
            }
            final Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId, sn);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(expectSearchOption, result.getData());
        }
        when(deviceModelService.queryDeviceModelCategoryBySerialNumber(sn2)).thenReturn(DeviceModelCategoryEnums.CAMERA.getCode());
        when(activityZoneService.getActivityZones(sn2)).thenReturn(Arrays.asList(new ActivityZoneDO() {{
            setId(9988);
            setVertices("9,9,8,8");
        }}));
        {
            final VideoSearchOption expectSearchOption = new VideoSearchOption();
            {
                expectSearchOption.getDevices().add(new VideoSearchOption.DeviceOption().setSerialNumber(sn2).setDeviceName("device_name_" + sn2)
                        .setModelCategory(DeviceModelCategoryEnums.CAMERA.getCode()).setRoleId(0).setIsBind(true)
                        .setActivityZoneList(Arrays.asList(new ActivityZoneDO() {{
                            setId(9988);
                            setVertices("9,9,8,8");
                        }})));
                expectSearchOption.getOperateOptions().add(new VideoSearchOption.OperateOption().setName("missing"));
                expectSearchOption.getOperateOptions().add(new VideoSearchOption.OperateOption().setName("marked"));
            }
            final Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId, sn2);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(expectSearchOption, result.getData());
        }
        when(deviceModelService.queryDeviceModelCategoryBySerialNumber(sn3)).thenReturn(DeviceModelCategoryEnums.DOORBELL.getCode());
        when(activityZoneService.getActivityZones(sn3)).thenReturn(Arrays.asList());
        {
            final VideoSearchOption expectSearchOption = new VideoSearchOption();
            {
                expectSearchOption.getDevices().add(VideoSearchOption.DeviceOption.createUnbind(sn3)
                        .setModelCategory(DeviceModelCategoryEnums.DOORBELL.getCode()));
                expectSearchOption.getAiEventTags().add(new VideoSearchOption.TagOption() {{
                    setName("vehicle");
                    getSubTags().add(new VideoSearchOption.TagOption().setName("vehicle_enter"));
                }});
                expectSearchOption.getAiEventTags().add(new VideoSearchOption.TagOption().setName("person"));
                expectSearchOption.getAiEventTags().sort(videoSearchService.createVideoTagComparator(VideoSearchOption.TagOption::getName));
                expectSearchOption.getDeviceEventTags().add(new VideoSearchOption.TagOption().setName("DEVICE_CALL"));
                expectSearchOption.getOperateOptions().add(new VideoSearchOption.OperateOption().setName("missing"));
                expectSearchOption.getOperateOptions().add(new VideoSearchOption.OperateOption().setName("marked"));
            }
            final Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId, sn3);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(expectSearchOption, result.getData());
        }
        {
            final VideoSearchOption expectSearchOption = new VideoSearchOption();
            {
                expectSearchOption.getDevices().add(new VideoSearchOption.DeviceOption().setSerialNumber(sn).setDeviceName("device_name_" + sn)
                        .setModelCategory(DeviceModelCategoryEnums.DOORBELL.getCode()).setRoleId(1).setIsBind(true));
                expectSearchOption.getDevices().add(new VideoSearchOption.DeviceOption().setSerialNumber(sn2).setDeviceName("device_name_" + sn2)
                        .setModelCategory(DeviceModelCategoryEnums.CAMERA.getCode()).setRoleId(0).setIsBind(true)
                        .setActivityZoneList(Arrays.asList(new ActivityZoneDO() {{
                            setId(9988);
                            setVertices("9,9,8,8");
                        }})));
                expectSearchOption.getDevices().add(VideoSearchOption.DeviceOption.createUnbind(sn3)
                        .setModelCategory(DeviceModelCategoryEnums.DOORBELL.getCode()));

                expectSearchOption.getAiEventTags().add(new VideoSearchOption.TagOption() {{
                    setName("vehicle");
                    getSubTags().add(new VideoSearchOption.TagOption().setName("vehicle_enter"));
                    getSubTags().add(new VideoSearchOption.TagOption().setName("vehicle_out"));
                }});
                expectSearchOption.getAiEventTags().add(new VideoSearchOption.TagOption().setName("person"));
                expectSearchOption.getAiEventTags().add(new VideoSearchOption.TagOption().setName("bird"));
                expectSearchOption.getAiEventTags().sort(videoSearchService.createVideoTagComparator(VideoSearchOption.TagOption::getName));
                expectSearchOption.getDeviceEventTags().add(new VideoSearchOption.TagOption().setName("DOORBELL_PRESS"));
                expectSearchOption.getDeviceEventTags().add(new VideoSearchOption.TagOption().setName("DEVICE_CALL"));
                expectSearchOption.getDeviceEventTags().sort(videoSearchService.createVideoTagComparator(VideoSearchOption.TagOption::getName));

                expectSearchOption.getOperateOptions().add(new VideoSearchOption.OperateOption().setName("missing"));
                expectSearchOption.getOperateOptions().add(new VideoSearchOption.OperateOption().setName("marked"));
            }
            videoSearchService.queryVideoSearchOption(userId);
            final Result<VideoSearchOption> result = videoSearchService.queryVideoSearchOption(userId);
            Assert.assertEquals(Result.successFlag, result.getResult());
            Assert.assertEquals(expectSearchOption, result.getData());
        }

    }

}
