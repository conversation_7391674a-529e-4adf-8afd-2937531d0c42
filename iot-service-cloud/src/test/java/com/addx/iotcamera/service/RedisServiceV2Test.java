package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.DeviceOperationDO;
import com.addx.iotcamera.bean.domain.ShareCacheDO;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.*;

import java.util.*;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RedisServiceV2Test {

    @InjectMocks
    private RedisService redisService;
    @Mock
    private StringRedisTemplate businessRedisTemplateClusterClient;
    @Mock
    private StringRedisTemplate readOnlyRedisTemplate;
    @Mock
    private ValueOperations<String, String> valueOperations;
    @Mock
    private ListOperations<String, String> listOperations;
    @Mock
    private SetOperations<String, String> setOperations;
    @Mock
    private HashOperations<String, Object, Object> hashOperations;
    @Mock
    private ZSetOperations<String, String> zSetOperations;
    @Mock
    private GeoOperations<String, String> geoOperations;

    private Map<String, String> mockRedis = new LinkedHashMap<>();

    @Before
    public void init() {
        when(businessRedisTemplateClusterClient.opsForValue()).thenReturn(valueOperations);
        when(readOnlyRedisTemplate.opsForValue()).thenReturn(valueOperations);
        when(businessRedisTemplateClusterClient.opsForList()).thenReturn(listOperations);
        when(businessRedisTemplateClusterClient.opsForSet()).thenReturn(setOperations);
        when(businessRedisTemplateClusterClient.opsForHash()).thenReturn(hashOperations);
        when(readOnlyRedisTemplate.opsForHash()).thenReturn(hashOperations);
        when(businessRedisTemplateClusterClient.opsForZSet()).thenReturn(zSetOperations);
        when(businessRedisTemplateClusterClient.opsForGeo()).thenReturn(geoOperations);
        {
            when(businessRedisTemplateClusterClient.hasKey(anyString())).thenAnswer(it -> mockRedis.containsKey(it.getArgument(0)));
            when(readOnlyRedisTemplate.hasKey(anyString())).thenAnswer(it -> mockRedis.containsKey(it.getArgument(0)));
            when(valueOperations.get(anyString())).thenAnswer(it -> mockRedis.get(it.getArgument(0)));
            doAnswer(it -> mockRedis.put(it.getArgument(0), it.getArgument(1)))
                    .when(valueOperations).set(anyString(), anyString(), anyLong(), any());
            when(businessRedisTemplateClusterClient.delete(anyString())).thenAnswer(it -> {
                mockRedis.remove(it.getArgument(0));
                return true;
            });
            when(valueOperations.increment(anyString(), anyLong())).thenAnswer(it -> {
                final Long oldVal = Optional.ofNullable(mockRedis.get(it.getArgument(0))).map(Long::valueOf).orElse(0L);
                final Long newVal = oldVal + (long) it.getArgument(1);
                mockRedis.put(it.getArgument(0), newVal + "");
                return newVal;
            });
        }
    }

    @Test
    public void test_deviceOperationDo() {
        String key = OpenApiUtil.shortUUID();
        DeviceOperationDO operation = DeviceOperationDO.builder().id(OpenApiUtil.shortUUID()).build();
        mockRedis.clear();
        {
            Assert.assertFalse(redisService.hasDeviceOperationDo(key));
            Assert.assertNull(redisService.getDeviceOperationDo(key));
        }
        {
            AssertUtil.assertNotException(() -> redisService.setDeviceOperationDo(key, operation));
            Assert.assertTrue(redisService.hasDeviceOperationDo(key));
            DeviceOperationDO operation2 = redisService.getDeviceOperationDo(key);
            Assert.assertNotNull(operation2);
            Assert.assertEquals(operation.getId(), operation2.getId());
        }
        {
            AssertUtil.assertNotException(() -> redisService.dropDeviceOperationDo(key));
            Assert.assertFalse(redisService.hasDeviceOperationDo(key));
            Assert.assertNull(redisService.getDeviceOperationDo(key));
        }
        {
            AssertUtil.assertNotException(() -> redisService.setDeviceOperationDOWithEmpty(key));
            Assert.assertNull(redisService.getDeviceOperationDo(key));
        }
    }

    @Test
    public void test_shareCacheDo() {
        String key = OpenApiUtil.shortUUID();
        ShareCacheDO shareCache = new ShareCacheDO();
        shareCache.setShareId(key);
        mockRedis.clear();
        Assert.assertNull(redisService.getShareCacheDO(key));
        AssertUtil.assertNotException(() -> redisService.setShareCacheDO(key, shareCache, 123));
        ShareCacheDO shareCache2 = redisService.getShareCacheDO(key);
        Assert.assertNotNull(shareCache2);
        Assert.assertEquals(shareCache.getShareId(), shareCache2.getShareId());
    }

    @Test
    public void test_listRightPushAll() {
        String key = "key12345";
        List<String> list = Arrays.asList("1", "2");

        List<String> redisListMock = new LinkedList<>();
        when(listOperations.rightPushAll(anyString(), anyCollection())).thenAnswer(it -> {
            redisListMock.addAll(it.getArgument(1));
            return new Long(redisListMock.size());
        });
        when(businessRedisTemplateClusterClient.expire(anyString(), anyLong(), any())).thenReturn(true);
        Assert.assertEquals(0, redisService.listRightPushAll(key, null, 100));
        Assert.assertEquals(0, redisService.listRightPushAll(key, Arrays.asList(), 100));
        Assert.assertEquals(list.size(), redisService.listRightPushAll(key, list, 100));
        Assert.assertEquals(list.size() * 2, redisService.listRightPushAll(key, list, 100));
    }

//    @Test
//    public void test_getAndDelete() {
//        when(redisTemplate.exec()).thenReturn(null);
//        Assert.assertNull(redisService.getAndDelete("key1"));
//        when(redisTemplate.exec()).thenReturn(Arrays.asList(null, 0));
//        Assert.assertNull(redisService.getAndDelete("key1"));
//        when(redisTemplate.exec()).thenReturn(Arrays.asList("value1", 0));
//        Assert.assertEquals("value1",redisService.getAndDelete("key1"));
//    }


    @Test
    public void test_setMembers() {
        when(setOperations.members(any())).thenReturn(new HashSet<>(ImmutableSet.of("a", "b")));
        Set<String> set = redisService.setMembers("key1");
        Assert.assertEquals(ImmutableSet.of("a", "b"), set);
    }

    @Test
    public void test_setAdd() {
        when(setOperations.add(anyString(), any())).thenAnswer(it -> (long) it.getArguments().length - 1);
        long result = redisService.setAdd("key1", Arrays.asList("v1", "v2"));
        Assert.assertEquals(2, result);
    }

    @Test
    public void test_delete_multi() {
        AssertUtil.assertNotException(() -> redisService.delete(Arrays.asList("k1", "k2")));
    }


    @Test
    public void readFromSlave() {
        String key = "test";
        AssertUtil.assertNotException(() -> redisService.set(key, "value"));
        Assert.assertNull(redisService.getFromSlave(key));
    }

    @Test
    public void test_getHashFieldValue(){
        String key = "key";
        Object actualResult = redisService.getHashFieldValue("key","field");
        Assert.assertNull(actualResult);
    }

    private ValueOperations<String, String> opsForValue;

    @Test
    public void test_generateLibraryId() {
        Assert.assertEquals(0, redisService.generateLibraryId(123));
        Assert.assertEquals(0, redisService.generateLibraryId(123));
        Assert.assertEquals(0, redisService.generateLibraryId(456));
        {
            when(valueOperations.increment(VideoStoreService.REDIS_KEY_VIDEO_GENERATE_LIBRARY_ID + "538465", -1L))
                    .thenThrow(new RuntimeException("mock"));
            Assert.assertEquals(0, redisService.generateLibraryId(538465));
        }
    }

    @Mock
    private Cursor cursor;

    @Test
    public void test_setScan() {
        final String key = OpenApiUtil.shortUUID();

        when(setOperations.scan(eq(key), any())).thenReturn(cursor);
        final List<String> list = Arrays.asList("123", "456", "789");
        final Iterator<String> iterator = list.iterator();
        when(cursor.hasNext()).thenAnswer(it -> iterator.hasNext());
        when(cursor.next()).thenAnswer(it -> iterator.next());
        final List<List<String>> results = redisService.setScan(key, null, 2).toList().blockingGet();
        Assert.assertEquals(2, results.size());
        Assert.assertEquals(Arrays.asList("123", "456"), results.get(0));
        Assert.assertEquals(Arrays.asList("789"), results.get(1));
    }

    @Test
    public void test_setRemote() {
        final String key = OpenApiUtil.shortUUID();
        final List<String> list = Arrays.asList("123", "456", "789");
        redisService.setRemove(key, list);
    }

//    @Test
//    public void test_getHashFieldValue(){
//        Map<String,String> map = Maps.newHashMap();
//        map.put("key","field");
//        when(readOnlyRedisTemplate.opsForHash().get(any(),any())).thenReturn(map);
//
//        Object actualResult = redisService.getHashFieldValue("key","field");
//        Assert.assertEquals(map,actualResult);
//    }

    @Test
    public void test_getFromLocalCache() {
        {
            Assert.assertEquals(Optional.empty(), redisService.getFromLocalCache(null));
            Assert.assertEquals(Optional.empty(), redisService.getFromLocalCache(""));
        }
        {
            String key = OpenApiUtil.shortUUID();
            Assert.assertEquals(Optional.empty(), redisService.getFromLocalCache(key));
        }
        {
            String key = OpenApiUtil.shortUUID();
            String value = OpenApiUtil.shortUUID();
            mockRedis.put(key, value);
            Assert.assertEquals(Optional.of(value), redisService.getFromLocalCache(key));
        }
        {
            String key = OpenApiUtil.shortUUID();
            String value = OpenApiUtil.shortUUID();
            mockRedis.put(key, value);
            when(valueOperations.get(key)).thenThrow(new RuntimeException("mock"));
            Assert.assertEquals(Optional.empty(), redisService.getFromLocalCache(key));
        }
    }

}
