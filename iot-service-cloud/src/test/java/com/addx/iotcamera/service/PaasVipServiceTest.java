package com.addx.iotcamera.service;

import com.addx.iotcamera.dao.openapi.DeviceVipDAO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PaasVipServiceTest {

    @InjectMocks
    private VipService paasVipService;
    @Mock
    private DeviceVipDAO deviceVipDAO;
    @Mock
    private LibraryService libraryService;
    @Mock
    private UserTierDeviceService userTierDeviceService;
    @Mock
    private UserService userService;

    @Test
    @DisplayName("设备vip")
    public void isVipDevice_test_null(){
        String serialNumber = "serialNumber";
        Integer userId = 1;

        when(userTierDeviceService.getDeviceCurrentTier(any(),any())).thenReturn(1);
        Boolean expectedResult = true;
        Boolean actualResult = paasVipService.isVipDevice(userId,serialNumber);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("设备vip-用户非vip")
    public void isVipDevice_test_device(){
        String serialNumber = "serialNumber";
        Integer userId = 1;
        when(userTierDeviceService.getDeviceCurrentTier(any(),any())).thenReturn(null);

        Boolean expectedResult = false;
        when(userService.queryUserById(any())).thenReturn(null);
        Boolean actualResult = paasVipService.isVipDevice(userId,serialNumber);
        Assert.assertEquals(expectedResult,actualResult);
    }
}
