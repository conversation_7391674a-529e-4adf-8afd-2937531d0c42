package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.RotationPointRequest;
import com.addx.iotcamera.bean.app.RotationPointSaveRequest;
import com.addx.iotcamera.bean.db.RotationPointDo;
import com.addx.iotcamera.dao.RotationPointDao;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * description: rotation test
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2023/7/18 10:41
 */
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RotationPointServiceTest {
    @InjectMocks
    private RotationPointService rotationPointService;
    @Mock
    private RotationPointDao rotationPointDao;

    @Test
    public void testSaveRotationPoint() {
        List<RotationPointDo> list = new ArrayList<>();
        when(rotationPointDao.queryDeviceRotationPoints(any())).thenReturn(list);
        rotationPointService.saveRotationPoint(new RotationPointSaveRequest());

        for (int i = 0; i < 3; i++) {
            list.add(new RotationPointDo());
        }
        when(rotationPointDao.queryDeviceRotationPoints(any())).thenReturn(list);
        rotationPointService.saveRotationPoint(new RotationPointSaveRequest());

        for (int i = 0; i < 3; i++) {
            list.add(new RotationPointDo());
        }
        Result result = rotationPointService.saveRotationPoint(new RotationPointSaveRequest());
        Result actualResult =  Result.Error(INVALID_PARAMS, "BEYOND_MAX_ROTATION_COUNT");
        assertEquals(actualResult.getResult(), result.getResult());
    }

    @Test
    public void testDeleteRotationPoint() {
        when(rotationPointDao.deleteRotationPoint(anyLong(),anyString())).thenReturn(1);
        RotationPointRequest req = new RotationPointRequest();
        req.setPointId(1L);
        req.setSerialNumber("test");
        Result result = rotationPointService.deleteRotationPoint(req);
        assertEquals(result.getResult(),Result.Success().getResult());
    }
}
