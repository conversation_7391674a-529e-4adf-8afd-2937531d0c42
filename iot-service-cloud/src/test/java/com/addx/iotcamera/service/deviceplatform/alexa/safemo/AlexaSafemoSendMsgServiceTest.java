package com.addx.iotcamera.service.deviceplatform.alexa.safemo;

import com.addx.iotcamera.bean.app.alexa.request.AlexaSafemoMsg;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.service.device_msg.KissWsClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AlexaSafemoSendMsgServiceTest {

    @InjectMocks
    AlexaSafemoSendMsgService alexaSafemoSendMsgService;
    @Mock
    private KissWsService kissSafeRTCWsService;
    @Mock
    KissWsClient kissWsClient;

    @Mock
    private AlexaSafemoRelationService alexaSafemoRelationService;

    @Test
    public void sendMsgToBxByAdminUserId() throws URISyntaxException {
        Integer adminUserId = 1;
        List<String> kissIpList = new ArrayList<>();
        kissIpList.add("127.0.0.1");
        List<KissWsClient> kissWsClientList = new ArrayList<>();
        kissWsClientList.add(kissWsClient);

        // 条件1
        alexaSafemoSendMsgService.sendMsgToBxByAdminUserId(null, null);

        // 条件2
        when(alexaSafemoRelationService.getKissIpListByAdminUserId(adminUserId)).thenReturn(kissIpList);
        when(kissSafeRTCWsService.getKissWsClientByKissIp(kissIpList)).thenReturn(kissWsClientList);
        AlexaSafemoMsg alexaSafemoMsg = new AlexaSafemoMsg();
        alexaSafemoSendMsgService.sendMsgToBxByAdminUserId(1, alexaSafemoMsg);
    }
}