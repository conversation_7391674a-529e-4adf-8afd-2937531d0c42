package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.dao.IProductDAO;
import com.addx.iotcamera.dao.ProductExchangeCodeDAO;
import com.addx.iotcamera.enums.ProductTypeEnums;
import com.addx.iotcamera.testutil.TestHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.FileWriter;
import java.util.*;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ExchangeCodeServiceTest {

    @InjectMocks
    private ProductExchangeCodeService exchangeCodeService;

    private TestHelper testHelper = TestHelper.getInstanceByLocal();
    //            private TestHelper testHelper = TestHelper.getInstanceByEnv("staging");
    //    private TestHelper testHelper = TestHelper.getInstanceByEnv("staging-eu");
//    private TestHelper testHelper = TestHelper.getInstanceByEnv("staging-us");
    //    @Spy
    private ProductExchangeCodeDAO exchangeCodeDAO0 = testHelper.getMapper(ProductExchangeCodeDAO.class);
    @Mock
    private ProductExchangeCodeDAO exchangeCodeDAO;
    @Mock
    private PaymentService paymentService;
    @Mock
    private ProductService productService;
    @Mock
    private UserService userService;
    @Mock
    private UserRoleService userRoleService;

    @Before
    public void init() {
        when(exchangeCodeDAO.create(any())).thenAnswer(AdditionalAnswers.delegatesTo(exchangeCodeDAO0));
        when(exchangeCodeDAO.batchCreate(any())).thenAnswer(AdditionalAnswers.delegatesTo(exchangeCodeDAO0));
        when(exchangeCodeDAO.queryByCode(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(exchangeCodeDAO0));
        when(exchangeCodeDAO.useExchangeCode(anyString(), anyInt(), any(), anyInt(), anyInt(), anyString())).thenAnswer(AdditionalAnswers.delegatesTo(exchangeCodeDAO0));
        IProductDAO productDAO0 = testHelper.getMapper(IProductDAO.class);
        when(productService.queryProductById(anyInt())).thenAnswer(it -> productDAO0.selectById(it.getArgument(0)));
        when(userService.queryUserById(anyInt())).thenReturn(new User());
    }

    @After
    public void after() {
        testHelper.commitAndClose();
    }

    @Test
    public void test_generateRandomExchangeCode() {
        String code = ProductExchangeCodeService.generateRandomExchangeCode();
        Assert.assertEquals(16, code.length());
    }

    //    @Test
    public void test_createExchangeCode_useExchangeCode() {
        Result<List<String>> createResult = exchangeCodeService.createExchangeCode(1055301, 0, 3600, 3
                , RandomStringUtils.randomAlphanumeric(10));
        log.info("createExchangeCode:{}", JSON.toJSONString(createResult, true));
        Assert.assertEquals(Result.successFlag, createResult.getResult());
        Assert.assertEquals(3, createResult.getData().size());

        String code = createResult.getData().get(0);
        Result useResult = exchangeCodeService.useExchangeCode(code, 12345, null);
        Assert.assertEquals(Result.successFlag, useResult.getResult());
        log.info("useResult:{}", JSON.toJSONString(useResult, true));
    }

    String[] levelName = {"", "基础版", "高级版", "专业版"};

    //    @Test
    public void test_batch_createExchangeCode() throws Exception {
        IProductDAO productDAO = testHelper.getMapper(IProductDAO.class);
        List<String> postfixes = Arrays.asList("lq", "jc", "zm", "sh", "yl");
        for (String postfix : postfixes) {
            List<ProductDO> productList = productDAO.selectByTierId(0);
            JSONArray arr = new JSONArray();
            for (ProductDO product : productList) {
                if (product.getTenantId() == null) continue;
                Result<List<String>> createResult = exchangeCodeService.createExchangeCode(product.getId(), 0,
                        3600 * 24 * 30, 20, product.getSubject() + "_20210422");
                arr.add(new JSONObject().fluentPut("subject", product.getSubject())
                        .fluentPut("tenantId", product.getTenantId())
                        .fluentPut("productId", product.getId())
                        .fluentPut("tierId", product.getTierId())
                        .fluentPut("type", product.getType())
                        .fluentPut("levelName", levelName[product.getTierId() % 10])
                        .fluentPut("typeName", ProductTypeEnums.codeOf(product.getType()).getMsg())
                        .fluentPut("exchangeCodes", createResult.getData()));
            }
//        log.info("batch_createExchangeCode:\n{}", JSON.toJSONString(arr, true));
            FileWriter fw = new FileWriter("/Users/<USER>/test/batch_createExchangeCode/staging-us-" + postfix + ".json");
            IOUtils.write(JSON.toJSONString(arr, true), fw);
        }
    }


    @Test
    public void test_updateProductTenantId() {
        // validateTierListConfig rightMap:
        // staging-cn
//        String jsonStr = "{21001:\"vicoo\",20002:\"vicoo\",20003:\"vicoo\",20004:\"vicoo\",20005:\"vicoo\",20006:\"vicoo\",20007:\"vicoo\",20008:\"vicoo\",20009:\"vicoo\",7001:\"vicoo\",7002:\"vicoo\",7003:\"vicoo\",7004:\"vicoo\",7005:\"vicoo\",7006:\"vicoo\",7007:\"vicoo\",7008:\"vicoo\",7009:\"vicoo\",1035001:\"jxja\",1035002:\"jxja\",1035003:\"jxja\",1035004:\"jxja\",1035005:\"jxja\",1035006:\"jxja\",1035007:\"jxja\",1035008:\"jxja\",1035009:\"jxja\",1025004:\"guard\",1025005:\"guard\",1025006:\"guard\",1025007:\"guard\",1025008:\"guard\",1025009:\"guard\",1025001:\"guard\",1025002:\"guard\",1025003:\"guard\",1015001:\"dzees\",1015002:\"dzees\",1015003:\"dzees\",1015004:\"dzees\",1015005:\"dzees\",1015006:\"dzees\",1015007:\"dzees\",1015008:\"dzees\",1015009:\"dzees\",1005001:\"soliom\",1005002:\"soliom\",1005003:\"soliom\",1005004:\"soliom\",1005005:\"soliom\",1005006:\"soliom\",1005007:\"soliom\",1005008:\"soliom\",1005009:\"soliom\"}";
        // staging-eu
//        String jsonStr = "{21001:\"vicoo\",20002:\"vicoo\",20003:\"vicoo\",20004:\"vicoo\",20005:\"vicoo\",20006:\"vicoo\",20007:\"vicoo\",20008:\"vicoo\",20009:\"vicoo\",7001:\"vicoo\",7002:\"vicoo\",7003:\"vicoo\",7004:\"vicoo\",7005:\"vicoo\",7006:\"vicoo\",7007:\"vicoo\",7008:\"vicoo\",7009:\"vicoo\",1055001:\"askari\",1055002:\"askari\",1055003:\"askari\",1055004:\"askari\",1055005:\"askari\",1055006:\"askari\",1055007:\"askari\",1055008:\"askari\",1055009:\"askari\",1035001:\"jxja\",1035002:\"jxja\",1035003:\"jxja\",1035004:\"jxja\",1035005:\"jxja\",1035006:\"jxja\",1035007:\"jxja\",1035008:\"jxja\",1035009:\"jxja\",1015001:\"dzees\",1015002:\"dzees\",1015003:\"dzees\",1015004:\"dzees\",1015005:\"dzees\",1015006:\"dzees\",1015007:\"dzees\",1015008:\"dzees\",1015009:\"dzees\",1005001:\"soliom\",1005002:\"soliom\",1005003:\"soliom\",1005004:\"soliom\",1005005:\"soliom\",1005006:\"soliom\",1005007:\"soliom\",1005008:\"soliom\",1005009:\"soliom\"}";
        // staging-us
//        String jsonStr = "{21001:\"vicoo\",20002:\"vicoo\",20003:\"vicoo\",20004:\"vicoo\",20005:\"vicoo\",20006:\"vicoo\",20007:\"vicoo\",20008:\"vicoo\",20009:\"vicoo\",7001:\"vicoo\",7002:\"vicoo\",7003:\"vicoo\",7004:\"vicoo\",7005:\"vicoo\",7006:\"vicoo\",7007:\"vicoo\",7008:\"vicoo\",7009:\"vicoo\",1055001:\"askari\",1055002:\"askari\",1055003:\"askari\",1055004:\"askari\",1055005:\"askari\",1055006:\"askari\",1055007:\"askari\",1055008:\"askari\",1055009:\"askari\",1035001:\"jxja\",1035002:\"jxja\",1035003:\"jxja\",1035004:\"jxja\",1035005:\"jxja\",1035006:\"jxja\",1035007:\"jxja\",1035008:\"jxja\",1035009:\"jxja\",1015001:\"dzees\",1015002:\"dzees\",1015003:\"dzees\",1015004:\"dzees\",1015005:\"dzees\",1015006:\"dzees\",1015007:\"dzees\",1015008:\"dzees\",1015009:\"dzees\",1005001:\"soliom\",1005002:\"soliom\",1005003:\"soliom\",1005004:\"soliom\",1005005:\"soliom\",1005006:\"soliom\",1005007:\"soliom\",1005008:\"soliom\",1005009:\"soliom\"}";
        // prod-cn
//        String jsonStr = "{20000:\"vicoo\",20002:\"vicoo\",20003:\"vicoo\",20004:\"vicoo\",20005:\"vicoo\",20006:\"vicoo\",20007:\"vicoo\",20008:\"vicoo\",20009:\"vicoo\",1035001:\"jxja\",1035002:\"jxja\",1035003:\"jxja\",1035004:\"jxja\",1035005:\"jxja\",1035006:\"jxja\",1035007:\"jxja\",1035008:\"jxja\",1035009:\"jxja\",1025004:\"guard\",1025005:\"guard\",1025006:\"guard\",1025007:\"guard\",1025008:\"guard\",1025009:\"guard\",1025001:\"guard\",1025002:\"guard\",1025003:\"guard\",1015001:\"dzees\",1015002:\"dzees\",1015003:\"dzees\",1015004:\"dzees\",1015005:\"dzees\",1015006:\"dzees\",1015007:\"dzees\",1015008:\"dzees\",1015009:\"dzees\",1005001:\"soliom\",1005002:\"soliom\",1005003:\"soliom\",1005004:\"soliom\",1005005:\"soliom\",1005006:\"soliom\",1005007:\"soliom\",1005008:\"soliom\",1005009:\"soliom\"}";
        // prod-eu
//        String jsonStr = "{20000:\"vicoo\",20002:\"vicoo\",20003:\"vicoo\",20004:\"vicoo\",20005:\"vicoo\",20006:\"vicoo\",20007:\"vicoo\",20008:\"vicoo\",20009:\"vicoo\",21001:\"vicoo\",1035001:\"jxja\",1035002:\"jxja\",1035003:\"jxja\",1035004:\"jxja\",1035005:\"jxja\",1035006:\"jxja\",1035007:\"jxja\",1035008:\"jxja\",1035009:\"jxja\",1015001:\"dzees\",1015002:\"dzees\",1015003:\"dzees\",1015004:\"dzees\",1015005:\"dzees\",1015006:\"dzees\",1015007:\"dzees\",1015008:\"dzees\",1015009:\"dzees\",1005001:\"soliom\",1005002:\"soliom\",1005003:\"soliom\",1005004:\"soliom\",1005005:\"soliom\",1005006:\"soliom\",1005007:\"soliom\",1005008:\"soliom\",1005009:\"soliom\"}";
        // prod-us
        String jsonStr = "{20000:\"vicoo\",20002:\"vicoo\",20003:\"vicoo\",20004:\"vicoo\",20005:\"vicoo\",20006:\"vicoo\",20007:\"vicoo\",20008:\"vicoo\",20009:\"vicoo\",21001:\"vicoo\",1035001:\"jxja\",1035002:\"jxja\",1035003:\"jxja\",1035004:\"jxja\",1035005:\"jxja\",1035006:\"jxja\",1035007:\"jxja\",1035008:\"jxja\",1035009:\"jxja\",1015001:\"dzees\",1015002:\"dzees\",1015003:\"dzees\",1015004:\"dzees\",1015005:\"dzees\",1015006:\"dzees\",1015007:\"dzees\",1015008:\"dzees\",1015009:\"dzees\",1005001:\"soliom\",1005002:\"soliom\",1005003:\"soliom\",1005004:\"soliom\",1005005:\"soliom\",1005006:\"soliom\",1005007:\"soliom\",1005008:\"soliom\",1005009:\"soliom\"}";
        Map<Integer, String> productId2TenantId = JSONObject.parseObject(jsonStr, new TypeReference<LinkedHashMap<Integer, String>>() {
        });
        StringBuilder stringBuilder = new StringBuilder();
        productId2TenantId.entrySet().stream().sorted(Comparator.comparingInt(it -> it.getKey())).forEach(entry -> {
            stringBuilder.append("update `camera`.`product` set `tenant_id`='").append(entry.getValue())
                    .append("' where `id`=").append(entry.getKey()).append(";\n");
        });
        log.info("test_updateProductTenantId:\n" + stringBuilder);
    }
}
