package com.addx.iotcamera.service.device.model;

import com.addx.iotcamera.bean.device.model.DeviceModelTenantSupportDO;
import com.addx.iotcamera.dao.model.IDeviceModelTenantDAO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class DeviceModelTenantServiceTest {

    @InjectMocks
    @Spy
    private DeviceModelTenantService deviceModelTenantService;

    @Mock
    private IDeviceModelTenantDAO iDeviceModelTenantDAO;


    @Test
    @DisplayName("型号与tenant关系删除")
    public void test() {
        when(iDeviceModelTenantDAO.deleteDeviceModelTenantDO(any())).thenReturn(1);
        deviceModelTenantService.deleteDeviceModelTenant(new DeviceModelTenantSupportDO());
        verify(iDeviceModelTenantDAO, times(1)).deleteDeviceModelTenantDO(any());
    }

    @Test
    @DisplayName("查询型号支持Tenant")
    public void test_queryModelTenantByModel(){
        when(iDeviceModelTenantDAO.queryDeviceModelTenantListByModel(any())).thenReturn(Lists.newArrayList());
        List<DeviceModelTenantSupportDO> expectResult = Lists.newArrayList();
        List<DeviceModelTenantSupportDO> actualResult = deviceModelTenantService.queryModelTenantByModel("");
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("批量查询型号支持Tenant")
    public void test_queryDeviceModelTenantDOByModel(){
        String modelNo = "cg1";
        DeviceModelTenantSupportDO support = new DeviceModelTenantSupportDO();
        support.setModelNo(modelNo);
        support.setTenantId(TENANTID_VICOO);
        when(iDeviceModelTenantDAO.queryDeviceModelTenantListByModels(any())).thenReturn(Arrays.asList(support));
        Map<String,List<String>> expectResult = new HashMap();
        expectResult.put(modelNo,Arrays.asList(TENANTID_VICOO));
        Map<String,List<String>> actualResult = deviceModelTenantService.queryDeviceModelTenantDOByModel(Lists.newArrayList());
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    public void test_queryDeviceModelTenantModelList(){
        when(iDeviceModelTenantDAO.queryDeviceModelTenantList(any())).thenReturn(Lists.newArrayList());
        Set<String> supportModelSet = deviceModelTenantService.queryDeviceModelTenantModelList(TENANTID_VICOO);
        Assert.assertEquals(supportModelSet.size(),0);
    }
}
