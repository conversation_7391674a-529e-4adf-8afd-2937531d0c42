package com.addx.iotcamera.service.lettuce.service.base;

import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.*;

import java.util.*;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LettuceRedisBaseServiceTest {

    @InjectMocks
    LettuceRedisBaseService<String, String> lettuceRedisBaseService;

    String key = "a";

    String field = "a";

    @Mock
    RedisTemplate<String, String> oldRedisTemplate;
    @Mock
    RedisTemplate<String, String> newRedisTemplate;
    @Mock
    ValueOperations<String, String> valueOperations;
    @Mock
    ListOperations<String, String> listOperations;
    @Mock
    SetOperations<String, String> setOperations;
    @Mock
    ZSetOperations<String, String> zSetOperations;
    @Mock
    HashOperations<String, Object, Object> hashOperations;
    @Mock
    ZSetOperations.TypedTuple<String> typedTuple;


    @Before
    public void before() {
        // mock数据
        List<String> resultList = Collections.singletonList("a");
        Set<String> resultSet = Collections.singleton("a");
        Set<ZSetOperations.TypedTuple<String>> typedTuples = Collections.singleton(new DefaultTypedTuple<>("a", 1d));
        Map<Object, Object> resultMap = new HashMap<>();
        resultMap.put("a", "a");
        Map<String, String> resultLettuceMap = new HashMap<>();
        // mock redis
        when(oldRedisTemplate.opsForValue()).thenReturn(valueOperations);
        when(oldRedisTemplate.opsForList()).thenReturn(listOperations);
        when(oldRedisTemplate.opsForSet()).thenReturn(setOperations);
        when(oldRedisTemplate.opsForZSet()).thenReturn(zSetOperations);
        when(oldRedisTemplate.opsForHash()).thenReturn(hashOperations);

        // mock redis
        when(newRedisTemplate.opsForValue()).thenReturn(valueOperations);
        when(newRedisTemplate.opsForList()).thenReturn(listOperations);
        when(newRedisTemplate.opsForSet()).thenReturn(setOperations);
        when(newRedisTemplate.opsForZSet()).thenReturn(zSetOperations);
        when(newRedisTemplate.opsForHash()).thenReturn(hashOperations);

        // mock redis
        when(oldRedisTemplate.opsForValue().get(key)).thenReturn("a");
        when(oldRedisTemplate.opsForList().range(key, 0, -1)).thenReturn(resultList);
        when(oldRedisTemplate.opsForSet().members(key)).thenReturn(resultSet);
        when(oldRedisTemplate.opsForZSet().rangeWithScores(key, 0, -1)).thenReturn(typedTuples);
        when(oldRedisTemplate.opsForHash().entries(key)).thenReturn(resultMap);
        when(oldRedisTemplate.getExpire(key)).thenReturn(1L);
        // mock redis
        when(newRedisTemplate.opsForValue().get(key)).thenReturn("a");
        when(newRedisTemplate.opsForList().range(key, 0, -1)).thenReturn(resultList);
        when(newRedisTemplate.opsForSet().members(key)).thenReturn(resultSet);
        when(newRedisTemplate.opsForZSet().rangeWithScores(key, 0, -1)).thenReturn(typedTuples);
        when(newRedisTemplate.opsForHash().entries(key)).thenReturn(resultMap);
        when(newRedisTemplate.getExpire(key)).thenReturn(1L);
    }

    @Test
    public void getRedisToRedisValue() {
        lettuceRedisBaseService.getRedisToRedisValue(key, oldRedisTemplate, newRedisTemplate);

        when(oldRedisTemplate.opsForValue().get(key)).thenReturn("a");
        when(newRedisTemplate.opsForValue().get(key)).thenReturn(null);
        lettuceRedisBaseService.getRedisToRedisValue(key, oldRedisTemplate, newRedisTemplate);
    }

    @Test
    public void getRedisToRedisList() {
        lettuceRedisBaseService.getRedisToRedisList(key, oldRedisTemplate, newRedisTemplate);

        when(oldRedisTemplate.opsForList().range(key, 0, -1)).thenReturn(Collections.singletonList("a"));
        when(newRedisTemplate.opsForList().range(key, 0, -1)).thenReturn(null);
        lettuceRedisBaseService.getRedisToRedisList(key, oldRedisTemplate, newRedisTemplate);
    }

    @Test
    public void getRedisToRedisSet() {
        lettuceRedisBaseService.getRedisToRedisSet(key, oldRedisTemplate, newRedisTemplate);

        when(oldRedisTemplate.opsForSet().members(key)).thenReturn(Collections.singleton("a"));
        when(newRedisTemplate.opsForSet().members(key)).thenReturn(null);
        lettuceRedisBaseService.getRedisToRedisSet(key, oldRedisTemplate, newRedisTemplate);
    }

    @Test
    public void getRedisToRedisZSet() {
        lettuceRedisBaseService.getRedisToRedisZSet(key, oldRedisTemplate, newRedisTemplate);

        Set<ZSetOperations.TypedTuple<String>> typedTuples = new HashSet<>();
        typedTuples.add(typedTuple);
        when(oldRedisTemplate.opsForZSet().rangeWithScores(key, 0, -1)).thenReturn(typedTuples);
        when(newRedisTemplate.opsForZSet().rangeWithScores(key, 0, -1)).thenReturn(null);
        lettuceRedisBaseService.getRedisToRedisZSet(key, oldRedisTemplate, newRedisTemplate);
    }

    @Test
    public void getRedisToRedisObjMap() {
        lettuceRedisBaseService.getRedisToRedisObjMap(key, oldRedisTemplate, newRedisTemplate);

        Map<Object, Object> map = new HashMap<>();
        map.put("a", "a");
        when(oldRedisTemplate.opsForHash().entries(key)).thenReturn(map);
        when(oldRedisTemplate.opsForSet().members(key)).thenReturn(Collections.singleton("a"));
        when(newRedisTemplate.opsForSet().members(key)).thenReturn(null);
    }

    @Test
    public void getRedisToRedisMap() {
        lettuceRedisBaseService.getRedisToRedisMap(key, oldRedisTemplate, newRedisTemplate);

        Map<Object, Object> map = new HashMap<>();
        map.put("a", "a");
        when(oldRedisTemplate.opsForHash().entries(key)).thenReturn(map);
        when(oldRedisTemplate.opsForSet().members(key)).thenReturn(Collections.singleton("a"));
        when(newRedisTemplate.opsForSet().members(key)).thenReturn(null);
        lettuceRedisBaseService.getRedisToRedisMap(key, oldRedisTemplate, newRedisTemplate);
    }

    @Test
    public void getRedisToRedisObjMapValue() {
        lettuceRedisBaseService.getRedisToRedisObjMapValue(key, oldRedisTemplate, newRedisTemplate);


        Map<Object, Object> map = new HashMap<>();
        map.put("a", "a");
        when(oldRedisTemplate.opsForHash().entries(key)).thenReturn(map);
        when(newRedisTemplate.opsForHash().entries(key)).thenReturn(map);
        lettuceRedisBaseService.getRedisToRedisObjMapValue(key, oldRedisTemplate, newRedisTemplate);
    }

    @Test
    public void hasKey() {
        boolean b = lettuceRedisBaseService.hasKey(key, oldRedisTemplate, newRedisTemplate);
        System.out.println(b);

        b = lettuceRedisBaseService.hasKey(key, oldRedisTemplate, newRedisTemplate);
        System.out.println(b);
        when(oldRedisTemplate.type(key)).thenReturn(DataType.STRING);
        b = lettuceRedisBaseService.hasKey(key, oldRedisTemplate, newRedisTemplate);
        System.out.println(b);
        when(newRedisTemplate.hasKey(key)).thenReturn(false);
        when(oldRedisTemplate.type(key)).thenReturn(DataType.LIST);
        b = lettuceRedisBaseService.hasKey(key, oldRedisTemplate, newRedisTemplate);
        System.out.println(b);
        when(oldRedisTemplate.type(key)).thenReturn(DataType.SET);
        b = lettuceRedisBaseService.hasKey(key, oldRedisTemplate, newRedisTemplate);
        System.out.println(b);
        when(oldRedisTemplate.type(key)).thenReturn(DataType.ZSET);
        b = lettuceRedisBaseService.hasKey(key, oldRedisTemplate, newRedisTemplate);
        System.out.println(b);
        when(oldRedisTemplate.type(key)).thenReturn(DataType.HASH);
        b = lettuceRedisBaseService.hasKey(key, oldRedisTemplate, newRedisTemplate);
        System.out.println(b);
    }

    @Test
    public void dataTransfer() {
        boolean b = lettuceRedisBaseService.dataTransfer(key, oldRedisTemplate, newRedisTemplate);
        System.out.println(b);
    }

}