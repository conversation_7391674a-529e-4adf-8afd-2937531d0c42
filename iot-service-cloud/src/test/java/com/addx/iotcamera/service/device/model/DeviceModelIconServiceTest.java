package com.addx.iotcamera.service.device.model;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.addx.iotcamera.bean.device.model.DeviceModelIconDO;
import com.addx.iotcamera.dao.model.IDeviceModelIconDAO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceModelIconServiceTest {
    @InjectMocks
    private DeviceModelIconService deviceModelIconService;

    @Mock
    private IDeviceModelIconDAO iDeviceModelIconDAO;

    @Test
    public void testQueryDeviceModelIconWithValidModelNo() {
        // Positive test case with valid model number
        String modelNo = "ABC123";

        DeviceModelIconDO deviceModelIconDO = new DeviceModelIconDO();
        
        when(iDeviceModelIconDAO.queryDeviceModelIcon(any())).thenReturn(deviceModelIconDO);
        DeviceModelIconDO result = deviceModelIconService.queryDeviceModelIcon(modelNo);

        Assert.assertNotNull(result);
        // Add more assertions to validate the output
    }

    @Test
    public void testQueryDeviceModelIconWithEmptyModelNo() {
        // Negative test case with empty model number
        String modelNo = "";

        DeviceModelIconDO result = deviceModelIconService.queryDeviceModelIcon(modelNo);

        Assert.assertNull(result);
    }
}
