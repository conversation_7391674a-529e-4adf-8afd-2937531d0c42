package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.db.user.FunctionOperateLog;
import com.addx.iotcamera.bean.domain.library.TierFreeNotifyPeriodDO;
import com.addx.iotcamera.dao.user.IFunctionOperateLogDao;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.Instant;
import java.util.Map;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class FunctionOperateLogServiceTest {
    @InjectMocks
    private FunctionOperateLogService functionOperateLogService;

    @Mock
    private IFunctionOperateLogDao iFunctionOperateLogDao;

    @Test
    @DisplayName("保护期规则未配置")
    public void test_inPeriod_operateProtect_empty() {
        Integer userId = 1;
        Map<Integer, Integer> operateProtectMap = Maps.newHashMap();
        String functionName = "test";

        TierFreeNotifyPeriodDO exceptedResult = TierFreeNotifyPeriodDO.builder()
                .inPeriod(false)
                .notifyCount(1)
                .build();

        TierFreeNotifyPeriodDO actualResult = functionOperateLogService.inPeriod(userId, operateProtectMap, functionName);
        Assert.assertEquals(exceptedResult, actualResult);
    }

    @Test
    @DisplayName("未操作过")
    public void test_inPeriod_operateLog_empty() {
        Integer userId = 1;
        Map<Integer, Integer> operateProtectMap = Maps.newHashMap();
        operateProtectMap.put(1, 1);
        String functionName = "test";

        when(iFunctionOperateLogDao.queryFunctionOperateLog(any(), any())).thenReturn(null);

        TierFreeNotifyPeriodDO exceptedResult = TierFreeNotifyPeriodDO.builder()
                .inPeriod(false)
                .notifyCount(1)
                .build();

        TierFreeNotifyPeriodDO actualResult = functionOperateLogService.inPeriod(userId, operateProtectMap, functionName);
        Assert.assertEquals(exceptedResult, actualResult);
    }

    @Test
    @DisplayName("未操作过")
    public void test_inPeriod_operate_not_contain() {
        Integer userId = 1;
        Map<Integer, Integer> operateProtectMap = Maps.newHashMap();
        operateProtectMap.put(1, 1);
        String functionName = "test";

        FunctionOperateLog functionOperateLog = new FunctionOperateLog();
        functionOperateLog.setNum(2);
        when(iFunctionOperateLogDao.queryFunctionOperateLog(any(), any())).thenReturn(functionOperateLog);

        TierFreeNotifyPeriodDO exceptedResult = TierFreeNotifyPeriodDO.builder()
                .inPeriod(true)
                .notifyCount(2)
                .build();

        TierFreeNotifyPeriodDO actualResult = functionOperateLogService.inPeriod(userId, operateProtectMap, functionName);
        Assert.assertEquals(exceptedResult, actualResult);
    }


    @Test
    @DisplayName("判断是否处于保护期内")
    public void test_inPeriod_operate() {
        Integer userId = 1;
        Map<Integer, Integer> operateProtectMap = Maps.newHashMap();
        operateProtectMap.put(1, 1000);
        String functionName = "test";


        FunctionOperateLog functionOperateLog = new FunctionOperateLog();
        functionOperateLog.setNum(1);
        functionOperateLog.setOperatorTime((int) Instant.now().getEpochSecond());
        when(iFunctionOperateLogDao.queryFunctionOperateLog(any(), any())).thenReturn(functionOperateLog);

        TierFreeNotifyPeriodDO exceptedResult = TierFreeNotifyPeriodDO.builder()
                .inPeriod(true)
                .notifyCount(2)
                .build();

        TierFreeNotifyPeriodDO actualResult = functionOperateLogService.inPeriod(userId, operateProtectMap, functionName);
        Assert.assertEquals(exceptedResult, actualResult);
    }


    @Test
    @DisplayName("判断是否处于保护期内")
    public void test_inPeriod_operate_noIn_period() {
        Integer userId = 1;
        Map<Integer, Integer> operateProtectMap = Maps.newHashMap();
        operateProtectMap.put(1, 1000);
        String functionName = "test";


        FunctionOperateLog functionOperateLog = new FunctionOperateLog();
        functionOperateLog.setNum(1);
        functionOperateLog.setOperatorTime((int) Instant.now().getEpochSecond() - 2000);
        when(iFunctionOperateLogDao.queryFunctionOperateLog(any(), any())).thenReturn(functionOperateLog);

        TierFreeNotifyPeriodDO exceptedResult = TierFreeNotifyPeriodDO.builder()
                .inPeriod(false)
                .notifyCount(2)
                .build();

        TierFreeNotifyPeriodDO actualResult = functionOperateLogService.inPeriod(userId, operateProtectMap, functionName);
        Assert.assertEquals(exceptedResult, actualResult);
    }


    @Test
    public void test_updateFunctionOperate(){
        when(iFunctionOperateLogDao.insertOrUpdateFunctionOperateLog(any())).thenReturn(1);
        functionOperateLogService.updateFunctionOperate("",1);
        verify(iFunctionOperateLogDao, times(1)).insertOrUpdateFunctionOperateLog(any());
    }
}
