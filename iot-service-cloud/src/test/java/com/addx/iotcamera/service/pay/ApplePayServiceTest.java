package com.addx.iotcamera.service.pay;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.payment.ApplePaymentRequest;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.domain.pay.OrderVerifyResultDO;
import com.addx.iotcamera.bean.domain.pay.PaymentConfig;
import com.addx.iotcamera.config.pay.PaymentCenterConfig;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.pay.IOrderProductDAO;
import com.addx.iotcamera.dao.pay.IPaymentFlowDAO;
import com.addx.iotcamera.service.PaymentService;
import com.addx.iotcamera.service.ProductService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.service.vip.OrderService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Sets;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.addx.iot.common.enums.ResultCollection.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ApplePayServiceTest {
    @InjectMocks
    private ApplePayService applePayService;

    @Mock
    private PaymentService paymentService;

    @Mock
    private IOrderDAO iOrderDAO;

    @Mock
    private IOrderProductDAO iOrderProductDAO;

    @Mock
    private OrderService orderService;

    @Mock
    private ProductService productService;

    @Mock
    private PaymentCenterConfig paymentCenterConfig;

    @Mock
    private IPaymentFlowDAO iPaymentFlowDAO;

    @Mock
    private UserVipService userVipService;

    @Test
    @DisplayName("验证IOS支付信息-订单信息为空")
    public void test_appleOrderVerify_verifyResult_null(){
        String verifyResult = null;
        PaymentConfig paymentConfig = new PaymentConfig();

        Integer expectResult = null;
        OrderVerifyResultDO actualResult = applePayService.appleOrderVerify(verifyResult,"",paymentConfig);
        Assert.assertEquals(expectResult,actualResult.getProductId());
    }
    @Test
    @DisplayName("验证IOS支付信息-订单信息为空")
    public void test_appleOrderVerifyV1_verifyResult_null(){
        String verifyResult = null;
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        PaymentConfig paymentConfig = new PaymentConfig();

        Integer expectResult = IOS_ORDER_EMPTY.getCode();
        OrderVerifyResultDO actualResult = applePayService.appleOrderVerifyV1(verifyResult,receipt,paymentConfig);
        Assert.assertEquals(expectResult,actualResult.getProductId());
    }

    @Test
    @DisplayName("批量获取已存在的账单-status error")
    public void test_appleOrderVerify_status_error(){
        String verifyResult = "{\"status\":\"20003\"}";
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        PaymentConfig paymentConfig = new PaymentConfig();

        Integer expectResult = null;
        OrderVerifyResultDO actualResult = applePayService.appleOrderVerify(verifyResult,"",paymentConfig);
        Assert.assertEquals(expectResult,actualResult.getProductId());
    }

    @Test
    @DisplayName("批量获取已存在的账单-status error")
    public void test_appleOrderVerifyV1_status_error(){
        String verifyResult = "{\"status\":\"20003\"}";
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        PaymentConfig paymentConfig = new PaymentConfig();

        Integer expectResult = IOS_ORDER_STATUS_ERROR.getCode();
        OrderVerifyResultDO actualResult = applePayService.appleOrderVerifyV1(verifyResult,receipt,paymentConfig);
        Assert.assertEquals(expectResult,actualResult.getProductId());
    }

    @Test
    @DisplayName("批量获取已存在的账单-bundle error")
    public void test_queryExistPaymentBatch_bundle_error(){
        JSONObject obj = new JSONObject();
        obj.put("status",0);
        JSONObject receiptOrder = new JSONObject();
        receiptOrder.put("bundle_id","addx.ai.vicoo");
        obj.put("receipt",receiptOrder);

        String verifyResult = obj.toJSONString();
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        PaymentConfig paymentConfig = new PaymentConfig();
        paymentConfig.setAppleBundleId("addx.ai.vicoo.staging");

        Integer expectResult = IOS_ORDER_PACKAGE_ERROR.getCode();
        OrderVerifyResultDO actualResult = applePayService.appleOrderVerifyV1(verifyResult,receipt,paymentConfig);
        Assert.assertEquals(expectResult,actualResult.getProductId());
    }


    @Test
    @DisplayName("批量获取已存在的账单-inApp empty")
    public void test_queryExistPaymentBatch_inApp_null(){
        JSONObject obj = new JSONObject();
        obj.put("status",0);
        JSONObject receiptOrder = new JSONObject();
        receiptOrder.put("bundle_id","addx.ai.vicoo");
        obj.put("receipt",receiptOrder);

        String verifyResult = obj.toJSONString();
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        PaymentConfig paymentConfig = new PaymentConfig();
        paymentConfig.setAppleBundleId("addx.ai.vicoo");

        Integer expectResult = IOS_ORDER_EMPTY.getCode();
        OrderVerifyResultDO actualResult = applePayService.appleOrderVerifyV1(verifyResult,receipt,paymentConfig);
        Assert.assertEquals(expectResult,actualResult.getProductId());
    }

    @Test
    @DisplayName("批量获取已存在的账单-inApp empty")
    public void test_appleOrderVerify_inApp_null(){
        JSONObject obj = new JSONObject();
        obj.put("status",0);
        JSONObject receiptOrder = new JSONObject();
        receiptOrder.put("bundle_id","addx.ai.vicoo");
        obj.put("receipt",receiptOrder);

        String verifyResult = obj.toJSONString();
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        PaymentConfig paymentConfig = new PaymentConfig();
        paymentConfig.setAppleBundleId("addx.ai.vicoo");

        Integer expectResult = null;
        OrderVerifyResultDO actualResult = applePayService.appleOrderVerify(verifyResult,"transaction_id",paymentConfig);
        Assert.assertEquals(expectResult,actualResult.getProductId());
    }

    @Test
    @DisplayName("批量获取已存在的账单-未支付")
    public void test_queryExistPaymentBatch_contain_transactionId(){
        String productId = "20002";
        String transactionId = "500001096701202";

        JSONObject obj = new JSONObject();
        obj.put("status",0);
        JSONObject receiptOrder = new JSONObject();
        receiptOrder.put("bundle_id","addx.ai.vicoo");

        JSONArray jsonArray = new JSONArray();
        JSONObject trade = new JSONObject();
        trade.put("transaction_id",transactionId);
        trade.put("product_id",productId);
        trade.put("purchase_date_ms",System.currentTimeMillis());
        trade.put("purchase_date_pst","2023-01-01 00:00:00");
        trade.put("purchase_date","2023-01-01 00:00:00");
        jsonArray.add(trade);
        receiptOrder.put("in_app",jsonArray);

        obj.put("receipt",receiptOrder);

        String verifyResult = obj.toJSONString();
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        receipt.setTransactionId("500001096701201");

        when(paymentService.queryExistPaymentBatch(any())).thenReturn(Sets.newHashSet());

        PaymentConfig paymentConfig = new PaymentConfig();
        paymentConfig.setAppleBundleId("addx.ai.vicoo");

        Integer expectResult = 0;
        OrderVerifyResultDO actualResult = applePayService.appleOrderVerifyV1(verifyResult,receipt,paymentConfig);
        Assert.assertEquals(expectResult,actualResult.getProductId());
    }


    @Test
    @DisplayName("批量获取已存在的账单-已支付")
    public void test_queryExistPaymentBatch_all_verify() {
        String productId = "20002";
        String transactionId = "500001096701202";

        //拼接账单信息
        JSONObject obj = new JSONObject();
        obj.put("status", 0);
        JSONObject receiptOrder = new JSONObject();
        receiptOrder.put("bundle_id", "addx.ai.vicoo");
        JSONArray jsonArray = new JSONArray();
        JSONObject trade = new JSONObject();
        trade.put("transaction_id", transactionId);
        trade.put("product_id", productId);
        trade.put("purchase_date_ms",System.currentTimeMillis());
        trade.put("purchase_date_pst","2023-01-01 00:00:00");
        trade.put("purchase_date","2023-01-01 00:00:00");
        trade.put("is_trial_period",false);
        jsonArray.add(trade);
        receiptOrder.put("in_app", jsonArray);
        obj.put("receipt", receiptOrder);


        String verifyResult = obj.toJSONString();
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        receipt.setTransactionId(transactionId);

        Set<String> existTradeNoSet = Sets.newHashSet();
        existTradeNoSet.add(transactionId);
        when(paymentService.queryExistPaymentBatch(any())).thenReturn(existTradeNoSet);

        PaymentConfig paymentConfig = new PaymentConfig();
        paymentConfig.setAppleBundleId("addx.ai.vicoo");

        Integer expectResult = 20002;
        OrderVerifyResultDO actualResult = applePayService.appleOrderVerifyV1(verifyResult, receipt, paymentConfig);
        Assert.assertEquals(expectResult,actualResult.getProductId());
    }

    @Test
    @DisplayName("查询订阅订单list")
    public void test_querySublist(){
        List<OrderDO> expectResult = Lists.newArrayList();
        List<OrderDO> actualResult = applePayService.querySublist(any());

        Assert.assertEquals(expectResult,actualResult);
    }



    @Test
    @DisplayName("订单验证结果为空")
    public void test_subOrder_verifyResult_empty() throws Exception {
        String verifyResult = "";
        ProductDO productDO = new ProductDO();
        OrderDO orderDO = new OrderDO();


        boolean expectResult = false;
        boolean actualResult = applePayService.subOrder(verifyResult,productDO,orderDO,"");
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("订单状态不正确")
    public void test_subOrder_status_error() throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("status",0);
        JSONArray array = new JSONArray();
        jsonObject.put("latest_receipt_info",array);

        String verifyResult = jsonObject.toJSONString();
        ProductDO productDO = new ProductDO();
        OrderDO orderDO = new OrderDO();

        boolean expectResult = false;
        boolean actualResult = applePayService.subOrder(verifyResult,productDO,orderDO,"");
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("订单记录为空")
    public void test_subOrder_orderList_empty() throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("status",0);
        JSONArray array = new JSONArray();
        jsonObject.put("latest_receipt_info",array);

        String verifyResult = jsonObject.toJSONString();
        ProductDO productDO = new ProductDO();
        OrderDO orderDO = new OrderDO();

        boolean expectResult = false;
        boolean actualResult = applePayService.subOrder(verifyResult,productDO,orderDO,"");
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("订单-历史订单")
    public void test_subOrder_orderList_history() throws Exception {
        Integer productId = 20103;
        Long currentTime = Instant.now().getEpochSecond();

        JSONObject jsonObject = this.initIosOrder(String.valueOf(productId + 1),currentTime);
        String verifyResult = jsonObject.toJSONString();

        ProductDO productDO = new ProductDO();
        productDO.setId(productId);
        productDO.setMonth(1);
        OrderDO orderDO = new OrderDO();
        orderDO.setOrderCancel(0);
        orderDO.setSubType(1);
        orderDO.setTimeEnd(currentTime.intValue());



        {
            when(orderService.queryOrderProductDO(any())).thenReturn(null);
            boolean expectResult = true;
            boolean actualResult = applePayService.subOrder(verifyResult,productDO,orderDO,"");
            Assert.assertEquals(expectResult,actualResult);
        }
        {
            when(iPaymentFlowDAO.queryLastPaymentTime(any())).thenReturn(0);
            when(productService.queryProductById(any())).thenReturn(productDO);
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(1).build());

            boolean expectResult = true;
            boolean actualResult = applePayService.subOrder(verifyResult,productDO,orderDO,"");
            Assert.assertEquals(expectResult,actualResult);
        }
        {

            when(iPaymentFlowDAO.queryLastPaymentTime(any())).thenReturn((int)(currentTime-1000));
            when(productService.queryProductById(any())).thenReturn(productDO);
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(1).build());

            boolean expectResult = true;
            boolean actualResult = applePayService.subOrder(verifyResult,productDO,orderDO,"");
            Assert.assertEquals(expectResult,actualResult);
        }
        {
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(productId).build());

            boolean expectResult = true;
            boolean actualResult = applePayService.subOrder(verifyResult,productDO,orderDO,"");
            Assert.assertEquals(expectResult,actualResult);
        }

    }


    @Test
    public void test_orderRenewStatus(){
        Integer productId = 1;
        Long currentTime = System.currentTimeMillis();

        OrderDO  orderDO = OrderDO.builder().subType(1).build();
        JSONObject jsonObject = this.initIosOrder(String.valueOf(productId + 1),currentTime);
        when(iOrderDAO.updateOrderCancelTime(any())).thenReturn(1);
        {
            // 已取消订阅套餐
            orderDO.setOrderCancel(1);
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(productId).build());
            applePayService.orderRenewStatus(jsonObject,orderDO,"");
            Assert.assertTrue(orderDO.getOrderCancel().equals(1));
        }
        {
            // 验证是否连续订阅订单
            orderDO.setSubType(0);
            applePayService.orderRenewStatus(jsonObject,orderDO,"");
            Assert.assertTrue(orderDO.getOrderCancel().equals(1));
        }
        orderDO.setSubType(1);
        {
            orderDO.setOrderCancel(0);
            // 订单号相等，product不同，已经续订新套餐
            orderDO.setTradeNo("original_transaction_id");
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(productId).build());
            applePayService.orderRenewStatus(jsonObject,orderDO,"");
            Assert.assertTrue(orderDO.getOrderCancel().equals(1));
        }
        {
            orderDO.setOrderCancel(0);
            // 订单号相等，product相同同，连续订阅取消
            orderDO.setTradeNo("original_transaction_id");
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(productId+1).build());
            applePayService.orderRenewStatus(jsonObject,orderDO,"");
            Assert.assertTrue(orderDO.getOrderCancel().equals(1));
        }
        {
            orderDO.setOrderCancel(0);
            // 订单不相等，商品相等--已经续订新套餐
            orderDO.setTradeNo("tradeNo");
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(productId).build());
            applePayService.orderRenewStatus(jsonObject,orderDO,"");
            Assert.assertTrue(orderDO.getOrderCancel().equals(1));
        }
        {
            orderDO.setOrderCancel(0);
            orderDO.setTradeNo("transaction_id");
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(productId).build());
            applePayService.orderRenewStatus(jsonObject,orderDO,"");
            Assert.assertTrue(orderDO.getOrderCancel().equals(1));
        }
        {
            orderDO.setOrderCancel(1);
            orderDO.setOrderCancelReason(0);
            // 订单号存在，商品相等， ios 平台notify
            orderDO.setTradeNo("original_transaction_id");

            when(productService.queryProductById(any())).thenReturn(new ProductDO().setMonth(1));
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(productId+1).build());
            applePayService.orderRenewStatus(jsonObject,orderDO,"");
            Assert.assertTrue(orderDO.getOrderCancel().equals(1));
        }
        {
            orderDO.setOrderCancel(0);
            // 订单号存在，商品相等， ios 平台notify
            orderDO.setTradeNo("original_transaction_id");

            when(productService.queryProductById(any())).thenReturn(new ProductDO().setMonth(1));
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(productId).build());
            applePayService.orderRenewStatus(jsonObject,orderDO,"");
            Assert.assertTrue(orderDO.getOrderCancel().equals(1));
        }

        {
            orderDO.setOrderCancel(0);
            orderDO.setTradeNo("original_transaction_id");
            when(productService.queryProductById(any())).thenReturn(new ProductDO().setMonth(1));
            when(orderService.queryOrderProductDO(any())).thenReturn(OrderProductDo.builder().productId(productId).build());
            applePayService.orderRenewStatus(jsonObject,orderDO,"");
            Assert.assertTrue(orderDO.getOrderCancel().equals(1));
        }
    }

    @Test
    @DisplayName("订单-购买订单")
    public void test_subOrder_orderList_current_pay() throws Exception {
        Integer productId = 20103;
        Long currentTime = Instant.now().getEpochSecond();

        JSONObject jsonObject = this.initIosOrder(String.valueOf(productId),currentTime+100);
        String verifyResult = jsonObject.toJSONString();

        ProductDO productDO = new ProductDO();
        productDO.setId(productId);

        OrderDO orderDO = new OrderDO();
        orderDO.setId(1L);
        orderDO.setTimeEnd(currentTime.intValue());
        orderDO.setOrderCancel(1);
        orderDO.setSubType(1);
        when(paymentService.queryPaymentFlow(any())).thenReturn(null);
        when(iOrderDAO.updateOrderStatus(any())).thenReturn(1);

        OrderProductDo orderProductDo = new OrderProductDo();
        when(iOrderProductDAO.selectOrderProductByOrderId(any())).thenReturn(orderProductDo);
        doNothing().when(paymentService).initUserVip(any(),any());
        when(paymentService.initPayment(any(),any(),any(),any())).thenReturn(null);

        boolean expectResult = true;
        boolean actualResult = applePayService.subOrder(verifyResult,productDO,orderDO,"");
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("订单-退款订单")
    public void test_subOrder_orderList_current_refund() throws Exception {
        Integer productId = 20103;
        Long currentTime = Instant.now().getEpochSecond();

        JSONObject jsonObject = this.initIosRefundOrder(String.valueOf(productId),currentTime+100);
        String verifyResult = jsonObject.toJSONString();

        ProductDO productDO = new ProductDO();
        productDO.setId(productId);

        OrderDO orderDO = new OrderDO();
        orderDO.setOrderCancel(0);
        orderDO.setId(1L);
        orderDO.setTimeEnd(currentTime.intValue());
        orderDO.setTradeNo("original_transaction_id");
        orderDO.setSubType(1);
        orderDO.setOrderCancel(1);
        when(paymentService.queryPaymentFlow(any())).thenReturn(new PaymentFlow());
        when(iOrderDAO.updateOrderStatus(any())).thenReturn(1);

        doNothing().when(paymentService).updateRefundInfo(any());

        when(iOrderDAO.updateOrderCancelTime(any())).thenReturn(1);

        doNothing().when(userVipService).userVipRefundOrUpgrade(any(),any());

        when(iOrderDAO.updateOrderCancelTime(any())).thenReturn(1);

        boolean expectResult = true;
        boolean actualResult = applePayService.subOrder(verifyResult,productDO,orderDO,"");
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("订单-退款订单")
    public void test_subOrder_d() throws Exception {
        Integer productId = 20103;
        Long currentTime = Instant.now().getEpochSecond();

        JSONObject jsonObject = this.initIosUpgradeOrder(String.valueOf(productId),currentTime+100);
        String verifyResult = jsonObject.toJSONString();

        ProductDO productDO = new ProductDO();
        productDO.setId(productId);

        OrderDO orderDO = new OrderDO();
        orderDO.setOrderCancel(0);
        orderDO.setId(1L);
        orderDO.setTimeEnd(currentTime.intValue());
        orderDO.setTradeNo("original_transaction_id");
        orderDO.setSubType(1);
        orderDO.setOrderCancel(1);
        when(paymentService.queryPaymentFlow(any())).thenReturn(new PaymentFlow());
        when(iOrderDAO.updateOrderStatus(any())).thenReturn(1);

        doNothing().when(paymentService).updateRefundInfo(any());

        when(iOrderDAO.updateOrderCancelTime(any())).thenReturn(1);

        doNothing().when(userVipService).userVipRefundOrUpgrade(any(),any());

        when(iOrderDAO.updateOrderCancelTime(any())).thenReturn(1);

        boolean expectResult = true;
        boolean actualResult = applePayService.subOrder(verifyResult,productDO,orderDO,"");
        Assert.assertEquals(expectResult,actualResult);
    }


    private JSONObject initIosOrder(String productId,Long expireTime){
        JSONObject orderInfo = new JSONObject();
        orderInfo.put("status",0);
        JSONArray array = new JSONArray();
        JSONObject order1 = new JSONObject();
        order1.put("product_id",productId);
        order1.put("expires_date_ms",expireTime*1000);
        order1.put("transaction_id","transaction_id");
        order1.put("purchase_date_ms",expireTime*1000);

        order1.put("purchase_date","2023-01-01 10:10:10");
        order1.put("purchase_date_pst","2023-01-01 10:10:10");
        array.add(order1);


        orderInfo.put("latest_receipt_info",array);



        JSONArray renewal = new JSONArray();
        JSONObject re = new JSONObject();
        re.put("original_transaction_id","original_transaction_id");
        re.put("auto_renew_status",0);
        re.put("product_id",productId);
        re.put("expiration_intent",1);
        renewal.add(re);
        orderInfo.put("pending_renewal_info",renewal);
        return orderInfo;
    }

    private JSONObject initIosRefundOrder(String productId,Long expireTime){
        JSONObject orderInfo = new JSONObject();
        orderInfo.put("status",0);
        JSONArray array = new JSONArray();
        JSONObject order1 = new JSONObject();
        order1.put("product_id",productId);
        order1.put("expires_date_ms",expireTime*1000);
        order1.put("transaction_id","transaction_id");
        order1.put("purchase_date_ms",expireTime*1000);
        order1.put("cancellation_date_ms",expireTime*1000);
        array.add(order1);


        orderInfo.put("latest_receipt_info",array);


        JSONArray renewal = new JSONArray();
        JSONObject re = new JSONObject();
        re.put("original_transaction_id","original_transaction_id");
        re.put("auto_renew_status",0);
        re.put("expiration_intent",1);
        renewal.add(re);
        orderInfo.put("pending_renewal_info",renewal);

        return orderInfo;
    }

    private JSONObject initIosUpgradeOrder(String productId,Long expireTime){
        JSONObject orderInfo = new JSONObject();
        orderInfo.put("status",0);
        JSONArray array = new JSONArray();
        JSONObject order1 = new JSONObject();
        order1.put("product_id",productId);
        order1.put("expires_date_ms",expireTime*1000);
        order1.put("transaction_id","transaction_id");
        order1.put("purchase_date_ms",expireTime*1000);
        order1.put("cancellation_date_ms",expireTime*1000);
        order1.put("is_upgraded","true");
        array.add(order1);


        orderInfo.put("latest_receipt_info",array);


        JSONArray renewal = new JSONArray();
        JSONObject re = new JSONObject();
        re.put("original_transaction_id","original_transaction_id");
        re.put("auto_renew_status",0);
        re.put("expiration_intent",1);
        renewal.add(re);
        orderInfo.put("pending_renewal_info",renewal);

        return orderInfo;
    }

    @Test
    public void updateOrderCancelStatus_ordernull(){
        when(orderService.querySubOrder(any())).thenReturn(null);
        applePayService.updateOrderCancelStatus("t",1);
        verify(orderService, times(0)).updateOrderCancel(any());
    }

    @Test
    public void updateOrderCancelStatus(){
        when(orderService.querySubOrder(any())).thenReturn(new OrderDO());
        applePayService.updateOrderCancelStatus("t",1);
        verify(orderService, times(1)).updateOrderCancel(any());
    }

    @Test
    @DisplayName("批量查询订单")
    public void test_queryIosOrderBatch(){
        Long startOrderId = 1L;
        List<OrderDO> expectedResult = Lists.newArrayList();
        when(iOrderDAO.queryIosOrderBatch(startOrderId)).thenReturn(Lists.newArrayList());
        List<OrderDO> actualResult = applePayService.queryIosOrderBatch(startOrderId);

        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("历史订单支付信息-extend empty")
    public void test_appleOrderResultHistory_extend_empty(){
        OrderDO orderDO = new OrderDO();

        boolean expectedResult = false;
        boolean actualResult = applePayService.appleOrderResultHistory(orderDO,"");
        Assert.assertEquals(expectedResult,actualResult);
    }
    @Test
    @DisplayName("历史订单支付信息-商品信息为空")
    public void test_appleOrderResultHistory_noproduct(){
        OrderDO orderDO = this.initIosOrderDO();
        when(productService.queryProductById(any())).thenReturn(null);

        boolean expectedResult = false;
        boolean actualResult = applePayService.appleOrderResultHistory(orderDO,"");
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("历史订单支付信息-历史订单验证")
    public void test_appleOrderResultHistory() throws Exception {
        OrderDO orderDO = this.initIosOrderDO();

        ProductDO productDO = new ProductDO();
        productDO.setTenantId(TENANTID_VICOO);
        when(productService.queryProductById(any())).thenReturn(productDO);

        Map<String, PaymentConfig> config = this.initPaymentConfig();
        when(paymentCenterConfig.getConfig()).thenReturn(config);

        boolean expectedResult = true;
        boolean actualResult = applePayService.appleOrderResultHistory(orderDO,"");
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("ios 验证结果为空")
    public void test_iosOrderHistory_verifyResult_null(){
        boolean exceptedResult = false;
        boolean actualResult = applePayService.iosOrderHistory(null,new OrderDO(),"");
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    @DisplayName("ios验证历史结果-status不正确")
    public void test_iosOrderHistory_verifyResult_status_error(){
        String orderInfo = this.initVerifyResult(1,false,false,false);
        boolean exceptedResult = false;

        boolean actualResult = applePayService.iosOrderHistory(orderInfo,new OrderDO(),"");
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    @DisplayName("ios验证历史结果-历史账单list")
    public void test_iosOrderHistory_verifyResult_history_empty(){
        String orderInfo = this.initVerifyResult(0,false,false,false);
        boolean exceptedResult = true;

        boolean actualResult = applePayService.iosOrderHistory(orderInfo,OrderDO.builder().orderCancel(0).subType(1).build(),"");
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    @DisplayName("ios验证历史结果-历史账单list-正常购买订单")
    public void test_iosOrderHistory_verifyResult_history_payOrder(){
        String orderInfo = this.initVerifyResult(0,true,false,false);
        boolean exceptedResult = true;

        boolean actualResult = applePayService.iosOrderHistory(orderInfo,OrderDO.builder().orderCancel(0).subType(1).build(),"");
        Assert.assertEquals(exceptedResult,actualResult);
    }


    @Test
    @DisplayName("ios验证历史结果-历史账单list-退订订单_paymentflew_null")
    public void test_iosOrderHistory_verifyResult_refundOrder_paymentFlew_null(){
        String orderInfo = this.initVerifyResult(0,true,true,false);
        boolean exceptedResult = true;
        when(paymentService.queryPaymentFlow(any())).thenReturn(null);
        boolean actualResult = applePayService.iosOrderHistory(orderInfo,OrderDO.builder().orderCancel(0).subType(1).build(),"");
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    @DisplayName("ios验证历史结果-历史账单list-退订订单")
    public void test_iosOrderHistory_verifyResult_refundOrder(){
        String orderInfo = this.initVerifyResult(0,true,true,false);
        boolean exceptedResult = true;
        when(paymentService.queryPaymentFlow(any())).thenReturn(new PaymentFlow());
        doNothing().when(paymentService).updateRefundInfo(any());

        OrderDO orderDO = new OrderDO();
        orderDO.setOrderCancel(0);
        orderDO.setTimeEnd((int)Instant.now().getEpochSecond());
        orderDO.setSubType(1);
        boolean actualResult = applePayService.iosOrderHistory(orderInfo,orderDO,"");
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    @DisplayName("ios验证历史结果-历史账单list-退订订单")
    public void test_iosOrderHistory_verifyResult_freeTrial(){
        String orderInfo = this.initVerifyResult(0,true,false,true);
        boolean exceptedResult = true;
        when(paymentService.queryPaymentFlow(any())).thenReturn(new PaymentFlow());
        doNothing().when(paymentService).updateRefundInfo(any());

        OrderDO orderDO = new OrderDO();
        orderDO.setOrderCancel(0);
        orderDO.setTimeEnd((int)Instant.now().getEpochSecond());
        orderDO.setSubType(1);
        boolean actualResult = applePayService.iosOrderHistory(orderInfo,orderDO,"");
        Assert.assertEquals(exceptedResult,actualResult);
    }

    @Test
    @DisplayName("ios验证历史结果-历史账单list-续订状态")
    public void test_iosOrderHistory_verifyResult_renewal(){
        String orderInfo = this.initVerifyRenewal();
        boolean exceptedResult = true;
        when(paymentService.queryPaymentFlow(any())).thenReturn(new PaymentFlow());
        doNothing().when(paymentService).updateRefundInfo(any());

        when(iOrderDAO.updateOrderCancelTime(any())).thenReturn(1);

        OrderDO orderDO = new OrderDO();
        orderDO.setOrderCancel(0);
        orderDO.setTradeNo("original_transaction_id");
        orderDO.setTimeEnd((int)Instant.now().getEpochSecond());
        orderDO.setSubType(1);
        boolean actualResult = applePayService.iosOrderHistory(orderInfo,orderDO,"");
        Assert.assertEquals(exceptedResult,actualResult);
    }

    private OrderDO initIosOrderDO(){
        OrderDO orderDO = new OrderDO();
        JSONObject obj = new JSONObject();
        obj.put("productId",1);
        orderDO.setExtend(obj.toJSONString());

        orderDO.setTenantId(TENANTID_VICOO);

        return orderDO;
    }


    private Map<String, PaymentConfig> initPaymentConfig(){
        PaymentConfig paymentConfig = new PaymentConfig();
        paymentConfig.setApplePassword("");
        paymentConfig.setAppleBundleId("");
        Map<String,PaymentConfig> result = Maps.newHashMap();
        result.put(TENANTID_VICOO,paymentConfig);

        return result;
    }

    /**
     * 初始化订单验证结果
     * @return
     */
    private String initVerifyResult(int status,boolean hasList,boolean refund,boolean freeTrial){
        JSONObject obj = new JSONObject();
        obj.put("status",status);


        JSONArray array = new JSONArray();
        if(hasList){
            JSONObject object = new JSONObject();
            object.put("transaction_id","590001157379254");
            object.put("expires_date_ms",1375340400000L);

            if(freeTrial){
                object.put("is_trial_period",true);
            }
            if(refund){
                object.put("cancellation_date_ms",System.currentTimeMillis());
            }
            array.add(object);
        }
        obj.put("latest_receipt_info",array);
        return obj.toJSONString();
    }

    private String initVerifyRenewal(){
        JSONObject obj = new JSONObject();
        obj.put("status",0);


        JSONArray array = new JSONArray();
        obj.put("latest_receipt_info",array);

        JSONArray renewal = new JSONArray();
        JSONObject re = new JSONObject();
        re.put("original_transaction_id","original_transaction_id");
        re.put("auto_renew_status",0);
        re.put("expiration_intent",1);
        renewal.add(re);
        obj.put("pending_renewal_info",renewal);

        return obj.toJSONString();
    }


    @Test
    public void test_queryPurchasedProductId(){
        JSONArray array = new JSONArray();
        JSONObject obj = new JSONObject();
        long expires_date_ms = System.currentTimeMillis() + 100 * 1000;
        obj.put("transaction_id","transaction_id");
        obj.put("expires_date_ms",expires_date_ms);
        obj.put("product_id",20001);
        obj.put("is_trial_period",true);
        obj.put("original_transaction_id","original_transaction_id");
        obj.put("purchase_date_ms",1674963027000L);
        obj.put("purchase_date_pst","2023-01-28 19:30:27 America/Los_Angeles");
        obj.put("purchase_date","2023-01-29 03:30:27 Etc/GMT");
        array.add(obj);

        when(paymentService.queryExistPaymentBatch(any())).thenReturn(Sets.newHashSet());

        OrderVerifyResultDO actualResult = applePayService.queryPurchasedProductId(array,new ApplePaymentRequest());
        Assert.assertTrue(actualResult.getVerify());
    }


    @Test
    @DisplayName("解析订单中指定的设备list-有设备")
    public void testQueryUserTierDeviceRequestWithValidInput() {
        String extend = "{\"tierDeviceList\":[\"iPhone 12\",\"iPad Pro\"]}";
        List<String> expectedOutput = Arrays.asList("iPhone 12", "iPad Pro");
        List<String> actualOutput = applePayService.queryUserTierDeviceRequest(extend);
        Assert.assertEquals(expectedOutput, actualOutput);
    }

    @Test
    @DisplayName("解析订单中指定的设备list-无设备")
    public void testQueryUserTierDeviceRequestWithEmptyInput() {
        String extend = "";
        List<String> expectedOutput = Lists.newArrayList();
        List<String> actualOutput = applePayService.queryUserTierDeviceRequest(extend);
        Assert.assertEquals(expectedOutput, actualOutput);
    }


    @Test
    public void test_queryAppleOrderExpireTime(){
        ApplePaymentRequest receipt = new ApplePaymentRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        receipt.setApp(app);
        receipt.setReceiptData("data");

        Map<String,PaymentConfig> paymentConfigMap = Maps.newHashMap();
        PaymentConfig paymentConfig = new PaymentConfig();
        paymentConfig.setApplePassword("test");
        paymentConfigMap.put(TENANTID_VICOO,paymentConfig);

        when(paymentCenterConfig.getConfig()).thenReturn(paymentConfigMap);


        Integer actualResult = applePayService.queryAppleOrderExpireTime(receipt,"");
        Assert.assertNull(actualResult);
    }
}
