// package com.addx.iotcamera.service;

// import org.addx.iot.common.vo.Result;
// import org.addx.iot.config.entity.DeviceSupport;
// import com.addx.iotcamera.bean.device.model.DeviceModelBatteryDO;
// import com.addx.iotcamera.bean.domain.FirmwareViewDO;
// import com.addx.iotcamera.bean.domain.User;
// import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
// import com.addx.iotcamera.bean.openapi.OpenApiDeviceConfig;
// import com.addx.iotcamera.bean.openapi.TenantAwsConfig;
// import com.addx.iotcamera.bean.tuple.Tuple2;
// import com.addx.iotcamera.bean.video.StorageDest;
// import com.addx.iotcamera.config.*;
// import com.addx.iotcamera.config.apollo.DnsServerConfig;
// import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
// import com.addx.iotcamera.dynamo.dao.DeviceConfigDAO;
// import com.addx.iotcamera.dynamo.dao.TenantAwsConfigDAO;
// import com.addx.iotcamera.helper.ConfigHelper;
// import com.addx.iotcamera.helper.aws.AwsHelper;
// import com.addx.iotcamera.mqtt.MqttSender;
// import com.addx.iotcamera.service.device.DeviceService;
// import com.addx.iotcamera.service.device.DeviceSettingService;
// import com.addx.iotcamera.service.device.DeviceStatusService;
// import com.addx.iotcamera.service.device.DeviceSupportService;
// import com.addx.iotcamera.service.firmware.FirmwareService;
// import com.addx.iotcamera.service.openapi.OpenApiConfigService;
// import com.addx.iotcamera.service.openapi.PaasAccessLogService;
// import com.addx.iotcamera.service.video.StorageAllocateService;
// import com.addx.iotcamera.util.OpenApiUtil;
// import com.alibaba.fastjson.JSON;
// import com.alibaba.fastjson.JSONObject;
// import com.amazonaws.services.s3.AmazonS3;
// import lombok.SneakyThrows;
// import lombok.extern.slf4j.Slf4j;
// import org.junit.Assert;
// import org.junit.Before;
// import org.junit.Test;
// import org.junit.jupiter.api.DisplayName;
// import org.junit.runner.RunWith;
// import org.mockito.AdditionalAnswers;
// import org.mockito.InjectMocks;
// import org.mockito.Mock;
// import org.mockito.junit.MockitoJUnitRunner;
// import org.springframework.integration.support.locks.ExpirableLockRegistry;
// import org.xbill.DNS.Address;

// import java.net.InetAddress;
// import java.net.URL;
// import java.util.*;
// import java.util.concurrent.ForkJoinPool;
// import java.util.concurrent.locks.Lock;
// import java.util.concurrent.locks.ReentrantLock;

// import static org.mockito.Mockito.*;

// @Slf4j
// @RunWith(MockitoJUnitRunner.Silent.class)
// public class OpenApiConfigServiceV2Test {

//     @InjectMocks
//     private OpenApiConfigService openApiConfigService;
//     @Mock
//     private StorageAllocateService storageAllocateService;
//     @Mock
//     private PaasTenantConfig paasTenantConfig;
//     @Mock
//     private DeviceService deviceService;
//     @Mock
//     private RedisService redisService;
//     @Mock
//     private MqttSender mqttSender;
//     @Mock
//     private UserRoleService userRoleService;
//     @Mock
//     private UserService userService;
//     @Mock
//     private DnsServerConfig dnsServerConfig;
//     @Mock
//     private VideoSliceConfig videoSliceConfig;
//     @Mock
//     private S3Config s3Config;
//     @Mock
//     private DeviceSettingService deviceSettingService;
//     @Mock
//     private PaasAccessLogService paasAccessLogService;
//     @Mock
//     private DeviceConfigService deviceConfigService;
//     @Mock
//     private DeviceConfigDAO deviceConfigDAO;
//     @Mock
//     private TenantAwsConfigDAO tenantAwsConfigDAO;
//     @Mock
//     private GlobalDeviceConfig globalDeviceConfig;
//     @Mock
//     private DeviceInfoService deviceInfoService;
//     @Mock
//     private S3 s3;
//     @Mock
//     private UserVipService userVipService;
//     @Mock
//     private VipService paasVipService;
//     @Mock
//     private DeviceStatusService deviceStatusService;
//     @Mock
//     private DeviceSupportService deviceSupportService;
//     @Mock
//     private FirmwareService firmwareService;
//     @Mock
//     private GcsOptions gcsOptions;
//     @Mock
//     private AmazonS3 s3Client;
//     @Mock
//     private ExpirableLockRegistry redisLockRegistry;

//     private String tenantId = "mytenant";
//     private String bucket = "mybucket";
//     private String sn = OpenApiUtil.shortUUID();
//     private String configId = OpenApiUtil.shortUUID();

//     private OpenApiDeviceConfig deviceConfig;

//     @Before
//     public void init() {
//         when(dnsServerConfig.getConfig()).thenReturn(Arrays.asList("***************"));
//         PaasTenantConfigTest.mockPaasTenantConfig(this.paasTenantConfig);
//         deviceConfig = ConfigHelper.loadYmlConfig("classpath:openapi/device_config/paas_owned2.json")
//                 .toJavaObject(OpenApiDeviceConfig.class);
//         deviceConfig.setTenantId(tenantId);

//         TenantAwsConfig tenantAwsConfig = ConfigHelper.loadYmlConfig("classpath:openapi/tenant_aws_config/paas_owned.yml")
//                 .toJavaObject(TenantAwsConfig.class);
//         tenantAwsConfig.setTenantId(tenantId);
//         when(tenantAwsConfigDAO.queryByTenantId(anyString())).thenReturn(tenantAwsConfig);
//         when(deviceConfigService.getVideoRollingDays(anyString(), any(), anyString()))
//                 .thenReturn(new Tuple2<>(30, (long) 30 * (3600 * 24)));
//         when(s3.getBucket()).thenReturn("bucket1");
//         when(s3.getDefaultBucket()).thenReturn("defaultBucket1");
//         when(s3Config.getClientRegion()).thenReturn("region1");
//         when(s3Config.getS3AccessKey()).thenReturn("s3Ak1");
//         when(s3Config.getS3SecretKey()).thenReturn("s3Sk2");
//         when(s3Config.getServerS3AccelerateEnable()).thenReturn(false);
//         openApiConfigService.setPool(new ForkJoinPool(2));
//         openApiConfigService.init();

//         when(deviceStatusService.queryDeviceStatusBySerialNumber(any())).thenReturn(new DeviceStatusDO());

//         when(storageAllocateService.getVipStorageDest(any(), anyString(), any(), any())).thenReturn(new StorageDest());
//         when(storageAllocateService.getNoVipStorageDest(any(), anyString(), any(), any())).thenReturn(new StorageDest());
//     }

//     @Test
//     public void test_createDeviceConfigs() {
//         when(userVipService.isNoUserTier(any())).thenReturn(true);
//         when(paasVipService.isVipDevice(any(),anyString())).thenReturn(false);
//         {
//             when(deviceConfigDAO.queryIdBySnAndTenantId(sn, tenantId)).thenReturn(Arrays.asList(configId));
//             Result result = openApiConfigService.createDeviceConfigs(tenantId, sn, () -> configId, deviceConfig);
//             Assert.assertEquals(new Integer(301), result.getResult());
//         }
//         when(deviceConfigDAO.queryIdBySnAndTenantId(sn, tenantId)).thenReturn(Arrays.asList());
//         {
//             when(redisService.setIfAbsent(anyString(), eq(configId), anyLong(), any())).thenReturn(false);
//             Result result = openApiConfigService.createDeviceConfigs(tenantId, sn, () -> configId, deviceConfig);
//             Assert.assertEquals(new Integer(301), result.getResult());
//         }
//         when(redisService.setIfAbsent(anyString(), eq(configId), anyLong(), any())).thenReturn(true);
//         {
//             when(deviceConfigDAO.save(deviceConfig)).thenReturn(0);
//             Result result = openApiConfigService.createDeviceConfigs(tenantId, sn, () -> configId, deviceConfig);
//             Assert.assertEquals(new Integer(-1), result.getResult());
//         }
//         when(deviceConfigDAO.save(deviceConfig)).thenReturn(1);
//         {
//             when(userRoleService.getDeviceAdminUser(sn)).thenReturn(0);
//             Result result = openApiConfigService.createDeviceConfigs(tenantId, sn, () -> configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         when(userRoleService.getDeviceAdminUser(sn)).thenReturn(12345);
//         {
//             when(deviceInfoService.getDeviceSupport(sn)).thenReturn(new DeviceSupport(){{
//                 setSupportPirSliceReport(0);
//             }});
//             Result result = openApiConfigService.createDeviceConfigs(tenantId, sn, () -> configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         when(deviceInfoService.getDeviceSupport(sn)).thenReturn(new DeviceSupport(){{
//             setSupportPirSliceReport(1);
//         }});
//         {
//             Result result = openApiConfigService.createDeviceConfigs(tenantId, sn, () -> configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         {
//             when(userVipService.isNoUserTier(any())).thenReturn(false);
//             Result result = openApiConfigService.createDeviceConfigs(tenantId, sn, () -> configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//     }

//     @Test
//     public void test_updateDeviceConfigs() {
//         when(userVipService.isNoUserTier(any())).thenReturn(true);
//         when(paasVipService.isVipDevice(any(),anyString())).thenReturn(false);
//         {
//             when(deviceConfigDAO.queryBySnAndConfigIdAndTenantId(sn, configId, tenantId)).thenReturn(null);
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(302), result.getResult());
//         }
//         when(deviceConfigDAO.queryBySnAndConfigIdAndTenantId(sn, configId, tenantId)).thenReturn(deviceConfig);
//         {
//             when(deviceConfigDAO.update(any())).thenReturn(null);
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(-1), result.getResult());
//         }
//         when(deviceConfigDAO.update(any())).thenReturn(deviceConfig);
//         {
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         when(deviceConfigDAO.save(deviceConfig)).thenReturn(1);
//         {
//             when(userRoleService.getDeviceAdminUser(sn)).thenReturn(0);
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         when(userRoleService.getDeviceAdminUser(sn)).thenReturn(12345);
//         {
//             when(deviceInfoService.getDeviceSupport(sn)).thenReturn(new DeviceSupport(){{
//                 setSupportPirSliceReport(0);
//             }});
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         when(deviceInfoService.getDeviceSupport(sn)).thenReturn(new DeviceSupport(){{
//             setSupportPirSliceReport(1);
//         }});
//         {
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         {
//             when(userVipService.isNoUserTier(any())).thenReturn(false);
//             Result result = openApiConfigService.createDeviceConfigs(tenantId, sn, () -> configId, deviceConfig);
//             Assert.assertEquals(new Integer(301), result.getResult());
//         }
//     }

//     @Test
//     public void test_updateDeviceConfigs_netvue() {
//         String tenantId = "netvue";
//         when(userVipService.isNoUserTier(any())).thenReturn(true);
//         when(paasVipService.isVipDevice(any(), anyString())).thenReturn(false);
//         {
//             when(deviceConfigDAO.queryBySnAndConfigIdAndTenantId(sn, configId, tenantId)).thenReturn(null);
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(302), result.getResult());
//         }
//         when(deviceConfigDAO.queryBySnAndConfigIdAndTenantId(sn, configId, tenantId)).thenReturn(deviceConfig);
//         {
//             when(deviceConfigDAO.update(any())).thenReturn(null);
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(-1), result.getResult());
//         }
//         when(deviceConfigDAO.update(any())).thenReturn(deviceConfig);
//         {
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         when(deviceConfigDAO.save(deviceConfig)).thenReturn(1);
//         {
//             when(userRoleService.getDeviceAdminUser(sn)).thenReturn(0);
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         when(userRoleService.getDeviceAdminUser(sn)).thenReturn(12345);
//         {
//             when(deviceInfoService.getDeviceSupport(sn)).thenReturn(new DeviceSupport(){{
//                 setSupportPirSliceReport(0);
//             }});
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         when(deviceInfoService.getDeviceSupport(sn)).thenReturn(new DeviceSupport(){{
//             setSupportPirSliceReport(1);
//         }});
//         {
//             Result result = openApiConfigService.updateDeviceConfigs(tenantId, sn, configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//         when(redisService.setIfAbsent(anyString(), eq(configId), anyLong(), any())).thenReturn(true);
//         {
//             when(userVipService.isNoUserTier(any())).thenReturn(false);
//             Result result = openApiConfigService.createDeviceConfigs(tenantId, sn, () -> configId, deviceConfig);
//             Assert.assertEquals(new Integer(0), result.getResult());
//         }
//     }

//     @Test
//     public void test_buildPaasOwnedDeviceConfig() {
//         OpenApiDeviceConfig config = openApiConfigService.buildPaasOwnedDeviceConfig("sn1");
//         Assert.assertNotNull(config);
//     }

//     @Test
//     @SneakyThrows
//     public void test_dnsJava() {
//         {
//             InetAddress addr = Address.getByName("api-stage.addx.live");
//             log.info("addr:{}", addr.getHostAddress());
//         }
//         {
//             InetAddress addr = Address.getByName("a4x-prod-us-vip-pro.s3.amazonaws.com");
//             log.info("addr:{}", addr.getHostAddress());
//         }
//     }

// //    @Test
// //    public void test_getHost2Ip_us() { // us
// //        String configJson = "{\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"133bf29da03083dcf170b50aefa893dc\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-prod-us-vip-basic\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"a4x-prod-us-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":15,\"region\":\"us-east-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/133bf29da03083dcf170b50aefa893dc/${traceId}/image.${imgType}\",\"rootPath\":\"https://a4x-prod-us-vip-basic.s3.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-prod-us-vip-none.s3.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1920x1080\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-prod-us-vip-basic\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"a4x-prod-us-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":15,\"region\":\"us-east-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/133bf29da03083dcf170b50aefa893dc/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://a4x-prod-us-vip-basic.s3.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-prod-us-vip-none.s3.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1920x1080\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-us.vicohome.io\"},\"mqtt\":{\"uri\":\"tcp://vmq-us.addx.live\",\"ip\":[\"*************\",\"**************\"]},\"ai\":{\"enable\":false}}";
// //        JSONObject configMessage = (JSONObject) JSON.parse(configJson);
// //        Map<String, List<String>> host2Ip = openApiConfigService.getHost2IpFromConfig(configMessage);
// //        log.info("test_getHost2Ip us :{}", JSON.toJSONString(host2Ip, true));
// //        Set<String> expectHosts = new HashSet(Arrays.asList("api-us.vicohome.io", "vmq-us.addx.live"
// //                , "a4x-prod-us-vip-none.s3.amazonaws.com", "a4x-prod-us-vip-basic.s3.amazonaws.com"));
// //        Assert.assertEquals(expectHosts, host2Ip.keySet());
// //    }

// //    @Test
// //    public void test_getHost2Ip_cn() { // cn
// //        String configJson = "{\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"86d7d9b8668469bba5f7af2a73185cd0\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn-vip-none\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"addx-prod-cn-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/86d7d9b8668469bba5f7af2a73185cd0/${traceId}/image.${imgType}\",\"rootPath\":\"https://addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn-vip-none\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"addx-prod-cn-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/86d7d9b8668469bba5f7af2a73185cd0/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api.addx.live\"},\"mqtt\":{\"uri\":\"tcp://vernemq-cn.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
// //        JSONObject configMessage = (JSONObject) JSON.parse(configJson);
// //        Map<String, List<String>> host2Ip = openApiConfigService.getHost2IpFromConfig(configMessage);
// //        log.info("test_getHost2Ip cn :{}", JSON.toJSONString(host2Ip, true));
// //        Set<String> expectHosts = new HashSet(Arrays.asList("api.addx.live", "vernemq-cn.addx.live"
// //                , "addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn"));
// //        Assert.assertEquals(expectHosts, host2Ip.keySet());
// //    }

//     @Test
//     public void test_getHost2Ip_parse_Uri_fail() { // 解析uri失败
//         String configJson = "{\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"86d7d9b8668469bba5f7af2a73185cd0\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn-vip-none\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"addx-prod-cn-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/86d7d9b8668469bba5f7af2a73185cd0/${traceId}/image.${imgType}\",\"rootPath\":\"httpss://addx-prod-cn-vip-?none.s3.cn-north-1?.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"httpss://addx-prod-cn-vip-?none.s3.cn-north-1?.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn-vip-none\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"addx-prod-cn-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/86d7d9b8668469bba5f7af2a73185cd0/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://addx-prod-cn-vip-none.s3?.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-prod-cn-vip-none.s3?.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api.addx.live\"},\"mqtt\":{\"uri\":\"tcp://vernemq-cn.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
//         JSONObject configMessage = (JSONObject) JSON.parse(configJson);
//         Map<String, List<String>> host2Ip = openApiConfigService.getHost2IpFromConfig(configMessage);
//         log.info("test_getHost2Ip cn :{}", JSON.toJSONString(host2Ip, true));
//         Set<String> expectHosts = new HashSet(Arrays.asList("api.addx.live", "vernemq-cn.addx.live"));
//         Assert.assertEquals(expectHosts, host2Ip.keySet());
//     }

// //    @Test
// //    public void test_getHost2Ip_get_ip_fail() { // 获取ip失败
// //        String configJson = "{\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"86d7d9b8668469bba5f7af2a73185cd0\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn-vip-none\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"addx-prod-cn-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/86d7d9b8668469bba5f7af2a73185cd0/${traceId}/image.${imgType}\",\"rootPath\":\"https://addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn-vip-none\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"addx-prod-cn-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/86d7d9b8668469bba5f7af2a73185cd0/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://addx-prod-cn-vip-none.s333.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-prod-cn-vip-none.s333.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api.addx.live\"},\"mqtt\":{\"uri\":\"tcp://vernemq-cn.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
// //        JSONObject configMessage = (JSONObject) JSON.parse(configJson);
// //        Map<String, List<String>> host2Ip = openApiConfigService.getHost2IpFromConfig(configMessage);
// //        log.info("test_getHost2Ip cn :{}", JSON.toJSONString(host2Ip, true));
// //        Set<String> expectHosts = new HashSet(Arrays.asList("api.addx.live", "vernemq-cn.addx.live"
// //                , "addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn"));
// //        Assert.assertEquals(expectHosts, host2Ip.keySet());
// //    }

//     @Test
//     public void test_DeviceModelBatteryDO_getVersion() {
//         // 'BK5100','1','5100','https://addx-device-config.s3.amazonaws.com/battery/2021/1629894634_CW2007_BK5100.txt'
//         DeviceModelBatteryDO model = new DeviceModelBatteryDO()
//                 .setBatteryCode("BK5100")
//                 .setVoltameterType(1)
//                 .setBattery(5100);
//         Assert.assertEquals((Integer) 0, model.getVersion());
//         model.setUrl("https://addx-device-config.s3.amazonaws.com/battery/2021/16298_CW2007_BK5100.txt");
//         Assert.assertEquals((Integer) 0, model.getVersion());
//         model.setUrl("https://addx-device-config.s3.amazonaws.com/battery/2021/1629894634_CW2007_BK5100.txt");
//         Assert.assertEquals((Integer) 1629894634, model.getVersion());
//         JSONObject jsonObj = (JSONObject) JSON.toJSON(model);
//         Assert.assertEquals(model.getVersion(), jsonObj.getInteger("version"));
//     }

//     @Test
//     @SneakyThrows
//     public void test_inflateConfigMessage() {
//         TenantAwsConfig tenantAwsConfig = ConfigHelper.loadYmlConfig("classpath:openapi/tenant_aws_config/paas_owned.yml")
//                 .toJavaObject(TenantAwsConfig.class);
//         AwsHelper awsHelper = new AwsHelper(tenantAwsConfig) {
//             @Override
//             public AmazonS3 s3Client() {
//                 return s3Client;
//             }
//         };
//         when(s3Client.getUrl(any(), any())).thenReturn(new URL("https://addx/image"));
//         when(userVipService.queryFreeTierLookBackDays(any(), any())).thenReturn(7);

//         String sn = OpenApiUtil.shortUUID();
//         OpenApiConfigService.DeviceConfigArgs args = new OpenApiConfigService.DeviceConfigArgs(123, "vicoo", sn);
//         JSONObject config = new JSONObject().fluentPut("serialNumber", sn);
//         {
//             openApiConfigService.inflateConfigMessage(awsHelper, args, config);
//         }
//         {
//             when(deviceInfoService.getDeviceSupport(sn)).thenReturn(new DeviceSupport() {{
//                 setSupportGoogleStorage(true);
//                 setSupportCos(true);
//                 setSupportOci(true);
//                 setSupportPirSliceReport(1);
//             }});
//             openApiConfigService.inflateConfigMessage(awsHelper, args, config);
//         }
//         /*
//         {
//             when(gcsOptions.isEnableGoogleStore(eq(sn), any())).thenReturn(false);
//             openApiConfigService.inflateConfigMessage(awsHelper, args, config);
//         }
//         when(gcsOptions.isEnableGoogleStore(eq(sn), any())).thenReturn(true);
//         {
//             when(gcsOptions.getBucketByLookBackDays(anyInt())).thenReturn(null);
//             openApiConfigService.inflateConfigMessage(awsHelper, args, config);
//         }
//         */
//         {
//             when(gcsOptions.getBucketByLookBackDays(anyInt())).thenReturn("addx-60d");
//             openApiConfigService.inflateConfigMessage(awsHelper, args, config);
//         }
//     }

//     @Test
//     public void test_publishDeviceConfig() {
//         when(redisLockRegistry.obtain(any())).thenAnswer(it -> new ReentrantLock(false));
//         {
//             final Result<JSONObject> result = openApiConfigService.publishDeviceConfig("", null);
//             Assert.assertNotEquals(result.getResult(), Result.successFlag);
//         }
//         String sn = OpenApiUtil.shortUUID();
//         {
//             when(userRoleService.getDeviceAdminUser(sn)).thenReturn(0);
//             final Result<JSONObject> result = openApiConfigService.publishDeviceConfig(sn, null);
//             Assert.assertNotEquals(result.getResult(), Result.successFlag);
//         }
//         int adminId = (int) System.currentTimeMillis();
//         when(userRoleService.getDeviceAdminUser(sn)).thenReturn(adminId);
//         {
//             final Result<JSONObject> result = openApiConfigService.publishDeviceConfig(sn, null);
//             Assert.assertNotEquals(result.getResult(), Result.successFlag);
//         }
//         {
//             final Result<JSONObject> result = openApiConfigService.publishDeviceConfig(sn, adminId);
//             Assert.assertNotEquals(result.getResult(), Result.successFlag);
//         }
//         {
//             final int adminId2 = adminId - 100;
//             when(userService.queryUserById(adminId2)).thenThrow(new RuntimeException("mock"));
//             final Result<JSONObject> result = openApiConfigService.publishDeviceConfig(sn, adminId2);
//             Assert.assertNotEquals(result.getResult(), Result.successFlag);
//             final Result result2 = openApiConfigService.publishDeviceConfigAsync(sn, null, adminId2);
//             Assert.assertNotEquals(result2.getResult(), Result.successFlag);
//         }
//         when(userService.queryUserById(adminId)).thenReturn(new User() {{
//             setId(adminId);
//             setTenantId("guard");
//         }});
//         {
//             final Result<JSONObject> result = openApiConfigService.publishDeviceConfig(sn, null);
//             Assert.assertEquals(result.getResult(), Result.successFlag);
//             final Result result2 = openApiConfigService.publishDeviceConfigAsync(sn, null, adminId);
//             Assert.assertEquals(result2.getResult(), Result.successFlag);
//         }

//     }

//     @Mock
//     private Lock timeoutLock;
//     @Test
//     @DisplayName("发布设备config，获取锁超时")
//     @SneakyThrows
//     public void test_publishDeviceConfig_get_lock_timeout() {
//         final String sn = OpenApiUtil.shortUUID();
//         when(redisLockRegistry.obtain(anyString())).thenReturn(timeoutLock);
//         when(timeoutLock.tryLock(anyLong(), any())).thenReturn(false);
//         final Result<JSONObject> result = openApiConfigService.publishDeviceConfig(sn, null);
//         Assert.assertNotEquals(Result.successFlag, result.getResult());
//         Assert.assertEquals("get lock timeout!", result.getMsg());
//     }

//     @Mock
//     private Lock interruptLock;
//     @Test
//     @DisplayName("发布设备config，获取锁被中断")
//     @SneakyThrows
//     public void test_publishDeviceConfig_get_lock_interrupt() {
//         final String sn = OpenApiUtil.shortUUID();
//         when(redisLockRegistry.obtain(anyString())).thenReturn(interruptLock);
//         when(interruptLock.tryLock(anyLong(), any())).thenThrow(new InterruptedException());
//         final Result<JSONObject> result = openApiConfigService.publishDeviceConfig(sn, null);
//         Assert.assertNotEquals(Result.successFlag, result.getResult());
//         Assert.assertEquals("get lock interrupted!", result.getMsg());
//     }
// }
