package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.device.attributes.OptionEnumMapping;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.service.device.DeviceEnumMappingService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.addx.iotcamera.service.device.DeviceSupportService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSet;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.model.DeviceAttributeIntRange;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.addx.iotcamera.dao.device.DeviceSupportDAOHelper.*;
import static org.addx.iot.common.enums.PirServiceName.*;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LocalDeviceSupportTest {

    @Mock
    private RedisService redisService;
    @Mock
    private DeviceSupportService deviceSupportService;
    @Mock
    private DeviceStatusService deviceStatusService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private DeviceModelConfigService deviceModelConfigService;
    @Mock
    private DeviceEnumMappingService deviceEnumMappingService;
    @Mock
    private DeviceModelService deviceModelService;
    @Mock
    private UserRoleService userRoleService;
    @InjectMocks
    private DeviceInfoService deviceInfoService;
    @Mock
    private TenantSettingService tenantSettingService;

    @Mock
    FactoryDataQueryService factoryDataQueryService;

    @Before
    public void before() {
        deviceInfoService.setGson(new Gson());

        when(deviceModelService.queryDeviceModelCategory(anyString())).thenReturn(DeviceModelCategoryEnums.DOORBELL.getCode());
        final OptionEnumMapping enumMapping = new OptionEnumMapping();
        when(deviceEnumMappingService.getEnumMappingByModelNo(anyString())).thenReturn(enumMapping);

        when(userRoleService.getAdminUserBySn(anyString())).thenAnswer(it -> {
            User user = new User().setId(123).setTenantId("xsense");
            return new Result<>(user);
        });
        when(tenantSettingService.getTenantSettingByTenantId(anyString())).thenAnswer(it -> {
            return new TenantSetting().setTenantId(it.getArgument(0))
                    .setPirRecordTimeDisabledOptions(new LinkedHashSet<>())
                    .setPirCooldownTimeDisabledOptions(new LinkedHashSet<>());
        });
    }

    public static CloudDeviceSupport getDefaultDeviceSupport() {
        return getDefaultDeviceSupport(false, 0, false);
    }

    public static CloudDeviceSupport getDefaultDeviceSupport(boolean supportWhiteLight, int supportDevicePersonDetect, boolean isDoorBell) {
        return new CloudDeviceSupport() {
            {
                setSupportWhiteLight(supportWhiteLight);
                setSupportPirSliceReport(0);
                setQuantityCharge(0);
                setAntiflickerSupport(0);
                setSupportStreamProtocol("");
                setSupportAudioCodectype("");
                setSupportKeepAliveProtocol("");
                setSupportCanStandby(0);
                setSupportDevicePersonDetect(supportDevicePersonDetect);
                setCanRotate(0);
                setSupportMotionTrack(0);
                setSupportFrequency(0);
                setAntiDisassemblyAlarm(0);
//            setMechanicalDingDongDurationRange(DeviceAttributeIntRange.DEFAULT_INT_RANGE);
//            setAlarmVolumeRange(DeviceAttributeIntRange.DEFAULT_INT_RANGE);
//            setVoiceVolumeRange(DeviceAttributeIntRange.DEFAULT_INT_RANGE);

                setSupportLocalVideoLookBack(false);
                setLocalVideoStorageType(0);
                setSupportSdCardFormat(false);
                setSupportNightVisionSwitch(true);
                setSupportAlarmFlashLight(supportWhiteLight);
                setNightVisionModeOptions(supportWhiteLight ? Arrays.asList("infrared", "white") : Arrays.asList("infrared"));
                setSupportStarlightSensor(false);  // 默认值不写入数据库
                setSupportUnlimitedWebsocket(false);  // 默认值不写入数据库
                setLiveStreamCodecOptions(Arrays.asList("h265"));
                setVideoResolutionOptions(Arrays.asList("mid", "high"));
                setMechanicalDingDongDurationRange(new DeviceAttributeIntRange(200, 1000, 100));
                setAlarmVolumeRange(new DeviceAttributeIntRange(10, 100, 1));
                setVoiceVolumeRange(new DeviceAttributeIntRange(0, 100, 1));
                setLiveSpeakerVolumeRange(new DeviceAttributeIntRange(0, 100, 1));
                setSupportDoorbellPressNotifySwitch(isDoorBell);
                setSupportIndoor(isDoorBell);
                setSupportMagicPix(false);
                setSupportPirRecordTime(true);
            }

        };
    }

    @Test
    public void test_getDeviceSupport() {
        String sn = OpenApiUtil.shortUUID();
        String deviceSupportKey = "deviceSupport:" + sn;
        String supportPirSliceReportKey = "deviceSupportPirSliceReport:" + sn;
        {
            final CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(sn);
            Assert.assertNotNull(cloudDeviceSupport);
//            AssertUtil.assertEquals(getDefaultDeviceSupport(), deviceSupport);
//            Assert.assertEquals(getDefaultDeviceSupport(), deviceSupport);
            AssertUtil.assertEquals(getDefaultDeviceSupport(), cloudDeviceSupport);
        }
        {
            when(redisService.getFromSlave(deviceSupportKey)).thenReturn(JSON.toJSONString(new CloudDeviceSupport()));
            final CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(sn);
            Assert.assertNotNull(cloudDeviceSupport);
//            AssertUtil.assertEquals(getDefaultDeviceSupport(), deviceSupport);
            Assert.assertEquals(getDefaultDeviceSupport(), cloudDeviceSupport);
        }
        {
            when(redisService.getFromSlave(deviceSupportKey)).thenReturn(JSON.toJSONString(new CloudDeviceSupport()));
            when(redisService.getFromSlave(supportPirSliceReportKey)).thenReturn(null);
            final CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(sn);
            Assert.assertEquals(getDefaultDeviceSupport(), cloudDeviceSupport);
        }
        {
            when(redisService.getFromSlave(deviceSupportKey)).thenReturn(JSON.toJSONString(new CloudDeviceSupport() {{
                setSupportPirSliceReport(1);
            }}));
            when(redisService.getFromSlave(supportPirSliceReportKey)).thenReturn("1");
            final CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(sn);
            final CloudDeviceSupport expectCloudDeviceSupport = getDefaultDeviceSupport();
            expectCloudDeviceSupport.setSupportPirSliceReport(1);
            Assert.assertEquals(expectCloudDeviceSupport, cloudDeviceSupport);
        }
    }

    @DisplayName("校验deviceSupport的null字段是否会跟随deviceModel修改")
    @Test
    public void test_getDeviceSupport_nullField_follow_deviceModel() {
        nullField_follow_deviceMode(it -> it.setStreamProtocol("abc"), it -> it.setSupportStreamProtocol("abc"));
        nullField_follow_deviceMode(it -> it.setAudioCodectype("def"), it -> it.setSupportAudioCodectype("def"));
        nullField_follow_deviceMode(it -> it.setKeepAliveProtocol("hij"), it -> it.setSupportKeepAliveProtocol("hij"));
        nullField_follow_deviceMode(it -> it.setCanStandby(true), it -> it.setSupportCanStandby(1));
        nullField_follow_deviceMode(it -> it.setDevicePersonDetect(true), it -> it.setSupportDevicePersonDetect(1));
        nullField_follow_deviceMode(it -> it.setCanRotate(true), it -> it.setCanRotate(1));
        nullField_follow_deviceMode(it -> it.setSupportMotionTrack(true), it -> it.setSupportMotionTrack(1));
        nullField_follow_deviceMode(it -> it.setSupportFrequency(true), it -> it.setSupportFrequency(1));
        nullField_follow_deviceMode(it -> it.setAntiDisassemblyAlarm(true), it -> it.setAntiDisassemblyAlarm(1));
    }

    @DisplayName("校验deviceSupport的null字段是否会跟随deviceStatus修改")
    @Test
    public void test_getDeviceSupport_nullField_follow_deviceStatus() {
        nullField_follow_deviceStatus(it -> it.getQuantityCharge(), it -> it.getQuantityCharge());
        nullField_follow_deviceStatus(it -> it.getAntiflicker(), it -> it.getAntiflickerSupport());
    }

    private void nullField_follow_deviceMode(Consumer<DeviceModel> handleDeviceModel, Consumer<CloudDeviceSupport> handleDeviceSupport) {
        final String sn = OpenApiUtil.shortUUID();
        final String modelNo = OpenApiUtil.shortUUID();
        when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(modelNo);
        final DeviceModel deviceModel = new DeviceModel();
        deviceModel.setWhiteLight(true);
        deviceModel.setDevicePersonDetect(true);
        handleDeviceModel.accept(deviceModel);
        when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(deviceModel);
        final CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(sn);
        Assert.assertNotNull(cloudDeviceSupport);
        final CloudDeviceSupport expectCloudDeviceSupport = getDefaultDeviceSupport(true, 1, true);
        handleDeviceSupport.accept(expectCloudDeviceSupport);
//        Assert.assertEquals(expectDeviceSupport, deviceSupport);
        AssertUtil.assertEquals(expectCloudDeviceSupport, cloudDeviceSupport);
    }

    private void nullField_follow_deviceStatus(Consumer<DeviceStatusDO> handleDeviceStatus, Consumer<CloudDeviceSupport> handleDeviceSupport) {
        final String sn = OpenApiUtil.shortUUID();
        final DeviceStatusDO deviceStatusDO = new DeviceStatusDO();
        handleDeviceStatus.accept(deviceStatusDO);
        when(deviceStatusService.queryDeviceStatusBySerialNumber(sn)).thenReturn(deviceStatusDO);
        final CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(sn);
        Assert.assertNotNull(cloudDeviceSupport);
        final CloudDeviceSupport expectCloudDeviceSupport = getDefaultDeviceSupport();
        handleDeviceSupport.accept(expectCloudDeviceSupport);
//        Assert.assertEquals(expectDeviceSupport, deviceSupport);
        AssertUtil.assertEquals(expectCloudDeviceSupport, cloudDeviceSupport);
    }

    @Test
    public void test_getSupportServiceNames() {
        Assert.assertEquals(ImmutableSet.of(s3), CloudDeviceSupport.getSupportServiceNames(null));

        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        Assert.assertEquals(ImmutableSet.of(s3), CloudDeviceSupport.getSupportServiceNames(null));

        cloudDeviceSupport.setSupportGoogleStorage(true);
        cloudDeviceSupport.setSupportCos(true);
        cloudDeviceSupport.setSupportOci(true);
        Assert.assertEquals(ImmutableSet.of(s3, gcs, cos, oci), CloudDeviceSupport.getSupportServiceNames(cloudDeviceSupport));

        cloudDeviceSupport.setSupportGoogleStorage(false);
        Assert.assertEquals(ImmutableSet.of(s3, cos, oci), CloudDeviceSupport.getSupportServiceNames(cloudDeviceSupport));

        cloudDeviceSupport.setSupportCos(false);
        Assert.assertEquals(ImmutableSet.of(s3, oci), CloudDeviceSupport.getSupportServiceNames(cloudDeviceSupport));

        cloudDeviceSupport.setSupportOci(false);
        Assert.assertEquals(ImmutableSet.of(s3), CloudDeviceSupport.getSupportServiceNames(cloudDeviceSupport));

    }

    @Test
    public void test_getRecResolution() {
        Assert.assertNull(CloudDeviceSupport.getRecResolution(null, null));
        Assert.assertNull(CloudDeviceSupport.getRecResolution(Arrays.asList(), null));
        Assert.assertNull(CloudDeviceSupport.getRecResolution(null, "mid"));

        final List<String> deviceSupportResolution = Arrays.asList("640x480", "1920x1080", "2560x1440");
        Arrays.asList("640x480", CloudDeviceSupport.getRecResolution(deviceSupportResolution, "mid"));
        Arrays.asList("1920x1080", CloudDeviceSupport.getRecResolution(deviceSupportResolution, "high"));
        Arrays.asList("2560x1440", CloudDeviceSupport.getRecResolution(deviceSupportResolution, "2k"));
        Arrays.asList(null, CloudDeviceSupport.getRecResolution(deviceSupportResolution, "4k"));

        final List<String> deviceSupportResolution2 = Arrays.asList("720P", "1080P");
        Arrays.asList("1280x720", CloudDeviceSupport.getRecResolution(deviceSupportResolution2, "mid"));
        Arrays.asList("1920x1080", CloudDeviceSupport.getRecResolution(deviceSupportResolution2, "high"));
        Arrays.asList(null, CloudDeviceSupport.getRecResolution(deviceSupportResolution2, "4k"));
    }

    @Test
    public void test_getVideoResolutionOptions() {
        Assert.assertEquals(Arrays.asList(), CloudDeviceSupport.getVideoResolutionOptions(null));
        Assert.assertEquals(Arrays.asList(), CloudDeviceSupport.getVideoResolutionOptions(Arrays.asList()));
        {
            final List<String> deviceSupportResolution = Arrays.asList("2560x1440", "1920x1080", "640x480");
            Assert.assertEquals(Arrays.asList("2k", "high", "mid"), CloudDeviceSupport.getVideoResolutionOptions(deviceSupportResolution));
        }
        {
            final List<String> deviceSupportResolution = Arrays.asList("4320P", "1536P", "720P", "abc");
            Assert.assertEquals(Arrays.asList("4k", "2k", "mid"), CloudDeviceSupport.getVideoResolutionOptions(deviceSupportResolution));
        }
    }

    @Test
    public void test_inflateDeviceSupport() {
        final String sn = OpenApiUtil.shortUUID();
        final String modelNo = "modelNo_" + OpenApiUtil.shortUUID();
        {
            final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
            deviceInfoService.inflateDeviceSupport(sn, cloudDeviceSupport);
        }
        {
            mockDeviceSupport(sn, modelNo, false);
            final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
            deviceInfoService.inflateDeviceSupport(sn, cloudDeviceSupport);
        }
        {
            mockDeviceSupport(sn, modelNo, false);
            final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
            deviceInfoService.inflateDeviceSupport(sn, cloudDeviceSupport);
        }
        {
            final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
            cloudDeviceSupport.setPirRecordTimeOptions(Arrays.asList("10s", "15s", "20s", "60s", "120s", "180s"));
            deviceInfoService.inflateDeviceSupport(sn, cloudDeviceSupport);
            Assert.assertEquals(Arrays.asList("10s", "15s", "20s", "60s", "120s", "auto"), cloudDeviceSupport.getPirRecordTimeOptions());
        }
    }

    private void mockDeviceSupport(String sn, String modelNo, boolean enable) {
        when(deviceStatusService.queryDeviceStatusBySerialNumber((sn))).thenAnswer(it -> {
            final DeviceStatusDO status = new DeviceStatusDO();
            status.setQuantityCharge(enable);
            status.setAntiflicker(enable ? 1 : 0);
            return status;
        });
        when(deviceManualService.getModelNoBySerialNumber((sn))).thenReturn(modelNo);
        when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenAnswer(it -> {
            final DeviceModel model = new DeviceModel();
            model.setStreamProtocol(enable ? "webrtc" : "");
            model.setAudioCodectype(enable ? "ACC" : "");
            model.setKeepAliveProtocol(enable ? "tcp" : "");
            model.setCanStandby(enable);
            model.setWhiteLight(enable);
            model.setDevicePersonDetect(enable);
            model.setCanRotate(enable);
            model.setSupportMotionTrack(enable);
            model.setSupportFrequency(enable);
            model.setAntiDisassemblyAlarm(enable);
            return model;
        });
        when(deviceModelService.queryDeviceModelCategory(modelNo)).thenAnswer(it -> enable ?
                DeviceModelCategoryEnums.DOORBELL.getCode() : DeviceModelCategoryEnums.CAMERA.getCode());
        when(deviceEnumMappingService.getEnumMappingByModelNo(modelNo)).thenAnswer(it -> {
            if (enable) {
                return IDeviceEnumMappingServiceTest.getDefaultEnumMapping();
            }
            final OptionEnumMapping enumMapping = new OptionEnumMapping();
            enumMapping.setPirSensitivityOptions(Arrays.asList());
            enumMapping.setPirRecordTimeOptions(Arrays.asList());
            enumMapping.setPirCooldownTimeOptions(Arrays.asList());
            enumMapping.setVideoAntiFlickerFrequencyOptions(Arrays.asList());
            enumMapping.setNightVisionSensitivityOptions(Arrays.asList());
            enumMapping.setAlarmDurationOptions(Arrays.asList());
            return enumMapping;
        });
    }

    private void assertDeviceSupport(boolean expectEnable, CloudDeviceSupport support) {
        Assert.assertEquals(new Integer(expectEnable ? 1 : 0), support.getQuantityCharge());
        Assert.assertEquals(new Integer(expectEnable ? 1 : 0), support.getAntiflickerSupport());
        Assert.assertEquals(expectEnable ? "webrtc" : "", support.getSupportStreamProtocol());
        Assert.assertEquals(expectEnable ? "ACC" : "", support.getSupportAudioCodectype());
        Assert.assertEquals(expectEnable ? "tcp" : "", support.getSupportKeepAliveProtocol());
        Assert.assertEquals(expectEnable, support.getSupportCanStandby());
        Assert.assertEquals(expectEnable, support.getSupportWhiteLight());
        Assert.assertEquals(expectEnable, support.getSupportDevicePersonDetect());
    }

    @Test
    public void test_deviceSupportToMap_deviceSupportFromMap() {
        final Field[] fields1 = CloudDeviceSupport.class.getDeclaredFields();
        log.info("fields1 num={}", fields1.length);
        final List<? extends Class<?>> types = Arrays.stream(fields1).map(it -> it.getType()).distinct().collect(Collectors.toList());
        final Map<Type, List<Field>> type2Fields = Arrays.stream(fields1).collect(Collectors.groupingBy(it -> it.getGenericType()));
        log.info("types num={}", types.size());
        for (final Field field : fields1) {
            if (field.getName().equals("supportDoorBellRingKey")) {
                Assert.assertTrue(isParameterizedType(field.getGenericType(), List.class, Integer.class));
            } else if (field.getName().equals("deviceSupportResolution")) {
                Assert.assertTrue(isParameterizedType(field.getGenericType(), List.class, String.class));
            }
        }
        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        cloudDeviceSupport.setSupportMechanicalDingDong(true);
        cloudDeviceSupport.setSupportCos(true);
        cloudDeviceSupport.setLiveSpeakerVolumeRange(new DeviceAttributeIntRange(1, 100, 1));
        cloudDeviceSupport.setSupportWebrtc(1);
        cloudDeviceSupport.setSupportStreamProtocol("abc");
        cloudDeviceSupport.setSupportDoorBellRingKey(Arrays.asList(1, 2, 3));
        cloudDeviceSupport.setDeviceSupportResolution(Arrays.asList("1920x1080", "1280x720"));

        final Map<String, Object> map = deviceSupportToMap(cloudDeviceSupport, true);
        final CloudDeviceSupport cloudDeviceSupport2 = deviceSupportFromMap(map, true);

        Assert.assertEquals(cloudDeviceSupport, cloudDeviceSupport2);
        AssertUtil.assertEquals(cloudDeviceSupport, cloudDeviceSupport2);

    }

}
