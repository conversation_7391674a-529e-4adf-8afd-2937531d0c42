package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.domain.DeviceGlobal;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.device.WowconfigCache;
import com.addx.iotcamera.config.device.DeviceGlobalConfig;
import com.addx.iotcamera.publishers.vernemq.responses.WowConfigResponse;
import com.addx.iotcamera.service.UdpService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class UdpServiceTest {
    @InjectMocks
    private UdpService udpService;

    @Mock
    private DeviceWowConfigParamService deviceWowConfigParamService;

    @Mock
    private DeviceModelConfigService deviceModelConfigService;

    @Mock
    private DeviceGlobalConfig deviceGlobalConfig;

    @Test
    @DisplayName("设备wowcongig 特殊配置- 无sn支持")
    public void test_changeDeviceConfigWhiteList_nosn_null() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class[] classes = new Class[2];
        classes[0] = String.class;
        classes[1] = WowConfigResponse.class;
        Method method = UdpService.class.getDeclaredMethod("changeDeviceConfigWhiteList", classes);
        //允许私有方法调用
        method.setAccessible(true);

        String serialNumber = "sn";
        WowConfigResponse rsp = new WowConfigResponse();
        when(deviceWowConfigParamService.queryWowConfigCache(serialNumber)).thenReturn(null);
        when(deviceWowConfigParamService.querySupportWowConfigCache()).thenReturn(new HashSet<>());
        method.invoke(udpService, serialNumber, rsp);

        WowConfigResponse expectedResult = new WowConfigResponse();
        Assert.assertEquals(expectedResult, rsp);
    }


    @Test
    @DisplayName("设备wowcongig 特殊配置- 无配置")
    public void test_changeDeviceConfigWhiteList_null() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class[] classes = new Class[2];
        classes[0] = String.class;
        classes[1] = WowConfigResponse.class;
        Method method = UdpService.class.getDeclaredMethod("changeDeviceConfigWhiteList", classes);
        //允许私有方法调用
        method.setAccessible(true);

        String serialNumber = "sn";
        WowConfigResponse rsp = new WowConfigResponse();
        when(deviceWowConfigParamService.querySupportWowConfigCache()).thenReturn(new HashSet<>(Arrays.asList(serialNumber)));
        when(deviceWowConfigParamService.queryWowConfigCache(serialNumber)).thenReturn(null);

        method.invoke(udpService, serialNumber, rsp);

        WowConfigResponse expectedResult = new WowConfigResponse();
        Assert.assertEquals(expectedResult, rsp);
    }


    @Test
    @DisplayName("设备wowcongig 特殊配置")
    public void test_changeDeviceConfigWhiteList() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class[] classes = new Class[2];
        classes[0] = String.class;
        classes[1] = WowConfigResponse.class;
        Method method = UdpService.class.getDeclaredMethod("changeDeviceConfigWhiteList", classes);
        //允许私有方法调用
        method.setAccessible(true);

        String serialNumber = "sn";
        WowConfigResponse rsp = new WowConfigResponse();


        when(deviceWowConfigParamService.querySupportWowConfigCache()).thenReturn(new HashSet<>(Arrays.asList(serialNumber)));

        WowconfigCache cache = new WowconfigCache();
        cache.setDtim(5);
        cache.setRetryCount(1);
        when(deviceWowConfigParamService.queryWowConfigCache(serialNumber)).thenReturn(cache);

        method.invoke(udpService, serialNumber, rsp);

        WowConfigResponse expectedResult = new WowConfigResponse();
        expectedResult.setDtim(5);
        expectedResult.setRetryCount(1);
        Assert.assertEquals(expectedResult, rsp);
    }


    @Test
    public void test_getKeepAliveParamsByModelNo() {
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(61);
                setKeepAliveTimeout(120);
                setMinKeepAliveInterval(70);
                setMaxKeepAliveInterval(79);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(61, result.getInterval());
            Assert.assertEquals(120, result.getTimeout());
            Assert.assertEquals(new Integer(70), result.getMinInterval());
            Assert.assertEquals(new Integer(79), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(61);
                setKeepAliveTimeout(120);
                setMinKeepAliveInterval(0);
                setMaxKeepAliveInterval(79);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(61, result.getInterval());
            Assert.assertEquals(120, result.getTimeout());
            Assert.assertEquals(new Integer(90), result.getMinInterval());
            Assert.assertEquals(new Integer(99), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(61);
                setKeepAliveTimeout(120);
                setMinKeepAliveInterval(70);
                setMaxKeepAliveInterval(null);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(61, result.getInterval());
            Assert.assertEquals(120, result.getTimeout());
            Assert.assertEquals(new Integer(90), result.getMinInterval());
            Assert.assertEquals(new Integer(99), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(61);
                setKeepAliveTimeout(120);
                setMinKeepAliveInterval(null);
                setMaxKeepAliveInterval(79);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(61, result.getInterval());
            Assert.assertEquals(120, result.getTimeout());
            Assert.assertEquals(new Integer(90), result.getMinInterval());
            Assert.assertEquals(new Integer(99), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(61);
                setKeepAliveTimeout(120);
                setMinKeepAliveInterval(79);
                setMaxKeepAliveInterval(70);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(99);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(61, result.getInterval());
            Assert.assertEquals(120, result.getTimeout());
            Assert.assertEquals(new Integer(90), result.getMinInterval());
            Assert.assertEquals(new Integer(99), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(61);
                setKeepAliveTimeout(120);
                setMinKeepAliveInterval(null);
                setMaxKeepAliveInterval(null);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(99);
                setMaxInterval(90);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(61, result.getInterval());
            Assert.assertEquals(120, result.getTimeout());
            Assert.assertEquals(new Integer(61), result.getMinInterval());
            Assert.assertEquals(new Integer(61), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(61);
                setKeepAliveTimeout(120);
                setMinKeepAliveInterval(null);
                setMaxKeepAliveInterval(null);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(null);
                setMaxInterval(99);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(61, result.getInterval());
            Assert.assertEquals(120, result.getTimeout());
            Assert.assertEquals(new Integer(61), result.getMinInterval());
            Assert.assertEquals(new Integer(61), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(61);
                setKeepAliveTimeout(120);
                setMinKeepAliveInterval(null);
                setMaxKeepAliveInterval(null);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(90);
                setMaxInterval(null);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(61, result.getInterval());
            Assert.assertEquals(120, result.getTimeout());
            Assert.assertEquals(new Integer(61), result.getMinInterval());
            Assert.assertEquals(new Integer(61), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(61);
                setKeepAliveTimeout(120);
                setMinKeepAliveInterval(0);
                setMaxKeepAliveInterval(79);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
                setMinInterval(null);
                setMaxInterval(null);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(61, result.getInterval());
            Assert.assertEquals(120, result.getTimeout());
            Assert.assertEquals(new Integer(61), result.getMinInterval());
            Assert.assertEquals(new Integer(61), result.getMaxInterval());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(null);
                setKeepAliveTimeout(120);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(140, result.getInterval());
            Assert.assertEquals(310, result.getTimeout());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(new DeviceModel() {{
                setKeepAliveInterval(61);
                setKeepAliveTimeout(null);
            }});
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(140, result.getInterval());
            Assert.assertEquals(310, result.getTimeout());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(null);
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap(modelNo, new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(140, result.getInterval());
            Assert.assertEquals(310, result.getTimeout());
        }
        {
            String modelNo = OpenApiUtil.shortUUID();
            when(deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo)).thenReturn(null);
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap("CG1", new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(modelNo);
            Assert.assertNotNull(result);
            Assert.assertEquals(140, result.getInterval());
            Assert.assertEquals(310, result.getTimeout());
        }
        {
            when(deviceGlobalConfig.getConfig()).thenReturn(Collections.singletonMap("CG1", new DeviceGlobal() {{
                setInterval(140);
                setTimeout(310);
            }}));
            DeviceGlobal result = udpService.getDeviceGlobalByModelNo(null);
            Assert.assertNotNull(result);
            Assert.assertEquals(140, result.getInterval());
            Assert.assertEquals(310, result.getTimeout());
        }
    }
}
