package com.addx.iotcamera.service.lettuce.config.base;

import com.addx.iotcamera.service.lettuce.properties.LettuceConfigProperties;
import io.lettuce.core.protocol.RedisCommand;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.Duration;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LettuceBaseConfigTest {

    LettuceBaseConfig lettuceBaseConfig = new LettuceBaseConfig();

    LettuceConfigProperties lettuceConfigProperties = new LettuceConfigProperties();

    LettuceConfigProperties.RedisConnConf redisConnConf = new LettuceConfigProperties.RedisConnConf();

    @Mock
    private RedisCommand<String, String, String> command;

    @Before
    public void before() {
        redisConnConf.setHost("127.0.0.1")
                .setPort(6379)
                .setMaxRedirect(5)
                .setDefaultCommandTimeout(1000)
                .setConnectTimeout(1000)
                .setMetaCommandTimeout(1000)
                .setShutdownTimeout(1000);
        lettuceConfigProperties.setMaxActive(2)
                .setMaxIdle(1).setMinIdle(1000).setTimeout(1000)
                .setBusinessRedisCluster(redisConnConf);
    }

    @Test
    public void clientResources() {
        lettuceBaseConfig.clientResources(lettuceConfigProperties);
    }

    @Test
    public void clientOptions() {
        lettuceBaseConfig.clientOptions(lettuceConfigProperties);
    }

    @Test
    public void getTimeout(){
        DynamicClusterTimeout dynamicClusterTimeout = new DynamicClusterTimeout(Duration.ofMillis(1), Duration.ofMillis(1));
        dynamicClusterTimeout.getTimeout(command);
    }
}