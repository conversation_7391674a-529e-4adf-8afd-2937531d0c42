package com.addx.iotcamera.service.firmware;

import com.addx.iotcamera.bean.domain.OTAFinishRequest;
import com.addx.iotcamera.bean.domain.device.DeviceOTADO;
import com.addx.iotcamera.dao.IFirmwareDAO;
import com.addx.iotcamera.dao.device.IDeviceOTADAO;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.MqttResponseService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.ReportLogService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.addx.iotcamera.constants.ReportLogConstants.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class MqttResponseServiceTest {
    @InjectMocks
    private MqttResponseService mqttResponseService;

    @Mock
    private DeviceService deviceService;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private IFirmwareDAO firmwareDAO;
    @Mock
    private IDeviceOTADAO iDeviceOTADAO;
    @Mock
    private ReportLogService reportLogService;

    @Mock
    private RedisService redisService;
    @Test
    public void test_handleOtaFinishRequest(){
        OTAFinishRequest request = new OTAFinishRequest();
        request.setId(1);
        OTAFinishRequest.OTAFinishRequestValue value = new OTAFinishRequest.OTAFinishRequestValue();
        value.setGitSha("gitSha");
        value.setVersion("1.1.1");
        value.setMcuVersion("1.0.0");
        value.setResult(-1);
        request.setValue(value);
        request.setSerialNumber("sn");

        when(deviceService.updateFirmwareVersionBySerialNumber(any(),any())).thenReturn(1);
        doNothing().when(deviceInfoService).deviceFirmwareBuilder(any());
        doNothing().when(deviceManualService).updateMcuVersionBySerialNumber(any(),any());

        when(firmwareDAO.finishOta(any())).thenReturn(1);
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("modelNo");

        DeviceOTADO deviceOTADO = new DeviceOTADO();
        deviceOTADO.setOtaStartTime(1);
        when(iDeviceOTADAO.queryDeviceOTABySerialNumber(any())).thenReturn(deviceOTADO);

        doNothing().when(reportLogService).reportLog(REPORT_TYPE_DEVICE_OTA_FINISH,REPORT_GROUP_REPORT,REPORTER_DEVICE, Maps.newHashMap());
        when(redisService.get(any())).thenReturn("1");
        doNothing().when(redisService).dropDeviceOperationDo(any());
        when(firmwareDAO.updateOtaOnAwake(request.getSerialNumber(),false)).thenReturn(1);

        mqttResponseService.handleOtaFinishRequest(request);
        Assert.assertTrue(true);
    }
}
