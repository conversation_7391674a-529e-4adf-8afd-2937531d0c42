package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.device.attributes.DeviceOnlineInfo;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceStateDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.enums.DeviceStateMachineEnums;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceStatusService;
import org.addx.iot.common.utils.PhosUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.addx.iot.common.enums.ResultCollection.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceAuthServiceTest {

    @InjectMocks
    private DeviceAuthService deviceAuthService;

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private DeviceStatusService deviceStatusService;

    @Mock
    private IDeviceDAO iDeviceDAO;

    @Mock
    private DeviceService deviceService;

    @Mock
    private StateMachineService stateMachineService;

    @Test
    public void checkAdminAccessWithDEVICE_NO_ACCESS() {

        Integer expectedResult = DEVICE_NO_ACCESS.getCode();

        Integer actualResult = deviceAuthService.checkAdminAccess(1, "testSerialNumber");

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void checkAdminAccessWithDEVICE_AUTH_LIMITATION() {
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setOnline(0);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setSerialNumber("");
        when(userRoleService.getUserRoleDOByUserIdAndSerialNumber(any(), any())).thenReturn(userRoleDO);

        DeviceStatusDO deviceStatusDO = new DeviceStatusDO();
        deviceStatusDO.setSerialNumber("testSerialNumber");
        when(deviceStatusService.queryDeviceStatusBySerialNumber(any())).thenReturn(deviceStatusDO);

        Integer expectedResult = DEVICE_AUTH_LIMITATION.getCode();
        Integer actualResult = deviceAuthService.checkAdminAccess(1, "testSerialNumber");

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void checkAdminAccessWithDEVICE_OFFLINE() {
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setOnline(0);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setSerialNumber("");
        userRoleDO.setAdminId(1);
        when(userRoleService.getUserRoleDOByUserIdAndSerialNumber(any(), any())).thenReturn(userRoleDO);

        DeviceStatusDO deviceStatusDO = new DeviceStatusDO();
        when(deviceStatusService.queryDeviceStatusBySerialNumber(any())).thenReturn(deviceStatusDO);

        DeviceStateDO deviceStateDO = new DeviceStateDO();
        deviceStateDO.setSn("");
        deviceStateDO.setStateId(DeviceStateMachineEnums.STATE_SHUTDOWN.getCode());
        when(stateMachineService.getDeviceState(any())).thenReturn(deviceStateDO);

        when(deviceStatusService.getDeviceOnlineInfo(any(),anyInt(),any(),any())).thenReturn(new DeviceOnlineInfo(){{
            setOnline(0);
            setAwake(0);
            setOfflineTime(PhosUtils.getUTCStamp());
        }});

        Integer expectedResult = DEVICE_OFFLINE.getCode();

        Integer actualResult = deviceAuthService.checkAdminAccess(1, "testSerialNumber");

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void checkAdminAccessWithSuccess() {
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setOnline(1);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setSerialNumber("");
        userRoleDO.setAdminId(1);
        when(userRoleService.getUserRoleDOByUserIdAndSerialNumber(any(), any())).thenReturn(userRoleDO);

        DeviceStatusDO deviceStatusDO = new DeviceStatusDO();
        deviceStatusDO.setLastAct((int) (System.currentTimeMillis() / 1000));
        deviceStatusDO.setLastAwake((int) (System.currentTimeMillis() / 1000));

        when(deviceStatusService.queryDeviceStatusBySerialNumber(any())).thenReturn(deviceStatusDO);

        DeviceStateDO deviceStateDO = new DeviceStateDO();
        deviceStateDO.setSn("");
        deviceStateDO.setStateId(DeviceStateMachineEnums.STATE_MQTT_CONNECTED.getCode());
        when(stateMachineService.getDeviceState(any())).thenReturn(deviceStateDO);

        when(deviceStatusService.getDeviceOnlineInfo(any(),anyInt(),any(),any())).thenReturn(new DeviceOnlineInfo(){{
            setOnline(1);
            setAwake(1);
        }});

        Integer expectedResult = SUCCESS.getCode();

        Integer actualResult = deviceAuthService.checkAdminAccess(1, "testSerialNumber");

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void checkCommonAccessWithDEVICE_NO_ACCESS() {
        Integer expectedResult = DEVICE_NO_ACCESS.getCode();

        Integer actualResult = deviceAuthService.checkCommonAccess(1, "testSerialNumber");

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void checkCommonAccessWithSUCCESS() {
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setOnline(1);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);

        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setSerialNumber("");
        userRoleDO.setAdminId(1);
        when(userRoleService.getUserRoleDOByUserIdAndSerialNumber(any(), any())).thenReturn(userRoleDO);

        DeviceStatusDO deviceStatusDO = new DeviceStatusDO();
        deviceStatusDO.setLastAct((int) (System.currentTimeMillis() / 1000));
        when(deviceStatusService.queryDeviceStatusBySerialNumber(any())).thenReturn(deviceStatusDO);

        Integer expectedResult = SUCCESS.getCode();

        Integer actualResult = deviceAuthService.checkCommonAccess(1, "testSerialNumber");

        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void test_checkActivatedAccess() {
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setOnline(0);
        deviceDO.setActivated(1);
        when(deviceService.getAllDeviceInfo(any())).thenReturn(deviceDO);
        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setSerialNumber("");
        when(userRoleService.getUserRoleDOByUserIdAndSerialNumber(any(), any())).thenReturn(userRoleDO);

        deviceAuthService.checkActivatedAccess(1, "1");
    }
}
