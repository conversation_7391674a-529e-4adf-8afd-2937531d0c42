package com.addx.iotcamera.service.extension;

import com.addx.iotcamera.bean.extension.Extension;
import com.addx.iotcamera.bean.extension.ExtensionResult;
import com.addx.iotcamera.dao.ExtensionDAO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.Silent.class)
public class ExtensionMarketServiceTest {

    @InjectMocks
    ExtensionMarketService extensionMarketService;
    @Mock
    ExtensionDAO extensionDAO;

    @Test
    public void extensionList() {
        System.out.println(extensionMarketService.extensionList("en", "iOS", "1.1.0"));
        System.out.println(extensionMarketService.extensionList(null, "other", "1.1.0"));
        System.out.println(extensionMarketService.extensionList(null, "iOS", "1.1.0"));
        System.out.println(extensionMarketService.extensionList("de", "other", "1.1.0"));
        System.out.println(extensionMarketService.extensionList("de", "iOS", "1.1.0"));

        List<Extension> extensionList = new ArrayList<>();
        extensionList.add(new Extension());
        when(extensionDAO.selectExtensionList(any(), any())).thenReturn(extensionList);

        System.out.println(extensionMarketService.extensionList("en", "iOS", "1.1.0"));
        System.out.println(extensionMarketService.extensionList(null, "other", "1.1.0"));
        System.out.println(extensionMarketService.extensionList(null, "iOS", "1.1.0"));
        System.out.println(extensionMarketService.extensionList("de", "other", "1.1.0"));
        System.out.println(extensionMarketService.extensionList("de", "iOS", "1.1.0"));

    }

    @Test
    public void extensionQuery() {
        Extension extension = new Extension();
        extension.setMinimumAppVersion("1.1.1.");
        extension.setDisplayName("Motion Zone");
        extension.setMinimumHubVersion("0.3.6");
        extension.setAndroidPreviewImages("http://android.com");
        extension.setIosPreviewImages("http://ios.com");
        when(extensionDAO.selectExtensionByName(eq("motionZone"), any(), any())).thenReturn(extension);
        ExtensionResult res = extensionMarketService.extensionQuery("motionZone", "en", "1.1.1", "iOS");
        Assert.assertEquals(res.getMinimumHubVersion(), extension.getMinimumHubVersion());
        Assert.assertEquals(res.getDisplayName(), extension.getDisplayName());
        res = extensionMarketService.extensionQuery("motionZone", "zh", "1.1.1", "iOS");
        Assert.assertEquals(res.getMinimumHubVersion(), extension.getMinimumHubVersion());
        Assert.assertEquals(res.getDisplayName(), extension.getDisplayName());
        res = extensionMarketService.extensionQuery("motionZone", "zh", "1.1.0", "iOS");
        Assert.assertEquals(res.getMinimumHubVersion(), extension.getMinimumHubVersion());
        Assert.assertEquals(res.getDisplayName(), extension.getDisplayName());
        res = extensionMarketService.extensionQuery("motionZone", null, "1.1.0", "iOS");
        Assert.assertEquals(res.getMinimumHubVersion(), extension.getMinimumHubVersion());
        Assert.assertEquals(res.getDisplayName(), extension.getDisplayName());

        res = extensionMarketService.extensionQuery("motionZone", null, "1.1.0(111)", "iOS");
        Assert.assertEquals(res.getMinimumHubVersion(), extension.getMinimumHubVersion());
        Assert.assertEquals(res.getDisplayName(), extension.getDisplayName());
        Assert.assertEquals(res.getPreviewImages(), extension.getIosPreviewImages());

        // app 类型不同，返回的image url不同
        res = extensionMarketService.extensionQuery("motionZone", null, "1.1.0(111)", "Android");
        Assert.assertEquals(res.getMinimumHubVersion(), extension.getMinimumHubVersion());
        Assert.assertEquals(res.getDisplayName(), extension.getDisplayName());
        Assert.assertEquals(res.getPreviewImages(), extension.getAndroidPreviewImages());
    }
}