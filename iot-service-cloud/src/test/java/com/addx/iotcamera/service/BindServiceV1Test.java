package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.BindByApOperationRequest;
import com.addx.iotcamera.bean.app.BindOperationRequest;
import com.addx.iotcamera.bean.domain.IpInfo;
import com.addx.iotcamera.config.DomainIpMappingConfig;
import com.addx.iotcamera.enums.DeviceBindTypeEnums;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.addx.iot.common.vo.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@SpringBootTest
public class BindServiceV1Test {

    @InjectMocks
    private BindServiceV1 bindServiceV1;

    @Mock
    private BindService bindService;

    @Mock
    private DomainIpMappingConfig domainIpMappingConfig;

    private Integer userId = 123;
    private IpInfo ipInfo;
    private String serverName = "api-us.vicohome.io";
    private String testIp = "************";

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        
        // 设置IP信息
        ipInfo = new IpInfo();
        ipInfo.setIp("***********");
        
        // 模拟domainIpMappingConfig.getFirstIpByDomain方法
        when(domainIpMappingConfig.getFirstIpByDomain(serverName)).thenReturn(testIp);
    }

    @Test
    public void testQueryDeviceBindCodeV1_JsonFormat() throws Exception {
        // 准备请求
        BindOperationRequest request = new BindOperationRequest();
        request.setCodeType(DeviceBindTypeEnums.JSON.getCode());
        request.setWidth(300);
        request.setHeight(300);
        request.setTraceId("12345678");
        
        // 模拟bindService.queryDeviceBindCode方法返回
        Map<String, String> resultData = new HashMap<>();
        JSONObject jsonContent = new JSONObject();
        jsonContent.put("r", "operation123");
        jsonContent.put("l", "en");
        jsonContent.put("t", "12345678");
        resultData.put("contents", jsonContent.toJSONString());
        resultData.put("image", "base64image");
        Result mockResult = new Result(resultData);
        
        when(bindService.queryDeviceBindCode(eq(userId), any(BindOperationRequest.class), eq(ipInfo)))
                .thenReturn(mockResult);
        
        // 调用测试方法
        Result result = bindServiceV1.queryDeviceBindCodeV1(userId, request, ipInfo, serverName);
        
        // 验证结果
        assertEquals(0, result.getResult());
        Map<String, String> data = (Map<String, String>) result.getData();
        assertNotNull(data);
        
        // 验证内容中包含IP
        String contents = data.get("contents");
        JSONObject jsonObject = JSON.parseObject(contents);
        assertEquals(testIp, jsonObject.getString("ip"));
        
        // 验证其他字段保持不变
        assertEquals("12345678", jsonObject.getString("t"));
        assertEquals("operation123", jsonObject.getString("r"));
    }

    @Test
    public void testQueryDeviceBindCodeV1_BinaryFormat() throws Exception {
        // 准备请求
        BindOperationRequest request = new BindOperationRequest();
        request.setCodeType(DeviceBindTypeEnums.TEXT.getCode());
        request.setWidth(300);
        request.setHeight(300);
        request.setTraceId("12345678");
        
        // 模拟bindService.queryDeviceBindCode方法返回
        Map<String, String> resultData = new HashMap<>();
        resultData.put("contents", "binary-content");
        resultData.put("image", "base64image");
        Result mockResult = new Result(resultData);
        
        when(bindService.queryDeviceBindCode(eq(userId), any(BindOperationRequest.class), eq(ipInfo)))
                .thenReturn(mockResult);
        
        // 调用测试方法
        Result result = bindServiceV1.queryDeviceBindCodeV1(userId, request, ipInfo, serverName);
        
        // 验证结果
        assertEquals(0, result.getResult());
        Map<String, String> data = (Map<String, String>) result.getData();
        assertNotNull(data);
        
        // 验证内容已更新
        String contents = data.get("contents");
        assertNotEquals("binary-content", contents);
        assertTrue(contents.length() > "binary-content".length());
    }

    @Test
    public void testBindCableDeviceV1() throws Exception {
        // 准备请求
        BindOperationRequest request = new BindOperationRequest();
        request.setWidth(300);
        request.setHeight(300);
        request.setUserSn("test-sn");
        
        // 模拟bindService.bindCableDevice方法返回
        Map<String, String> resultData = new HashMap<>();
        resultData.put("contents", "binary-content");
        resultData.put("image", "base64image");
        Result mockResult = new Result(resultData);
        
        when(bindService.bindCableDevice(eq(userId), any(BindOperationRequest.class), eq(ipInfo)))
                .thenReturn(mockResult);
        
        // 调用测试方法
        Result result = bindServiceV1.bindCableDeviceV1(userId, request, ipInfo, serverName);
        
        // 验证结果
        assertEquals(0, result.getResult());
        Map<String, String> data = (Map<String, String>) result.getData();
        assertNotNull(data);
        
        // 验证内容已更新
        String contents = data.get("contents");
        assertNotEquals("binary-content", contents);
        assertTrue(contents.length() > "binary-content".length());
    }

    @Test
    public void testQueryDeviceBindByApTextResultV1() throws Throwable {
        // 准备请求
        BindByApOperationRequest request = new BindByApOperationRequest();
        
        // 模拟bindService.queryDeviceBindByApTextResult方法返回
        Map<String, String> resultData = new HashMap<>();
        resultData.put("bindText", "binary-content");
        resultData.put("bindJson", "{\"r\":\"operation123\",\"l\":\"en\"}");
        Result mockResult = new Result(resultData);
        
        when(bindService.queryDeviceBindByApTextResult(eq(userId), any(BindByApOperationRequest.class), eq(ipInfo)))
                .thenReturn(mockResult);
        
        // 调用测试方法
        Result result = bindServiceV1.queryDeviceBindByApTextResultV1(userId, request, ipInfo, serverName);
        
        // 验证结果
        assertEquals(0, result.getResult());
        Map<String, String> data = (Map<String, String>) result.getData();
        assertNotNull(data);
        
        // 验证bindText已更新
        String bindText = data.get("bindText");
        assertNotEquals("binary-content", bindText);
        assertTrue(bindText.length() > "binary-content".length());
        
        // 验证bindJson已更新并包含IP
        String bindJson = data.get("bindJson");
        JSONObject jsonObject = JSON.parseObject(bindJson);
        assertEquals(testIp, jsonObject.getString("ip"));
    }
} 