package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.AITask;
import com.addx.iotcamera.config.opanapi.SaasAiTaskConfig;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.openapi.SaasAIService;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.LinkedList;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class SaasAIServiceTest {

    @InjectMocks
    private SaasAIService saasAIService;
    @Mock
    private SaasAiTaskConfig aiTaskConfig;
    @Mock
    private MqSender oldTaskMqSender;
    @Mock
    RedisService redisService;

    // 记录发送过老任务的traceId
    private LinkedList<String> oldTaskTraceIds = new LinkedList<>();

    @Before
    public void init() {
        doAnswer(invocationOnMock -> {
            // mqSender.send(videoSegmentTopic, aiTask.getTraceId(), aiTask);
            String traceId = invocationOnMock.getArgument(1);
            oldTaskTraceIds.add(traceId);
            return null;
        }).when(oldTaskMqSender).send(any(), anyString(), any());
    }

    private void setAiTaskConfig(int oldPercent, int saasPercent, boolean useSaasResult) {
        when(aiTaskConfig.getSendOldTaskPercent()).thenReturn(oldPercent);
        when(aiTaskConfig.getSendSaasTaskPercent()).thenReturn(saasPercent);
        when(aiTaskConfig.getUseSaasResult()).thenReturn(useSaasResult);
    }

    private static AITask buildAiTask(String postfix) {
        AITask aiTask = new AITask();
        aiTask.setTraceId("trace_" + postfix);
        aiTask.setTaskId("task_" + postfix);
        aiTask.setSerialNumber("sn_" + postfix);
        return aiTask;
    }

    @Test
    public void not_use_saas_result() {
        setAiTaskConfig(100, 10, false);
        oldTaskTraceIds.clear();
        for (int i = 0; i < 1000; i++) {
            AITask aiTask = buildAiTask(OpenApiUtil.shortUUID());
            String[] idArr = saasAIService.handleOldAndNewTaskPercent(aiTask);
            Assert.assertTrue(!oldTaskTraceIds.getLast().startsWith("old:"));
            Assert.assertEquals(aiTask.getTraceId(), oldTaskTraceIds.getLast());
        }
    }

    @Test
    public void use_10_percent_saas_result() {
        setAiTaskConfig(100, 10, true);
        oldTaskTraceIds.clear();
        for (int i = 0; i < 1000; i++) {
            AITask aiTask = buildAiTask(OpenApiUtil.shortUUID());
            String[] idArr = saasAIService.handleOldAndNewTaskPercent(aiTask);
            if (idArr == null || idArr[0].startsWith("saas:")) {
                Assert.assertTrue(!oldTaskTraceIds.getLast().startsWith("old:"));
                Assert.assertEquals(aiTask.getTraceId(), oldTaskTraceIds.getLast());
            } else {
                Assert.assertTrue(oldTaskTraceIds.getLast().startsWith("old:"));
                Assert.assertEquals(aiTask.getTraceId(), oldTaskTraceIds.getLast());
            }
        }
    }

    @Test
    public void use_50_percent_saas_result() {
        setAiTaskConfig(20, 50, true);
        oldTaskTraceIds.clear();
        for (int i = 0; i < 1000; i++) {
            AITask aiTask = buildAiTask(OpenApiUtil.shortUUID());
            String[] idArr = saasAIService.handleOldAndNewTaskPercent(aiTask);
            if (idArr == null || idArr[0].startsWith("saas:")) {
                Assert.assertTrue(!oldTaskTraceIds.getLast().startsWith("old:"));
                Assert.assertEquals(aiTask.getTraceId(), oldTaskTraceIds.getLast());
            } else {
                if (SaasAIService.traceIdInPercent(aiTask.getTraceId(), 20)) {
                    Assert.assertTrue(oldTaskTraceIds.getLast().startsWith("old:"));
                    Assert.assertEquals(aiTask.getTraceId(), oldTaskTraceIds.getLast());
                }
            }
        }
    }

//    @Test
//    public void test_qrcode1() throws Exception {
//        String content = "{\"p\":\"REMtOTk5OTk5\",\"r\":\"6b7e5564\",\"c\":0,\"e\":\"cn\",\"l\":\"en\",\"n\":\"REMtOTk5OQ==\"}";
//        String expectCode = "iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQAQAAAACoxAthAAACcklEQVR42u2cTZKEIAyF48pjeFQ8qsdwJdOSX9CuqVmSeS6oseFzlYKXlzBU//wQECBJkIvkWS8eCs+cyz18fto+S8qhqwqQXAivORhpf1+8UFavbZAJINmQOxpkhpa6b6fEDy8UxMIJSFKEtnuTOGz3uF93osXCCUhaRKTATho67cSQVyBZETssStMGRKYSTvrtfAEyOeJCsRxfh2/aEsjUSHx2UYaqFv0zX/NKIFMjmhJwhsDaQHaKz4R+xiYKkExI+1GGGoPDzg5TkB5jQJIgF2sDlYcUX1uYbOYSsWkEJBfC7/XNIjAZqRMFSCZE3tkYEFOwcppQgjdkHhKQTIhuEhwNbhGEnJGsZgAkHyIWgQ7VcgXZJOJQgSRD7ul9NIPYGZQ0IcoHIIkQTRO4KEyaJFJ/dkj8EJBciJeCLTUckkRazD7yJBFICqTGmp8liS4U3SpiQQkkFyIGUROK5glYmEipkN7sQSCzI5clB94JVLs+sNVrBheQbEjX+KVJYjwsunYRIJkQ3xAoqASWDwdJO5BuIV1RGEgKRLLC9yQxfiE4ikBSIF1DSD27CNk65yhWE4AkQvrq39AkcmpT4DYUhYFMj8QzoYZagDWCSs0gNIICyYRY0ydtZ5QFoRxkXcEVSCbEmsHanuEJAz2KwjR0EQCZH+l84SFJPLqokaMESCLEhaKZ//6cS+cNPa79ApkeCRdAPEnkFqHVvQPdQoAkQ/zqn1T+VT7QcPfDAwZINsRLwSYKzTc8enUBJCHivSDMmS2knSLLI2CAzI6Ew4JMG9jGYf2A73dFgcyNDHaAXvag7laYrgOSCsH/uQLyT5EfdhCnECwYEsoAAAAASUVORK5CYII=";
//        String realCode = QRcodeUtil.genBarcode(content, 400, 400);
//        Assert.assertEquals(expectCode, realCode);
//    }
//
//    @Test
//    public void test_qrcode2() throws Exception {
//        String content = "795ee2ed    DC-9999  cn  DC-999999";
//        String expectCode = "iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQAQAAAACoxAthAAABuklEQVR42u3cQY7DIAyFYSQOlqtzMCRGbQA/03SSOLObP+oiTfi6QhjbSVO7fSQIBAL5n6SkceTXpZL7vTK++gEQyH0iY8p+vaZtv1RfX/2PQCARso8s824dk/BtdQAE8pD0MSltb7WNaQmB/BXpJ2ld+iCQh8TCq//oXu5rRIZATolkAeXw82tmAYGcEj32AT2qvk+2swoJBHJK5q0eVWuve6w5Qo+5EEiE2JjaE4Q0M4U20ocKgQRJGwtdmtNSou1SwoVAAqRkWfR8m6A1S0vd0geB3CLJjSyy3CXpQG2HqSgEcoHoLm5GWI2tRXZ0EEiA9PHVwqs7l06BVkggkHtkbtLmRm7Ow2qR101LCOQ6+ajN9h/RxMFSBgjkNmmjY24hVTsF8xYEEiU9I5AymlY/SraSmnusEQK5TJpct9wzS8D9LNtCIHeIm3W2xFlJLckUhUACZGmjz86mLHd2HQIJkM8Ewc1J3chBIDFy0Me03rp7oSlDIEGyvgKwWNf0hEAekVnosLygHu/6IJAYac0noVJPKxkCiRNX9JCy7dqcgkBixL9m0vw8LP6JIAgkQPgXGggEAjk7fgCrK9GVKSjNpgAAAABJRU5ErkJggg==";
//        String realCode = QRcodeUtil.genBarcode(content, 400, 400);
//        Assert.assertEquals(expectCode, realCode);
//    }

//    @Test
//    public void test() throws Exception {
//        // longse gkKwXRizYawazyGSnjmBg5 ijJdsi+RTy6LptSCQ+HNfw==
//        String url = OpenApiAuthService.createSignedUrl(
////                "https://api-stage.addx.live/open-api/auth/token"
//                "https://api-staging-us.addx.live/open-api/auth/token"
//                , "gkKwXRizYawazyGSnjmBg5", "ijJdsi+RTy6LptSCQ+HNfw==");
//        log.info("url:\n{}", url);
//    }
}
