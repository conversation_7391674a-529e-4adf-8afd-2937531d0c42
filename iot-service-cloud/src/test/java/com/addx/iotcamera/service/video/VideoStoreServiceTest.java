package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.db.DeviceLibraryViewDO;
import com.addx.iotcamera.bean.db.UserLibraryViewDO;
import com.addx.iotcamera.dynamo.dao.VideoSliceDAO;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.LibraryStatusService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.VideoSearchService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryStatusDAO;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashSet;

import static com.addx.iotcamera.service.video.VideoCacheServiceTest.createMockVideoCache;
import static com.addx.iotcamera.service.video.VideoStoreService.CreateResult.CREATED;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoStoreServiceTest {

    @InjectMocks
    private VideoStoreService videoStoreService;

    @Mock
    private VideoCacheService videoCacheService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private VideoSliceDAO videoSliceDAO;
    @Mock
    private IShardingLibraryDAO shardingLibraryDAO;
    @Mock
    private IShardingLibraryStatusDAO shardingLibraryStatusDAO;
    @Mock
    private RedisService redisService;
    @Mock
    private MqSender mqSender;
    @Mock
    private VideoSearchService videoSearchService;
    @Mock
    private TimeTicker timeTicker;
    @Mock
    private LibraryStatusService libraryStatusService;

    @Mock
    FactoryDataQueryService  factoryDataQueryService;

    @Mock
    private DeviceManualService deviceManualService;

    @Before
    public void init() {
        when(videoCacheService.getVideoEvent(any())).thenAnswer(it -> timeTicker.readSeconds() + "");
    }

    @After
    public void after() {

    }

    @Test
    public void test_createVideo() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
//        {
//            when(shardingLibraryDAO.insertLibrary(any())).thenAnswer(it -> {
//                throw new RuntimeException();
//            });
//            Assert.assertEquals(ERROR, videoStoreService.createVideo(video));
//        }
//        {
//            when(shardingLibraryDAO.insertLibrary(any())).thenThrow(DuplicateKeyException.class);
//            Assert.assertEquals(DUPLICATE_KEY, videoStoreService.createVideo(video));
//        }
        {
            when(shardingLibraryDAO.insertLibrary(any())).thenReturn(1);
            Assert.assertEquals(CREATED, videoStoreService.createVideo(video));
        }
    }

    @Test
    public void test_updateVideo() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
//        {
//            when(shardingLibraryDAO.updateLibraryByTraceIdV2(any(),anyInt(),anyInt())).thenThrow(new RuntimeException(""));
//            Assert.assertEquals(false, videoStoreService.updateVideo(video, ""));
//        }
        {
            when(shardingLibraryDAO.updateLibraryByUserIdAndTraceIdV2(any())).thenReturn(1);
            Assert.assertEquals(true, videoStoreService.updateVideo(video, ""));
        }
    }

    @Test
    public void test_queryNewVideoLibraryByTraceId() {
        {
            when(shardingLibraryStatusDAO.selectAdminsByUserId(any())).thenAnswer(it -> null);
            DeviceLibraryViewDO result = videoStoreService.selectLibraryViewByAdminIdAndTraceId(1, "");
            Assert.assertNull(result);
        }
        {
            when(shardingLibraryStatusDAO.selectAdminsByUserId(any())).thenAnswer(it -> new HashSet<>());
            DeviceLibraryViewDO result = videoStoreService.selectLibraryViewByAdminIdAndTraceId(1, "");
            Assert.assertNull(result);
        }
        {
            UserLibraryViewDO view = new UserLibraryViewDO();
            view.setTraceId(OpenApiUtil.shortUUID());
            when(shardingLibraryStatusDAO.selectAdminsByUserId(any())).thenAnswer(it -> new HashSet<>(Arrays.asList(1, 2)));
            when(shardingLibraryDAO.selectLibraryViewByAdminIdAndTraceId(any(), any())).thenAnswer(it -> view);
            DeviceLibraryViewDO result = videoStoreService.selectLibraryViewByAdminIdAndTraceId(1, "");
            Assert.assertEquals(view, result);
        }
    }

}
