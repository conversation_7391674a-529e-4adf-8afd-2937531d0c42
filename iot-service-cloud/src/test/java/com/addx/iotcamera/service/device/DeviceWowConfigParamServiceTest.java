package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.domain.device.WowconfigCache;
import com.addx.iotcamera.dao.device.IDeviceWowConfigDAO;
import com.addx.iotcamera.service.RedisService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceWowConfigParamServiceTest {
    @InjectMocks
    private DeviceWowConfigParamService deviceWowConfigParamService;

    @Mock
    private IDeviceWowConfigDAO iDeviceWowConfigDAO;

    @Mock
    private RedisService redisService;

    @Test
    @DisplayName("查询deviceWowconfig param")
    public void test_queryWowConfigCache(){
        WowconfigCache cache = new WowconfigCache();
        when(iDeviceWowConfigDAO.queryDeviceWowconfigParam(any())).thenReturn(cache);

        WowconfigCache expectedResult = cache;
        WowconfigCache actualResult = deviceWowConfigParamService.queryWowConfigCache("test");
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("更新参数")
    public void test_updateDeviceWowConfigParam(){
        WowconfigCache cache = new WowconfigCache();
        when(iDeviceWowConfigDAO.updateDeviceWowconfigParam(cache)).thenReturn(-1);
        doReturn(true).when(redisService).delete(anyString());
        deviceWowConfigParamService.updateDeviceWowConfigParam(cache);
        verify(iDeviceWowConfigDAO, times(1)).updateDeviceWowconfigParam(cache);
    }

    @Test
    @DisplayName("删除参数")
    public void test_deleteDeviceWowConfigParam(){
        String sn = "sn";
        when(iDeviceWowConfigDAO.deleteDeviceWowconfigParam(sn)).thenReturn(-1);
        doReturn(true).when(redisService).delete(sn);
        deviceWowConfigParamService.deleteDeviceWowConfigParam(sn);
        verify(iDeviceWowConfigDAO, times(1)).deleteDeviceWowconfigParam(sn);
    }
}
