package com.addx.iotcamera.service.openapi;


import com.addx.iotcamera.bean.openapi.PaasTenantInfo;
import com.addx.iotcamera.config.PaasTenantConfigTest;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.dao.openapi.PaasTenantConfigDAO;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PaasTenantConfigServiceTest {

    @InjectMocks
    private PaasTenantConfigService paasTenantConfigService;

    @Mock
    private PaasTenantConfigDAO paasTenantConfigDAO;

    @Test
    public void test_getPaasTenantInfoByTenantId() {
        {
            final String tenantId = OpenApiUtil.shortUUID();
            when(paasTenantConfigDAO.queryByTenantId(tenantId)).thenReturn(null);
            Assert.assertNull(paasTenantConfigService.getPaasTenantInfoByTenantId(tenantId).orElse(null));
        }
        {
            final String tenantId = OpenApiUtil.shortUUID();
            when(paasTenantConfigDAO.queryByTenantId(tenantId)).thenThrow(new RuntimeException());
            Assert.assertNull(paasTenantConfigService.getPaasTenantInfoByTenantId(tenantId).orElse(null));
        }
        {
            final String tenantId = null;
            when(paasTenantConfigDAO.queryByTenantId(tenantId)).thenThrow(new RuntimeException());
            Assert.assertNull(paasTenantConfigService.getPaasTenantInfoByTenantId(tenantId).orElse(null));
        }
        {
            final String tenantId = OpenApiUtil.shortUUID();
            final PaasTenantInfo paasTenantInfo = new PaasTenantInfo().setTenantId(tenantId);
            when(paasTenantConfigDAO.queryByTenantId(tenantId)).thenReturn(paasTenantInfo);
            Assert.assertEquals(paasTenantInfo, paasTenantConfigService.getPaasTenantInfoByTenantId(tenantId).orElse(null));

            when(paasTenantConfigDAO.queryByTenantId(tenantId)).thenReturn(null);
            Assert.assertEquals(paasTenantInfo, paasTenantConfigService.getPaasTenantInfoByTenantId(tenantId).orElse(null));

            paasTenantConfigService.getCache().invalidateAll();
            Assert.assertNull(paasTenantConfigService.getPaasTenantInfoByTenantId(tenantId).orElse(null));

            when(paasTenantConfigDAO.queryByTenantId(tenantId)).thenReturn(paasTenantInfo);
            Assert.assertNull(paasTenantConfigService.getPaasTenantInfoByTenantId(tenantId).orElse(null));

            paasTenantConfigService.getCache().invalidateAll();
            Assert.assertEquals(paasTenantInfo, paasTenantConfigService.getPaasTenantInfoByTenantId(tenantId).orElse(null));
        }
    }

    @Test
    public void test_savePaasTenantConfig() {
        final PaasTenantConfig config = PaasTenantConfigTest.loadPaasTenantConfig();

        final TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        final PaasTenantConfigDAO dao = testHelper.getMapper(PaasTenantConfigDAO.class);
        when(paasTenantConfigDAO.save(any())).thenAnswer(AdditionalAnswers.delegatesTo(dao));
        when(paasTenantConfigDAO.queryByTenantId(any())).thenAnswer(AdditionalAnswers.delegatesTo(dao));

        for (final String tenantId : config.getConfig().keySet()) {
            final PaasTenantInfo paasTenantInfo = config.getConfig().get(tenantId);
            final int saveNum = paasTenantConfigService.savePaasTenantConfig(paasTenantInfo);
            Assert.assertTrue(saveNum > 0);
            final Optional<PaasTenantInfo> paasTenantInfoOpt = paasTenantConfigService.getPaasTenantInfoByTenantId(tenantId);
            Assert.assertEquals(paasTenantInfo, paasTenantInfoOpt.orElse(null));
        }
        testHelper.commitAndClose();
    }


}
