package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.db.user.UserDiscountAlertReportDO;
import com.addx.iotcamera.bean.exception.ParamException;
import com.addx.iotcamera.dao.user.IUserDiscountAlertReportDAO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UserDiscountAlertReportServiceTest {

    @InjectMocks
    private UserDiscountAlertReportService userDiscountAlertReportService;

    @Mock
    private IUserDiscountAlertReportDAO userDiscountAlertReportDAO;

    @Test
    @DisplayName("插入记录")
    public void test_insertUserDiscountAlertReport() {
        when(userDiscountAlertReportDAO.insertUserDiscountAlertReport(any(UserDiscountAlertReportDO.class))).thenReturn(1);

        userDiscountAlertReportService.insertUserEjectReport(new UserDiscountAlertReportDO());

        verify(userDiscountAlertReportDAO, times(1)).insertUserDiscountAlertReport(any(UserDiscountAlertReportDO.class));
    }

    @Test
    @DisplayName("查询用户弹出报告")
    public void test_queryUserEjectReportDOLast() {
        // 模拟 DAO 层行为
        UserDiscountAlertReportDO expectedReport = new UserDiscountAlertReportDO();
        when(userDiscountAlertReportDAO.queryByUserId(1)).thenReturn(expectedReport);

        // 调用方法
        UserDiscountAlertReportDO actualReport = userDiscountAlertReportService.queryUserEjectReportDOLast(1);

        // 验证结果
        Assert.assertEquals(expectedReport, actualReport);
    }

    @Test
    @DisplayName("更新报告 - 有效 ID")
    public void testUpdateReport_ValidId() {
        // 模拟 DAO 层行为
        Long id = 1L;
        UserDiscountAlertReportDO reportDO = new UserDiscountAlertReportDO();
        when(userDiscountAlertReportDAO.queryById(id)).thenReturn(reportDO);

        // 调用方法
        userDiscountAlertReportService.updateReport(id);

        // 验证 DAO 层是否被正确调用
        verify(userDiscountAlertReportDAO, times(1)).appReportUserDiscountAlert(id);
    }

    @Test(expected = ParamException.class)
    @DisplayName("更新报告 - 无效 ID")
    public void testUpdateReport_InvalidId() {
        // 模拟 DAO 层行为
        Long id = 2L;
        when(userDiscountAlertReportDAO.queryById(id)).thenReturn(null);

        // 调用方法并验证异常抛出
        userDiscountAlertReportService.updateReport(id);
    }
}