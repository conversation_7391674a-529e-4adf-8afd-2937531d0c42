package com.addx.iotcamera.service.deviceplatform.alexa.safemo;

import com.addx.iotcamera.bean.app.P2PTicketResponse;
import com.addx.iotcamera.bean.db.device.DeviceServerAllocDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.TwilioConfig;
import com.addx.iotcamera.kiss.KissFinder;
import com.addx.iotcamera.kiss.bean.IceServer;
import com.addx.iotcamera.kiss.node.KissNode;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.device.DeviceServerAllocService;
import com.addx.iotcamera.service.live.TwilioService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AlexaSafemoLiveServiceTest {

    @InjectMocks
    AlexaSafemoLiveService alexaSafemoLiveService;
    @Mock
    private IKissService kissService;
    @Mock
    private KissFinder kissSafeRTCFinder;
    @Mock
    private UserService userService;
    @Mock
    private TwilioService twilioService;
    @Mock
    private TwilioConfig twilioConfig;
    @Setter
    @Value("${webrtc.ticket.validDurationMillis}")
    private Long webrtcTicketValidDurationMillis;
    @Mock
    private DeviceServerAllocService deviceServerAllocService;

    @Test
    public void getWebrtcTicketForSafemo() {
        when(deviceServerAllocService.queryBySn(anyString())).thenAnswer(it -> {
            return new DeviceServerAllocDO().setSn(it.getArgument(0));
        });
//        String bxSerialNumber = "1";
//        String cxSerialNumber = bxSerialNumber + "-" + "1";
        String bxSerialNumber = "1";
        String cxSerialNumber = "1";
        Integer adminUserId = 1;
        User user = new User().setCountryNo("cn");
        Set<String> snSet = new HashSet<>();
        snSet.add(bxSerialNumber);
        List<IceServer> viewerIceServer = new ArrayList<>();
        viewerIceServer.add(new IceServer());
        KissNode kissNode = new KissNode();
        ReflectionTestUtils.setField(alexaSafemoLiveService, "webrtcTicketValidDurationMillis", 1000L);

        // 条件1
        when(twilioConfig.getWhiteSnSet()).thenReturn(snSet);
        when(twilioService.getIceServerList()).thenReturn(viewerIceServer);
        when(userService.queryUserById(adminUserId)).thenReturn(user);
        when(kissSafeRTCFinder.chooseWebsocketNodeBNAndCountryNo(eq(bxSerialNumber), any())).thenReturn(kissNode);
        P2PTicketResponse webrtcTicketForSafemo = alexaSafemoLiveService.getWebrtcTicketForSafemo(adminUserId, bxSerialNumber, cxSerialNumber, "1");
        Assert.assertNotNull(webrtcTicketForSafemo);

        // 条件2
        when(twilioConfig.getWhiteSnSet()).thenReturn(new HashSet<>());
        when(twilioService.getIceServerList()).thenReturn(new ArrayList<>());
        when(kissService.chooseTurnServer(bxSerialNumber, adminUserId + "")).thenReturn(viewerIceServer);
        webrtcTicketForSafemo = alexaSafemoLiveService.getWebrtcTicketForSafemo(adminUserId, bxSerialNumber, cxSerialNumber, "1");
        Assert.assertNotNull(webrtcTicketForSafemo);

        // 异常
        when(userService.queryUserById(adminUserId)).thenReturn(null);
        Assert.assertThrows(RuntimeException.class, () -> alexaSafemoLiveService.getWebrtcTicketForSafemo(adminUserId, bxSerialNumber, cxSerialNumber, "1"));
    }

}