package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.device_msg.DeviceMsgSrc;
import com.addx.iotcamera.mqtt.MqttConsumer;
import com.addx.iotcamera.mqtt.handler.extend.*;
import com.addx.iotcamera.service.device_msg.DeviceMsgSrcManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class MqttConsumerV2Test {

//    @InjectMocks
//    private MqttConsumer mqttConsumer;

    @Mock
    private ThreadPoolTaskExecutor executor;
    @Mock
    private ThreadPoolExecutor threadPoolExecutor;
    @Mock
    BlockingQueue<Runnable> executorQueue;

    @Mock
    private GlobalLogService globalLogService;
    @Mock
    private DeviceMsgSrcManager deviceMsgSrcManager;
    @Mock
    CmdAckHandler cmdAckHandler;
    @Mock
    ConnectionHandler connectionHandler;
    @Mock
    RequestHandler requestHandler;
    @Mock
    SettingAckHandler settingAckHandler;
    @Mock
    StatusHandler statusHandler;
    @Mock
    WebrtcAckHandler webrtcAckHandler;

    @Before
    public void before() {
        doAnswer(it -> {
            Runnable runnable = it.getArgument(0);
            if (runnable != null) runnable.run();
            return null;
        }).when(executor).execute(any());
        when(executor.getThreadPoolExecutor()).thenReturn(threadPoolExecutor);
        when(threadPoolExecutor.getQueue()).thenReturn(executorQueue);
        when(executorQueue.size()).thenReturn(10);

        when(cmdAckHandler.type()).thenReturn(new CmdAckHandler().type());
        when(connectionHandler.type()).thenReturn(new ConnectionHandler().type());
        when(requestHandler.type()).thenReturn(new RequestHandler().type());
        when(settingAckHandler.type()).thenReturn(new SettingAckHandler().type());
        when(statusHandler.type()).thenReturn(new StatusHandler().type());
        when(webrtcAckHandler.type()).thenReturn(new WebrtcAckHandler().type());
    }

    @Test
    public void test() {
        MqttConsumer mqttConsumer = new MqttConsumer(cmdAckHandler, connectionHandler, requestHandler, settingAckHandler, statusHandler, webrtcAckHandler);
        mqttConsumer.setGlobalLogService(globalLogService);
        mqttConsumer.setDeviceMsgSrcManager(deviceMsgSrcManager);
        mqttConsumer.setExecutor(executor);
        {
            String topic = "eaveye/device/b046bc7c90b8cf5a704edc1fc1aae619/connection";
            String payload = "{\"time\":1713254619,\"id\":1,\"value\":\"expected disconnected\",\"reason\":3,\"battery\":22}";
            mqttConsumer.consume(topic, payload, DeviceMsgSrc.IOT_MQTT);
        }
        {
            String topic = "eaveye/device/b046bc7c90b8cf5a704edc1fc1aae619/request";
            String payload = "{\"name\":\"reportEvent\",\"time\":1713254608478,\"id\":0,\"value\":{\"event\":1,\"videoUploadType\":1,\"traceId\":\"010170201713254608N8Zq4bf4127\",\"motionType\":\"video\",\"channel\":\"single\",\"isValidTrigger\":1,\"battery\":100,\"chargingMode\":0}}";
            mqttConsumer.consume(topic, payload, DeviceMsgSrc.IOT_HTTP);
        }
        {
            String topic = "eaveye/device/b046bc7c90b8cf5a704edc1fc1aae619/cmd_ack";
            String payload = "{\"code\":-1,\"id\":\"cmd:837a99e4c6b6405996899e87f7caec34\",\"method\":\"CMD_ACK\",\"name\":\"set\",\"senderClientId\":\"e5ad6b78e67d56d1221fda7f8fe19b20\",\"time\":1713254685}";
            mqttConsumer.consume(topic, payload, DeviceMsgSrc.KISS_WS);
        }

    }
}
