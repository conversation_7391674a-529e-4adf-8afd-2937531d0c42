package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.db.user.UserEjectReportDO;
import com.addx.iotcamera.bean.exception.ParamException;
import com.addx.iotcamera.dao.user.IUserEjectReportDAO;
import com.addx.iotcamera.enums.user.UserEjectTypeEnum;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.Instant;

import static org.mockito.Mockito.*;
@RunWith(MockitoJUnitRunner.class)
public class UserEjectReportServiceTest {
    @InjectMocks
    private UserEjectReportService userEjectReportService;
    @Mock
    private IUserEjectReportDAO iUserEjectReportDAO;

    @Test
    @DisplayName("插入记录")
    public void test_insertUserEjectReport(){
        when(iUserEjectReportDAO.insertUserEjectReport(any())).thenReturn(1);
        userEjectReportService.insertUserEjectReport(new UserEjectReportDO());
        verify(iUserEjectReportDAO,times(1)).insertUserEjectReport(any());
    }


    @Test
    @DisplayName("弹出结果查询")
    public void test_queryUserEjectReportDOLast(){
        when(iUserEjectReportDAO.queryByUserId(1,UserEjectTypeEnum.TIER_FREE.getCode(), null)).thenReturn(new UserEjectReportDO());
        UserEjectReportDO exceptedResult = new UserEjectReportDO();
        UserEjectReportDO actualResult = userEjectReportService.queryUserEjectReportDOLast(1, UserEjectTypeEnum.TIER_FREE, null);
        Assert.assertEquals(exceptedResult,actualResult);
    }


    @Test
    @DisplayName("app 上报弹出结果")
    public void testUpdateReport_ValidId() {
        Long id = 1L;
        UserEjectReportDO reportDO = new UserEjectReportDO();
        when(iUserEjectReportDAO.queryById(id)).thenReturn(reportDO);

        userEjectReportService.updateReport(id);

        verify(iUserEjectReportDAO, times(1)).appReportUserEject(id);
    }

    @Test(expected = ParamException.class)
    public void testUpdateReport_InvalidId() {
        Long id = 2L;
        when(iUserEjectReportDAO.queryById(id)).thenReturn(null);

        userEjectReportService.updateReport(id);
    }



    @Test
    public void test_updateReminderCountForOps(){
        when(iUserEjectReportDAO.updateReminderCountForOps(any(),any(),anyInt())).thenReturn(1);
        Boolean actualResult = userEjectReportService.updateReminderCountForOps(1L, Instant.now().getEpochSecond(),1);
        Assert.assertEquals(true,actualResult);
    }

    @Test
    public void test_deleteReminderCountForOps(){
        when(iUserEjectReportDAO.deleteReminderCountForOps(anyInt())).thenReturn(1);
        Boolean actualResult = userEjectReportService.deleteReminderCountForOps(1);
        Assert.assertEquals(true,actualResult);
    }

}
