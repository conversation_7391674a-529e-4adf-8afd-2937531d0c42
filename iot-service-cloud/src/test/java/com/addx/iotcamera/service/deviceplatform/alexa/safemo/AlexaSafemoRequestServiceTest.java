package com.addx.iotcamera.service.deviceplatform.alexa.safemo;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AlexaSafemoRequestServiceTest {

    @InjectMocks
    AlexaSafemoRequestService alexaSafemoRequestService;

    @Mock
    AlexaSafemoMsgProcessService alexaSafemoMsgProcessService;

    @Mock
    AlexaSafemoMsgDataService alexaSafemoMsgDataService;

    @Mock
    AlexaSafemoSendMsgService alexaSafemoSendMsgService;

    @Test
    public void sendQueryDeviceListRequest() {
        Integer adminUserId = 1;
        alexaSafemoRequestService.sendQueryDeviceListRequest(1);
    }

    @Test
    public void sendQueryCxDeviceInfoAndStatusRequest() {
        Integer adminUserId = 1;
        String cxSerialNumber = "1";
        alexaSafemoRequestService.sendQueryCxDeviceInfoAndStatusRequest(adminUserId, cxSerialNumber);
    }

    @Test
    public void sendAdminLinkToAlexaMsg() {
        String messageId = "1";
        Integer adminUserId = 1;
        Boolean adminLinkToAlexa = true;

        alexaSafemoRequestService.sendAdminLinkToAlexaMsg(messageId, adminUserId, adminLinkToAlexa);
    }

    @Test
    public void sendSwitchDeviceCodecRequest() {
        Integer adminUserId = 1;
        alexaSafemoRequestService.sendSwitchDeviceCodecRequest(adminUserId);
    }

    @Test
    public void getAdminUserIdJSON() {
        Integer adminUserId = 1;
        alexaSafemoRequestService.getAdminUserIdJSON(adminUserId);
    }
}
