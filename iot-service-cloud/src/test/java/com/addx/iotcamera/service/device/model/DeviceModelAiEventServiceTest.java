

package com.addx.iotcamera.service.device.model;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceModelAiEventServiceTest {

    @Mock
    private com.addx.iotcamera.dao.model.IDeviceModelEventDAO mockIDeviceModelEventDAO;

    @InjectMocks
    private com.addx.iotcamera.service.device.model.DeviceModelEventService deviceModelEventServiceUnderTest;

    @Test
    public void testQueryDeviceModelEvent() throws Exception {
        // Setup
        // Configure IDeviceModelEventDAO.queryDeviceModelEvent(...).
        final com.addx.iotcamera.bean.device.model.DeviceModelAISupportDo deviceModelAISupportDo = new com.addx.iotcamera.bean.device.model.DeviceModelAISupportDo();
        deviceModelAISupportDo.setModelNo("modelNo");
        deviceModelAISupportDo.setEvent("event,bird");
        when(mockIDeviceModelEventDAO.queryDeviceModelEvent("modelNo")).thenReturn(deviceModelAISupportDo);

        // Run the test
        final java.util.Set<java.lang.String> result = deviceModelEventServiceUnderTest.queryDeviceModelEvent("modelNo");

        // Verify the results
        assertEquals(new java.util.HashSet<>(java.util.Arrays.asList("event")), result);
    }
}

