package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.dao.model.IDeviceModelConfigDAO;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.function.Consumer;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceModelTest {

    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private IDeviceModelConfigDAO iDeviceModelConfigDAO;
    @InjectMocks
    private DeviceModelConfigService deviceModelConfigService;

    @Before
    public void before() {
    }

    private static DeviceModel getDefaultDeviceModel() {
        return new DeviceModel() {{
            setStreamProtocol(null);
            setAudioCodectype(null);
            setKeepAliveProtocol(null);
            setCanStandby(false);
            setDevicePersonDetect(false);
            setCanRotate(false);
            setSupportMotionTrack(false);
            setSupportFrequency(false);
            setAntiDisassemblyAlarm(false);
        }};
    }

    @DisplayName("查询设备型号配置")
    @Test
    public void test_queryDeviceModelConfig() {
        final String sn = OpenApiUtil.shortUUID();
        final String modelNo = OpenApiUtil.shortUUID();
        when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(modelNo);
        {
            when(iDeviceModelConfigDAO.queryDeviceModelConfig(modelNo)).thenReturn(null);
            final DeviceModel deviceModel = deviceModelConfigService.queryDeviceModelConfig(sn);
            Assert.assertNull(deviceModel);
        }
        {
            when(iDeviceModelConfigDAO.queryDeviceModelConfig(modelNo)).thenReturn(getDefaultDeviceModel());
            final DeviceModel deviceModel = deviceModelConfigService.queryDeviceModelConfig(sn);
            Assert.assertNotNull(deviceModel);
        }
    }

    @DisplayName("校验deviceModel是否会跟随deviceSupport修改")
    @Test
    public void test_queryDeviceModelConfig_field_follow_deviceSupport() {
        field_follow_deviceSupport(it -> it.setStreamProtocol("abc"), it -> it.setSupportStreamProtocol("abc"));
        field_follow_deviceSupport(it -> it.setAudioCodectype("def"), it -> it.setSupportAudioCodectype("def"));
        field_follow_deviceSupport(it -> it.setKeepAliveProtocol("hij"), it -> it.setSupportKeepAliveProtocol("hij"));
        field_follow_deviceSupport(it -> it.setCanStandby(true), it -> it.setSupportCanStandby(1));
        field_follow_deviceSupport(it -> it.setDevicePersonDetect(true), it -> it.setSupportDevicePersonDetect(1));
        field_follow_deviceSupport(it -> it.setCanRotate(true), it -> it.setCanRotate(1));
        field_follow_deviceSupport(it -> it.setSupportMotionTrack(true), it -> it.setSupportMotionTrack(1));
        field_follow_deviceSupport(it -> it.setSupportFrequency(true), it -> it.setSupportFrequency(1));
        field_follow_deviceSupport(it -> it.setAntiDisassemblyAlarm(true), it -> it.setAntiDisassemblyAlarm(1));
    }

    private void field_follow_deviceSupport(Consumer<DeviceModel> handleDeviceModel, Consumer<CloudDeviceSupport> handleDeviceSupport) {
        final String sn = OpenApiUtil.shortUUID();
        final String modelNo = OpenApiUtil.shortUUID();
        when(deviceManualService.getModelNoBySerialNumber(sn)).thenReturn(modelNo);
        when(iDeviceModelConfigDAO.queryDeviceModelConfig(modelNo)).thenAnswer(it -> getDefaultDeviceModel());
        final CloudDeviceSupport cloudDeviceSupport = LocalDeviceSupportTest.getDefaultDeviceSupport();
        handleDeviceSupport.accept(cloudDeviceSupport);
        when(deviceInfoService.getRowDeviceSupport(sn)).thenReturn(cloudDeviceSupport);
        final DeviceModel deviceModel = deviceModelConfigService.queryDeviceModelConfig(sn);
        Assert.assertNotNull(deviceModel);
        final DeviceModel expectDeviceModel = new DeviceModel();
        handleDeviceModel.accept(expectDeviceModel);
        Assert.assertEquals(expectDeviceModel, deviceModel);
    }
}
