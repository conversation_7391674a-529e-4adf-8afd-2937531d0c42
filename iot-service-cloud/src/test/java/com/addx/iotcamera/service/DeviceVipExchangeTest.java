package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.openapi.*;
import com.addx.iotcamera.dao.openapi.PaasVipExchangeDAO;
import com.addx.iotcamera.enums.PaasVipType;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.openapi.*;
import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.addx.iotcamera.bean.openapi.PaasVipLevel.EXCHANGE_BASIC;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceVipExchangeTest {

    @InjectMocks
    private PaasVipExchangeService paasVipExchangeService;
    @Mock
    private PaasVipProductService paasVipProductService;
    @Mock
    private PaasVipExchangeDAO paasVipExchangeDAO;
    @Mock
    private UserService userService;
    @Mock
    private VipService paasVipService;
    @Mock
    private DeviceVipService deviceVipService;
    @Mock
    private OpenApiConfigService openApiConfigService;
    @Mock
    private PaasUserVipService paasUserVipService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private DeviceSettingService deviceSettingService;

//    private TestHelper testHelper;

    @Before
    public void init() {
//        testHelper = TestHelper.getInstanceByLocal();
//        testHelper = TestHelper.getInstanceByEnv("staging");
//        testHelper = TestHelper.getInstanceByEnv("staging-eu");
//        testHelper = TestHelper.getInstanceByEnv("staging-us");
//        final DeviceVipService deviceVipServiceReal = new DeviceVipService();
//        deviceVipServiceReal.setDeviceVipDAO(testHelper.getMapper(DeviceVipDAO.class));
//        paasVipExchangeService.setDeviceVipService(deviceVipServiceReal);
//        paasVipExchangeService.setPaasVipExchangeDAO(testHelper.getMapper(PaasVipExchangeDAO.class));
        User user = new User();
        user.setId(99999);
        when(userService.queryUserById(any())).thenReturn(user);
        when(userService.queryByTenantIdAndThirdUserId(anyString(), anyString())).thenReturn(user);
        when(deviceVipService.updateDeviceVip(any(), any())).thenAnswer(invocation -> {
            DeviceVipUpdate update = invocation.getArgument(0);
            return Result.Success();
        });
        when(paasVipProductService.queryAllPaasVipProduct()).thenReturn(Arrays.asList(
                new PaasVipProduct().setId("0").setVipLevel(0),
                new PaasVipProduct().setId("1").setVipLevel(1),
                new PaasVipProduct().setId("2").setVipLevel(2),
                new PaasVipProduct().setId("3").setVipLevel(3),
                new PaasVipProduct().setId("e2m03").setVipLevel(EXCHANGE_BASIC.getCode()).setMonth(3).setLookBackDays(30).setStorage(10000L)
        ));
        Map<String, PaasVipExchangeCode> codeMap = new LinkedHashMap<>();
        when(paasVipExchangeDAO.saveExchangeBatch(any())).thenAnswer(it -> 1);
        when(paasVipExchangeDAO.saveExchangeCodes(any())).thenAnswer(it -> {
            final List<PaasVipExchangeCode> list = it.getArgument(0);
            for (final PaasVipExchangeCode code : list) {
                codeMap.put(code.getCode(), code);
            }
            return list.size();
        });

        when(paasVipExchangeDAO.queryCodesByBatchId(anyString())).thenAnswer(it -> new ArrayList<>(codeMap.keySet()));
        when(paasVipExchangeDAO.queryByCode(anyString())).thenAnswer(it -> codeMap.get(it.getArgument(0)));
        when(paasVipExchangeDAO.useExchangeCode(anyString(), any(), any(), any(), any())).thenAnswer(it -> {
            final String code = it.getArgument(0);
            return codeMap.containsKey(code) ? 1 : 0;
        });
    }

    @After
    public void onAfter() {
//        testHelper.commitAndClose();
    }

    //    @Test
    public void test_batchCreateExchangeCode() {
        int expireSeconds = 3600 * 24 * 365;

        PaasVipExchangeBatchCreate input = new PaasVipExchangeBatchCreate()
//                .setTenantId("paastest")
                .setTenantId("longse")
                .setRemark("测试批量生成设备维度兑换码_" + new Date())
                .setOperatorName("unit-test")
                .setCreateTime(new Date())
                .setList(Arrays.asList(
                        new PaasVipExchangeBatchCreate.Item().setExpireSeconds(expireSeconds)
                                .setProductId("e1m01").setNum(10),
                        new PaasVipExchangeBatchCreate.Item().setExpireSeconds(expireSeconds)
                                .setProductId("e1m03").setNum(100),
                        new PaasVipExchangeBatchCreate.Item().setExpireSeconds(expireSeconds)
                                .setProductId("e2m12").setNum(100),
                        new PaasVipExchangeBatchCreate.Item().setExpireSeconds(expireSeconds)
                                .setProductId("e3m03").setNum(195)
                ));
        Result<JSONObject> result = paasVipExchangeService.batchCreateExchangeCode(input);
        log.info("useExchangeCode:{}", JSON.toJSONString(result));
        Assert.assertEquals((Integer) 0, result.getResult());
        Assert.assertEquals((Integer) 405, result.getData().getInteger("num"));
    }

    @Test
    public void test_useExchangeCode() {
        int expireSeconds = 3600 * 24 * 365;

        PaasVipExchangeBatchCreate input = new PaasVipExchangeBatchCreate()
                .setTenantId("paastest")
                .setRemark("测试批量生成设备维度兑换码_" + new Date())
                .setOperatorName("unit-test")
                .setCreateTime(new Date())
                .setType(PaasVipType.DEVICE_VIP)
                .setList(Arrays.asList(
                        new PaasVipExchangeBatchCreate.Item().setExpireSeconds(expireSeconds)
                                .setProductId("e2m03").setNum(2)
                ));
        Result<JSONObject> result = paasVipExchangeService.batchCreateExchangeCode(input);
        log.info("batchCreateExchangeCode:{}", JSON.toJSONString(result));
        Assert.assertEquals((Integer) 0, result.getResult());
        Assert.assertEquals((Integer) 2, result.getData().getInteger("num"));
        String batchId = result.getData().getString("batchId");

        List<String> codes = paasVipExchangeService.queryCodesByBatchId(batchId);
        Assert.assertEquals(2, codes.size());

        PaasVipExchangeCodeUse use = new PaasVipExchangeCodeUse().setCode(codes.get(0))
                .setUseTime(new Date()).setTenantId("paastest")
                .setUserId("zyj_test_1645698061")
                .setType(PaasVipType.DEVICE_VIP)
                .setSerialNumber("sn_" + System.currentTimeMillis());
        Result result2 = paasVipExchangeService.useExchangeCode(use);
        log.info("useExchangeCode:{}", JSON.toJSONString(result2));
        Assert.assertEquals((Integer) 0, result2.getResult());
        {
            when(deviceVipService.updateDeviceVip(any(), any())).thenReturn(Result.Failure(""));
            Result result4 = paasVipExchangeService.useExchangeCode(use);
            Assert.assertNotEquals((Integer) 0, result4.getResult());
        }

        PaasVipExchangeCodeUse use1 = new PaasVipExchangeCodeUse().setCode(codes.get(0))
                .setUseTime(new Date()).setTenantId("paastest")
                .setUserId("zyj_test_1645698061")
                .setType(PaasVipType.USER_VIP)
                .setSerialNumber("sn_" + System.currentTimeMillis());
        PaasVipExchangeCode code = new PaasVipExchangeCode();
        code.setCode("1");
        code.setStatus(PaasVipExchangeCode.Status.NO_USE.getCode());
        code.setTenantId("paastest");
        code.setType(PaasVipType.USER_VIP.getCode());
        code.setExpiredTime(DateUtils.getDateAfter(new Date(), 2));
        when(paasVipExchangeDAO.queryByCode(anyString())).thenReturn(code);
        JSONObject jsonObject = new JSONObject();
        List<String> list = ImmutableList.of("1");
        jsonObject.fluentPut("deviceSns", list);
        when(deviceInfoService.checkIf4GDeviceHasOfficialSimCard(anyString())).thenReturn(true);
        when(paasUserVipService.updatePaasUserVip(any())).thenReturn(new Result(0, "ok", jsonObject));

        Result result3 = paasVipExchangeService.useExchangeCode(use1);
        log.info("useExchangeCode:{}", JSON.toJSONString(result3));
        Assert.assertEquals((Integer) 0, result3.getResult());
    }

    //    @Test
    public void test_build_url() {
        // 'gkKwXRizYawazyGSnjmBg5','ijJdsi+RTy6LptSCQ+HNfw==','longse'
        String signedUrl = OpenApiAuthService.createSignedUrl(
                "https://api-stage.addx.live/open-api/device-vip/exchange-code",
                "gkKwXRizYawazyGSnjmBg5",
                "ijJdsi+RTy6LptSCQ+HNfw==");
        String curlCmd = "curl -X POST '" + signedUrl + "' -H 'Content-Type:application/json'" +
                " -d '{\"code\":\"26PYA85XR8KXM7G2\",\"serialNumber\":\"" + OpenApiUtil.shortUUID() + "\",\"tenantId\":\"longse\"}'";
        System.out.println(curlCmd);
    }
}
