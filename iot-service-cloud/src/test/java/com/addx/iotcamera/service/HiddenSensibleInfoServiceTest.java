package com.addx.iotcamera.service;

import com.addx.iotcamera.testutil.TestHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import static com.addx.iotcamera.service.HiddenSensibleInfoService.HIDDEN_VALUE;

@RunWith(JUnit4.class)
public class HiddenSensibleInfoServiceTest {

    @Test
    public void test() {

        HiddenSensibleInfoService service = TestHelper.loadPropertySource(HiddenSensibleInfoService.class);

        service.hideSensitiveFields("", "{}");
        service.hideSensitiveFields(null, "{}");
        service.hideSensitiveFields("", "null");
        {
            String requestUrl = "https://api-staging-us.addx.live/account/login";
            String reqBody = "{\"app\":{\"bundle\":\"addx.ai.vicoo.staging\",\"channelId\":1000,\"appBuild\":\"staging20220223u18501e24f9\",\"appName\":\"VicoHome\",\"tenantId\":\"vicoo\",\"countlyId\":\"\",\"version\":14260,\"appType\":\"iOS\"},\"loginType\":0,\"password\":\"123456qQ\",\"code\":\"\",\"msgType\":202,\"email\":\"<EMAIL>\",\"msgToken\":\"5b74af171b23ead6d1e1f23d8c7ffaf210d67753af3085b6d85fbe355edee033\",\"requestId\":\"49d7237e1e56d781d34932787db67d50\",\"countryNo\":\"US\",\"language\":\"pt\"}";
            String reqBody2 = service.hideSensitiveFields(requestUrl, reqBody);
            compareTwoJson(reqBody, reqBody2);
        }
        {
            String requestUrl = "https://api-staging-us.vicohome.io/user/setusertoken";
            String reqBody = "{\"app\":{\"apiVersion\":\"v1\",\"appName\":\"VicoHome Stage\",\"appType\":\"Android\",\"bundle\":\"com.smartaddx.vicohome\",\"channelId\":0,\"tenantId\":\"vicoo\",\"version\":*********,\"versionName\":\"2.10.1\"},\"countryNo\":\"US\",\"language\":\"zh\",\"msgToken\":\"djdxjybgcJE:APA91bEqq8TXnVUvgj_t2aESewGfzbVNjCuZRPgnSMGlP5eg8X8UQDHaFDr0cLQVv-Jl0I1-Pmd9lJitc7JhauNpjVyx2UEfERebTF3MdTvyraQ8PCkE3oSBxc29Sqp7BP8y3KEUgZSz\",\"msgType\":\"101\"}";
            String reqBody2 = service.hideSensitiveFields(requestUrl, reqBody);
            compareTwoJson(reqBody, reqBody2);
        }
    }

    private void compareTwoJson(String reqBody, String reqBody2) {
        JSONObject obj1 = JSON.parseObject(reqBody);
        JSONObject obj2 = JSON.parseObject(reqBody2);
        for (String key : obj1.keySet()) {
            if ("password".equals(key)) {
                Assert.assertEquals(HIDDEN_VALUE, obj2.get(key));
            } else {
                Assert.assertEquals(obj1.get(key), obj2.get(key));
            }
        }
    }

}
