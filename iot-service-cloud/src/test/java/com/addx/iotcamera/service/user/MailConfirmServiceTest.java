package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.apollo.MailSend;
import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.MailConfirmRequest;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.config.apollo.MailSendConfig;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.dao.IMailConfirmDAO;
import com.addx.iotcamera.enums.SendEmailTypeEnums;
import com.addx.iotcamera.service.MailConfirmService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.util.ConfigCenterUtil;
import com.aliyuncs.exceptions.ClientException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static com.addx.iotcamera.controller.auth.AuthController.CONFIRM_CODE_PERIOD;
import static org.addx.iot.common.constant.AppConstants.*;
import static org.addx.iot.common.enums.ResultCollection.INVALID_PHONE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class MailConfirmServiceTest {
    @InjectMocks
    private MailConfirmService mailConfirmService;

    @Mock
    private IMailConfirmDAO mailConfirmDAO;
    
    @Mock
    private MailSendConfig mailSendConfig;
    
    @Mock
    private TenantTierConfig tenantTierConfig;
    
    @Mock
    private ConfigCenterUtil configCenterUtil;
    
    @Mock
    private AppAccountConfig appAccountConfig;
    
    @Mock
    private UserService userService;


    @Test
    @DisplayName("发送验证码-不重发验证码")
    public void test_sendMailConfirmType() throws ClientException {
        MailConfirmRequest confirmRequest = this.initMailConfirmRequest();
        MailConfirmRequest storedConfirm = new MailConfirmRequest();
        storedConfirm.setActiveTime(PhosUtils.getUTCStamp() - 30);
        when(mailConfirmDAO.getMailConfirm(any())).thenReturn(storedConfirm);

        Result expectedResult = Result.Success();
        Result actualResult = mailConfirmService.sendMailConfirmType(confirmRequest, SendEmailTypeEnums.RESET);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    @DisplayName("发送验证码-之前无发送验证码记录")
    public void test_sendMailConfirmType_noStoreMailConfirm() throws ClientException {
        MailConfirmRequest confirmRequest = this.initMailConfirmRequest();

        when(mailConfirmDAO.getMailConfirm(any())).thenReturn(null);
        when(mailConfirmDAO.insertMailConfirm(any())).thenReturn(1);
        
        when(configCenterUtil.queryCopyRight(TENANTID_GUARD,confirmRequest.getApp())).thenReturn("copywrite");

        EmailAccount emailAccount = new EmailAccount();
        emailAccount.setAccount("account");
        emailAccount.setPassword("password");
        when(appAccountConfig.queryEmailAccount(any())).thenReturn(emailAccount);
        
        when(mailSendConfig.getConfig()).thenReturn(this.initMailSendConfig());
        
        Result expectedResult = Result.Failure("send error");
        Result actualResult = mailConfirmService.sendMailConfirmType(confirmRequest, SendEmailTypeEnums.RESET);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("发送验证码-之前有发送验证码记录-超过重复时间")
    public void test_sendMailConfirmType_hasStoreMailConfirm_expire() throws ClientException {
        MailConfirmRequest confirmRequest = this.initMailConfirmRequest();

        MailConfirmRequest storedConfirm = new MailConfirmRequest();
        storedConfirm.setActiveTime(PhosUtils.getUTCStamp() - CONFIRM_CODE_PERIOD - 100);
        when(mailConfirmDAO.getMailConfirm(any())).thenReturn(storedConfirm);
        when(mailConfirmDAO.insertMailConfirm(any())).thenReturn(1);

        when(configCenterUtil.queryCopyRight(TENANTID_GUARD,confirmRequest.getApp())).thenReturn("copywrite");

        EmailAccount emailAccount = new EmailAccount();
        emailAccount.setAccount("account");
        emailAccount.setPassword("password");
        when(appAccountConfig.queryEmailAccount(any())).thenReturn(emailAccount);

        when(mailSendConfig.getConfig()).thenReturn(this.initMailSendConfig());

        Result expectedResult = Result.Failure("send error");
        Result actualResult = mailConfirmService.sendMailConfirmType(confirmRequest, SendEmailTypeEnums.RESET);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("发送验证码-之前有发送验证码记录-超过重复时间")
    public void test_sendMailConfirmType_hasStoreMailConfirm() throws ClientException {
        MailConfirmRequest confirmRequest = this.initMailConfirmRequest();

        MailConfirmRequest storeConfirmRequest = this.initMailConfirmRequest();
        storeConfirmRequest.setCode("code");
        storeConfirmRequest.setActiveTime(PhosUtils.getUTCStamp() - CONFIRM_CODE_PERIOD + 100);
        when(mailConfirmDAO.getMailConfirm(any())).thenReturn(storeConfirmRequest);
        when(mailConfirmDAO.insertMailConfirm(any())).thenReturn(1);

        when(configCenterUtil.queryCopyRight(TENANTID_GUARD,storeConfirmRequest.getApp())).thenReturn("copywrite");

        EmailAccount emailAccount = new EmailAccount();
        emailAccount.setAccount("account");
        emailAccount.setPassword("password");
        when(appAccountConfig.queryEmailAccount(any())).thenReturn(emailAccount);

        when(mailSendConfig.getConfig()).thenReturn(this.initMailSendConfig());

        Result expectedResult = Result.Failure("send error");
        Result actualResult = mailConfirmService.sendMailConfirmType(confirmRequest, SendEmailTypeEnums.RESET);
        Assert.assertEquals(expectedResult,actualResult);
    }
    
    
    private MailConfirmRequest initMailConfirmRequest(){
        MailConfirmRequest request = MailConfirmRequest.builder()
                .email("email")
                .build();
        request.setLanguage(APP_LANGUAGE_ZH);

        AppInfo appInfo = new AppInfo();
        appInfo.setTenantId(TENANTID_GUARD);
        appInfo.setApiVersion("v1");

        request.setApp(appInfo);

        
        return request;
    }
    
    private Map<String,Map<String,MailSend>> initMailSendConfig(){
        MailSend mailSend = new MailSend();
        mailSend.setResetTitle("title");
        mailSend.setResetContent("content");
        mailSend.setAwarenessService("vicoo");
        Map<String,MailSend> languageMailSend = Maps.newHashMap();
        languageMailSend.put(APP_LANGUAGE_ZH,mailSend);

        Map<String,Map<String,MailSend>> tenantConfig = Maps.newHashMap();
        tenantConfig.put(TENANTID_GUARD,languageMailSend);
        return tenantConfig;
    }

    @Test(expected = BaseException.class)
    public void testSendConfirmCode_WithNonExistingUser_ShouldThrowBaseException() throws ClientException {
        // Arrange
        MailConfirmRequest request = new MailConfirmRequest();
        request.setUserId(1);
        when(userService.queryUserById(1)).thenReturn(null);
        
        mailConfirmService.sendConfirmCode(request);
    }

    @Test
    public void testSendConfirmCode_WithValidUser_ShouldReturnSuccessResult() throws ClientException {
        // Arrange
        MailConfirmRequest confirmRequest = this.initMailConfirmRequest();
        confirmRequest.setUserId(1);
        
        User user = new User();
        when(userService.queryUserById(any())).thenReturn(user);
        
        MailConfirmRequest storeConfirmRequest = this.initMailConfirmRequest();
        storeConfirmRequest.setCode("code");
        storeConfirmRequest.setActiveTime(PhosUtils.getUTCStamp() - CONFIRM_CODE_PERIOD + 100);
        when(mailConfirmDAO.getMailConfirm(any())).thenReturn(storeConfirmRequest);
        when(mailConfirmDAO.insertMailConfirm(any())).thenReturn(1);

        when(configCenterUtil.queryCopyRight(TENANTID_GUARD,storeConfirmRequest.getApp())).thenReturn("copywrite");

        EmailAccount emailAccount = new EmailAccount();
        emailAccount.setAccount("account");
        emailAccount.setPassword("password");
        when(appAccountConfig.queryEmailAccount(any())).thenReturn(emailAccount);

        when(mailSendConfig.getConfig()).thenReturn(this.initMailSendConfig());

        Result expectedResult = Result.Failure("send error");
        Result actualResult = mailConfirmService.sendMailConfirmType(confirmRequest, SendEmailTypeEnums.RESET);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    @DisplayName("获取邮件title")
    public void test_getSendEmailTitle(){
        MailSend mailSend = initMailSend();
        String expectedResult = "stationShareEmailTitle";
        String actualResult = mailConfirmService.getSendEmailTitle(mailSend,SendEmailTypeEnums.SHARE_STATION_NEW);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    public void test_getSendEmailTitleV2(){
        MailSend mailSend = initMailSend();
        String expectedResult = "kbTitle";
        String actualResult = mailConfirmService.getSendEmailTitle(mailSend,SendEmailTypeEnums.USER_FEEDBACK_4_KB_APP);
        Assert.assertEquals(expectedResult,actualResult);
        String expectedResultBody = "kbContent";
        when(configCenterUtil.queryCopyRight(any(),any())).thenReturn("");
        when(tenantTierConfig.getConfig()).thenReturn(Maps.newHashMap());
        actualResult = mailConfirmService.getSendEmailBody("","",SendEmailTypeEnums.USER_FEEDBACK_4_KB_APP,mailSend,TENANTID_VICOO,new AppInfo());
        Assert.assertEquals(expectedResultBody,actualResult);

    }

    @Test
    @DisplayName("获取邮件文案body")
    public void test_getSendEmailBody(){
        MailSend mailSend = this.initMailSend();
        AppInfo appInfo = new AppInfo();
        appInfo.setApiVersion("V0");
        appInfo.setTenantId(TENANTID_VICOO);

        when(configCenterUtil.queryCopyRight(any(),any())).thenReturn("");
        when(tenantTierConfig.getConfig()).thenReturn(Maps.newHashMap());
        String expectedResult;
        String actualResult;
        {
            //分享邀请-新用户
            expectedResult = "stationShareEmailContentNew";
            actualResult = mailConfirmService.getSendEmailBody("","",SendEmailTypeEnums.SHARE_STATION_NEW,mailSend, TENANTID_VICOO,appInfo);
            Assert.assertEquals(expectedResult,actualResult);
        }
        {
            //分享邀请-已存在的用户
            expectedResult = "stationShareEmailContent";
            actualResult = mailConfirmService.getSendEmailBody("","",SendEmailTypeEnums.SHARE_STATION,mailSend, TENANTID_VICOO,appInfo);
            Assert.assertEquals(expectedResult,actualResult);
        }
    }

    private MailSend initMailSend(){
        MailSend mailSend = new MailSend();
        mailSend.setStationShareEmailContentNew("stationShareEmailContentNew");
        mailSend.setStationShareEmailContent("stationShareEmailContent");

        mailSend.setStationShareEmailTitle("stationShareEmailTitle");
        mailSend.setAwarenessService("awarenessService");

        mailSend.setUserFreeBack4KBAppTitle("kbTitle");
        mailSend.setUserFreeBack4KBAppContent("kbContent");
        return mailSend;
    }


    @Test
    @DisplayName("发送验证码")
    public void test_sendConfirmEmail() throws ClientException {
        MailConfirmRequest confirmRequest = new MailConfirmRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        confirmRequest.setApp(app);
        confirmRequest.setEmail("email");
        confirmRequest.setPhone("phone");
        String methodName;

        Result exceptedResult ;
        Result actualResult ;


        {
            //用户不存在,reset
            methodName = "resetConfirm";
            when(userService.getUserByEmailAndTenantId(any(),any(),any())).thenReturn(null);

            exceptedResult = Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");
            actualResult = mailConfirmService.sendConfirmEmail(confirmRequest,methodName);
            Assert.assertEquals(exceptedResult,actualResult);
        }

        confirmRequest.setCodeType(0);
        {
            // 注册验证码，用户已存在
            when(userService.getUserByEmailAndTenantId(any(),any(),any())).thenReturn(new User());
            methodName = "registerConfirm";
            exceptedResult = Result.Error(-1002, "ACCOUNT_IN_USE");
            actualResult = mailConfirmService.sendConfirmEmail(confirmRequest,methodName);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        confirmRequest.setCodeType(3);
        when(userService.getUserByEmailAndTenantId(any(),any(),any())).thenReturn(new User());
        {
            methodName = "resetConfirm";

            confirmRequest.setPhone("");
            //邮箱不符合规则
            exceptedResult = Result.Error(-1011, "INVALID_EMAIL");
            actualResult = mailConfirmService.sendConfirmEmail(confirmRequest,methodName);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        confirmRequest.setEmail("<EMAIL>");
        {
            confirmRequest.setPhone("phone");
            //手机号不符合规则
            exceptedResult = Result.Error(INVALID_PHONE, "INVALID_PHONE");
            actualResult = mailConfirmService.sendConfirmEmail(confirmRequest,methodName);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        confirmRequest.setPhone("");

        {
            //用户不存在,reset
            methodName = "";
            when(userService.getUserByEmailAndTenantId(any(),any(),any())).thenReturn(null);

            confirmRequest.setCodeType(SendEmailTypeEnums.SIGNIN.getCode());
            exceptedResult = Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");
            actualResult = mailConfirmService.sendConfirmEmail(confirmRequest,methodName);
            Assert.assertEquals(exceptedResult,actualResult);
        }

        {
            //用户不存在,reset
            methodName = "";
            when(userService.getUserByEmailAndTenantId(any(),any(),any())).thenReturn(new User());
            when(mailConfirmDAO.getMailConfirm(any())).thenReturn(MailConfirmRequest.builder().activeTime(PhosUtils.getUTCStamp()-30).build());
            confirmRequest.setCodeType(SendEmailTypeEnums.SIGNIN.getCode());
            exceptedResult = Result.Success();
            actualResult = mailConfirmService.sendConfirmEmail(confirmRequest,methodName);
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }
    @Test(expected = BaseException.class)
    public void test_sendConfirmEmail_exception() throws ClientException {
        MailConfirmRequest confirmRequest = new MailConfirmRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(TENANTID_VICOO);
        confirmRequest.setApp(app);
        confirmRequest.setEmail("<EMAIL>");
        confirmRequest.setCodeType(3);

        String methodName = "";

        when(userService.getUserByEmailAndTenantId(any(),any(),any())).thenReturn(new User());

        Result exceptedResult = Result.Success();
        Result actualResult = mailConfirmService.sendConfirmEmail(confirmRequest,methodName);
        Assert.assertEquals(exceptedResult,actualResult);
    }
}
