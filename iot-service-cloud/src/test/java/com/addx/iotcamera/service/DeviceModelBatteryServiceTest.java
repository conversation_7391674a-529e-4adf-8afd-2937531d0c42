package com.addx.iotcamera.service;

import com.addx.iotcamera.dao.model.IDeviceModelBatteryDAO;
import com.addx.iotcamera.service.device.model.DeviceModelBatteryService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceModelBatteryServiceTest {

    @Mock
    private IDeviceModelBatteryDAO iDeviceModelBatteryDAO;
    @InjectMocks
    private DeviceModelBatteryService deviceModelBatteryService;

    @Before
    public void before() {

    }

    @Test
    public void test_publishBatteryConfig() {
        when(iDeviceModelBatteryDAO.queryDeviceModelBatteryDO(any(), any())).thenReturn(null);
        deviceModelBatteryService.queryDeviceModelBattery("123", 456);
    }


}
