package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.LibraryRequest;
import com.addx.iotcamera.bean.app.LibraryStatusTbRequest;
import com.addx.iotcamera.bean.db.LibraryEventDO;
import com.addx.iotcamera.bean.db.LibraryStatusTb;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryStatusDAO;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class LibraryStatusServiceTest {

    @InjectMocks
    @Spy
    private LibraryStatusService libraryStatusService;

    @Mock
    private VideoSearchService videoSearchService;

    @Mock
    private IShardingLibraryDAO iShardingLibraryDAO;

    @Mock
    private IShardingLibraryStatusDAO iShardingLibraryStatusDAO;

    @Test
    public void test() throws Exception {
        when(iShardingLibraryStatusDAO.selectLibraryEventList(any())).thenReturn(Collections.singletonList(new LibraryEventDO(){{
            setVideoEvent(1L);
            setStartTime(0);
            setEndTime(1);
            setLibraryCount(1);
        }}));
        when(iShardingLibraryStatusDAO.selectLibraryStatusList(any())).thenReturn(Arrays.asList(
            LibraryStatusTb.builder().videoEvent("1").libraryId(1).traceId("trace_01").tags("a").build(),
            LibraryStatusTb.builder().videoEvent("1").libraryId(2).traceId("trace_02").doorbellTags("b").build(),
            LibraryStatusTb.builder().videoEvent("1").libraryId(3).traceId("trace_03").deviceCallEventTag("c").build()
        ));

        LibraryRequest libraryRequest = new LibraryRequest();
        libraryRequest.setUserId(1);
        libraryRequest.setStartTimestamp(1L);
        libraryStatusService.queryLibraryStatusList(libraryRequest);
        libraryStatusService.queryLibraryEventList(libraryRequest);
        libraryStatusService.queryLibraryCount(libraryRequest);
        libraryStatusService.queryLibraryEventCount(libraryRequest);

        libraryRequest = new LibraryRequest();
        libraryRequest.setUserId(1);
        libraryRequest.setEndTimestamp(1L);
        libraryStatusService.queryLibraryStatusList(libraryRequest);
        libraryStatusService.queryLibraryStatusList(libraryRequest);
        libraryStatusService.queryLibraryEventList(libraryRequest);
        libraryStatusService.queryLibraryCount(libraryRequest);
        libraryStatusService.queryLibraryEventCount(libraryRequest);
    }

    @Test
    public void testUpdateLibraryStatus() {
        Integer result = libraryStatusService.updateLibraryRead(new LibraryStatusTbRequest());
        Assert.assertTrue(result.intValue() == 0);

        result = libraryStatusService.updateLibraryRead(new LibraryStatusTbRequest(){{
            setLibraryIds("1");
        }});
        Assert.assertTrue(result.intValue() == 0);

        result = libraryStatusService.updateLibraryRead(new LibraryStatusTbRequest(){{
            setTraceIdList(Collections.singletonList("trace_01"));
        }});
        Assert.assertTrue(result.intValue() == 0);

        result = libraryStatusService.updateLibraryRead(new LibraryStatusTbRequest(){{
            setLibraryId(1);
        }});
        Assert.assertTrue(result.intValue() == 0);

        result = libraryStatusService.updateLibraryRead(new LibraryStatusTbRequest(){{
            setTraceId("trace_01");
        }});
        Assert.assertTrue(result.intValue() == 0);

        result = libraryStatusService.updateLibraryMarked(new LibraryStatusTbRequest());
        Assert.assertTrue(result.intValue() == 0);

        result = libraryStatusService.updateLibraryMarked(new LibraryStatusTbRequest(){{
            setLibraryIds("1");
        }});
        Assert.assertTrue(result.intValue() == 0);

        result = libraryStatusService.updateLibraryMarked(new LibraryStatusTbRequest(){{
            setTraceIdList(Collections.singletonList("trace_01"));
        }});
        Assert.assertTrue(result.intValue() == 0);

        result = libraryStatusService.updateLibraryMarked(new LibraryStatusTbRequest(){{
            setLibraryId(1);
        }});
        Assert.assertTrue(result.intValue() == 0);

        result = libraryStatusService.updateLibraryMarked(new LibraryStatusTbRequest(){{
            setTraceId("trace_01");
        }});
        Assert.assertTrue(result.intValue() == 0);
    }

    @Test
    public void test_useQueryResult() throws Exception {
        libraryStatusService.selectLibraryStatusByTraceIdAndUserId("trace_01", 1);
        test();
    }

    @Test
    public void test_updateLibraryStatusTags() {
        libraryStatusService.updateLibraryStatusTags("sn1", Collections.singletonList(1), new LibraryStatusTb());
        Mockito.verify(iShardingLibraryStatusDAO).updateLibraryTags(any());
    }

    @Test
    public void test_queryLastTraceIdByUserIdAndSn() {
        libraryStatusService.queryLastTraceIdByUserIdAndSn(any(), any());
    }

    @Test
    public void test_selectLibraryStatusByTraceIdAndUserId() {
        LibraryStatusTb libraryStatusTb = new LibraryStatusTb();
        libraryStatusTb.setAdminId(1);
        when(iShardingLibraryStatusDAO.selectLibraryStatusByTraceIdAndUserId("1", 1)).thenReturn(libraryStatusTb);
        when(iShardingLibraryDAO.selectLibraryByUserIdAndTraceId(1, "1")).thenReturn(null);
        LibraryStatusTb result = libraryStatusService.selectLibraryStatusByTraceIdAndUserId("1", 1);
        Assert.assertNull(result);
    }

    @Test
    public void test_queryAdminIdByUserIdAndTraceId() {
        libraryStatusService.queryAdminIdByUserIdAndTraceId(1, "1");
    }
}
