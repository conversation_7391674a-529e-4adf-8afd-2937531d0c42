package com.addx.iotcamera.service.deviceplatform.alexa.safemo;

import com.addx.iotcamera.bean.app.alexa.result.DeviceInfoAndStatusDO;
import com.addx.iotcamera.bean.domain.deviceplatform.PlatformLinkedDeviceSafemoMapDo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AlexaSafemoDeviceQueryServiceTest {

    @InjectMocks
    AlexaSafemoDeviceQueryService alexaSafemoDeviceQueryService;
    @Mock
    AlexaSafemoRequestService alexaSafemoRequestService;
    @Mock
    AlexaSafemoMsgDataService alexaSafemoMsgDataService;

    @Test
    public void queryDeviceList() {
        Integer adminUserId = 1;
        String messageId = "1";
        List<DeviceInfoAndStatusDO> deviceList = anyList();

        when(alexaSafemoRequestService.sendQueryDeviceListRequest(adminUserId)).thenReturn(messageId);
        when(alexaSafemoMsgDataService.getDeviceList(messageId)).thenReturn(deviceList);
        List<DeviceInfoAndStatusDO> deviceListFromQuery = alexaSafemoDeviceQueryService.queryDeviceList(adminUserId);
        Assert.assertNotNull(deviceListFromQuery);

        List<DeviceInfoAndStatusDO> emptyList = alexaSafemoDeviceQueryService.queryDeviceList(null);
        Assert.assertTrue(emptyList.isEmpty());
    }

    @Test
    public void queryDeviceListMap() {
        Integer adminUserId = 1;
        List<DeviceInfoAndStatusDO> deviceList = anyList();

        when(alexaSafemoDeviceQueryService.queryDeviceList(adminUserId)).thenReturn(deviceList);
        PlatformLinkedDeviceSafemoMapDo platformLinkedDeviceSafemoMapDo = alexaSafemoDeviceQueryService.queryDeviceListMap(adminUserId);
        Assert.assertNotNull(platformLinkedDeviceSafemoMapDo);

        PlatformLinkedDeviceSafemoMapDo emptyMap = alexaSafemoDeviceQueryService.queryDeviceListMap(null);
        Assert.assertTrue(emptyMap.getPlatformLinkedDevicesMap().get("alexa").isEmpty());
    }

    @Test
    public void queryDeviceInfoAndStatus() {
        Integer adminUserId = 1;
        String cxSerialNumber = "1";
        String messageId = "1";

        when(alexaSafemoRequestService.sendQueryCxDeviceInfoAndStatusRequest(adminUserId, cxSerialNumber)).thenReturn(messageId);
        when(alexaSafemoMsgDataService.getDeviceInfoAndStatus(messageId)).thenReturn(new DeviceInfoAndStatusDO());
        DeviceInfoAndStatusDO deviceInfoAndStatusDO = alexaSafemoDeviceQueryService.queryDeviceInfoAndStatus(adminUserId, cxSerialNumber);
        Assert.assertNotNull(deviceInfoAndStatusDO);
    }
}
