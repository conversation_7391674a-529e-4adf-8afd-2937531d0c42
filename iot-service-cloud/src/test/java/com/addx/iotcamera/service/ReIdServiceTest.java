package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.reid.*;
import com.addx.iotcamera.dao.MessageNotificationSettingsDao;
import com.addx.iotcamera.dynamo.dao.UserReIdDAO;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.video.ReidCacheService;
import com.addx.iotcamera.testutil.TestHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ReIdServiceTest {

    @InjectMocks
    private ReIdService reIdService;
    @Mock
    private UserReIdDAO userReIdDAO;
    //@Mock
    private MessageNotificationSettingsService messageNotificationSettingsService;
    @Mock
    private MqSender mqSender;
    @Mock
    private S3Service s3Service;

    @Value("${spring.kafka.topics.reid-update}")
    private String reIdUpdateTopic;

    @Mock
    private ReidCacheService reidCacheService;

    @Before
    public void before() {
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
//        TestHelper testHelper = TestHelper.getInstanceByEnv("staging");
//        TestHelper testHelper = TestHelper.getInstanceByEnv("staging-us");
//        TestHelper testHelper = TestHelper.getInstanceByEnv("staging-eu");

        S3Service s3Service = new S3Service();
        s3Service.setS3Client(testHelper.getS3Client());
//        when(this.s3Service.copyObject(anyString(), any(), anyString())).thenAnswer(AdditionalAnswers.delegatesTo(s3Service));

        String tableName = testHelper.getConfig().getJSONObject("dynamo").getJSONObject("userReId").getString("tableName");
//        userReIdDAO = UserReIdDAO.builder()
//                .dynamoDB(testHelper.getAmazonInit().dynamoDB())
//                .tableName(tableName).build();
//        userReIdDAO.init();
//        reIdService.setUserReIdDAO(userReIdDAO);

        MessageNotificationSettingsDao messageNotificationSettingsDao = testHelper.getMapper(MessageNotificationSettingsDao.class);
        messageNotificationSettingsService = new MessageNotificationSettingsService();
        messageNotificationSettingsService.setMessageNotificationSettingsDao(messageNotificationSettingsDao);
        reIdService.setMessageNotificationSettingsService(messageNotificationSettingsService);
//        reIdService.init();
    }

    //    private String userId = "unit_test_" + System.currentTimeMillis();
    private String userId = "950";
//    private Set<ReIdData> expectReIdDataList = new LinkedHashSet<>();

    //@Order(1)
    @Test
    public void test_save() {
        long time = System.currentTimeMillis();
        final String userId = "user_" + time;
        final String labelPrefix = "label_" + time;

        ReIdData reIdData = createReIdData(labelPrefix, 0, 1)
                .setUserId(userId).setObject(AiObjectEnum.VEHICLE.getObjectName());

        List<UserReId> userReIds = Lists.newArrayList();
        UserReId userReId = new UserReId();
        userReId.setLabelId(labelPrefix+"_0");
        userReId.setDeleted(false);
        userReIds.add(userReId);

        when(reidCacheService.queryReId(any())).thenReturn(userReIds);

        when(userReIdDAO.batchWrite(anyList(),anyList())).thenReturn(1);

        doNothing().when(reidCacheService).cleanCacheSingle(any());
        doNothing().when(reidCacheService).cleanCacheBatch(any());

        int expectedResult = 0;
        int actualResult = reIdService.save(reIdData);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    public void test_save_empty() {
        long time = System.currentTimeMillis();
        final String userId = "user_" + time;
        final String labelPrefix = "label_" + time;

        ReIdData reIdData = createReIdData(labelPrefix, 0, 1)
                .setUserId(userId).setObject(AiObjectEnum.VEHICLE.getObjectName());

        List<UserReId> userReIds = Lists.newArrayList();
        UserReId userReId = new UserReId();
        userReId.setLabelId(labelPrefix);
        userReId.setDeleted(false);
        userReIds.add(userReId);

        when(reidCacheService.queryReId(any())).thenReturn(userReIds);

        when(userReIdDAO.batchWrite(anyList(),anyList())).thenReturn(1);

        doNothing().when(reidCacheService).cleanCacheSingle(any());
        doNothing().when(reidCacheService).cleanCacheBatch(any());

        int expectedResult = 0;
        int actualResult = reIdService.save(reIdData);
        Assert.assertEquals(expectedResult, actualResult);
    }

    private static void assertReId(Map<String, UserReId> userReIdMap, String labelId, boolean pushed, boolean marked, boolean deleted) {
        UserReId userReId = userReIdMap.get(labelId);
        Assert.assertNotNull("userReId[" + labelId + "] is null!", userReId);
        Assert.assertEquals("userReId[" + labelId + "] pushed error!", pushed, userReId.getPushed());
        Assert.assertEquals("userReId[" + labelId + "] marked error!", marked, userReId.getMarked());
        Assert.assertEquals("userReId[" + labelId + "] deleted error!", deleted, userReId.getDeleted());
    }

    private ReIdData createReIdData(String lableIdPrefix, int startIndex, int endIndex) {
        List<ReIdLabel> labels = new LinkedList<>();
        int imageUrlIndex = 0;
        for (int i = startIndex; i < endIndex; i++) {
            String labelId = lableIdPrefix + "_" + i;
            List<ReIdImage> reIdImages = new LinkedList<>();
            for (int j = 0; j < 4; j++) {
//                String imgUrl = "https://addx-staging.s3.cn-north-1.amazonaws.com.cn/user_multimedia/vehicle_img/16e6e09bfe957e75f34cbcc4f677dd3c/08141630638129VjcMM16evtHYZVC_0.jpg";
                String imgUrl = "https://a4x-staging-us.s3.amazonaws.com/device_video_slice/reid/${order}.jpeg"
                        .replace("${order}", (imageUrlIndex++) + "");
                reIdImages.add(new ReIdImage().setId(labelId + "_img" + j).setUrl(imgUrl).setTimestamp((long) PhosUtils.getUTCStamp()));
            }
            labels.add(new ReIdLabel().setId(labelId).setImages(reIdImages));
        }
        return new ReIdData().setLabels(labels);
    }

    @Test
    public void test_mark() {
        ReIdUpdateVO reIdUpdateVO = new ReIdUpdateVO()
                .setUserId(userId)
                .setMarkLabelIds(Arrays.asList("25d6338f2bc14bba8c7aafc13ecbf146", "2bd825fbea754ed98d8ec34b9a312a80"));
        reIdService.update(reIdUpdateVO);
        Assert.assertTrue(true);
    }

    @Test
    public void test_queryByUserIdAndLabelIds() {
        List<UserReId> list = userReIdDAO.queryByUserIdAndLabelIds("950", Arrays.asList("label_2", "label_3"));
        Assert.assertTrue(true);
    }

    @Test
    public void test_update() {
        String json = "{\"markLabelIds\":[\"label_0\"]}";
        ReIdUpdateVO input = JSON.parseObject(json, ReIdUpdateVO.class).setUserId("950");

        when(reidCacheService.queryNotDeletedReId(any())).thenReturn(Lists.newArrayList());
        reIdService.update(input);
        Assert.assertTrue(true);
    }

    //    @Test
    public void test_delete() {
        int num = userReIdDAO.batchWrite(null, Arrays.asList(
                new UserReId().setUserId("950").setLabelId("label"),
                new UserReId().setUserId("950").setLabelId("label_0"),
                new UserReId().setUserId("950").setLabelId("label_1"),
                new UserReId().setUserId("950").setLabelId("label_2"),
                new UserReId().setUserId("950").setLabelId("label_3"),
                new UserReId().setUserId("950").setLabelId("label_4"))
        );
        Assert.assertTrue(true);
    }

    private AtomicInteger labelCount = new AtomicInteger(0);
    private AtomicInteger imageCount = new AtomicInteger(0);
    private Random random = new Random();

    public String createLabelId() {
        return "L" + labelCount.getAndIncrement();
    }

    public String createImageId() {
        return "I" + imageCount.getAndIncrement();
    }

    //    @Test
    public void test_reIdService_update() {
        List<UserReId> oldReIds = buildReIds();
        when(userReIdDAO.queryNotDeletedReId(anyString())).thenReturn(oldReIds);

        List<UserReId> userReIds = ((JSONArray) JSON.toJSON(oldReIds)).toJavaList(UserReId.class);
        List<ReIdImage> images0 = userReIds.get(0).getImages();
        ReIdUpdateVO reIdUpdate = new ReIdUpdateVO().setDeleteLabelImages(new LinkedHashMap<>());
        List<String> deleteImageIds = new LinkedList<>();
        deleteImageIds.add(images0.remove(random.nextInt(images0.size())).getId());
        deleteImageIds.add(images0.remove(random.nextInt(images0.size())).getId());
        reIdUpdate.getDeleteLabelImages().put(userReIds.get(0).getLabelId(), deleteImageIds);
        reIdService.update(reIdUpdate);
        Assert.assertTrue(true);
    }

    private List<UserReId> buildReIds() {
        return Arrays.asList(
                // 未推送过
                new UserReId().setLabelId(createLabelId()).setEventObject(AiObjectEnum.VEHICLE.getObjectName())
                        .setImages(buildImages(3)).setMarked(false).setPushed(false).setDeleted(false),
                // 推送过，标记
                new UserReId().setLabelId(createLabelId()).setEventObject(AiObjectEnum.VEHICLE.getObjectName())
                        .setImages(buildImages(5)).setMarked(true).setPushed(true).setDeleted(false),
                // 推送过，未标记
                new UserReId().setLabelId(createLabelId()).setEventObject(AiObjectEnum.VEHICLE.getObjectName())
                        .setImages(buildImages(3)).setMarked(false).setPushed(true).setDeleted(false),
                // 删除，标记
                new UserReId().setLabelId(createLabelId()).setEventObject(AiObjectEnum.VEHICLE.getObjectName())
                        .setImages(buildImages(0)).setMarked(true).setPushed(true).setDeleted(true),
                // 删除，未标记
                new UserReId().setLabelId(createLabelId()).setEventObject(AiObjectEnum.VEHICLE.getObjectName())
                        .setImages(buildImages(0)).setMarked(false).setPushed(true).setDeleted(true)
        );
    }

    private List<ReIdImage> buildImages(int num) {
        List<ReIdImage> reIdImages = new LinkedList<>();
        for (int i = 0; i < num; i++) {
            String imageId = createImageId();
            ReIdImage reIdImage = new ReIdImage().setId(imageId)
                    .setUrl("https://host/a/b/" + imageId)
                    .setTimestamp(10_0000_0000L + random.nextInt(10000));
            reIdImages.add(reIdImage);
        }
        return reIdImages;
    }

    //    @Test
    public void test_queryNotifySetting() {
        Integer userId = 82;
        String sn = "acbcb0503c7c051f0ad0afa496f76ef7";
        List<ReIdNotifySettingOption> options = reIdService.queryNotifySetting(userId, sn);
        for (ReIdNotifySettingOption option : options) {
            option.setChecked(!option.getChecked());
        }
        int updateNum = reIdService.updateNotifySetting(userId, sn, options);
        Assert.assertEquals(1, updateNum);
    }

    //    @Test
    public void test_reInitReIdCoverImage() {
        reIdService.reInitReIdCoverImage();
    }

    /**
     * 模拟reid数据
     */
//    @Test
    public void mockReIdData() {
        final TestHelper testHelper = TestHelper.getInstanceByEnv("staging-us");
        String tableName = testHelper.getConfig().getJSONObject("dynamo").getJSONObject("userReId").getString("tableName");
        final UserReIdDAO userReIdDAO = UserReIdDAO.builder()
                .dynamoDB(testHelper.getAmazonInit().dynamoDB())
                .tableName(tableName).build();
        userReIdDAO.init();
        final ReIdService reIdService = new ReIdService();
        reIdService.setUserReIdDAO(userReIdDAO);

        long time = System.currentTimeMillis();
//        final String userId = 1007865 + "";
        final String userId = 390 + "";
        final String labelPrefix = "label_" + time;
        ReIdData reIdData1 = createReIdData(labelPrefix, 0, 20)
                .setUserId(userId).setObject(AiObjectEnum.VEHICLE.getObjectName());
//        String imgUrl = "https://a4x-staging-us.s3.amazonaws.com/device_video_slice/76a9c38d1bff15806a64bd9ac46a637c/03091640228991HQUf5V3F4WcEg96/image.jpg";
//        String imgUrl = "https://a4x-staging-us.s3.amazonaws.com/device_video_slice/76a9c38d1bff15806a64bd9ac46a637c/j88fPiUkIdK8rYARKLnCR3/slice_2000_${order}_0.ts";
//        String imgUrl = "https://a4x-staging-us.s3.amazonaws.com/device_video_slice/76a9c38d1bff15806a64bd9ac46a637c/KCDDbXsDvFuInZ4dm7mQj1/img_${order}.jpeg";
//        String imgUrl = "https://a4x-staging-us.s3.amazonaws.com/device_video_slice/reid/${order}.jpeg";
//        AtomicInteger count = new AtomicInteger(0);
//        reIdData1.getLabels().forEach(label -> label.getImages().forEach(it -> it.setUrl(imgUrl.replace("${order}", count.getAndIncrement() + ""))));
        List<String> imageUrls = Arrays.asList(
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667545044quvSw8JT30lef6C_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667544686I0ByaPVbLRt9DRh_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667544319V80WBaOSEEP_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667544205JgGoxkTMMUTV7A0_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667543234dToWd06bWfn_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667542941XBo2K44QRu7_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667541194bfak1qBSlwt_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/0100786516675384313DXDKDw9UCN_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/0100786516675380277bLc316t6cv_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667537717vliuAALjSFg_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667536221pcIjjOTm0GJusNQ_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667535704mD1cRwCeEtS_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667535348j6cF7xgFaQO_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667534565UlxNjcz94R25m9y_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667534106NEji5zwFZ4EpoJt_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667533461BtOFqi28VUg8QSJ_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667533086KIW7zhbIgz15z1q_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667532873bqXAnPVFYIx_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667531476lBa2pdhS5et_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667530576waJpq4KOouqZiQ9_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667530556en7GfCFvzTt_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667529930CiBfwg7bJQD_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667529027jGK7HLOrnPI_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/01007865166752873807nwCGzHZQX_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667526958wmjdFbZei66H4ea_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667526246UJyCZgXHy5C_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667486890uVa0gBW4i3rCvmN_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667486192A1sGiBlMecd5HqE_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667483081gKe1qSdGtmX_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667482517kkk5eASu3fmA04w_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667479224DD9Xe0xZ5L1_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667478867NMuc3Re3AHnZlkG_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667478868DlbCncQzgiL_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667478454gfAP7IxXjGWqn42_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667475385dcbcT7GHV62_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/039016674748729MRJIIuMyiVou0a_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667474497tbLUdLSWWLI_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667474381OTD4aeVgWZF_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667474139ldbiQeLQp2P_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667473422HzgnAVMpnZq_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667472836Fj5r4I7MYro_gallery.jpg",
                "https://a4x-staging-us-vip-pro.s3.amazonaws.com/ai-saas-out-storage/03901667471460gzKih4ETlJa5bgz_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667470509R0I4hXsA4ub_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667470022eZxO3fIZH0k_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667469297cXW7ihWERu5_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667467158JcodeTPOFUz_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667467078bx5o1V4uI4M_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667464611t4GmzjoIeq0_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667464101l4k01451VXL_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/0100786516674624089FytxOuLrzZ_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010078651667461967F3tOz8aBGNi_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667460313dyEhmW3Yca6_gallery.jpg",
                "https://a4x-staging-us-vip-none.s3.amazonaws.com/ai-saas-out-storage/010097651667458205O1Ldkylp2J1_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/03651667454846ypKdZ4vLXdWyiH3_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667452617pfchM83rLh5_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667449679B9qq7MRkKOR_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667449398qg81Y72vjgP_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/036516674491860YH8x8OtA5wfWrC_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/0100258716674483158vsowc2eN3O_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667447532Ut5EXHJGqIZ_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010025871667447505BPCTTxwQaQb_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667446628ew74pw8862H_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667446150xDM3jgMmGHd_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667442254d4h2DgEtoW1_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667441498uLQTSBtklj7_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667441396wBJXUBYM8is_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/03651667425003nTA33LPZIJRFtLc_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/01002587166739426912v7O71LqVC_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010025871667391264Y0Fkr9ECgxW_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010025871667386462NscKP31sLk0_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667386039RGGNK7JwyEd_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667385068DAFfVuLt4Cl_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667383793IsBXybkpFhJ_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667381903GS7HjqWPxDi_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667380436eWBEDh6j5Iv_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010071981667380282ArzPUVqKQXs_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/0100258716673790816lbRkUFZmfq_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/03651667379024sL1OvKpq8cgLi1S_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010025871667377930ivlgdTuKZgP_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010078651667377368wHb5Fw3U3Tj_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010025871667361266Cz2nTNpfCko_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/0100258716673606681IQ9YsPKS5t_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010025871667360374uKi3NXQtAyZ_gallery.jpg",
                "https://a4x-staging-us-vip-none.s3.amazonaws.com/ai-saas-out-storage/03651667305838p53FhKo49DOJ2bZ_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/0100719816673028118Kd1feM01IT_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/01002587166730161560T3bd3dhN8_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/0100719816672954641tin301j1xR_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/010025871667291431bp56jcHd8ci_gallery.jpg",
                "https://a4x-staging-us-vip-none.s3.amazonaws.com/ai-saas-out-storage/03651667291063kw1bxso7M0kKosI_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010025871667290497hyDd41i5TV7_gallery.jpg",
                "https://a4x-staging-us-vip-none.s3.amazonaws.com/ai-saas-out-storage/03651667289177ZSFRPH6jzxhWz8y_gallery.jpg",
                "https://a4x-staging-us-vip-basic.s3.amazonaws.com/ai-saas-out-storage/0100258716672891616nLUCQ0V07g_gallery.jpg",
                "https://a4x-staging-us.s3.amazonaws.com/ai-saas-out-storage/010071981667286607BLsa6ZNUVQT_gallery.jpg",
                "https://a4x-staging-us-vip-none.s3.amazonaws.com/ai-saas-out-storage/03651667285615OSO5IaYSMUPMFAx_gallery.jpg",
                "https://a4x-staging-us-vip-none.s3.amazonaws.com/ai-saas-out-storage/03651667273412k9nmuJrfQyPTJwG_gallery.jpg"
        );
        AtomicInteger count = new AtomicInteger(0);
        reIdData1.getLabels().forEach(label -> label.getImages().forEach(it -> it.setUrl(imageUrls.get(count.getAndIncrement() % imageUrls.size()))));
        reIdService.save(reIdData1);
    }

//    @Test
    public void test_reIdService_queryNotDeletedReId(){
        final List<UserReId> list = reIdService.queryNotDeletedReId("390894");
        log.info("");
    }

    @Test
    @DisplayName("更新userReid并清空缓存")
    public void test_updateReIdLabel(){
        when(reidCacheService.updateReIdLabel(any())).thenReturn(1);
        doNothing().when(reidCacheService).cleanCacheBatch(any());

        UserReId userReId = new UserReId();
        reIdService.updateReIdLabel(userReId);
        verify(reidCacheService, times(1)).cleanCacheBatch(any());
    }

    @Test
    public void test_queryNotPushedReId(){
        when(reidCacheService.queryNotPushedReId(any())).thenReturn(Lists.newArrayList());

        List<UserReId> expectedResult = Lists.newArrayList();
        List<UserReId> actualResult = reIdService.queryNotPushedReId("");
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    public void test_queryMarkedReId(){
        when(reidCacheService.queryMarkedReId(any())).thenReturn(Lists.newArrayList());

        List<UserReId> expectedResult = Lists.newArrayList();
        List<UserReId> actualResult = reIdService.queryMarkedReId("");
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    public void test_queryValidUserReId(){
        String labelId = "labelId";
        String userId = "userId";
        UserReId userReId = new UserReId();
        userReId.setUserId(userId).setLabelId(labelId);

        when(reidCacheService.queryValidUserReId(any(),any())).thenReturn(userReId);

        UserReId actualResult = reIdService.queryValidUserReId(userId,labelId);
        Assert.assertEquals(userReId,actualResult);
    }


    @Test
    public void test_queryNotDeletedReId(){
        when(reidCacheService.queryNotDeletedReId(any())).thenReturn(Lists.newArrayList());
        List<UserReId> expectedResult = Lists.newArrayList();
        List<UserReId> actualResult = reIdService.queryNotDeletedReId(userId);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    public void test_updatePushed(){
        List<String> labelIds = Arrays.asList("lable1");
        when(reidCacheService.updateReIdPush(any())).thenReturn(1);

        int expectedResult = 1;
        int actualResult = reIdService.updatePushed(userId,labelIds);
        Assert.assertEquals(expectedResult,actualResult);
    }
}
