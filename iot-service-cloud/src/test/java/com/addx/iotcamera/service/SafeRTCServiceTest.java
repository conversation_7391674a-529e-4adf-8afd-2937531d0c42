package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.WebsocketTicketVO;
import com.addx.iotcamera.bean.db.device.DeviceServerAllocDO;
import com.addx.iotcamera.config.KissSafeConfig;
import com.addx.iotcamera.kiss.KissFinder;
import com.addx.iotcamera.kiss.bean.ChooseNodeCondition;
import com.addx.iotcamera.kiss.bean.SignalServer;
import com.addx.iotcamera.kiss.KissNodeType;
import com.addx.iotcamera.kiss.node.KissNode;
import com.addx.iotcamera.kiss.service.impl.KissServiceImpl;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceServerAllocService;
import com.addx.iotcamera.service.safertc.SafeRTCService;
import com.addx.iotcamera.util.Assert.AssertUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class SafeRTCServiceTest {

    @InjectMocks
    private SafeRTCService safeRTCService;
    @Mock
    private StreamService streamService;
    @Mock
    private RedisService redisService;
    @Mock
    private KissFinder kissFinder;
    @Mock
    private DeviceManualService deviceManualService;
    @Mock
    private KissServiceImpl kissServiceImpl;
    @Mock
    private DeviceServerAllocService deviceServerAllocService;

    @Mock
    private KissSafeConfig kissSafeConfig;

    private Map<String, JSONObject> redisMap = new LinkedHashMap<>();

    @Before
    public void before() throws InterruptedException {
        when(redisService.hashEntries(anyString())).thenAnswer(it -> redisMap.get(it.getArgument(0)));
        doAnswer(it -> {
            Optional.ofNullable(redisMap.get(it.getArgument(0)))
                    .ifPresent(value -> value.remove(it.getArgument(0)));
            return null;
        }).when(redisService).deleteHashField(anyString(), anyString());
        doAnswer(it -> {
            redisMap.computeIfAbsent(it.getArgument(0), k -> new JSONObject())
                    .put(it.getArgument(1), it.getArgument(2));
            return null;
        }).when(redisService).hashPut(anyString(), anyString(), any());
        when(deviceServerAllocService.queryBySn(anyString())).thenAnswer(it -> {
            return new DeviceServerAllocDO().setSn(it.getArgument(0));
        });

        KissSafeConfig config = new KissSafeConfig();
        config.setIpInfo(new ArrayList<KissSafeConfig.KissIpInfo>(){{ add(new KissSafeConfig.KissIpInfo().setKissIp("*******").setKissIp("*******").setGaDomainName("ga.com").setKissDomainName("kiss.com"));}});
        config.setAllocations(new ArrayList<KissSafeConfig.Allocation>() {{ add(new KissSafeConfig.Allocation().setType("ga").setGaIndex(0).setKissIndex(0).setUserIds("1,2,3"));}});
        safeRTCService.init();
        KissSafeConfig.addOnChangeListener(safeRTCService);
        config.init();
        config.init();
        Thread.sleep(2000);
    }

    //    @Test
    public void test_getWebsocketTicket() {
        String sn = OpenApiUtil.shortUUID();
        String linkToken1 = OpenApiUtil.shortUUID();
        String linkToken2 = OpenApiUtil.shortUUID();
        String secret = OpenApiUtil.shortUUID();
        String countryNo = "US";
        String redisKey = "bx:linkToken:" + sn;

        KissNode kissNode1 = new KissNode() {{
            setWebsocketAddr("turn://xxx." + linkToken1);
            setSignalServerSecret(secret);
            setExtranetIp4("***************");
        }};
        KissNode kissNode2 = new KissNode() {{
            setWebsocketAddr("turn://xxx." + linkToken2);
            setSignalServerSecret(secret);
            setExtranetIp4("***************");
        }};
        final SignalServer signalServer1 = SignalServer.from(kissNode1);
        final SignalServer signalServer2 = SignalServer.from(kissNode2);

        kissFinder.getNodeMapByType(KissNodeType.kissSafeRtc).put(signalServer1.getIpAddress(), kissNode1);
        redisMap.put(redisKey, new JSONObject()
                .fluentPut(linkToken1, JSON.toJSONString(signalServer1))
                .fluentPut(linkToken2, JSON.toJSONString(signalServer2))
        );
        when(kissFinder.chooseWebsocketNodeBNAndCountryNo(anyString(), any())).thenReturn(kissNode2);

        {
            WebsocketTicketVO websocketTicket = safeRTCService.getWebsocketTicket(sn, linkToken1, sn, countryNo, null);
            Assert.assertEquals(websocketTicket.getId(), sn);
            Assert.assertEquals(websocketTicket.getSignalServerIpAddress(), signalServer1.getIpAddress());
            Assert.assertEquals(websocketTicket.getSignalServer(), signalServer1.getAddr());
            Assert.assertEquals(websocketTicket.getWebsocketPath(), "/bx-channel/connect");
        }
        {
            WebsocketTicketVO websocketTicket = safeRTCService.getWebsocketTicket(sn, linkToken2, sn, countryNo, null);
            Assert.assertEquals(websocketTicket.getId(), sn);
            Assert.assertEquals(websocketTicket.getSignalServerIpAddress(), signalServer2.getIpAddress());
            Assert.assertEquals(websocketTicket.getSignalServer(), signalServer2.getAddr());
            Assert.assertEquals(websocketTicket.getWebsocketPath(), "/bx-channel/connect");
        }
        kissFinder.getNodeMapByType(KissNodeType.kissSafeRtc).clear();
    }

    @Test
    public void test_getWebsocketTicket2() {
        Integer userId = new Random().nextInt(1000_0000);
        String sn = "AIC" + OpenApiUtil.shortUUID();
        String linkToken = userId + sn + System.currentTimeMillis();
        String countryNo = "US";
        {
            when(kissFinder.chooseWebsocketNodeBNAndCountryNo(eq(sn), any())).thenReturn(null);
            AssertUtil.assertThrowException(() -> safeRTCService.getWebsocketTicket(sn, linkToken, sn, countryNo, userId));
        }
        {
            KissNode kissNode1 = new KissNode() {{
                setWebsocketAddr("wss://xxx.yyy");
                setSignalServerSecret("abc");
                setExtranetIp4("***************");
            }};
            when(kissFinder.chooseWebsocketNodeBNAndCountryNo(eq(sn), any())).thenReturn(kissNode1);
            WebsocketTicketVO ticket = safeRTCService.getWebsocketTicket(sn, linkToken, sn, countryNo, null);
            Assert.assertEquals(kissNode1.getExtranetIp4(), ticket.getSignalServerIpAddress());
            Assert.assertEquals(kissNode1.getWebsocketAddr(), ticket.getSignalServer());
        }
    }

    //    @Test
    public void test_kissNode() {
        KissNode kissNode1 = new KissNode() {{
            setWebsocketAddr("wss://xxx.yyy");
            setSignalServerSecret("abc");
            setExtranetIp4("***************");
            setIntranetIp4("***************");
        }};
        Assert.assertEquals(JSON.toJSONString(kissNode1), kissNode1.toString());
        Assert.assertEquals(JSON.toJSONString(kissNode1), kissNode1 + "");

        Map<String, KissNode> kissNodeMap = new TreeMap<>();
        kissNodeMap.put(kissNode1.getIntranetIp4(), kissNode1);
        Assert.assertEquals(JSON.toJSONString(kissNodeMap), kissNodeMap.toString());
        Assert.assertEquals(JSON.toJSONString(kissNodeMap), kissNodeMap + "");

    }

    @Test
    public void test_getSignalServerAddr() {
        final String sn = OpenApiUtil.shortUUID();
        final String countryNo = OpenApiUtil.shortUUID();

        final KissNode kissNode = new KissNode();
        kissNode.setWebsocketAddr("kiss");
        kissNode.setSignalServerSecret("secret");
        kissNode.setExtranetIp4("127.0.0.1");
        ChooseNodeCondition condition = new ChooseNodeCondition().setCountryNo(countryNo).setPurpose("normal");
        when(kissFinder.chooseWebsocketNodeBNAndCountryNo(eq(sn), eq(condition))).thenReturn(kissNode);

        final KissNode kissNode2 = new KissNode();
        kissNode2.setWebsocketAddr("kiss2");
        kissNode2.setSignalServerSecret("secret2");
        kissNode2.setExtranetIp4("*********");

        final SignalServer signalServer = SignalServer.from(kissNode);
        final SignalServer signalServer2 = SignalServer.from(kissNode2);
        {
            SignalServer result = safeRTCService.getSignalServerAddr(sn, true, "Unknow", null);
            Assert.assertEquals(null, result);
        }
        {
            SignalServer result = safeRTCService.getSignalServerAddr(sn, true, countryNo,null);
            Assert.assertEquals(signalServer, result);
        }
        {
            when(kissServiceImpl.getKissNodeFromSignalServerCache(sn)).thenReturn(kissNode2);
            SignalServer result = safeRTCService.getSignalServerAddr(sn, false, countryNo,null);
            Assert.assertEquals(signalServer2, result);
        }
        {
            when(kissServiceImpl.getKissNodeFromSignalServerCache(sn)).thenReturn(null);
            SignalServer result = safeRTCService.getSignalServerAddr(sn, false, countryNo,null);
            Assert.assertEquals(signalServer, result);
        }

        // test KissConfig 命中情况
        {
            Integer userId = 1;
            when(kissSafeConfig.getAllocations()).thenReturn(new ArrayList<KissSafeConfig.Allocation>(){{ add(new KissSafeConfig.Allocation().setUserIds("1,2,3").setKissIndex(0).setGaIndex(0).setType("ga"));}});
            when(kissSafeConfig.getIpInfo()).thenReturn(new ArrayList<KissSafeConfig.KissIpInfo>(){{add(new KissSafeConfig.KissIpInfo().setKissDomainName("kiss.com").setGaDomainName("ga.com").setKissIp("*******").setGaIp("*******"));}});
            kissFinder.getNodeMapByType(KissNodeType.kissSafeRtc).put("*******", new KissNode().setSignalServerSecret("gs_ssss").setDomain("ga.com").setExtranetIp4("*******"));
            SignalServer result = safeRTCService.getSignalServerAddr(sn, false, countryNo,userId);
            Assert.assertEquals(result.getGaIp(), "*******");
            Assert.assertEquals(result.getIpAddress(), "*******");
            Assert.assertEquals(result.getSecret(), "gs_ssss");
        }

    }
}
