package com.addx.iotcamera.service;

import com.addx.iotcamera.config.CloudfrontConfig;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.MyAwsUtil;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.cloudfront.CloudFrontUrlSigner;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.InputStream;
import java.security.PrivateKey;
import java.security.Security;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.util.MyAwsUtil.loadPrivateKey;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class CloudfrontServiceTest {

    private TestHelper testHelper;

    @Before
    public void init() {
        this.testHelper = TestHelper.getInstanceByLocal();
    }

    @After
    public void after() {
        this.testHelper.commitAndClose();
    }

    @Test
    public void test_getSignedURLWithCannedPolicy() throws Exception {
        /*
        https://addx-firmware-cn.s3.cn-north-1.amazonaws.com.cn/CG4_sys_img/test/2021-01-06/03-28-58/g1_0.1.60_upgrade.fw
        https://addx-library.s3.amazonaws.com/G1_sys_img/prod/2020-04-08/12-38-39/g1_0.1.11_upgrade.fw
        https://addx-test.s3.cn-north-1.amazonaws.com.cn/img/003UQ5bCNQjBpjUv_video_gallery_person_80064068_3.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20210315T093032Z&X-Amz-SignedHeaders=host&X-Amz-Expires=172799&X-Amz-Credential=AKIAWLWALEBSL32LBZJI%2F20210315%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=7cb0ab238d4d2e3f903ba0c55ab1acac416baafc778a3f92c73777a2ba0341ec
        https://addx-staging-us.s3.amazonaws.com/img/0PNodCNUea86YKan_video_gallery_07595248_3.jpg
        */
        String s3ObjectKey = "G1_sys_img/test/2020-07-27/10-26-02//data/jenkins/workspace/G1/reference/out/hi3518ev300_battery_ipcam_GC2053/burn/g1_0.1.34_upgrade.fw";
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, 1);
        Date expiredDate = calendar.getTime();

        // Signed URLs for a private distribution
        // Note that Java only supports SSL certificates in DER format,
        // so you will need to convert your PEM-formatted file to DER format.
        // To do this, you can use openssl:
        // openssl pkcs8 -topk8 -nocrypt -in origin.pem -inform PEM -out new.der
        //    -outform DER
        // So the encoder works correctly, you should also add the bouncy castle jar
        // to your project and then add the provider.

        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());

//        String distributionDomain = "d1wuae6gug9zq6.cloudfront.cn"; // cn
//        String distributionDomain = "de1m9vxg7r6wi.cloudfront.net"; // eu
        String distributionDomain = "d125qowng65n2z.cloudfront.net"; // us
//        String privateKeyFilePath = "cloudfront/rsa-private-key.der";
        String privateKeyFilePath = "secret-local/cloudfront_private_key.pem";
//        String privateKeyFilePath = "secret-local/cloudfront_private_key.der";
        String keyPairId = "KCDDKGFEGVV9H";
//        String keyPairId = "b991f26a-0209-4361-b8a1-f25563e64";
//        String keyPairId = "firmware";
        String policyResourcePath = "https://" + distributionDomain + "/" + s3ObjectKey;

        // Convert your DER file into a byte array.

        InputStream derPrivateKeyInputStream = new ClassPathResource(privateKeyFilePath).getInputStream();
        PrivateKey derPrivateKey = loadPrivateKey(privateKeyFilePath, derPrivateKeyInputStream);
//        CloudFrontService cloudFrontService = new CloudFrontService("local");

        String signedUrlCanned = CloudFrontUrlSigner.getSignedURLWithCannedPolicy(
//                "https://" + distributionDomain + "/" + s3ObjectKey, // Resource URL or Path
                policyResourcePath,
                keyPairId,     // Certificate identifier,
                // an active trusted signer for the distribution
                derPrivateKey, // DER Private key data
                expiredDate // DateLessThan
        );

        /*
        1.使用 OpenSSL 生成长度为 2048 位的 RSA 密钥对，并将其保存到名为 private_key.pem 的文件中
        openssl genrsa -out private_key.pem 2048
        2.生成的文件同时包含公有密钥和私有密钥。从名为 private_key.pem 的文件中提取公有密钥。
        openssl rsa -pubout -in private_key.pem -out public_key.pem

        https://d1wuae6gug9zq6.cloudfront.cn/CG4_sys_img/test/2021-01-06/03-28-58/g1_0.1.60_upgrade.fw
        ?Expires=1615791289
        &Signature=vX3hDQNZI0NIGs2FGikxzvCE~HLQtUuR9msidieXkpjSzdvow8mvu5gfMUg1Vb6X8yVOfQCCGQqZVfnBSa6iX6imaV4-1BqIs8azA0cJV4wH1BQ6Rq4STjUYR9jxP7MIszcWfLjNjgd68JmMN0PRz4ceK02suD6uYjXigjA9odnkjkqMF~Jy50DCN2uXalg9x2ov7~~QRgp~7zrrMbw5LJjhbfg~Qt-DWNJn2Lfmw8HCbjX1UqNQG9Omn6yVm~~hi5hNTJCPgNb5qHYvx0GBBezliUiRIAqmKH4R9ZXhMXlrFNE4Ka0IzlNeOI4AtWExJT1Hd3Oc~V2ENAnEYrPyLA__
        &Key-Pair-Id=cloudFrontFirmwarePublicKey
         */
        log.info("policyResourcePath:\n" + policyResourcePath);
        log.info("signedUrlCanned:\n" + signedUrlCanned);

    }

    //    @InjectMocks
    private S3Service s3Service;

    //    @Test
    public void test2() throws Exception {
        s3Service.setS3Client(testHelper.getS3Client());

        String url = "https://addx-test.s3.cn-north-1.amazonaws.com.cn/img/003UQ5bCNQjBpjUv_video_gallery_person_80064068_3.jpg";
        String signedUrl = s3Service.preSignUrl(url);
        log.info("url:\n" + url);
        log.info("signedUrl:\n" + signedUrl);
    }

//    @InjectMocks
//    private CloudFrontService cloudFrontService;

//    @Test
    public void test_createSignedCloudfrontUrl() throws Exception {
//        JSONObject cloudfront = TestHelper.getInstanceByEnv("local").getConfig().getJSONObject("cloudfront");
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        JSONObject cloudfront = testHelper.getConfig().getJSONObject("cloudfront");
        CloudfrontConfig cloudfrontConfig = cloudfront.toJavaObject(CloudfrontConfig.class);
        CloudfrontService cloudFrontService = new CloudfrontService(cloudfrontConfig);

        S3Service s3Service = new S3Service();
        s3Service.setS3Client(testHelper.getS3Client());
        cloudFrontService.setS3Service(s3Service);

        String cloudfrontName = "firmware";
//        String s3Url = "https://addx-firmware-us.s3.amazonaws.com/G1_sys_img/test/2020-07-27/10-26-02//data/jenkins/workspace/G1/reference/out/hi3518ev300_battery_ipcam_GC2053/burn/g1_0.1.34_upgrade.fw";
//        String s3Url = "https://addx-firmware-us.s3.amazonaws.com/CG721_sys_img/staging/2021-03-01/12-24-23/upgrade.fw";
        String s3Url = "https://addx-firmware-eu.s3.eu-central-1.amazonaws.com/CG421_sys_img/staging/2020-11-14/09-04-20/upgrade.fw";
        MyAwsUtil.S3Object s3Object = MyAwsUtil.parseS3Url(s3Url);
        String s3Url2 = MyAwsUtil.buildS3Url(s3Object);
        Assert.assertEquals(s3Url, s3Url2);
        String signedUrl = cloudFrontService.createCloudfrontUrl(cloudfrontName, s3Url, null);
        log.info("signedUrl:{}", signedUrl);
    }

    //    @Test
    public void test_createSignedCloudfrontUrl2() {
        List<String> s3Urls = Arrays.asList(
                "https://addx-test.s3.cn-north-1.amazonaws.com.cn/deviceSplitVideo/hls/segment-000.ts",
                "https://addx-test.s3.cn-north-1.amazonaws.com.cn/deviceSplitVideo/hls/segment-001.ts",
                "https://addx-test.s3.cn-north-1.amazonaws.com.cn/deviceSplitVideo/hls/segment-002.ts",
                "https://addx-test.s3.cn-north-1.amazonaws.com.cn/deviceSplitVideo/hls/segment-003.ts",
                "https://addx-test.s3.cn-north-1.amazonaws.com.cn/deviceSplitVideo/hls/segment-004.ts",
                "https://addx-test.s3.cn-north-1.amazonaws.com.cn/deviceSplitVideo/hls/segment-005.ts"
        );
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        S3Service s3Service = new S3Service();
        s3Service.setS3Client(testHelper.getS3Client());

        s3Service.setExpireMinutes(60 * 24 * 7); // 最长7天
        List<String> signedUrls = s3Urls.stream().map(s3Service::preSignUrl).collect(Collectors.toList());
//        s3Urls.forEach(System.out::println);
        signedUrls.forEach(System.out::println);
    }

    //    @Test
    public void test_uploadAndCreateSignedUrl() {
//        String srcPath = "/Users/<USER>/Downloads/hls/";
        String srcPath = "/Users/<USER>/Downloads/hls/aws.m3u8";
        String desRootPath = "deviceSplitVideo/hls/";
        List<String> signedUrls = uploadAndCreateSignedUrl(srcPath, desRootPath);
        signedUrls.forEach(System.out::println);
    }

    private List<String> uploadAndCreateSignedUrl(String path, String desRootPath) {
        TestHelper testHelper = TestHelper.getInstanceByEnv("test");
        S3Service s3Service = new S3Service();
        s3Service.setS3Client(testHelper.getS3Client());
        s3Service.setExpireMinutes(60 * 24 * 7); // 最长7天

        JSONObject s3config = testHelper.getConfig().getJSONObject("s3config");
        String bucket = s3config.getString("bucket");

        List<String> signedUrls = new LinkedList<>();
        FuncUtil.foreachRecursiveStruct(Arrays.asList(new File(path))
                , file -> {
                    if (file.isDirectory()) {
                        return Arrays.asList(file.listFiles());
                    } else {
                        return Collections.emptyList();
                    }
                }, (parent, file) -> {
                    if (file.isFile()) {
                        String name;
                        if (parent.size() > 0) {
                            name = file.getAbsolutePath().substring(parent.get(0).getAbsolutePath().length() + 1);
                        } else {
                            name = file.getName();
                        }
                        String key = desRootPath + name;

                        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, key, file);
                        ObjectMetadata objectMetadata = new ObjectMetadata();
                        if (key.endsWith(".m3u8")) {
                            objectMetadata.setContentType("application/vnd.apple.mpegurl");
                        }
                        putObjectRequest.setMetadata(objectMetadata);
                        PutObjectResult result = testHelper.getS3Client().putObject(putObjectRequest);

                        String s3Url = "https://addx-test.s3.cn-north-1.amazonaws.com.cn/" + key;
                        String signUrl = s3Service.preSignUrl(s3Url);
                        signedUrls.add(signUrl);
                    }
                    return true;
                });
        return signedUrls;
    }

//    @Test
    public void test_preSign() throws Exception {
        TestHelper testHelper = TestHelper.getInstanceByEnv("staging-eu");
        S3Service s3Service = new S3Service();
        s3Service.setVideoService(new VideoService());
        s3Service.setS3Client(testHelper.getAmazonInit().s3Client());

//        String url = "https://addx-test.s3.cn-north-1.amazonaws.com.cn/img/003UQ5bCNQjBpjUv_video_gallery_person_80064068_3.jpg";
        String url = "https://a4x-staging-eu.s3.eu-central-1.amazonaws.com/device_video_slice/8633a332f14a428c3f030aa886128209/055671629125540OKGy74S79K0zPf/slice_1999_0_0.ts";
        String signedUrl = s3Service.preSignUrl(url);
        log.info("url:\n" + url);
        log.info("signedUrl:\n" + signedUrl);
    }
}
