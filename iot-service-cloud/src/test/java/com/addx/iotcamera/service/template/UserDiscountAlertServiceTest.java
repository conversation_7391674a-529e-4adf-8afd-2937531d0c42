package com.addx.iotcamera.service.template;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.db.user.UserDiscountAlertReportDO;
import com.addx.iotcamera.bean.db.user.UserDiscountBannerReportDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.library.TierFreeUserExpireDO;
import com.addx.iotcamera.bean.response.vip.VipDiscountAlertResponse;
import com.addx.iotcamera.config.TenantTierIdConfig;
import com.addx.iotcamera.config.template.GrayscaleTemplateConfig;
import com.addx.iotcamera.dao.pay.IPaymentFlowDAO;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.user.FunctionOperateLogService;
import com.addx.iotcamera.service.user.UserDiscountAlertReportService;
import com.addx.iotcamera.service.user.UserDiscountBannerReportService;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.service.vip.TierService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigInteger;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class UserDiscountAlertServiceTest {

    @Spy
    @InjectMocks
    private UserDiscountAlertService userDiscountAlertService;

    @Mock
    private UserVipService userVipService;

    @Mock
    private IPaymentFlowDAO paymentFlowDAO;

    @Mock
    private TierService tierService;

    @Mock
    private FunctionOperateLogService functionOperateLogService;

    @Mock
    private UserService userService;

    @Mock
    private MailConfirmService mailConfirmService;

    @Mock
    private MqSender mqSender;

    @Mock
    private UserDiscountAlertReportService userDiscountAlertReportService;

    @Mock
    private TenantTierIdConfig tenantTierIdConfig;

    @Mock
    private OrderService orderService;

    @Mock
    private ProductService productService;

    @Mock
    private PaymentService paymentService;

    @Mock
    private UserDiscountBannerReportService userDiscountBannerReportService;

    @Mock
    private GrayscaleTemplateConfig grayscaleTemplateConfig;

    @Mock
    private RedisService redisService;

    @Mock
    private AppRequestBase requestBase;

    @Mock
    private AppInfo app;

    @Before
    public void setUp() {
        // 确保每次测试前，requestBase.getApp() 返回有效的 App 对象
        when(requestBase.getApp()).thenReturn(app);
    }

    @Test
    @DisplayName("测试 computeMd5Hash 生成正常 MD5 哈希值")
    public void test_computeMd5Hash_normalCase() {
        Integer userId = 12345;

        // 调用 computeMd5Hash
        BigInteger result = userDiscountAlertService.computeMd5Hash(userId);

        // 确保结果不为 null
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.signum() != 0); // 确保结果为非零
    }

    @Test
    @DisplayName("测试 computeMd5Hash 处理大整数 userId")
    public void test_computeMd5Hash_largeUserId() {
        Integer userId = Integer.MAX_VALUE;

        // 调用 computeMd5Hash
        BigInteger result = userDiscountAlertService.computeMd5Hash(userId);

        // 确保结果不为 null
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.signum() != 0); // 确保结果为非零
    }

    @Test
    @DisplayName("测试 computeMd5Hash 捕获异常情况")
    public void test_computeMd5Hash_exception() {
        Integer userId = null;

        // 模拟异常情况，使用null或其他方法（虽然通常不会触发NoSuchAlgorithmException，但可以模拟）
        try {
            userDiscountAlertService.computeMd5Hash(userId);
            fail("应该抛出 NullPointerException");
        } catch (NullPointerException e) {
            // 期望抛出 NullPointerException
            Assert.assertTrue(true);
        }
    }

    @Test
    @DisplayName("测试 verifyGrayscaleByCategory 当模板为空时返回 true")
    public void test_verifyGrayscaleByCategory_templateNull() {
        Integer userId = 12345;
        Integer category = 1;

        // 模拟 grayscaleTemplateConfig 返回 null
        when(grayscaleTemplateConfig.queryGrayscaleTemplate(anyString())).thenReturn(null);

        // 调用 verifyGrayscaleByCategory
        boolean result = userDiscountAlertService.verifyGrayscaleByCategory(userId);

        // 模板为空时应该返回 true
        Assert.assertTrue(result);
    }

    @Test
    @DisplayName("测试 verifyGrayscaleByCategory 当模板配置缺失时返回 true")
    public void test_verifyGrayscaleByCategory_missingTemplateConfig() {
        Integer userId = 12345;
        Integer category = 1;

        // 模拟 template 的 GrayscaleTotal 和 GrayscaleScale 为空
        GrayscaleTemplateConfig.GrayscaleTemplate template = new GrayscaleTemplateConfig.GrayscaleTemplate();
        when(grayscaleTemplateConfig.queryGrayscaleTemplate(anyString())).thenReturn(template);

        // 调用 verifyGrayscaleByCategory
        boolean result = userDiscountAlertService.verifyGrayscaleByCategory(userId);

        // GrayscaleTotal 或 GrayscaleScale 为空时应该返回 true
        Assert.assertTrue(result);
    }

    @Test
    @DisplayName("测试 verifyGrayscaleByCategory 使用正常配置计算")
    public void test_verifyGrayscaleByCategory_normalCase() {
        Integer userId = 368309;

        // 模拟有效的 GrayscaleTemplate 配置
        GrayscaleTemplateConfig.GrayscaleTemplate template = new GrayscaleTemplateConfig.GrayscaleTemplate();
        template.setGrayscaleTotal(100);
        template.setGrayscaleScale(9);
        when(grayscaleTemplateConfig.queryGrayscaleTemplate(anyString())).thenReturn(template);

        // 模拟 MD5 计算结果为 25
        doReturn(BigInteger.valueOf(25)).when(userDiscountAlertService).computeMd5Hash(userId);

        // 调用 verifyGrayscaleByCategory
        boolean result = userDiscountAlertService.verifyGrayscaleByCategory(userId);

        // 验证结果，hashValue % total < scale 应该返回 false
        Assertions.assertFalse(result);

        // 再次模拟 MD5 计算返回更小的哈希值
        doReturn(BigInteger.valueOf(5)).when(userDiscountAlertService).computeMd5Hash(userId);

        // 再次调用
        result = userDiscountAlertService.verifyGrayscaleByCategory(userId);

        // 验证结果，hashValue % total < scale 应该返回 true
        Assertions.assertTrue(result);
    }

    @Test
    @DisplayName("显示 Alert 且没有历史记录时")
    public void test_queryVipDiscountAlert_showAlertNoHistory() {
        Integer userId = 1;

        // 模拟 tenantId 为 vicoo
        when(app.getTenantId()).thenReturn("vicoo");

        // 模拟一个有效的 User 对象，且国家为 US
        User user = new User();
        user.setCountryNo("US");
        when(userService.queryUserById(userId)).thenReturn(user);

        // 模拟没有历史的 UserDiscountAlertReportDO
        when(userDiscountAlertReportService.queryUserEjectReportDOLast(userId)).thenReturn(null);

        // 使用 doReturn 模拟显示 Alert 的条件
        TierFreeUserExpireDO tierFreeUserExpireDO = TierFreeUserExpireDO.builder().notify(true).build();
        doReturn(true).when(userDiscountAlertService).verifyDisplayAlert(userId, requestBase);

        // 调用方法
        VipDiscountAlertResponse response = userDiscountAlertService.queryVipDiscountAlert(userId, requestBase);

        // 验证返回结果
        Assert.assertTrue(response.getShouldShowAlert());
        Assert.assertNull(response.getDiscountAlertId());

        // 验证报告插入
        verify(userDiscountAlertReportService, times(1)).insertUserEjectReport(any(UserDiscountAlertReportDO.class));
    }

    @Test
    @DisplayName("Banner 已过期且显示 Alert 时新建 Banner")
    public void test_queryVipDiscountAlert_createBannerWhenExpired() {
        Integer userId = 1;

        // 模拟 tenantId 为 vicoo
        when(app.getTenantId()).thenReturn("vicoo");

        // 模拟一个有效的 User 对象，且国家为 US
        User user = new User();
        user.setCountryNo("US");
        when(userService.queryUserById(userId)).thenReturn(user);

        // 模拟没有历史的 UserDiscountAlertReportDO
        when(userDiscountAlertReportService.queryUserEjectReportDOLast(userId)).thenReturn(null);

        // 使用 doReturn 模拟显示 Alert 的条件满足
        doReturn(true).when(userDiscountAlertService).verifyDisplayAlert(userId, requestBase);

        // 模拟没有 Banner 历史记录
        when(userDiscountBannerReportService.queryLastUserBannerReport(userId)).thenReturn(null);

        // 调用方法
        VipDiscountAlertResponse response = userDiscountAlertService.queryVipDiscountAlert(userId, requestBase);

        // 验证返回结果
        Assert.assertTrue(response.getShouldShowBanner());
        Assert.assertNull(response.getDiscountBannerId());

        // 验证 Banner 被创建
        verify(userDiscountBannerReportService, times(1)).insertUserBannerReport(any(UserDiscountBannerReportDO.class));
    }

    @Test
    @DisplayName("已存在 Banner 时返回 Banner")
    public void test_queryVipDiscountAlert_existingBanner() {
        Integer userId = 1;

        // 模拟 tenantId 为 vicoo
        when(app.getTenantId()).thenReturn("vicoo");

        // 模拟一个有效的 User 对象，且国家为 US
        User user = new User();
        user.setCountryNo("US");
        when(userService.queryUserById(userId)).thenReturn(user);

        // 模拟已有的 Banner 报告
        UserDiscountBannerReportDO existingBannerReport = new UserDiscountBannerReportDO();
        existingBannerReport.setExpiredTime((int) (Instant.now().getEpochSecond() + 1000));  // 未过期
        existingBannerReport.setAppReport(0);  // 表示未上报
        when(userDiscountBannerReportService.queryLastUserBannerReport(userId)).thenReturn(existingBannerReport);

        // 使用 doReturn 模拟显示 Alert 的条件满足
        doReturn(true).when(userDiscountAlertService).verifyDisplayAlert(userId, requestBase);

        // 调用方法
        VipDiscountAlertResponse response = userDiscountAlertService.queryVipDiscountAlert(userId, requestBase);

        // 验证返回结果
        Assert.assertTrue(response.getShouldShowBanner());
        Assert.assertEquals(existingBannerReport.getExpiredTime(), response.getInValidTime());
        Assert.assertEquals(existingBannerReport.getId(), response.getDiscountBannerId());
    }

    @Test
    @DisplayName("判断产品 ID 设置")
    public void test_queryVipDiscountAlert_setProductIds() {
        Integer userId = 1;

        // 模拟 tenantId 为 vicoo
        when(app.getTenantId()).thenReturn("vicoo");

        // 模拟一个有效的 User 对象，且国家为 US
        User user = new User();
        user.setCountryNo("US");
        when(userService.queryUserById(userId)).thenReturn(user);

        // 使用 doReturn 模拟显示 Alert 的条件
        doReturn(true).when(userDiscountAlertService).verifyDisplayAlert(userId, requestBase);

        // 模拟 tenantTierIdConfig 配置
        Map<String, Map<String, Integer>> config = new HashMap<>();
        Map<String, Integer> productConfig = new HashMap<>();
        productConfig.put("original", 101);
        productConfig.put("new", 202);
        config.put("vicoo", productConfig);
        when(tenantTierIdConfig.getConfig()).thenReturn(config);

        // 调用方法
        VipDiscountAlertResponse response = userDiscountAlertService.queryVipDiscountAlert(userId, requestBase);

        // 验证产品 ID 被正确设置
        Assert.assertEquals(101, response.getOldMonthProductId().intValue());
        Assert.assertEquals(202, response.getNewAnnualProductId().intValue());
    }

    @Test
    @DisplayName("验证是否显示提醒")
    public void test_verifyDisplayAlert() {
        Integer userId = 1;
        AppRequestBase requestBase = new AppRequestBase();

        // 模拟返回一个国家为 "US" 的用户
        User user = new User();
        user.setCountryNo("US"); // 设置国家为 "US"
        when(userService.queryUserById(userId)).thenReturn(user); // 确保不会返回null

        // 模拟 tenantTierIdConfig 的配置，确保逻辑条件满足
        Map<String, Map<String, Integer>> configMap = new HashMap<>();
        Map<String, Integer> productMap = new HashMap<>();
        productMap.put("original", 1);
        configMap.put("vicoo", productMap);
        when(tenantTierIdConfig.getConfig()).thenReturn(configMap);

        requestBase.getApp().setTenantId("vicoo");

        // 调用 verifyDisplayAlert 方法并验证结果
        boolean result = userDiscountAlertService.verifyDisplayAlert(userId, requestBase);

        Assert.assertFalse(result);
    }

    @Test
    public void test_verifyDisplayAlert_userNotInUS() {
        Integer userId = 1;
        AppRequestBase requestBase = new AppRequestBase();

        User user = new User();
        user.setCountryNo("FR");
        when(userService.queryUserById(userId)).thenReturn(user);

        Map<String, Map<String, Integer>> configMap = new HashMap<>();
        Map<String, Integer> productMap = new HashMap<>();
        productMap.put("original", 1);
        configMap.put("vicoo", productMap);
        when(tenantTierIdConfig.getConfig()).thenReturn(configMap);

        requestBase.getApp().setTenantId("vicoo");

        boolean result = userDiscountAlertService.verifyDisplayAlert(userId, requestBase);

        Assert.assertFalse(result);
    }

    @Test
    @DisplayName("验证 verifyDisplayAlert 覆盖 paymentFlow 逻辑（使用 builder）")
    public void test_verifyDisplayAlert_withPaymentFlow_usingBuilder() {
        Integer userId = 1;
        AppRequestBase requestBase = new AppRequestBase();

        // 模拟返回一个国家为 "US" 的用户
        User user = new User();
        user.setCountryNo("US");
        when(userService.queryUserById(userId)).thenReturn(user);

        // 模拟 tenantTierIdConfig 的配置
        Map<String, Map<String, Integer>> configMap = new HashMap<>();
        Map<String, Integer> productMap = new HashMap<>();
        Integer originProductId = 101; // 模拟 productId
        productMap.put("original", originProductId);
        configMap.put("vicoo", productMap);
        when(tenantTierIdConfig.getConfig()).thenReturn(configMap);

        // 使用 builder 构建 UserVipDO 列表
        List<UserVipDO> userVipDOList = Arrays.asList(
                UserVipDO.builder()
                        .userId(1)
                        .tradeNo("trade123")
                        .orderId(1L)
                        .build()
        );
        when(userVipService.queryUserVipList(anyInt(), anyInt(), anyInt())).thenReturn(userVipDOList);

        // 模拟 OrderService 验证订单状态为有效
        when(orderService.verifyOrderSubStatus(anyLong())).thenReturn(true);

        // 模拟 PaymentFlow 列表
        PaymentFlow paymentFlow = new PaymentFlow();
        paymentFlow.setUserId(userId);
        paymentFlow.setProductId(originProductId);
        List<PaymentFlow> paymentFlowList = Arrays.asList(paymentFlow);

        // 模拟 PaymentService 返回交易列表
        when(paymentService.queryPaymentBatch(anyList())).thenReturn(paymentFlowList);

        // 模拟 verifyGrayscaleByCategory 返回 true
        doReturn(true).when(userDiscountAlertService).verifyGrayscaleByCategory(userId);

        // 调用 verifyDisplayAlert 方法
        boolean result = userDiscountAlertService.verifyDisplayAlert(userId, requestBase);

        // 验证结果
        Assert.assertTrue(result); // 因为 verifyGrayscaleByCategory 返回 true
    }

    @Test
    @DisplayName("TenantID 不等于 vicoo 时不显示 Alert 和 Banner")
    public void test_queryVipDiscountAlert_tenantIdNotVicoo() {
        Integer userId = 1;

        // 模拟返回一个有效的 App 对象
        AppInfo app = new AppInfo();
        app.setTenantId("otherTenant");
        when(requestBase.getApp()).thenReturn(app);  // 确保 requestBase.getApp() 不为 null

        // 调用方法
        VipDiscountAlertResponse response = userDiscountAlertService.queryVipDiscountAlert(userId, requestBase);

        // 验证返回结果
        Assert.assertFalse(response.getShouldShowAlert());
        Assert.assertFalse(response.getShouldShowBanner());
    }

    @Test
    @DisplayName("测试 verifyGrayscaleByCategory 命中条件")
    public void test_verifyGrayscaleByCategory_hitsCondition() {
        Integer userId = 1474088;

        // 模拟有效的 GrayscaleTemplate 配置
        GrayscaleTemplateConfig.GrayscaleTemplate template = new GrayscaleTemplateConfig.GrayscaleTemplate();
        template.setGrayscaleTotal(100);
        template.setGrayscaleScale(10); // 设置 scale 为 30
        when(grayscaleTemplateConfig.queryGrayscaleTemplate(anyString())).thenReturn(template);

        // 计算 MD5 哈希值
        BigInteger hashValue = userDiscountAlertService.computeMd5Hash(userId);
        int hashMod = hashValue.mod(BigInteger.valueOf(template.getGrayscaleTotal())).intValue(); // 计算 hashValue % total

        // 调用 verifyGrayscaleByCategory
        boolean result = userDiscountAlertService.verifyGrayscaleByCategory(userId);

        // 验证结果，hashValue % total < scale 应该返回 true (如果 hashMod < 30)
        assertTrue("Expected true because " + hashMod + " < " + template.getGrayscaleScale(),
                hashMod < template.getGrayscaleScale());
        assertTrue(result);
    }

}