package com.addx.iotcamera.service;

import com.addx.iotcamera.aop.JwtFilter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.FilterChain;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class JwtFilterTest {

    @InjectMocks
    private JwtFilter jwtFilter;
    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;
    @Mock
    private FilterChain chainnse;

    @Test
    public void test() {
        jwtFilter.InitResponse(response);
    }

}
