package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.openapi.AwsCredentials;
import com.addx.iotcamera.bean.openapi.SaasAITaskOM;
import com.addx.iotcamera.bean.openapi.TenantAwsConfig;
import com.addx.iotcamera.config.VideoSliceConfig;
import com.addx.iotcamera.dynamo.dao.TenantAwsConfigDAO;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.helper.GoogleStorageService;
import com.addx.iotcamera.helper.aws.AwsHelper;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.addx.iotcamera.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.GetItemRequest;
import com.amazonaws.services.dynamodbv2.model.GetItemResult;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.services.securitytoken.AWSSecurityTokenService;
import com.amazonaws.services.securitytoken.model.AssumeRoleRequest;
import com.amazonaws.services.securitytoken.model.AssumeRoleResult;
import com.amazonaws.services.securitytoken.model.Credentials;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class S3ServiceTest {

    @InjectMocks
    private S3Service s3Service;
    @Mock
    private VideoService videoService;
    @Mock
    private GoogleStorageService googleStorageService;
    @Mock
    private CosService cosService;
    @Mock
    private OciService ociService;
    @Mock
    private AmazonS3 amazonS3Client;
    @Mock
    private RedisService redisService;
    @Mock
    private AWSSecurityTokenService stsClient;
    @Mock
    private Credentials credentials;
    @Mock
    private VideoSliceConfig videoSliceConfig;
    @Mock
    private LinodeService linodeService;

    @Test
    public void test_deleteObjects() {
        List<String> videoUrls = Arrays.asList(
                "https://addx-test.s3.cn-north-1.amazonaws.com.cn/video/testEnv/5128fa13d76659313ccd859be2279187_1612322010_PAwB23bdu796132H_586d9cc8a773894b7605899ed65daebd_converted.mp4",
                "https://addx-test.s3.cn-north-1.amazonaws.com.cn/video/testEnv/5128fa13d76659313ccd859be2279187_1612321940_IgwB23bck862ndlK_6e419d29a59b485d2c3519d1c3643145_converted.mp4"
        );
        DeleteObjectsResult result = new DeleteObjectsResult((List) videoUrls);
        when(amazonS3Client.deleteObjects(any())).thenReturn(result);
//        when(amazonS3Client.deleteObjects(any())).thenCallRealMethod();

        int deleteNum = s3Service.deleteObject(videoUrls);
        Assert.assertEquals(videoUrls.size(), deleteNum);
    }

    private AmazonS3 s3Client;
    @Before
    public void init() {
        TestHelper testHelper = TestHelper.getInstanceByLocal();
        s3Client = testHelper.getS3Client();

    }
//    @Spy
//    private AmazonS3 s3Client;

    @Test
    public void test_s3Client() throws Exception {
//        ObjectMetadata video = s3Client.getObjectMetadata("addx-test", "video");
//        bucket: 'addx-test'
//        clientRegion: 'cn-north-1'
//        s3AccessKey: '********************'
//        s3SecretKey: 'SXycRbyidSaPSVuL3ORry98tKCx2dwXlcRo8lBlr'


        String bucketName = "addx-test";
        boolean exist = s3Client.doesBucketExistV2(bucketName);
        Assert.assertTrue(exist);

        String key = "video/zyj-unit-test/" + UUID.randomUUID();
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentEncoding("UTF-8");
        metadata.setContentType("plain/text");
        metadata.addUserMetadata("title", "key");
        String text = "【ni_hao_" + key + "end】";
        byte[] bytes = text.getBytes(StandardCharsets.UTF_8);
        ByteArrayInputStream is = new ByteArrayInputStream(bytes);
        metadata.setContentLength(bytes.length);
        PutObjectRequest putReq = new PutObjectRequest(bucketName, key, is, metadata);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.addUserMetadata("companyName", "addx");
        putReq.setMetadata(objectMetadata);
        PutObjectResult putResult = s3Client.putObject(putReq);
        Assert.assertTrue(putResult.getETag() != null);

        ListObjectsV2Request listReq = new ListObjectsV2Request();
        listReq.setBucketName(bucketName);
        listReq.setPrefix("video/zyj-unit-test/");
        ListObjectsV2Result listResult = s3Client.listObjectsV2(listReq);
        Assert.assertTrue(listResult.getObjectSummaries().size() > 0);

        List<String> videoUrls = new LinkedList<>();
        for (S3ObjectSummary objectSummary : listResult.getObjectSummaries()) {
            ObjectMetadata objectMetadata1 = s3Client.getObjectMetadata(bucketName, objectSummary.getKey());
            Map<String, String> userMetadata1 = objectMetadata1.getUserMetadata();
            S3Object s3Obj = s3Client.getObject(bucketName, objectSummary.getKey());
            Map<String, String> userMetadata2 = s3Obj.getObjectMetadata().getUserMetadata();
            String content = s3Client.getObjectAsString(bucketName, objectSummary.getKey());
//            Assert.assertTrue(content.contains(key));
            String videoUrl = "https://addx-test.s3.cn-north-1.amazonaws.com.cn/" + objectSummary.getKey();
            videoUrls.add(videoUrl);
        }
        Assert.assertTrue(videoUrls.stream().anyMatch(url -> url.contains(key)));

        ReflectUtil.setPrivateField(s3Service, "s3Client", s3Client);
        int deleteNum = s3Service.deleteObject(videoUrls);
        Assert.assertEquals(videoUrls.size(), deleteNum);
    }

//    @Test
    public void test_putObject() {
        String traceId = OpenApiUtil.shortUUID();

//        String configJson = "{\"serialNumber\":\"f084172b0bb1ea8cd3d06916a3d95128\",\"motion\":{\"expireDays\":30,\"maxDurationInS\":20,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-staging\",\"expireDays\":0,\"region\":\"cn-north-1\"},\"keyTemplate\":\"device_video_slice/f084172b0bb1ea8cd3d06916a3d95128/${traceId}/image.${imgType}\",\"rootPath\":\"https://addx-staging.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-staging\",\"expireDays\":0,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"keyTemplate\":\"device_video_slice/f084172b0bb1ea8cd3d06916a3d95128/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://addx-staging.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":6,\"cooldownInS\":300},\"iotService\":{\"baseUrl\":\"https://api-stage.addx.live\"},\"mqtt\":{\"domain\":\"vernemq-staging.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"serialNumber\":\"30a1e5d13fc1f21ac47fcaaebde27425\",\"motion\":{\"expireDays\":30,\"maxDurationInS\":20,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us\",\"expireDays\":0,\"region\":\"us-east-1\"},\"keyTemplate\":\"device_video_slice/30a1e5d13fc1f21ac47fcaaebde27425/${traceId}/image.${imgType}\",\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us\",\"expireDays\":0,\"region\":\"us-east-1\"},\"reportComplete\":true,\"keyTemplate\":\"device_video_slice/30a1e5d13fc1f21ac47fcaaebde27425/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":6,\"cooldownInS\":300},\"iotService\":{\"baseUrl\":\"https://api-staging-us.vicohome.io\"},\"mqtt\":{\"domain\":\"vernemq-staging-us.addx.live\",\"ip\":[\"************\",\"************\"]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"serialNumber\":\"8633a332f14a428c3f030aa886128209\",\"motion\":{\"expireDays\":30,\"maxDurationInS\":20,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us\",\"expireDays\":0,\"region\":\"us-east-1\"},\"keyTemplate\":\"device_video_slice/8633a332f14a428c3f030aa886128209/${traceId}/image.${imgType}\",\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\"},\"resolution\":\"1920x1080\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us\",\"expireDays\":0,\"region\":\"us-east-1\"},\"reportComplete\":true,\"keyTemplate\":\"device_video_slice/8633a332f14a428c3f030aa886128209/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\"},\"resolution\":\"1920x1080\"},\"noTriggerTimeoutInS\":6,\"cooldownInS\":300},\"iotService\":{\"baseUrl\":\"https://api-staging-us.vicohome.io\"},\"mqtt\":{\"domain\":\"vernemq-staging-us.addx.live\",\"ip\":[\"************\",\"************\"]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"serialNumber\":\"24b91d654d7793ae17ce42174210111a\",\"motion\":{\"expireDays\":0,\"maxDurationInS\":180,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us\",\"expireDays\":0,\"region\":\"us-east-1\"},\"keyTemplate\":\"device_video_slice/24b91d654d7793ae17ce42174210111a/${traceId}/image.${imgType}\",\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us\",\"expireDays\":0,\"region\":\"us-east-1\"},\"reportComplete\":true,\"keyTemplate\":\"device_video_slice/24b91d654d7793ae17ce42174210111a/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-staging-us.vicohome.io\"},\"mqtt\":{\"domain\":\"vernemq-staging-us.addx.live\",\"ip\":[\"************\",\"************\"]},\"ai\":{\"enable\":false}}";
//        String configJson = " {\"serialNumber\":\"24b91d654d7793ae17ce42174210111a\",\"motion\":{\"expireDays\":0,\"maxDurationInS\":180,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn\",\"expireDays\":0,\"region\":\"cn-north-1\"},\"keyTemplate\":\"device_video_slice/24b91d654d7793ae17ce42174210111a/${traceId}/image.${imgType}\",\"rootPath\":\"https://addx-prod-cn.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn\",\"expireDays\":0,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"keyTemplate\":\"device_video_slice/24b91d654d7793ae17ce42174210111a/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://addx-prod-cn.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api.addx.live\"},\"mqtt\":{\"domain\":\"vernemq-cn.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"serialNumber\":\"16b656dc9c04407152ccd6737ad22db4\",\"motion\":{\"expireDays\":0,\"maxDurationInS\":180,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-staging\",\"expireDays\":0,\"region\":\"cn-north-1\"},\"keyTemplate\":\"device_video_slice/16b656dc9c04407152ccd6737ad22db4/${traceId}/image.${imgType}\",\"rootPath\":\"https://addx-staging.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-staging\",\"expireDays\":0,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"keyTemplate\":\"device_video_slice/16b656dc9c04407152ccd6737ad22db4/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://addx-staging.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-stage.addx.live\"},\"mqtt\":{\"domain\":\"vernemq-staging.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"serialNumber\":\"4eb04375131cce4abce69672c76c7ac7\",\"motion\":{\"expireDays\":0,\"maxDurationInS\":180,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn\",\"expireDays\":0,\"region\":\"cn-north-1\"},\"keyTemplate\":\"device_video_slice/4eb04375131cce4abce69672c76c7ac7/${traceId}/image.${imgType}\",\"rootPath\":\"https://addx-prod-cn.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn\",\"expireDays\":0,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"keyTemplate\":\"device_video_slice/4eb04375131cce4abce69672c76c7ac7/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://addx-prod-cn.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api.addx.live\"},\"mqtt\":{\"domain\":\"vernemq-cn.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"serialNumber\":\"3e15f73b2b6429da816042eb0ed622ef\",\"motion\":{\"expireDays\":0,\"maxDurationInS\":180,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-staging\",\"expireDays\":0,\"region\":\"cn-north-1\"},\"keyTemplate\":\"device_video_slice/3e15f73b2b6429da816042eb0ed622ef/${traceId}/image.${imgType}\",\"rootPath\":\"https://addx-staging.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-staging\",\"expireDays\":0,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"keyTemplate\":\"device_video_slice/3e15f73b2b6429da816042eb0ed622ef/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://addx-staging.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-stage.addx.live\"},\"mqtt\":{\"domain\":\"vernemq-staging.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"serialNumber\":\"ca9136cbafd0e30655411484574de134\",\"motion\":{\"expireDays\":0,\"maxDurationInS\":180,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us\",\"expireDays\":0,\"region\":\"us-east-1\"},\"keyTemplate\":\"device_video_slice/ca9136cbafd0e30655411484574de134/${traceId}/image.${imgType}\",\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us\",\"expireDays\":0,\"region\":\"us-east-1\"},\"reportComplete\":true,\"keyTemplate\":\"device_video_slice/ca9136cbafd0e30655411484574de134/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-staging-us.vicohome.io\"},\"mqtt\":{\"domain\":\"vmq-staging-us.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"serialNumber\":\"ca9136cbafd0e30655411484574de134\",\"motion\":{\"expireDays\":0,\"maxDurationInS\":180,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us\",\"expireDays\":0,\"region\":\"us-east-1\"},\"keyTemplate\":\"device_video_slice/ca9136cbafd0e30655411484574de134/${traceId}/image.${imgType}\",\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us\",\"expireDays\":0,\"region\":\"us-east-1\"},\"reportComplete\":true,\"keyTemplate\":\"device_video_slice/ca9136cbafd0e30655411484574de134/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\"},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-staging-us.vicohome.io\"},\"mqtt\":{\"domain\":\"vmq-staging-us.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"34a90d650ffca32dd5bb30ae3a07a814\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-staging\",\"needAddStorageDayTag\":true,\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/34a90d650ffca32dd5bb30ae3a07a814/${traceId}/image.${imgType}\",\"rootPath\":\"https://addx-staging-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-staging-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-staging\",\"needAddStorageDayTag\":true,\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/34a90d650ffca32dd5bb30ae3a07a814/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://addx-staging-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-staging-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-stage.addx.live\"},\"mqtt\":{\"domain\":\"vernemq-staging.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"bindInfo\":{\"adminId\":8849},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"177dc57b7e5e6ac840e3b71250caa2c4\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":15,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn-vip-none\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"addx-prod-cn-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/177dc57b7e5e6ac840e3b71250caa2c4/${traceId}/image.${imgType}\",\"rootPath\":\"https://addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"640x360\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-prod-cn-vip-none\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"addx-prod-cn-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/177dc57b7e5e6ac840e3b71250caa2c4/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"rootPath\":\"https://addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-prod-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"640x360\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api.addx.live\"},\"mqtt\":{\"domain\":\"vernemq-cn.addx.live\",\"ip\":[]},\"ai\":{\"enable\":false}}";
//        String configJson = "{\"bindInfo\":{\"adminId\":74037},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"c4cd7d0fb2ce7df8c57445263067fe24\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-prod-us-vip-none\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"a4x-prod-us-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"us-east-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/c4cd7d0fb2ce7df8c57445263067fe24/${traceId}/image.${imgType}\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://a4x-prod-us-vip-none.s3.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-prod-us-vip-none.s3.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-prod-us-vip-none\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"a4x-prod-us-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"us-east-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/c4cd7d0fb2ce7df8c57445263067fe24/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://a4x-prod-us-vip-none.s3.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-prod-us-vip-none.s3.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-us.vicohome.io\"},\"mqtt\":{\"uri\":\"tcp://vmq-us.addx.live\"},\"ai\":{\"enable\":false},\"dns\":{\"a4x-prod-us-vip-none.s3.amazonaws.com\":[\"**************\"],\"api-us.vicohome.io\":[\"**************\"],\"vmq-us.addx.live\":[\"**************\"]}}";
//        String configJson = "{\"bindInfo\":{\"adminId\":28716},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"608f8e65fa7d7ae2be54bba08bd10a3e\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-pre-cn-vip-none\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"addx-pre-cn-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":3,\"region\":\"cn-north-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/${traceId}/image.${imgType}\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://addx-pre-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-pre-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-pre-cn-vip-none\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"addx-pre-cn-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":3,\"region\":\"cn-north-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://addx-pre-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://addx-pre-cn-vip-none.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":60},\"iotService\":{\"baseUrl\":\"https://api-pre.addx.live\"},\"mqtt\":{\"uri\":\"tcp://vmq-pre.addx.live\"},\"ai\":{\"enable\":false},\"dns\":{\"api-pre.addx.live\":[\"**************\"],\"addx-pre-cn-vip-none.s3.cn-north-1.amazonaws.com.cn\":[\"************\"],\"vmq-pre.addx.live\":[\"**************\"]}}";
//        String configJson = "{\"bindInfo\":{\"adminId\":165148},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"03c78811ed7bffe49073f6cc19d1462f\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":20,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-prod-eu-vip-3d\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"a4x-prod-eu-vip-3d\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":3,\"region\":\"eu-central-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/03c78811ed7bffe49073f6cc19d1462f/${traceId}/image.${imgType}\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://a4x-prod-eu-vip-3d.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-prod-eu-vip-3d.s3-accelerate.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-prod-eu-vip-3d\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"a4x-prod-eu-vip-3d\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":3,\"region\":\"eu-central-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/03c78811ed7bffe49073f6cc19d1462f/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://a4x-prod-eu-vip-3d.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-prod-eu-vip-3d.s3-accelerate.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-eu.vicohome.io\"},\"mqtt\":{\"uri\":\"tcp://vmq-eu.addx.live\"},\"ai\":{\"enable\":false},\"dns\":{\"vmq-eu.addx.live\":[\"************\"],\"api-eu.vicohome.io\":[\"************\"],\"a4x-prod-eu-vip-3d.s3-accelerate.amazonaws.com\":[\"**************\"]},\"battery\":{\"battery\":5200,\"batteryCode\":\"BTW5200\",\"url\":\"https://addx-device-config.s3.amazonaws.com/battery/2022/**********_BTW5200_CW2017.txt\",\"version\":**********,\"voltameterType\":1}}";
//        String configJson = "{\"bindInfo\":{\"adminId\":1015992},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"608f8e65fa7d7ae2be54bba08bd10a3e\",\"common\":{\"canAutoOtaTime\":0},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us-vip-3d\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"a4x-staging-us-vip-3d\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":3,\"region\":\"us-east-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/${traceId}/image.${imgType}\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://a4x-staging-us-vip-3d.s3.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-staging-us-vip-3d.s3.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-staging-us-vip-3d\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"a4x-staging-us-vip-3d\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":3,\"region\":\"us-east-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://a4x-staging-us-vip-3d.s3.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-staging-us-vip-3d.s3.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":60},\"iotService\":{\"baseUrl\":\"https://api-staging-us.vicohome.io\"},\"mqtt\":{\"uri\":\"tcp://vmq-staging-us.addx.live\"},\"ai\":{\"enable\":false},\"dns\":{\"vmq-staging-us.addx.live\":[\"**************\"],\"api-staging-us.vicohome.io\":[\"**************\"],\"a4x-staging-us-vip-3d.s3.amazonaws.com\":[\"************\"]}}";
//        String configJson = "{\"bindInfo\":{\"adminId\":106411},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"608f8e65fa7d7ae2be54bba08bd10a3e\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-pre-eu-vip-3d\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"a4x-pre-eu-vip-3d\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":3,\"region\":\"eu-central-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/${traceId}/image.${imgType}\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://a4x-pre-eu-vip-3d.s3.eu-central-1.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-pre-eu-vip-3d.s3.eu-central-1.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-pre-eu-vip-3d\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"a4x-pre-eu-vip-3d\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":3,\"region\":\"eu-central-1\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://a4x-pre-eu-vip-3d.s3.eu-central-1.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-pre-eu-vip-3d.s3.eu-central-1.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":60},\"iotService\":{\"baseUrl\":\"https://api-pre-eu.vicohome.io\"},\"mqtt\":{\"uri\":\"tcp://vmq-pre-eu.addx.live\"},\"ai\":{\"enable\":false},\"dns\":{\"a4x-pre-eu-vip-3d.s3.eu-central-1.amazonaws.com\":[\"*************\"],\"api-pre-eu.vicohome.io\":[\"*************\"],\"vmq-pre-eu.addx.live\":[\"***********\"]}}";
//        String configJson = "{\"bindInfo\":{\"adminId\":52397},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"2908b8d6c206a95cfe747b9d4fc3c6ba\",\"common\":{},\"motion\":{\"expireDays\":10,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"nvs-ap-southeast-1-motioncapture\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"nvs-ap-southeast-1-motioncapture\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"ap-southeast-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"netvue%40netvue.com/5034520463100885/${traceId}/image.${imgType}\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://nvs-ap-southeast-1-motioncapture.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://nvs-ap-southeast-1-motioncapture.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":false},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"nvs-ap-southeast-1-videomotion\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"nvs-ap-southeast-1-videomotion\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"ap-southeast-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"netvue%40netvue.com/5034520463100885/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://nvs-ap-southeast-1-videomotion.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://nvs-ap-southeast-1-videomotion.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":false},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":6,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api.addx.live\"},\"mqtt\":{\"uri\":\"tcp://vmq-cn.addx.live\"},\"ai\":{\"enable\":false},\"dns\":{\"vmq-cn.addx.live\":[\"**************\"],\"nvs-ap-southeast-1-motioncapture.s3.cn-north-1.amazonaws.com.cn\":[\"*************\"],\"nvs-ap-southeast-1-videomotion.s3.cn-north-1.amazonaws.com.cn\":[\"*************\"],\"api.addx.live\":[\"*************\"]},\"battery\":{\"battery\":9000,\"batteryCode\":\"HK9000\",\"url\":\"https://addx-device-config.s3.amazonaws.com/battery/2022/**********_HK9000_CW2017.txt\",\"version\":**********,\"voltameterType\":1}}";
//        String configJson = "{\"bindInfo\":{\"adminId\":8135},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"26cbaeb311ea0eee560a37a7df44cbfa\",\"common\":{},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-prod-eu-vip-plus\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"a4x-prod-eu-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":30,\"region\":\"eu-central-1\",\"aiEdgeImageBucket\":\"a4x-prod-eu-ai-edge\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/26cbaeb311ea0eee560a37a7df44cbfa/${traceId}/image.${imgType}\",\"defaultServiceName\":\"s3\",\"aiEdgeImageRootPath\":\"https://a4x-prod-eu-ai-edge.s3-accelerate.amazonaws.com/\",\"rootPath\":\"https://a4x-prod-eu-vip-plus.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\",\"aiEdgeImageKeyTemplate\":\"ai_edge/26cbaeb311ea0eee560a37a7df44cbfa/${traceId}/image.${imgType}\",\"defaultRootPath\":\"https://a4x-prod-eu-vip-none.s3-accelerate.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1920x1080\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-prod-eu-vip-plus\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"a4x-prod-eu-vip-none\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":30,\"region\":\"eu-central-1\",\"aiEdgeImageBucket\":\"a4x-prod-eu-ai-edge\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/26cbaeb311ea0eee560a37a7df44cbfa/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"defaultServiceName\":\"s3\",\"aiEdgeImageRootPath\":\"https://a4x-prod-eu-ai-edge.s3-accelerate.amazonaws.com/\",\"rootPath\":\"https://a4x-prod-eu-vip-plus.s3-accelerate.amazonaws.com/\",\"serviceName\":\"s3\",\"aiEdgeImageKeyTemplate\":\"ai_edge/26cbaeb311ea0eee560a37a7df44cbfa/${traceId}/image.${imgType}\",\"defaultRootPath\":\"https://a4x-prod-eu-vip-none.s3-accelerate.amazonaws.com/\",\"reportSlice\":true},\"resolution\":\"1920x1080\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-eu.vicohome.io\"},\"mqtt\":{\"uri\":\"tcp://vmq-eu.addx.live\"},\"ai\":{\"enable\":false},\"dns\":{\"a4x-prod-eu-vip-none.s3-accelerate.amazonaws.com\":[\"************\"],\"vmq-eu.addx.live\":[\"************\"],\"api-eu.vicohome.io\":[\"************\"],\"a4x-prod-eu-ai-edge.s3-accelerate.amazonaws.com\":[\"**************\"],\"a4x-prod-eu-vip-plus.s3-accelerate.amazonaws.com\":[\"*************\"]}}";
//        String configJson = "{\"bindInfo\":{\"adminId\":64},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"7e75051cc7078b806a2da8b5dc5f3f1b\",\"common\":{\"canAutoOtaTime\":0},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"addx-smb\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"addx-smb\",\"defaultStorageDays\":7,\"keyRawPrefix\":\"device_video_slice/7e75051cc7078b806a2da8b5dc5f3f1b\",\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\",\"keyPrefix\":\"device_video_slice/7e75051cc7078b806a2da8b5dc5f3f1b\",\"aiEdgeImageBucket\":\"addx-smb-ai-edge\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/7e75051cc7078b806a2da8b5dc5f3f1b/${traceId}/image.${imgType}\",\"defaultServiceName\":\"s3\",\"aiEdgeImageRootPath\":\"https://addgrep: x-smb-ai-edge.s3.cn-north-1.amazonaws.com.cn/\",\"rootPath\":\"https://addx-smb.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"aiEdgeImageKeyTemplate\":\"ai_edge/7e75051cc7078b806a2da8b5dc5f3f1b/${traceId}/image.${imgType}\",\"defaultRootPath\":\"https://addx-smb.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"addx-smb\",\"needAddStorageDayTag\":false,\"defaultBucket\":\"addx-smb\",\"defaultStorageDays\":7,\"keyRawPrefix\":\"device_video_slice/7e75051cc7078b806a2da8b5dc5f3f1b\",\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"cn-north-1\",\"keyPrefix\":\"device_video_slice/7e75051cc7078b806a2da8b5dc5f3f1b\",\"aiEdgeImageBucket\":\"addx-smb-ai-edge\"},\"reportComplete\":true,\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/7e75051cc7078b806a2da8b5dc5f3f1b/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"defaultServiceName\":\"s3\",\"aiEdgeImageRootPath\":\"https://addx-smb-ai-edge.s3.cn-north-1.amazonaws.com.cn/\",\"rootPath\":\"https://addx-smb.s3.cn-north-1.amazonaws.com.cn/\",\"serviceName\":\"s3\",\"aiEdgeImageKeyTemplate\":\"ai_edge/7e75051cc7078b806a2da8b5dc5f3f1b/${traceId}/image.${imgType}\",\"defaultRootPath\":\"https://addx-smb.s3.cn-north-1.amazonaws.com.cn/\",\"reportSlice\":true},\"resolution\":\"\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-smb.addx.live\"},\"mqtt\":{\"uri\":\"tcp://vmq-smb.addx.live\"},\"ai\":{\"enable\":false},\"dns\":{},\"battery\":{\"battery\":5200,\"batteryCode\":\"LH5200\",\"url\":\"https://addx-library.s3.cn-north-1.amazonaws.com.cn/battery/CW_LH5200.txt\",\"version\":0,\"voltameterType\":1}}";
//        String configJson = "{\"bindInfo\":{\"adminId\":155175},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"9a1f5db04c4db128a5435c91f4af0b39\",\"common\":{\"canAutoOtaTime\":0},\"motion\":{\"expireDays\":0,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"a4x-prod-eu\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"a4x-prod-eu\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"eu-central-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/9a1f5db04c4db128a5435c91f4af0b39/${traceId}/image.${imgType}\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://a4x-prod-eu.s3.eu-central-1.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-prod-eu.s3.eu-central-1.amazonaws.com/\",\"reportSlice\":false},\"resolution\":\"1280x720\"},\"type\":\"video\",\"config\":{\"headSlicePeriod\":2000,\"provider\":\"AWS_S3\",\"tailSlicePeriod\":4000,\"config\":{\"bucket\":{\"bucket\":\"a4x-prod-eu\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"a4x-prod-eu\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":7,\"region\":\"eu-central-1\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"device_video_slice/9a1f5db04c4db128a5435c91f4af0b39/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://a4x-prod-eu.s3.eu-central-1.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://a4x-prod-eu.s3.eu-central-1.amazonaws.com/\",\"reportSlice\":false},\"resolution\":\"1280x720\"},\"noTriggerTimeoutInS\":0,\"cooldownInS\":0},\"iotService\":{\"baseUrl\":\"https://api-eu.vicohome.io\"},\"mqtt\":{\"uri\":\"tcp://vmq-eu.addx.live\"},\"ai\":{\"enable\":false},\"dns\":{\"vmq-eu.addx.live\":[\"************\"],\"a4x-prod-eu.s3.eu-central-1.amazonaws.com\":[\"**************\"],\"api-eu.vicohome.io\":[\"************\"]},\"battery\":{\"battery\":8000,\"batteryCode\":\"PX8000\",\"url\":\"https://addx-device-config.s3.amazonaws.com/battery/2021/**********_PX8000_CW2017.txt\",\"version\":**********,\"voltameterType\":1}}";
        String configJson = "{\"bindInfo\":{\"adminId\":1260728},\"deviceAudio\":{\"liveAudioToggleOn\":true,\"liveSpeakerVolume\":100,\"recordingAudioToggleOn\":true},\"serialNumber\":\"8705126c9dddd2825fd3098d8a2e4bcd\",\"common\":{\"canAutoOtaTime\":0},\"motion\":{\"expireDays\":10,\"maxDurationInS\":10,\"coverConfig\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"sm-us-east-2-motioncapture\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"sm-us-east-2-motioncapture\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":3,\"region\":\"us-east-2\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"df05a0837883e2a6/5031522613000009/${traceId}/image.${imgType}\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://sm-us-east-2-motioncapture.s3.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://sm-us-east-2-motioncapture.s3.amazonaws.com/\",\"reportSlice\":false},\"resolution\":\"1920x1080\"},\"type\":\"video\",\"config\":{\"provider\":\"AWS_S3\",\"config\":{\"bucket\":{\"bucket\":\"sm-us-east-2-videomotion\",\"needAddStorageDayTag\":true,\"defaultBucket\":\"sm-us-east-2-videomotion\",\"defaultStorageDays\":7,\"vipExpireTime\":**********,\"vipStorageDays\":3,\"region\":\"us-east-2\"},\"storageClass\":\"STANDARD\",\"keyTemplate\":\"df05a0837883e2a6/5031522613000009/${traceId}/slice_${period}_${order}_${isLast}.ts\",\"defaultServiceName\":\"s3\",\"rootPath\":\"https://sm-us-east-2-videomotion.s3.amazonaws.com/\",\"serviceName\":\"s3\",\"defaultRootPath\":\"https://sm-us-east-2-videomotion.s3.amazonaws.com/\",\"reportSlice\":false},\"resolution\":\"1920x1080\"},\"noTriggerTimeoutInS\":6,\"cooldownInS\":60},\"iotService\":{\"baseUrl\":\"https://api-us.vicohome.io\"},\"mqtt\":{\"uri\":\"tcp://vmq-us.addx.live\"},\"ai\":{\"enable\":false},\"dns\":{\"sm-us-east-2-videomotion.s3.amazonaws.com\":[\"*************\"],\"sm-us-east-2-motioncapture.s3.amazonaws.com\":[\"*************\"],\"api-us.vicohome.io\":[\"************\"],\"vmq-us.addx.live\":[\"*************\"]},\"battery\":{\"battery\":8000,\"batteryCode\":\"PX8000\",\"url\":\"https://addx-device-config.s3.amazonaws.com/battery/2021/**********_PX8000_CW2017.txt\",\"version\":**********,\"voltameterType\":1}}";
        JSONObject motion = JSONObject.parseObject(configJson).getJSONObject("motion");

//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1628841737,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"07611628841731ZvSnocyofpnmMkb\",\"imageKeyTemplate\":\"device_video_slice/f084172b0bb1ea8cd3d06916a3d95128/07611628841731ZvSnocyofpnmMkb/image.${imgType}\",\"serialNumber\":\"f084172b0bb1ea8cd3d06916a3d95128\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"Z60qcl2kRgw7wbZpqcGek/48+xCABCORIW9liWKd\",\"expirationSeconds\":1628884937,\"sessionToken\":\"FwoDYXdzEBgaDCEziHBfOFncAVaejCLFAdtvpUPkuTnunVokJEcwMU6nKqE5cFLiDfaGxVBGVYQjvL0LNnVnsoV4CaOZdQexJ3bbVcDoCLX2FV++CQ4nKOowP8otVBhr4hqCm0FEaa3dPkP/BjXN5L3/RbiC2K07h4C/U2CCJ0/Duj7gNCeb1O9oyaDMrY6Uyyo0lV74KzjixPLkNx1gzC9cE94DhT1t16Ttm+uQV6K8NoEaCg2gnKaV4kIzCH+9i7GOlEV8QZx0BIQ5faNpuqWpvRrolfjwFlt8OZ/4KInO2IgGMi3SZrUNTsBH6IHwJaMYA0/pAf3aCxT1vNkO8H5KHwJ1IdcGbAEaw6n6a/aTJoY=\",\"expiration\":1628884937000},\"tailSlicePeriod\":4000,\"rootPath\":\"https://addx-staging.s3.cn-north-1.amazonaws.com.cn/\",\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"addx-staging\",\"clientRegion\":\"cn-north-1\",\"sliceKeyTemplate\":\"device_video_slice/f084172b0bb1ea8cd3d06916a3d95128/07611628841731ZvSnocyofpnmMkb/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1629192606,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"06441629192597nM5EKjOMLqs6lpg\",\"imageKeyTemplate\":\"device_video_slice/30a1e5d13fc1f21ac47fcaaebde27425/06441629192597nM5EKjOMLqs6lpg/image.${imgType}\",\"serialNumber\":\"30a1e5d13fc1f21ac47fcaaebde27425\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"Kmnzr7ftB+ahBbHr9TxUdA9yp0e242k2jLEjZHA7\",\"expirationSeconds\":1629235806,\"sessionToken\":\"FwoGZXIvYXdzEFsaDF78ah7JMH5cK4wkciLEAWl43NtGr8v8a95fbwO+vMJn1B2u55sFq1XSlfxEFgi9n72BgSrE4mV8iTKYZSYuqlrTnh85GLL/OyTGBFIMawPzY7E+T1ZxaSCbGBHOtlAi8bLMKVl/emg/rLh642/XwBJ2B7mDfN0VdCF4OYUsgsP59a6bKsy3hUv2E97KP9NZN91dtGe/jvfVKEHtoyNBSxlg57Q95MjYO5jBmwun7PLfHBVpzXAFpprUxqnODb96OYSXWC322HSaRF08tXPqx/G08RMonoPuiAYyLq6RFDm8j2Dv3cZkziIfCzxOa++QyH55VETwR+CEmCWBOYhB5hWZW/XKr2QDh3M=\",\"expiration\":1629235806000},\"tailSlicePeriod\":4000,\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"a4x-staging-us\",\"clientRegion\":\"us-east-1\",\"sliceKeyTemplate\":\"device_video_slice/30a1e5d13fc1f21ac47fcaaebde27425/06441629192597nM5EKjOMLqs6lpg/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1629204828,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"03381629204824qH71v60pdSfvId6\",\"imageKeyTemplate\":\"device_video_slice/8633a332f14a428c3f030aa886128209/03381629204824qH71v60pdSfvId6/image.${imgType}\",\"serialNumber\":\"8633a332f14a428c3f030aa886128209\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"h3oRaZxfz3CjUPJds+kLCDrgGt285CPr5wjMl0ze\",\"expirationSeconds\":1629248028,\"sessionToken\":\"FwoGZXIvYXdzEF4aDI5aeSUs+kntM2apNiLEAUnzpi2XqlxS66dmcU/V6FcCn6dQPQIbfIUSl66poBaiOOpPutkWZEneHAUHduTaSYU0ld31FCt53lCrEisXbBm9ZY7ceWVpRl4UOG9ET4egk3XdKnvkPHlUJpErl/x6Zu69WQRggCvuoh+oYrIGTXCDp3afDuW5WoPcAyxaZWnSCUboPah53kRDUInUSPDBU3gKPnygm7w0D9F6QY2bGAGqKxUYzbBko0dAB8/HTT2aaMhAN2L3Ph6mt75rrTlScdyFQ6Qo3OLuiAYyLi4Y7CpJD9o6GOJVLt5U9IhlvzjIYIyotxfvGeGEGrW/2dn47JC2R8QF4v6WHBA=\",\"expiration\":1629248028000},\"tailSlicePeriod\":4000,\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"a4x-staging-us\",\"clientRegion\":\"us-east-1\",\"sliceKeyTemplate\":\"device_video_slice/8633a332f14a428c3f030aa886128209/03381629204824qH71v60pdSfvId6/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1629869690,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"05601629869685B6IMetXicqXqEfA\",\"imageKeyTemplate\":\"device_video_slice/24b91d654d7793ae17ce42174210111a/05601629869685B6IMetXicqXqEfA/image.${imgType}\",\"serialNumber\":\"24b91d654d7793ae17ce42174210111a\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"fgoWzEKOAVdyMhEmLH/gbuaVxifRNyvzJNAbiARL\",\"expirationSeconds\":1629912890,\"sessionToken\":\"FwoGZXIvYXdzEBcaDFgPvJcdLigOeBqqFSLEAWjEhWdIVZk7P3moyVQB3kJxDLl9HjneRdZvsKgzVrJvQ26oP2TxjbUbUom14u9953M2fN/0CixhamLHtgY8QAGA6cioZcuIBAFvHQ0FT10RQx0CI4mgqy5ccqznKb6pENXaArrJVVQpWj/qvrDjbJtpXDtppUzsSU5osrMrl+TT+xDK1lQ/FD8dq+dz3s5mcehvscG7xkXGSlwbChhQBV2dPduDnFd9+f6ejhGziIcNeDwiO9yD8tZV02qY6QySlu1QG3Io+qyXiQYyLgl49wAizwdORp7+7krwVn1waRJYrQaRK9cCX9eWQ4Hu5FxaGq73PGRhIKEnXnA=\",\"expiration\":1629912890000},\"tailSlicePeriod\":4000,\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"a4x-staging-us\",\"clientRegion\":\"us-east-1\",\"sliceKeyTemplate\":\"device_video_slice/24b91d654d7793ae17ce42174210111a/05601629869685B6IMetXicqXqEfA/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1629875949,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"0609416298759463X2D4sG8txeYHD\",\"imageKeyTemplate\":\"device_video_slice/24b91d654d7793ae17ce42174210111a/0609416298759463X2D4sG8txeYHD/image.${imgType}\",\"serialNumber\":\"24b91d654d7793ae17ce42174210111a\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"eRWEoZqNmlCF2LfkBoWw9n7gAUnXZ46ILe7Na1O2\",\"expirationSeconds\":1629919149,\"sessionToken\":\"FwoDYXdzEDgaDPPgMRkcjiO6EXx75iLFAW7ZMckHa85eKjK20LbLUZGIU8DxfFrLYCYbDcfzqFUROSRzVYMk9TA3RVWgApn8f7nKSW485YLQq50v/xlC057XEqKbfOg5wmaC8Sr5BLaQXxetP8NSlHtCr72RWOrkBTiAIA3fC7jDtKZmDVXiY9P2PJHjdqh0D2dN92HzEedZ+Pm5mXr6AvEDka6jYzE8c8h3pANzuov9DpexoYRt1kFhiK3aYOI8EvzA+SrbBfRMPk6hGRSLCvM4NznuVdiRC2lg+I4kKO3dl4kGMi3/N6aF9y7e6/MNgNv6ojtOC0IAA9TpazYuBGi6cK5nmobQVmD5i5MOY2AUtrE=\",\"expiration\":1629919149000},\"tailSlicePeriod\":4000,\"rootPath\":\"https://addx-prod-cn.s3.cn-north-1.amazonaws.com.cn/\",\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"addx-prod-cn\",\"clientRegion\":\"cn-north-1\",\"sliceKeyTemplate\":\"device_video_slice/24b91d654d7793ae17ce42174210111a/0609416298759463X2D4sG8txeYHD/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1629893471,\"value\":{\"traceId\":\"07051629893467KGqiCV0MZbCjglo\",\"serialNumber\":\"16b656dc9c04407152ccd6737ad22db4\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"jMXdsF9O547eLk8GY120hbMKYHCmzVjc+JU+F7d2\",\"expirationSeconds\":1629936671,\"sessionToken\":\"IQoJb3JpZ2luX2VjEA8aDmNuLW5vcnRod2VzdC0xIkgwRgIhAOWNNKHK3zsIjOlj4USNzqVmwIirBUnI/NuRf2bHV06WAiEAo/0BINNYuBmdEibvqCc1pAR3gZL/uUwzsG4zudsOky8qrgIIPRABGgw5MDIyODA2MDY1MzAiDDsitO5t57y5MQ2a6yqLAgld1IocySmuBkJWfUjdbVYX4Jf1h/k48/kOT8LJQSbTVeSYeSBhHM8Ed2kTv/byRhOTTozvJ9DXKvBo8Xhb6ypZBwSFX7PPJUVJ35xc3l5MbCDwsis30Q+Gh0XuxVItytdIJSLgz0FTG9jj2BVwsot+mihk6K45jXHOfNvD4QJ1lirIi9L/nLaffCguHRKdiA3CsZSdCvUMhFAeP4cXdVSMylcfeUoC0ZNv3THNyKIh+b/S0INOEhPCOhyLMfOibPTkSS74ggk47OYnwSPUAPMw5j9GYYH9+CajSDMGlgg6QTWK6Kx4uOltqPs1yRGyVJsL9qySYGcHH89ovxATOKkdbhykmfc4adqBwjDf5piJBjqcAYmgaeDOWehUlc0vnogvKCQrLGOuQfn6/wAEauO3MWFW8vPoCCMxhSbhnlMBEnTBSUwuvdGYIrQRtSqcuptBvcynvAAZz0J+V2jSo/shdLv1E0Cl+AjJqpIaF96+O1MDbvPc/Rlg3yKDADKZQAbLMFVW58BlGlBiUnlXp0JTtl94jwp2Cc29mOKszMAaAEduWz5ei+ru3vnSfzVI5A==\",\"expiration\":1629936671000},\"videoUploadType\":1,\"serviceName\":\"s3\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1629891469,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"0414516298914660CXv3DJNX5w664\",\"imageKeyTemplate\":\"device_video_slice/4eb04375131cce4abce69672c76c7ac7/0414516298914660CXv3DJNX5w664/image.${imgType}\",\"serialNumber\":\"4eb04375131cce4abce69672c76c7ac7\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"Kf9gdmDd+t+/CgBG/YlgMV2RS8DjDBY9KTkk1QLU\",\"expirationSeconds\":1629934669,\"sessionToken\":\"FwoDYXdzEDwaDHRGXGr0Pw8CQjhSAiLFAbj3IFECiCF2cDyEtlvRmohVRh3tQV/r04Cj17dTJH5MCmOJqTajr6ig9muFTvQCGUBIzBXOqgI4bOURzPP0Uw2x8lPWPPZfq6MJEPEKk1nGcU5Dp8y8XultuLdiYHkLTNo22arudwk4YllP5vMndWkw/gwUlN8kWLowG+kaZ2jHcezcTcoSxtHB+2O3vKoJ33tJPf7SjsbeGOH8ryehs5fek5OtE5tv3JiAdeXukBJjc4JxTZEC2qbKC67UdJtoWxPFsxeDKI3XmIkGMi1MBavFI5w7MO5jIgOm/4rJWH0w2WIKTAzU54FQ/ooYyecAhXEcsoQ7HiPI9OM=\",\"expiration\":1629934669000},\"tailSlicePeriod\":4000,\"rootPath\":\"https://addx-prod-cn.s3.cn-north-1.amazonaws.com.cn/\",\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"addx-prod-cn\",\"clientRegion\":\"cn-north-1\",\"sliceKeyTemplate\":\"device_video_slice/4eb04375131cce4abce69672c76c7ac7/0414516298914660CXv3DJNX5w664/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1631242908,\"value\":{\"traceId\":\"09421631242905B4u4JekUKOU6Wg5\",\"serialNumber\":\"3e15f73b2b6429da816042eb0ed622ef\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"y9VDqvrk9oB6+6oz6xdfoYigK7JK6T0JW8Yw/9s2\",\"expirationSeconds\":1631286108,\"sessionToken\":\"IQoJb3JpZ2luX2VjEIb//////////wEaDmNuLW5vcnRod2VzdC0xIkYwRAIgBqO0stDJy4U6338r/PGmZxil26GWTfCb+mVD8wZIdicCICoYdOoOxoyi3htOzZR6TQEutsktophnRauG7dECXAGgKrcCCLP//////////wEQARoMOTAyMjgwNjA2NTMwIgyMNCGt3Cb+zo04X7QqiwLifnIc1Lv15d00Ls/CTCxZ5omGuWm02PLsYLYdW0xrNhGLU1HqNSXOopT6KQJPTdwzcoGsjO/RgR4SlO4ULDlsdpbE0cvWcmFI0LqtgT/ykPZuaLahCrfONiql1Q9xsosXm5Zng4/iZhFMVcdS9ZZ1wb2hOo9twbbtgT4swu17q2KOgfeMEfd8cgKezQO9D8UKYy80gbStgoPnOgLfBwlby3tFtC+wJO0dbz3k8+6yzZ8mvsPKELGEUG0Y17rIHc6h23jZaPpVD0uMWOZb2NwdFNGEw7hErTNiihuWtN0B0GIUsO8zFXXOjXz9dt4PqlSlVYMjoh7F6CQ3QyKgAzZWYTL1lI+rwTNb8YEwnJXriQY6ngGkrgXaenJDkzbC9YR+u1Uqe7lttt7h5tjWbn0adTricyUrTh3J2shnPZ5qs4kTBl/KWgtnPa1gtnpxoi8h+TQiaUgzy0Dh2Qv/O7QUeeICyazAMos16o3YFil8EHBrLFIG37mTUESD8I+pPQlI1rzJPh8qoBTGhP50gjF2pnvEwAUjNvYgaYw41QVGiL6VDCXv5+Sogu9C8dqwZvX77g==\",\"expiration\":1631286108000},\"videoUploadType\":1,\"serviceName\":\"s3\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1636598952,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"03381631414948Nwq607sMFlTyFHa\",\"imageKeyTemplate\":\"device_video_slice/ca9136cbafd0e30655411484574de134/03381631414948Nwq607sMFlTyFHa/image.${imgType}\",\"serialNumber\":\"ca9136cbafd0e30655411484574de134\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"Hvt6hN6AdPLW8KGqw4ckWhnCHGOXzSzKTzo7iI0Q\",\"expirationSeconds\":1636642152,\"sessionToken\":\"FwoGZXIvYXdzEGQaDPTwgg1fCK48ICSKQyLEAZwpG//TBYAKN97m1KIMumOeCzWc0Tp08z67ei3MWlyBLp+Zb+nF3nxNxDGPyOJYw2DPVbUANqErPMS9SN4phz82g7T6MErPT+rpXtnLQpbkDzZ38pXE3gHIgxHvGRRAHCzSE0TfgptVye+wyUP80jtjawo8v2IK+W/SlboE6b1+YaYKqfvvMFoCFNduTpTCoWzEdcWBmj9aHThx0D8ebH2OQ2fPJO+Vxmt5WdNNKJLyKJQ6BHPiBSIqRT6HcUsWoIN84UQoqImyjAYyLrbRuEZAhdH4a2f0XJh1Le037ONPgGnig2VcqfFIB1oIy/8nzVBui4YtfnvQq1c=\",\"expiration\":1636642152000},\"tailSlicePeriod\":4000,\"rootPath\":\"https://a4x-staging-us.s3-accelerate.amazonaws.com/\",\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"a4x-staging-us\",\"clientRegion\":\"us-east-1\",\"sliceKeyTemplate\":\"device_video_slice/ca9136cbafd0e30655411484574de134/03381631414948Nwq607sMFlTyFHa/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1655799859,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"07051655799854JunhPWgIYjPFa7p\",\"imageKeyTemplate\":\"device_video_slice/34a90d650ffca32dd5bb30ae3a07a814/07051655799854JunhPWgIYjPFa7p/image.${imgType}\",\"serialNumber\":\"34a90d650ffca32dd5bb30ae3a07a814\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"Y3iJvywL6psnw0Jx3SRn+Fh+gdNw6xUgDPQNlsQZ\",\"expirationSeconds\":1655843059,\"sessionToken\":\"FwoDYXdzEFkaDIGgK4qnYDvYXAqBJCLFAQ+T7sJKXaSt3LUV2QpyVv3APMVdai29gGPdCaIlr1muYi3d/J8yA6/MwTk+gygS6f0yexjlKsbNC5U5b17u/8QMyragSq8KZbPSUmT37neJlw3KN9i+KewIufiEx3Xci6wXR2M398xRnPLmDLmTgrT9iB/VpiSCHqbliXhKdBOPHsrqqLz7yEA8cGPEUQUVciQhUuquxR5I2bAzDgezsI5nGeUtpymz4lXV4ZMe8S7lX5sDmv6x32N2lhJGMPDoq9bP3IWbKLOAxpUGMi3icw6anFmjhyE8vyHW5ilI7cCMDTBuY/OkUGfnS4uTPVtKmKbE2OWvLxfG15Q=\",\"expiration\":1655843059000},\"tailSlicePeriod\":4000,\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"addx-staging\",\"clientRegion\":\"cn-north-1\",\"sliceKeyTemplate\":\"device_video_slice/34a90d650ffca32dd5bb30ae3a07a814/07051655799854JunhPWgIYjPFa7p/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":1,\"name\":\"reportEvent\",\"time\":1663127759,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"088491663127758Xu3OC40ou88ynA\",\"imageKeyTemplate\":\"device_video_slice/177dc57b7e5e6ac840e3b71250caa2c4/088491663127758Xu3OC40ou88ynA/image.${imgType}\",\"serialNumber\":\"177dc57b7e5e6ac840e3b71250caa2c4\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"rAdSghvC2tBpWdKd8Enijg3XGRX8s0pJTwa+6LdT\",\"expirationSeconds\":1663170959,\"sessionToken\":\"FwoDYXdzEEwaDP2hR7sx17CYEq2IlCLFAQhETUBbwBe1/lgSvSeU2ugrp/3D66DrJm47cyGJEIGhkQ2XlrHUZ5jnbyND/83S77BzEPrru92kDJ2HzH+CzYNRwQ5jCrihDC3NvNCIJe9t457uswnjf+5eG4UoSPlY0eSdjWAae7P9tA/3qGA58kHt/9L9CnaSekCCiRgktvwSZK+OneLkKeZbkkr+hS1KaoSoXK+t/FSlUlve3sK4xe/iheS7lq3knKVsKSgGbIulSry7nrGwLFEVJDo0CYsF8PyIHDjsKM+hhZkGMi1A9ozPgw3sx5VCoQG5Itw2AP/8sT5SEXMo9D4JjAGgqGtFjBTCDy6iysy0BkY=\",\"expiration\":1663170959000},\"tailSlicePeriod\":4000,\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"640x360\",\"bucket\":\"addx-prod-cn\",\"clientRegion\":\"cn-north-1\",\"sliceKeyTemplate\":\"device_video_slice/177dc57b7e5e6ac840e3b71250caa2c4/088491663127758Xu3OC40ou88ynA/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1673333341,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"01276181673333337rVnyVhHEEgjo\",\"imageKeyTemplate\":\"device_video_slice/fd992bb13edb4c30c549a37fb1268127/01276181673333337rVnyVhHEEgjo/image.${imgType}\",\"serialNumber\":\"fd992bb13edb4c30c549a37fb1268127\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"/4iBqNDTr5WCrR2WajdjrUnxvUcasN/MzmINhwBk\",\"expirationSeconds\":1673376541,\"sessionToken\":\"FwoGZXIvYXdzEEAaDFH55dJj8ymzhQRMrSLEAXh7J1BoLD/I9lMA5Ys49fuaXT0mdtcBlq5wxv1KwNMSWXZGTUjKCKRB1SUYaEvffhuG63GH+MbMvXtV+FmvyR9xBvU0ObHjBToOqAJtHjKRWuKtVkk+l+Ecffsu66Ly+u8dRsKTcpCcWb+Ln4Zb0/Ktq/bGX8lGsajfqFhU1hBBIpVKbkgqnV4eQ+w2x9TM/YW5C0D5E/f6eD/Z+O9Fy7VLeNlY0dnyFa/JBVtZBHnGCr36XC4uCRNVkaecaPwwD/Z1eo0o3ZT0nQYyLjIew2Z36AroiPy2DKzcV+cYJPu3MrwzjTiXS60BoNu9JgxtLJYZdS+VAxzChk8=\",\"expiration\":1673376541000},\"tailSlicePeriod\":4000,\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"a4x-prod-us\",\"clientRegion\":\"us-east-1\",\"sliceKeyTemplate\":\"device_video_slice/fd992bb13edb4c30c549a37fb1268127/01276181673333337rVnyVhHEEgjo/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1676285492,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"0287161676285490qmISDxDouoHDv\",\"imageKeyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/0287161676285490qmISDxDouoHDv/image.${imgType}\",\"serialNumber\":\"608f8e65fa7d7ae2be54bba08bd10a3e\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"cuRkM0mhNjQd1Amqk7GjygsEY1+sI7Bj1YizdPMd\",\"expirationSeconds\":1676328692,\"sessionToken\":\"FwoDYXdzEJP//////////wEaDBw4SuyB9WkDZL7n2SLFAduIJ9goLt1kKej8EbfNU92PUj6O0DilS5dE6S7XtmyCw/8+coS3Newe5QaUJ2ovEm9LC3vudfDAu6t+Sw8jW5SjO9/QUTRAxNxU8s8GjB33kd+k2n8nsYc3B+7QPRAhWIXwdLqSkwBXznaAsRgD6e4pc5kteHIuTkVNwES22IdFpMy6uM58YkSL/FROAKo34fYvPsu6VV3bvFNr/iciAmDZ1gTSGddYS471iycPT945gaUzZpYQPmySy/4nTcdvofPyKbuBKLSsqJ8GMi1JtnedR24b+EGGkjuioB327SQeotXWle995CIUuVVVg5eSf3kIQFgj5hMgvHw=\",\"expiration\":1676328692000},\"tailSlicePeriod\":4000,\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"addx-pre-cn\",\"clientRegion\":\"cn-north-1\",\"sliceKeyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/0287161676285490qmISDxDouoHDv/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1676431850,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"01651481676431848y4JCUS5JNzFH\",\"imageKeyTemplate\":\"device_video_slice/03c78811ed7bffe49073f6cc19d1462f/01651481676431848y4JCUS5JNzFH/image.${imgType}\",\"serialNumber\":\"03c78811ed7bffe49073f6cc19d1462f\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"PMcYhlmnjXkhXyBpf1plwxcgTF6C2AhyvsI2eEB1\",\"expirationSeconds\":**********,\"sessionToken\":\"FwoGZXIvYXdzEJ3//////////wEaDNl4o3ZLT9DmUdqRbyLEAVjthhsZkTJi8IoqrOoDaoHgAaMOwaibM7WWVAadLd0vNhNJFENBNrA9fS4lKjd6L1f79DV8bJj7cxbe+gH+nuSptQsQVwoAYOIQzSNSN2G3GWN4MZsKO7uMYTyJehIcWBpUjNPIgGoV2ziUg6khikrk309VAhDSXzbg9X2VZ7R7vYKmq/woFKquOkmM9Li8EZdHXHjDr7yVCVJ/4eFfeQn+RgC23G51gfla9TXtyn1doWz66jRvyfimr4gN2BGHUABpyxso6qOxnwYyLsVKRXMDebBgf2zImkWOYTltoSvMYffXmn1Db4Ca2TDpvjO9xHc91G7parl7nlg=\",\"expiration\":**********000},\"tailSlicePeriod\":4000,\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"a4x-prod-eu\",\"clientRegion\":\"eu-central-1\",\"sliceKeyTemplate\":\"device_video_slice/03c78811ed7bffe49073f6cc19d1462f/01651481676431848y4JCUS5JNzFH/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":**********,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"010159921676444425S7j0DKWXFGR\",\"imageKeyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/010159921676444425S7j0DKWXFGR/image.${imgType}\",\"serialNumber\":\"608f8e65fa7d7ae2be54bba08bd10a3e\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"zNGZHC7hT0FKyPF4OKKQaNRhLhn76X/5eXaVWpL4\",\"expirationSeconds\":1676487634,\"sessionToken\":\"FwoGZXIvYXdzEKD//////////wEaDJK9Kxh4GQwyB8hFIyLEAaZM1Bk4lg83HnbkACPnJYZN6nvITnQRrH+crLAAF5XccBSWi4wixPfeu5MJJB/twHP9DoWbJ6HcfmCh6xCRiglxmEhrzmveMP4huLXJij3jWs9C968kKSfP4dir5JqZReYhwemcWyiTBjhigR+RSSV2IdFusJ00TYg+LusfR8Ce/xsHf23fXahuie+9oLFwFD5TCJzCDSUt4mO6p/76dif2FMLPkVZ7NQT/uSUNRnNUrZd8AwA3xuiF0PCdQ7OTN+ldKk8okoaynwYyLmHdw/kQm/VY9ug0X6FM7fAcg75vL26OYtmmdfO7HxLDlWVfBRzf+3jL/CUyxvg=\",\"expiration\":1676487634000},\"tailSlicePeriod\":4000,\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"a4x-staging-us\",\"clientRegion\":\"us-east-1\",\"sliceKeyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/010159921676444425S7j0DKWXFGR/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1676461922,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"010641116764619161UxFgmrDNJLO\",\"imageKeyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/010641116764619161UxFgmrDNJLO/image.${imgType}\",\"serialNumber\":\"608f8e65fa7d7ae2be54bba08bd10a3e\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"nY6vDhl3NeEeO46cTqoQ3YI+cvCN152nssZ3PVoa\",\"expirationSeconds\":1676505122,\"sessionToken\":\"FwoGZXIvYXdzEKX//////////wEaDMdnJe3Hmq+sByXXsiLEAVz8srIWrQHJlCBphA3NableWonbbsZ1uTEWS53s0Zo7Nx202yeOH7rCMwTdWXxOGetJazuOlwzK3nr44dYLY23eRaeQFDJNdfmaU/n/4T6pbyQHK/8qPBA1Ack76zgqFXrJ7ktI2RZYAIn2Ah4TiDlmyx5oNlvr1NhAwxFyQ0CL5RVG7Rpu5HUPjvUAylVTgt04/ytqj43DLe3I0WiXhR5p4Rk+84InG+0do5+4/Sj0GEmutJOkQzsHGchA4xpbAgBh3Y0o4o6znwYyLuXJYtencNS1IXM8IJ7dV1bTLweYffoZAXZlBDb64/O881gP1iR7uqq+vWBveMc=\",\"expiration\":1676505122000},\"tailSlicePeriod\":4000,\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1280x720\",\"bucket\":\"a4x-pre-eu\",\"clientRegion\":\"eu-central-1\",\"sliceKeyTemplate\":\"device_video_slice/608f8e65fa7d7ae2be54bba08bd10a3e/010641116764619161UxFgmrDNJLO/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1681123143,\"value\":{\"traceId\":\"0523971681123140Y646mr5VSfjg8\",\"serialNumber\":\"2908b8d6c206a95cfe747b9d4fc3c6ba\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"PX+wwMvT+gyYnaSOB5pvchUaqaws7bguNavNvJll\",\"expirationSeconds\":1681166343,\"sessionToken\":\"FwoDYXdzENP//////////wEaDHmlA+ecFXbJ8cRtFSLFAUiNlY2RnoGFLUqAKUWtjN41XRBkWsgJTa5IBa3EpA43hKTje9NcNgp7X54gIZmL7Rlh0l3w5bB6g9E43PGWyZuV0CKsyg3/rgyDnhX/2W+6MZdAY2deL/3IAExaVl45ntQndLFaLrIayO49X2OhAN5FHF+Hofmm/kqv0D4oWkVSR2htGmlXwHunVKV8kY7Gj2gXaPc897xDBj09fTylVz1tmwqsf8KTUhDeD3G8xZVGDmQODG20kSC47rwDsu7rma83BwnZKMfOz6EGMi3senWATd7v3KfbuMEh4/K9GftuVDtJf476QXvVlwHYuL83WqAeaEJa3+DJX4I=\",\"expiration\":1681166343000},\"videoUploadType\":1,\"serviceName\":\"s3\",\"cosCredentials\":{\"secretKey\":\"Avo7IS0CxcwtR3vQmB2KbygedKviInlsNZEX+aOIios=\",\"expirationSeconds\":1681166343,\"sessionToken\":\"dC85Bfms5TNWns47oQdiiofIAQiGrX9a55a74af6221cc543c9ac19f3896734eaSUi_lVJVSnyMRj1a3Ih4xR6UIUu5gGu9BY1PuZdgofnb_ZYAsG_U5Z8YLmhpo378IRnS0ZlM8SVJVAdTgC_vAi4ACithUPFIupfAcEDvBoLxQnGJuegSz1EG0mU9JZaC07kE9y6Kkp9-V0OgsX-gRWjr1GMecD1Oy2JvaNmVyKUswrq4MKEOb7wylZj6p0-9X0TOhEkfYfEQhFdj09ka9Me5Wei-Zt6ICXtdxVRFo1_375iarCy97Rlzsvpm2lPtoZQsO6cFhFs17Nw2TpfAHhJoENwcshFEWK4eTdhXFySJbBm_n70BDviog04_5ZC3L80AREGx6DGWV-vZd7LuEitsog4e-a5f0O19ej4T133uBsjjmygeGOf_D6Cye50JfCmCxi_7cFoGGmPnuRkckZNnCk3mYkxd2jrZ6B0ghvm1Cltq5ba41azAy2GmwCjVMCOgmv2cgsWG7TFblgfK9AcblM5xvVA9hZv5yfCSG3cZ-WF2wQiTpBCy18JEXjaD3xYXZAVbg1ncKv_4xDjPUsv4DlFuymNoxi2aIyVGmXowB3riH8a0VIFeLKEwUZaoPc5Ud6PAZ0d_yYQPSqCLJZMfJmegSHAUH66ocrMij5xZRG-JgiBZL_jQhGyOSkjL4eF3vvtMfdF6VDftu06HhlK5kcOjnJgsqWxBGSixcHylgRqaqP0eFdxqgub3J1_Cuq_OWsP2ijy0JQM9OxO_YHMY9QAU_m1DeKSNuojQPxyJnZjCJmYklgvrgGzjww_jBtiADwiQG_wJrHjaenfPueoxexAED999XBjLGu1Frw829RxHD8eUvc-0UCcuuBO6Q0-7PQiVtx5DYVTScltxddwciiXFndQO025ofC-XibP9ZNb78keSAXvu7IHp8lwowy0yPLm2YhJUZCemrwwZZxWe3VLUnOp4_MWMogoAtaE0dVihsPTQMwA5R3VRVPhF-joMs-FfaIUMQ0vMrN-fihcL4bR_xVSnW9sHHBt8tChy4N6eQETErTKa5it1NZFo\",\"secretId\":\"AKIDb3AB5xwKRl0pRjMU5fnNFTv67a5RFERe1ImSHxTakBhn_2q3LYfj-5EHeGcRZn8I\"}}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1681886402,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"081351681886396XpbM3Bg0v67XMs\",\"imageKeyTemplate\":\"device_video_slice/26cbaeb311ea0eee560a37a7df44cbfa/081351681886396XpbM3Bg0v67XMs/image.${imgType}\",\"serialNumber\":\"26cbaeb311ea0eee560a37a7df44cbfa\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"4yiC0frSJ6n1Anig+3cnVsO3R2WBHIqJsJpEjn7T\",\"expirationSeconds\":1681918953,\"sessionToken\":\"FwoGZXIvYXdzEIX//////////wEaDFAXAT197NekNjt7zSLEAaATh49vg2OHaMPHleixf2nCJWCDWUdAvyIbCt8JcNO7aHXmn01vwAwDQ7cd8NGToEHBWreMuse1VqQmvQ/P4ZZfDc0rjOCNyxZa0amJ3Ec6jvZk9ZoC8vfjXwgEKnmxWLSubl8c+t/SsPoV3RzmSU0a5qtanILRV8fXYe59kGxjRzc57AoIJz/UKVuMWgyZdOUK0ATvhrRMWjl5TO3yBf+fsrocNoJSQ6aeSfMpIRiSFanSe6OPuY+yQMDl7tromRvpCrEoqcb9oQYyLu0oPNnnXTK9epvrhuJWJ01TQAIPn9Hf8g90KYnb2NzG1QckTWIg4WPuGplS9bk=\",\"expiration\":1681918953000},\"tailSlicePeriod\":4000,\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"1920x1080\",\"bucket\":\"a4x-prod-eu\",\"clientRegion\":\"eu-central-1\",\"sliceKeyTemplate\":\"device_video_slice/26cbaeb311ea0eee560a37a7df44cbfa/081351681886396XpbM3Bg0v67XMs/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1690445163,\"value\":{\"headSlicePeriod\":2000,\"traceId\":\"064169044515680BKpTAizxs8PQCz\",\"imageKeyTemplate\":\"device_video_slice/7e75051cc7078b806a2da8b5dc5f3f1b/064169044515680BKpTAizxs8PQCz/image.${imgType}\",\"serialNumber\":\"7e75051cc7078b806a2da8b5dc5f3f1b\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"JSzvWNH0zQiw3Pp++0Y7K/Xt1jkz1ngIpz6Osa6r\",\"expirationSeconds\":1690486450,\"sessionToken\":\"FwoDYXdzEPD//////////wEaDNEfkALLS1HmiYDbSSLFAfu3tV4ueTwBS4dNm40r55hCrCphFKgNUAhP8bKH63oVGJOqpfOydEr0bQGkWFOD5zJ5KCBiaeSWTWCBPR9l300gosJBeAukbycXNBEAE8xH6snApFIJKWzfr4i1DWa2AgFHMJEpdRxophgaTbyGZBp6wBBRSyAvoC0ZJPAN3+ykZpoo+hJ/ujPrRh2rrM+GrpR2XATYFYIX+c4lax/QGlY8a5wEOME4TXw1y5wPjAZ4+WWjLaPHzIKhobwsHRSkbZ/8c+T5KPK7iKYGMi0BShkQQJkPm1boueAS0a3U9wdQIcjdxzcsmhO4L+pLa/0PXyiy7YDnG2hM0I0=\",\"expiration\":1690486450000},\"tailSlicePeriod\":4000,\"videoUploadType\":1,\"serviceName\":\"s3\",\"resolution\":\"\",\"bucket\":\"addx-smb\",\"clientRegion\":\"cn-north-1\",\"ociCredentials\":{\"accessUri\":[\"/videoFile/upload/p/sUhWlwZEbNpPChq5MP90667HM74tUKgIbOdB7CViLlIFPUUilhUJtfFzusYeYJtD/n/default/b/pir/o/\",\"/videoFile/upload/p/sUhWlwZEbNrnTF152Zc2Tx6QwD9QAlfmRxlieSP5vq-iIJhzIGaUGkKeBSu7QRQmh09JfnmALuc/n/default/b/ai-edge/o/\"],\"expirationSeconds\":1690531563},\"sliceKeyTemplate\":\"device_video_slice/7e75051cc7078b806a2da8b5dc5f3f1b/064169044515680BKpTAizxs8PQCz/slice_${period}_${order}_${isLast}.ts\"}}";
//        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1691614171,\"value\":{\"traceId\":\"01551751691614166b1VBnZR4pZzf\",\"serialNumber\":\"9a1f5db04c4db128a5435c91f4af0b39\",\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"tr5L7WHGptK331J5CaNiK7qwrK8Tws7jRg9BSubD\",\"expirationSeconds\":1691643691,\"sessionToken\":\"FwoGZXIvYXdzEBIaDK74YRPL6N8CfH2fgCLFAYn+EGhQYyq2zF08REuOmSt7peaGCF9Yj4wESI3iOpO7nV8lXB9WYuFCaD4NOQ6PqbpOsljfGRU651o/anSiNkQ+TQtzUK008Q0ErsexubsZ+0lDSc2Kdh7Rkzk4p67K0ANjTLe2ze3rjNL6XcNhNQDjrp4bxx/BJ1LkMvU0Skw78OF5FwqFgepDI/zVq0Woi8L1DdmHJTb+/UFTOF50PFzj433bsVoHXOvD9uW7SoO5zp/6LdUj9sVdwZNz8T65eJfQSA+2KOuMz6YGMi3tds2gfMGA54dJZF0FJbPDebe2uKhYo/KEBWIfD5X3icLC8SqsibloyA62W5Q=\",\"expiration\":1691643691000},\"videoUploadType\":1,\"serviceName\":\"s3\",\"gcsCredentials\":{\"expirationSeconds\":1691617770,\"expiration\":1691617770534,\"accessToken\":\"ya29.c.b0Aaekm1LUftkDdO3Pkxk9F5u3OK5TtVUnXonhDWtF4yNOFLvzQinAZrqugk3i8nlqSGnkfXthVYQ6axgdJIsAGld5QfXH9i2AoIUys-iWJIEOBAE9rrxLFeO1fr3a8g7wAZ7kGCgB2Nc0usqG2CXP6ft43-5SIhG1rj9-K-kPFkOEEme1_Z8Kh28taLHNFjE6zbS_lXniTgGznxdgaUSB9gSR0UsB4cuqqd2r7FDGnkjar95H6K55k1wXCGsSEHpK6bVlGdruryAu_cYfmUxKsFnuMna2h3nk1qFTLSBpqfIJrcdPwzaV1SJb1zl9dvMA9pddDlrTG337KF5-hw86mo5bUenx9JzkURXJ699zJ8Swiy-bbXmpqs5zJp0swVmxjIB1oa0Vk7JoVnasknWk2QsJb4XcaUYO77B4fknXkRucmdrhgsocyig_rk_1j4rwxO7dW2MtmiQSZv6rBUolk8f3XZZ4UggoyBQs3MfX8kF3IB1Vc60WRycR78Iqcub26XU9VgY98FWeyQut_omUV7wBhvaz7QFWgiqX_IqkksfQz5oSf2YVaf7tBb5tr4o-yncg7lzyllJcrQ3y0MZvRiihwQteZbJlxoSS97z3bduinV5ndQ6gBVIj7rMljgU4_24QddzaiZbFlk385h658fnMswB_3QX4w99Onvkgsjn_Rm975z_F_hrXk7snYI4zu-zqBgB886Vwlz4l4zSWIrtXkq58lRtj3SZkIVb0xr65zYSokze3rydImbjqaRvq5e8mtkUR6Y6gu9SFnW4qi4f7Iy0lhpgQ7BwtzaW8JmiM-dd_zM3th4yzZW8v-dx6Ui61eBQWVvMphkvW8fu6X6zdrVO15m-ediue-zgtJwM9VShlQjkiO5FkeV2t8zj4qwWix4qXk_vVo5JZcotca90k1y1i0s39q9MfSm0v_29h8a8-Yyxu2OiVqduQS_Ij5V1FFwjOI5vpjknI050XzRi_mk1vpya8p6xq12p3UyOl_aFkqdkZp3x\"},\"ociCredentials\":{\"accessUri\":[\"/p/gn88T-KudczOGcXfCW7hgiuW55HUGupbquyU4jObQmnwMHezs6kqnRFLbBQKRFbJ/n/idrw0j0fjn4v/b/a4x-prod-eu-ai-edge/o/\",\"/p/8WmUZBBwBeMFrIkI-gC5nTdS6cHuGuA6kgjVNuqK9YXK_OHbd4visjNSZNKM2ylx/n/idrw0j0fjn4v/b/prod-eu-vip-3d/o/\",\"/p/p9Xwlpzwo0KbqjWNNih7fDWFBqvXumPzvMYmvv0zRgks6absxeuinaUhbYAa_ka4/n/idrw0j0fjn4v/b/prod-eu-vip-7d/o/\",\"/p/QiYvjq0H_hxnlitmAw-Ou9WUmE9hr1gaVJH1lfmCWr0kZ9wD5zPwOqevPnNj9Ry-/n/idrw0j0fjn4v/b/prod-eu-vip-15d/o/\",\"/p/yqj43vHTrhmRR8BKUGqw_rLDGAEbjQ4ubu2dQklTseLOsdXp-7EKr-h0qwD4r1de/n/idrw0j0fjn4v/b/prod-eu-vip-30d/o/\",\"/p/MLrPuSF3ALBBy4T60caCfc_odWvyd8uelKEorW4jkvEsNC8iGq6eaKhOV55B4xse/n/idrw0j0fjn4v/b/prod-eu-vip-60d/o/\"],\"expirationSeconds\":1691693151}}}";
        String json = "{\"id\":0,\"name\":\"reportEvent\",\"time\":1691614171,\"value\":{\"traceId\":\"01551751691614166b1VBnZR4pZzf\",\"serialNumber\":\"9a1f5db04c4db128a5435c91f4af0b39\",\"credentials\":" +
                "{\"accessKeyId\":\"********************\"," +
                "\"secretAccessKey\":\"s4PKPiRI+mE8WS86lRNjOiExl+X3GKrvpQ+chW3k\"," +
                "\"expirationSeconds\":1691643691," +
                "\"sessionToken\":\"" +
                "FwoGZXIvYXdzEKT//////////wEaDChJ1qhSqy8p3+mjVyKyAY8SQi3vQsyesWNBikrVmc6blvPyzbnfQDhs/mLzG0qDm47k9wuFuOFLqTlpcn29vaZ72agaiE+/ch4uCx83AYcNCgCsZSBPl1mHTNcCosSY62XYKkRKy/bJ7oKy/oUa3yuJ1BOQtEzwL+dqvAEz5CzCMXw1cQmjQgzRQLjmxiknYjgktiRrZBcVcfhTDoWX/6tUbLjoHdPCdJOf+LL2ybWkm2uOQInQ/0SnByDOGmJFG6YoopWxqgYyLeAjFyOLpwC5csPsDlTZdIWK0HqPNWRN3udcF74gBsUCEDb9S6OGAlCiB4D4Bw==" +
                "\"," +
                "\"expiration\":1691643691000}" +
                ",\"videoUploadType\":1,\"serviceName\":\"s3\",\"gcsCredentials\":{\"expirationSeconds\":1691617770,\"expiration\":1691617770534,\"accessToken\":\"ya29.c.b0Aaekm1LUftkDdO3Pkxk9F5u3OK5TtVUnXonhDWtF4yNOFLvzQinAZrqugk3i8nlqSGnkfXthVYQ6axgdJIsAGld5QfXH9i2AoIUys-iWJIEOBAE9rrxLFeO1fr3a8g7wAZ7kGCgB2Nc0usqG2CXP6ft43-5SIhG1rj9-K-kPFkOEEme1_Z8Kh28taLHNFjE6zbS_lXniTgGznxdgaUSB9gSR0UsB4cuqqd2r7FDGnkjar95H6K55k1wXCGsSEHpK6bVlGdruryAu_cYfmUxKsFnuMna2h3nk1qFTLSBpqfIJrcdPwzaV1SJb1zl9dvMA9pddDlrTG337KF5-hw86mo5bUenx9JzkURXJ699zJ8Swiy-bbXmpqs5zJp0swVmxjIB1oa0Vk7JoVnasknWk2QsJb4XcaUYO77B4fknXkRucmdrhgsocyig_rk_1j4rwxO7dW2MtmiQSZv6rBUolk8f3XZZ4UggoyBQs3MfX8kF3IB1Vc60WRycR78Iqcub26XU9VgY98FWeyQut_omUV7wBhvaz7QFWgiqX_IqkksfQz5oSf2YVaf7tBb5tr4o-yncg7lzyllJcrQ3y0MZvRiihwQteZbJlxoSS97z3bduinV5ndQ6gBVIj7rMljgU4_24QddzaiZbFlk385h658fnMswB_3QX4w99Onvkgsjn_Rm975z_F_hrXk7snYI4zu-zqBgB886Vwlz4l4zSWIrtXkq58lRtj3SZkIVb0xr65zYSokze3rydImbjqaRvq5e8mtkUR6Y6gu9SFnW4qi4f7Iy0lhpgQ7BwtzaW8JmiM-dd_zM3th4yzZW8v-dx6Ui61eBQWVvMphkvW8fu6X6zdrVO15m-ediue-zgtJwM9VShlQjkiO5FkeV2t8zj4qwWix4qXk_vVo5JZcotca90k1y1i0s39q9MfSm0v_29h8a8-Yyxu2OiVqduQS_Ij5V1FFwjOI5vpjknI050XzRi_mk1vpya8p6xq12p3UyOl_aFkqdkZp3x\"},\"ociCredentials\":{\"accessUri\":[\"/p/gn88T-KudczOGcXfCW7hgiuW55HUGupbquyU4jObQmnwMHezs6kqnRFLbBQKRFbJ/n/idrw0j0fjn4v/b/a4x-prod-eu-ai-edge/o/\",\"/p/8WmUZBBwBeMFrIkI-gC5nTdS6cHuGuA6kgjVNuqK9YXK_OHbd4visjNSZNKM2ylx/n/idrw0j0fjn4v/b/prod-eu-vip-3d/o/\",\"/p/p9Xwlpzwo0KbqjWNNih7fDWFBqvXumPzvMYmvv0zRgks6absxeuinaUhbYAa_ka4/n/idrw0j0fjn4v/b/prod-eu-vip-7d/o/\",\"/p/QiYvjq0H_hxnlitmAw-Ou9WUmE9hr1gaVJH1lfmCWr0kZ9wD5zPwOqevPnNj9Ry-/n/idrw0j0fjn4v/b/prod-eu-vip-15d/o/\",\"/p/yqj43vHTrhmRR8BKUGqw_rLDGAEbjQ4ubu2dQklTseLOsdXp-7EKr-h0qwD4r1de/n/idrw0j0fjn4v/b/prod-eu-vip-30d/o/\",\"/p/MLrPuSF3ALBBy4T60caCfc_odWvyd8uelKEorW4jkvEsNC8iGq6eaKhOV55B4xse/n/idrw0j0fjn4v/b/prod-eu-vip-60d/o/\"],\"expirationSeconds\":1691693151}}}";
        JSONObject value = JSON.parseObject(json).getJSONObject("value");

//        if (value.getJSONObject("credentials").getIntValue("expirationSeconds") < PhosUtils.getUTCStamp()) {
//            log.info("credentials 已过期");
//            return;
//        }
//        String bucket=value.getString("bucket");
//        String region=value.getString("clientRegion");
//        String keyTemplate=value.getString("sliceKeyTemplate")

        JSONObject motionConfig = motion.getJSONObject("config").getJSONObject("config");
        String bucket = motionConfig.getJSONObject("bucket").getString("bucket");
//        String bucket = "addx-staging-vip-none";
//        String bucket = "addx-staging-vip-3d";
        String region = motionConfig.getJSONObject("bucket").getString("region");
        String keyTemplate = motionConfig.getString("keyTemplate");

        Credentials sessionCredentials = value.getJSONObject("credentials").toJavaObject(Credentials.class);
        BasicSessionCredentials awsCredentials = new BasicSessionCredentials(
                sessionCredentials.getAccessKeyId(),
                sessionCredentials.getSecretAccessKey(),
                sessionCredentials.getSessionToken());
        AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                .withRegion(region)
//                .withRegion("cn-north-1")
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                .withAccelerateModeEnabled(configJson.contains(".s3-accelerate."))
                .build();
        String key = keyTemplate
                .replace("${traceId}", traceId)
                .replace("${period}", "2000")
                .replace("${order}", "0")
                .replace("${isLast}", "0");
        ByteArrayInputStream bais = new ByteArrayInputStream("123".getBytes());
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, key, bais, new ObjectMetadata());
        putObjectRequest.setStorageClass(StorageClass.Standard);
//        putObjectRequest.setTagging(new ObjectTagging(Arrays.asList(new Tag("cdate", "20210331"))));
        PutObjectResult putObjectResult = s3Client.putObject(putObjectRequest);
        log.info("putObjectResult={}", JSON.toJSONString(putObjectResult, true));
    }


    //    @Test
    public void test_sts_staging_eu() {
        TestHelper testHelper = TestHelper.getInstanceByEnv("staging");
        JSONObject sts = testHelper.getConfig().getJSONObject("sts");
        AWSSecurityTokenService stsClient = testHelper.getAmazonInit().stsClient(sts.getString("clientRegion")
                , sts.getString("accessKey"), sts.getString("secretKey"));
        JSONObject videoslice = testHelper.getConfig().getJSONObject("videoslice");
        AwsCredentials sessionCredentials;
        try {
            AssumeRoleRequest assumeRoleRequest = new AssumeRoleRequest();
            assumeRoleRequest.setExternalId("db009e1e545e9787618fac04826ca50f");
            assumeRoleRequest.setRoleArn(videoslice.getString("roleArn"));
            assumeRoleRequest.setRoleSessionName(PhosUtils.getUUID());
            assumeRoleRequest.setDurationSeconds(43200);
            AssumeRoleResult assumeRoleResult = stsClient.assumeRole(assumeRoleRequest);
            sessionCredentials = new AwsCredentials(assumeRoleResult.getCredentials());
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, ResultCollection.AWS_ASSUME_ROLE_ERROR.getMsg(), e);
            return;
        }
    }

    //    @Test
    public void test_s3_staging_eu() {
        TestHelper testHelper = TestHelper.getInstanceByEnv("staging-eu");

        TenantAwsConfigDAO tenantAwsConfigDAO = new TenantAwsConfigDAO();
        tenantAwsConfigDAO.setTenantAwsConfigMapper(testHelper.getAmazonInit().tenantAwsConfigMapper());
        TenantAwsConfig paas_owned = tenantAwsConfigDAO.queryByTenantId("paas_owned");

        GetItemRequest getItemRequest = new GetItemRequest().withTableName("staging_eu_tenantAwsConfig")
                .withKey(Collections.singletonMap("tenantId", new AttributeValue().withS("paas_owned")));
        GetItemResult result = testHelper.getAmazonInit().dynamoDB().getItem(getItemRequest);

        JSONObject s3 = testHelper.getConfig().getJSONObject("s3");
        AmazonS3 s3Client = testHelper.getAmazonInit().newS3Client(s3.getString("clientRegion")
                , s3.getString("accessKey"), s3.getString("secretKey"), true);

        String bucket = s3.getString("bucket");
        String key = PhosUtils.getUUID();

        ByteArrayInputStream bais = new ByteArrayInputStream("123".getBytes());
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, key, bais, new ObjectMetadata());
        putObjectRequest.setTagging(new ObjectTagging(Arrays.asList(new Tag("cdate", "20210331"))));
        PutObjectResult putObjectResult = s3Client.putObject(putObjectRequest);
        log.info("putObjectResult={}", JSON.toJSONString(putObjectResult, true));
    }

    @Test
    public void test_assumeRole_netvue_prod_cn() {
        JSONObject config = ConfigHelper.loadYmlConfig("classpath:openapi/tenant_aws_config/netvue_prod-cn.yml");
        AwsHelper awsHelper = new AwsHelper(config.toJavaObject(TenantAwsConfig.class));
        Result<AwsCredentials> result = awsHelper.assumeRole("4cf245248b11d30008a6985a624b710e");
        log.info("test_assumeRole_netvue_prod_cn:{}", JSON.toJSONString(result));
    }

    @Test
    public void test_copyObject() {
        when(amazonS3Client.copyObject(any())).thenReturn(null);
        s3Service.copyObject("b1", "k1", "b2", "k2");
    }

    @Test
    public void test_putObject_str() {
        when(amazonS3Client.putObject(anyString(), anyString(), anyString())).thenReturn(null);
        s3Service.putObject("b1", "k1", "body");
    }

    @Test
    public void test_listObjects() {
        AtomicInteger count = new AtomicInteger(0);
        final int objNumPerQuery = 3;
        when(amazonS3Client.listObjects(any(ListObjectsRequest.class))).thenAnswer(it -> {
            ListObjectsRequest req = it.getArgument(0);
            ObjectListing listing = new ObjectListing();
            for (int i = 0; i < objNumPerQuery; i++) {
                S3ObjectSummary summary = new S3ObjectSummary();
                summary.setKey(req.getPrefix() + OpenApiUtil.shortUUID());
                listing.getObjectSummaries().add(summary);
            }
            if (count.getAndIncrement() < 3) {
                listing.setNextMarker(OpenApiUtil.shortUUID());
            }
            return listing;
        });
        List<String> keys = s3Service.listObjects("b1", "k1/k2/");
        Assert.assertEquals(objNumPerQuery * count.get(), keys.size());
    }

//    @Test
    @SneakyThrows
    public void test_downloadQuestionBackVideo() {
        TestHelper testHelper = TestHelper.getInstanceByEnv("staging");
        String bucketName = testHelper.getConfig().getJSONObject("s3").getString("bucket");
        String keyPrefix = "video_backup/device_video_slice/8a935a5333c9f59b46f55d2bfdaa9b21/09501659406406VOQCel26xluxS9G/";
        S3Service s3Service = new S3Service();
        s3Service.setS3Client(testHelper.getS3Client());
        final List<String> keys = s3Service.listObjects(bucketName, keyPrefix);
        String outFile = "/Users/<USER>/workspace/iot-service-old/target/" + keyPrefix.replace("/", "__") + ".zip";
        try (FileOutputStream os = new FileOutputStream(outFile);
             ZipOutputStream zos = new ZipOutputStream(os)) {
            for (String key : keys) {
                String name = key.substring(keyPrefix.length());
                log.info("downloadQuestionBackVideo putZipEntry name={}", name);
                zos.putNextEntry(new ZipEntry(name));
                S3Object s3Obj = s3Service.getObject(bucketName, key);
                IOUtils.copy(s3Obj.getObjectContent(), zos);
//                copy(s3Obj.getObjectContent(), zos);
                zos.closeEntry();
            }
        }
    }

    @SneakyThrows
    private static void copy(InputStream is, OutputStream os) {
        byte[] buf = new byte[1024];
        int len;
        while ((len = is.read(buf)) != -1) {
            os.write(buf, 0, len);
        }
    }

    @Test
    public void test_createAwsCredential() {
        final String sn = OpenApiUtil.shortUUID();
        final String redisKey = "s3_credential:" + sn;

        final Integer currentSeconds = PhosUtils.getUTCStamp();
        final AwsCredentials awsCredentials = new AwsCredentials().setSessionToken("1");
        final AwsCredentials awsCredentials2 = new AwsCredentials().setSessionToken("2");
        {
            when(redisService.get(redisKey)).thenAnswer(it -> "");
            awsCredentials.setExpirationSeconds(currentSeconds + 3600 * 2);
            final AwsCredentials cred = s3Service.createAwsCredential(sn, it -> awsCredentials2);
            Assert.assertEquals(awsCredentials2, cred);
        }
        {
            when(redisService.get(redisKey)).thenAnswer(it -> "null");
            awsCredentials.setExpirationSeconds(currentSeconds + 3600 * 2);
            final AwsCredentials cred = s3Service.createAwsCredential(sn, it -> awsCredentials2);
            Assert.assertEquals(awsCredentials2, cred);
        }
        {
            when(redisService.get(redisKey)).thenAnswer(it -> JSON.toJSONString(awsCredentials));
            awsCredentials.setExpirationSeconds(currentSeconds + 3600 - 1);
            final AwsCredentials cred = s3Service.createAwsCredential(sn, it -> awsCredentials2);
            Assert.assertEquals(awsCredentials2, cred);
        }
        {
            when(redisService.get(redisKey)).thenAnswer(it -> JSON.toJSONString(awsCredentials));
            awsCredentials.setExpirationSeconds(currentSeconds + 3600 * 2);
            final AwsCredentials cred = s3Service.createAwsCredential(sn, it -> awsCredentials2);
            Assert.assertEquals(awsCredentials, cred);
        }
        when(redisService.get(redisKey)).thenAnswer(it -> null);
        {
            final AwsCredentials cred = s3Service.createAwsCredential(sn, it -> {
                throw new RuntimeException("mock exception");
            });
            Assert.assertNull(cred);
        }
        {
            doNothing().doAnswer(it -> {
                throw new RuntimeException("mock exception");
            }).when(redisService).set(anyString(), anyString(), anyInt());
            final AwsCredentials cred = s3Service.createAwsCredential(sn, it -> awsCredentials2);
            Assert.assertEquals(awsCredentials2, cred);
        }
        {
            final AssumeRoleResult result = new AssumeRoleResult();
            final Credentials credentials3 = new Credentials();
            credentials3.setSessionToken("3");
            credentials3.setExpiration(new Date());
            result.setCredentials(credentials3);
            when(stsClient.assumeRole(any())).thenReturn(result);
            final AwsCredentials cred = s3Service.createAwsCredential(sn);
            Assert.assertEquals(new AwsCredentials(credentials3), cred);
        }
    }

    @Test
    public void test_preSignSaasAITaskOM() {
        final SaasAITaskOM empty = new SaasAITaskOM();
        s3Service.preSignSaasAITaskOM(empty);
        Assert.assertEquals(new SaasAITaskOM(), empty);

        final SaasAITaskOM input = new SaasAITaskOM();
        input.setImages(Arrays.asList(null, new SaasAITaskOM.ImageOM().setImageUrl(""),
                new SaasAITaskOM.ImageOM().setImageUrl("http://s3/image")
        ));
        input.setCoverImageUrl("http://s3/coverImage");
        input.setEvents(Arrays.asList(null, new SaasAITaskOM.EventOM().setSummaryUrl(""),
                new SaasAITaskOM.EventOM().setSummaryUrl("http://s3/summury")
        ));
        s3Service.preSignSaasAITaskOM(input);
    }

}
