package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.domain.AITask;
import com.addx.iotcamera.bean.openapi.PaasTenantInfo;
import com.addx.iotcamera.bean.openapi.RecognitionObjectCategory;
import com.addx.iotcamera.bean.openapi.SaasAITaskIM;
import com.addx.iotcamera.config.AiTaskParamsConfig;
import com.addx.iotcamera.config.PaasTenantConfigTest;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.config.opanapi.SaasAiTaskConfig;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.ActivityZoneService;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.S3Service;
import com.addx.iotcamera.service.VipService;
import com.addx.iotcamera.service.device.DeviceSupportService;
import com.addx.iotcamera.util.OpenApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.Random;

import static com.addx.iotcamera.service.video.VideoCacheServiceTest.createMockVideoCache;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoAIServiceTest {

    @InjectMocks
    private VideoAIService videoAIService;
    @Mock
    private VipService paasVipService;
    @Mock
    private StorageAllocateService storageAllocateService;
    @Mock
    private PaasTenantConfig paasTenantConfig;
    @Mock
    private S3Service s3Service;
    @Mock
    private SaasAiTaskConfig aiTaskConfig;
    @Mock
    private AiTaskParamsConfig aiTaskParamsConfig;
    @Mock
    private ActivityZoneService activityZoneService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private MqSender mqSender;
    @Mock
    private TimeTicker timeTicker;
    @Mock
    private VideoReportLogService videoReportLogService;

    @Mock
    private DeviceSupportService deviceSupportService;

    @Before
    public void init() {
        when(aiTaskConfig.getOutStorage()).thenReturn(new SaasAITaskIM.OutStorage());
        when(storageAllocateService.createOutStorage(any(), any())).thenAnswer(it -> it.getArgument(0));
        when(paasTenantConfig.getPaasTenantInfo(anyString())).thenReturn(new PaasTenantInfo());
//        videoAIService.setSaasAiTaskTopic("saasAiTaskTopic");
//        videoAIService.setVideoGenerateTopic("videoGenerateTopic");
//        videoAIService.setSaasAiTaskResultTopic("saasAiTaskResultTopic");
        PaasTenantConfigTest.mockPaasTenantConfig(this.paasTenantConfig);
    }

    @After
    public void after() {

    }

    @Test
    public void test_createDefaultSaasAITask() {
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);
        {
            video.setHasAiAbility(false);
            Assert.assertNotNull(videoAIService.createDefaultSaasAITask(video, Arrays.asList()));
        }
        video.setHasAiAbility(true);
        video.setIsBirdVip(true);
        {
            when(deviceInfoService.pushAiList(any(), any(), anyCollection())).thenReturn(Arrays.asList());
            Assert.assertNotNull(videoAIService.createDefaultSaasAITask(video, Arrays.asList()));
        }
        when(deviceInfoService.pushAiList(any(), any(), anyCollection())).thenReturn(Arrays.asList("person", "pet", "vehicle", "package"));
        {
            when(aiTaskParamsConfig.getIdentificationBoxByTenantId(anyString())).thenReturn(null);
            Assert.assertNotNull(videoAIService.createDefaultSaasAITask(video, Arrays.asList()));
        }
        AITask.IdentificationBox identificationBox = new AITask.IdentificationBox()
                .setColors(Arrays.asList(new AITask.Color().setColor("#556677").setName("person")));
        when(aiTaskParamsConfig.getIdentificationBoxByTenantId(anyString())).thenReturn(identificationBox);
        Assert.assertNotNull(videoAIService.createDefaultSaasAITask(video, Arrays.asList(AiObjectEnum.BIRD.getObjectName())));
    }


    @Test
    public void test_sendSaasAiTask() {
        Integer userId = (new Random()).nextInt(1000_0000);
        String sn = OpenApiUtil.shortUUID();
        String traceId = OpenApiUtil.shortUUID();
        VideoCache video = createMockVideoCache(sn, traceId);

        SaasAITaskIM aiTask = new SaasAITaskIM();
        aiTask.setActivityZoneList(new LinkedList<>());
        aiTask.setOutStorage(new SaasAITaskIM.OutStorage());
        video.setDefaultSaasAITask(aiTask);
        when(deviceSupportService.queryDeviceSupportBySn(anyString())).thenReturn(new CloudDeviceSupport());
        {
            aiTask.setRecognitionObjects(new LinkedList<>());
            Assert.assertTrue(videoAIService.sendSaasAiTask(video, null));
        }
        for (RecognitionObjectCategory category : RecognitionObjectCategory.values()) {
            aiTask.getRecognitionObjects().add(new SaasAITaskIM.RecognitionObject()
                    .setCategory(category).setFunctions(category.getPaasOwnedBizFunctions()));
        }
//        {
//            video.setBucket(null);
//            Assert.assertTrue(videoAIService.sendSaasAiTask(video, null));
//        }
//        {
//            video.setBucket("bucket1");
//            Assert.assertTrue(videoAIService.sendSaasAiTask(video, null));
//        }
        {
            doThrow(RuntimeException.class).when(mqSender).send(any(), anyString(), any());
            Assert.assertFalse(videoAIService.sendSaasAiTask(video, null));
        }
        {
            doNothing().when(mqSender).send(any(), anyString(), any());
            Assert.assertTrue(videoAIService.sendSaasAiTask(video, null));
        }
    }

    /*
    @Test
    public void test_buildOutParams() {
        final String accountId = OpenApiUtil.shortUUID();
        final String tenantId = OpenApiUtil.shortUUID();
        final VideoCache videoCache = new VideoCache("", "", 0L);
        videoCache.setAccountId(accountId);
        videoCache.setTenantId(tenantId);
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableAiResultToThird(true));
            final String str = videoAIService.buildOutParams(videoCache);
            final String expectStr = new JSONObject().fluentPut("outEncodeType", 0).fluentPut("accountId", accountId)
                    .fluentPut("tenantId", tenantId).toJSONString();
            Assert.assertEquals(expectStr, str);
        }
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableAiResultToThird(false));
            final String str = videoAIService.buildOutParams(videoCache);
            final String expectStr = new JSONObject().fluentPut("outEncodeType", 0).toJSONString();
            Assert.assertEquals(expectStr, str);
        }
    }

    @Test
    public void test_buildOutputTopic() {
        final String accountId = OpenApiUtil.shortUUID();
        final String tenantId = OpenApiUtil.shortUUID();
        final VideoCache videoCache = new VideoCache("", "", 0L);
        videoCache.setAccountId(accountId);
        videoCache.setTenantId(tenantId);
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableAiResultToThird(true));
            final String str = videoAIService.buildOutputTopic(videoCache);
            Assert.assertEquals("saasAiTaskResultTopic", str);
        }
        {
            when(paasTenantConfig.getPaasTenantInfo(tenantId)).thenReturn(new PaasTenantInfo().setEnableAiResultToThird(false));
            final String str = videoAIService.buildOutputTopic(videoCache);
            Assert.assertEquals("videoGenerateTopic", str);
        }
    }
    */

}
