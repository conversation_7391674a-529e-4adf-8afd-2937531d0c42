package com.addx.iotcamera.service.deviceplatform.alexa.safemo;

import com.addx.iotcamera.bean.app.alexa.result.DeviceInfoAndStatusDO;
import com.addx.iotcamera.service.RedisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.integration.redis.util.RedisLockRegistry;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest(RedisLockRegistry.class)
public class AlexaSafemoMsgDataServiceTest {

    @InjectMocks
    AlexaSafemoMsgDataService alexaSafemoMsgDataService;
    @Mock
    private RedisService redisService;
    @Mock
    private RedisLockRegistry redisLockRegistry;
    @Mock
    Lock lock;

    @Before
    public void init() throws Exception {
        PowerMockito.whenNew(RedisLockRegistry.class).withAnyArguments().thenReturn(redisLockRegistry);
    }


    @Test
    public void addDeviceList() throws InterruptedException {
        String messageId = "1";
        String redisKey = "alexa:safemo:device:list:" + messageId;
        List<DeviceInfoAndStatusDO> deviceList = anyList();
        String deviceListRedisKey = "alexa:safemo:device:list:" + messageId;

        // 条件1
        alexaSafemoMsgDataService.addDeviceList(null, deviceList);

        // 条件2
        alexaSafemoMsgDataService.addDeviceList(messageId, null);

        // 条件3
        when(redisLockRegistry.obtain(redisKey + ":lockKey")).thenReturn(lock);
        when(lock.tryLock(300, TimeUnit.MILLISECONDS)).thenReturn(false);
        alexaSafemoMsgDataService.addDeviceList(messageId, deviceList);

        // 条件4
        when(redisLockRegistry.obtain(redisKey + ":lockKey")).thenReturn(lock);
        when(lock.tryLock(1, TimeUnit.MILLISECONDS)).thenReturn(true);
        when(redisService.get(deviceListRedisKey)).thenReturn(null);
        alexaSafemoMsgDataService.addDeviceList(messageId, deviceList);

        // 条件5
        deviceList.add(new DeviceInfoAndStatusDO());
        when(redisService.get(deviceListRedisKey)).thenReturn(JSON.toJSONString(deviceList));
        alexaSafemoMsgDataService.addDeviceList(messageId, deviceList);
    }

    @Test
    public void setDeviceInfoAndStatus() {
        String messageId = "1";
        alexaSafemoMsgDataService.setDeviceInfoAndStatus(messageId, new DeviceInfoAndStatusDO());
    }

    @Test
    public void getDeviceList() {
        String messageId = "1";
        String deviceListRedisKey = "alexa:safemo:device:list:" + messageId;
        List<DeviceInfoAndStatusDO> deviceList = new ArrayList<>();
        deviceList.add(new DeviceInfoAndStatusDO().setDeviceName("aaa"));

        // 条件1
        when(redisService.get(deviceListRedisKey)).thenReturn(null);
        List<DeviceInfoAndStatusDO> deviceListResult = alexaSafemoMsgDataService.getDeviceList(messageId);
        Assert.assertTrue(deviceListResult.isEmpty());

        // 条件2
        when(redisService.get(deviceListRedisKey)).thenReturn(JSON.toJSONString(deviceList));
        deviceListResult = alexaSafemoMsgDataService.getDeviceList(messageId);
        Assert.assertFalse(deviceListResult.isEmpty());

        // 条件3
        when(redisService.get(deviceListRedisKey)).thenReturn("aaa");
        deviceListResult = alexaSafemoMsgDataService.getDeviceList(messageId);
        Assert.assertTrue(deviceListResult.isEmpty());
    }

    @Test
    public void getDeviceInfoAndStatus() {
        String messageId = "1";
        String deviceInfoRedisKey = "alexa:safemo:device:info:status:" + messageId;

        // 条件1
        when(redisService.get(deviceInfoRedisKey)).thenReturn(null);
        DeviceInfoAndStatusDO deviceInfoAndStatus = alexaSafemoMsgDataService.getDeviceInfoAndStatus(messageId);
        Assert.assertNull(deviceInfoAndStatus);

        // 条件2
        when(redisService.get(deviceInfoRedisKey)).thenReturn(JSON.toJSONString(new DeviceInfoAndStatusDO().setDeviceName("aaa")));
        deviceInfoAndStatus = alexaSafemoMsgDataService.getDeviceInfoAndStatus(messageId);
        Assert.assertNotNull(deviceInfoAndStatus);
    }
}