package com.addx.iotcamera.service.proto;

import com.addx.iotcamera.bean.device_msg.DeviceRetainedMsg;
import com.addx.iotcamera.enums.ProtoMsgName;
import com.addx.iotcamera.mqtt.handler.extend.StatusHandler;
import com.addx.iotcamera.service.device_msg.DeviceMsgService;
import com.addx.iotcamera.service.device_msg.DeviceMsgServiceInterface;
import com.addx.iotcamera.service.proto_msg.ProtoMsgConvertor;
import com.addx.iotcamera.service.proto_msg.ProtoMsgTrans;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.GeneratedMessage;
import io.reactivex.functions.Function;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * protoMsg验证服务
 * 1、把JSON请求转成proto对象，再转回JSON，比较前后是否有差异
 */
@Slf4j
//@ConditionalOnProperty(name = "", havingValue = "")
//@Component
//@Configuration
public class ProtoMsgValidateService {

    //    @Lazy
//    @Autowired
//    @Qualifier("deviceMsgService")
    private DeviceMsgService deviceMsgService;

    @Primary
    @Bean("deviceMsgServiceProxy")
    public DeviceMsgServiceInterface deviceMsgServiceProxy() {
        return new DeviceMsgServiceInterface() {

            @Override
            public Result httpTokenByJson(String sn, String payload) {
                return validate(ProtoMsgName.httpToken.name(), sn, payload, () -> {
                    return deviceMsgService.httpTokenByJson(sn, payload);
                });
            }

            @Override
            public Result statusByJson(String sn, String payload) {
                return validate(ProtoMsgName.status.name(), sn, payload, () -> {
                    return deviceMsgService.statusByJson(sn, payload);
                });
            }

            @Override
            public Result connectionByJson(String sn, String payload) {
                return validate(ProtoMsgName.connection.name(), sn, payload, () -> {
                    return deviceMsgService.connectionByJson(sn, payload);
                });
            }

            @Override
            public Result requestByJson(String msgName, String sn, String payload) {
                return validate(msgName, sn, payload, () -> {
                    return deviceMsgService.requestByJson(msgName, sn, payload);
                });
            }

            @Override
            public Result<List<DeviceRetainedMsg>> queryRetainedMsgByJson(String sn, String payload) {
                JSONObject payloadObj = JSON.parseObject(payload);
                String msgName = getMsgNameFromQueryRetainedMsg(payloadObj);
                return validate(msgName, sn, payload, () -> {
                    return deviceMsgService.queryRetainedMsgByJson(sn, payload);
                });
            }
        };
    }

    public static String getMsgNameFromQueryRetainedMsg(JSONObject payloadObj) {
        List<String> names = payloadObj.getJSONArray("names").toJavaList(String.class);
        List<String> msgNames = names.stream().map(TextUtil::toCamel).filter(it -> ProtoMsgName.msgNameOf(it) != null).collect(Collectors.toList());
        assert msgNames.size() == 1;
        return msgNames.get(0);
    }

    public <T> T validate(String msgNameStr, String sn, String payload, Supplier<T> supplier) {
        final ProtoMsgName msgName = ProtoMsgName.msgNameOf(msgNameStr);
        if (msgName == null) {
            log.error("validate invalid! msgName={},sn={}", msgNameStr, sn);
            return supplier.get();
        }
        try {
            validReqBody(new DeviceMsg().setType(0).setMsgName(msgNameStr).setReqBody(JSON.parseObject(payload)));
        } catch (Throwable e) {
            log.error("validate payload error! msgName={},sn={},payload={}", msgNameStr, sn, payload, e);
        }
        T result = null;
        try {
            return result = supplier.get();
        } finally {
            if (result != null) {
                String resultJson = JSON.toJSONString(result);
                try {
                    validReqBody(new DeviceMsg().setType(1).setMsgName(msgNameStr).setReqBody(JSON.parseObject(resultJson)));
                } catch (Throwable e) {
                    log.error("validate result error! msgName={},sn={},result={}", msgNameStr, sn, resultJson, e);
                }
            }
        }
    }

    public void validate(ProtoMsgName msgName, String sn, ProtoMsgConvertor<?> convertor, String inputStr) {
        String bpMsgClsName = convertor.getCls().getSimpleName();
        String logPrefix = bpMsgClsName.endsWith("Response") ? "validate result" : "validate payload";

        boolean pass = true;

        GeneratedMessage pbMsg = convertor.parseJsonStr(inputStr);
        String jsonStr = convertor.printJsonStr(pbMsg);
        GeneratedMessage pbMsg2 = convertor.parseJsonStr(jsonStr);
        if (!Objects.equals(pbMsg, pbMsg2)) {
            log.warn("{} : pbMsg not equals! msgName={},sn={},bpMsgClsName={},pbMsg={},pbMsg2={}", logPrefix, msgName, sn, bpMsgClsName, comparePbMsg(pbMsg), comparePbMsg(pbMsg2));
            pass = false;
        }
//        String jsonStrFromInput = toNormalizingJsonString(JSON.parseObject(inputStr));
//        String jsonStrFromPbMsg = toNormalizingJsonString(JSON.parseObject(jsonStr));
        JSONObject obj1 = JSON.parseObject(inputStr);
        JSONObject obj2 = JSON.parseObject(jsonStr);
        Map<String, int[]> path2Count = new LinkedHashMap<>();
        boolean eqFlag = compareJson(obj1, obj2, "$", path2Count);
        if (!eqFlag) {
            log.warn("{} : json not equals! msgName={},sn={},bpMsgClsName={},path2Count={},inputStr={}", logPrefix, msgName, sn, bpMsgClsName, comparePbMsg(CountIndex.getNotEqNum(), path2Count), inputStr);
            pass = false;
        }
        if (pass) {
            int jsonByteNum = inputStr.getBytes(StandardCharsets.UTF_8).length;
            int protoByteNum = pbMsg2.toByteArray().length;
            log.warn("{} : ok! msgName={},sn={},bpMsgClsName={},jsonByteNum={},protoByteNum={},path2Count={},inputStr={},pbMsg={}", logPrefix, msgName, sn, bpMsgClsName
                    , jsonByteNum, protoByteNum, comparePbMsg(CountIndex.getEqNum(), path2Count), inputStr, comparePbMsg(pbMsg));
        }
    }

    public static boolean isEquals(Map<String, int[]> path2Count) {
        for (CountIndex index : CountIndex.getNotEqNum()) {
            for (int[] value : path2Count.values()) {
                if (value[index.ordinal()] > 0) return false;
            }
        }
        return true;
    }

    public static String comparePbMsg(List<CountIndex> enums, Map<String, int[]> path2Count) {
        Map<String, Map<String, Integer>> name2Path2Num = new LinkedHashMap<>();
        for (CountIndex index : enums) {
//            Map<String, Integer> path2Num = name2Path2Num.computeIfAbsent(index.name(), k -> new LinkedHashMap<>());
            Map<String, Integer> path2Num = new LinkedHashMap<>();
            for (Map.Entry<String, int[]> entry : path2Count.entrySet()) {
                String path = entry.getKey();
                int num = entry.getValue()[index.ordinal()];
                if (num == 0) continue;
                path2Num.compute(path, (k, v) -> v == null ? num : v + num);
            }
            if (!path2Num.isEmpty()) {
                name2Path2Num.compute(index.name(), (k, v) -> {
                    if (v == null) {
                        return path2Num;
                    } else {
                        v.putAll(path2Num);
                        return v;
                    }
                });
            }
        }
        return JSON.toJSONString(name2Path2Num, true);
    }

    public enum CountIndex {
        allNullEqNum,
        anyNullNotEqNum,

        objEqNum,
        objNotEqNum,

        arrEqNum,
        arrNotEqNum,

        valEqNum,
        valNotEqNum,
        ;

        public static int[] createCountArray() {
            int[] arr = new int[values().length];
            Arrays.fill(arr, 0);
            return arr;
        }

        public static List<CountIndex> getNotEqNum() {
            return Arrays.stream(values()).filter(it -> it.name().endsWith("NotEqNum")).collect(Collectors.toList());
        }

        public static List<CountIndex> getEqNum() {
            return Arrays.stream(values()).filter(it -> !it.name().endsWith("NotEqNum") && it.name().endsWith("EqNum")).collect(Collectors.toList());
        }
    }

    public static boolean compareJson(Object root1, Object root2, String path, Map<String, int[]> path2Count) {
        int[] count = path2Count.computeIfAbsent(path, k -> CountIndex.createCountArray());
        boolean eqFlag = true;
        if (root1 == null && root2 == null) {
            count[CountIndex.allNullEqNum.ordinal()]++;
        } else if (root1 == null || root2 == null) {
            count[CountIndex.anyNullNotEqNum.ordinal()]++;
            eqFlag = false;
        } else if (root1 instanceof JSONObject && root2 instanceof JSONObject) {
            JSONObject obj1 = (JSONObject) root1;
            JSONObject obj2 = (JSONObject) root2;
            Set<String> keys = new LinkedHashSet<>();
            keys.addAll(obj1.keySet());
            keys.addAll(obj2.keySet());
            for (String key : keys) {
                eqFlag &= compareJson(obj1.get(key), obj1.get(key), path + "." + key, path2Count);
            }
            count[eqFlag ? CountIndex.objEqNum.ordinal() : CountIndex.objNotEqNum.ordinal()]++;
        } else if (root1 instanceof JSONArray && root2 instanceof JSONArray) {
            JSONArray arr1 = (JSONArray) root1;
            JSONArray arr2 = (JSONArray) root2;
            int size = Math.max(arr1.size(), arr2.size());
            for (int i = 0; i < size; i++) {
                eqFlag &= compareJson(i < arr1.size() ? arr1.get(i) : null, i < arr2.size() ? arr2.get(i) : null, path + ".[]", path2Count);
            }
            count[eqFlag ? CountIndex.arrEqNum.ordinal() : CountIndex.arrNotEqNum.ordinal()]++;
        } else {
            String str1 = toNormalizingJsonString(root1);
            String str2 = toNormalizingJsonString(root2);
            eqFlag = str1.equals(str2);
            count[eqFlag ? CountIndex.valEqNum.ordinal() : CountIndex.valNotEqNum.ordinal()]++;
        }
        return eqFlag;
    }

    public static String toNormalizingJsonString(Object root) {
        if (root == null) {
            return null;
        } else if (root instanceof String) {
            return "\"" + root + "\"";
        } else if (root instanceof Map) {
            Map<String, Object> obj = (Map<String, Object>) root;
            return obj.keySet().stream().sorted().map(key -> {
                String value = toNormalizingJsonString(obj.get(key));
                if (value == null) return null;
                return "\"" + key + "\":" + value;
            }).filter(Objects::nonNull).collect(Collectors.joining(",", "{", "}"));
        } else if (root instanceof Collection) {
            Collection<Object> arr = (Collection<Object>) root;
            return arr.stream().map(it -> toNormalizingJsonString(it)).filter(Objects::nonNull).sorted()
                    .collect(Collectors.joining(",", "[", "]"));
        } else {
            return root.toString();
        }
    }

    public static String comparePbMsg(GeneratedMessage pbMsg) {
//        return pbMsg.toString().replaceAll("\n", " ; ");
        return pbMsg.toString();
    }

    public static String comparePbMsg(GeneratedMessage pbMsg1, GeneratedMessage pbMsg2) {
        String[] lines1 = pbMsg1.toString().split("\n");
        String[] lines2 = pbMsg2.toString().split("\n");
        StringBuilder builder = new StringBuilder("\n");
        builder.append("Compare GeneratedMessage object:").append("\n");
        builder.append("Object equals:").append(pbMsg1.equals(pbMsg2)).append("\n");
        builder.append("toString equals:").append(pbMsg1.toString().equals(pbMsg2.toString())).append("\n");
        int width1 = 0;
        int width2 = 0;
        for (int i = 0; i < Math.max(lines1.length, lines2.length); i++) {
            String line1 = i < lines1.length ? lines1[i] : "";
            String line2 = i < lines2.length ? lines2[i] : "";
            width1 = Math.max(width1, line1.length());
            width2 = Math.max(width2, line2.length());
        }
        builder.append(StringUtils.repeat('-', width1 + width2 + 22)).append("\n");
        for (int i = 0; i < Math.max(lines1.length, lines2.length); i++) {
            String line1 = i < lines1.length ? lines1[i] : "";
            String line2 = i < lines2.length ? lines2[i] : "";
            builder.append(String.format("| %-" + width1 + "s \t|  %-" + width2 + "s \t| %s |%n", line1, line2, line1.equals(line2)));
        }
        builder.append(StringUtils.repeat('-', width1 + width2 + 22)).append("\n");
        return builder.toString();
    }

    @Data
    @Accessors(chain = true)
    public static class DeviceMsg {
        private String msgName;
        private Integer type; // 0-req;1-resp
        private JSONObject reqBody;
    }

    @Data
    @Accessors(chain = true)
    public static class ValidReqBodyResult {
        private boolean ok = false;
        private ProtoMsgName msgName;
        private JSONObject reqBody;
        private GeneratedMessage pbMsg;
    }

    @SneakyThrows
    public static ValidReqBodyResult validReqBody(DeviceMsg deviceMsg) {
        JSONObject reqBody;
        String msgNameStr;
        if ("queryRetainedMsg".equals(deviceMsg.getMsgName())) {
            JSONObject reqBody0 = deviceMsg.getReqBody();
            if (deviceMsg.getType() == 0) {
                msgNameStr = getMsgNameFromQueryRetainedMsg(reqBody0);
                reqBody = new JSONObject();
            } else {
                JSONObject retainedMsg = reqBody0.getJSONArray("data").toJavaList(JSONObject.class).stream().findFirst().orElse(null);
                msgNameStr = TextUtil.toCamel(retainedMsg.getString("name"));
                reqBody = new JSONObject().fluentPutAll(reqBody0).fluentPut("data", retainedMsg.getJSONObject("value"));
            }
        } else {
            reqBody = deviceMsg.getReqBody();
            msgNameStr = deviceMsg.getMsgName();
        }
        ProtoMsgName msgName = ProtoMsgName.msgNameOf(msgNameStr);
        ValidReqBodyResult failResult = new ValidReqBodyResult().setMsgName(msgName).setReqBody(reqBody);

        ProtoMsgConvertor<? extends GeneratedMessage> convertor;
        if (deviceMsg.getType() == 0) {
            convertor = msgName.getProtoMsgConvertor();
            if (msgName == ProtoMsgName.status) {
                reqBody.put("statusList", StatusHandler.getStatusList(reqBody));
            }
        } else {
            JSONObject reqBody2 = transReqBody(msgName, reqBody);
            Function<JSONObject, JSONObject> handler = ProtoMsgTrans.msgName2ParsePreHandler.get(msgName);
            if (handler != null) {
                handler.apply(reqBody);
            }
            Map<String, int[]> path2Count = new LinkedHashMap<>();
            ProtoMsgValidateService.compareJson(reqBody, reqBody2, "$", path2Count);
            String path2CountStr = ProtoMsgValidateService.comparePbMsg(Arrays.asList(CountIndex.values()), path2Count);
            if (!ProtoMsgValidateService.isEquals(path2Count)) {
                log.info("check msgName2ParsePreHandler fail! deviceMsg={},reqBody={},path2Count:{}", JSON.toJSONString(deviceMsg, true), JSON.toJSONString(reqBody, true), path2CountStr);
                return failResult;
            }
            convertor = msgName.getProtoResponseMsgConvertor();
        }
        GeneratedMessage pbMsg = convertor.parseJsonStr(reqBody.toJSONString());
        if (pbMsg == null) {
            log.info("check proto json to pbMsg fail! deviceMsg={}", JSON.toJSONString(deviceMsg, true));
            return failResult;
        }
        String pbMsgJsonStr = convertor.printJsonStr(pbMsg);
        JSONObject pbMsgJsonObj = JSON.parseObject(pbMsgJsonStr);
        Map<String, int[]> path2Count = new LinkedHashMap<>();
        ProtoMsgValidateService.compareJson(reqBody, pbMsgJsonObj, "$", path2Count);
        String path2CountStr = ProtoMsgValidateService.comparePbMsg(Arrays.asList(CountIndex.values()), path2Count);
        if (!ProtoMsgValidateService.isEquals(path2Count)) {
            log.info("check proto json eq pbMsgJsonStr fail! deviceMsg={},pbMsgJsonStr={},path2Count:{}", JSON.toJSONString(deviceMsg, true), JSON.toJSONString(pbMsgJsonObj, true), path2CountStr);
            return failResult;
        }
        GeneratedMessage pbMsg2 = convertor.parseJsonStr(pbMsgJsonStr);
        if (pbMsg2 == null) {
            log.info("check proto pbMsgJsonStr to pbMsg2 fail! deviceMsg={},pbMsgJsonStr={},pbMsg={}", JSON.toJSONString(deviceMsg, true), JSON.toJSONString(pbMsgJsonObj, true), comparePbMsg(pbMsg));
            return failResult;
        }
        if (!pbMsg.equals(pbMsg2)) {
            log.info("check proto pbMsg eq pbMsg2 fail! deviceMsg={},comparePbMsg={}", JSON.toJSONString(deviceMsg, true), comparePbMsg(pbMsg, pbMsg2));
            return failResult;
        }
        byte[] bytes = pbMsg.toByteArray();
        String bytesBase64 = Base64.getEncoder().encodeToString(bytes);
        GeneratedMessage pbMsg3 = convertor.parseFrom(bytes);
        if (pbMsg3 == null) {
            log.info("check proto bytes to pbMsg3 fail! deviceMsg={},bytesLength={},bytesBase64={},pbMsg={}", JSON.toJSONString(deviceMsg, true), bytes.length, bytesBase64, comparePbMsg(pbMsg));
            return failResult;
        }
        if (!pbMsg.equals(pbMsg3)) {
            log.info("check proto pbMsg eq pbMsg3 fail! deviceMsg={},bytesLength={},bytesBase64={},comparePbMsg={}", JSON.toJSONString(deviceMsg, true), bytes.length, bytesBase64, comparePbMsg(pbMsg, pbMsg3));
            return failResult;
        }
        log.info("check proto pbMsg eq pbMsg2 ok! deviceMsg={},bytesLength={},bytesBase64={},pbMsg:\n{}", JSON.toJSONString(deviceMsg, true), bytes.length, bytesBase64, comparePbMsg(pbMsg));
        return new ValidReqBodyResult().setMsgName(msgName).setReqBody(reqBody).setOk(true).setPbMsg(pbMsg);
    }

    public static JSONObject transReqBody(ProtoMsgName msgName, JSONObject reqBody0) {
        JSONObject reqBody = JSON.parseObject(JSON.toJSONString(reqBody0));
        if (msgName == ProtoMsgName.config) {
            Optional.ofNullable(reqBody.getJSONObject("data")).map(it -> it.getJSONObject("dns")).ifPresent(dns -> {
                dns.replaceAll((k, v) -> new JSONObject().fluentPut("ip", dns.get(k)));
            });

        } else if (msgName == ProtoMsgName.setting) {
            Optional.ofNullable(reqBody.getJSONObject("data")).map(it -> it.getJSONObject("value")).ifPresent(value -> {
                Object val = value.remove("floodlightSchedulePlan");
                if (val instanceof String && StringUtils.isNotBlank((String) val)) {
                    value.put("floodlightSchedulePlan", JSON.parseArray((String) val));
                }
            });

        } else if (msgName == ProtoMsgName.dormancyPlanSetting) {
            Optional.ofNullable(reqBody.getJSONObject("data")).map(it -> it.getJSONObject("value")).ifPresent(value -> {
                value.forEach((k, v) -> {
                    value.put(k, new JSONObject().fluentPut("plans", v));
                });
            });

        }
        if (msgName == ProtoMsgName.syncApInfo) {
            JSONObject data = reqBody.getJSONObject("data").getJSONObject("value");
            if (data != null) {
                String value = data.getString("apInfo");
                if (value != null) {
                    data.put("apInfo", JSON.parseObject(value));
                }
            }
        }
        return reqBody;
    }

    public static boolean equals(GeneratedMessage pbMsg1, GeneratedMessage pbMsg2) {
        if (pbMsg1 == null && pbMsg2 == null) return true;
        if (pbMsg1 == null || pbMsg2 == null) return false;
        return pbMsg1.toString().equals(pbMsg2.toString());
    }

}
