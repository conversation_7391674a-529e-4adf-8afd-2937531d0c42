package com.addx.iotcamera.service.lettuce.service.base;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LettuceBaseServiceTest {

    @InjectMocks
    LettuceBaseService<String, String> lettuceBaseService;

    @Mock
    RedisTemplate<String, String> redisTemplate;

    @Test
    public void getRedisValue() {
        assertNull(lettuceBaseService.getRedisValue(null, () -> "a"));
        assertNotNull(lettuceBaseService.getRedisValue(redisTemplate, () -> "a"));
    }

    @Test
    public void getRedisList() {
        assertNull(lettuceBaseService.getRedisList(null, ArrayList::new));
        assertNotNull(lettuceBaseService.getRedisList(redisTemplate, ArrayList::new));
    }

    @Test
    public void getRedisSet() {
        assertNull(lettuceBaseService.getRedisSet(null, HashSet::new));
        assertNotNull(lettuceBaseService.getRedisSet(redisTemplate, HashSet::new));

    }

    @Test
    public void getRedisSetWithScore() {
        assertNull(lettuceBaseService.getRedisSetWithScore(null, HashSet::new));
        assertNotNull(lettuceBaseService.getRedisSetWithScore(redisTemplate, HashSet::new));
    }

    @Test
    public void getRedisObjMap() {
        assertNull(lettuceBaseService.getRedisObjMap(null, HashMap::new));
        assertNotNull(lettuceBaseService.getRedisObjMap(redisTemplate, HashMap::new));
    }

    @Test
    public void getRedisMap() {
        assertNull(lettuceBaseService.getRedisMap(null, HashMap::new));
        assertNotNull(lettuceBaseService.getRedisMap(redisTemplate, HashMap::new));
    }

}