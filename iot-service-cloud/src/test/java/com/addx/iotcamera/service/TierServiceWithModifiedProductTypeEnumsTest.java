package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.vip.TierProductDeviceNumDO;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.dao.TierDao;
import com.addx.iotcamera.enums.ProductTypeEnums;
import com.addx.iotcamera.service.vip.TierService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.List;

import static com.addx.iotcamera.enums.ProductTypeEnums.PRODUCT_DEVICE_NUM;
import static com.addx.iotcamera.enums.ProductTypeEnums.PRODUCT_LEVEL_DEVICE_NUM;
import static com.addx.iotcamera.enums.pay.TierServiceTypeEnums.TIER_4G;
import static com.addx.iotcamera.enums.pay.TierServiceTypeEnums.TIER_CLOID_SERVICE;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@PrepareForTest({ProductTypeEnums.class})
@RunWith(PowerMockRunner.class)
public class TierServiceWithModifiedProductTypeEnumsTest {

    @InjectMocks
    private TierService tierService;

    @Mock
    private ProductService productService;

    @Mock
    private TierDao tierDao;

    @Test(expected = BaseException.class)
    public void testInitTierListV2_productTypeList_empty() {
        mockStatic(ProductTypeEnums.class);
        when(ProductTypeEnums.queryProductTypeListByTierServiceType(TIER_CLOID_SERVICE)).thenReturn(Lists.newArrayList());
        TierProductDeviceNumDO actualResult = tierService.initTierListV2(TIER_CLOID_SERVICE, TENANTID_VICOO, false, 0);
        assertNotNull(actualResult);
    }

    @Test
    public void testInitTierListV2_TIER_CLOID_SERVICE_empty_highLevelProductType() {
        mockStatic(ProductTypeEnums.class);

        when(ProductTypeEnums.queryProductTypeListByTierServiceType(TIER_CLOID_SERVICE)).thenReturn(Arrays.asList(3, 4));


        // Arrange
        String tenantId = "test-tenant";

        ProductDO product1 = new ProductDO();
        product1.setId(1);
        product1.setType(PRODUCT_DEVICE_NUM.getCode());
        product1.setTierId(1);
        product1.setSubscriptionPeriod(0);
        product1.setShowInTier(1);

        ProductDO product2 = new ProductDO();
        product2.setId(2);
        product2.setType(PRODUCT_DEVICE_NUM.getCode());
        product2.setTierId(2);
        product2.setSubscriptionPeriod(1);
        product2.setShowInTier(1);

        ProductDO product3 = new ProductDO();
        product3.setId(3);
        product3.setType(PRODUCT_DEVICE_NUM.getCode());
        product3.setTierId(3);
        product3.setSubscriptionPeriod(1);
        product3.setShowInTier(1);




        ProductDO product4 = new ProductDO();
        product4.setId(4);
        product4.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product4.setTierId(4);
        product4.setSubscriptionPeriod(0);
        product4.setShowInTier(1);

        ProductDO product5 = new ProductDO();
        product5.setId(5);
        product5.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product5.setTierId(5);
        product5.setSubscriptionPeriod(1);
        product5.setShowInTier(1);


        ProductDO product6 = new ProductDO();
        product6.setId(6);
        product6.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product6.setTierId(6);
        product6.setSubscriptionPeriod(1);
        product6.setShowInTier(1);

        List<ProductDO> productList = Arrays.asList(product1, product2, product3, product4,product5,product6);
        when(productService.queryProductListByType(Arrays.asList(PRODUCT_DEVICE_NUM.getCode(), PRODUCT_LEVEL_DEVICE_NUM.getCode()), tenantId, false, 0)).thenReturn(productList);


        Tier tier1 = new Tier();
        tier1.setTierId(1);
        tier1.setMaxDeviceNum(1);
        tier1.setRollingDays(30);
        tier1.setLevel(1);

        Tier tier2 = new Tier();
        tier2.setTierId(2);
        tier2.setMaxDeviceNum(null);
        tier2.setRollingDays(30);
        tier2.setLevel(2);


        Tier tier4 = new Tier();
        tier4.setTierId(4);
        tier4.setMaxDeviceNum(2);
        tier4.setRollingDays(30);
        tier4.setLevel(1);

        Tier tier5 = new Tier();
        tier5.setTierId(5);
        tier5.setMaxDeviceNum(null);
        tier5.setRollingDays(30);
        tier5.setLevel(2);

        List<Tier> tierList = Arrays.asList(tier1, tier2,tier4,tier5);

        when(tierDao.getTierByTierIds(any())).thenReturn(tierList);
        when(ProductTypeEnums.queryProductTypeByTierServiceTypeV2(TIER_CLOID_SERVICE)).thenReturn(null);

        // Act
        TierProductDeviceNumDO result = tierService.initTierListV2(TIER_CLOID_SERVICE,tenantId, false, 0);
        assertEquals(null, result.getTierV2List().getMonthlyProductList());
        assertEquals(null, result.getTierV2List().getYearlyProductList());
    }

    @Test
    public void testInitTierListV2_TIER_CLOID_SERVICE_empty_lowLevelProductType() {
        mockStatic(ProductTypeEnums.class);

        when(ProductTypeEnums.queryProductTypeListByTierServiceType(TIER_CLOID_SERVICE)).thenReturn(Arrays.asList(3, 4));


        // Arrange
        String tenantId = "test-tenant";

        ProductDO product1 = new ProductDO();
        product1.setId(1);
        product1.setType(PRODUCT_DEVICE_NUM.getCode());
        product1.setTierId(1);
        product1.setSubscriptionPeriod(0);
        product1.setShowInTier(1);

        ProductDO product2 = new ProductDO();
        product2.setId(2);
        product2.setType(PRODUCT_DEVICE_NUM.getCode());
        product2.setTierId(2);
        product2.setSubscriptionPeriod(1);
        product2.setShowInTier(1);

        ProductDO product3 = new ProductDO();
        product3.setId(3);
        product3.setType(PRODUCT_DEVICE_NUM.getCode());
        product3.setTierId(3);
        product3.setSubscriptionPeriod(1);
        product3.setShowInTier(1);




        ProductDO product4 = new ProductDO();
        product4.setId(4);
        product4.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product4.setTierId(4);
        product4.setSubscriptionPeriod(0);
        product4.setShowInTier(1);

        ProductDO product5 = new ProductDO();
        product5.setId(5);
        product5.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product5.setTierId(5);
        product5.setSubscriptionPeriod(1);
        product5.setShowInTier(1);


        ProductDO product6 = new ProductDO();
        product6.setId(6);
        product6.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product6.setTierId(6);
        product6.setSubscriptionPeriod(1);
        product6.setShowInTier(1);

        List<ProductDO> productList = Arrays.asList(product1, product2, product3, product4,product5,product6);
        when(productService.queryProductListByType(Arrays.asList(PRODUCT_DEVICE_NUM.getCode(), PRODUCT_LEVEL_DEVICE_NUM.getCode()), tenantId, false, 0)).thenReturn(productList);


        Tier tier1 = new Tier();
        tier1.setTierId(1);
        tier1.setMaxDeviceNum(1);
        tier1.setRollingDays(30);
        tier1.setLevel(1);

        Tier tier2 = new Tier();
        tier2.setTierId(2);
        tier2.setMaxDeviceNum(null);
        tier2.setRollingDays(30);
        tier2.setLevel(2);


        Tier tier4 = new Tier();
        tier4.setTierId(4);
        tier4.setMaxDeviceNum(2);
        tier4.setRollingDays(30);
        tier4.setLevel(1);

        Tier tier5 = new Tier();
        tier5.setTierId(5);
        tier5.setMaxDeviceNum(null);
        tier5.setRollingDays(30);
        tier5.setLevel(2);

        List<Tier> tierList = Arrays.asList(tier1, tier2,tier4,tier5);

        when(tierDao.getTierByTierIds(any())).thenReturn(tierList);
        when(ProductTypeEnums.queryProductTypeByTierServiceTypeV1(TIER_CLOID_SERVICE)).thenReturn(null);

        // Act
        TierProductDeviceNumDO result = tierService.initTierListV2(TIER_CLOID_SERVICE,tenantId, false, 0);
        assertEquals(null, result.getTierV1List().getMonthlyProductList());
        assertEquals(0, result.getTierV1List().getYearlyProductList().size());
    }

    @Test
    public void testInitTierListV2_TIER_4G_empty_lowLevelProductType() {
        mockStatic(ProductTypeEnums.class);

        when(ProductTypeEnums.queryProductTypeListByTierServiceType(TIER_4G)).thenReturn(Arrays.asList(3, 4));


        // Arrange
        String tenantId = "test-tenant";

        ProductDO product1 = new ProductDO();
        product1.setId(1);
        product1.setType(PRODUCT_DEVICE_NUM.getCode());
        product1.setTierId(1);
        product1.setSubscriptionPeriod(0);
        product1.setShowInTier(1);

        ProductDO product2 = new ProductDO();
        product2.setId(2);
        product2.setType(PRODUCT_DEVICE_NUM.getCode());
        product2.setTierId(2);
        product2.setSubscriptionPeriod(1);
        product2.setShowInTier(1);

        ProductDO product3 = new ProductDO();
        product3.setId(3);
        product3.setType(PRODUCT_DEVICE_NUM.getCode());
        product3.setTierId(3);
        product3.setSubscriptionPeriod(1);
        product3.setShowInTier(1);




        ProductDO product4 = new ProductDO();
        product4.setId(4);
        product4.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product4.setTierId(4);
        product4.setSubscriptionPeriod(0);
        product4.setShowInTier(1);

        ProductDO product5 = new ProductDO();
        product5.setId(5);
        product5.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product5.setTierId(5);
        product5.setSubscriptionPeriod(1);
        product5.setShowInTier(1);


        ProductDO product6 = new ProductDO();
        product6.setId(6);
        product6.setType(PRODUCT_LEVEL_DEVICE_NUM.getCode());
        product6.setTierId(6);
        product6.setSubscriptionPeriod(1);
        product6.setShowInTier(1);

        List<ProductDO> productList = Arrays.asList(product1, product2, product3, product4,product5,product6);
        when(productService.queryProductListByType(Arrays.asList(PRODUCT_DEVICE_NUM.getCode(), PRODUCT_LEVEL_DEVICE_NUM.getCode()), tenantId, false, 30)).thenReturn(productList);


        Tier tier1 = new Tier();
        tier1.setTierId(1);
        tier1.setMaxDeviceNum(1);
        tier1.setRollingDays(30);
        tier1.setLevel(1);

        Tier tier2 = new Tier();
        tier2.setTierId(2);
        tier2.setMaxDeviceNum(null);
        tier2.setRollingDays(30);
        tier2.setLevel(2);


        Tier tier4 = new Tier();
        tier4.setTierId(4);
        tier4.setMaxDeviceNum(2);
        tier4.setRollingDays(30);
        tier4.setLevel(1);

        Tier tier5 = new Tier();
        tier5.setTierId(5);
        tier5.setMaxDeviceNum(null);
        tier5.setRollingDays(30);
        tier5.setLevel(2);

        List<Tier> tierList = Arrays.asList(tier1, tier2,tier4,tier5);

        when(tierDao.getTierByTierIds(any())).thenReturn(tierList);
        when(ProductTypeEnums.queryProductTypeByTierServiceTypeV1(TIER_4G)).thenReturn(null);

        // Act
        TierProductDeviceNumDO result = tierService.initTierListV2(TIER_4G,tenantId, false, 30);
        assertEquals(null, result.getTierV1List().getMonthlyProductList());
        assertEquals(0, result.getTierV1List().getYearlyProductList().size());
    }
}
