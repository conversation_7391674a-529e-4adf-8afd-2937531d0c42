package com.addx.iotcamera.service.deviceplatform.alexa.safemo;

import com.addx.iotcamera.bean.app.alexa.result.DeviceInfoAndStatusDO;
import com.addx.iotcamera.service.deviceplatform.alexa.AlexaService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AlexaSafemoMsgProcessServiceTest {

    @InjectMocks
    AlexaSafemoMsgProcessService alexaSafemoMsgProcessService;
    @Mock
    AlexaSafemoRelationService alexaSafemoRelationService;
    @Mock
    AlexaSafemoMsgDataService alexaSafemoMsgDataService;
    @Mock
    AlexaSafemoRequestService alexaSafemoRequestService;
    @Mock
    AlexaService alexaService;

    @Test
    public void receiveUserAndKissRelation() {
        alexaSafemoMsgProcessService.receiveUserAndKissRelation(1, "1");
    }

    @Test
    public void receiveDeviceListData() {
        alexaSafemoMsgProcessService.receiveDeviceListData(("1"), new ArrayList<>());
    }

    @Test
    public void receiveDeviceInfoAndStatusChangeData() {
        alexaSafemoMsgProcessService.receiveDeviceInfoAndStatusChangeData("1", new DeviceInfoAndStatusDO());
    }

    @Test
    public void receiveIsAdminLinkToAlexa() {
        Integer adminUserId = 1;
        when(alexaService.isUserLinked(null, adminUserId)).thenReturn(true);
        alexaSafemoMsgProcessService.receiveIsAdminLinkToAlexa("1", 1);
    }
}