package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.cache.DevicePirNotifyFactor;
import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.LibraryTraceDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.init.AmazonInit;
import com.addx.iotcamera.bean.openapi.OpenApiDeviceConfig;
import com.addx.iotcamera.bean.openapi.PirUploadConfig;
import com.addx.iotcamera.bean.video.UploadVideoBeginRequest;
import com.addx.iotcamera.bean.video.UploadVideoBeginResponse;
import com.addx.iotcamera.bean.video.UploadVideoCompleteRequest;
import com.addx.iotcamera.bean.video.VideoSliceRequest;
import com.addx.iotcamera.config.S3;
import com.addx.iotcamera.config.S3Config;
import com.addx.iotcamera.config.VideoSliceConfig;
import com.addx.iotcamera.dynamo.dao.IVideoSliceDAO;
import com.addx.iotcamera.dynamo.dao.TenantAwsConfigDAO;
import com.addx.iotcamera.enums.UserType;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.helper.TraceIdHelper;
import com.addx.iotcamera.helper.aws.AwsHelper;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.addx.iotcamera.service.video.StorageParamService;
import com.addx.iotcamera.service.video.VideoGenerateService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.SnsUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper;
import com.amazonaws.services.identitymanagement.AmazonIdentityManagement;
import com.amazonaws.services.identitymanagement.model.CreateUserRequest;
import com.amazonaws.services.identitymanagement.model.CreateUserResult;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.event.S3EventNotification;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.services.securitytoken.AWSSecurityTokenService;
import com.amazonaws.services.securitytoken.model.AssumeRoleRequest;
import com.amazonaws.services.securitytoken.model.AssumeRoleResult;
import com.amazonaws.services.securitytoken.model.Credentials;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.reactivex.Observable;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.apache.commons.io.IOUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ClassPathResource;

import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;

import static com.addx.iotcamera.constants.VideoConstants.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class VideoServiceTest {

    @InjectMocks
    private VideoService videoService;
    @Mock
    private VipService vipService;
    @Mock
    private VideoGenerateService videoGenerateService;
    @Mock
    private StorageParamService storageParamService;
    @Mock
    private IVideoSliceDAO videoSliceDAO;
    @Mock
    private VideoStoreService videoStoreService;
    @Mock
    private RedisService redisService;
    @Mock
    private S3Service s3Service;
    @Mock
    private AmazonS3 s3Client;
    @Mock
    private LibraryStatusService libraryStatusService;
    @Mock
    private LibraryService libraryService;
    @Mock
    private UserRoleService userRoleService;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private ApplicationContext applicationContext;
    @Spy
    private JwtHelper jwtHelper;
    @Mock
    private DeviceService deviceService;
    @Mock
    private OpenApiConfigService openApiConfigService;
    @Mock
    private TenantAwsConfigDAO tenantAwsConfigDAO;
    @Mock
    private UserService userService;
    @Mock
    private OpenApiWebhookService openApiWebhookService;
    @Mock
    private NotificationService notificationService;
    @Mock
    private ReportLogService reportLogService;
    @Mock
    private AIService aiService;
    @Mock
    private LibraryUpdateReceiveAllService libraryUpdateReceiveAllService;
    @Mock
    private IShardingLibraryDAO shardingLibraryDAO;
    @Mock
    private S3 s3;
    @Mock
    private PushService pushService;
    @Mock
    private Device4GService device4GService;
    @Mock
    private ForkJoinPool pool;

    private S3Config s3Config;
    private String bucket;
    private AWSSecurityTokenService stsClient;

    private boolean isMock = true;
    private VideoSliceConfig videoSliceConfig;

//    private TestHelper testHelper = TestHelper.getInstanceByEnv("staging-us");
    private TestHelper testHelper = TestHelper.getInstanceByLocal();
//        private TestHelper testHelper = TestHelper.getInstanceByEnv("staging");
    private DynamoDBMapper tenantAwsConfigMapper;
//    private TestHelper testHelper = TestHelper.getInstanceByEnv("staging-eu");

    @Before
    public void init() {
        when(vipService.isDeviceNoPlan(any(), any())).thenReturn(false);

        final ForkJoinPool forkJoinPool = new ForkJoinPool(1);
        when(pool.submit((Callable) any())).thenAnswer(AdditionalAnswers.delegatesTo(forkJoinPool));
        this.s3Config = testHelper.getConfig().getObject("s3config", S3Config.class);
        this.videoSliceConfig = testHelper.getConfig().getObject("videoslice", VideoSliceConfig.class);
        this.bucket = testHelper.getConfig().getJSONObject("s3config").getString("bucket");
        AmazonInit amazonInit = testHelper.getAmazonInit();
        this.tenantAwsConfigMapper = amazonInit.tenantAwsConfigMapper();
        this.stsClient = testHelper.getStsClient();
        this.s3Client = amazonInit.s3Client();
        videoService.setS3Client(s3Client);
        videoService.setS3(testHelper.getConfig().getObject("s3", S3.class));
        videoService.setVideoSliceConfig(videoSliceConfig);

        TenantAwsConfigDAO tenantAwsConfigDAO = new TenantAwsConfigDAO();
        tenantAwsConfigDAO.setTenantAwsConfigMapper(amazonInit.tenantAwsConfigMapper());
        when(this.tenantAwsConfigDAO.queryByTenantId(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(tenantAwsConfigDAO));

        when(applicationContext.getBean(VideoService.class)).thenReturn(videoService);
        if (!isMock) {
//            VideoSliceDAO videoSliceDAO = testHelper.getMapper(VideoSliceDAO.class);
//            AmazonDynamoDB dynamoDB = testHelper.getAmazonInit().dynamoDB();
//            DynamoDBMapper dynamoDBMapper = testHelper.getAmazonInit().videoSliceMapper();
//            VideoSliceDAO videoSliceDAO = new VideoSliceDAO();
//            videoSliceDAO.setDynamoDB(dynamoDB);
//            videoSliceDAO.setVideoSliceMapper(dynamoDBMapper);
            final VideoStoreService videoStoreService = new VideoStoreService();

            when(this.videoStoreService.saveVideoSlice(any())).thenAnswer(AdditionalAnswers.delegatesTo(videoStoreService));
            when(this.videoStoreService.querySliceByAdminUserIdAndTraceId(anyInt(), any())).then(AdditionalAnswers.delegatesTo(videoStoreService));
            when(this.videoStoreService.queryVideoUrlByAdminUserIdAndTraceIds(anyInt(), any())).then(AdditionalAnswers.delegatesTo(videoStoreService));
            when(this.videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(anyInt(), any(), anyInt())).then(AdditionalAnswers.delegatesTo(videoStoreService));
            S3Service s3Service = new S3Service();
            s3Service.setS3Client(this.s3Client);
            s3Service.setVideoService(videoService);
            when(this.s3Service.preSignUrl(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(s3Service));
        }
        when(s3Service.getExpireMinutes()).thenReturn(2880);
        when(storageParamService.deviceNeedCloudStorageParams(any())).thenReturn(true);

        videoService.init();
    }

    @After
    public void after() {
    }

    //    @Test
    public void test() {
        String urlPtn = "https://addx-test.s3.cn-north-1.amazonaws.com.cn/deviceSplitVideo/hls/segment-00%s.ts";
        List<VideoSliceDO> sliceDOList = new LinkedList<>();
        for (int i = 0; i < 5; i++) {
            VideoSliceDO videoSliceDO = new VideoSliceDO();
            videoSliceDO.setVideoUrl(String.format(urlPtn, i));
            videoSliceDO.setPeriod(new BigDecimal(2));
            sliceDOList.add(videoSliceDO);
        }
        String id = new Random().nextInt(1000_0000) + "";
        when(videoStoreService.querySliceByAdminUserIdAndTraceId(1, eq(id))).thenReturn(sliceDOList);
        s3Service.setS3Client(s3Client);

        byte[] bytes = videoService.downloadVideoM3u8(1, id);
        log.info("m3u8:\n{}\n", new String(bytes, StandardCharsets.UTF_8));
        ByteArrayInputStream bais = new ByteArrayInputStream(bytes);

        String key = "deviceSplitVideo/hls3/download.m3u8";
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType("application/vnd.apple.mpegurl");
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, key, bais, objectMetadata);
        SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
        Tag cdateTag = new Tag("cdate", yyyyMMdd.format(new Date()));
        putObjectRequest.setTagging(new ObjectTagging(Arrays.asList(cdateTag)));
        PutObjectResult result = s3Client.putObject(putObjectRequest);
        log.info("result=" + result);
        String url = s3Client.getUrl(bucket, key).toString();
        String signedUrl = s3Service.preSignUrl(url);
        log.info("signedUrl=" + signedUrl);
    }

    //    @Test
    public void test_s3Service_preSignUrl() {
        AmazonInit amazonInit = new AmazonInit();
        amazonInit.setS3Config(s3Config);
        S3Service s3Service = new S3Service();
        s3Service.setVideoService(videoService);
        s3Service.setS3Client(amazonInit.s3Client());
        String url = "https://addx-test.s3.cn-north-1.amazonaws.com.cn/device_video_slice/sn/traceId/list3.m3u8";
        String signedUrl = s3Service.preSignUrl(url);
        log.info("signedUrl=" + signedUrl);
    }

    //    @Test
    public void test_videoSliceLastNamePtnStr() {
        Matcher matcher = PATTERN_VIDEO_SLICE_LAST_NAME.matcher("slice_123_32_1.ts");
        if (matcher.matches()) {
            log.info("{},{},{}", matcher.group(1), matcher.group(2), matcher.group(3));
        }
    }

    @Test
    public void test_handleVideoSliceUpdate() {
        Random random = new Random();
        int adminId = random.nextInt(9000) + 1000;
        String traceId = "trace" + adminId;
        String sn = "sn" + adminId;

        UserRoleDO userRole = new UserRoleDO();
        userRole.setAdminId(adminId);
        when(userRoleService.getDeviceAdminUserRole(sn)).thenReturn(userRole);
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setDeviceName("zyj-unit-test-" + adminId);
        when(deviceService.getAllDeviceInfo(sn)).thenReturn(deviceDO);
        // 1.第一次接收到s3事件
        LibraryTraceDO libraryTrace = new LibraryTraceDO();
        // when(libraryService.getLibraryTrace(traceId)).thenReturn(libraryTrace);
//        when(libraryDAO.selectLibraryByTraceId(traceId)).thenReturn(null);

        videoService.handleVideoSliceUpdate(traceId, sn, "http://any.png"
                , Collections.emptyList());
//        verify(libraryDAO, times(1)).insertLibrary(any());

        // 2.接收到所有s3事件
        List<VideoSliceDO> sliceList = new LinkedList<>();
        int sliceNum = 5;
        for (int i = 0; i < sliceNum; i++) {
            VideoSliceDO slice = new VideoSliceDO();
            slice.setSerialNumber(sn);
            slice.setVideoUrl("https//host/video/download/m3u8/" + traceId + "_" + i);
            slice.setPeriod(new BigDecimal(2));
            slice.setOrder(i);
            slice.setIsLast(i == sliceNum - 1);
            slice.setS3EventTime(System.currentTimeMillis());
            slice.setFileSize(100);
            sliceList.add(slice);
        }
        libraryTrace.setLibraryId(123);
        // when(libraryService.getLibraryTrace(traceId)).thenReturn(libraryTrace);
        when(videoStoreService.querySliceByAdminUserIdAndTraceId(1, libraryTrace.getLibraryId() + "")).thenReturn(Collections.emptyList());
        when(redisService.hashIncrementInt(any(), any(), anyInt(), anyInt())).thenReturn(sliceList.size());
        when(redisService.rangeList(anyString())).thenReturn(Arrays.asList("{\"event\":18}", "{\"event\":19}", "{\"event\":20}"));

        Result result = videoService.handleVideoSliceUpdate(traceId, sn, null
                , sliceList);
//        verify(videoSliceDAO, times(sliceNum)).insertSlice(any());
        Assert.assertEquals(Result.successFlag, result.getResult());

        result = videoService.handleVideoSliceUpdate(traceId, sn, null
                , sliceList);
//        verify(videoSliceDAO, times(sliceNum)).insertSlice(any());
        Assert.assertEquals(Result.successFlag, result.getResult());
    }

    public static UploadVideoCompleteRequest createUploadVideoCompleteRequest(String sn, String traceId) {
        if (sn == null) sn = PhosUtils.getUUID();
        if (traceId == null) traceId = "traceId_" + PhosUtils.getUUID();
        String prefix = UPLOAD_KEY_PREFIX.replace("${sn}", sn).replace("${traceId}", traceId);
        List<VideoSliceRequest> sliceRequestList = new LinkedList<>();
        for (int i = 0; i < 5; i++) {
            String videoKey = prefix + UPLOAD_KEY_POSTFIX_SLICE
                    .replace("${period}", "2000")
                    .replace("${order}", i + "")
                    .replace("${isLast}", i == 4 ? "1" : "0");
            sliceRequestList.add(VideoSliceRequest.builder()
                    .videoKey(videoKey).fileSize(2021).uploadSuccess(i == 1 ? 0 : 1)
                    .startUploadingTimestamp(10000_00000_011L)
                    .endUploadingTimestamp(10000_00000_012L)
                    .startRecordingTimestamp(10000_00000_013L)
                    .endRecordingTimestamp(10000_00000_014L)
                    .build());
        }
        UploadVideoCompleteRequest request = UploadVideoCompleteRequest.builder()
                .serialNumber(sn).traceId(traceId).sliceList(sliceRequestList)
                .s3AddressReceivedTimestamp(10000_00000_001L)
                .totalStartRecordingTimestamp(10000_00000_002L)
                .totalEndRecordingTimestamp(10000_00000_003L)
                .resolution("1920x1080").videoFormat("m3u8")
                .imageKey(prefix + UPLOAD_KEY_POSTFIX_IMAGE.replace("${imgType}", "jpg"))
                .build();
        JSONObject errMap = UploadVideoCompleteRequest.validate(request);
        log.info("errMap:\n{}", JSON.toJSONString(errMap, true));
        Assert.assertTrue(errMap.isEmpty());
        UploadVideoCompleteRequest.preHandle(request);
        return request;
    }

    @Test
    public void test_videoUploadComplete_hasLibraryId() {
        UploadVideoCompleteRequest request = createUploadVideoCompleteRequest(null, null);
        prepare_handleVideoSliceUpdate(461204, false);
        when(pushService.getDevicePirNotifyFactor(any())).thenReturn(new DevicePirNotifyFactor());
        Result result = videoService.videoUploadComplete(request);
        log.info("result:\n{}", JSON.toJSONString(result, true));
        Assert.assertEquals(Result.successFlag, result.getResult());
        if (!isMock) {
            testHelper.commitAndClose();
        }
    }

    //    @Test
    public void test_handleS3EventNotify_noLibraryId() {
        UploadVideoCompleteRequest request = createUploadVideoCompleteRequest(null, null);
        prepare_handleVideoSliceUpdate(null, false);
        Result result = videoService.videoUploadComplete(request);
        log.info("result:\n{}", JSON.toJSONString(result, true));
        Assert.assertEquals(Result.successFlag, result.getResult());
        if (!isMock) {
//            updateSliceVideoUrl(result.getData());
            testHelper.commitAndClose();
        }
    }

    //    @Test
    public void test_downloadVideoM3u8() {
        // curl -X GET 'https://api-test.addx.live/video/download/m3u8/460057' -H 'Authorization:Bearer eyJhbGciOiJIUzUxMiJ9.eyJzZWVkIjoiNmIyMWFmZDk5MmQzNDRhY2EyOGQ1NDdhNGU2MzQzNWEiLCJleHAiOjI2MTY2NTI3MTYsInVzZXJJZCI6MTIzfQ.EQ5B6aA3IoxgoS2YyVjvppIGt3c5P7_u29h68PecneKvdboIq6AdD-hmLRNzWo8KaobL2AFBVio6hOfGGH5bQA'
        byte[] bytes = videoService.downloadVideoM3u8(1, "7118988");
        String text = new String(bytes, StandardCharsets.UTF_8);
        log.info("downloadVideoM3u8:\n{}", text);
    }

    //    @Test
    public void test_preSignM3u8Url_validateM3u8Token() {
        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            log.info("args:" + Arrays.toString(args));
            when(redisService.get((String) args[0])).thenReturn((String) args[1]);
            return invocation;
        }).when(redisService).set(anyString(), anyString(), anyInt());

//        String url = "https://api-test.addx.live/video/download/m3u8/460057.m3u8";
        String url = "https://api-test.addx.live/video/download/m3u8/460057";
//        String url = "https://api-test.addx.live/video/download/m3u8/460057";
        Integer userId = 123;

        String signedUrl = videoService.preSignM3u8Url(url, userId);
        Assert.assertNotNull(signedUrl);
        log.info("signedUrl:" + signedUrl);
        String token = URI.create(signedUrl).getQuery().split("=")[1];
        Integer userId2 = videoService.validateM3u8Token("460057", token);
        Assert.assertEquals(userId, userId2);
    }

    /****
     select concat('"',v.video_url,'",') from video_library v
     join library_question_back t on t.trace_id=v.trace_id and t.user_id=v.admin_id
     where v.admin_id=109422;
     */
    @Test
    public void test_preSignM3u8Url() {
        /*
        List<String> urls = Arrays.asList(
                "https://api-us.vicohome.io/video/download/m3u8/0634061638723473HcPGTFjGzm8gB.m3u8",
                "https://api-us.vicohome.io/video/download/m3u8/0634061638723260wvcAxy08HZ38z.m3u8",
                "https://api-us.vicohome.io/video/download/m3u8/0634061638723073DNv7SV1NmpFwF.m3u8",
                "https://api-us.vicohome.io/video/download/m3u8/0634061638722865Z4lfgOT95fJYt.m3u8"
        );
        List<String> urls = Arrays.asList(
                "https://api-us.vicohome.io/video/download/m3u8/0169401639485796umKq0VPuciPH4.m3u8",
                "https://api-us.vicohome.io/video/download/m3u8/0826971638742616c87keZXXlQ5gr.m3u8",
                "https://api-us.vicohome.io/video/download/m3u8/0169401639476775Q2JodlZOqot8T.m3u8",
                "https://api-us.vicohome.io/video/download/m3u8/0169401639548524iPf73XDP57TS8.m3u8",
                "https://api-us.vicohome.io/video/download/m3u8/0826971638742616c87keZXXlQ5gr.m3u8");
         */
        /*
        List<String> urls = Arrays.asList(
                "https://api-eu.vicohome.io/video/download/m3u8/01094221654781334LkrlioQghcoI.m3u8",
                "https://api-eu.vicohome.io/video/download/m3u8/01094221655474434UCGcqjuBCI4P.m3u8"
        );
         */
        List<String> urls = Arrays.asList(
                "https://api-us.vicohome.io/video/download/m3u8/01763061675225900xvB38gCt1TEO.m3u8"
        );
        Integer userId = 176306;
        for (String url : urls) {
            String signedUrl = videoService.preSignM3u8Url(url, userId);
            System.out.println(signedUrl);
            String[] traceIdAndToken = signedUrl.substring(signedUrl.lastIndexOf('/') + 1).split("\\.m3u8\\?token=");
            Integer realUserId = videoService.validateM3u8Token(traceIdAndToken[0], traceIdAndToken[1]);
            Assert.assertEquals(userId, realUserId);
        }
    }

    @Test
    public void test_downloadVideoM3u8Ts() throws IOException {
        String fileName = "0";
        FileOutputStream fos = new FileOutputStream(fileName);
        String url = "https://api-test.addx.s3/video/download/m3u8/04031701057075Ns6KBPZSAs8nzuF.s3";
        VideoSliceDO videoSliceDO = new VideoSliceDO();
        videoSliceDO.setVideoUrl(url);
        when(videoStoreService.querySliceByAdminUserIdAndTraceIdAndOrder(anyInt(), anyString(), anyInt())).thenReturn(videoSliceDO);
        videoService.downloadVideoM3u8Ts(1, "460057", fileName, fos);
        fos.close();
    }

    //    @Test
    public void test_deleteSliceVideoUrl() {
        when(s3Service.deleteObject(any())).thenAnswer(invoke -> {
            List<Integer> ids = (List<Integer>) invoke.getArguments()[0];
            log.info("ids:" + ids);
            return ids.size();
        });
        Observable.fromArray("460056", "460057", "460058").buffer(2)
                .map((e)-> videoStoreService.queryVideoUrlByAdminUserIdAndTraceIds(1, e))
                .forEach(s3Service::deleteObject);
//        verify(s3Service.deleteObjectAsync(any()), times(2));
    }

    private void updateSliceVideoUrl(Object libraryId) {
        try (Connection conn = testHelper.getDataSource().getConnection()) {
            String sql = "update camera.video_slice \n" +
                    "set video_url=concat('https://addx-test.s3.cn-north-1.amazonaws.com.cn/deviceSplitVideo/hls/segment-00',`order`,'.ts')\n" +
                    "where library_id='" + libraryId + "';";
            conn.prepareStatement(sql).executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void test_assumeRole() {
        String sn = PhosUtils.getUUID();

        AssumeRoleRequest request = new AssumeRoleRequest();
        request.setExternalId(sn);
//        request.setRoleArn("arn:aws-cn:iam::************:role/test_bucket_access_role");
        request.setRoleArn(testHelper.getConfig().getJSONObject("videoslice").getString("roleArn"));
        request.setRoleSessionName("zyj-unit-test-role-session");
//        request.setDurationSeconds(60 * 24);
        request.setDurationSeconds(900);
//        String policy = "{\"Version\":\"2012-10-17\",\"Statement\":[{" +
//                "\"Action\":[\"s3:PutObject\"]," +
//                ",\"Effect\":\"Allow\"" +
////                ",\"Resource\":\"arn:aws-cn:s3:::addx-test/deviceSplitVideo/*\"" +
////                ",\"Resource\":\"arn:aws-cn:iam::************:user/test_bucket_admin/*\"" +
//                ",\"Resource\":\"arn:aws-cn:iam::************:role/test_bucket_access_role/*\"" +
////                ",\"Resource\":\"*\"" +
//                "}]}";
        String allowPrefix = "device_video_slice/zyj-unit-test";
//        request.setPolicy(AwsHelper.createAwsAssumeRolePolicy(Arrays.asList(bucket), allowPrefix));
        log.info("policy={}", request.getPolicy());
        AssumeRoleResult result = stsClient.assumeRole(request);
        log.info("assumeRoleResult={}", JSON.toJSONString(result, true));
        Credentials sessionCredentials = result.getCredentials();
        BasicSessionCredentials awsCredentials = new BasicSessionCredentials(
                sessionCredentials.getAccessKeyId(),
                sessionCredentials.getSecretAccessKey(),
                sessionCredentials.getSessionToken());
        AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                .withRegion(s3Config.getClientRegion())
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                .build();
        ByteArrayInputStream bais = new ByteArrayInputStream("123".getBytes());
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket
//        PutObjectRequest putObjectRequest = new PutObjectRequest("addx-staging"
                , allowPrefix + "/" + sn, bais, new ObjectMetadata());
//        putObjectRequest.setTagging(new ObjectTagging(Arrays.asList(new Tag("cdate", "20210331"))));
        PutObjectResult putObjectResult = s3Client.putObject(putObjectRequest);
        log.info("putObjectResult={}", JSON.toJSONString(putObjectResult, true));
    }

    //    @Test
    public void test_iam_createUser() {
        AmazonInit amazonInit = new AmazonInit();
        amazonInit.setS3Config(testHelper.getConfig().getObject("s3config", S3Config.class));
        AmazonIdentityManagement iamClient = amazonInit.iamClient();

        String sn = PhosUtils.getUUID();

        CreateUserRequest request = new CreateUserRequest().withUserName(sn);
        CreateUserResult response = iamClient.createUser(request);

        log.info("CreateUserResult={}", JSON.toJSONString(response, true));
    }

    //    @Test
    public void test_videoUploadBegin() throws IOException {
        String tenantId = "netvue";
//        String tenantId = "default";
        AwsHelper awsHelper = AwsHelper.getInstance(tenantId, tenantAwsConfigDAO::queryByTenantId);
        when(openApiConfigService.getAwsHelper(anyString())).thenReturn(awsHelper);

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setRecResolution("1920x1080");
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(deviceDO);
        User user = new User();
        user.setType(UserType.THIRD.getCode());
        user.setTenantId(tenantId);
        when(userRoleService.getAdminUserBySn(anyString())).thenReturn(new Result<>(user));
        UserRoleService.UserRoles userRoles = new UserRoleService.UserRoles(1, Collections.emptyList());
        when(userRoleService.queryUserRolesBySn(anyString())).thenReturn(userRoles);
        when(userService.queryUserById(anyInt())).thenReturn(user);
//        when(openApiConfigService.getPrefixBySn(anyString())).thenReturn("wx_1234/9876");
//        when(openApiConfigService.getPrefixBySnAndTenantId(anyString(), anyString()))
//                .thenReturn("netvue".equals(tenantId) ? "wx_1234/9876" : "device_video_slice");

        String json = IOUtils.toString(ConfigHelper.loadConfig("classpath:example/tian_he_rong_v2.json"));
        OpenApiDeviceConfig deviceConfig = OpenApiDeviceConfig.fromJson(json);
        when(openApiConfigService.queryBySnAndTenantId(anyString(), anyString())).thenReturn(deviceConfig);

        UploadVideoBeginRequest uploadVideoBeginRequest = new UploadVideoBeginRequest(
                "sn_" + PhosUtils.getUUID(), "traceId_" + PhosUtils.getUUID(),
                (int) System.currentTimeMillis(), OpenApiDeviceConfig.MotionType.video.name(), VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW.getCode());
        Result<JSONObject> result = videoService.videoUploadBegin(uploadVideoBeginRequest);
        log.info("result:\n{}", JSON.toJSONString(result, true));
        UploadVideoBeginResponse resp = result.getData().toJavaObject(UploadVideoBeginResponse.class);
        PirUploadConfig videoPirUploadConfig = result.getData().getObject("video", PirUploadConfig.class);
        PirUploadConfig imagePirUploadConfig = result.getData().getObject("coverImage", PirUploadConfig.class);

        UploadVideoCompleteRequest completeRequest = UploadVideoCompleteRequest.builder()
                .serialNumber(resp.getSerialNumber())
                .traceId(resp.getTraceId())
                .s3AddressReceivedTimestamp(System.currentTimeMillis())
                .totalStartRecordingTimestamp(System.currentTimeMillis() + 1000 * 2)
                .totalEndRecordingTimestamp(System.currentTimeMillis() + 3000 * 2)
//                .imageKey(resp.getImageKeyTemplate().replace("${imgType}", "jpg"))
                .imageKey(imagePirUploadConfig.getKeyTemplate().replace("${imgType}", "jpg"))
                .resolution(resp.getResolution())
                .videoFormat("ts")
                .sliceList(Arrays.asList(
                        VideoSliceRequest.builder().videoKey(videoPirUploadConfig.getKeyTemplate()
                                .replace("${period}", "2000")
                                .replace("${order}", "0")
                                .replace("${isLast}", "0")
                        ).fileSize(2222).uploadSuccess(1)
                                .startRecordingTimestamp(System.currentTimeMillis())
                                .endRecordingTimestamp(System.currentTimeMillis() + 1000)
                                .startUploadingTimestamp(System.currentTimeMillis() + 1000)
                                .endUploadingTimestamp(System.currentTimeMillis() + 1000 * 2)
                                .build(),
                        VideoSliceRequest.builder().videoKey(videoPirUploadConfig.getKeyTemplate()
                                .replace("${period}", "2100")
                                .replace("${order}", "1")
                                .replace("${isLast}", "1")
                        ).fileSize(2201).uploadSuccess(0)
                                .startRecordingTimestamp(System.currentTimeMillis())
                                .endRecordingTimestamp(System.currentTimeMillis() + 1000)
                                .startUploadingTimestamp(System.currentTimeMillis() + 1000)
                                .endUploadingTimestamp(System.currentTimeMillis() + 1000 * 2)
                                .build()
                )).build();
        completeRequest.setSerialNumber(resp.getSerialNumber());
        log.info("completeRequest:\n{}", JSON.toJSONString(completeRequest, true));

        BasicSessionCredentials awsCredentials = new BasicSessionCredentials(
                resp.getCredentials().getAccessKeyId(),
                resp.getCredentials().getSecretAccessKey(),
                resp.getCredentials().getSessionToken());
        AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
//                .withRegion(awsHelper.getS3Config().getClientRegion())
                .withRegion(videoPirUploadConfig.getClientRegion())
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
//                .withAccelerateModeEnabled(true)
                .build();
        for (VideoSliceRequest slice : completeRequest.getSliceList()) {

            ByteArrayInputStream bais = new ByteArrayInputStream("123".getBytes());
//        PutObjectRequest putObjectRequest = new PutObjectRequest("addx-test"
            PutObjectRequest putObjectRequest = new PutObjectRequest(videoPirUploadConfig.getBucket()
                    , slice.getVideoKey(), bais, new ObjectMetadata());
            SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
            Tag cdateTag = new Tag("cdate", yyyyMMdd.format(new Date()));
            putObjectRequest.setTagging(new ObjectTagging(Arrays.asList(cdateTag)));
            PutObjectResult putObjectResult = s3Client.putObject(putObjectRequest);
            log.info("putObjectResult={}", JSON.toJSONString(putObjectResult, true));
        }

    }

    //    @Test
    public void test_videoUploadBegin_image() {
        String tenantId = "netvue";
//        String tenantId = "default";

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setRecResolution("1920x1080");
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(deviceDO);
        User user = new User();
        user.setType(UserType.THIRD.getCode());
        user.setTenantId(tenantId);
        when(userRoleService.getAdminUserBySn(anyString())).thenReturn(new Result<>(user));
        UserRoleService.UserRoles userRoles = new UserRoleService.UserRoles(1, Collections.emptyList());
        when(userRoleService.queryUserRolesBySn(anyString())).thenReturn(userRoles);
        when(userService.queryUserById(anyInt())).thenReturn(user);

        JSONObject json = ConfigHelper.loadYmlConfig("classpath:example/tian_he_rong_v2.json");
        OpenApiDeviceConfig deviceConfig = json.toJavaObject(OpenApiDeviceConfig.class);
        when(openApiConfigService.queryBySnAndTenantId(anyString(), anyString())).thenReturn(deviceConfig);

        UploadVideoBeginRequest uploadVideoBeginRequest = new UploadVideoBeginRequest(
                "sn_" + PhosUtils.getUUID(), "traceId_" + PhosUtils.getUUID(),
                (int) System.currentTimeMillis(), OpenApiDeviceConfig.MotionType.video.name(), VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW.getCode());
        Result<JSONObject> result = videoService.videoUploadBegin(uploadVideoBeginRequest);
        log.info("result:\n{}", JSON.toJSONString(result, true));
        uploadCoverImageAndMotionImage(result.getData());
    }

    private void uploadCoverImageAndMotionImage(JSONObject data) {
        PirUploadConfig motionImagePirUploadConfig = data.getObject("motionImage", PirUploadConfig.class);
        PirUploadConfig imagePirUploadConfig = data.getObject("coverImage", PirUploadConfig.class);

        List<PirUploadConfig> pirUploadConfigs = Arrays.asList(motionImagePirUploadConfig, imagePirUploadConfig);
        LinkedList<String> keys = new LinkedList<>();

        String motionImageKey = motionImagePirUploadConfig.getKeyTemplate()
                .replace("${eventTs}", PhosUtils.getUTCStamp() + "")
                .replace("${snapTs}", (PhosUtils.getUTCStamp() + 2) + "");
        log.info("motionImageKey:\n{}", motionImageKey);
        keys.add(motionImageKey);

        String imageKey = imagePirUploadConfig.getKeyTemplate()
                .replace("${imgType}", "jpg");
        log.info("imageKey:\n{}", imageKey);
        keys.add(imageKey);

        for (PirUploadConfig pirUploadConfig : pirUploadConfigs) {
            String key = keys.removeFirst();

            BasicSessionCredentials awsCredentials = new BasicSessionCredentials(
                    data.getJSONObject("credentials").getString("accessKeyId"),
                    data.getJSONObject("credentials").getString("secretAccessKey"),
                    data.getJSONObject("credentials").getString("sessionToken"));
            AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                    .withRegion(pirUploadConfig.getClientRegion())
                    .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                    .build();

            ByteArrayInputStream bais = new ByteArrayInputStream("123".getBytes());
            PutObjectRequest putObjectRequest = new PutObjectRequest(pirUploadConfig.getBucket()
                    , key, bais, new ObjectMetadata());
            SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
            Tag cdateTag = new Tag("cdate", yyyyMMdd.format(new Date()));
            putObjectRequest.setTagging(new ObjectTagging(Arrays.asList(cdateTag)));
            PutObjectResult putObjectResult = s3Client.putObject(putObjectRequest);
            log.info("putObjectResult={}", JSON.toJSONString(putObjectResult, true));
        }
    }

    //    @Test
    public void test_videoUploadBegin_image2() {
        String json = "{\"headSlicePeriod\":2000.0,\"traceId\":\"08hW02Rs0DJI0yR10ktqhD6ge2y76\",\"serialNumber\":\"288eb1ea8971fef2115b9adf41021d3b\",\"motionImage\":{\"bucket\":\"nvcn-cn-northwest-1-motioncapture\",\"clientRegion\":\"cn-northwest-1\",\"rootPath\":\"https://nvcn-cn-northwest-1-motioncapture.s3.cn-northwest-1.amazonaws.com.cn/\",\"keyTemplate\":\"7aba1bc3286fd8df/288eb1ea8971fef2/08hW02Rs0DJI0yR10ktqhD6ge2y76/nvc_${eventTs}_${snapTs}.jpeg\",\"resolution\":\"1280x720\"},\"credentials\":{\"accessKeyId\":\"********************\",\"secretAccessKey\":\"uL+vusdIQhegH6BZGFMF1DfvqKUaDmkYv8IQSUdF\",\"sessionToken\":\"IQoJb3JpZ2luX2VjEK///////////wEaDmNuLW5vcnRod2VzdC0xIkYwRAIgO9mf2KBJffYV1rl1fb8hLqi+2THFy6OT+3TsaaU5jIsCID/pgSuoc7Zi1EKTaO6w909tbz5ZfLhzVwkxls1ubBXKKrYCCN3//////////wEQARoMOTAyMjgwNjA2NTMwIgyvfBwIczlgdFbjXAoqigJ2nift0QhrlXxEQ7KH/sr7A4kE5B9N6zVjLjPUzyYb3OMh3uwAqAZYPgHTQegxhFtHUhJmCLmDgWpHVYVRq22jJvyzy7CwEJgjOqOHuqBsyPktsk/yckwrlPykTFdMLxXpDecAOoCYpoZrTJN6gIPQZf0JzdBm0EPIQ6f1+rfdREtNIecDQrpzzmiIz6SexOPOk8qcKqOgkRfD9ABFdMWtWB/0jstbB8gS84xEUP2rLZWfYXveMVf2yrszpVzwOYhF0GiAvM2ytUyImdZT2XCT4zVYu1oCnky2IEBojoFppJt9LN+8A5/0mffeq0QDfvTzoOIscTKU234JAKOUnwTVunXrCMOiEez+aDCs/dqHBjqeAedOG9Xqy6MWciqBgDAHNzAXb92M4xKXjQTja//7fLT+0IdRN9QgzO+Gws5NS+rLZ7zdFP+ZpzB5KQtlfEYM8uZpTl55tBT+7IbwGxOFT+fNMFhrW8Dy33felYdmZ17nPe+0pltRENv9j3c9GNDzokd5IEaHLFe+Vwij5ieEy4QAq1NRL/EuZ4i1LgVgxCcyvxRqWv8cua8YE0JtLG/t\",\"expiration\":\"Jul 20, 2021 12:46:44 PM\"},\"coverImage\":{\"bucket\":\"nvcn-cn-northwest-1-motioncapture\",\"clientRegion\":\"cn-northwest-1\",\"rootPath\":\"https://nvcn-cn-northwest-1-motioncapture.s3.cn-northwest-1.amazonaws.com.cn/\",\"keyTemplate\":\"7aba1bc3286fd8df/288eb1ea8971fef2/08hW02Rs0DJI0yR10ktqhD6ge2y76/image.${imgType}\",\"resolution\":\"1280x720\"},\"tailSlicePeriod\":4000.0,\"videoUploadType\":1.0,\"serviceName\":\"s3\"}";
        uploadCoverImageAndMotionImage(JSON.parseObject(json));
    }

    private void prepare_handleVideoSliceUpdate(Integer libraryId, boolean randomLibraryId) {
        Random random = new Random();
        LibraryTraceDO libraryTrace = new LibraryTraceDO();
        if (randomLibraryId) {
            libraryTrace.setLibraryId(random.nextInt(900_0000) + 100_0000);
        } else {
            libraryTrace.setLibraryId(libraryId);
        }
        // when(libraryService.getLibraryTrace(anyString())).thenReturn(libraryTrace);
        UserRoleDO userRole = new UserRoleDO();
        userRole.setAdminId(123);
        when(userRoleService.getDeviceAdminUserRole(anyString())).thenReturn(userRole);
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setDeviceName("device_name_kdjsflsd");
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(deviceDO);
        when(videoStoreService.querySliceByAdminUserIdAndTraceId(1, libraryTrace.getLibraryId() + "")).thenReturn(new ArrayList<>());
    }

    @Test
    public void test_receiveVideoSlice() throws Exception {
        InputStream is = new ClassPathResource("example/s3_event_notify_message.json").getInputStream();
        String json = IOUtils.toString(is);
        S3EventNotification s3EventNotification = S3EventNotification.parseJson(json);

        prepare_handleVideoSliceUpdate(null, true);
        Result result = videoService.handleS3EventNotify(s3EventNotification);
        log.info("");

        when(videoGenerateService.isEnable(anyString(), anyString())).thenReturn(true);
        result = videoService.handleS3EventNotify(s3EventNotification);

        is = new ClassPathResource("example/s3_event_notify_message_img.json").getInputStream();
        json = IOUtils.toString(is);
        s3EventNotification = S3EventNotification.parseJson(json);
        result = videoService.handleS3EventNotify(s3EventNotification);
    }

    //        @Test
    public void test_isMessageSignatureValid() throws Exception {
        String awsCertEndpoint = testHelper.getConfig().getJSONObject("videoslice").getString("awsCertEndpoint");
//        InputStream is = new ClassPathResource("example/s3_SubscriptionConfirmation.json").getInputStream();
        InputStream is = new ClassPathResource("example/s3_SubscriptionConfirmation_staging.json").getInputStream();
        String json = IOUtils.toString(is);
        SnsUtil.Message snsMsg = JSON.parseObject(json, SnsUtil.Message.class);
//        InputStream pemIs = new ClassPathResource("example/SimpleNotificationService-d4662e1b0c5825978ea59998a51cdfbe.pem").getInputStream();
//        X509Certificate x509Certificate = SnsUtil.loadX509Certificate(pemIs);
        boolean result = SnsUtil.isMessageSignatureValid(snsMsg, awsCertEndpoint);
        Assert.assertTrue(result);
    }

    @Test
    public void test_count_distinct_tags() throws Exception {
        InputStream is = ConfigHelper.loadConfig("classpath:/example/video_library_distinct_tags_20210402_pord_us.txt");
        LinkedHashSet<String> tags = new LinkedHashSet();
        for (String line : IOUtils.readLines(is)) {
            for (String tag : line.split(",")) {
                tags.add(tag);
            }
        }
        for (String tag : tags) {
            System.out.println(tag);
        }
    }

    @Test
    public void test_genTraceIdForRecordVideo() {
        log.info("A:{},Z:{},a:{},z:{},0:{},9:{}", (int) 'A', (int) 'Z', (int) 'a', (int) 'z', (int) '0', (int) '9');
        log.info("alphanumericCharSet:{}", TraceIdHelper.alphanumericCharSet);
        log.info("9 -> {}", TraceIdHelper.toAlphanumeric(9));
        log.info("9+26 -> {}", TraceIdHelper.toAlphanumeric(9 + 26));
        log.info("9+26+26 -> {}", TraceIdHelper.toAlphanumeric(9 + 26 + 26));
        log.info("762 -> {}", TraceIdHelper.toAlphanumeric(762));
        log.info("Integer.MAX_VALUE -> {}", TraceIdHelper.toAlphanumeric(Integer.MAX_VALUE)); // 1BCkl2
        log.info("Long.MAX_VALUE -> {}", TraceIdHelper.toAlphanumeric(Long.MAX_VALUE)); // 7M85y0N8lZa
        log.info("9_9999_9999_9999L -> {}", TraceIdHelper.toAlphanumeric(9_9999_9999_9999L)); // DOTKr3Q2
        log.info("9_999_9999_9999_9999L -> {}", TraceIdHelper.toAlphanumeric(9_999_9999_9999_9999L)); // 9RgsGBBNJ
        log.info("maxUUID -> {}", TraceIdHelper.toAlphanumeric(new BigInteger("ffffffffffffffffffffffffffffffff", 16))); // 7CHf7TM8N9KLFt5mgd24N7
    }

    @Test
    public void test_random() {
        long seed = System.currentTimeMillis();
        Random random1 = new Random(seed);
        Random random2 = new Random(seed);
        for (int i = 0; i < 10; i++) {
            Assert.assertEquals(random1.nextInt(), random2.nextInt());
        }
    }

    @Test
    public void test_random2() throws InterruptedException {
        List<Integer> list1 = new LinkedList<>();
        Thread thread1 = new Thread(() -> {
            for (int i = 0; i < 10; i++) {
                list1.add(ThreadLocalRandom.current().nextInt());
            }
        });
        thread1.start();
        List<Integer> list2 = new LinkedList<>();
        for (int i = 0; i < 10; i++) {
            list2.add(ThreadLocalRandom.current().nextInt());
        }
        thread1.join();
        for (int i = 0; i < list1.size(); i++) {
            Assert.assertNotEquals(list1.get(i), list2.get(i));
        }
    }

    @Test
    public void test_generateTraceId() {
        String traceId = TraceIdHelper.generateRandomTraceId(100, "0");
        Assert.assertEquals(29, traceId.length());
    }

    // 测试--
    //    @Test
    public void test_generateTraceId_multi() {
        HashSet<String> set = new LinkedHashSet<>();
        for (int i = 0; i < 10000; i++) {
            String traceId = TraceIdHelper.generateRandomTraceId(100, "0");
            Assert.assertTrue(set.add(traceId));
        }
    }

//    @Test
    public void test_validateM3u8Token() {
        // downloadM3u8 traceId=0403751632407003PwQFUNb3MfbqE,token=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VySWQiOjQwMzc1LCJ0cmFjZUlkIjoiMDQwMzc1MTYzMjQwNzAwM1B3UUZVTmIzTWZicUUiLCJleHAiOjE2MzI1ODAwMzB9.XYzVGC9Y2wbEIUop4dOp8tWX3eoEY2tjlRDvpJ38SOLJqj80PDaAnKmDR5ImmQKJ0ErxgL8Zusdud6NgGify1w
        String traceId = "0403751632407003PwQFUNb3MfbqE";
//        String token = "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VySWQiOjQwMzc1LCJ0cmFjZUlkIjoiMDQwMzc1MTYzMjQwNzAwM1B3UUZVTmIzTWZicUUiLCJleHAiOjE2MzI1ODAwMzB9.XYzVGC9Y2wbEIUop4dOp8tWX3eoEY2tjlRDvpJ38SOLJqj80PDaAnKmDR5ImmQKJ0ErxgL8Zusdud6NgGify1w";
        String token = "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VySWQiOjQwMzc1LCJ0cmFjZUlkIjoiMDQwMzc1MTYzMjQwNzAwM1B3UUZVTmIzTWZicUUiLCJleHAiOjE2MzI1ODAwMzB9.XYzVGC9Y2wbEIUop4dOp8tWX3eoEY2tjlRDvpJ38SOLJqj80PDaAnKmDR5ImmQKJ0ErxgL8Zusdud6NgGify1w";

//        JwtHelper jwtHelper = new JwtHelper();
        String SECRET = "ZqdQpCTbf7V3Lle52YPhPAIhdwZQ4RFTsdl0KBOGYUA0qfBViAeuCN82uWY2josPTHeCjBGEr9g4spSEXwxjPJCecrSmNwCQDIZALSVcpXzceHqiPQSDG45LPqFTc3QoTCyyl6ShiuTGS32hFtpZ8FPm4jxpdNUT3mnbXiYVjK3mUhVeNemW3dLQGCiPwFoOulDZjGM0tagSxi0QHeNe7XVaNpBx1P2XRVdGF5o";
        Claims claims = Jwts.parser()
                .setSigningKey(SECRET)
                .parseClaimsJws(token)
                .getBody();
        JSONObject obj = new JSONObject(claims);
        log.info("real   traceId:{}", obj.getString("traceId"));
        log.info("expect traceId:{}", traceId);
    }

//    @Test
//    public void test_pushDoorbellEvent() {
//        videoService.pushDoorbellEvent(UUID.randomUUID().toString(), "sn_01", EReportEvent.DOORBELL_PRESS.name(), "http://any.img");
//        videoService.pushDoorbellEvent(UUID.randomUUID().toString(), "sn_01", EReportEvent.DOORBELL_REMOVE.name(), null);
//    }

//    @Test
//    public void test_pushDevicecallEvent() {
//        videoService.pushDoorbellEvent(UUID.randomUUID().toString(), "sn_01", EReportEvent.DEVICE_CALL.name(), "http://any.img");
//        videoService.pushDoorbellEvent(UUID.randomUUID().toString(), "sn_01", EReportEvent.DEVICE_CALL.name(), null);
//    }

    /*
    @Test
    public void test_videoUpdateTask() throws Exception {
        AtomicInteger i = new AtomicInteger();
        when(redisService.zrangeByScore(any(), anyDouble(), anyDouble(), anyLong(), anyLong())).thenAnswer((invocationOnMock -> {
            return i.getAndIncrement() < 1 ? Collections.singleton("trace_01") : Collections.emptySet();
        }));
        when(redisService.zremove(anyString(), anyString())).thenReturn(true);
        when(redisService.hashMultiGet(any(), anyList())).thenReturn(new JSONObject(){{
            put(S3_VIDEO_SLICE_INFO_NUM, 1);
            put(S3_VIDEO_SLICE_INFO_DOORBELL_EVENT_INFO, "[{\"event\":18},{\"event\":19}]");
            put(S3_VIDEO_SLICE_INFO_DEVICE_CALL_INFO, "[{\"event\":20}, {}]");
        }});

        ForkJoinPool forkJoinPool = new ForkJoinPool();
        Field forkJoinPoolField = VideoService.class.getDeclaredField("forkJoinPool");
        forkJoinPoolField.setAccessible(true);
        forkJoinPoolField.set(videoService, forkJoinPool);

        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(500);
                forkJoinPool.shutdownNow();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        videoService.videoUpdateTask("aaa", 100);

        when(redisService.hashMultiGet(any(), anyList())).thenReturn(new JSONObject(){{
            put(S3_VIDEO_SLICE_INFO_NUM, 1);
            put(S3_VIDEO_SLICE_INFO_DOORBELL_EVENT_INFO, "[{\"event\":18},{\"event\":19}]");
        }});
        videoService.videoUpdateTask("bbb", 100);

        when(redisService.hashMultiGet(any(), anyList())).thenReturn(new JSONObject(){{
            put(S3_VIDEO_SLICE_INFO_NUM, 1);
            put(S3_VIDEO_SLICE_INFO_DOORBELL_EVENT_INFO, "[{\"event\":18},{\"event\":19}]");
            put(S3_VIDEO_SLICE_INFO_DEVICE_CALL_INFO, "{}");
        }});
        videoService.videoUpdateTask("ccc", 100);
    }
    */

    @Test
    @SneakyThrows
    public void test_getS3RootPath() {
        String rootPath = videoService.getS3RootPath("bucket1");
        Assert.assertEquals("https://bucket1.s3.cn-north-1.amazonaws.com.cn/", rootPath);
    }

    @Test
    @SneakyThrows
    public void test_getS3ObjectUrl() {
        final Boolean enable = videoSliceConfig.getDeviceS3AccelerateEnable();
        try {
            videoSliceConfig.setDeviceS3AccelerateEnable(false);
            String url1 = videoService.getS3ObjectUrl("bucket1", "path1/path2");
            Assert.assertEquals("https://bucket1.s3.cn-north-1.amazonaws.com.cn/path1/path2", url1);

            videoSliceConfig.setDeviceS3AccelerateEnable(true);
            String url2 = videoService.getS3ObjectUrl("bucket1", "path1/path2");
            Assert.assertEquals("https://bucket1.s3-accelerate.amazonaws.com/path1/path2", url2);
        } finally {
            videoSliceConfig.setDeviceS3AccelerateEnable(enable);
        }
    }

    @Test
    public void test_querySliceByUserIdAndTraceId() {
        videoService.querySliceByUserIdAndTraceId(anyInt(), anyString());
    }

    @Test
    public void test_queryVideoUrlByAdminUserIdAndTraceIds() {
        videoService.queryVideoUrlByAdminUserIdAndTraceIds(anyInt(), any());
    }

}
