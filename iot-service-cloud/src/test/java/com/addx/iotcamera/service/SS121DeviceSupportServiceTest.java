package com.addx.iotcamera.service;

import com.addx.iotcamera.dao.device.DeviceSupportDAO;
import com.addx.iotcamera.publishers.vernemq.MqttReceivePackage;
import com.addx.iotcamera.service.device.DeviceSupportService;
import com.addx.iotcamera.testutil.TestHelper;
import com.addx.iotcamera.util.OpenApiUtil;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.config.entity.CloudDeviceRemoteDO;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.junit.Assert;
import org.mockito.AdditionalAnswers;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Map;

import static com.addx.iotcamera.dao.device.DeviceSupportDAOHelper.deviceSupportToMap;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
//@RunWith(MockitoJUnitRunner.Silent.class)
public class SS121DeviceSupportServiceTest {

    @InjectMocks
    private DeviceSupportService deviceSupportService;

    @Mock
    private DeviceSupportDAO deviceSupportDAO;

    private TestHelper testHelper;
    private DeviceSupportDAO deviceSupportDAOReal;

//    @Before
    public void before() {
        testHelper = TestHelper.getInstanceByEnv("test");
        deviceSupportDAOReal = testHelper.getMapper(DeviceSupportDAO.class);
        when(deviceSupportDAO.insert(anyString(), any())).thenAnswer(AdditionalAnswers.delegatesTo(deviceSupportDAOReal));
        when(deviceSupportDAO.queryBySerialNumber(anyString())).thenAnswer(AdditionalAnswers.delegatesTo(deviceSupportDAOReal));
    }

//    @After
    public void after() {
        testHelper.commitAndClose();
    }

//    @Test
    public void test() {
        MqttReceivePackage receivePackage = new MqttReceivePackage();
        receivePackage.setSerialNumber(OpenApiUtil.shortUUID());
        /*
        "supportSnapshotRecording": true, // 是否支持快照录像
        "snapshotRecordingCaptureInterval": [
            "auto", // automatic camra在白天和晚上自动按不同间隔进行拍摄
            "3600s", // 1 hour
            "1800s", // 30 minutes
            "300s", // 5 minutes
            "60s", // 1 minutes
            "10s", // 10 seconds
        ]
        "supportEventRecordingDualView": true, // 是否支持事件录像的双画面功能
        "deviceDualViewType":0, //0，不支持双画面，1 电子，2 物理
        "deviceDualViewInfo":[ //deviceDualViewType 为0时可不携带该字段
        {"viewType":"mainView", "supportResolution":"3860x2140,1280x720,auto"},
        {"viewType":"subView", "supportResolution":"1280x720"}],
         */
        receivePackage.setValue(new JSONObject()
                .fluentPut("supportSnapshotRecording", true)
                .fluentPut("snapshotRecordingCaptureInterval", new JSONArray()
                        .fluentAdd("auto")
                        .fluentAdd("3600s")
                        .fluentAdd("1800s")
                        .fluentAdd("300s")
                        .fluentAdd("60s")
                        .fluentAdd("10s")
                )
                .fluentPut("supportEventRecordingDualView", true)
                .fluentPut("deviceDualViewType", 1)
                .fluentPut("deviceDualViewInfo", new JSONArray()
                        .fluentAdd(new JSONObject().fluentPut("viewType", "mainView").fluentPut("supportResolution", "3860x2140,1280x720,auto"))
                        .fluentAdd(new JSONObject().fluentPut("viewType", "subView").fluentPut("supportResolution", "1280x720"))
                )
        );
        CloudDeviceRemoteDO CloudDeviceRemoteDO = receivePackage.getValue().toJavaObject(CloudDeviceRemoteDO.class);
        CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.from(CloudDeviceRemoteDO);
        Map<String, Object> deviceSupportMap1 = deviceSupportToMap(cloudDeviceSupport, true);
        int insertNum = deviceSupportService.insertDeviceSupport(receivePackage.getSerialNumber(), cloudDeviceSupport);
        Assert.assertEquals(1, insertNum);
        Map<String, Object> deviceSupportMap2 = deviceSupportDAOReal.queryBySerialNumber(receivePackage.getSerialNumber());
        for (String camelKey : receivePackage.getValue().keySet()) {
            String key = TextUtil.toUnderline(camelKey);
            Object v1 = deviceSupportMap1.get(key);
            Object v2 = deviceSupportMap2.get(key);
            log.info("deviceSupportMap: {} , {} , {}", key, v1, v2);
        }
        for (String camelKey : receivePackage.getValue().keySet()) {
            String key = TextUtil.toUnderline(camelKey);
            Object v1 = deviceSupportMap1.get(key);
            Object v2 = deviceSupportMap2.get(key);
            Assert.assertEquals(String.format("key=%s,v1=%s,v2=%s", camelKey, v1, v2), v1, v2);
        }
        CloudDeviceSupport cloudDeviceSupport2 = deviceSupportService.queryDeviceSupportBySn(receivePackage.getSerialNumber());
        JSONObject deviceSupportJsonObj = (JSONObject) JSONObject.toJSON(cloudDeviceSupport2);
        for (String camelKey : receivePackage.getValue().keySet()) {
            Object v1 = receivePackage.getValue().get(camelKey);
            Object v2 = deviceSupportJsonObj.get(camelKey);
            Assert.assertEquals(String.format("key=%s,v1=%s,v2=%s", camelKey, v1, v2), v1, v2);
        }
    }

}
