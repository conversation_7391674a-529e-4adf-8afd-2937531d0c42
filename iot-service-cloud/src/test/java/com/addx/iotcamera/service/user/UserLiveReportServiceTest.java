package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.domain.user.UserAppScoreDO;
import com.addx.iotcamera.dao.user.IUserLiveReportDao;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.addx.iotcamera.enums.user.UserLiveResultEnum.LIVE_FAIL;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UserLiveReportServiceTest {
    @InjectMocks
    private UserLiveReportService userLiveReportService;
    @Mock
    private IUserLiveReportDao iUserLiveReportDao;

    @Test
    @DisplayName("直播结果为空，不计算直播成功率")
    public void test_saveLiveReport_resultNull(){
        Integer userId = 1;
        String result = "";
        String liveId = "";

        userLiveReportService.saveLiveReport(userId,result,liveId);
        verify(iUserLiveReportDao, times(0)).insertUserLiveReport(any());
    }


    @Test
    @DisplayName("直播结果错误，不计算直播成功率")
    public void test_saveLiveReport_resultError(){
        Integer userId = 1;
        String result = "test";
        String liveId = "";

        userLiveReportService.saveLiveReport(userId,result,liveId);
        verify(iUserLiveReportDao, times(0)).insertUserLiveReport(any());
    }

    @Test
    @DisplayName("保存直播结果记录")
    public void test_saveLiveReport(){
        Integer userId = 1;
        String result = "liveSuccess";
        String liveId = "liveId";
        when(iUserLiveReportDao.insertUserLiveReport(any())).thenReturn(1);

        userLiveReportService.saveLiveReport(userId,result,liveId);
        verify(iUserLiveReportDao, times(1)).insertUserLiveReport(any());
    }

    @Test
    @DisplayName("直播成功率-低")
    public void liveSuccessRate_low(){
        UserAppScoreDO userAppScoreDO = new UserAppScoreDO();
        userAppScoreDO.setUserId(1);
        userAppScoreDO.setCreateTime(1);
        userAppScoreDO.setEffectiveTime(1L);

        when(iUserLiveReportDao.queryUserLiveReport(1,1,1,LIVE_FAIL.getCode())).thenReturn(50);
        when(iUserLiveReportDao.queryUserLiveReport(1,1,1,null)).thenReturn(100);

        Boolean expectedResult = false;
        Boolean actualResult = userLiveReportService.liveSuccessRate(userAppScoreDO);
        Assert.assertEquals(expectedResult,actualResult);
    }



    @Test
    @DisplayName("直播成功率-高")
    public void liveSuccessRate(){
        UserAppScoreDO userAppScoreDO = new UserAppScoreDO();
        userAppScoreDO.setUserId(1);
        userAppScoreDO.setCreateTime(1);
        userAppScoreDO.setEffectiveTime(1L);

        when(iUserLiveReportDao.queryUserLiveReport(1,1,1,LIVE_FAIL.getCode())).thenReturn(0);
        when(iUserLiveReportDao.queryUserLiveReport(1,1,1,null)).thenReturn(100);

        Boolean expectedResult = true;
        Boolean actualResult = userLiveReportService.liveSuccessRate(userAppScoreDO);
        Assert.assertEquals(expectedResult,actualResult);
    }
}
