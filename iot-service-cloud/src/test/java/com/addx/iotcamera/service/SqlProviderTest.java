package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.LibraryRequest;
import com.addx.iotcamera.bean.db.LibraryStatusTb;
import com.addx.iotcamera.bean.domain.library.StorageClearLibraryInfo;
import com.addx.iotcamera.dao.library.SqlProvider;
import com.addx.iotcamera.shardingjdbc.dao.library.ShardingSqlProvider;
import com.addx.iotcamera.shardingjdbc.dao.library.ShardinglibraryStatusProvider;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.addx.iotcamera.dao.library.SqlProvider.getSerialNumberActivityZone;
import static com.addx.iotcamera.dao.library.SqlProvider.getSerialNumberActivityZoneV2;

@RunWith(MockitoJUnitRunner.class)
public class SqlProviderTest {

    // 测试sql拼接
    @Test
    public void test_getSerialNumberActivityZone() {
        Map<String, List<Integer>> serialNumberToActivityZone = new LinkedHashMap<>();
        serialNumberToActivityZone.put("abc", Arrays.asList());
        System.out.println(getSerialNumberActivityZone(serialNumberToActivityZone));
        serialNumberToActivityZone.put("abc2", Arrays.asList());
        System.out.println(getSerialNumberActivityZone(serialNumberToActivityZone));
        serialNumberToActivityZone.remove("abc2");
        System.out.println(getSerialNumberActivityZone(serialNumberToActivityZone));
        serialNumberToActivityZone.put("abc", Arrays.asList(111, 222));
        System.out.println(getSerialNumberActivityZone(serialNumberToActivityZone));
        serialNumberToActivityZone.put("def", Arrays.asList());
        System.out.println(getSerialNumberActivityZone(serialNumberToActivityZone));
        serialNumberToActivityZone.put("xyz", Arrays.asList(333, 444));
        System.out.println(getSerialNumberActivityZone(serialNumberToActivityZone));
    }

    @Test
    public void test_getSerialNumberActivityZoneV2() {
        Map<String, List<Integer>> serialNumberToActivityZone = new LinkedHashMap<>();
        serialNumberToActivityZone.put("abc", null);
        System.out.println(getSerialNumberActivityZoneV2(serialNumberToActivityZone));
        serialNumberToActivityZone.put("abc", Arrays.asList());
        System.out.println(getSerialNumberActivityZoneV2(serialNumberToActivityZone));
        serialNumberToActivityZone.put("abc2", Arrays.asList());
        System.out.println(getSerialNumberActivityZoneV2(serialNumberToActivityZone));
        serialNumberToActivityZone.remove("abc2");
        System.out.println(getSerialNumberActivityZoneV2(serialNumberToActivityZone));
        serialNumberToActivityZone.put("abc", Arrays.asList(111, 222));
        System.out.println(getSerialNumberActivityZoneV2(serialNumberToActivityZone));
        serialNumberToActivityZone.put("def", Arrays.asList());
        System.out.println(getSerialNumberActivityZoneV2(serialNumberToActivityZone));
        serialNumberToActivityZone.put("xyz", Arrays.asList(333, 444));
        System.out.println(getSerialNumberActivityZoneV2(serialNumberToActivityZone));
    }

    @Test
    public void test_getTagCondition() {
        LibraryRequest libraryRequest = new LibraryRequest();
        libraryRequest.setUserId(1);
        libraryRequest.setTags(Collections.singletonList("person"));

        String sql = new SqlProvider().selectCountLibrary(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        sql = new SqlProvider().selectCountLibraryEvent(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        VideoLibraryStorageClearService.storageClearLibraryInfoThreadLocal.set(new StorageClearLibraryInfo());
        sql = new SqlProvider().selectLibrary(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        sql = new SqlProvider().selectLibraryEvent(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        libraryRequest.setDoorbellTags(Arrays.asList("tag1", "tag2"));

        sql = new ShardingSqlProvider().selectCountLibrary(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        sql = new ShardingSqlProvider().selectCountLibraryEvent(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        sql = new ShardingSqlProvider().selectLibrary(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        sql = new ShardingSqlProvider().selectLibraryEvent(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        libraryRequest.setDeviceCallEventTag("DEVICE_CALL");

        sql = new ShardingSqlProvider().selectCountLibrary(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        sql = new ShardingSqlProvider().selectCountLibraryEvent(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        sql = new ShardingSqlProvider().selectLibrary(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        sql = new ShardingSqlProvider().selectLibraryEvent(libraryRequest);
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        String tagCondition = ShardingSqlProvider.getTagCondition(null, null, null);
        Assert.assertTrue(StringUtils.isEmpty(tagCondition));

        tagCondition = ShardingSqlProvider.getTagCondition(Collections.singletonList("person"), null, null);
        Assert.assertTrue(StringUtils.isNotEmpty(tagCondition));

        tagCondition = ShardingSqlProvider.getTagCondition(null, Collections.singletonList("doorbell_press"), null);
        Assert.assertTrue(StringUtils.isNotEmpty(tagCondition));

        tagCondition = ShardingSqlProvider.getTagCondition(null, null, "device_call");
        Assert.assertTrue(StringUtils.isNotEmpty(tagCondition));

        tagCondition = ShardingSqlProvider.getTagCondition(Collections.singletonList("person"), Collections.singletonList("doorbell_press"), "device_call");
        Assert.assertTrue(StringUtils.isNotEmpty(tagCondition));
    }

    @Test
    public void test_selectLibraryStatus() {
        ShardinglibraryStatusProvider shardinglibraryStatusProvider = new ShardinglibraryStatusProvider();
        String sql = shardinglibraryStatusProvider.selectLibraryStatus(LibraryStatusTb.builder().build());
        Assert.assertTrue(StringUtils.isNotEmpty(sql));

        sql = shardinglibraryStatusProvider.selectLibraryStatus(LibraryStatusTb.builder().traceId("trace_01").build());
        Assert.assertTrue(StringUtils.isNotEmpty(sql));
    }
}
