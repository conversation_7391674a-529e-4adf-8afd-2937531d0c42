package com.addx.iotcamera.config.app;

import com.addx.iotcamera.bean.response.ZendeskItem;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.zendesk.client.v2.Zendesk;

import java.util.Map;

import static org.addx.iot.common.constant.AppConstants.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ZendeskConfigTest {
    @InjectMocks
    private ZendeskConfig zendeskConfig;

    @Before
    public void init(){
        ZendeskItem zendeskItem = new ZendeskItem();
        Map<String,ZendeskItem> countryMap = Maps.newHashMap();
        countryMap.put(ZENDESK_NODE_US,zendeskItem);
        Map<String,Map<String,ZendeskItem>> sdkconfig = Maps.newHashMap();
        sdkconfig.put(TENANTID_VICOO,countryMap);
        zendeskConfig.setSdkconfig(sdkconfig);

        ZendeskConfig.ZendeskUser zendeskUser = new ZendeskConfig.ZendeskUser();
        zendeskUser.setUrl("https://1117336245552676.us-east-1.fc.aliyuncs.com/2016-08-15/proxy/zendesk/zendesk_api_proxy");
        zendeskUser.setUsername("<EMAIL>");
        zendeskUser.setToken("krHQMo2TJxsnKFKzl8Bk3b6AeGwMWEfvcNwGXh2x");
        Map<String, ZendeskConfig.ZendeskUser> userMap = Maps.newHashMap();
        userMap.put(TENANTID_VICOO,zendeskUser);
        zendeskConfig.setUserconfig(userMap);

    }
    @Test
    public void test_queryZendeskItem(){
        {
            ZendeskItem exceptionResult = null;
            ZendeskItem actualResult = zendeskConfig.queryZendeskItem(TENANTID_VICOO,ZENDESK_NODE_CN);
            Assert.assertEquals(exceptionResult,actualResult);
        }
        {
            ZendeskItem exceptionResult = new ZendeskItem();
            ZendeskItem actualResult = zendeskConfig.queryZendeskItem(TENANTID_GUARD,ZENDESK_NODE_US);
            Assert.assertEquals(exceptionResult,actualResult);
        }
    }

    @Test
    public void test_init(){
        try{
            zendeskConfig.init();
        }catch (Exception e){

        }

    }

    @Test
    public void test_queryZendesk(){
        {
            Zendesk actualResult = zendeskConfig.queryZendesk(TENANTID_VICOO);

            Zendesk exceptionZendesk = null;
            Assert.assertEquals(exceptionZendesk,actualResult);
        }
        {
            Zendesk actualResult = zendeskConfig.queryZendesk(TENANTID_GUARD);

            Zendesk exceptionZendesk = null;
            Assert.assertEquals(exceptionZendesk,actualResult);
        }
    }
}
