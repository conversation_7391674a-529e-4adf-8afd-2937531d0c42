package com.addx.iotcamera.config.app;


import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.response.user.ABTestResult;
import com.addx.iotcamera.bean.response.user.FreeLicenseABTestResult;
import com.addx.iotcamera.bean.response.user.RecommendProductDO;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.abtest.AbTestService;
import com.addx.iotcamera.service.abtest.model.AbFeatureSimpleResult;
import com.addx.iotcamera.service.user.UserSettingService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.addx.iotcamera.constants.AbTestConstants.*;
import static org.addx.iot.common.constant.AppConstants.*;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.Silent.class)
@Slf4j
public class AppRecommendProductConfigTest {

    @InjectMocks
    private AppRecommendProductConfig appRecommendProductConfig;

    @Mock
    private AbTestService abTestService;

    @Mock
    private UserSettingService userSettingService;

    @Mock
    private IUserVipDAO userVipDAO;

    @Before
    public void init(){
        Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
        AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();
        recommendProduct.setRecommendProductId(20111);
        recommendProduct.setFreeProductId(20004);

        recommendProduct.setDefaultMonthSelectProductId(20401);
        recommendProduct.setDefaultYearSelectProductId(20402);

        List<UserVipTier.RecommendProduct> yearList = Lists.newArrayList();
        UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
        product.setProductId(20401);
        product.setMaxDeviceNum(1);
        yearList.add(product);
        recommendProduct.setRecommendProductIdYear(yearList);

        List<UserVipTier.RecommendProduct> fourGList = Lists.newArrayList();
        UserVipTier.RecommendProduct product4G = new UserVipTier.RecommendProduct();
        product4G.setProductId(21101);
        product4G.setMaxDeviceNum(1);
        fourGList.add(product4G);
        recommendProduct.setRecommendProduct4g(fourGList);

        List<UserVipTier.RecommendProduct> v1List = Lists.newArrayList();
        UserVipTier.RecommendProduct productV1 = new UserVipTier.RecommendProduct();
        productV1.setProductId(20401);
        productV1.setMaxDeviceNum(1);
        v1List.add(productV1);
        recommendProduct.setRecommendProductIdV1(v1List);

        map.put(TENANTID_VICOO,recommendProduct);
        appRecommendProductConfig.setConfig(map);
    }

    @Test
    @DisplayName("获取推荐商品-未配置")
    public void test_RecommendProductId_null() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "vicoo";
        Integer expectResult = null;
        Integer actualResult = appRecommendProductConfig.queryRecommendProductId(tenantId);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("获取推荐商品")
    public void test_RecommendProductId() {
        String tenantId = "vicoo";
        Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
        AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();
        recommendProduct.setRecommendProductId(20111);
        recommendProduct.setFreeProductId(null);
        map.put(tenantId,recommendProduct);
        appRecommendProductConfig.setConfig(map);

        Integer expectResult = 20111;
        Integer actualResult = appRecommendProductConfig.queryRecommendProductId(tenantId);
        Assert.assertEquals(expectResult,actualResult);
    }



    @Test
    @DisplayName("获取免费商品-未配置")
    public void test_FreeProductId_null() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "vicoo";
        Integer expectResult = null;
        Integer actualResult = appRecommendProductConfig.queryFreeProductId(tenantId);
        Assert.assertEquals(expectResult,actualResult);
    }


    @Test
    @DisplayName("获取免费商品")
    public void test_FreeProductId() {
        String tenantId = "vicoo";
        Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
        AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();
        recommendProduct.setRecommendProductId(20111);
        recommendProduct.setFreeProductId(20004);
        map.put(tenantId,recommendProduct);
        appRecommendProductConfig.setConfig(map);

        Integer expectResult = 20004;
        Integer actualResult = appRecommendProductConfig.queryFreeProductId(tenantId);
        Assert.assertEquals(expectResult,actualResult);
    }

    @Test
    @DisplayName("获取年订阅商品")
    public void test_queryRecommendProductIdYear(){
        List<UserVipTier.RecommendProduct> exceptedResult ;
        List<UserVipTier.RecommendProduct> actualResult ;
        {
            exceptedResult = Lists.newArrayList();
            actualResult = appRecommendProductConfig.queryRecommendProductIdYear(TENANTID_GUARD,APP_TYPE_ANDROID);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            exceptedResult = Lists.newArrayList();
            actualResult = appRecommendProductConfig.queryRecommendProductIdYear(TENANTID_GUARD,APP_TYPE_IOS);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
            product.setProductId(20401);
            product.setMaxDeviceNum(1);
            exceptedResult = Arrays.asList(product);
            actualResult = appRecommendProductConfig.queryRecommendProductIdYear(TENANTID_VICOO,APP_TYPE_IOS);
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }


    @Test
    @DisplayName("获取推荐商品Id-v1 - 未配置")
    public void test_queryRecommendProductIdV1_empty() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "vicoo";
        String appType = "ANDROID";
        List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductIdV1(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取推荐商品Id-v1-7天freetrial")
    public void test_queryRecommendProductIdV1With7DayFreeTrial_empty() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "vicoo";
        String appType = "Android";
        List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductIdV1With7DayFreeTrial(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取推荐商品Id-v1-7天freetrial")
    public void test_queryRecommendProductIdV1With7DayFreeTrial_guard() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "guard";
        String appType = "Android";
        List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductIdV1With7DayFreeTrial(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取推荐商品Id-v1-7天freetrial")
    public void test_queryRecommendProductIdV1With7DayFreeTrial() {
        String tenantId = "vicoo";
        String appType = "IOS";
        Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
        AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();

        List<UserVipTier.RecommendProduct> v1List = Lists.newArrayList();
        UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
        product.setProductId(20301);
        product.setMaxDeviceNum(2);
        v1List.add(product);
        recommendProduct.setRecommendProductIdV1With7DayFreeTrial(v1List);

        map.put(tenantId, recommendProduct);
        appRecommendProductConfig.setConfig(map);

        List<UserVipTier.RecommendProduct> expectedResult = v1List;
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductIdV1With7DayFreeTrial(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取推荐商品Id-year-7天freetrial")
    public void test_queryRecommendProductIdYearWith7DayFreeTrial_empty() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "vicoo";
        String appType = "Android";
        List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductIdYearWith7DayFreeTrial(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取推荐商品Id-year-7天freetrial")
    public void test_queryRecommendProductIdYearWith7DayFreeTrial_guard() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "guard";
        String appType = "Android";
        List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductIdYearWith7DayFreeTrial(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取推荐商品Id-v1-7天freetrial")
    public void test_queryRecommendProductIdYearWith7DayFreeTrial() {
        String tenantId = "vicoo";
        String appType = "IOS";
        Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
        AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();

        List<UserVipTier.RecommendProduct> v1List = Lists.newArrayList();
        UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
        product.setProductId(20301);
        product.setMaxDeviceNum(2);
        v1List.add(product);
        recommendProduct.setRecommendProductIdYearWith7DayFreeTrial(v1List);

        map.put(tenantId, recommendProduct);
        appRecommendProductConfig.setConfig(map);

        List<UserVipTier.RecommendProduct> expectedResult = v1List;
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductIdYearWith7DayFreeTrial(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取推荐商品Id-v1")
    public void test_queryRecommendProductIdV1() {
        String tenantId = "vicoo";
        String appType = "IOS";
        Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
        AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();

        List<UserVipTier.RecommendProduct> v1List = Lists.newArrayList();
        UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
        product.setProductId(20301);
        product.setMaxDeviceNum(2);
        v1List.add(product);
        recommendProduct.setRecommendProductIdV1(v1List);

        map.put(tenantId, recommendProduct);
        appRecommendProductConfig.setConfig(map);

        List<UserVipTier.RecommendProduct> expectedResult = v1List;
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductIdV1(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取4G推荐商品 - 未配置")
    public void test_queryRecommendProduct4G_NotGuard_isAndroid_empty() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "vicoo";
        String appType = "Android";
        List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProduct4G(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取4G推荐商品 - 未配置")
    public void test_queryRecommendProduct4G_isGuard_isAndroid_empty() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "guard";
        String appType = "Android";
        List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProduct4G(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取4G推荐商品 - 未配置")
    public void test_queryRecommendProduct4G_NotGuard_NotAndroid_empty() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "vicoo";
        String appType = "iOS";
        List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProduct4G(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取4G推荐商品 - 未配置")
    public void test_queryRecommendProduct4G_isGuard_NotAndroid_empty() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "guard";
        String appType = "iOS";
        List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProduct4G(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取4G half year推荐商品")
    public void test_queryRecommendProductHalfYear4G_isGuard_NotAndroid_empty() {
        {
            appRecommendProductConfig.setConfig(Maps.newHashMap());
            String tenantId = "guard";
            String appType = "iOS";
            List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
            List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductHalfYear4G(tenantId, appType);
            Assert.assertEquals(expectedResult, actualResult);
        }
        {
            appRecommendProductConfig.setConfig(Maps.newHashMap());
            String tenantId = "guard";
            String appType = "Android";
            List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
            List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductHalfYear4G(tenantId, appType);
            Assert.assertEquals(expectedResult, actualResult);
        }
        {
            appRecommendProductConfig.setConfig(Maps.newHashMap());
            String tenantId = "vicoo";
            String appType = "Android";
            List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
            List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductHalfYear4G(tenantId, appType);
            Assert.assertEquals(expectedResult, actualResult);
        }
        {
            String tenantId = "vicoo";
            String appType = "IOS";
            Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
            AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();

            List<UserVipTier.RecommendProduct> product4gList = Lists.newArrayList();
            UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
            product.setProductId(20403);
            product.setMaxDeviceNum(3);
            product4gList.add(product);
            recommendProduct.setRecommendProductHalfYear4g(product4gList);

            map.put(tenantId, recommendProduct);
            appRecommendProductConfig.setConfig(map);

            List<UserVipTier.RecommendProduct> expectedResult = product4gList;
            List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductHalfYear4G(tenantId, appType);
            Assert.assertEquals(expectedResult, actualResult);
        }
    }

    @Test
    @DisplayName("获取4G year推荐商品")
    public void test_queryRecommendProductYear4G_isGuard_NotAndroid_empty() {
        {
            appRecommendProductConfig.setConfig(Maps.newHashMap());
            String tenantId = "guard";
            String appType = "iOS";
            List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
            List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductYear4G(tenantId, appType);
            Assert.assertEquals(expectedResult, actualResult);
        }
        {
            appRecommendProductConfig.setConfig(Maps.newHashMap());
            String tenantId = "guard";
            String appType = "Android";
            List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
            List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductYear4G(tenantId, appType);
            Assert.assertEquals(expectedResult, actualResult);
        }
        {
            appRecommendProductConfig.setConfig(Maps.newHashMap());
            String tenantId = "vicoo";
            String appType = "Android";
            List<UserVipTier.RecommendProduct> expectedResult = Lists.newArrayList();
            List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductYear4G(tenantId, appType);
            Assert.assertEquals(expectedResult, actualResult);
        }
        {
            String tenantId = "vicoo";
            String appType = "IOS";
            Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
            AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();

            List<UserVipTier.RecommendProduct> product4gList = Lists.newArrayList();
            UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
            product.setProductId(20403);
            product.setMaxDeviceNum(3);
            product4gList.add(product);
            recommendProduct.setRecommendProductYear4g(product4gList);

            map.put(tenantId, recommendProduct);
            appRecommendProductConfig.setConfig(map);

            List<UserVipTier.RecommendProduct> expectedResult = product4gList;
            List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProductYear4G(tenantId, appType);
            Assert.assertEquals(expectedResult, actualResult);
        }
    }

    @Test
    @DisplayName("获取4G推荐商品")
    public void test_queryRecommendProduct4G() {
        String tenantId = "vicoo";
        String appType = "IOS";
        Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
        AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();

        List<UserVipTier.RecommendProduct> product4gList = Lists.newArrayList();
        UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
        product.setProductId(20403);
        product.setMaxDeviceNum(3);
        product4gList.add(product);
        recommendProduct.setRecommendProduct4g(product4gList);

        map.put(tenantId, recommendProduct);
        appRecommendProductConfig.setConfig(map);

        List<UserVipTier.RecommendProduct> expectedResult = product4gList;
        List<UserVipTier.RecommendProduct> actualResult = appRecommendProductConfig.queryRecommendProduct4G(tenantId, appType);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取4G推荐商品默认选中id - 未配置")
    public void test_queryDefaultSelectProduct4GId_empty() {
        appRecommendProductConfig.setConfig(Maps.newHashMap());
        String tenantId = "guard";
        Integer expectedResult = null;
        Integer actualResult = appRecommendProductConfig.queryDefaultSelectProduct4GId(tenantId);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取4G推荐商品默认选中id - 已配置")
    public void test_queryDefaultSelectProduct4G() {
        String tenantId = "vicoo";
        Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
        AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();
        recommendProduct.setRecommendProductId(20111);
        recommendProduct.setFreeProductId(20004);
        recommendProduct.setDefaultSelectProduct4GId(21101);
        map.put(tenantId,recommendProduct);
        appRecommendProductConfig.setConfig(map);
        Integer expectedResult = 21101;
        Integer actualResult = appRecommendProductConfig.queryDefaultSelectProduct4GId(tenantId);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    @DisplayName("获取4G推荐商品half year默认选中id - 已配置")
    public void test_queryDefaultSelectHalfYearProduct4G() {
        {
            String tenantId = "vicoo";
            Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
            AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();
            recommendProduct.setRecommendProductId(20111);
            recommendProduct.setFreeProductId(20004);
            recommendProduct.setDefaultSelectProductHalfYear4GId(21101);
            map.put(tenantId,recommendProduct);
            appRecommendProductConfig.setConfig(map);
            Integer expectedResult = 21101;
            Integer actualResult = appRecommendProductConfig.queryDefaultSelectProductHalfYear4GId(tenantId);
            Assert.assertEquals(expectedResult, actualResult);
        }
        {
            appRecommendProductConfig.setConfig(Maps.newHashMap());
            String tenantId = "guard";
            Integer expectedResult = null;
            Integer actualResult = appRecommendProductConfig.queryDefaultSelectProductHalfYear4GId(tenantId);
            Assert.assertEquals(expectedResult, actualResult);
        }
    }

    @Test
    @DisplayName("获取4G推荐商品year默认选中id - 已配置")
    public void test_queryDefaultSelectYearProduct4G() {
        {
            String tenantId = "vicoo";
            Map<String, AppRecommendProductConfig.TierRecommendProduct> map = Maps.newHashMap();
            AppRecommendProductConfig.TierRecommendProduct recommendProduct = new AppRecommendProductConfig.TierRecommendProduct();
            recommendProduct.setRecommendProductId(20111);
            recommendProduct.setFreeProductId(20004);
            recommendProduct.setDefaultSelectProductYear4GId(21101);
            map.put(tenantId,recommendProduct);
            appRecommendProductConfig.setConfig(map);
            Integer expectedResult = 21101;
            Integer actualResult = appRecommendProductConfig.queryDefaultSelectProductYear4GId(tenantId);
            Assert.assertEquals(expectedResult, actualResult);
        }
        {
            appRecommendProductConfig.setConfig(Maps.newHashMap());
            String tenantId = "guard";
            Integer expectedResult = null;
            Integer actualResult = appRecommendProductConfig.queryDefaultSelectProductYear4GId(tenantId);
            Assert.assertEquals(expectedResult, actualResult);
        }
    }
    @Test
    @DisplayName("查询默认选择商品")
    public void test_queryDefaultSelectProductId(){
        Integer exceptedResult ;
        Integer actualResult ;
        {
            // tenantId 非vicoo
            exceptedResult = 0;
            actualResult = appRecommendProductConfig.queryDefaultSelectProductId(TENANTID_GUARD,false);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            // tenantId = vicoo,默认月订阅商品
            exceptedResult = 20401;
            actualResult = appRecommendProductConfig.queryDefaultSelectProductId(TENANTID_VICOO,false);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            // tenantId = vicoo,默认年订阅商品
            exceptedResult = 20402;
            actualResult = appRecommendProductConfig.queryDefaultSelectProductId(TENANTID_VICOO,true);
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }

    @Test
    public void test_initRecommendProductDO(){
        Integer userId = 1;
        String appType = APP_TYPE_IOS;
        String language = APP_LANGUAGE_EN;
        String tenantId = TENANTID_VICOO;
        String appVersion = "";
        AppRequestBase appRequestBase = new AppRequestBase();
        AppInfo appInfo = new AppInfo();
        appInfo.setAppType(appType);
        appInfo.setVersionName(appVersion);
        appRequestBase.setApp(appInfo);
        RecommendProductDO exceptedResult = new RecommendProductDO();
        RecommendProductDO actualResult ;

        List<UserVipTier.RecommendProduct> yearList;

        {
            // 年订阅商品ab命中
            // 推荐商品未命中
            yearList = Lists.newArrayList();
            UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
            product.setProductId(20401);
            product.setMaxDeviceNum(1);
            yearList.add(product);
            exceptedResult.setRecommendProductList(appRecommendProductConfig.queryRecommendProductIdV1(tenantId,appType));
            exceptedResult.setRecommendProductYearList(yearList);
            exceptedResult.setDefaultSelectedProductId(20401);
            exceptedResult.setYearFeatureGroup("1");
            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("20401")
                    .variationId(0)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);



            // button ab命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("continue")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("continue");
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            abFeatureSimpleResultList.put(abFeatureSimpleResult.getFeatureId(), abFeatureSimpleResult.getVariationId());
            abFeatureSimpleResultList.put(abDefaultProductResult.getFeatureId(), abDefaultProductResult.getVariationId());
            abFeatureSimpleResultList.put(buttonAbFeatureSimpleResult.getFeatureId(), buttonAbFeatureSimpleResult.getVariationId());
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
            when(userVipDAO.queryUserVipInfo(userId, 0, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(
                    new ArrayList<>()
            );
            when (abTestService.getFreeLicenseAbResult(userId, appRequestBase)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(false)
                            .hitControlGroup(true).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            exceptedResult.setFreeTrialDay(1);

            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            // 年订阅商品ab命中
            // 推荐商品未命中
            yearList = Lists.newArrayList();
            UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
            product.setProductId(20401);
            product.setMaxDeviceNum(1);
            yearList.add(product);
            exceptedResult.setRecommendProductList(appRecommendProductConfig.queryRecommendProductIdV1(tenantId,appType));

            exceptedResult.setRecommendProductYearList(yearList);
            exceptedResult.setDefaultSelectedProductId(20902);
            exceptedResult.setYearFeatureGroup("1");
            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);
            exceptedResult.setDefaultSelectedProductId(20402);


            // button ab命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("continue")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("continue");
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            abFeatureSimpleResultList.put(abFeatureSimpleResult.getFeatureId(), abFeatureSimpleResult.getVariationId());
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            exceptedResult.setFreeTrialDay(1);

            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit0DayExperimentGroup(false)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            // 年订阅商品ab命中
            // 推荐商品未命中
            yearList = Lists.newArrayList();
            UserVipTier.RecommendProduct product = new UserVipTier.RecommendProduct();
            product.setProductId(20401);
            product.setMaxDeviceNum(1);
            yearList.add(product);
            exceptedResult.setRecommendProductList(appRecommendProductConfig.queryRecommendProductIdV1(tenantId,appType));

            exceptedResult.setRecommendProductYearList(yearList);
            exceptedResult.setDefaultSelectedProductId(20902);
            exceptedResult.setYearFeatureGroup("1");
            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);
            exceptedResult.setDefaultSelectedProductId(20402);


            // button ab命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("continue")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("continue");
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            abFeatureSimpleResultList.put(abFeatureSimpleResult.getFeatureId(), abFeatureSimpleResult.getVariationId());
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            exceptedResult.setFreeTrialDay(1);

            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            Assert.assertEquals(exceptedResult,actualResult);
        }
        {
            exceptedResult = new RecommendProductDO();
            // 年订阅商品ab 未命中
            // 推荐商品未命中
            exceptedResult.setDefaultSelectedProductId(20401);
            exceptedResult.setRecommendProductList(appRecommendProductConfig.queryRecommendProductIdV1(tenantId,appType));
            exceptedResult.setYearFeatureGroup("1");


            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);
            exceptedResult.setDefaultSelectedProductId(20402);


            // button ab未命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("");
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            exceptedResult.setFreeTrialDay(1);
            List<UserVipTier.RecommendProduct> recommendProductYearList = new ArrayList<>();
            UserVipTier.RecommendProduct recommendProduct = new UserVipTier.RecommendProduct().setProductId(20401).setMaxDeviceNum(1).setSubscriptionGroupId(null);
            recommendProductYearList.add(recommendProduct);
            exceptedResult.setRecommendProductYearList(recommendProductYearList);
            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            Assert.assertEquals(exceptedResult,actualResult);
        }

        {
            exceptedResult = new RecommendProductDO();
            // 年订阅商品ab 未命中
            // 推荐商品未命中
            exceptedResult.setDefaultSelectedProductId(20401);
            exceptedResult.setRecommendProductList(null);
            exceptedResult.setYearFeatureGroup("1");


            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);
            exceptedResult.setDefaultSelectedProductId(20402);


            // button ab未命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("subscribe_btn");
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit0DayExperimentGroup(true)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            exceptedResult.setFreeTrialDay(1);
            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            Assert.assertEquals(exceptedResult,actualResult);
        }

        {
            exceptedResult = new RecommendProductDO();
            // 年订阅商品ab 未命中
            // 推荐商品未命中
            exceptedResult.setRecommendProductList(null);
            exceptedResult.setYearFeatureGroup("1");

            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);


            // button ab未命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("subscribe_btn");
            when (abTestService.getFreeLicenseAbResult(userId, appRequestBase)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(true).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            abFeatureSimpleResultList.put("annual_products", 1);
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            exceptedResult.setFreeTrialDay(1);
            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            Assert.assertEquals(exceptedResult,actualResult);
        }

        {
            exceptedResult = new RecommendProductDO();
            // 年订阅商品ab 未命中
            // 推荐商品未命中
            exceptedResult.setRecommendProductList(null);
            exceptedResult.setYearFeatureGroup("1");


            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("")
                    .variationId(-1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);


            // button ab未命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("subscribe_btn");
            when (abTestService.getFreeLicenseAbResult(userId, appRequestBase)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(false)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            abFeatureSimpleResultList.put("annual_products", 1);
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            exceptedResult.setFreeTrialDay(1);
            exceptedResult.setDefaultSelectedProductId(20402);
            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            Assert.assertEquals(exceptedResult,actualResult);
        }

        {
            exceptedResult = new RecommendProductDO();
            // 年订阅商品ab 未命中
            // 推荐商品未命中
            exceptedResult.setRecommendProductList(null);
            exceptedResult.setYearFeatureGroup("1");

            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);


            // button ab未命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("1");
            when (abTestService.getFreeLicenseAbResult(userId, appRequestBase)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(false).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(true)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            abFeatureSimpleResultList.put("annual_products", 1);
            abFeatureSimpleResultList.put("defaut_selected_product", 1);
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            exceptedResult.setFreeTrialDay(1);
            exceptedResult.setDefaultSelectedProductId(1);

            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            Assert.assertEquals(exceptedResult,actualResult);
        }

        {
            exceptedResult = new RecommendProductDO();
            // 年订阅商品ab 未命中
            // 推荐商品未命中
            exceptedResult.setRecommendProductList(null);
            exceptedResult.setYearFeatureGroup("1");

            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);


            // button ab未命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("subscribe_btn");
            when (abTestService.getFreeLicenseAbResult(userId, appRequestBase)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(true).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(false).hit7DayExperimentGroup(false)
                    .hitControlGroup(false).value(1).experimentSuccessful(false).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            abFeatureSimpleResultList.put("annual_products", 1);
            abFeatureSimpleResultList.put("defaut_selected_product", 1);
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            exceptedResult.setFreeTrialDay(30);

            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            Assert.assertEquals(exceptedResult,actualResult);
        }

        {
            exceptedResult = new RecommendProductDO();
            // 年订阅商品ab 未命中
            // 推荐商品未命中
            exceptedResult.setRecommendProductList(null);
            exceptedResult.setYearFeatureGroup("1");

            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);


            // button ab未命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("subscribe_btn");
            when (abTestService.getFreeLicenseAbResult(userId, appRequestBase)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(true).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(true)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            abFeatureSimpleResultList.put("annual_products", 1);
            abFeatureSimpleResultList.put("defaut_selected_product", 1);
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            exceptedResult.setFreeTrialDay(1);

            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }

    @Test
    public void test_initRecommendProductDONotVicoo(){
        Integer userId = 1;
        String appType = APP_TYPE_IOS;
        String language = APP_LANGUAGE_EN;
        String tenantId = TENANTID_GUARD;
        String appVersion = "";
        AppRequestBase appRequestBase = new AppRequestBase();
        AppInfo appInfo = new AppInfo();
        appInfo.setAppType(appType);
        appInfo.setVersionName(appVersion);
        appRequestBase.setApp(appInfo);
        RecommendProductDO exceptedResult = new RecommendProductDO();
        RecommendProductDO actualResult ;

        List<UserVipTier.RecommendProduct> yearList;

        {
            exceptedResult = new RecommendProductDO();
            // 年订阅商品ab 未命中
            // 推荐商品未命中
            exceptedResult.setRecommendProductList(new ArrayList<>());
            exceptedResult.setYearFeatureGroup("-1");

            AbFeatureSimpleResult abFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abFeatureSimpleResult);


            AbFeatureSimpleResult abDefaultProductResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, appVersion)).thenReturn(abDefaultProductResult);


            // button ab未命中
            AbFeatureSimpleResult buttonAbFeatureSimpleResult = AbFeatureSimpleResult.builder()
                    .featureId(YEAR_TIER_RECOMMEND_BUTTON)
                    .value("1")
                    .variationId(1)
                    .build();
            when(abTestService.singleFeatureCheck("1",YEAR_TIER_RECOMMEND_BUTTON,appType,language,tenantId, appVersion)).thenReturn(buttonAbFeatureSimpleResult);
            exceptedResult.setButtonFeatureGroup("subscribe_btn");
            when (abTestService.getFreeLicenseAbResult(userId, appRequestBase)).thenReturn(FreeLicenseABTestResult.builder().isNotFreeTrial(true).isFreeTrial(false)
                    .build());
            when (abTestService.getAwarenessFreeTrailDayAbResult(userId, appRequestBase)).thenReturn(ABTestResult.builder().hit0DayExperimentGroup(true).hit7DayExperimentGroup(true)
                    .hitControlGroup(false).value(1).experimentSuccessful(true).abFeatureSimpleResultList(new HashMap<>())
                    .build());
            Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
            abFeatureSimpleResultList.put("pay_button_copy", 1);
            exceptedResult.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
            exceptedResult.setFreeTrialDay(1);
            exceptedResult.setDefaultSelectedProductId(0);
            when(userSettingService.queryUserSetting(userId)).thenReturn(UserSettingsDO.builder().supportFreeLicense(1).build());
            when(userVipDAO.queryUserVipInfo(userId, 0, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())).thenReturn(
                    new ArrayList<>()
            );

            actualResult = appRecommendProductConfig.initRecommendProductDO(userId,appType,language,tenantId, appRequestBase);
            Assert.assertEquals(exceptedResult,actualResult);
        }
    }
}
