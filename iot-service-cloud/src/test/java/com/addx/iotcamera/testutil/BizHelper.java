package com.addx.iotcamera.testutil;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class BizHelper {

    /**
     * 模拟多语言配置
     *
     * @param config key -> language -> value
     */
    public static void mockMultiLanguageConfig(Map<String, Map<String, String>> config, Iterable<String> keys) {
        List<String> languages = Arrays.asList("zh", "en", "ja", "de", "fr", "ru", "it", "es");
        for (String key : keys) {
            config.computeIfAbsent(key, it -> languages.stream()
                    .collect(Collectors.toMap(lan -> lan, lan -> "[" + lan + "]" + key)));
        }
    }

}
