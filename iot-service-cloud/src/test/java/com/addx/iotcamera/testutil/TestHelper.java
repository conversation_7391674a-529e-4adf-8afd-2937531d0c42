package com.addx.iotcamera.testutil;

import com.addx.iotcamera.bean.init.AmazonInit;
import com.addx.iotcamera.config.S3Config;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.util.ProxyUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.securitytoken.AWSSecurityTokenService;
import com.zaxxer.hikari.HikariDataSource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.junit.Assert;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 测试帮助类
 * 有时需要连接外部依赖做一些测试：mysql、s3、redis...
 */
@Slf4j
public class TestHelper {

    @Getter
    private final JSONObject config;

    @Getter
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HelperConfig {
        @Builder.Default
        private final boolean autoCommit = false;
        @Builder.Default
        private final String dbConfigName = "camera";
    }

    private DataSource dataSource;
    private SqlSessionFactory sqlSessionFactory;
    private SqlSession sqlSession;
    private RedisConnectionFactory redisConnectionFactory;
    private RedisTemplate redisTemplate;
    private AmazonInit amazonInit;

    public TestHelper(JSONObject config) {
        log.info("TestHelper constructor");
        this.config = new JSONObject(config);
    }

    private static HelperConfig getHelperConfig(JSONObject config) {
        HelperConfig helperConfig = config.getObject("helperConfig", HelperConfig.class);
        return Optional.ofNullable(helperConfig).orElseGet(HelperConfig::new);
    }

    public <T> T getMapper(Class<T> cls) {
        return getSession().getMapper(cls);
    }

    public void close() {
        if (sqlSession != null) {
            sqlSession.close();
        }
    }

    public void commitAndClose() {
        if (sqlSession != null) {
            try {
                sqlSession.commit();
                sqlSession.close();
            } catch (Exception e) {
            }
        }
    }

    public void commit() {
        if (sqlSession != null) {
            try {
                sqlSession.commit();
            } catch (Exception e) {
            }
        }
    }

    public SqlSession getSession() {
        if (sqlSession != null) return sqlSession;
        return (sqlSession = getSqlSessionFactory().openSession());
    }

    public SqlSessionFactory getSqlSessionFactory() {
        if (sqlSessionFactory != null) return sqlSessionFactory;
        return (sqlSessionFactory = getSqlSessionFactory(getDataSource()));
    }

    public S3Config getS3Config() {
        return config.getObject("s3config", S3Config.class);
    }

    public AmazonInit getAmazonInit() {
        if (amazonInit != null) return amazonInit;
        // eg: dynamo.videoSlice.tableName
        return (amazonInit = getAmazonInit(getS3Config(), config.getJSONObject("dynamo"), config.getJSONObject("dynamodb")));
    }

    public AWSSecurityTokenService getStsClient() {
        final JSONObject sts = config.getJSONObject("sts");
        return getAmazonInit().stsClient(sts.getString("clientRegion"), sts.getString("accessKey"), sts.getString("secretKey"));
    }

    public AmazonS3 getS3Client() {
        return getAmazonInit().s3Client();
    }

    public DataSource getDataSource() {
        if (dataSource != null) return dataSource;
        return (dataSource = getDataSource(config.getJSONObject("spring").getJSONObject("datasource")));
    }

    public static SqlSessionFactory getSqlSessionFactory(DataSource dataSource) {
        Environment environment = new Environment("local-test", new JdbcTransactionFactory(), dataSource);
        Configuration configuration = new Configuration(environment);
        configuration.setUseGeneratedKeys(true);
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.addMappers("com.addx.iotcamera.dao");
        return new SqlSessionFactoryBuilder().build(configuration);
    }

    public static DataSource getDataSource(JSONObject config) {
        boolean autoCommit = getHelperConfig(config).isAutoCommit();
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(config.getString("driver-class-name"));
        dataSource.setJdbcUrl(config.getString("url"));
        dataSource.setUsername(config.getString("username"));
        dataSource.setPassword(config.getString("password"));
        dataSource.setAutoCommit(autoCommit);
        dataSource.setConnectionTimeout(3000L);
        return dataSource;
    }

    private static AmazonInit getAmazonInit(S3Config s3Config, JSONObject dynamo, JSONObject dynamodb) {
        AmazonInit amazonInit = ProxyUtil.createCacheProxy(AmazonInit.class, "testHelper-amazonInit");
        amazonInit.setS3Config(s3Config);
        amazonInit.setVideoSliceTableName(dynamo.getJSONObject("videoSlice").getString("tableName"));
        amazonInit.setVideoSliceCompleteTableName(dynamo.getJSONObject("videoSliceComplete").getString("tableName"));
        amazonInit.setVideoLibraryTableName(dynamo.getJSONObject("videoLibrary").getString("tableName"));
        amazonInit.setVideoEventTableName(dynamo.getJSONObject("videoEvent").getString("tableName"));
        amazonInit.setTenantAwsConfigTableName(dynamo.getJSONObject("tenantAwsConfig").getString("tableName"));
        amazonInit.setDeviceConfigTableName(dynamo.getJSONObject("deviceConfig").getString("tableName"));
        amazonInit.setDynamoDbAccessKey(dynamodb.getString("accessKey"));
        amazonInit.setDynamoDbSecretKey(dynamodb.getString("secretKey"));
        amazonInit.setDynamoDbClientRegion(dynamodb.getString("clientRegion"));
        return amazonInit;
    }

    private static RedisConnectionFactory getRedisConnectionFactory(JSONObject config) {
//        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
//        redisConfig.setDatabase(config.getIntValue("database"));
//        redisConfig.setHostName(config.getString("host"));
//        redisConfig.setPort(config.getIntValue("port"));
//        redisConfig.setPassword(RedisPassword.of(config.getString("password")));
//        JedisConnectionFactory factory = new JedisConnectionFactory(redisConfig);
//        return factory;
        return null;
    }

    private static RedisTemplate getRedisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<?, ?> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        //key序列化方式;但是如果方法上有Long等非String类型的话，会报类型转换错误；
        RedisSerializer<String> redisSerializer = new StringRedisSerializer();//Long类型不可以会出现异常信息;
        redisTemplate.setKeySerializer(redisSerializer);
        redisTemplate.setHashKeySerializer(redisSerializer);
        //默认使用JdkSerializationRedisSerializer序列化方式;会出现乱码，改成StringRedisSerializer
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringSerializer);
        redisTemplate.setValueSerializer(stringSerializer);
        redisTemplate.setHashKeySerializer(stringSerializer);
        redisTemplate.setHashValueSerializer(stringSerializer);
        return redisTemplate;
    }

    public RedisConnectionFactory getRedisConnectionFactory() {
        if (redisConnectionFactory != null) return redisConnectionFactory;
        return (redisConnectionFactory = getRedisConnectionFactory(config.getJSONObject("spring").getJSONObject("redis")));
    }

    public RedisTemplate getRedisTemplate() {
        if (redisTemplate != null) return redisTemplate;
        return (redisTemplate = getRedisTemplate(getRedisConnectionFactory()));
    }

//    private static final ConcurrentHashMap<String, TestHelper> env2Instance = new ConcurrentHashMap<>();

    public static TestHelper getInstanceByEnv(String env) {
        return getInstanceByEnv(env, null);
    }

    public static String getIotServiceDir() {
        return System.getenv("PWD"); // eg: /abc/workspace/iot-service-old
    }

    public static String getAppConfigDir() {
        return System.getenv("IOT_SERVICE_CONFIG_DIR"); // eg: /abc/workspace/app-config/iot-service-old
    }

    public static TestHelper getInstanceByEnv(String env, HelperConfig helperConfig) {
        if (env == null) return null;
        if (env.contains("staging") || env.contains("pre") || env.contains("prod")) {
            String configPath = getAppConfigDir() + "/application-" + env + ".yml";
            return getInstanceByFile(configPath, helperConfig);
        } else {
            String configPath = "classpath://application-" + env + ".yml";
            return getInstanceByFile(configPath, helperConfig);
        }
    }

    public static TestHelper getInstanceByFile(String configPath, HelperConfig helperConfig) {
        if (configPath == null) return null;
//        return env2Instance.computeIfAbsent(configPath, (key) -> {
        JSONObject config = ConfigHelper.loadYmlConfig(configPath);
        handleMysqlParams(config, configPath);
        config.put("helperConfig", helperConfig);
        return new TestHelper(config);
    }

    /**
     * 处理mysql参数
     * 线上只能用只读库
     *
     * @param config
     * @param configPath
     */
    private static void handleMysqlParams(JSONObject config, String configPath) {
        JSONObject dbConfig = config.getJSONObject("spring").getJSONObject("datasource");
        if (dbConfig.containsKey("dynamic")) {
            log.info("使用了dynamicDatabase:{}", configPath);
            String dbConfigName = getHelperConfig(config).getDbConfigName();
            dbConfig = dbConfig.getJSONObject("dynamic").getJSONObject("datasource").getJSONObject(dbConfigName);
            config.getJSONObject("spring").put("datasource", dbConfig);
        }
        final String dbUrlPtn = "**********************************************************************************************************************************************";
        if (configPath.contains("test")) {
            dbConfig.put("url", String.format(dbUrlPtn, "bj-cdb-dvkn9800.sql.tencentcdb.com:61341")); // 外网地址
        } else if (configPath.contains("staging-us")) {
            // 不变
        } else if (configPath.contains("staging-eu")) {
            // 不变
        } else if (configPath.contains("staging")) {
            dbConfig.put("url", String.format(dbUrlPtn, "bj-cynosdbmysql-grp-4e5zrhv6.sql.tencentcdb.com:29811"));
        } else if (configPath.contains("prod-us")) {
            dbConfig.put("url", String.format(dbUrlPtn, "us-prod-db.cluster-c9uvgwnpohpx.us-east-1.rds.amazonaws.com:3306"));
            dbConfig.put("username", "readOnly");
            dbConfig.put("password", "readOnly@123456");
        } else if (configPath.contains("prod-eu")) {
            dbConfig.put("url", String.format(dbUrlPtn, "eu-prod-db.cluster-chypldblqgzh.eu-central-1.rds.amazonaws.com:3306"));
            dbConfig.put("username", "readOnly");
            dbConfig.put("password", "readOnly@123456");
        } else if (configPath.contains("prod")) {
            dbConfig.put("url", String.format(dbUrlPtn, "152.136.213.104:3306"));
            dbConfig.put("username", "readOnly");
            dbConfig.put("password", "readOnly@123456");
        }
    }

    public static TestHelper getInstanceByLocal() {
        return getInstanceByEnv("local");
    }

    public static <T> T loadPropertySource(Class<T> cls) {
        if (!cls.isAnnotationPresent(PropertySource.class)) return null;
        if (!cls.isAnnotationPresent(ConfigurationProperties.class)) return null;
        String path = cls.getAnnotation(PropertySource.class).value()[0];
        String prefix = cls.getAnnotation(ConfigurationProperties.class).prefix();
        JSONObject jsonObject = ConfigHelper.loadYmlConfig(path);
        if (StringUtils.isNotBlank(prefix)) {
            for (String key : prefix.split("\\.")) {
                jsonObject = jsonObject.getJSONObject(key);
            }
        }
        T obj = jsonObject.toJavaObject(cls);
        for (Field field : cls.getDeclaredFields()) {
            if (field.isAnnotationPresent(Autowired.class)) {
                try {
                    Object obj2 = loadPropertySource(field.getType());
                    if (!field.isAccessible()) field.setAccessible(true);
                    field.set(obj, obj2);
                } catch (Exception e) {
                    com.addx.iotcamera.util.LogUtil.error(log, "load inner field", e);
                }
            }
        }
        for (Method method : cls.getDeclaredMethods()) {
            if (method.isAnnotationPresent(PostConstruct.class)) {
                try {
                    if (!method.isAccessible()) method.setAccessible(true);
                    method.invoke(obj);
                } catch (Exception e) {
                    com.addx.iotcamera.util.LogUtil.error(log, "load inner field", e);
                }
            }
        }
        return obj;
    }

    /*
    private static <T> T createCacheProxy(Class<T> cls) {
        return (T) createCacheProxyFactory(cls).create();
    }

    private static Enhancer createCacheProxyFactory(Class cls) {
        ConcurrentHashMap<Method, Object> beanCache = new ConcurrentHashMap<>();
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(cls);
        enhancer.setCallback((MethodInterceptor) (o, method, args, methodProxy) -> {
            boolean isFactoryMethod = method.isAnnotationPresent(Bean.class);
            if (isFactoryMethod) {
                Object bean = beanCache.get(method);
                if (bean != null) return bean;
            }
            Object bean = methodProxy.invokeSuper(o, args);
            if (isFactoryMethod) {
                beanCache.put(method, bean);
            }
            return bean;
        });
        return enhancer;
    }
    */

//    /**
//     * 让mock对象方法的使用真实对象代理
//     *
//     * @param mockDAO mockito创建的mock对象
//     * @param realDAO 真实实现类
//     * @param methods 方法
//     * @param <T>
//     * @param <R>
//     */
//    @SneakyThrows
//    public static <T, R> void mockCallReal(T mockDAO, R realDAO, Method... methods) {
//        for (Method method : methods) {
//            Object[] args = IntStream.range(0, method.getParameterCount()).mapToObj(it -> any()).toArray();
//            when(method.invoke(mockDAO, args)).then(it -> method.invoke(realDAO, it.getArguments()));
//        }
//    }
//
//    @SneakyThrows
//    public <T, R> R mockCallReal(T mockDAO, Class<R> realDAOCls) {
//        R realDAO = getMapper(realDAOCls);
//        mockCallReal(mockDAO, realDAO, realDAOCls.getDeclaredMethods());
//        return realDAO;
//    }

    public static void assertDeepEquals(Object o1, Object o2) {
        assertJsonEquals(JSON.toJSON(o1), JSON.toJSON(o2), "$");
    }

    private static void assertJsonEquals(Object json1, Object json2, String path) {
        if (json1 == null && json2 == null) return;
        Assert.assertTrue("any is null!path=" + path, json1 != null && json2 != null);
        if (json1 instanceof JSONArray) {
            Assert.assertTrue("json2 not JSONArray!path=" + path, json2 instanceof JSONArray);
            Assert.assertEquals("JSONArray.size not eq!path=" + path, ((JSONArray) json1).size(), ((JSONArray) json2).size());
            Iterator<Object> it1 = ((JSONArray) json1).iterator();
            Iterator<Object> it2 = ((JSONArray) json2).iterator();
            for (int i = 0; it1.hasNext() && it2.hasNext(); i++) {
                assertJsonEquals(it1.next(), it2.next(), path + "[" + i + "]");
            }
        } else if (json1 instanceof JSONObject) {
            Assert.assertTrue("json2 not JSONObject!path=" + path, json2 instanceof JSONObject);
            Assert.assertEquals("JSONObject.size not eq!path=" + path, ((JSONObject) json1).size(), ((JSONObject) json2).size());
            for (String key : ((JSONObject) json1).keySet()) {
                assertJsonEquals(((JSONObject) json1).get(key), ((JSONObject) json2).get(key), path + "." + key);
            }
        } else {
            Assert.assertEquals("obj not eq!path=" + path, json1, json2);
        }
    }

    public static void injectSpyPropertySource(Object testObj) {
        Class<?> testCls = testObj.getClass();
        EnableConfigurationProperties ann1 = testCls.getAnnotation(EnableConfigurationProperties.class);
        if (ann1 == null) return;
        try {
            Map<Class, Object> beanMap = new LinkedHashMap<>();
            for (Class<?> cls : ann1.value()) {
                beanMap.put(cls, loadPropertySource(cls));
            }
            for (Field field : testCls.getDeclaredFields()) {
//                if (!field.isAnnotationPresent(Spy.class)) continue;
                Object bean = beanMap.get(field.getType());
                if (bean == null) continue;
                field.setAccessible(true);
                Object rowBean = field.get(testObj);
                if (rowBean == null) continue;
                BeanUtils.copyProperties(bean, rowBean);
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "handlePropertySource testObj={}", testObj, e);
        }
    }
}
