package com.addx.iotcamera.controller.inner;

import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceStateDO;
import com.addx.iotcamera.bean.openapi.OpenApiAccount;
import com.addx.iotcamera.bean.request.DeactivateDeviceRequest;
import com.addx.iotcamera.enums.DeviceOnlineStatusEnums;
import com.addx.iotcamera.service.BindService;
import com.addx.iotcamera.service.StateMachineService;
import com.addx.iotcamera.service.device.DeviceService;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;

import static com.addx.iotcamera.service.openapi.OpenApiConfigService.PAAS_OWNED_TENANT_ID;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceControllerTest {

    @InjectMocks
    private DeviceController deviceController;

    @Mock
    private DeviceService deviceService;

    @Mock
    private BindService bindService;

    @Mock
    private StateMachineService stateMachineService;

    private OpenApiAccount account;
    @Before
    public void init(){
        account = new OpenApiAccount();
        account.setTenantIds(Collections.singletonList(PAAS_OWNED_TENANT_ID));
    }

    @Test
    public void deactivateDevice_device_notExist() {
        DeactivateDeviceRequest request = new DeactivateDeviceRequest();
        request.setUserId(11);
        request.setSerialNo("serial_number");
        Result result = deviceController.deactivateDevice(request, account);

        Assert.assertTrue(result.getResult()==-1);
        Assert.assertEquals("设备不存在",result.getMsg());
    }

    @Test
    public void deactivateDevice_device_notActivate() {


        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setActivated(0);
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(deviceDO);
        DeactivateDeviceRequest request = new DeactivateDeviceRequest();
        request.setUserId(11);
        request.setSerialNo("serial_number");
        Result result = deviceController.deactivateDevice(request, account);

        Assert.assertTrue(result.getResult()==-1);
        Assert.assertEquals("设备未激活",result.getMsg());
    }

    @Test
    public void deactivateDevice_device_online1() {

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setActivated(1);
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(deviceDO);
        DeactivateDeviceRequest request = new DeactivateDeviceRequest();
        request.setUserId(11);
        request.setSerialNo("serial_number");
        HashMap<String, DeviceStateDO> map = new HashMap<>();
        map.put(request.getSerialNo(), DeviceStateDO.builder().onlineStatus(DeviceOnlineStatusEnums.ONLINE).build());
        when(stateMachineService.batchGetDeviceState(Arrays.asList(request.getSerialNo()))).thenReturn(map);
        Result result = deviceController.deactivateDevice(request, account);

        Assert.assertTrue(result.getResult()==-2);
        Assert.assertEquals("当前设备在线",result.getMsg());
    }

    @Test
    public void deactivateDevice_device_online2() {

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setActivated(1);
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(deviceDO);
        DeactivateDeviceRequest request = new DeactivateDeviceRequest();
        request.setUserId(11);
        request.setSerialNo("serial_number");
        HashMap<String, DeviceStateDO> map = new HashMap<>();
        map.put(request.getSerialNo(), DeviceStateDO.builder().onlineStatus(DeviceOnlineStatusEnums.WAKE).build());
        when(stateMachineService.batchGetDeviceState(Arrays.asList(request.getSerialNo()))).thenReturn(map);
        Result result = deviceController.deactivateDevice(request, account);

        Assert.assertTrue(result.getResult()==-2);
        Assert.assertEquals("当前设备在线",result.getMsg());
    }


    @Test
    public void deactivateDevice() {
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setActivated(1);
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(deviceDO);
        DeactivateDeviceRequest request = new DeactivateDeviceRequest();
        request.setUserId(11);
        request.setSerialNo("serial_number");
        HashMap<String, DeviceStateDO> map = new HashMap<>();
        map.put(request.getSerialNo(), DeviceStateDO.builder().onlineStatus(DeviceOnlineStatusEnums.OFFLINE).build());
        when(stateMachineService.batchGetDeviceState(Arrays.asList(request.getSerialNo()))).thenReturn(map);
        Result success = Result.Success();
        when(bindService.deactivateDeviceBySerialNumber(request.getSerialNo())).thenReturn(success);
        Result result = deviceController.deactivateDevice(request, account);

        Assert.assertEquals(success,result);
    }

    @Test
    public void deactivateDevice_2() {
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setActivated(1);
        when(deviceService.getAllDeviceInfo(anyString())).thenReturn(deviceDO);
        DeactivateDeviceRequest request = new DeactivateDeviceRequest();
        request.setUserId(11);
        request.setSerialNo("serial_number");
        HashMap<String, DeviceStateDO> map = new HashMap<>();
        map.put(request.getSerialNo(), null);
        when(stateMachineService.batchGetDeviceState(Arrays.asList(request.getSerialNo()))).thenReturn(map);
        Result success = Result.Success();
        when(bindService.deactivateDeviceBySerialNumber(request.getSerialNo())).thenReturn(success);
        Result result = deviceController.deactivateDevice(request, account);

        Assert.assertEquals(success,result);
    }
}