package com.addx.iotcamera.controller;

import com.addx.iotcamera.bean.app.BindOperationRequest;
import com.addx.iotcamera.bean.domain.IpInfo;
import com.addx.iotcamera.bean.domain.UserToken;
import com.addx.iotcamera.controller.device.lifecycle.DeviceLifecycleController;
import com.addx.iotcamera.enums.DeviceNetType;
import com.addx.iotcamera.service.BindService;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.GeoIpService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.addx.iot.common.enums.ResultCollection.SUCCESS;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceLifeCycleControllerTest {

    @InjectMocks
    private DeviceLifecycleController deviceLifecycleController;
    @Mock
    private DeviceInfoService deviceInfoService;
    @Mock
    private BindService bindService;
    @Mock
    private GeoIpService geoIpService;

    private UserToken userToken;

    @Before
    public void before() {
        userToken = new UserToken();
        userToken.setUserId(1234567890);

        when(geoIpService.getIpInfo(any())).thenReturn(new IpInfo());
    }

    @SneakyThrows
    @Test
    public void test_queryDeviceCode() {
        when(bindService.queryDeviceBindCode(any(), any(), any())).thenReturn(Result.Success());
        {
            BindOperationRequest request = new BindOperationRequest();
            request.setDeviceNetType(DeviceNetType.WIFI.getCode());
            Result result = deviceLifecycleController.queryDeviceCode(userToken, request, null);
            Assert.assertEquals(INVALID_PARAMS.getCode(), result.getResult().intValue());
        }
        {
            BindOperationRequest request = new BindOperationRequest();
            request.setDeviceNetType(DeviceNetType.WIFI.getCode());
            request.setNetworkName("any_network_name");
            request.setPassword("any_network_pwd");
            Result result = deviceLifecycleController.queryDeviceCode(userToken, request, null);
            Assert.assertEquals(SUCCESS.getCode(), result.getResult().intValue());
        }
        {
            BindOperationRequest request = new BindOperationRequest();
            request.setDeviceNetType(DeviceNetType.CABLE.getCode());
            Result result = deviceLifecycleController.queryDeviceCode(userToken, request, null);
            Assert.assertEquals(SUCCESS.getCode(), result.getResult().intValue());
        }
        {
            BindOperationRequest request = new BindOperationRequest();
            request.setDeviceNetType(DeviceNetType.CABLE.getCode());
            request.setNetworkName("any_network_name");
            request.setPassword("any_network_pwd");
            Result result = deviceLifecycleController.queryDeviceCode(userToken, request, null);
            Assert.assertEquals(SUCCESS.getCode(), result.getResult().intValue());
        }
    }

    @SneakyThrows
    @Test
    public void test_queryDeviceCodeV2() {
        when(bindService.queryDeviceBindCode(any(), any(), any())).thenReturn(Result.Success());
        {
            BindOperationRequest request = new BindOperationRequest();
            request.setDeviceNetType(DeviceNetType.WIFI.getCode());
            Result result = deviceLifecycleController.queryDeviceCodeV2(userToken, request, null);
            Assert.assertEquals(INVALID_PARAMS.getCode(), result.getResult().intValue());
        }
        {
            BindOperationRequest request = new BindOperationRequest();
            request.setDeviceNetType(DeviceNetType.WIFI.getCode());
            request.setNetworkName("any_network_name");
            request.setPassword("any_network_pwd");
            Result result = deviceLifecycleController.queryDeviceCodeV2(userToken, request, null);
            Assert.assertEquals(SUCCESS.getCode(), result.getResult().intValue());
        }
        {
            BindOperationRequest request = new BindOperationRequest();
            request.setDeviceNetType(DeviceNetType.CABLE.getCode());
            Result result = deviceLifecycleController.queryDeviceCodeV2(userToken, request, null);
            Assert.assertEquals(SUCCESS.getCode(), result.getResult().intValue());
        }
        {
            BindOperationRequest request = new BindOperationRequest();
            request.setDeviceNetType(DeviceNetType.CABLE.getCode());
            request.setNetworkName("any_network_name");
            request.setPassword("any_network_pwd");
            Result result = deviceLifecycleController.queryDeviceCodeV2(userToken, request, null);
            Assert.assertEquals(SUCCESS.getCode(), result.getResult().intValue());
        }
        {
            BindOperationRequest request = new BindOperationRequest();
            request.setDeviceNetType(DeviceNetType.MOBILE.getCode());
            request.setNetworkName("any_network_name");
            request.setPassword("any_network_pwd");
            Result result = deviceLifecycleController.queryDeviceCodeV2(userToken, request, null);
            Assert.assertEquals(SUCCESS.getCode(), result.getResult().intValue());
        }
    }

    @SneakyThrows
    @Test
    public void test_bindCableDevice() {
        when(bindService.bindCableDevice(any(), any(), any())).thenReturn(Result.Success());
        {
            BindOperationRequest request = new BindOperationRequest();
            Result result = deviceLifecycleController.bindCableDevice(userToken, request, null);
            Assert.assertEquals(INVALID_PARAMS.getCode(), result.getResult().intValue());
        }
        {
            BindOperationRequest request = new BindOperationRequest();
            request.setUserSn("any_sn");
            Result result = deviceLifecycleController.bindCableDevice(userToken, request, null);
            Assert.assertEquals(SUCCESS.getCode(), result.getResult().intValue());
        }
    }
}
