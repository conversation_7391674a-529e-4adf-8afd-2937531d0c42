package com.addx.iotcamera.controller;

import com.addx.iotcamera.bean.db.LogAnalysisConfig;
import com.addx.iotcamera.bean.db.RoleConfig;
import com.addx.iotcamera.bean.log.GetConfigRequest;
import com.addx.iotcamera.bean.log.PushConfigRequest;
import com.addx.iotcamera.bean.log.SerialInfo;
import com.addx.iotcamera.controller.log.AnalysisController;
import com.addx.iotcamera.dao.log.ILogAnalysisConfigDao;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.device_msg.IotLocalService;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.service.log.AnalysisService;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AnalysisControllerTest {

    @InjectMocks
    private AnalysisController analysisController;
    @Spy
    @InjectMocks
    private AnalysisService analysisService;

    @Mock
    private RedisService redisService;
    @Mock
    private ILogAnalysisConfigDao logAnalysisConfigDao;
    @Spy
    @InjectMocks
    private IotLocalService iotLocalService;
    @Mock
    private KissWsService kissSafeRTCWsService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

}
