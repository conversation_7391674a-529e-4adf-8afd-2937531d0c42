package com.addx.iotcamera.controller;

import com.addx.iotcamera.bean.domain.device.http.DeviceCallRequest;
import com.addx.iotcamera.controller.device.operation.DeviceCallController;
import com.addx.iotcamera.service.device.DeviceCallService;
import com.addx.iotcamera.service.video.VideoGenerateService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.UUID;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceCallControllerTest {

    @Mock
    private VideoGenerateService videoGenerateService;
    @InjectMocks
    @Spy
    private DeviceCallController deviceCallController;

    @Mock
    private DeviceCallService deviceCallService;

    @Test
    public void test() {
        DeviceCallRequest deviceCallRequest = new DeviceCallRequest();
        deviceCallRequest.setTraceId(UUID.randomUUID().toString());
        deviceCallRequest.setSerialNumber("sn_01");

        Result result = deviceCallController.deviceCall(deviceCallRequest, null);

        Assert.assertTrue(result.getResult().intValue() == 0);
    }
}
