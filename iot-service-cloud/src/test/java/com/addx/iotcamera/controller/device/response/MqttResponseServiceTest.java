package com.addx.iotcamera.controller.device.response;

import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.FirmwareViewDO;
import com.addx.iotcamera.bean.domain.OTARequest;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.service.MqttResponseService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.util.DeviceAutoOtaUtil;
import com.google.api.client.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class MqttResponseServiceTest {

    @InjectMocks
    private MqttResponseService mqttResponseService;

    @InjectMocks
    private DeviceAutoOtaUtil deviceAutoOtaUtil;

    @Mock
    private FirmwareService firmwareService;

    @Mock
    private OpenApiConfigService deviceThirdConfigService;

    @Mock
    private DeviceSettingService deviceSettingService;

    @Mock
    private MqttSender mqttSender;

    @Test
    public void test_handleOtaRequest() throws Exception {
        deviceAutoOtaUtil.afterPropertiesSet();
        when(deviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO() {{
            setOtaAutoUpgrade(true);
        }});

        VernemqPublisher vernemqPublisher = new VernemqPublisher();
        Field mqttSenderField = VernemqPublisher.class.getDeclaredField("mqttSender");
        mqttSenderField.setAccessible(true);
        mqttSenderField.set(vernemqPublisher, mqttSender);
        vernemqPublisher.init();

        OTARequest otaRequest = new OTARequest();
        otaRequest.setId(1);
        otaRequest.setSerialNumber("sn_01");
        Result result = mqttResponseService.handleOtaRequest(otaRequest);
        Assert.assertTrue(Objects.equal(result.getResult(), ResultCollection.SUCCESS.getCode()));

        when(firmwareService.buildFirmwareView(anyString(), anyString())).thenReturn(FirmwareViewDO.builder().firmwareStatus(0).newestFirmwareId("1.0").build());
        result = mqttResponseService.handleOtaRequest(otaRequest);
        Assert.assertTrue(Objects.equal(result.getResult(), ResultCollection.SUCCESS.getCode()));

        when(firmwareService.buildFirmwareView(anyString(), anyString())).thenReturn(FirmwareViewDO.builder().firmwareStatus(1).newestFirmwareId("1.0").build());
        when(firmwareService.startOtaFromDevice(any(), any(), any())).thenReturn(Result.Success());
        otaRequest.setValue(otaRequest.new OTATransportValue());
        result = mqttResponseService.handleOtaRequest(otaRequest);
        Assert.assertTrue(Objects.equal(result.getResult(), ResultCollection.SUCCESS.getCode()));

        when(firmwareService.startOtaFromDevice(any(), any(), any())).thenThrow(RuntimeException.class);
        result = mqttResponseService.handleOtaRequest(otaRequest);
        Assert.assertTrue(!Objects.equal(result.getResult(), ResultCollection.SUCCESS.getCode()));


        when(deviceSettingService.getDeviceSetting(any())).thenReturn(new DeviceAppSettingsDO() {{
            setOtaAutoUpgrade(false);
        }});
        result = mqttResponseService.handleOtaRequest(otaRequest);
        Assert.assertTrue(Objects.equal(result.getResult(), ResultCollection.SUCCESS.getCode()));

        mqttSenderField.set(vernemqPublisher, null);
        vernemqPublisher.init();
    }

    
}
