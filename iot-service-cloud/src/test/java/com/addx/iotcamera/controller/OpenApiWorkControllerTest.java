package com.addx.iotcamera.controller;

import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.service.QuestionBackService;
import org.junit.Test;

import java.util.LinkedHashMap;

public class OpenApiWorkControllerTest {

    private JwtHelper jwtHelper = new JwtHelper();

    public void createUrl(String sn, String traceId) {
        LinkedHashMap<String, Object> claims = new LinkedHashMap<>();
        final String keyPrefix = QuestionBackService.buildBackupVideoJsonKey(sn, traceId, "");
        claims.put("keyPrefix", keyPrefix);
        String token = jwtHelper.createToken(claims, 3600 * 24 * 30);
        System.out.printf("// sn=%s,traceId=%s\n\n", sn, traceId);
        System.out.printf("https://api-eu.addx.live/workTemp/getQuestionBackVideoM3u8?token=%s\n\n", token);
    }

    @Test
    public void test() {
        final String str = "" +
                "ad0f14d727e914e9c80b658951ca1d7e, 03449251670511774HZDaae0o6416\n" +
                "ee2843e370e96bb0f5d6d0b30010a6c2, 04191051670542927BuYibDf6FWTt";
        for (final String line : str.split("\n")) {
            final String[] strs = line.split(" *, *");
            createUrl(strs[0], strs[1]);
        }
    }
    @Test
    public void test2() {
        final String str = "" +
                "4221ec5b49467b3b8a70e4e268f2436c,0809371670515358KE7uMGENLgH5V";
        for (final String line : str.split("\n")) {
            final String[] strs = line.split(" *, *");
            createUrl(strs[0], strs[1]);
        }
    }
}
