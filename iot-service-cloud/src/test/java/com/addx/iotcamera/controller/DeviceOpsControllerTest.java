package com.addx.iotcamera.controller;

import com.addx.iotcamera.config.MqttConfig;
import com.addx.iotcamera.config.springbatch.reader.VideoSliceKafkaItemReader;
import com.addx.iotcamera.controller.ops.DeviceOpsController;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;

import static com.addx.iotcamera.util.Assert.AssertUtil.assertEquals;
import static org.mockito.Mockito.verify;

/**
 * description:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2023/9/21 09:54
 */
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceOpsControllerTest {
    @Mock
    private VideoSliceKafkaItemReader sliceDataReader;
    @Mock(name = "newVideoSliceKafkaItemReader")
    private VideoSliceKafkaItemReader newVideoSliceKafkaItemReader;
    @Mock
    private MqttConfig mqttConfig;
    @Mock
    private KafkaListenerEndpointRegistry endpointRegistry;
    @InjectMocks
    private DeviceOpsController myController;

    @Test
    public void testStopWithValidKey() throws MqttException {
        // Arrange
        String key = "994b360cca30a5c3a15f0d1f968b622b";

        // Act
        String result = myController.stop(key);

        // Assert
        assertEquals("success", result);
        verify(sliceDataReader).close();
        verify(mqttConfig).disconnect();
    }

    @Test
    public void testStopWithInvalidKey() throws MqttException {
        // Arrange
        String key = "invalidKey";

        // Act
        String result = myController.stop(key);

        // Assert
        assertEquals("close failed", result);
        verify(sliceDataReader, Mockito.never()).close();
        verify(mqttConfig, Mockito.never()).disconnect();
    }
}
