package com.addx.iotcamera.controller;

import com.addx.iotcamera.bean.analytics.AgreeInfo;
import com.addx.iotcamera.bean.analytics.ListUserAgreeListRequest;
import com.addx.iotcamera.bean.analytics.UpdateUserAgreeRequest;
import com.addx.iotcamera.bean.db.UserAgreeDO;
import com.addx.iotcamera.controller.analytics.AnalyticsController;
import com.addx.iotcamera.dao.user_agree.IUserAgreeDao;
import com.addx.iotcamera.dao.user_agree.IUserAgreeWaterDao;
import com.addx.iotcamera.service.user_agree.UserAgreeService;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AnalyticsControllerTest {

    @InjectMocks
    private AnalyticsController analyticsController;

    @Spy
    @InjectMocks
    private UserAgreeService userAgreeService;

    @Mock
    private IUserAgreeDao userAgreeDao;
    @Mock
    private IUserAgreeWaterDao userAgreeWaterDao;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testListUserAgreeList() throws ClientException {

        Result result = analyticsController.listUserAgreeList(1, null, null);
        Assert.assertTrue(result.getResult().intValue() == -102);


        ListUserAgreeListRequest request = new ListUserAgreeListRequest();
        result = analyticsController.listUserAgreeList(1, request, null);
        Assert.assertTrue(result.getResult().intValue() == -102);

        request.setTypeList(Arrays.asList(1));
        Mockito.when(userAgreeDao.getUserAgreeList(any(), any())).thenReturn(new ArrayList<>());
        result = analyticsController.listUserAgreeList(1, request, null);

        Assert.assertTrue(result.getResult().intValue() == 0);
    }

    @Test
    public void testUpdateUserAgree() throws ClientException {

        Result result = analyticsController.updateUserAgree(0, null, null);
        Assert.assertTrue(result.getResult().intValue() == -102);

        UpdateUserAgreeRequest request = new UpdateUserAgreeRequest();
        result = analyticsController.updateUserAgree(1, null, null);
        Assert.assertTrue(result.getResult().intValue() == -102);

        result = analyticsController.updateUserAgree(1, request, null);
        Assert.assertTrue(result.getResult().intValue() == -102);

        AgreeInfo agreeInfo = new AgreeInfo();
        request.setAgreeInfo(agreeInfo);
        result = analyticsController.updateUserAgree(1, request, null);
        Assert.assertTrue(result.getResult().intValue() == -102);


        agreeInfo.setAgreePath(1);
        agreeInfo.setIsAgree(true);
        agreeInfo.setType(1);
        agreeInfo.setVersion("0");
        request.setAgreeInfo(agreeInfo);
        Mockito.when(userAgreeDao.getUserAgreeList(any(), any())).thenReturn(new ArrayList<>());
        Mockito.when(userAgreeWaterDao.insert(any())).thenReturn(1);
        Mockito.when(userAgreeDao.insert(any())).thenReturn(1);

        result = analyticsController.updateUserAgree(1, request, null);
        Assert.assertTrue(result.getResult().intValue() == 0);

        UserAgreeDO userAgreeDO = new UserAgreeDO();
        userAgreeDO.setUserId(1);
        userAgreeDO.setUpdateTimestamp(1);
        userAgreeDO.setCreateTimestamp(1);
        userAgreeDO.setVersion("1");
        userAgreeDO.setId(1);
        Mockito.when(userAgreeDao.getUserAgreeList(any(), any())).thenReturn(Arrays.asList(userAgreeDO));
        result = analyticsController.updateUserAgree(1, request, null);
        Assert.assertTrue(result.getResult().intValue() == 0);
    }
}
