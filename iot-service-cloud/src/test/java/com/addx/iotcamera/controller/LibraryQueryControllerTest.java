package com.addx.iotcamera.controller;

import com.addx.iotcamera.bean.app.LibraryStatusTbRequest;
import com.addx.iotcamera.controller.meta.library.LibraryOperationController;
import com.addx.iotcamera.service.LibraryStatusService;
import com.addx.iotcamera.service.VideoSearchService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class LibraryQueryControllerTest {

    @InjectMocks
    @Spy
    private LibraryOperationController libraryOperationController;

    @Mock
    private LibraryStatusService libraryStatusService;

    @Mock
    private VideoSearchService videoSearchService;

    @Test
    public void test() {
        Mockito.when(libraryStatusService.updateLibraryRead(any())).thenReturn(1);
        Mockito.when(libraryStatusService.updateLibraryMarked(any())).thenReturn(1);

        Result result = libraryOperationController.updateLibraryRead(1, new LibraryStatusTbRequest() {{
        }}, null);
        Assert.assertTrue(result.getResult().intValue() == 0);

        result = libraryOperationController.updateLibraryRead(1, new LibraryStatusTbRequest() {{
            setTraceIds("trace_01");
        }}, null);
        Assert.assertTrue(result.getResult().intValue() == 0);

        result = libraryOperationController.updateLibraryRead(1, new LibraryStatusTbRequest() {{
            setTraceIdList(Collections.singletonList("trace_01"));
        }}, null);
        Assert.assertTrue(result.getResult().intValue() == 0);

        result = libraryOperationController.updateLibraryRead(1, new LibraryStatusTbRequest() {{
            setTraceIds("trace_01");
            setTraceIdList(Collections.singletonList("trace_01"));
        }}, null);
        Assert.assertTrue(result.getResult().intValue() == 0);

        result = libraryOperationController.updateLibraryMarked(1, new LibraryStatusTbRequest() {{
        }}, null);
        Assert.assertTrue(result.getResult().intValue() == 0);

        result = libraryOperationController.updateLibraryMarked(1, new LibraryStatusTbRequest() {{
            setTraceIds("trace_01");
        }}, null);
        Assert.assertTrue(result.getResult().intValue() == 0);

        result = libraryOperationController.updateLibraryMarked(1, new LibraryStatusTbRequest() {{
            setTraceIdList(Collections.singletonList("trace_01"));
        }}, null);
        Assert.assertTrue(result.getResult().intValue() == 0);

        result = libraryOperationController.updateLibraryMarked(1, new LibraryStatusTbRequest() {{
            setTraceIds("trace_01");
            setTraceIdList(Collections.singletonList("trace_01"));
        }}, null);
        Assert.assertTrue(result.getResult().intValue() == 0);
    }
}
