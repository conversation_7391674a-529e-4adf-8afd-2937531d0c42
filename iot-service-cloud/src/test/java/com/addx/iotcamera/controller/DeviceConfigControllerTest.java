package com.addx.iotcamera.controller;

import com.addx.iotcamera.bean.app.device.DeviceConfigRequest;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.controller.device.operation.DeviceConfigController;
import com.addx.iotcamera.service.DeviceAuthService;
import com.addx.iotcamera.service.DeviceConfigService;
import com.addx.iotcamera.service.device.DeviceStatusService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceConfigControllerTest {

    @InjectMocks
    @Spy
    private DeviceConfigController deviceConfigController;

    @Mock
    private DeviceAuthService deviceAuthService;

    @Mock
    private DeviceConfigService deviceConfigService;

    @Mock
    private DeviceStatusService deviceStatusService;

    @Test
    public void test() {
        when(deviceAuthService.checkAdminAccess(any(), any())).thenReturn(0);
        when(deviceConfigService.queryDoorbellConfig(any(), any())).thenReturn(new DeviceConfigRequest.DoorbellConfig());
        when(deviceStatusService.queryDeviceStatusBySerialNumber(any())).thenReturn(DeviceStatusDO.builder().build());

        Result result = deviceConfigController.queryDoorbellConfig(1, new DeviceConfigRequest().setSerialNumber("sn_01"));
        Assert.assertTrue(result.getData() != null);

        deviceConfigController.updateDoorbellConfig(1, new DeviceConfigRequest().setDoorbellConfig(null));
    }
}
