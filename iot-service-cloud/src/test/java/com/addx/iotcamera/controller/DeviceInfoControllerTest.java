package com.addx.iotcamera.controller;

import com.addx.iotcamera.bean.app.DeviceApInfoRequest;
import com.addx.iotcamera.bean.domain.device.DeviceApInfoDO;
import com.addx.iotcamera.controller.device.info.DeviceInfoController;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.VideoSearchService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeviceInfoControllerTest {

    @InjectMocks
    @Spy
    private DeviceInfoController deviceInfoController;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private VideoSearchService videoSearchService;

    @Test
    public void test() {
        Mockito.when(deviceInfoService.queryDeviceApInfo(any(), any(), any(), any())).thenReturn(new DeviceApInfoDO());

        Result result = deviceInfoController.queryDeviceApInfo(1, new DeviceApInfoRequest() {{
            setApRuleText("AX03AICCCC2232324441101");
        }}, null);
        DeviceApInfoDO deviceApInfoDO = (DeviceApInfoDO) ((Map)result.getData()).get("deviceApInfo");
        Assert.assertTrue(deviceApInfoDO.getSupportApConnect().intValue() == 0);
        Assert.assertTrue(deviceApInfoDO.getSupportApSetWifi().intValue() == 0);
        Assert.assertTrue(deviceApInfoDO.getNetworkModePosition() == null);

        result = deviceInfoController.queryDeviceApInfo(1, new DeviceApInfoRequest() {{
            setApRuleText("AX03AICCCC22323244411011");
        }}, null);
        deviceApInfoDO = (DeviceApInfoDO) ((Map)result.getData()).get("deviceApInfo");
        Assert.assertTrue(deviceApInfoDO.getSupportApConnect().intValue() == 1);
        Assert.assertTrue(deviceApInfoDO.getSupportApSetWifi().intValue() == 0);
        Assert.assertTrue(deviceApInfoDO.getNetworkModePosition() == null);

        result = deviceInfoController.queryDeviceApInfo(1, new DeviceApInfoRequest() {{
            setApRuleText("AX03AICCCC223232444110112");
        }}, null);
        deviceApInfoDO = (DeviceApInfoDO) ((Map)result.getData()).get("deviceApInfo");
        Assert.assertTrue(deviceApInfoDO.getSupportApConnect().intValue() == 1);
        Assert.assertTrue(deviceApInfoDO.getSupportApSetWifi().intValue() == 2);
        Assert.assertTrue(deviceApInfoDO.getNetworkModePosition() == null);

        result = deviceInfoController.queryDeviceApInfo(1, new DeviceApInfoRequest() {{
            setApRuleText("AX03AICCCC2232324441101123");
        }}, null);
        deviceApInfoDO = (DeviceApInfoDO) ((Map)result.getData()).get("deviceApInfo");
        Assert.assertTrue(deviceApInfoDO.getSupportApConnect().intValue() == 1);
        Assert.assertTrue(deviceApInfoDO.getSupportApSetWifi().intValue() == 2);
        Assert.assertTrue("25-26".equals(deviceApInfoDO.getNetworkModePosition()));
    }

    @Test
    public void test_greaterVersionNum(){
        Assert.assertFalse(DeviceInfoController.greaterVersionNum(null,3));
        Assert.assertFalse(DeviceInfoController.greaterVersionNum("",3));
        Assert.assertFalse(DeviceInfoController.greaterVersionNum("003",3));
        Assert.assertFalse(DeviceInfoController.greaterVersionNum("03",3));
        Assert.assertTrue(DeviceInfoController.greaterVersionNum("04",3));
    }
}
