package com.addx.iotcamera.controller;

import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.controller.xxlJob.XxlJobController;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.user.UserSafemoSendEmailService;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class XxlJobControllerTest {

    @InjectMocks
    private XxlJobController xxlJobController;

    @Spy
    @InjectMocks
    private UserSafemoSendEmailService userSafemoSendEmailService;

    @Mock
    private RedisService redisService;

    @Mock
    private IUserDAO iUserDAO;

    @Mock
    private AppAccountConfig appAccountConfig;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExecuteSendEmail4Safemo112APP() throws ClientException {
        Mockito.when(redisService.get(any())).thenReturn(null);
        Mockito.when(iUserDAO.selectByTimeStamp(anyInt())).thenReturn(new ArrayList<>());
        Mockito.when(appAccountConfig.queryEmailAccount(any())).thenReturn(null);

        xxlJobController.executeSendEmail4Safemo112APP(null);

        ArrayList<User> users = new ArrayList<>();
        User u1 = new User();
//        u1.setEmail("")
        u1.setId(1);
        User u2 = new User();
        u2.setId(2);
        u2.setEmail("2");
        Mockito.when(redisService.get(any())).thenReturn("1");

        Mockito.when(iUserDAO.selectByTimeStamp(anyInt())).thenReturn(users);
        xxlJobController.executeSendEmail4Safemo112APP(null);

    }
}
