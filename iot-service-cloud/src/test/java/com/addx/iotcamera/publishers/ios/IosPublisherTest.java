package com.addx.iotcamera.publishers.ios;

import com.addx.iotcamera.bean.apollo.AppPush;
import com.addx.iotcamera.bean.msg.ios.IosMsgEntity;
import com.addx.iotcamera.config.apollo.PushConfig;
import com.addx.iotcamera.publishers.notification.PushArgs;
import com.addx.iotcamera.publishers.notification.iOS.BaseIosNotification;
import com.addx.iotcamera.publishers.notification.iOS.IosMessage;
import com.addx.iotcamera.publishers.notification.iOS.IosPublisher;
import com.addx.iotcamera.service.ReportLogService;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.turo.pushy.apns.ApnsClient;
import com.turo.pushy.apns.ApnsPushNotification;
import com.turo.pushy.apns.PushType;
import com.turo.pushy.apns.util.concurrent.PushNotificationFuture;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.GenericFutureListener;
import io.prometheus.client.CollectorRegistry;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import javax.net.ssl.SSLException;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class IosPublisherTest {

    @Spy
    @InjectMocks
    private IosPublisher iosPublisher;

    @Mock
    private PushConfig pushConfig;

    @Mock
    private ReportLogService reportLogService;

    private ApnsClient apnsClient = Mockito.mock(ApnsClient.class);

    private Boolean pushTimeout = true;

    private Map<String, IosPublisher.MyApnsClient> clientMap = null;

    @Before
    public void init() throws Exception {
        PrometheusMetricsUtil prometheusMetricsUtil = new PrometheusMetricsUtil();
        prometheusMetricsUtil.collectorRegistry = new CollectorRegistry(true);
        prometheusMetricsUtil.initMetrics();

        when(apnsClient.sendNotification(any())).thenReturn(new PushNotificationFuture() {
            @Override
            public boolean isSuccess() {
                return false;
            }

            @Override
            public boolean isCancellable() {
                return false;
            }

            @Override
            public Throwable cause() {
                return null;
            }

            @Override
            public Future sync() throws InterruptedException {
                return null;
            }

            @Override
            public Future syncUninterruptibly() {
                return null;
            }

            @Override
            public Future await() throws InterruptedException {
                return null;
            }

            @Override
            public Future awaitUninterruptibly() {
                return null;
            }

            @Override
            public boolean await(long l, TimeUnit timeUnit) throws InterruptedException {
                return false;
            }

            @Override
            public boolean await(long l) throws InterruptedException {
                return false;
            }

            @Override
            public boolean awaitUninterruptibly(long l, TimeUnit timeUnit) {
                return false;
            }

            @Override
            public boolean awaitUninterruptibly(long l) {
                return false;
            }

            @Override
            public Object getNow() {
                return null;
            }

            @Override
            public boolean cancel(boolean b) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return false;
            }

            @Override
            public Object get() throws InterruptedException, ExecutionException {
                return null;
            }

            @Override
            public Object get(long timeout, @NotNull TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
                if (pushTimeout) {
                    pushTimeout = false;
                    throw new TimeoutException();
                }
                return null;
            }

            @Override
            public Future removeListeners(GenericFutureListener[] genericFutureListeners) {
                return null;
            }

            @Override
            public Future removeListener(GenericFutureListener genericFutureListener) {
                return this;
            }

            @Override
            public Future addListeners(GenericFutureListener[] genericFutureListeners) {
                return this;
            }

            @Override
            public Future addListener(GenericFutureListener genericFutureListener) {
                return this;
            }

            @Override
            public ApnsPushNotification getPushNotification() {
                return null;
            }
        });

        Field clientMapField = IosPublisher.class.getDeclaredField("clientMap");
        clientMapField.setAccessible(true);
        ((Map<String, IosPublisher.MyApnsClient>) clientMapField.get(iosPublisher)).put("api.development.push.apple.com@/tmp/dev/addx_ai_vicoo_voip@addx", new IosPublisher.MyApnsClient(apnsClient));
        ((Map<String, IosPublisher.MyApnsClient>) clientMapField.get(iosPublisher)).put("api.push.apple.com@/tmp/prod/addx_ai_vicoo_voip@addx", new IosPublisher.MyApnsClient(apnsClient));
        ((Map<String, IosPublisher.MyApnsClient>) clientMapField.get(iosPublisher)).put("test", new IosPublisher.MyApnsClient(Mockito.mock(ApnsClient.class)));
    }

    @Test
    public void test_voip_dev() throws Exception {
        AppPush appPush = new AppPush();
        appPush.setFolder("/tmp/dev/");
        appPush.setPassword("addx");
        appPush.setProSuffix("");
        appPush.setDevSuffix("");

        when(pushConfig.getDefaultIosPush()).thenReturn(appPush);

        PushArgs pushArgs = new PushArgs();
        pushArgs.setBundleName("addx.ai.vicoo.voip");
        pushArgs.setMsgType(201);
        pushArgs.setSerialNumber("8f855215e72db6650bc37cd3507648fc");
        pushArgs.setPushType(PushType.VOIP.getHeaderValue());
        pushArgs.setMsgToken("82f7a1e2ed5bab02d0fe7b0532ea7c271f1020b4fa811abae66060dcf366d835");
//        pushArgs.setMsgToken("587e0740c1e3ac0cf2726a793c9fa3f8ab6edd49ee134ff07240577cbafeec54");

        IosMsgEntity msgDO = new IosMsgEntity();
        msgDO.setMsgId(PhosUtils.getUTCStamp() % 10000);
        msgDO.setType(2);
        msgDO.setTraceId(UUID.randomUUID().toString());
        msgDO.setTimestamp((int) (System.currentTimeMillis() / 1000));
        msgDO.setSerialNumber(pushArgs.getSerialNumber());
        msgDO.setVoipUUID(UUID.randomUUID());
        msgDO.setVideoEvent(UUID.randomUUID().toString());
        BaseIosNotification bin = new BaseIosNotification();
        bin.setBody("this is call from test");
        bin.setTitle("hello jian heng voip");
        bin.setMutableContent(1);

        IosMessage im = new IosMessage();
        im.setTo(pushArgs.getMsgToken());
        im.setNotification(bin);
        im.setData(msgDO);

        ApnsClient apnsClient = Mockito.mock(ApnsClient.class);
        Field clientMapField = IosPublisher.class.getDeclaredField("clientMap");
        clientMapField.setAccessible(true);
        ((Map<String, IosPublisher.MyApnsClient>) clientMapField.get(iosPublisher)).put("", new IosPublisher.MyApnsClient(apnsClient));

        for (int i = 0; i <= 1; i++) {
            iosPublisher.publish(im, false, pushArgs);
        }

        Thread.sleep(2000);
    }

    @Test
    public void test_voip_prod() throws InterruptedException {
        AppPush appPush = new AppPush();
        appPush.setFolder("/tmp/prod/");
        appPush.setPassword("addx");
        appPush.setProSuffix("");
        appPush.setDevSuffix("");

        when(pushConfig.getDefaultIosPush()).thenReturn(appPush);

        PushArgs pushArgs = new PushArgs();
        pushArgs.setBundleName("addx.ai.vicoo.voip");
        pushArgs.setMsgType(202);
        pushArgs.setSerialNumber("8f855215e72db6650bc37cd3507648fc");
        pushArgs.setPushType(PushType.VOIP.getHeaderValue());
        pushArgs.setMsgToken("0aad3b2d1270a06c057de64bb549741c8aeea94347dfcd6216bdf2d14d3d044e");
//        pushArgs.setMsgToken("587e0740c1e3ac0cf2726a793c9fa3f8ab6edd49ee134ff07240577cbafeec54");

        IosMsgEntity msgDO = new IosMsgEntity();
        msgDO.setMsgId(PhosUtils.getUTCStamp() % 10000);
        msgDO.setType(2);
        msgDO.setTraceId(UUID.randomUUID().toString());
        msgDO.setTimestamp((int) (System.currentTimeMillis() / 1000));
        msgDO.setSerialNumber(pushArgs.getSerialNumber());
        msgDO.setVoipUUID(UUID.randomUUID());
        msgDO.setVideoEvent(UUID.randomUUID().toString());
        BaseIosNotification bin = new BaseIosNotification();
        bin.setBody("this is call from test");
        bin.setTitle("hello jian heng voip");
        bin.setMutableContent(1);

        IosMessage im = new IosMessage();
        im.setTo(pushArgs.getMsgToken());
        im.setNotification(bin);
        im.setData(msgDO);

        for (int i = 0; i <= 1; i++) {
            iosPublisher.publish(im, true, pushArgs);
        }

        Thread.sleep(2000);
    }

    @Test
    public void test_checkIfCertExpired() throws Exception {
        Field clientMapField = IosPublisher.class.getDeclaredField("clientMap");
        clientMapField.setAccessible(true);

        iosPublisher.checkIfCertExpired(null, apnsClient);
        ((Map<String, IosPublisher.MyApnsClient>) clientMapField.get(iosPublisher)).values().forEach(myApnsClient -> {
            Assert.assertTrue(!myApnsClient.isCertExpired());

        });

        iosPublisher.checkIfCertExpired(new SSLException(""), apnsClient);
        ((Map<String, IosPublisher.MyApnsClient>) clientMapField.get(iosPublisher)).values().forEach(myApnsClient -> {
            Assert.assertTrue(!myApnsClient.isCertExpired());
        });

        iosPublisher.checkIfCertExpired(new SSLException("certificate_expired"), apnsClient);
        ((Map<String, IosPublisher.MyApnsClient>) clientMapField.get(iosPublisher)).values().forEach(myApnsClient -> {
            if(myApnsClient.getApnsClient() == apnsClient) {
                Assert.assertTrue(myApnsClient.isCertExpired());
            } else {
                Assert.assertTrue(!myApnsClient.isCertExpired());
            }
        });
    }
}
