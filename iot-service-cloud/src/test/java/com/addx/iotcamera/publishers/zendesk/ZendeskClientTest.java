package com.addx.iotcamera.publishers.zendesk;

import com.addx.iotcamera.bean.UserBindFailFeedbackEmailDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.dao.UserBindFailFeedbackEmailDAO;
import com.addx.iotcamera.dao.dbt.DbtSafemoBindFailDAO;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.util.EmailUtils;
import com.google.api.client.util.Lists;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Field;
import java.util.List;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(EmailUtils.class)
public class ZendeskClientTest {
    @InjectMocks
    private ZendeskClient zendeskClient;

    @Mock
    private DbtSafemoBindFailDAO dbtSafemoBindFailDAO;

    @Mock
    private UserBindFailFeedbackEmailDAO userBindFailFeedbackEmailDAO;

    @Mock
    private AppAccountConfig appAccountConfig;

    @Mock
    private UserService userService;

    @SneakyThrows
    @Before
    public void beforeTest() throws NoSuchFieldException, IllegalAccessException {

        Field safemoBindFailFeedbackHtmlBodyMapField = zendeskClient.getClass().getDeclaredField("safemoBindFailFeedbackHtmlBodyMap");
        Field safemoBindFailFeedbackSubjectMapField = zendeskClient.getClass().getDeclaredField("safemoBindFailFeedbackSubjectMap");

        safemoBindFailFeedbackHtmlBodyMapField.setAccessible(true);
        safemoBindFailFeedbackSubjectMapField.setAccessible(true);

        zendeskClient.afterPropertiesSet();
    }

    @Test
    public void test_executeZendeskSafemoBindFailFeedbackEmail() throws InterruptedException {
        String queryExecutionId = "getQueryExecutionId";

        List<Integer> userIdList = Lists.newArrayList();
        User safemo = new User();
        safemo.setTenantId("safemo");
        safemo.setName("name");
        safemo.setEmail("email");

        User safemo_without_email = new User();
        safemo_without_email.setTenantId("safemo");
        safemo_without_email.setName("name");

        User vicooHome = new User();
        vicooHome.setTenantId("vicooHome");
        vicooHome.setEmail("email");

        userIdList.add(1);
        when(userService.queryUserById(1)).thenReturn(null);

        userIdList.add(2);
        when(userService.queryUserById(2)).thenReturn(safemo_without_email);

        userIdList.add(3);
        when(userService.queryUserById(3)).thenReturn(vicooHome);

        userIdList.add(4);
        when(userService.queryUserById(4)).thenReturn(safemo);
        UserBindFailFeedbackEmailDO userBindFailFeedbackEmailDO = new UserBindFailFeedbackEmailDO();
        userBindFailFeedbackEmailDO.setSendStatus(1);
        when(userBindFailFeedbackEmailDAO.queryUserBindFailFeedBackEmailByUserId(4)).thenReturn(userBindFailFeedbackEmailDO);

        userIdList.add(5);
        when(userService.queryUserById(5)).thenReturn(safemo);
        when(userBindFailFeedbackEmailDAO.queryUserBindFailFeedBackEmailByUserId(5)).thenReturn(null);

        when(dbtSafemoBindFailDAO.querySafemoBindFailUserIdList()).thenReturn(userIdList);

        EmailAccount emailAccount = new EmailAccount();
        emailAccount.setAccount("account");
        emailAccount.setPassword("password");
        when(appAccountConfig.queryEmailAccount(any())).thenReturn(emailAccount);

        when(userBindFailFeedbackEmailDAO.addUserBindFailFeedBackEmail(any())).thenReturn(1);

        PowerMockito.mockStatic(EmailUtils.class);
        zendeskClient.executeSafemoBindFailFeedbackEmail();
    }
}