package com.addx.iotcamera.publishers.googlehome;

import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.service.DeviceConfigService;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.deviceplatform.googlehome.GoogleHomeService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;

import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GoogleHomeClientTest {

    @Mock
    private KafkaProperties mockKafkaProperties;
    @Mock
    private IDeviceDAO mockDeviceDAO;
    @Mock
    private GoogleHomeService mockGoogleHomeService;
    @Mock
    private DeviceInfoService mockDeviceInfoService;
    @Mock
    private DeviceConfigService mockDeviceConfigService;
    @Mock
    private UserService mockUserService;

    @InjectMocks
    private GoogleHomeClient googleHomeClientUnderTest;

    @Test
    public void testAfterPropertiesSet() throws Exception {
        // Setup
        when(mockKafkaProperties.buildConsumerProperties()).thenReturn(new HashMap<>());

        // Run the test
        googleHomeClientUnderTest.afterPropertiesSet();

        Thread.sleep(1000);
    }

    @Test
    public void testGoogleHomeKafkaMsgListener() throws Exception {
        ConsumerRecord<String, String> record = new ConsumerRecord<>("topic", 0, 0L, "key", null);
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);

        record = new ConsumerRecord<>("topic", 0, 0L, "key", "{}");
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);

        record = new ConsumerRecord<>("topic", 0, 0L, "key", "{\"eventEnum\": \"DEVICE_ONLINE\"}");
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);

        record = new ConsumerRecord<>("topic", 0, 0L, "key", "{\"eventEnum\": \"DEVICE_OFFLINE\"}");
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);

        record = new ConsumerRecord<>("topic", 0, 0L, "key", "{\"eventEnum\": \"DEVICE_BIND\"}");
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);

        record = new ConsumerRecord<>("topic", 0, 0L, "key", "{\"eventEnum\": \"DEVICE_BIND\", \"operationId\":\"a\"}");
        when(mockDeviceDAO.selectOperation(anyString())).thenReturn(new BindOperationTb() {{
            setUserId(1);
            setSerialNumber("sn_01");
        }});

        when(mockDeviceInfoService.getSingleDevice(any(),any())).thenReturn(new DeviceDO(){{
            setLinkedPlatforms("googlehome");
        }});
        when(mockGoogleHomeService.canAccessGoogleHome(any())).thenReturn(false);
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);

        when(mockGoogleHomeService.canAccessGoogleHome(any())).thenReturn(true);
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);

        when(mockGoogleHomeService.getGoogleHomeApiKey(any())).thenReturn(null);
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);

        when(mockGoogleHomeService.getGoogleHomeApiKey(any())).thenReturn(new HashMap<String, Object>() {{
            put(GoogleHomeService.KEY_GOOGLE_HOME_GRAPH_API_KEY, "aaa");
        }});
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);
        when(mockGoogleHomeService.getGoogleHomeApiKey(any())).thenReturn(new HashMap<String, Object>() {{
            put(GoogleHomeService.KEY_GOOGLE_HOME_GRAPH_API_URL, "http://any");
        }});
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);
        when(mockGoogleHomeService.getGoogleHomeApiKey(any())).thenReturn(new HashMap<String, Object>() {{
            put(GoogleHomeService.KEY_GOOGLE_HOME_GRAPH_API_KEY, "aaa");
            put(GoogleHomeService.KEY_GOOGLE_HOME_GRAPH_API_URL, "http://any");
        }});
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);

        record = new ConsumerRecord<>("topic", 0, 0L, "key", "{\"eventEnum\": \"MOTION_PIR_DETECTED\", \"userId\": 1, \"serialNumber\": \"sn_01\"}");
        when(mockGoogleHomeService.getGoogleHomeAccessToken(any(), any())).thenReturn("mockAccessToken");
        googleHomeClientUnderTest.googleHomeKafkaMsgListener(record);
    }
}
