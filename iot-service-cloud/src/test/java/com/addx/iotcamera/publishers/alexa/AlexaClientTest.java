package com.addx.iotcamera.publishers.alexa;

import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.enums.DevicePlatformEventEnums;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.deviceplatform.alexa.AlexaService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.util.DeviceCodecUtil;
import com.addx.iotcamera.util.JsonUtil;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.context.ContextConfiguration;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.powermock.api.mockito.PowerMockito.when;

@PrepareForTest({AlexaClient.class})
@RunWith(MockitoJUnitRunner.Silent.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {AlexaClient.class})
public class AlexaClientTest {

    @InjectMocks
    @Spy
    private AlexaClient mockAlexaClient;

    @Mock
    private AlexaService alexaService;

    @Mock
    private DeviceInfoService deviceInfoService;

    @Mock
    private DeviceManualService deviceManualService;

    @Mock
    private DeviceModelService deviceModelService;

    @Mock
    private DeviceModelConfigService deviceModelConfigService;

    @Mock
    private DeviceStatusService deviceStatusService;

    @Mock
    private IDeviceDAO deviceDAO;

    @Test
    public void testAlexaKafkaMsgListener() throws Exception {
        mockAlexaClient.afterPropertiesSet();

        int mockUserId = 123;

        when(alexaService.canAccessAlexa(any())).thenReturn(true);
        when(alexaService.isUserLinked(any(), anyInt())).thenReturn(true);
        Map<String, Object> amazonTokenAndEventGatewayMap = new HashMap<>();
        amazonTokenAndEventGatewayMap.put(AlexaService.KEY_EVENT_GATEWAY_ENDPOINT, "https://localhost:8080");
        amazonTokenAndEventGatewayMap.put(AlexaService.KEY_ACCESS_TOKEN, "some_access_token");
        when(alexaService.getAmazonTokenAndEventGateway(any())).thenReturn(amazonTokenAndEventGatewayMap);
        when(deviceInfoService.getSingleDevice(any(),any())).thenReturn(DeviceDO.builder().linkedPlatforms("alexa").build());
        when(deviceManualService.getModelNoBySerialNumber(any())).thenReturn("cg1");
        when(deviceModelService.queryDeviceModelCategory(any())).thenReturn(1);
        when(deviceDAO.getAllDeviceInfo(any())).thenReturn(DeviceDO.builder().serialNumber("sn_01").modelNo("cg1").build());
        when(deviceStatusService.queryDeviceStatusBySerialNumber(any())).thenReturn(new DeviceStatusDO(){{
            setBatteryLevel(99);
        }});
        when(deviceModelConfigService.queryDeviceModelConfig(any())).thenReturn(new DeviceModel());

        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setUserId(mockUserId);
        deviceDO.setDeviceName("mock-device_name");
        deviceDO.setSerialNumber("mock-serialNumber");
        deviceDO.setMacAddress("aa:bb:cc:dd");
        deviceDO.setFirmwareId("v0.1");
        deviceDO.setModelNo("go");
        deviceDO.setDisplayModelNo("G0");
        deviceDO.setDisplayGitSha("0.1");
        deviceDO.setOnline(1);
        deviceDO.setCodec(DeviceCodecUtil.H264);
        deviceDO.setDeviceSupport(CloudDeviceSupport.builder().supportAlexaWebrtc(1).build());
        deviceDO.setLinkedPlatforms("alexa");
        when(deviceInfoService.getSingleDevice(any(),any())).thenReturn(deviceDO);

        Map<String, Object> alexaEventParamMap = new HashMap<>();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DEVICE_BIND);
        ConsumerRecord<String, String> record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DEVICE_BESHARED);
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DEVICE_UPDATE_NAME);
        alexaEventParamMap.put("newDeviceName", "newDevice");
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DEVICE_DEACTIVATE);
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DEVICE_BEUNSHARED);
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("online", true);
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DEVICE_ONLINE);
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("online", false);
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DEVICE_OFFLINE);
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.MOTION_PIR_DETECTED);
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DOORBELL_PRESS);
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        when(deviceInfoService.getSingleDevice(any(),any())).thenReturn(DeviceDO.builder().linkedPlatforms(null).build());
        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DOORBELL_PRESS);
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        when(deviceModelService.queryDeviceModelCategory(any())).thenReturn(2);
        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DEVICE_UPDATE_NAME);
        alexaEventParamMap.put("newDeviceName", "newDevice1");
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);
        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.DOORBELL_PRESS);
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        alexaEventParamMap.clear();
        alexaEventParamMap.put("userId", mockUserId);
        alexaEventParamMap.put("serialNumber", "mock-serialNumber");
        alexaEventParamMap.put("eventEnum", DevicePlatformEventEnums.LOW_BATTERY);
        record = new ConsumerRecord<>("alexa-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(alexaEventParamMap));
        mockAlexaClient.alexaKafkaMsgListener(record);

        mockAlexaClient.sendDoorbellPressReport("", "", "");

        mockAlexaClient.sendBatteryLowEvent("", "", "", 10);
    }

    @Test
    public void descriptionName() {
        mockAlexaClient.descriptionName("VicoHome");
        mockAlexaClient.descriptionName("vico");
        mockAlexaClient.descriptionName("safemo");
        mockAlexaClient.descriptionName("sapp");
        mockAlexaClient.descriptionName(null);
        mockAlexaClient.descriptionName("123");
    }

}
