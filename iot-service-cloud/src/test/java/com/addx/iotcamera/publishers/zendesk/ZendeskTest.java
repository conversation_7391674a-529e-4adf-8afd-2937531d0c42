package com.addx.iotcamera.publishers.zendesk;

import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.db.ZendeskTicketDO;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.config.app.*;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.dao.IShareDAO;
import com.addx.iotcamera.dao.IUserRoleDAO;
import com.addx.iotcamera.dynamo.dao.ZendeskTicketDao;
import com.addx.iotcamera.enums.ZendeskActionEnums;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.util.JsonUtil;
import com.google.api.client.util.Lists;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.zendesk.client.v2.Zendesk;
import org.zendesk.client.v2.model.Ticket;
import org.zendesk.client.v2.model.User;

import java.lang.reflect.Field;
import java.time.Instant;
import java.util.*;
import java.util.regex.Matcher;

import static org.addx.iot.common.constant.AppConstants.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
// @Ignore
public class ZendeskTest {

    @InjectMocks
    private ZendeskClient zendeskClient;

    @Mock
    private IDeviceDAO deviceDAO;

    @Mock
    private IShareDAO shareDAO;

    @Mock
    private ZendeskTicketDao zendeskTicketDao;

    @Mock
    private Zendesk zendesk;

    @Mock
    private IUserRoleDAO userRoleDAO;

    @Mock
    private UserService userService;

    @Mock
    private AppAccountConfig appAccountConfig;

    @Mock
    private EmailGuideConfig emailGuideConfig;

    @Mock
    private NewEmailConfig newEmailConfig;

    @Mock
    private ZendeskConfig zendeskConfig;

    @Mock
    private CopyWrite copyWrite;

    @Mock
    private TenantTierConfig tenantTierConfig;

    @Mock
    private NoSendEmailConfig noSendEmailConfig;

    @Mock
    private RedisService redisService;

    @SneakyThrows
    @Before
    public void beforeTest() throws NoSuchFieldException, IllegalAccessException {
        when(zendeskConfig.queryZendesk(any())).thenReturn(new Zendesk.Builder("").setUsername("").setToken("").setRetry(true).build());

        Field notSupportedTenantIdSetField = zendeskClient.getClass().getDeclaredField("notSupportedTenantIdSet");
        Field supportedTenantIdSetField = zendeskClient.getClass().getDeclaredField("supportedTenantIdSet");
        Field welcomeHtmlBodyMapField = zendeskClient.getClass().getDeclaredField("welcomeHtmlBodyMap");
        Field welcomeSubjectMapField = zendeskClient.getClass().getDeclaredField("welcomeSubjectMap");
        Field noBindCameraAfter48hHtmlBodyMapField = zendeskClient.getClass().getDeclaredField("noBindCameraAfter48hHtmlBodyMap");
        Field noBindCameraAfter48hSubjectMapField = zendeskClient.getClass().getDeclaredField("noBindCameraAfter48hSubjectMap");
        Field newNoBindCameraAfter48hHtmlBodyMapField = zendeskClient.getClass().getDeclaredField("newNoBindCameraAfter48hHtmlBodyMap");
        Field newNoBindCameraAfter48hSubjectMapField = zendeskClient.getClass().getDeclaredField("newNoBindCameraAfter48hSubjectMap");
        Field noBindCameraAfter24hHtmlBodyMapField = zendeskClient.getClass().getDeclaredField("noBindCameraAfter24hHtmlBodyMap");
        Field noBindCameraAfter24hSubjectMapField = zendeskClient.getClass().getDeclaredField("noBindCameraAfter24hSubjectMap");
        Field activeProfilesField = zendeskClient.getClass().getDeclaredField("activeProfiles");

        notSupportedTenantIdSetField.setAccessible(true);
        supportedTenantIdSetField.setAccessible(true);
        welcomeHtmlBodyMapField.setAccessible(true);
        welcomeSubjectMapField.setAccessible(true);
        noBindCameraAfter48hHtmlBodyMapField.setAccessible(true);
        noBindCameraAfter48hSubjectMapField.setAccessible(true);
        newNoBindCameraAfter48hHtmlBodyMapField.setAccessible(true);
        newNoBindCameraAfter48hSubjectMapField.setAccessible(true);
        noBindCameraAfter24hHtmlBodyMapField.setAccessible(true);
        noBindCameraAfter24hSubjectMapField.setAccessible(true);
        activeProfilesField.setAccessible(true);

        notSupportedTenantIdSetField.set(zendeskClient, Collections.emptySet());
        supportedTenantIdSetField.set(zendeskClient, Collections.singleton("vicoo"));
        welcomeHtmlBodyMapField.set(zendeskClient, Collections.singletonMap("en", "welcomeHtmlBody"));
        welcomeSubjectMapField.set(zendeskClient, Collections.singletonMap("en", "welcomeSubject"));
        noBindCameraAfter48hHtmlBodyMapField.set(zendeskClient, Collections.singletonMap("en", "noBindCameraAfter48hHtmlBody"));
        noBindCameraAfter48hSubjectMapField.set(zendeskClient, Collections.singletonMap("en", "noBindCameraAfter48hSubject"));
        newNoBindCameraAfter48hHtmlBodyMapField.set(zendeskClient, Collections.singletonMap("en", "newNoBindCameraAfter48hHtmlBody"));
        newNoBindCameraAfter48hSubjectMapField.set(zendeskClient, Collections.singletonMap("en", "newNoBindCameraAfter48hSubject"));
        noBindCameraAfter24hHtmlBodyMapField.set(zendeskClient, Collections.singletonMap("en", "noBindCameraAfter24hHtmlBody"));
        noBindCameraAfter24hSubjectMapField.set(zendeskClient, Collections.singletonMap("en", "noBindCameraAfter24hSubject"));
        activeProfilesField.set(zendeskClient, "stage-us");

        zendeskClient.afterPropertiesSet();

        EmailAccount emailAccount = new EmailAccount();
        emailAccount.setAccount(VICOO_SEND_EMAIL);
        emailAccount.setPassword("AddxBeijing123");
        emailAccount.setSendChannel("ali");
        when(appAccountConfig.queryEmailAccount(anyString())).thenReturn(emailAccount);

        when(tenantTierConfig.queryAppNameByTenant(any())).thenReturn("");
        when(noSendEmailConfig.getZendeskEmail()).thenReturn(Sets.newHashSet());
    }

    @Test
    public void testZendeskKafkaMsgListener() {
        int mockUserId = 123;

        when(deviceDAO.selectOperation(anyString())).thenReturn(BindOperationTb.builder().userId(mockUserId).build());
        when(deviceDAO.queryBindHistory(anyInt())).thenReturn(Collections.singletonList(BindOperationTb.builder().operationId("aaa").build()));
        when(userService.queryUserById(any())).thenReturn(new com.addx.iotcamera.bean.domain.User(){{
            setId(1);
            setEmail("<EMAIL>");
            setTenantId("vicoo");
            setLanguage("en");
            setName("tester");
            setRegistTime(Long.valueOf(Instant.now().getEpochSecond()).intValue());
        }});

        doNothing().when(zendeskTicketDao).insert(any(ZendeskTicketDO.class));
        when(zendeskTicketDao.queryByUserIdAndTicketType(anyInt(), anyString())).thenReturn(ZendeskTicketDO.builder().zdTicketId(11L).build());

        when(zendesk.createTicket(any(Ticket.class))).thenReturn(new Ticket() {{
            setId(11L);
        }});
        when(zendesk.addTagToTicket(anyInt(), anyString())).thenReturn(Collections.singletonList("tag01"));
        when(zendesk.createOrUpdateUser(any(User.class))).thenReturn(new User() {{
            setId(1L);
        }});
        when(zendesk.getTicket(anyLong())).thenReturn(new Ticket() {{
            setId(11L);
        }});
        when(zendesk.lookupUserByEmail(anyString())).thenReturn(Collections.singleton(new User(){{setId(10000L);}}));

        Map<String, Object> zendeskActionMap = new HashMap<>();
        zendeskActionMap.put("userId", mockUserId);
        zendeskActionMap.put("name", "test_user");
        zendeskActionMap.put("email", "<EMAIL>");
        zendeskActionMap.put("actionFrom", ZendeskActionEnums.USER_CREATE);
        ConsumerRecord<String, String> record = new ConsumerRecord<>("zendesk-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(zendeskActionMap));
        try{
            zendeskClient.zendeskKafkaMsgListener(record);
        }catch (Exception e){

        }


        zendeskActionMap.clear();
        zendeskActionMap.put("userId", mockUserId);
        zendeskActionMap.put("tenantId", "vicoo");
        zendeskActionMap.put("countryNo", "en");
        zendeskActionMap.put("name", "test_user");
        zendeskActionMap.put("email", "<EMAIL>");
        zendeskActionMap.put("actionFrom", ZendeskActionEnums.USER_CREATE);
        record = new ConsumerRecord<>("zendesk-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(zendeskActionMap));
        try{
            zendeskClient.zendeskKafkaMsgListener(record);
        }catch (Exception e){

        }

        zendeskActionMap.clear();

        when(copyWrite.queryValue(any(),any())).thenReturn("test");
        zendeskActionMap.put("userId", mockUserId);
        zendeskActionMap.put("email", "<EMAIL>");
        zendeskActionMap.put("tenantId", "vicoo");
        zendeskActionMap.put("actionFrom", ZendeskActionEnums.WELCOME_TICKET_CREATE);
        record = new ConsumerRecord<>("zendesk-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(zendeskActionMap));
        try{
            zendeskClient.zendeskKafkaMsgListener(record);
        }catch (Exception e){

        }

        when(zendesk.getCurrentUser()).thenReturn(new User(){{
            setId(1L);
        }});
        zendeskActionMap.clear();
        zendeskActionMap.put("userId", mockUserId);
        zendeskActionMap.put("email", "<EMAIL>");
        zendeskActionMap.put("tenantId", "vicoo");
        zendeskActionMap.put("actionFrom", ZendeskActionEnums.WELCOME_TICKET_CREATE);
        record = new ConsumerRecord<>("zendesk-mock-test", 0, 0L, String.valueOf(System.currentTimeMillis()), JsonUtil.toJson(zendeskActionMap));
        try{
            zendeskClient.zendeskKafkaMsgListener(record);
        }catch (Exception e){

        }
    }

    @Test
    public void test_sendNewUserNotBindCameraAfter48hEmail() {
        com.addx.iotcamera.bean.domain.User user = new com.addx.iotcamera.bean.domain.User();
        user.setId(1);
        user.setName("tester");
        user.setTenantId("vicoo");
        user.setLanguage("zh");
        user.setEmail("<EMAIL>");
        when(userService.queryUserById(anyInt())).thenReturn(user);

        when(noSendEmailConfig.getZendeskEmail()).thenReturn(Sets.newHashSet());

        when(userRoleDAO.queryUserRoleByUserId(anyInt())).thenReturn(null);

        List<BindOperationTb> bindOperationTbs = Collections.singletonList(BindOperationTb.builder().answered(0).build());
        when(deviceDAO.queryBindHistory(anyInt())).thenReturn(bindOperationTbs);

        EmailAccount emailAccount = new EmailAccount();
        emailAccount.setAccount("test");
        when(appAccountConfig.queryEmailAccount(any())).thenReturn(emailAccount);


        ZendeskTicketDO ticketDO = ZendeskTicketDO.builder().zdTicketId(1L).build();

        when(zendeskTicketDao.queryByUserIdAndTicketType(1,"WELCOME_TICKET")).thenReturn(ticketDO);
        {
            Set<String> zendeskEmail = new HashSet<>();
            zendeskEmail.add("vicoo");
            when(newEmailConfig.getZendeskEmail()).thenReturn(zendeskEmail);
            zendeskClient.sendNewUserNotBindCameraAfter48hEmail(1, "vicohome app");
        }
        {
            Set<String> zendeskEmail = new HashSet<>();
            when(newEmailConfig.getZendeskEmail()).thenReturn(zendeskEmail);
            zendeskClient.sendNewUserNotBindCameraAfter48hEmail(1, "vicohome app");
        }



//        when(zendeskTicketDao.queryByUserIdAndTicketType(anyInt(), any())).thenReturn(ZendeskTicketDO.builder().zdTicketId(11L).build());
//        when(zendesk.getTicket(anyLong())).thenReturn(new Ticket() {{
//            setId(11L);
//        }});
//        zendeskClient.sendNewUserNotBindCameraAfter48hEmail(1, "vicohome app");
//
//        when(zendesk.getTicket(anyLong())).thenReturn(new Ticket() {{
//            setId(11L);
//            setAssigneeId(1L);
//        }});

    }

    @Test
    public void test_sendNewUserNotBindCameraAfter24hEmail() {
        com.addx.iotcamera.bean.domain.User user = new com.addx.iotcamera.bean.domain.User();
        user.setId(1);
        user.setName("tester");
        user.setTenantId("vicoo");
        user.setLanguage("zh");
        user.setEmail("<EMAIL>");
        try{
            when(userService.queryUserById(anyInt())).thenReturn(user);
            when(userRoleDAO.queryUserRoleByUserId(anyInt())).thenReturn(null);
            when(redisService.get(any())).thenReturn("");
            zendeskClient.sendNewUserNotBindCameraAfter24hEmail(1, "vicohome app");
        }catch (Exception e){

        }
    }

    @Test
    public void test_queryAppWelComEmailBody_dzees(){
        String tenantId = "dzees";
        String expectedResult = "";
        when(copyWrite.queryValue(any(),any())).thenReturn("");
        String actualResult = copyWrite.queryValue(tenantId,APP_LANGUAGE_EN);
        Assert.assertEquals(expectedResult,actualResult);
    }

    @Test
    public void test_queryAppWelComEmailBody(){
        String tenantId = "vicoo";
        String expectedResult = "";
        when(copyWrite.queryValue(any(),any())).thenReturn("");
        String actualResult = copyWrite.queryValue(tenantId,APP_LANGUAGE_ZH);
        Assert.assertEquals(expectedResult,actualResult);
    }


    @Test
    public void testQueryAppWelComEmailBody() {
        String tenantId = TENANTID_VICOO;
        String language = APP_LANGUAGE_EN;
        String requesterName = "John Doe";
        String recipient = "<EMAIL>";
        String appName = "MyApp";

        String expectedWelcomeHtmlBody = WELCOM_BODY_VICOO.replaceAll("\\$USER_NAME", Matcher.quoteReplacement(requesterName))
                .replaceAll("\\$SUPPORT_EMAIL", Matcher.quoteReplacement(recipient))
                .replaceAll("\\$APP_NAME", Matcher.quoteReplacement(appName));
        String actualWelcomeHtmlBody = zendeskClient.queryAppWelComEmailBody(tenantId, language, requesterName, recipient, appName);

        Assert.assertEquals(expectedWelcomeHtmlBody, actualWelcomeHtmlBody);
    }

    @Test
    public void testQueryAppWelComEmailBodyNegative() {
        String tenantId = "other";
        String language = "en";
        String requesterName = "John Doe";
        String recipient = "<EMAIL>";
        String appName = "MyApp";

        String expectedWelcomeHtmlBody = "Welcome to our app!";
        when(copyWrite.queryValue(any(),any())).thenReturn(expectedWelcomeHtmlBody);

        String actualWelcomeHtmlBody = zendeskClient.queryAppWelComEmailBody(tenantId, language, requesterName, recipient, appName);

        Assert.assertEquals(expectedWelcomeHtmlBody, actualWelcomeHtmlBody);
    }

    @Test
    public void test_updateTicket(){
        Zendesk exceptionZendesk = new Zendesk
                .Builder("https://1117336245552676.us-east-1.fc.aliyuncs.com/2016-08-15/proxy/zendesk/zendesk_api_proxy")
                .setUsername("<EMAIL>")
                .setToken("krHQMo2TJxsnKFKzl8Bk3b6AeGwMWEfvcNwGXh2x")
                .setRetry(true)
                .build();
        when(zendeskConfig.queryZendesk(any())).thenReturn(exceptionZendesk);
        zendeskClient.updateTicket(null, TENANTID_VICOO);
    }

    @Test(expected = BaseException.class)
    public void test_updateUser(){
        Zendesk exceptionZendesk = new Zendesk
                .Builder("https://1117336245552676.us-east-1.fc.aliyuncs.com/2016-08-15/proxy/zendesk/zendesk_api_proxy")
                .setUsername("<EMAIL>")
                .setToken("krHQMo2TJxsnKFKzl8Bk3b6AeGwMWEfvcNwGXh2x")
                .setRetry(true)
                .build();
        when(zendeskConfig.queryZendesk(any())).thenReturn(exceptionZendesk);

        zendeskClient.updateUser(new User(), TENANTID_VICOO);
    }

    @Test(expected = NullPointerException.class)
    public void test_createTicket(){
        Zendesk exceptionZendesk = new Zendesk
                .Builder("https://1117336245552676.us-east-1.fc.aliyuncs.com/2016-08-15/proxy/zendesk/zendesk_api_proxy")
                .setUsername("<EMAIL>")
                .setToken("krHQMo2TJxsnKFKzl8Bk3b6AeGwMWEfvcNwGXh2x")
                .setRetry(true)
                .build();
        when(zendeskConfig.queryZendesk(any())).thenReturn(exceptionZendesk);

        zendeskClient.createTicket(null, TENANTID_VICOO, Lists.newArrayList(),null,null,true,null);
    }


    @Test
    public void test_getTicket(){
        Zendesk exceptionZendesk = new Zendesk
                .Builder("https://1117336245552676.us-east-1.fc.aliyuncs.com/2016-08-15/proxy/zendesk/zendesk_api_proxy")
                .setUsername("<EMAIL>")
                .setToken("krHQMo2TJxsnKFKzl8Bk3b6AeGwMWEfvcNwGXh2x")
                .setRetry(true)
                .build();
        when(zendeskConfig.queryZendesk(any())).thenReturn(exceptionZendesk);


        zendeskClient.getTicket(0L, TENANTID_VICOO);
    }

//    @Test
    public void test_updateUserTags(){
        Zendesk exceptionZendesk = new Zendesk
                .Builder("https://1117336245552676.us-east-1.fc.aliyuncs.com/2016-08-15/proxy/zendesk/zendesk_api_proxy")
                .setUsername("<EMAIL>")
                .setToken("krHQMo2TJxsnKFKzl8Bk3b6AeGwMWEfvcNwGXh2x")
                .setRetry(true)
                .build();
        when(zendeskConfig.queryZendesk(any())).thenReturn(exceptionZendesk);

        com.addx.iotcamera.bean.domain.User user = new com.addx.iotcamera.bean.domain.User();
        user.setTenantId(TENANTID_VICOO);
        user.setId(1);
        user.setName("name");

        List<String> tags = Arrays.asList("person");
        zendeskClient.updateUserTags(user,tags);
    }


    @Test
    public void test_getCurrentUser(){
        Zendesk exceptionZendesk = new Zendesk
                .Builder("https://1117336245552676.us-east-1.fc.aliyuncs.com/2016-08-15/proxy/zendesk/zendesk_api_proxy")
                .setUsername("<EMAIL>")
                .setToken("krHQMo2TJxsnKFKzl8Bk3b6AeGwMWEfvcNwGXh2x")
                .setRetry(true)
                .build();
        when(zendeskConfig.queryZendesk(any())).thenReturn(exceptionZendesk);

        zendeskClient.getCurrentUser(TENANTID_VICOO);
    }
}
