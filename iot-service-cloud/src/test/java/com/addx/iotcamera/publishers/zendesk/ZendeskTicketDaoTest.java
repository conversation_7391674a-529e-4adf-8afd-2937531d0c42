package com.addx.iotcamera.publishers.zendesk;

import com.addx.iotcamera.bean.db.ZendeskTicketDO;
import com.addx.iotcamera.dynamo.dao.ZendeskTicketDao;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

//@ActiveProfiles("test")
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = IotCameraApplication.class)
public class ZendeskTicketDaoTest {

    @Autowired
    private ZendeskTicketDao zendeskTicketDao;

//    @Test
    public void testZendeskTicketDao() {
        ZendeskTicketDO zendeskTicketDO = ZendeskTicketDO.builder().zdTicketId(1L)
                .userId(22)
                .ticketType("test_ticket")
                .createTimestamp(System.currentTimeMillis())
                .build();
        zendeskTicketDao.insert(zendeskTicketDO);

        ZendeskTicketDO dbZendeskTicketDO = zendeskTicketDao.queryByHashKey(zendeskTicketDO.getZdTicketId());
        assertNotNull(dbZendeskTicketDO);

        dbZendeskTicketDO = zendeskTicketDao.queryByUserIdAndTicketType(zendeskTicketDO.getUserId(), zendeskTicketDO.getTicketType());
        assertNotNull(dbZendeskTicketDO);

        zendeskTicketDao.addTag(zendeskTicketDO.getZdTicketId(), "tag01");
        dbZendeskTicketDO = zendeskTicketDao.queryByUserIdAndTicketType(zendeskTicketDO.getUserId(), zendeskTicketDO.getTicketType());
        assertTrue(dbZendeskTicketDO.getZdTicketTags().contains("tag01"));

        zendeskTicketDao.delete(zendeskTicketDO.getZdTicketId());
    }
}
