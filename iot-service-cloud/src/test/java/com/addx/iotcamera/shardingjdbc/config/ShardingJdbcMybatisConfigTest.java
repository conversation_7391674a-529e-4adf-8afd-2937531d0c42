package com.addx.iotcamera.shardingjdbc.config;

import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.TransactionIsolationLevel;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mybatis.spring.SqlSessionTemplate;

import javax.sql.DataSource;
import java.sql.Connection;


@RunWith(MockitoJUnitRunner.Silent.class)
public class ShardingJdbcMybatisConfigTest {

    @Test
    public void testShardingSqlTemplate() throws Exception {
        ShardingJdbcMybatisConfig shardingJdbcMybatisConfig = new ShardingJdbcMybatisConfig();

        SqlSessionTemplate sqlSessionTemplate = shardingJdbcMybatisConfig.shardingSqlSessionTemplate(null, Mockito.mock(DataSource.class));
        SqlSession sqlSession = sqlSessionTemplate.getSqlSessionFactory().openSession(Mockito.mock(Connection.class));
        Assert.assertTrue(sqlSession != null);

        ShardingJdbcMybatisConfig.transactionLevelThreadLocal.set(TransactionIsolationLevel.REPEATABLE_READ);
        sqlSession = sqlSessionTemplate.getSqlSessionFactory().openSession(Mockito.mock(Connection.class));
        Assert.assertTrue(sqlSession != null);

        sqlSession = sqlSessionTemplate.getSqlSessionFactory().openSession(TransactionIsolationLevel.READ_UNCOMMITTED);
        Assert.assertTrue(sqlSession != null);
    }
    
}
