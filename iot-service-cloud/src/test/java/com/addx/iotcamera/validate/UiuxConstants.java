package com.addx.iotcamera.validate;

import com.google.common.collect.ImmutableSet;

import java.util.Set;

public class UiuxConstants {

    // 必需的键
    public static Set<String> REQUIRED_KEYS = ImmutableSet.of(
            "tier_message_storage",
            "tier_message_name_basic",
            "tier_message_name_plus",
            "tier_message_name_pro",
            "tier_message_name_basic_plus",
            "tier_message_name_basic_pro",
            "tier_message_name_plus_pro",
            "tier_message_subhead_basic",
            "tier_message_subhead_plus",
            "tier_message_subhead_pro",
            "tier_message_unit",
            "tier_message_discount",
            "tier_message_cloud_title",
            "tier_message_cloud",
            "tier_message_notification_title",
            "tier_message_notification_describe",
            "tier_message_activity_title",
            "tier_message_activity_describe",
            "tier_message_tierDescribecloud_title",
            "tier_message_tierDescribecloud_describe",
            "tier_message_moreDetail_title",
            "oneMonth",
            "months",
            "monthly",
            "bindContactTitle",
            "bindContactContent",
            "resetTitle",
            "resetContent",
            "loginInTitle",
            "loginInContent",
            "signInTitle",
            "signInContent",
            "freeMessage",
            "currentTierMessage",
            "expirationReminderBody",
            "expirationReminderBodyToday",
            "changeReminderTitle",
            "expirationReminderTitle",
            "protectionMessage",
            "protectionMessageToday",
            "changeReminderBody",
            "changeRemindePopupMessage",
            "changeRemindePopupMessageToday",
            "expirationReminderPopupMessage",
            "expirationReminderPopupMessageToday",
            "openService",
            "MotionTitleForActivityZone",
            "commonMotionBody",
            "GuestRequestTitle",
            "InvitedNewCameraTitle",
            "InvitedNewCameraBody",
            "lowBatteryTitle",
            "lowBatteryBody",
            "vehicle.exist",
            "vehicle.pass",
            "vehicle.vehicle_enter",
            "vehicle.vehicle_out",
            "vehicle.vehicle_held_up",
            "pet.exist",
            "pet.pass",
            "pet.wandering",
            "person.exist",
            "person.pass",
            "person.wandering",
            "package.package_drop_off",
            "package.package_pick_up",
            "package.package_exist",
            "cry.cry",
            "productExplainCommon1",
            "productExplainCommon2",
            "productExplainCommon3",
            "productExplainCommonAndroid3",
            "productExplainUpgrade1",
            "productExplainUpgrade2",
            "productExplainUpgrade3",
            "productExplainUpgrade4",
            "productExplainUpgradeAndroid4",
            "productExplainSub1",
            "productExplainSub2",
            "productExplainSub6",
            "productExplainSub3",
            "productExplainSub4",
            "productExplainSub5",
            "productExplainSubAndroid5",
            "personrecognitionerror",
            "notidentifyperson",
            "packageidentificationerror",
            "notidentifypackage",
            "petidentificationerror",
            "notidentifypet",
            "vehiclerecognitionerror",
            "notidentifyvehicle",
            "videofeedbackcellother",
            "deviceDormancyToday",
            "deviceDormancyTomorrow",
            "deviceDormancyMonday",
            "deviceDormancyTuesday",
            "deviceDormancyWednesday",
            "deviceDormancyThursday",
            "deviceDormancyFriday",
            "deviceDormancySaturday",
            "deviceDormancySunday",
            "deviceDormancy",
            "deviceDefaultLocation",
            "device.name");
}
