package com.addx.iotcamera.validate;


import com.addx.iotcamera.bean.init.CopyWriteInit;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.config.apollo.*;
import com.addx.iotcamera.config.app.ProductExplanReplaceConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class ValidateUiux {

    @InjectMocks
    private CopyWriteInit copyWriteInit;
    @Mock
    private GlobalConfig globalConfig;
    @Mock
    private ProductConfig productConfig;
    @Mock
    private ProductSubConfig productSubConfig;
    @Mock
    private ProductUpgradeConfig productUpgradeConfig;
    @Mock
    private ProductExplainConfig productExplainConfig;
    @Mock
    private ProductExplanReplaceConfig productExplanReplaceConfig;
    @Mock
    private TenantTierConfig tenantTierConfig;

    @Test
    public void test_initCopyWrite() {
//        CopyWrite copyWrite = copyWriteInit.initCopyWrite();
//        /** 之前报错是运行时找不到某些key而报错，因此需要校验程序里用到的key和实际的是否一样 **/
//        Set<String> realKeys = copyWrite.getConfig().keySet();
//        List<String> lessKeys = FuncUtil.subtractToList(UiuxConstants.REQUIRED_KEYS, realKeys);
//        List<String> moreKeys = FuncUtil.subtractToList(realKeys, UiuxConstants.REQUIRED_KEYS);
//        log.info("initCopyWrite 校验完成! lessKeys={},moreKeys={}", lessKeys, moreKeys);
//        Assert.assertEquals("uiux缺少必需的键:" + lessKeys, 0, lessKeys.size());
    }


    @Test
    public void test_dataType(){
        log.info("数据类型{}",NumberUtils.isDigits("-1"));
        log.info("数据类型{}",NumberUtils.isNumber("-1"));
    }
}
