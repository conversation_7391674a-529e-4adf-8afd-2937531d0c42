package com.addx.iotcamera.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.thingmodel.ThingModel;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.LinkedList;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ThingModelTest {

    @Test
    public void test_getPropertyDefaultValue() {
        ThingModel thingModel = new ThingModel();
        thingModel.setProperties(new LinkedList<>());
        thingModel.getProperties().add(new ThingModel.ThingEntity() {{
            setIdentifier("id1");
            setType("ENUM");
            setDefaultValue("default1");
        }});
        thingModel.getProperties().add(new ThingModel.ThingEntity() {{
            setIdentifier("id2");
            setType("RANGE");
            setDefaultValue(0);
        }});
        thingModel.getProperties().add(new ThingModel.ThingEntity() {{
            setIdentifier("id3");
            setType("SWITCH");
            setDefaultValue(0);
        }});
        thingModel = JSON.parseObject(JSONObject.toJSONString(thingModel), ThingModel.class).init();

        Assert.assertNull(ThingModel.getPropertyValue(thingModel, null, "id0", String.class));
        Assert.assertNull(ThingModel.getPropertyValue(thingModel, new JSONObject(), "id0", String.class));
        {
            JSONObject jsonObj = new JSONObject();
            Assert.assertEquals("default1", ThingModel.getPropertyValue(thingModel, jsonObj, "id1", String.class));
            jsonObj.fluentPut("id1", "val1");
            Assert.assertEquals("val1", ThingModel.getPropertyValue(thingModel, jsonObj, "id1", String.class));
        }
        {
            JSONObject jsonObj = new JSONObject();
            Assert.assertEquals(Integer.valueOf(0), ThingModel.getPropertyValue(thingModel, jsonObj, "id2", Integer.class));
            jsonObj.fluentPut("id2", 99);
            Assert.assertEquals(Integer.valueOf(99), ThingModel.getPropertyValue(thingModel, jsonObj, "id2", Integer.class));
            Assert.assertEquals(Long.valueOf(99), ThingModel.getPropertyValue(thingModel, jsonObj, "id2", Long.class));
        }
        {
            JSONObject jsonObj = new JSONObject();
            Assert.assertEquals(Integer.valueOf(0), ThingModel.getPropertyValue(thingModel, jsonObj, "id3", Integer.class));
            jsonObj.fluentPut("id3", 1);
            Assert.assertEquals(Integer.valueOf(1), ThingModel.getPropertyValue(thingModel, jsonObj, "id3", Integer.class));
            Assert.assertEquals(Long.valueOf(1), ThingModel.getPropertyValue(thingModel, jsonObj, "id3", Long.class));
        }
    }

}
