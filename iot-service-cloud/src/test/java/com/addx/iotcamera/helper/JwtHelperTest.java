package com.addx.iotcamera.helper;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class JwtHelperTest {

    @InjectMocks
    JwtHelper jwtHelper;
    @Mock
    HttpServletRequest httpServletRequest;

    // JwtHelper.HEADER_STRING
    private static final String TOKEN_HEADER = "Authorization";

    @Test
    public void test_validateTokenAndGetClaims() {
        String token = "aad7f5e6c64b88d419b5b79153402d2e_1615170494_c2cf868e74ff0e1d4bdbfaddde2a9055";
        when(httpServletRequest.getHeader(TOKEN_HEADER)).thenReturn(token);

        Map<String, Object> map = jwtHelper.validateTokenAndGetClaims(httpServletRequest);
        Assert.assertEquals(0, map.size());

        map.put("userId", 3);
        token = jwtHelper.createToken(map, System.currentTimeMillis());
        when(httpServletRequest.getHeader(TOKEN_HEADER)).thenReturn(token);
        map = jwtHelper.validateTokenAndGetClaims(httpServletRequest);
        Assert.assertEquals(2, map.size());

        map.put("expiration", 1615170494L);
        token = jwtHelper.createToken(map, System.currentTimeMillis());
        when(httpServletRequest.getHeader(TOKEN_HEADER)).thenReturn(token);
        map = jwtHelper.validateTokenAndGetClaims(httpServletRequest);
        Assert.assertEquals(3, map.size());

    }

    @Test
    public void test_getLoginTokenIfOauthAccessToken() {
        String loginToken = "Bearer eyJhbGciOiJIUzUxMiJ9.eyJzZWVkIjoiMjQ2M2M2YmU4NDhjNGJkOGJiODJmY2QzZjFjZTE0MjEiLCJleHAiOjI2MjU3OTkwNjUsInVzZXJJZCI6MTMyfQ.4LwNDmAX49LiyegEo6K9iSbTRMHlZyN0oZ2mDIFRIYvsSi9iRM_1LOBoGjALx5aS4-CkonuKAPT3kiWVqCNjDg";
        String token = "Bearer eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2MjY2OTI4MjksInVzZXJfbmFtZSI6IjEzMiIsImp0aSI6IlltbHpiMjVuWDJWNVNtaGlSMk5wVDJsS1NWVjZWWGhOYVVvNUxtVjVTbnBhVjFaclNXcHZhVTFxVVRKTk1rMHlXVzFWTkU1RWFHcE9SMHByVDBkS2FVOUVTbTFaTWxGNldtcEdhbHBVUlRCTmFrVnBURU5LYkdWSVFXbFBha2t5VFdwVk0wOVVhM2RPYWxWelNXNVdlbHBZU2twYVEwazJUVlJOZVdaUkxqUk1kMDVFYlVGWU5EbE1hWGxsWjBWdk5rczVhVk5pVkZKTlNHeGFlVTR3YjFveWJVUkpSbEpKV1haelUyazVhVkpOWHpGTVQwSnZSMnBCVEhnMVlWTTBMVU5yYjI1MVMwRlFWRE5yYVZkV2NVTk9ha1JuIiwiY2xpZW50X2lkIjoidGVzdF9jbGllbnRfMCIsInNjb3BlIjpbImFsZXhhIl19.q7xGj-qviqTTUO6RL4bJ0gcHp71EkswNJjdF8NUz-esQR4qbvFww3r2hBnS8eUJIKOFKQQJNNZtOTpcLsNL9UA";
        String parsedLoginToken = jwtHelper.getLoginTokenIfOauthAccessToken(token);
        Assert.assertEquals(parsedLoginToken, loginToken);
    }
}
