package com.addx.iotcamera.helper;

import com.addx.iotcamera.bean.msg.MsgEntityBase;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.enums.PushTypeEnums;
import com.addx.iotcamera.publishers.notification.PushArgs;
import com.addx.iotcamera.publishers.notification.iOS.IosPublisher;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.ReportLogService;
import com.addx.iotcamera.service.message.FcmV1Service;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class MsgHelperTest {

    @InjectMocks
    @Spy
    private MsgHelper msgHelper;

    @Mock
    private IosPublisher iosPublisher;

    @Mock
    private ReportLogService reportLogService;

    @Mock
    private RedisService redisService;

    @Mock
    private FcmV1Service fcmV1Service;

    @Test
    public void test() {
        PushArgs pushArgs = new PushArgs();
        pushArgs.setUserId(0);
        MsgEntityBase msgEntityBase = new MsgEntityBase();

        pushArgs.setMsgType(201);
        pushArgs.setPushType(null);
        msgHelper.Send(pushArgs, msgEntityBase);
        pushArgs.setMsgType(201);
        msgEntityBase.setType(MsgType.DEVICE_CALL_MSG);
        msgHelper.Send(pushArgs, msgEntityBase);
        pushArgs.setMsgType(201);
        pushArgs.setIosVoipToken(UUID.randomUUID().toString());
        msgHelper.Send(pushArgs, msgEntityBase);
        when(redisService.setIfAbsent(any(), any(), anyLong(), any())).thenReturn(false);
        msgHelper.Send(pushArgs, msgEntityBase);
        when(redisService.setIfAbsent(any(), any(), anyLong(), any())).thenReturn(true);
        msgHelper.Send(pushArgs, msgEntityBase);

        pushArgs.setMsgType(202);
        msgEntityBase.setType(null);
        pushArgs.setIosVoipToken(null);
        msgHelper.Send(pushArgs, msgEntityBase);
        pushArgs.setMsgType(202);
        msgEntityBase.setType(MsgType.DEVICE_CALL_MSG);
        msgHelper.Send(pushArgs, msgEntityBase);
        pushArgs.setMsgType(202);
        pushArgs.setIosVoipToken(UUID.randomUUID().toString());
        msgHelper.Send(pushArgs, msgEntityBase);
        when(redisService.setIfAbsent(any(), any(), anyLong(), any())).thenReturn(false);
        msgHelper.Send(pushArgs, msgEntityBase);
        when(redisService.setIfAbsent(any(), any(), anyLong(), any())).thenReturn(true);
        msgHelper.Send(pushArgs, msgEntityBase);
    }

    @Test
    public void test_pushType_fcmV1() {
        PushArgs pushArgs = new PushArgs();
        pushArgs.setUserId(123);
        pushArgs.setMsgType(PushTypeEnums.PUSH_FCM_V1.getCode());
        MsgEntityBase msg = new MsgEntityBase();
        msgHelper.Send(pushArgs, msg);
    }
}
