package com.addx.iotcamera.helper;

import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.util.Base64;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class TrackerTokenHelperTest {

    private TrackerTokenHelper trackerTokenHelper;

    @Before
    public void before() {
        trackerTokenHelper = new TrackerTokenHelper();
        trackerTokenHelper.setTrackerTokenSecret("e+ligDUW1ST8xM8ELvip0YFSe1aOW+4ZZhn0s03/T1E=");
        trackerTokenHelper.setSafePushTokenSecret("e+ligDUW1ST8xM8ELvip0YFSe1aOW+4ZZhn0s03/T1E=");
    }

    @Test
    @SneakyThrows
    public void test_blowfish_by_bouncycastle() {
        // 添加Bouncy Castle作为安全提供者
        Security.addProvider(new BouncyCastleProvider());

        // 原始数据
        String originalText = "Hello, World!";
        System.out.println("原始数据: " + originalText);

        // 生成密钥
        byte[] keyBytes = "ThisIsASecretKey".getBytes();
        SecretKey secretKey = new SecretKeySpec(keyBytes, "Blowfish");

        // 创建加密器和解密器
        Cipher encryptCipher = Cipher.getInstance("Blowfish", "BC");
        Cipher decryptCipher = Cipher.getInstance("Blowfish", "BC");

        // 使用密钥初始化加密器和解密器
        encryptCipher.init(Cipher.ENCRYPT_MODE, secretKey);
        decryptCipher.init(Cipher.DECRYPT_MODE, secretKey);

        // 加密数据
        byte[] encryptedBytes = encryptCipher.doFinal(originalText.getBytes());
        String encryptedText = Base64.getEncoder().encodeToString(encryptedBytes);
        System.out.println("加密后数据: " + encryptedText);

        // 解密数据
        byte[] decryptedBytes = decryptCipher.doFinal(Base64.getDecoder().decode(encryptedText));
        String decryptedText = new String(decryptedBytes);
        System.out.println("解密后数据: " + decryptedText);
    }

    @Test
    public void test_blowfishHelper() {
        Security.addProvider(new BouncyCastleProvider());

        final BlowfishHelper blowfishHelper = new BlowfishHelper("ThisIsASecretKeyThisIsASecretKey".getBytes());
        final String str = "$USER_ID\n\n$CURRENT_TIMESTAMP\n\n$DEFAULT_TTL";
        final String encryptStr = blowfishHelper.encrypt(str);
        final String decryptStr = blowfishHelper.decrypt(encryptStr);
        Assert.assertEquals(str, decryptStr);

        try {
            final BlowfishHelper blowfishHelper1 = new BlowfishHelper(null);
            Assert.assertFalse(true);
        } catch (Throwable e) {
        }
        try {
            final BlowfishHelper blowfishHelper1 = new BlowfishHelper(new byte[0]);
            Assert.assertFalse(true);
        } catch (Throwable e) {
        }
        try {
            blowfishHelper.encrypt(null);
            Assert.assertFalse(true);
        } catch (Throwable e) {
        }
        try {
            blowfishHelper.decrypt("");
            Assert.assertFalse(true);
        } catch (Throwable e) {
        }
    }

    //    @Test
    @SneakyThrows
    public void generateBlowfishSecret() {
        // 添加Bouncy Castle作为安全提供者
        Security.addProvider(new BouncyCastleProvider());
        // 指定密钥长度（以比特为单位）
        int keyLength = 256; // 指定为128位长度的密钥
        // 创建KeyGenerator对象
        KeyGenerator keyGenerator = KeyGenerator.getInstance("Blowfish", "BC");
        // 设置密钥长度
        keyGenerator.init(keyLength);
        // 生成密钥
        SecretKey secretKey = keyGenerator.generateKey();
        // 获取密钥的字节数组表示
        byte[] keyBytes = secretKey.getEncoded();
        // 打印密钥的十六进制表示
        System.out.println("生成的密钥: " + Base64.getEncoder().encodeToString(keyBytes));
    }

    @Test
    public void test_generateTrackerToken() {
        final int userId = OpenApiUtil.randInt();
        final String trackerToken = trackerTokenHelper.generateTrackerToken(userId);
        final String[] content = trackerTokenHelper.parseTrackerToken(trackerToken);
        Assert.assertEquals(userId + "", content[0]);
    }

    @Test
    public void test_parseTrackerToken() {
        final String[] content = trackerTokenHelper.parseTrackerToken("VrU7QFJVn51XS1kRwHXxNKKbATU7O/RaEOEhKqhitFY=");
        log.info("content:{}", JSON.toJSONString(content));
    }

    @Test
    public void test_generateSafePushToken() {
        final int userId = OpenApiUtil.randInt();
        final String nodeName = "prod-us";
        final String safePushToken = trackerTokenHelper.generateSafePushToken(userId, nodeName);
        final String[] content = trackerTokenHelper.parseSafePushToken(safePushToken);
        Assert.assertEquals(userId + "", content[0]);
        Assert.assertEquals(nodeName, content[2]);
    }

}
