package com.addx.iotcamera.helper;

import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.service.proto_msg.ProtoMsgCtx;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RestLogHelperTest {

    @InjectMocks
    private RestLogHelper restLogHelper;

    @Mock
    private HttpServletRequest request;
    @Mock
    private ProtoMsgCtx ctx;

    @Test
    public void test_printRestApiLog() {
        when(request.getRequestURL()).thenAnswer(it -> new StringBuffer("https://guard.addx.live/x/y/z"));
        when(request.getHeader("X-Forwarded-For")).thenAnswer(it -> "127.0.0.99");
        when(request.getContentLength()).thenAnswer(it -> 9999);
        {
            when(ctx.isReqParseSuccess()).thenReturn(false);
            restLogHelper.printRestApiLog(request, ctx);
            Assert.assertNotNull(request.getRequestURL());
        }
        {
            when(ctx.isReqParseSuccess()).thenReturn(true);
            restLogHelper.printRestApiLog(request, ctx);
            Assert.assertNotNull(request.getRequestURL());
        }
        {
            when(ctx.isReqParseSuccess()).thenReturn(true);
            when(request.getAttribute(RequestAttributeKeys.USER_ID)).thenReturn(123);
            restLogHelper.printRestApiLog(request, ctx);
            Assert.assertNotNull(request.getRequestURL());
        }
        {
            when(ctx.isReqParseSuccess()).thenReturn(true);
            when(request.getAttribute(RequestAttributeKeys.USER_ID)).thenThrow(new RuntimeException(""));
            restLogHelper.printRestApiLog(request, ctx);
            Assert.assertNotNull(request.getRequestURL());
        }
    }

    @Test
    public void test_printResponseLog() {
        when(request.getRequestURL()).thenAnswer(it -> new StringBuffer("https://guard.addx.live/x/y/z"));
        when(request.getHeader("X-Forwarded-For")).thenAnswer(it -> "127.0.0.99");
        when(request.getContentLength()).thenAnswer(it -> 9999);
        {
            restLogHelper.printResponseLog(request, ctx);
            Assert.assertNotNull(request.getRequestURL());
        }
    }
}
