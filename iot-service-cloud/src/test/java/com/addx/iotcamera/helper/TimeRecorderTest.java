package com.addx.iotcamera.helper;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.addx.iotcamera.helper.TimeRecorder.buildReportLog;
import static com.addx.iotcamera.helper.TimeRecorder.videoSliceTimeRecorder;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class TimeRecorderTest {

    @Test
    public void test() throws InterruptedException {
        Thread[] threads = new Thread[3];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new Thread(() -> {
                try {
                    this.mockTask();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });
            threads[i].start();
        }
        for (int i = 0; i < threads.length; i++) {
            threads[i].join();
        }
    }

    public void mockTask() throws InterruptedException {
        Random random = new Random();
        videoSliceTimeRecorder.recordBegin("controller");
        Thread.sleep(random.nextInt(10) + 1);
        videoSliceTimeRecorder.recordBegin("service");
        Thread.sleep(random.nextInt(10) + 1);
        videoSliceTimeRecorder.recordBegin("task1");
        Thread.sleep(random.nextInt(10) + 1);
        videoSliceTimeRecorder.recordEnd("task1");
        Thread.sleep(random.nextInt(10) + 1);
        videoSliceTimeRecorder.recordBegin("task2");
        Thread.sleep(random.nextInt(10) + 1);
        videoSliceTimeRecorder.recordEnd("task2");
        Thread.sleep(random.nextInt(10) + 1);
        videoSliceTimeRecorder.recordEnd("service");
        Thread.sleep(random.nextInt(10) + 1);
        videoSliceTimeRecorder.recordBegin("task3");
        Thread.sleep(random.nextInt(10) + 1);
        videoSliceTimeRecorder.recordEnd("task3");
        Thread.sleep(random.nextInt(10) + 1);
        videoSliceTimeRecorder.recordEnd("controller");
        LinkedHashMap<String, TimeRecorder.Times> key2Times = videoSliceTimeRecorder.clearKey2Times();
        log.info(buildReportLog(videoSliceTimeRecorder.getName(), key2Times));
    }
}
