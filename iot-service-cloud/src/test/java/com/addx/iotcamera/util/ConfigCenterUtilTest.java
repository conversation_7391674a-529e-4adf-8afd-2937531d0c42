package com.addx.iotcamera.util;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.config.app.AppCopyrightConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ConfigCenterUtilTest {
    @InjectMocks
    private ConfigCenterUtil configCenterUtil;

    @Mock
    private AppCopyrightConfig appCopyrightConfig;


    @Test
    @DisplayName("未配置copywrite")
    public void query_copywrite(){

        when(appCopyrightConfig.queryCopyrite(any(),any())).thenReturn("c");

        AppInfo appInfo = new AppInfo();
        appInfo.setAppType("iOS");

        String expectedResult = "c";
        String actualResult = configCenterUtil.queryCopyRight(TENANTID_VICOO,appInfo);

        Assert.assertEquals(expectedResult,actualResult);
    }
}
