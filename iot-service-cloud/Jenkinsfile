node('cn-public-jenkins-agent') {
  stage('SCM') {
    checkout scm
  }
  stage('SonarQube Analysis') {
    withSonarQubeEnv() {
      sh "mvn clean verify"
      sh "export JAVA_HOME=/data/apps/jdk-17.0.12;export PATH=$JAVA_HOME/bin:$PATH;mvn sonar:sonar -Dsonar.projectKey=CLOUD_iot-service-old_89c8ba9c-7fab-41b3-bea2-b8c8c7a18e59 -Dsonar.projectName='iot-service-old' -Dsonar.coverage.jacoco.xmlReportPaths=target/jacoco-ut/jacoco.xml"
    }
  }
}
