syntax = "proto2";

package tutorial;

option java_multiple_files = true;
option java_package = "org.addx.iot.common.proto.deviceMsg";
option java_outer_classname = "PbDetectPirResultResponseProto";

// h2接口：/deviceMsg/detectPirResultResponse
message PbDetectPirResultResponse {

  // path=["detectPirResultResponse","result"] , path[2]=result , types=["java.lang.Integer"] , values=["0"]
  optional int32 result = 1;
  // path=["detectPirResultResponse","data"] , path[2]=data
  optional PbData data = 2;
  // path=["detectPirResultResponse","msg"] , path[2]=msg , types=["java.lang.String"] , values=["Success"]
  optional string msg = 3;

  message PbData {


  }

}
