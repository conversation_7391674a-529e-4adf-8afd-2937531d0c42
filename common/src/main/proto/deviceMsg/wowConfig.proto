syntax = "proto2";

package tutorial;

option java_multiple_files = true;
option java_package = "org.addx.iot.common.proto.deviceMsg";
option java_outer_classname = "PbWowConfigProto";

// h2接口：/deviceMsg/wowConfig
message PbWowConfig {

  // path=["wowConfig","name"] , path[2]=name , types=["java.lang.String"] , values=["wowConfig"]
  optional string name = 1;
  // path=["wowConfig","time"] , path[2]=time , types=["java.lang.Integer"] , values=["1732799005","1732796636","1732798215","1732797490","1732797681","1732797074","1732799492","1732798190","1732801893","1732801870"]
  optional int32 time = 2;
  // path=["wowConfig","id"] , path[2]=id , types=["java.lang.Integer"] , values=["1","2","3","929","908","890","891","892","950","895"]
  optional int32 id = 3;

}
