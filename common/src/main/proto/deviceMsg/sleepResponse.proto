syntax = "proto2";

package tutorial;

option java_multiple_files = true;
option java_package = "org.addx.iot.common.proto.deviceMsg";
option java_outer_classname = "PbSleepResponseProto";

// h2接口：/deviceMsg/sleepResponse
message PbSleepResponse {

  // path=["sleepResponse","data"] , path[2]=data
  optional PbData data = 1;
  // path=["sleepResponse","msg"] , path[2]=msg , types=["java.lang.String"] , values=["Success"]
  optional string msg = 2;
  // path=["sleepResponse","result"] , path[2]=result , types=["java.lang.Integer"] , values=["0"]
  optional int32 result = 3;

  message PbData {


  }

}
