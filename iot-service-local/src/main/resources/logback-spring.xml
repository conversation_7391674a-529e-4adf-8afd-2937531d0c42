<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">

    <contextName>logback</contextName>

    <!-- 只在指定环境进行脱敏 -->
    <springProfile name="test,local">
        <!-- 指定脱敏类的位置 -->
        <conversionRule conversionWord="msg" converterClass="com.addx.iotcamera.helper.log.SensitiveInfoConverter"/>
    </springProfile>

    <!--输出到控制台-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
        <encoder class="com.addx.tracking.config.logmasker.LogMaskerEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} trace_id=%X{trace_id} span_id=%X{span_id} [%thread] %-5level %logger{50} - %X{requestId} %X{clientId} %msg%n</pattern>
            <charset>UTF-8</charset> <!-- 设置字符集 -->
        </encoder>
    </appender>

    <appender name="OpenTelemetry" class="io.opentelemetry.instrumentation.logback.mdc.v1_0.OpenTelemetryAppender">
        <appender-ref ref="CONSOLE"/>
    </appender>


    <logger name="org.apache.kafka" level="info"/>
    <logger name="io.lettuce" level="info"/>
    <logger name="org.springframework" level="info"/>
    <logger name="org.mybatis" level="info"/>
    <logger name="org.apache.ibatis" level="info"/>

    <!--root节点是必选节点，用来指定最基础的日志输出级别，只有一个level属性-->
    <!--level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，-->
    <!--不能设置为INHERITED或者同义词NULL。默认是DEBUG-->
    <!--可以包含零个或多个元素，标识这个appender将会添加到这个logger。-->
    <root level="info">
        <appender-ref ref="OpenTelemetry"/>
<!--        <appender-ref ref="DEBUG_FILE"/>-->
<!--        <appender-ref ref="INFO_FILE"/>-->
<!--        <appender-ref ref="WARN_FILE"/>-->
<!--        <appender-ref ref="ERROR_FILE"/>-->
    </root>

</configuration>