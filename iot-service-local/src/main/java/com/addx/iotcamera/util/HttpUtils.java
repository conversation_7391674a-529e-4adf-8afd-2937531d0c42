package com.addx.iotcamera.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import org.addx.iot.common.vo.Result;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.async.methods.SimpleHttpRequest;
import org.apache.hc.client5.http.async.methods.SimpleHttpResponse;
import org.apache.hc.client5.http.auth.UsernamePasswordCredentials;
import org.apache.hc.client5.http.impl.DefaultHttpRequestRetryStrategy;
import org.apache.hc.client5.http.impl.async.CloseableHttpAsyncClient;
import org.apache.hc.client5.http.impl.async.HttpAsyncClients;
import org.apache.hc.client5.http.impl.auth.BasicScheme;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.io.HttpClientConnectionManager;
import org.apache.hc.client5.http.protocol.HttpClientContext;
import org.apache.hc.client5.http.ssl.ClientTlsStrategyBuilder;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactoryBuilder;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.concurrent.FutureCallback;
import org.apache.hc.core5.http.*;
import org.apache.hc.core5.http.protocol.HttpContext;
import org.apache.hc.core5.http.ssl.TLS;
import org.apache.hc.core5.http2.impl.nio.ProtocolNegotiationException;
import org.apache.hc.core5.reactor.IOReactorStatus;
import org.apache.hc.core5.ssl.SSLContexts;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.web.client.HttpServerErrorException;

import javax.net.ssl.*;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

public class HttpUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtils.class);

    /**
     * 初始化http2 client
     */
    private static volatile List<CloseableHttpAsyncClient> asyncHttp2ClientList = new ArrayList<>(2);
    static {
        for (int i = 0; i < 2; i++) {
            asyncHttp2ClientList.add(initHttp2Client());
        }
    }
    
    public static byte[] SimpleDownload(String downloadUrl) throws IOException {
        URL url = new URL(downloadUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        InputStream inputStream = conn.getInputStream();

        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        if (inputStream != null) {
            inputStream.close();
        }
        return bos.toByteArray();
    }

    public static String httpPutPojo(Object pojo, String url) {
        return httpPojo(pojo, new HttpPut(url));
    }

    public static String httpPostPojo(Object pojo, String url) {
        return httpPojo(pojo, new HttpPost(url));
    }

    public static String httpPojo(Object pojo, HttpEntityEnclosingRequestBase post) {
        URI url = post.getURI();
        String message = "Unkown Error";
        Integer result = -1;
        Object data = null;
        long startTimestamp = System.currentTimeMillis();
        try {
            HttpClient httpClient = new DefaultHttpClient();
            //构造消息头
            post.setHeader("Content-type", "application/json; charset=utf-8");
            post.setHeader("Connection", "Close");

            // 构建消息实体
            if (pojo != null) {
                String jsonStr = JSON.toJSONString(pojo);
                StringEntity entity = new StringEntity(jsonStr);
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                post.setEntity(entity);
            }

            HttpResponse response = httpClient.execute(post);
            int statusCode = response.getStatusLine().getStatusCode();
            String body = HttpStatus.SC_OK == statusCode ? EntityUtils.toString(response.getEntity()) : "";
            LOGGER.info("httpPostPojo url:{} statusCode:{} result:{}", url, statusCode, body);
            return body;

        } catch (Exception ex) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "httpPostPojo url:{} Error: ", url, ex);
            return "";
        }
    }

    public static String httpGet(String url, Map<String, String> map) {
        long startTimestamp = System.currentTimeMillis();
        try {
            StringBuilder urlSb = new StringBuilder(url);
            int i = 0;
            for (String key : map.keySet()) {
                if (i != 0) {
                    urlSb.append("&");
                }
                i++;
                urlSb.append(key).append("=").append(map.get(key));
            }

            url = urlSb.toString();
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(5000).setConnectionRequestTimeout(5000)
                    .setSocketTimeout(5000).build();

            httpGet.setConfig(requestConfig);


            // 构造消息头
            httpGet.setHeader("Content-type", "application/json; charset=utf-8");
            httpGet.setHeader("Connection", "Close");

            // 构建消息实体
            HttpResponse response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            String result = EntityUtils.toString(response.getEntity());
//            LOGGER.info("httpGet url:{},result:{}", url, result);

            if (HttpStatus.SC_OK == statusCode) {
                return result;
            } else {
                return "";
            }
        } catch (Exception ex) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "HTTP Post Error: ", ex);
            return "";
        }
    }

    public static String httpsPost(String receipt, String url) {
        long startTimestamp = System.currentTimeMillis();
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, new TrustManager[]{new TrustAnyTrustManager()}, new java.security.SecureRandom());
            URL console = new URL(url);
            HttpsURLConnection conn = (HttpsURLConnection) console.openConnection();
            conn.setSSLSocketFactory(sc.getSocketFactory());
            conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestProperty("Proxy-Connection", "Keep-Alive");
            conn.setDoInput(true);
            conn.setDoOutput(true);
            BufferedOutputStream hurlBufOus = new BufferedOutputStream(conn.getOutputStream());
            //拼成固定的格式传给平台
            hurlBufOus.write(receipt.getBytes());
            hurlBufOus.flush();
            InputStream is = conn.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(is));
            String line = null;
            StringBuffer sb = new StringBuffer();
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "https post error:{}", e);
        }
        return null;
    }


    private static class TrustAnyTrustManager implements X509TrustManager {

        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }

    private static class TrustAnyHostnameVerifier implements HostnameVerifier {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }

    /**
     * 初始化参数
     *
     * @param requestParams
     * @return
     */
    public static Map<String, String> initParams(Map<String, String[]> requestParams) {
        Map<String, String> params = Maps.newHashMap();
        for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用
            params.put(name, valueStr);
        }
        return params;
    }

    public static Result<String> httpRequestWithBaseAuth(ClassicHttpRequest httpRequest, String accountSid, String authToken) {
        URI uri;
        long startTimestamp = System.currentTimeMillis();
        try {
            uri = httpRequest.getUri();
        } catch (URISyntaxException e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "httpRequestWithBaseAuth error! httpRequest={}", httpRequest, e);
            return Result.Failure("httpRequestWithBaseAuth uri语法错误！");
        }
        LOGGER.info("httpRequestWithBaseAuth begin! uri={}", uri);
        HttpClientContext localContext = createLocalContext(uri, accountSid, authToken);
        try (org.apache.hc.client5.http.impl.classic.CloseableHttpClient httpclient = org.apache.hc.client5.http.impl.classic.HttpClients.custom()
                .setConnectionManager(isTls(uri.getScheme()) ? createConnectionManager(uri.getHost()) : null)
                .build();
        ) {
            CloseableHttpResponse resp = httpclient.execute(httpRequest, localContext);
            String respBody = IOUtils.toString(resp.getEntity().getContent());
            LOGGER.info("httpRequestWithBaseAuth end! uri={},code={},body={}", uri, resp.getCode(), respBody);
            if (resp.getCode() == org.apache.hc.core5.http.HttpStatus.SC_OK || resp.getCode() == org.apache.hc.core5.http.HttpStatus.SC_CREATED) {
                return new Result(respBody);
            }
            return Result.Error(resp.getCode(), "httpRequestWithBaseAuth 请求失败！body=" + respBody);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "httpRequestWithBaseAuth error! uri={}", uri, e);
            return Result.Failure("httpRequestWithBaseAuth 请求发生异常！");
        }
    }

    public static boolean isTls(String schema) {
        return "https".equalsIgnoreCase(schema);
    }

    public static HttpClientContext createLocalContext(URI uri, String userName, String password) {
        int port = uri.getPort() > 0 ? uri.getPort() : (isTls(uri.getScheme()) ? 443 : 80);
        // Generate Basic scheme object and add it to the local auth cache
        final HttpHost target = new HttpHost(uri.getScheme(), uri.getHost(), port);
        // Generate Basic scheme object and add it to the local auth cache
        final BasicScheme basicAuth = new BasicScheme();
        basicAuth.initPreemptive(new UsernamePasswordCredentials(userName, password.toCharArray()));
        // Add AuthCache to the execution context
        final HttpClientContext localContext = HttpClientContext.create();
        localContext.resetAuthExchange(target, basicAuth);
        return localContext;
    }

    public static HttpClientConnectionManager createConnectionManager(String host)
            throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        // Trust standard CA and those trusted by our custom strategy
        final SSLContext sslcontext = SSLContexts.custom()
                .loadTrustMaterial((chain, authType) -> {
                    // CN=*.twilio.com, O="Twilio, Inc.", L=San Francisco, ST=California, C=US
//                    String name = chain[0].getSubjectDN().getName();
                    return true;
                })
                .build();
        // Allow TLSv1.2 protocol only
        final org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory sslSocketFactory = SSLConnectionSocketFactoryBuilder.create()
                .setSslContext(sslcontext)
                .setTlsVersions(TLS.V_1_2)
                .build();
        final HttpClientConnectionManager cm = PoolingHttpClientConnectionManagerBuilder.create()
                .setSSLSocketFactory(sslSocketFactory)
                .build();
        return cm;
    }

    /*
     * 使用http2.0进行请求
     */
    public static String http2RequestWithPool(String method, String url, Map<String, String> headers, String receipt) {
        if(CollectionUtils.isEmpty(asyncHttp2ClientList)) {
            return null;
        }
        String requestId = MDC.get("requestId");
        long startTimestamp = System.currentTimeMillis();
        URI uri = null;
        AtomicReference<String> responseAtom = new AtomicReference<>();
        try {
            Integer randClientIndex = ((int)(Math.random() * asyncHttp2ClientList.size())) % asyncHttp2ClientList.size();
            CloseableHttpAsyncClient client = asyncHttp2ClientList.get(randClientIndex);
            if (client.getStatus() != IOReactorStatus.ACTIVE) {
                synchronized(client) {
                    if(asyncHttp2ClientList.get(randClientIndex) == client) {
                        asyncHttp2ClientList.set(randClientIndex, initHttp2Client());
                        try {
                            client.close();
                        } catch (Exception e) {
                        }
                    }
                    client = asyncHttp2ClientList.get(randClientIndex);
                }
            }
            uri = new URI(url);
            SimpleHttpRequest httpRequest = new SimpleHttpRequest(Method.normalizedValueOf(method), uri);
            ContentType contentType = ContentType.APPLICATION_JSON;
            if(MapUtils.isNotEmpty(headers)) {
                for(Map.Entry<String, String> headerEntry : headers.entrySet()) {
                    httpRequest.setHeader(headerEntry.getKey(), headerEntry.getValue());
                    if(headerEntry.getKey().equalsIgnoreCase("content-type")) {
                        contentType = ContentType.create(headerEntry.getValue(), StandardCharsets.UTF_8);
                    }
                }
            }
            if(StringUtils.isNotEmpty(receipt)) {
                httpRequest.setBody(receipt, contentType);
            }

            Future<SimpleHttpResponse> future = client.execute(httpRequest, new FutureCallback<SimpleHttpResponse>() {
                @Override
                public void completed(SimpleHttpResponse result) {
                    if(StringUtils.isNotEmpty(requestId)) {
                        MDC.put("requestId", requestId);
                    }
                    String responseStr = result.getBodyText();
                    responseAtom.set(responseStr);
                    if (result.getCode() >= 400) {
                        throw new HttpServerErrorException(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR, responseStr);
                    }
                }

                @Override
                public void failed(Exception ex) {
                    if(StringUtils.isNotEmpty(requestId)) {
                        MDC.put("requestId", requestId);
                    }
                    throw new HttpServerErrorException(org.springframework.http.HttpStatus.BAD_REQUEST, ex.getMessage());
                }

                @Override
                public void cancelled() {     
                    if(StringUtils.isNotEmpty(requestId)) {
                        MDC.put("requestId", requestId);
                    }
                    LOGGER.info("request cancelled {}", url);               
                }
            });
            try{
                future.get(5, TimeUnit.SECONDS);
            }catch(Exception e) {
            }
        } catch (Exception e) {
            LOGGER.error("http2PostWithPool error! uri={}", uri !=null ? uri.getHost() : null, e);
        }
        return responseAtom.get();
    }

    static CloseableHttpAsyncClient initHttp2Client() {
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom().loadTrustMaterial(TrustAllStrategy.INSTANCE).build();
        } catch (Exception e) {
            sslContext = SSLContexts.createDefault();
        }

        CloseableHttpAsyncClient asyncHttpClient = HttpAsyncClients.customHttp2()
                .setTlsStrategy(ClientTlsStrategyBuilder.create()
                        .setTlsVersions(TLS.V_1_0, TLS.V_1_1, TLS.V_1_2, TLS.V_1_3)
                        .setSslContext(sslContext)
                        .build())
                .setRetryStrategy(new DefaultHttpRequestRetryStrategy() {
                    @Override
                    public boolean retryRequest(HttpRequest request, IOException exception, int execCount,
                            HttpContext context) {
                        return exception instanceof ProtocolNegotiationException;
                    }

                    @Override
                    public boolean retryRequest(org.apache.hc.core5.http.HttpResponse response, int execCount,
                            HttpContext context) {
                        return false;
                    }
                })
                .setDefaultRequestConfig(org.apache.hc.client5.http.config.RequestConfig.custom()
                        .setConnectionRequestTimeout(Timeout.ofSeconds(5))
                        .setConnectTimeout(Timeout.ofSeconds(600))
                        .setConnectionKeepAlive(TimeValue.ofSeconds(3600))
                        .setResponseTimeout(Timeout.ofSeconds(5)).setContentCompressionEnabled(false).build())
                .evictIdleConnections(TimeValue.ofSeconds(3600))
                .build();
        asyncHttpClient.start();
        return asyncHttpClient;
    }

    public static String getRemoteAddr(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (StringUtils.isBlank(ipAddress) || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        return ipAddress;
    }
}
