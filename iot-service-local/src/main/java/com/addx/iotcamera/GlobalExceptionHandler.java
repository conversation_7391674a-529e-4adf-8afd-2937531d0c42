package com.addx.iotcamera;

import com.addx.iotcamera.bean.exception.ParamException;
import com.addx.iotcamera.bean.exception.PayNotifyException;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.event.BugsnagManager;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.util.RequestUtil;
import com.alibaba.fastjson.JSON;
import org.addx.iot.common.exception.BaseException;
import org.addx.iot.common.vo.Result;
import org.apache.catalina.connector.ClientAbortException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanCreationNotAllowedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

import static org.addx.iot.common.enums.ResultCollection.FATAL_ERROR;
import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;

/**
 * <AUTHOR>
 */
@ControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    @Autowired
    private BugsnagManager bugsnagManager;

    /**
     * 处理 自定义异常 的异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = ParamException.class)
    @ResponseBody
    public Result exceptionHandler(HttpServletRequest req, ParamException e) {
        logger.info("Global catch : INVALID_PARAMS: " + e.getMessage(), e);
        return Result.Error(e.getErrorCode(), e.getErrorMsg());
    }

    /**
     * 处理 MQTT_ERROR 的异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = IdNotSetException.class)
    @ResponseBody
    public Result exceptionHandler(HttpServletRequest req, IdNotSetException e) {
        logger.info("Global catch : IdNotSetException: " + e.getMessage(), e);
        return Result.Error(-7777, "ID_NOT_SET: " + e.getMessage());
    }

    /**
     * 处理 支付订阅通知 的异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = PayNotifyException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public Result exceptionHandler(HttpServletRequest req, PayNotifyException e) {
        logger.info("Global catch : PayNotifyException: {}", e);
        return Result.Error(e.getErrorCode(), "订阅通知验证失败: " + e.getErrorMsg());
    }

    /**
     * 执行时抛出异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = BaseException.class)
    @ResponseBody
    public Result baseException(HttpServletRequest req, BaseException e) {
        logger.info("Global catch : BaseException: " + e.getMessage(), e);
        return Result.Error(e.getErrorCode(), e.getErrorMsg());
    }

    /**
     * 接口参数校验异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public Object validExceptionHandler(MethodArgumentNotValidException e) {
        FieldError fieldError = e.getBindingResult().getFieldError();
        logger.info("Global catch : MethodArgumentNotValidException: " + e.getMessage(), e);
        assert fieldError != null;
        return Result.Error(INVALID_PARAMS, fieldError.getDefaultMessage());
    }

    /**
     * 处理空指针异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = NullPointerException.class)
    @ResponseBody
    public Result nullPointerExceptionHandler(HttpServletRequest req, Exception e) {
        com.addx.iotcamera.util.LogUtil.error(logger, "Global catch :nullPointerExceptionHandler！原因是: ", e);
        bugsnagManager.notify(e);
        return Result.Error(INVALID_PARAMS, "NullPointerException Error");
    }


    /**
     * 参数异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    @ResponseBody
    public Result httpMessageNotReadableExceptionHandler(HttpServletRequest req, Exception e) {
        com.addx.iotcamera.util.LogUtil.error(logger, "http消息不可读 method={},url={},headers={}", req.getMethod(), req.getRequestURL(), JSON.toJSONString(RequestUtil.getHeaderMap(req)), e);
        return Result.Error(INVALID_PARAMS, "参数错误");
    }

    /**
     * 全局处理http请求参数错误异常
     */
    @ExceptionHandler(value = ServletRequestBindingException.class)
    @ResponseBody
    public Result servletRequestBindingExceptionHandler(HttpServletRequest req, ServletRequestBindingException e) {
        /*
        有些controller方法配置在登录验证白名单中，其实需要token解析后端userId，token失效会抛异常，产生下列返回体
        {"result":-102,"msg":"http请求参数错误:Missing request attribute 'userId' of type Integer","data":{}}
        /account/**下的白名单已经精确配置了，为了防止还有遗漏，拦截一下找不到userId的异常
         */
        if (e instanceof MissingServletRequestParameterException) {
            MissingServletRequestParameterException missParamException = (MissingServletRequestParameterException) e;
            if (RequestAttributeKeys.USER_ID.equals(missParamException.getParameterName())) {
                return Result.Error(-1029, "INVALID_TOKEN");
            }
        }
        return Result.Error(INVALID_PARAMS, "http请求参数错误:" + e.getMessage());
    }

    /**
     * 程序关闭时单例销毁了，对系统没有影响
     */
    @ExceptionHandler(value = BeanCreationNotAllowedException.class)
    @ResponseBody
    public Result beanCreationNotAllowedExceptionHandler(HttpServletRequest req, BeanCreationNotAllowedException e) {
        return Result.Error(FATAL_ERROR, "系统维护中");
    }

    /**
     * 客户端断开了连接，对response.outputStream写数据抛异常
     */
    @ExceptionHandler(value = ClientAbortException.class)
    public void clientAbortExceptionHandler(HttpServletRequest req, ClientAbortException e) {
        // 返回值没有意义，直接忽略这个异常
    }

    /**
     * 处理其他异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Result exceptionHandler(HttpServletRequest req, Exception e) {
        String message = Optional.ofNullable(e).map(Exception::getMessage).orElse("");
        if (message.contains("Invalid UTF-8 start byte 0xff")) {
            // pass
        } else if (message.startsWith("UserMethodArgumentResolver Authorization null")) {
            return Result.Error(INVALID_PARAMS, "缺少http请求头:Authorization");
        } else {
            com.addx.iotcamera.util.LogUtil.error(logger, "Global catch :未知异常！原因是: ", e);
        }
        bugsnagManager.notify(e);
        return Result.Error(FATAL_ERROR, "Unknown Error");
    }
}
