package com.addx.iotcamera.service.device;

import com.addx.domain.settings.device.DeviceAttributeSubDomain;
import com.addx.domain.settings.device.model.DeviceAttribute;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.device.CoolDownDO;
import com.addx.iotcamera.bean.device.DeviceDormancyStatus;
import com.addx.iotcamera.bean.device.attributes.*;
import com.addx.iotcamera.bean.device.model.DeviceModelIconDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.DeviceOTADO;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.bean.domain.device.SdCard;
import com.addx.iotcamera.bean.domain.report.ReportInfo;
import com.addx.iotcamera.config.device.DeviceSettingConfig;
import com.addx.iotcamera.config.device.DoorBellRingConfig;
import com.addx.iotcamera.config.device.FreeUserVipTier2Config;
import com.addx.iotcamera.constants.DeviceAttributeName;
import com.addx.iotcamera.enums.DeviceNetType;
import com.addx.iotcamera.enums.DoorbellPressNotifyType;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.enums.WhiteLightEnums;
import com.addx.iotcamera.helper.MyLockRegistry;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.alexa.AlexaSafemoRequestService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.statemachine.StateMachineService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.util.FuncUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.thingmodel.ThingModel;
import org.addx.iot.common.thingmodel.ThingModelConfig;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.LocalDeviceSupport;
import org.addx.iot.domain.config.model.DeviceAttributeIntRange;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import static com.addx.iotcamera.bean.device.attributes.OptionEnumMapping.*;
import static com.addx.iotcamera.constants.DeviceAttributeName.*;
import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;
import static com.addx.iotcamera.constants.DeviceModelSettingConstants.*;
import static com.addx.iotcamera.controller.device.operation.DeviceUserConfigController.redisLockTimeout;
import static com.addx.iotcamera.controller.device.operation.DeviceUserConfigController.userConfigPre;
import static com.addx.iotcamera.enums.device.DeviceModelCategoryEnums.BASE_STATION;
import static com.addx.iotcamera.service.device.DeviceEnumMappingService.oldEnumName2Value;

@Slf4j
@Component @Lazy
public class DeviceAttributeService implements DeviceAttributeSubDomain {
    @Autowired @Lazy
    private DoorBellRingConfig doorBellRingConfig;
    @Autowired @Lazy
    private S3Service s3Service;
    @Autowired @Lazy
    private DeviceManualService deviceManualService;
    @Autowired @Lazy
    private DeviceModelIconService deviceModelIconService;
    @Autowired @Lazy
    private DeviceInfoService deviceInfoService;
    @Resource @Lazy
    private DeviceSettingConfig deviceSettingConfig;
    @Autowired @Lazy
    private DeviceModelService deviceModelService;
    @Autowired @Lazy
    private DeviceModelConfigService deviceModelConfigService;
    @Autowired @Lazy
    private DeviceOTAService deviceOTAService;
    @Autowired @Lazy
    private FirmwareService firmwareService;
    @Autowired @Lazy
    private DeviceSdCardStatusService deviceSdCardStatusService;
    @Autowired @Lazy
    private DeviceService deviceService;
    @Autowired @Lazy
    private DeviceStatusService deviceStatusService;
    @Autowired @Lazy
    private DeviceSettingService deviceSettingService;
    @Autowired @Lazy
    private StateMachineService stateMachineService;
    @Autowired @Lazy
    private DeviceDormancyPlanService deviceDormancyPlanService;
    @Autowired @Lazy
    private LocationInfoService locationInfoService;
    @Autowired @Lazy
    private RoleDefinitionService roleDefinitionService;
    @Autowired @Lazy
    private UserRoleService userRoleService;
    @Autowired @Lazy
    private MyLockRegistry lockRegistry;
    @Autowired @Lazy
    private DeviceEnumMappingService deviceEnumMappingService;
    @Autowired @Lazy
    private UserTierDeviceService userTierDeviceService;

    @Autowired @Lazy
    private BxBindService bxBindService;
    @Autowired @Lazy
    private UserSettingService userSettingService;

    @Resource @Lazy
    @Autowired
    AlexaSafemoRequestService alexaSafemoRequestService;

    @Autowired
    private ThingModelConfig thingModelConfig;

    @Override
    public Boolean modifyAttribute(Integer userId, String sn, Integer dtype, List<DeviceAttribute> deviceAttributes) {
        DeviceAttributesModify input = new DeviceAttributesModify();
        input.setSerialNumber(sn);
        input.setUserId(userId);
        List<DeviceModifiableAttribute> modifiableAttributes = new ArrayList<>();
        for(DeviceAttribute item : deviceAttributes){
            DeviceModifiableAttribute deviceModifiableAttribute = new DeviceModifiableAttribute();
            deviceModifiableAttribute.setName(item.getName());
            deviceModifiableAttribute.setValue(item.getValue());
            modifiableAttributes.add(deviceModifiableAttribute);
        }

        input.setModifiableAttributes(modifiableAttributes);
        if(BASE_STATION.getCode() == dtype){
            return this.modifyBstationAttributes(input).getResult().equals(Result.Success().getResult());
        }
        return this.modifyDeviceAttributes(input).getResult().equals(Result.Success().getResult());
    }

    public List<DeviceModifiableAttribute> transfer(String sn, List<DeviceAttribute> attributes) {

        List<DeviceModifiableAttribute> attrList = new LinkedList<>();
        final DeviceAttributeSource src = getAttributeSource(sn);
        for(DeviceAttribute deviceAttribute: attributes) {

            final DeviceAttributeName attrName = nameOf(deviceAttribute.getName());
            final Object value = deviceAttribute.getValue();
            if (attrName == null) {
                continue;
            } else if (value == null) {
                continue; // ignore
            }

            switch (attrName){
                case alarmDuration: {
                    DeviceEnumAttributes tmp = new DeviceEnumAttributes().setName(alarmDuration.name())
                            .setValue((String) value)
                            .setOptions(src.getSupport().getAlarmDurationOptions());
                    attrList.add(tmp);
                    break;
                } default: {
                    DeviceSwitchAttributes tmp = new DeviceSwitchAttributes().setName(attrName.name())
                            .setValue((Boolean) value);
                    attrList.add(tmp);
                }
            }
        }
        return attrList;
    }

    //转换成iot-base的模型
    public List<DeviceAttribute> transferToDeviceAttribute(List<DeviceModifiableAttribute> attributes) {

        List<DeviceAttribute> attrList = new LinkedList<>();
        for(DeviceModifiableAttribute deviceAttribute: attributes) {
            DeviceAttribute attr = new DeviceAttribute();
            attr.setName(deviceAttribute.getName());
            attr.setValue(deviceAttribute.getValue());
            attrList.add(attr);
        }
        return attrList;
    }


    // 把属性来源都放在这，避免重复查询
    public class DeviceAttributeSource {
        @Getter
        private final String sn;

        private DeviceAttributeSource(String sn) {
            this.sn = sn;
        }

        private DeviceDO device;
        private DeviceManualDO manual;
        private DeviceModelIconDO modelIcon;
        private LocalDeviceSupport support;
        private DeviceStatusDO status;
        private DeviceStateDO state;
        private DeviceDormancyStatus deviceDormancyStatus;
        private DeviceOnlineInfo onlineInfo;
        private DeviceDormancyInfo dormancyInfo;
        private Optional<FirmwareViewDO> firmware;
        private DeviceOTADO ota;
        private Integer adminId;
        private SdCard sdCard;
        private String firmwareGitSha;

        private DeviceSettingsDO settings;
        private PushInfo pushInfo;
        private OptionEnumMapping enumMapping;

        private Integer modelCategory;
        private List<DeviceObjectEnumAttributes.Item> locationOptions;
        private List<String> doorbellPressNotifyTypeOptions;

        private String defaultPirSensitivity;

        private Boolean isNoVipOrFreeTier2;

        private UserSettingsDO userSettingsDO;

        /*
        public boolean isSupportDoorbellPressNotifyTypeSwitch() {
            return getModelCategory() == DeviceModelCategoryEnums.DOORBELL.getCode()
                    || (getSupport() != null && getSupport().getSupportDeviceCall() == 1);
        }
        */
        /*
        public List<String> getDoorbellPressNotifyTypeOptions() {
            if (doorbellPressNotifyTypeOptions != null) return doorbellPressNotifyTypeOptions;
            boolean isIOS = AppType.IOS.getName().equals(appReq.getApp().getAppType());
            String voipToken = Optional.ofNullable(getPushInfo()).map(it -> it.getIosVoipToken()).orElse(null);
            final List<String> options;
            if (!isIOS || StringUtils.isNotBlank(voipToken)) {
                options = Arrays.asList(DoorbellPressNotifyType.phone.name(), DoorbellPressNotifyType.push.name());
            } else {
                options = Arrays.asList(DoorbellPressNotifyType.push.name());
            }
            return doorbellPressNotifyTypeOptions = options;
        }
        */
        public List<String> getDoorbellPressNotifyTypeOptions() {
            return Arrays.asList(DoorbellPressNotifyType.phone.name(), DoorbellPressNotifyType.push.name());
        }

        /* 按产品给的型号表配置: https://a4x-paas.feishu.cn/wiki/wikcn6CQ0RchwwjMHAxXF7rYoQe?office_edit=1&table=tbl3zSYshYbBZgLE&view=vewX9u5Est
        public List<String> getPowerSourceOptions() {
            if (getModelCategory() == DeviceModelCategoryEnums.DOORBELL.getCode()) {
                return Arrays.asList("wired", "battery_power_only");
            } else {
                return Arrays.asList("solar_panel", "plug_in", "battery_power_only");
            }
        }
        */

        public Integer getModelCategory() {
            if (modelCategory != null) return modelCategory;
            DeviceDO deviceDO = this.getDevice();
            return deviceDO.getModelCategory();
        }

        public List<DeviceObjectEnumAttributes.Item> getLocationOptions() {
            if (locationOptions != null) return locationOptions;
            return locationOptions = locationInfoService.listUserLocations(getAdminId()).stream()
                    .map(it -> new DeviceObjectEnumAttributes.Item().setId(it.getId() + "").fluentPut("text", it.getLocationName()))
                    .collect(Collectors.toList());
        }

        public Integer getAdminId() {
            if (adminId != null) return adminId;
            return adminId = Optional.ofNullable(userRoleService.getDeviceAdminUserRole(sn)).map(it -> it.getAdminId())
                    .orElseThrow(() -> new RuntimeException("设备未绑定!"));
        }

        public UserRoleEnums getUserRole(Integer userId) {
            return isAdmin(userId) ? UserRoleEnums.ADMIN : UserRoleEnums.USER;
        }

        public boolean isAdmin(Integer userId) {
            return Objects.equals(getAdminId(), userId);
        }

        public DeviceDO getDevice() {
            if (device != null) return device;
            device = deviceService.getAllDeviceInfo(sn);
            device.setDeviceSupport(getSupport());
            return device;
        }

        public Optional<FirmwareViewDO> getFirmware() {
            if (firmware != null) return firmware;
            return firmware = Optional.ofNullable(firmwareService.buildFirmwareView(getDevice(), getOta()));
        }

        public DeviceOTADO getOta() {
            if (ota != null) return ota;
            return ota = deviceOTAService.queryDeviceOtaBySerialNumber(sn);
        }

        public SdCard getSdCard() {
            if (sdCard != null) return sdCard;
            return sdCard = deviceSdCardStatusService.querySdCard(sn);
        }

        public String getFirmwareGitSha() {
            if (firmwareGitSha != null) return firmwareGitSha;
            return firmwareGitSha = deviceInfoService.getDeviceFirmwareBuilder(sn);
        }

        public DeviceManualDO getManual() {
            if (manual != null) return manual;
            return manual = deviceManualService.getDeviceManualBySerialNumber(sn);
        }

        //约定：icon smallIcon 设备在绑定时向云端查询并保存在本地，设置接口因为访问的是本地数据库，无法更新，不提供图片
        public DeviceModelIconDO getModelIcon() {
            if (modelIcon != null) return modelIcon;
             modelIcon =  new DeviceModelIconDO() ;
             modelIcon.setModelNo(getManual().getModelNo());
             modelIcon.setIconUrl("");
             modelIcon.setSmallIconUrl("");
             return modelIcon;
        }

        public LocalDeviceSupport getSupport() {
            if (support != null) return support;
            return this.support = deviceInfoService.getDeviceSupport(sn);
        }

        public String getDefaultPirSensitivity() {
            if (defaultPirSensitivity != null) return defaultPirSensitivity;
            Integer defaultValue = deviceSettingConfig.queryMotionSensitivity(getManual().getModelNo());
            return defaultPirSensitivity = getEnumName(getEnumMapping().getPirSensitivityOptions(), defaultValue, null);
        }

        public DeviceSettingsDO getSettings() {
            if (settings != null) return settings;
            settings = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
            setSettingDefaultEnumValue(settings);
            return settings;
        }

        /**
         * 设置deviceSettings默认枚举值
         */
        public void setSettingDefaultEnumValue(DeviceSettingsDO settings) {
            if (settings == null || getEnumMapping() == null) return;
            if (StringUtils.isBlank(settings.getPirSensitivity())) {
                /* prod-us camera.device_settings.motion_sensitivity: 1,2,3 */
                settings.setPirSensitivity(getEnumName(getEnumMapping().getPirSensitivityOptions(), settings.getMotionSensitivity(), this::getDefaultPirSensitivity));
            }
            if (StringUtils.isBlank(settings.getPirRecordTime())) {
                /* prod-us camera.device_settings.rec_len: -1,10,15,20 */ // DeviceModelSettingConstants.VIDEO_SECONDS_VALUE
                settings.setPirRecordTime(getEnumName(getEnumMapping().getPirRecordTimeOptions(), settings.getRecLen(), () -> "10s"));
            }
            if (StringUtils.isBlank(settings.getPirCooldownTime())) {
                /* prod-us camera.device_settings.cooldown_in_s: 0,10,30,60,120,180,300,600,1800,3600 */ // CoolDownDO.DEFAULT_VALUE
                settings.setPirCooldownTime(getEnumName(getEnumMapping().getPirCooldownTimeOptions(), settings.getCooldownInS(), () -> "10s"));
            }
            if (StringUtils.isBlank(settings.getVideoResolution()) && getDevice() != null) {
                /* prod-us camera.device.rec_resolution:  */
                settings.setVideoResolution(getEnumName(getEnumMapping().getVideoResolutionOptions(), getDevice().getRecResolution(), () -> "mid"));
            }
            if (StringUtils.isBlank(settings.getVideoAntiFlickerFrequency())) {
                /* prod-us camera.device_settings.antiflicker: 50,60 */ // BindService.DEFAULT_ANTIFLICKER
                settings.setVideoAntiFlickerFrequency(getEnumName(getEnumMapping().getVideoAntiFlickerFrequencyOptions(), settings.getAntiflicker(), () -> "60Hz"));
            }
            if (settings.getDoorbellPressNotifySwitch() == null) {
                settings.setDoorbellPressNotifySwitch(DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_SWITCH);
            }
            if (StringUtils.isBlank(settings.getDoorbellPressNotifyType())) {
                settings.setDoorbellPressNotifyType(DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_TYPE);
            }
            if (StringUtils.isBlank(settings.getNightVisionSensitivityEnum())) {
                settings.setNightVisionSensitivityEnum(getEnumName(getEnumMapping().getNightVisionSensitivityOptions(), settings.getNightVisionSensitivity(), null));
            }
            if (StringUtils.isBlank(settings.getNightVisionModeEnum())) {
                settings.setNightVisionModeEnum(getEnumName(getEnumMapping().getNightVisionModeOptions(), settings.getNightVisionMode(), null));
            }
            if (StringUtils.isBlank(settings.getPowerSource())) {
                settings.setPowerSource(OptionEnumMapping.OPTION_ENUM_UNSELECTED);
            }
            if (StringUtils.isBlank(settings.getAlarmDurationEnum())) {
                settings.setAlarmDurationEnum(getEnumName(getEnumMapping().getAlarmDurationOptions(), settings.getPirSirenDuration(), () -> "5s"));
            }
            if (settings.getOverallLightSwitch() == null) {
                settings.setOverallLightSwitch(DEFAULT_VALUE_OVERALL_LIGHT_SWITCH);
            }
            if (settings.getOverallLightIntensity() == null) {
                settings.setOverallLightIntensity(DEFAULT_VALUE_OVERALL_LIGHT_INTENSITY);
            }
        }

        public DeviceStateDO getState() {
            if (state != null) return state;
            try {
                state = stateMachineService.batchGetDeviceState(Arrays.asList(sn)).get(sn);
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.warn(log, "failed batchGetDeviceState serialNumberList:{}", sn);
            }
            return state;
        }

        public DeviceStatusDO getStatus() {
            if (status != null) return status;
            return status = deviceStatusService.queryDeviceStatusBySerialNumber(sn);
        }

        public DeviceDormancyStatus getDeviceDormancyStatus() {
            if (deviceDormancyStatus != null) return deviceDormancyStatus;
            return deviceDormancyStatus = deviceDormancyPlanService.queryDeviceDormancyStatus(sn);
        }

        public DeviceOnlineInfo getOnlineInfo() {
            if (onlineInfo != null) return onlineInfo;
            DeviceDO deviceDO = this.getDevice();
            if(deviceDO.getModelCategory().equals(BASE_STATION.getCode())){
                return new DeviceOnlineInfo().setOnline(1).setAwake(1);
            }
            return onlineInfo = deviceStatusService.getDeviceOnlineInfo(getSn(), PhosUtils.getUTCStamp(), getStatus(), getState());
        }

        public DeviceDormancyInfo getDormancyInfo() {
            if (dormancyInfo != null) return dormancyInfo;
            return dormancyInfo = deviceInfoService.getDeviceDormancyInfo(getSn(), getAdminId(), getOnlineInfo().getOnline());
        }

        public OptionEnumMapping getEnumMapping() {
            if (enumMapping != null) return enumMapping;
            if (getManual() == null) return null;
            return enumMapping = deviceEnumMappingService.getEnumMappingByModelNo(getManual().getModelNo());
        }

        /*
        套餐组需求文档：https://a4x-paas.feishu.cn/wiki/wikcn9fAr0m9xw1RrXCHeAO5n4b
        实现逻辑参考：com.addx.iotcamera.service.UserService.getAppFormOptions
        无套餐、免费套餐2、免费套餐3不能设置一分钟以下的拍摄间隔
        */
        public Boolean getIsNoVipOrFreeTier2() {
            if (isNoVipOrFreeTier2 != null) return isNoVipOrFreeTier2;
            return isNoVipOrFreeTier2 = userTierDeviceService.getIsNoVipOrFreeTier2(getAdminId(), sn);
        }

    }

    public DeviceAttributeSource getAttributeSource(String sn) {
        return new DeviceAttributeSource(sn);
    }

    /**
     * v0版。由后端根据现有设备上报数据进行转换
     *
     * @return
     */
    public Result<DeviceAttributes> getDeviceAttributes(DeviceAttributesQuery input) {
        final String sn = input.getSerialNumber();
        final DeviceAttributeSource src = getAttributeSource(sn);
        /* result:输出结果 */
        final DeviceAttributes result = new DeviceAttributes().setSerialNumber(sn);
        if (input.getReturnFixedAttributes()) {
            DeviceFixedAttributes attrs = new DeviceFixedAttributes();
            result.setFixedAttributes(attrs);
            attrs.setUserSn(src.getManual().getUserSn());
            attrs.setModelNo("SP10".equals(src.getManual().getDisplayModelNo()) ? "SP10" : src.getManual().getModelNo());
            attrs.setDisplayModelNo(StringUtils.isNotBlank(src.getManual().getDisplayModelNo()) ? src.getManual().getDisplayModelNo() : src.getManual().getModelNo());
            attrs.setModelCategory(src.getModelCategory());
            attrs.setWiredMacAddress(src.getManual().getWiredMacAddress());
            attrs.setCanStandBy(src.getSupport().getSupportCanStandby());
            attrs.setQuantityCharge(Optional.ofNullable(src.getSupport().getQuantityCharge()).orElse(0) > 0);
            attrs.setMacAddress(src.getManual().getMacAddress());
            attrs.setWiredMacAddress(src.getManual().getWiredMacAddress());
            attrs.setIcon(Optional.ofNullable(src.getModelIcon().getIconUrl()).orElse(""));
            attrs.setSmallIcon(Optional.ofNullable(src.getModelIcon().getSmallIconUrl()).orElse(""));
            attrs.setSupportOtaAutoUpgrade(src.getSupport().getSupportOtaAutoUpgrade());
            //bx必须功能 ，支持自动ota
            DeviceDO deviceDO = src.getDevice();
            if(deviceDO.getModelCategory().equals(BASE_STATION.getCode())){
                attrs.setSupportOtaAutoUpgrade(true);
            }
            attrs.setActivatedTime(src.getDevice().getActivatedTime());
            attrs.setActivated(src.getDevice().getActivated());
            attrs.setRole(src.getUserRole(input.getUserId()).getRole());
            attrs.setRoleName(src.getUserRole(input.getUserId()).getRoleName());

            /*** deviceSupport中需要透传给设备的支持项 开始 */
            attrs.setDeviceSupportAlarm(src.getSupport().getDeviceSupportAlarm()); // 是否支持警铃
            attrs.setSupportWebrtc(src.getSupport().getSupportWebrtc()); // 是否支持webRtc
            attrs.setSupportRecLamp(src.getSupport().getSupportRecLamp()); // 是否支持录像指示灯
            attrs.setSupportVoiceVolume(src.getSupport().getSupportVoiceVolume()); // 是否支持提示音音量调节
            attrs.setSupportAlarmVolume(src.getSupport().getSupportAlarmVolume()); // 是否支持警铃音量调节
            attrs.setSupportLiveAudioToggle(src.getSupport().getSupportLiveAudioToggle()); // 是否支持直播录音开关设置
            attrs.setSupportRecordingAudioToggle(src.getSupport().getSupportRecordingAudioToggle()); // 是否支持sd卡直播录音开关设置
            attrs.setSupportLiveSpeakerVolume(src.getSupport().getSupportLiveSpeakerVolume()); // 是否支持对讲音量设置
            attrs.setSupportAlarmWhenRemoveToggle(src.getSupport().getSupportAlarmWhenRemoveToggle()); // 是否支持门铃拆除时报警开关
            attrs.setP2pConnMgtStrategy(src.getSupport().getP2pConnMgtStrategy()); // p2p连接消息策略
            attrs.setSupportAlexaWebrtc(src.getSupport().getSupportAlexaWebrtc()); // 是否支持alexa
            attrs.setSupportResetVoltameter(src.getSupport().getSupportResetVoltameter()); // 是否支持重置电量计
            attrs.setSupportKeepAliveProtocol(src.getSupport().getSupportKeepAliveProtocol()); // 保活协议,
            attrs.setSupportDeviceCall(src.getSupport().getSupportDeviceCall()); // 是否支持设备呼叫
            attrs.setSupportChargeAutoPowerOn(src.getSupport().getSupportChargeAutoPowerOn()); // 是否支持充电自动开机
            attrs.setCanRotate(src.getSupport().getCanRotate()); // 是否支持摇头,0:否1:是
            attrs.setSupportMotionTrack(src.getSupport().getSupportMotionTrack()); // 是否支持运动追踪
            attrs.setSupportFrequency(src.getSupport().getSupportFrequency()); // 是否支持频率
            attrs.setAntiDisassemblyAlarm(src.getSupport().getAntiDisassemblyAlarm()); // 是否支持防拆除警报
            attrs.setSupportDoorbellPressNotifySwitch(src.getSupport().getSupportDoorbellPressNotifySwitch()); // 是否支持按铃通知开关
            attrs.setIsShield(src.getSupport().getIsShield()); // 是否带屏蔽罩
            attrs.setSupportWifiPowerLevel(src.getSupport().getSupportWifiPowerLevel()); // 是否支持wifi发射功率档位调节
            attrs.setSupportNetTest(src.getSupport().getSupportNetTest()); // 是否支持网络测试
            /*** deviceSupport中需要透传给设备的支持项 结束 */

            /*** deviceSupport中需要透传给设备的支持项-二期 开始 */
            attrs.setSupportLocalVideoLookBack(src.getSupport().getSupportLocalVideoLookBack()); // 是否支持本地存储视频回看
            attrs.setLocalVideoStorageType(src.getSupport().getLocalVideoStorageType()); // 本地视频存储类型
//            attrs.setMechanicalDingDongDurationRange(src.getSupport().getMechanicalDingDongDurationRange()); // 是否支持机械响铃时长控制
            attrs.setSupportSdCardFormat(src.getSupport().getSupportSdCardFormat()); // 是否支持SD卡格式化
            attrs.setSupportNightVisionSwitch(src.getSupport().getSupportNightVisionSwitch()); // 是否支持夜视开关控制
//            attrs.setNightVisionSensitivityOptions(src.getSupport().getNightVisionSensitivityOptions()); // 是否支持夜视灵敏度控制
//            attrs.setNightVisionModeOptions(src.getSupport().getNightVisionModeOptions()); // 是否支持夜视模式调节
//            attrs.setAlarmVolumeRange(src.getSupport().getAlarmVolumeRange()); // 警铃音量调节范围
//            attrs.setVoiceVolumeRange(src.getSupport().getVoiceVolumeRange()); // 提示音音量调节范围
            attrs.setSupportWhiteLight(src.getSupport().getSupportWhiteLight()); // 是否支持白光灯
            attrs.setSupportAlarmFlashLight(src.getSupport().getSupportAlarmFlashLight()); // 是否支持报警闪光灯
            /*** deviceSupport中需要透传给设备的支持项-二期 结束 */
            attrs.setSupportRotateCalibration(src.getSupport().getSupportRotateCalibration());
            attrs.setSupportUnlimitedWebsocket(src.getSupport().getSupportUnlimitedWebsocket());
            attrs.setSupportIndoor(src.getSupport().getSupportIndoor());
            attrs.setSupportStarlightSensor(src.getSupport().getSupportStarlightSensor());

            attrs.setSupportMagicPix(src.getSupport().getSupportMagicPix());

            // 设备端ai检测support字段
            attrs.setSupportPirAi(BooleanUtils.isTrue(src.getSupport().getSupportPersonAi()) || BooleanUtils.isTrue(src.getSupport().getSupportPetAi()));
            attrs.setSupportEventAnalytics(BooleanUtils.isTrue(src.getSupport().getSupportEventAnalytics()));


            attrs.setSupportJson(src.getSupport().getSupportJson());
        }
        if (input.getReturnRealTimeAttributes()) {
            DeviceRealTimeAttributes attrs = new DeviceRealTimeAttributes();
            result.setRealTimeAttributes(attrs);
            attrs.setOnline(src.getOnlineInfo().getOnline());
            attrs.setOfflineTime(src.getOnlineInfo().getOfflineTime());
            attrs.setDeviceStatus(src.getDormancyInfo().getDeviceStatus());
            /* 供电相关 */
            attrs.setChargingMode(src.getStatus().getChargingMode());
            attrs.setIsCharging(src.getStatus().getIsCharging());
            attrs.setBatteryLevel(src.getStatus().getBatteryLevel());
            /* 网络相关 */
            setDeviceNetTypeByType(input, src, attrs);

            attrs.setSignalStrength(src.getStatus().getSignalStrength());
            attrs.setNetworkName(src.getStatus().getNetworkName());
            attrs.setIp(src.getStatus().getIp());
            attrs.setWifiChannel(src.getStatus().getWifiChannel());
            /* 固件相关 */
            attrs.setFirmwareId(src.getDevice().getFirmwareId());
            attrs.setNewestFirmwareId(src.getFirmware().map(it -> it.getTargetFirmware()).orElse(null));
            attrs.setFirmwareStatus(src.getFirmware().map(it -> it.getFirmwareStatus()).orElse(null));
            attrs.setMcuNumber(src.getManual().getMcuNumber());
            attrs.setDisplayGitSha(src.getFirmwareGitSha());
            /* sd卡属性*/
            attrs.setSdCard(src.getSdCard());
            attrs.setWhiteLight(Optional.ofNullable(src.getStatus().getWhiteLight()).orElse(WhiteLightEnums.NOSUPPORT.getCode()));

            attrs.setSsdTotal(StringUtils.isNotEmpty(src.getStatus().getSsdTotal()) ?Integer.parseInt(src.getStatus().getSsdTotal()) : 0);
            attrs.setSsdUsed(StringUtils.isNotEmpty(src.getStatus().getSsdUsed()) ?Integer.parseInt(src.getStatus().getSsdUsed()) : 0);
            attrs.setSsdStatus(src.getStatus().getSsdStatus());
            attrs.setStorageData(StringUtils.isNotEmpty(src.getStatus().getStorageData()) ?Integer.parseInt(src.getStatus().getStorageData()) : 0);
            attrs.setStorageSystem(StringUtils.isNotEmpty(src.getStatus().getStorageSystem()) ?Integer.parseInt(src.getStatus().getStorageSystem()) : 0);
            attrs.setStorageTotal(StringUtils.isNotEmpty(src.getStatus().getStorageTotal()) ?Integer.parseInt(src.getStatus().getStorageTotal()) : 0);
            attrs.setBatterySn(src.getStatus().getBatterySn());
            attrs.setBatteryModelNo(src.getStatus().getBatteryModelNo());
            attrs.setBatteryRemoval(src.getStatus().getBatteryRemoval());
            attrs.setSolarSn(src.getStatus().getSolarSn());
            attrs.setSolarModelNo(src.getStatus().getSolarModelNo());
            attrs.setSolarOriginalModelNo(src.getStatus().getSolarOriginalModelNo());
        }
        if (input.getReturnModifiableAttributes()) {
            final List<DeviceModifiableAttribute> attrs = createDeviceModifiableAttributes(src);
            result.setModifiableAttributes(src.isAdmin(input.getUserId()) ? prefixAttrs(src, attrs) : attrs);
        }

        //set  bind info ,从首页获取，暂不需要
        //setBindInfo(input, result);

        return new Result(result);
    }

    public DeviceModifiableAttribute getModifiableAttributeByName(String attributeName,String serialNumber){
        Result<DeviceAttributes> deviceAttributesResult = getDeviceAttributes(new DeviceAttributesQuery().setReturnModifiableAttributes(true).setSerialNumber(serialNumber));
        for(DeviceModifiableAttribute deviceModifiableAttribute : deviceAttributesResult.getData().getModifiableAttributes()){
            if(deviceModifiableAttribute.getName().equals(attributeName)){
                return deviceModifiableAttribute;
            }
        }
        return null;
    }

    private void setDeviceNetTypeByType(DeviceAttributesQuery input, DeviceAttributeSource src, DeviceRealTimeAttributes attrs) {
        UserRoleDO userRole = userRoleService.getUserRoleDOByUserIdAndSerialNumber(input.getUserId(), input.getSerialNumber());
        //绑定到BX的设备，如果没有上报，网络设置为direct to hub
        if(StringUtils.isNotEmpty(userRole.getAssociationSn()) && src.getStatus().getDeviceNetType() == null){
            attrs.setDeviceNetType(DeviceNetType.HUB.getCode());
        }else {
            attrs.setDeviceNetType(Optional.ofNullable(src.getStatus().getDeviceNetType()).orElse(DeviceNetType.WIFI.getCode()));
        }
    }

    public List<DeviceModifiableAttribute> createDeviceModifiableAttributes(DeviceAttributeSource src) {
        final OptionEnumMapping enumMapping = src.getEnumMapping();
        List<DeviceModifiableAttribute> attrList = new LinkedList<>();
        /* 1.运动检测-总开关*/
        attrList.add(new DeviceSwitchAttributes().setName(pirSwitch.name())
                .setValue(src.getSettings().getPir() > 0));
        /* 2.运动检测-灵敏度*/
        attrList.add(new DeviceEnumAttributes().setName(pirSensitivity.name())
                .setValue(src.getSettings().getPirSensitivity())
                .setOptions(src.getSupport().getPirSensitivityOptions()));
        /* 3.运动检测-录制时长*/
        {
            DeviceEnumAttributes pirRecordTimeAttr = new DeviceEnumAttributes().setName(pirRecordTime.name())
                    .setValue(src.getSettings().getPirRecordTime())
                    .setOptions(src.getSupport().getPirRecordTimeOptions());
            if (src.getIsNoVipOrFreeTier2()) {
                List<Integer> videoSecondsValueList = FreeUserVipTier2Config.getVideoSecondsValueListBySerialNumber(src.getSn());
                pirRecordTimeAttr.setOptions(src.getEnumMapping().getPirRecordTimeOptions().stream().filter(pirRecordTimeOption -> videoSecondsValueList.contains(pirRecordTimeOption.getValue())).map(it -> it.getEnumName()).collect(Collectors.toList()));
                pirRecordTimeAttr.setDisabledOptions(src.getEnumMapping().getPirRecordTimeOptions().stream().filter(pirRecordTimeOption -> !videoSecondsValueList.contains(pirRecordTimeOption.getValue())).map(it -> it.getEnumName()).collect(Collectors.toList()));
            }
            attrList.add(pirRecordTimeAttr);
        }
        /* 4.运动检测-触发间隔*/
        attrList.addAll(getPirCooldownSwitchAndTime(src));
        /* 6.定时休眠-总开关*/
        if (src.getSupport().getDeviceDormancySupport() > 0) {
            attrList.add(new DeviceSwitchAttributes().setName(timedDormancySwitch.name())
                    .setValue(src.getDevice().getDormancyPlanSwitch() > 0));
        }
        /* 6.运动追踪-总开关*/
        if (src.getSupport().getSupportMotionTrack() > 0) {
            attrList.add(new DeviceSwitchAttributes().setName(motionTrackingSwitch.name())
                    .setValue(src.getSettings().getMotionTrack() > 0));
        }
        /* 7.视频-分辨率*/
        attrList.add(getVideoResolution(src));
        /* 8.视频-翻转开关*/
        if (src.getSupport().getDeviceSupportMirrorFlip()) {
            attrList.add(new DeviceSwitchAttributes().setName(videoFlipSwitch.name())
                    .setValue(src.getSettings().getMirrorFlip() > 0));
        }
        /* 9.视频-抗频闪开关*/
        if (src.getSupport().getAntiflickerSupport() > 0) {
            attrList.add(new DeviceSwitchAttributes().setName(videoAntiFlickerSwitch.name())
                    .setValue(src.getSettings().getAntiflickerSwitch() > 0));
            /* 10.视频-抗频闪频率*/
            attrList.add(new DeviceEnumAttributes().setName(videoAntiFlickerFrequency.name())
                    .setValue(src.getSettings().getVideoAntiFlickerFrequency())
                    .setOptions(src.getSupport().getVideoAntiFlickerFrequencyOptions()));
        }
        /* 11.音视频流联动(切换视频编码)*/
        if (deviceInfoService.getSupportChangeCodec(src.getDevice())) {
            // supportChangeCodec: 是否支持切换编码
            // defaultCodec: 用户修改，下发到设备。 ["h256","h264"]
            // codec: 设备上报。 ["main=h265&sub=h265","main=h264&sub=h264"]
            attrList.add(new DeviceEnumAttributes().setName(liveStreamCodec.name())
                    .setValue(src.getSettings().getDefaultCodec())
                    .setOptions(src.getSupport().getLiveStreamCodecOptions()));
        }
        /* 13.设备所在位置*/
        attrList.add(new DeviceObjectEnumAttributes().setName(location.name())
                .setValue(FuncUtil.findFirst(src.getLocationOptions(), it -> it.getId(), src.getDevice().getLocationId() + ""))
                .setOptions(src.getLocationOptions()));
        /* 14.设备名称*/
        attrList.add(new DeviceTextAttributes().setName(deviceName.name())
                .setValue(src.getDevice().getDeviceName()));
        /* 15.时区*/
        attrList.add(new DeviceTextAttributes().setName(timeZone.name())
                .setValue(src.getSettings().getTimeZone()));
        /* 16.按铃通知开关*/
        if (src.getSupport().getSupportDoorbellPressNotifySwitch()) { // 兼容：modelCategory==2
            attrList.add(new DeviceSwitchAttributes().setName(doorbellPressNotifySwitch.name())
                    .setValue(src.getSettings().getDoorbellPressNotifySwitch()));
            /* 17.按铃通知方式*/
            attrList.add(new DeviceEnumAttributes().setName(doorbellPressNotifyType.name())
                    .setValue(src.getSettings().getDoorbellPressNotifyType())
                    .setOptions(src.getDoorbellPressNotifyTypeOptions()));
        }
        /* 18.一键呼叫开关*/
        if (src.getSupport().getSupportDeviceCall() > 0) { // 20220503增加的字段，不需要兼容
            attrList.add(new DeviceSwitchAttributes().setName(deviceCallNotifySwitch.name())
                    .setValue(src.getSettings().getDeviceCallToggleOn()));
        }
        /* 19.机械响铃开关*/
        if (src.getSupport().isSupportMechanicalDingDong()) { // 20220530增加的字段，不需要兼容
            attrList.add(new DeviceSwitchAttributes().setName(mechanicalDingDongSwitch.name())
                    .setValue(src.getSettings().getMechanicalDingDongSwitch() > 0));
            /* 20.机械响铃时长*/
            attrList.add(new DeviceIntRangeAttributes().setName(mechanicalDingDongDuration.name())
                    .setValue(src.getSettings().getMechanicalDingDongDuration())
                    .setIntRange(src.getSupport().getMechanicalDingDongDurationRange()));
        }
        /* 21.运动触发报警开关*/
        if (src.getSupport().getDeviceSupportAlarm()) { // 是否支持警铃 // 20200905增加的字段，不需要兼容
            attrList.add(new DeviceSwitchAttributes().setName(motionAlertSwitch.name())
                    .setValue(src.getSettings().getNeedAlarm() > 0));
        }
        /* 22.防拆报警开关*/
        if (src.getSupport().getAntiDisassemblyAlarm() > 0) { // 是否支持防拆除警报 // 20220920增加的字段，不需要兼容
            attrList.add(new DeviceSwitchAttributes().setName(antiDisassemblyAlarmSwitch.name())
                    .setValue(src.getSettings().getAlarmWhenRemoveToggleOn()));
        }
        /* 23.报警闪光灯开关*/
        if (src.getSupport().getSupportAlarmFlashLight()) { // 是否支持报警闪光灯 // 兼容：status.whiteLight!=-1
            attrList.add(new DeviceSwitchAttributes().setName(alarmFlashLightSwitch.name())
                    .setValue(src.getSettings().getWhiteLightScintillation() > 0)); // 白光灯闪烁
        }
        /* 24.指示灯开关*/
        if (src.getSupport().getSupportRecLamp() > 0) { // 是否支持录像指示灯 // 20201209增加的字段，不需要兼容
            attrList.add(new DeviceSwitchAttributes().setName(recLampSwitch.name())
                    .setValue(src.getSettings().getRecLamp() > 0)); // 录像指示灯
        }
        /* 25.夜视开关*/
        if (src.getSupport().getSupportNightVisionSwitch()) {
            attrList.add(new DeviceSwitchAttributes().setName(nightVisionSwitch.name())
                    .setValue(src.getSettings().getIrThreshold() > 0)); // 夜视开关
        }
        /* 27.夜视模式*/
        if (CollectionUtils.isNotEmpty(src.getSupport().getNightVisionModeOptions())) { // 是否支持夜视模式调节
            attrList.add(new DeviceEnumAttributes().setName(nightVisionMode.name())
                    .setValue(src.getSettings().getNightVisionModeEnum()) // 夜视灵敏度-数值
                    .setOptions(src.getSupport().getNightVisionModeOptions()));
        }
        /* 26.夜视灵敏度*/
        if (CollectionUtils.isNotEmpty(src.getSupport().getNightVisionSensitivityOptions())) { // 是否支持夜视开关控制
            attrList.add(new DeviceEnumAttributes().setName(nightVisionSensitivity.name())
                    .setValue(src.getSettings().getNightVisionSensitivityEnum()) // 夜视灵敏度-数值
                    .setOptions(src.getSupport().getNightVisionSensitivityOptions()));
        }
        /* 28.报警时长*/
        if (src.getSupport().getDeviceSupportAlarm()) { // 20200915增加的字段，不需要兼容
            attrList.add(new DeviceEnumAttributes().setName(alarmDuration.name())
                    .setValue(src.getSettings().getAlarmDurationEnum())
                    .setOptions(src.getSupport().getAlarmDurationOptions()));
        }
        /* 29.警铃音量*/
        if (src.getSupport().getSupportAlarmVolume() > 0) { // 20201210增加的字段，不需要兼容
            attrList.add(new DeviceIntRangeAttributes().setName(alarmVolume.name())
                    .setValue(src.getSettings().getAlarmVolume())
                    .setIntRange(src.getSupport().getAlarmVolumeRange()));
        }
        /* 30.设备语音语种选择*/
        if (CollectionUtils.isNotEmpty(src.getSupport().getDeviceSupportLanguage())) { // 设备支持的语言
            attrList.add(new DeviceEnumAttributes().setName(voiceLanguage.name())
                    .setValue(src.getSettings().getLanguage())
                    .setOptions(src.getSupport().getDeviceSupportLanguage()));
        }
        /* 31.门铃铃声选择*/
        if (CollectionUtils.isNotEmpty(src.getSupport().getSupportDoorBellRingKey())) {
            final List<DeviceObjectEnumAttributes.Item> options = new LinkedList<>();
            DeviceObjectEnumAttributes.Item currentOption = null;
            for (final Integer id : src.getSupport().getSupportDoorBellRingKey()) {
                final DeviceObjectEnumAttributes.Item option = new DeviceObjectEnumAttributes.Item().setId(id + "")
                        .fluentPut("url", s3Service.preSignUrl(doorBellRingConfig.getConfig().get(id)));
                options.add(option);
                if (Objects.equals(id, src.getSettings().getDoorBellRingKey())) {
                    currentOption = option;
                }
            }
            attrList.add(new DeviceObjectEnumAttributes().setName(doorBellRing.name())
                    .setValue(currentOption).setOptions(options));
        }
        /* 32.提示音音量*/
        if (src.getSupport().getSupportVoiceVolume() > 0) { // 是否支持提示音音量调节 // 20201210增加的字段，不需要兼容
            attrList.add(new DeviceIntRangeAttributes().setName(voiceVolume.name())
                    .setValue(src.getSettings().getVoiceVolume())
                    .setIntRange(src.getSupport().getVoiceVolumeRange()));
        }
        /* 33.直播收音*/
        if (src.getSupport().getSupportLiveAudioToggle() > 0) { // 20221218增加的字段，不需要兼容
            attrList.add(new DeviceSwitchAttributes().setName(liveAudioSwitch.name())
                    .setValue(src.getSettings().getLiveAudioToggleOn()));
        }
        /* 34.录像收音*/
        if (src.getSupport().getSupportRecordingAudioToggle() > 0) { // 20221218增加的字段，不需要兼容
            attrList.add(new DeviceSwitchAttributes().setName(recordingAudioSwitch.name())
                    .setValue(src.getSettings().getRecordingAudioToggleOn()));
        }
        /* 35.供电方式。选项在后端*/
        final List<String> powerSourceOptions = getEnumNames(enumMapping.getPowerSourceOptions());
        if (CollectionUtils.isNotEmpty(powerSourceOptions)) {
            attrList.add(new DeviceEnumAttributes().setName(powerSource.name())
                    .setValue(src.getSettings().getPowerSource())
                    .setOptions(powerSourceOptions));
        }
        if (src.getSupport().getSupportChargeAutoPowerOn() > 0) {
            /* 35.供电方式。选项在后端*/
            attrList.add(new DeviceSwitchAttributes().setName(chargeAutoPowerOnSwitch.name())
                    .setValue(src.getSettings().getChargeAutoPowerOnSwitch() > 0));
            /* 37.充电自动开机电量*/
            attrList.add(new DeviceEnumAttributes().setName(chargeAutoPowerOnCapacity.name())
                    .setValue(src.getSettings().getChargeAutoPowerOnCapacity() + "pct")
                    .setOptions(getEnumNames(enumMapping.getChargeAutoPowerOnCapacityOptions())));
        }
        /* 38.声音设置-直播对讲音量*/
        if (src.getSupport().getSupportLiveSpeakerVolume() > 0) { // 20221218增加的字段，不需要兼容
            attrList.add(new DeviceIntRangeAttributes().setName(liveSpeakerVolume.name())
                    .setValue(src.getSettings().getLiveSpeakerVolume())
                    .setIntRange(src.getSupport().getLiveSpeakerVolumeRange()));
        }
        /** 38. pir检测结果偏好 */
        if (BooleanUtils.isTrue(src.getSupport().getSupportPersonAi())) {
            List<String> pirAiCheckableList = Arrays.asList("detectPersonAi");
            final DeviceCheckBoxAttributes.Item item = new DeviceCheckBoxAttributes.Item()
                    .setCheckables(pirAiCheckableList).setAt_leat(0).setAt_most(pirAiCheckableList.size());
            List<String> pirAiValueList = new LinkedList<>();
            if (BooleanUtils.isTrue(src.getSettings().getDetectPersonAi())) {
                pirAiValueList.add("detectPersonAi");
            }
            attrList.add(new DeviceCheckBoxAttributes().setName(pirAi.name()).setOptions(Arrays.asList(item)).setValue(pirAiValueList));
        }

        /* 41. 自动OTA开关 */
        attrList.add(new DeviceSwitchAttributes().setName(otaAutoUpgrade.name())
                .setValue(src.getSettings().getOtaAutoUpgrade()));

        /* 43. 日志上传 */
        attrList.add(new DeviceSwitchAttributes().setName(logUpload.name())
                .setValue(src.getSettings().getLogUpload()));
        /* 46. 设备wifi功率模式 */
        if (CollectionUtils.isNotEmpty(src.getSupport().getWifiPowerModeOptions())) {
            attrList.add(new DeviceEnumAttributes().setName(wifiPowerMode.name())
                    .setValue(Optional.ofNullable(src.getSettings().getWifiPowerMode()).orElse(DEFAULT_VALUE_WIFI_POWER_MODE))
                    .setOptions(src.getSupport().getWifiPowerModeOptions()));
        }
        /* 总体灯光开关 */
        if (src.getSupport().getSupportOverallLightSwitch()) {
            attrList.add(new DeviceSwitchAttributes().setName(overallLightSwitch.name())
                    .setValue(src.getSettings().getOverallLightSwitch()));
        }
        /* 总体灯光强度 */
        if (src.getSupport().getOverallLightRange() != null) {
            attrList.add(new DeviceIntRangeAttributes().setName(overallLightIntensity.name())
                    .setValue(src.getSettings().getOverallLightIntensity())
                    .setIntRange(src.getSupport().getOverallLightRange()));
        }
        if(src.getSupport().getSupportJson()!=null) {
            JSONObject supportJson = src.getSupport().getSupportJson();
            JSONObject propertyJson = src.getSettings().getPropertyJson();
            for (ThingModel.ThingEntity entity : thingModelConfig.getThingModelByModelNo(src.getManual().getModelNo()).getProperties()) {
                processThingModelAttribute(attrList, supportJson, propertyJson, entity);
            }
        }
        return attrList;
    }

    public static void processThingModelAttribute( List<DeviceModifiableAttribute> attrList, JSONObject supportJson, JSONObject propertyJson, ThingModel.ThingEntity entity) {
        try{
            if (StringUtils.isNotEmpty(entity.getSupportName())) {
                //不存在 或 上报不支持，则不返回设置
                if (! supportJson.containsKey(entity.getSupportName())) {
                    return;
                }
            }

            switch (entity.getType()) {
                case "SWITCH":
                    addSwitchToAttrList(attrList, propertyJson, entity);
                    break;
                case "ENUM":
                    addEnumToAttrList(attrList, supportJson, propertyJson, entity);
                    break;
                case "BITENUM":
                    addBitEnumToAttrList(attrList, supportJson, propertyJson, entity);
                    break;
                case "RANGE":
                    addRangeToAttrList(attrList, supportJson, propertyJson, entity);
                    break;
                case "INT":
                    addIntToAttrList(attrList, propertyJson, entity);
                    break;
                case "JSON":
                    addJsonToAttrList(attrList, supportJson, propertyJson, entity);
                    break;
                default:
                    //不支持的类型
                    log.error("error thingModel type, {}", JSON.toJSONString(entity));
                    break;
            }

        }catch (Exception e){
            log.error("thingModel get attribute error {},{}, {}", JSON.toJSONString(entity) , supportJson, propertyJson , e);
        }
    }

    private static void addIntToAttrList(List<DeviceModifiableAttribute> attrList, JSONObject propertyJson, ThingModel.ThingEntity entity) {
        //未设置则使用默认值
        Long dbValue = null;
        if(propertyJson != null){
            dbValue = propertyJson.getLong(entity.getIdentifier());
        }
        Integer value =  dbValue != null ? dbValue.intValue() : (Integer) entity.getDefaultValue();

        attrList.add(new DeviceIntAttributes().setName(entity.getIdentifier())
                .setValue(value));
    }

    private static void addJsonToAttrList(List<DeviceModifiableAttribute> attrList, JSONObject supportJson, JSONObject propertyJson, ThingModel.ThingEntity entity) {
        //未设置则使用默认值
        String dbValue = null;
        if(propertyJson != null ){
            dbValue = propertyJson.getString(entity.getIdentifier());
            if(!JSON.isValidObject(dbValue)){
                dbValue = null;
            }
        }

        String value =  dbValue != null ? dbValue : (String) entity.getDefaultValue() ;

        List<String> options = (List<String>) supportJson.get(entity.getOptionName());
        if(options == null) {
            options = entity.getDefaultOptions();
        }
        attrList.add(new DeviceJsonAttributes().setName(entity.getIdentifier())
                .setValue(value)
                .setOptions(options));
    }

    private static void addRangeToAttrList(List<DeviceModifiableAttribute> attrList, JSONObject supportJson, JSONObject propertyJson, ThingModel.ThingEntity entity) {
        //未设置则使用默认值，数字的json反序列化后为Long类型
        Long dbValue = null;
        if(propertyJson != null ){
            dbValue = propertyJson.getLong(entity.getIdentifier());
        }

        Integer value =  dbValue != null ? dbValue.intValue() : (Integer) entity.getDefaultValue();

        attrList.add(new DeviceIntRangeAttributes().setName(entity.getIdentifier())
                .setValue(value)
                .setIntRangeByLinkedTreeMap(supportJson.get(entity.getOptionName())));
    }

    private static void addEnumToAttrList(List<DeviceModifiableAttribute> attrList, JSONObject supportJson, JSONObject propertyJson, ThingModel.ThingEntity entity) {
        //未设置则使用默认值
        String dbValue = null;
        if(propertyJson != null ){
            dbValue = propertyJson.getString(entity.getIdentifier());
        }

        String value =  dbValue != null ? dbValue : (String) entity.getDefaultValue();

        List<String> options = (List<String>) supportJson.get(entity.getOptionName());
        if(options == null){
            options = entity.getDefaultOptions();
        }
        attrList.add(new DeviceEnumAttributes().setName(entity.getIdentifier())
                .setValue(value)
                .setOptions(options));
    }

    // value字段是int，每一个bit位都是开关；support字段是int，表示支持到哪个value
    private static void addBitEnumToAttrList(List<DeviceModifiableAttribute> attrList, JSONObject supportJson, JSONObject propertyJson, ThingModel.ThingEntity entity) {
        //未设置则使用默认值
        String dbValue = null;
        if(propertyJson != null ){
            dbValue = propertyJson.getString(entity.getIdentifier());
        }

        String value =  dbValue != null ? dbValue : (String) entity.getDefaultValue();

        List<String> options = getBitEnumOptions(supportJson, entity);
        if (options.isEmpty()) return;

        attrList.add(new DeviceEnumAttributes().setName(entity.getIdentifier())
                .setValue(value)
                .setOptions(options));
    }

    private static List<String> getBitEnumOptions(JSONObject supportJson, ThingModel.ThingEntity entity) {
        Integer supportValue = supportJson.getInteger(entity.getSupportName());
        if (supportValue == null) return Collections.emptyList();
        List<String> options = entity.getDefaultOptions().stream()
                .filter(it -> Integer.parseInt(it) <= supportValue).collect(Collectors.toList());
        return options;
    }

    private static void addSwitchToAttrList(List<DeviceModifiableAttribute> attrList, JSONObject propertyJson, ThingModel.ThingEntity entity) {
        //未设置则使用默认值
        Long dbValue = null;
        if(propertyJson != null ){
            dbValue = propertyJson.getLong(entity.getIdentifier());
        }
        //1 0 数字转一下 展示
        Boolean value =  dbValue != null ? (dbValue.equals(1L)) : entity.getDefaultValue().equals(1);

        attrList.add(new DeviceSwitchAttributes().setName(entity.getIdentifier())
                .setValue(value));
    }

    @Data
    @Accessors(chain = true)
    public static class ModifySummary {
        private DeviceAppSettingsDO appSetting;
        private DeviceDO device;

        public List<DeviceAttributeName> appSettingAttrNames = new LinkedList<>(); // appSetting修改的属性
        public List<DeviceAttributeName> deviceAttrNames = new LinkedList<>(); // device修改的属性

        public List<DeviceModifiableAttribute> unknowAttrs = new LinkedList<>(); // 不认识的属性
        public List<DeviceModifiableAttribute> errorAttrs = new LinkedList<>(); // 错误的属性
    }

    public ModifySummary createModifySummary(DeviceAttributesModify input, DeviceAttributeSource src) {
        final OptionEnumMapping enumMapping = src.getEnumMapping();
        /* summary:输出结果 */
        final ModifySummary summary = new ModifySummary();
        final DeviceAppSettingsDO appSetting = new DeviceAppSettingsDO()
                .setSerialNumber(input.getSerialNumber()).setCooldown(new CoolDownDO());
        final DeviceDO device = new DeviceDO();
        device.setSerialNumber(input.getSerialNumber());
        summary.setAppSetting(appSetting).setDevice(device);

        for (DeviceModifiableAttribute attr : input.getModifiableAttributes()) {
            final DeviceAttributeName attrName = nameOf(attr.getName());

            //check if defined in thing model
            Optional<ThingModel.ThingEntity> thingEntity = Optional.empty();
            if(attrName == null){
                thingEntity = Optional.ofNullable(thingModelConfig.getThingModelByModelNo(src.getManual().getModelNo()).getIdentifier2Property().get(attr.getName()));
            }

            final Object value = attr.getValue();
            if (attrName == null && !thingEntity.isPresent()) {
                summary.getUnknowAttrs().add(attr);
                continue;
            } else if (value == null) {
                continue; // ignore
            }

            if (thingEntity.isPresent()) {
                JSONObject supportJson = src.getSupport().getSupportJson();
                addThingModelAttributeToSummary(supportJson, summary, appSetting, attr, thingEntity, value);
                // 处理完属性 thingmodel属性
                if("pushSwitch".equals(attr.getName())){
                    device.setPushIgnored(!(Boolean)value);
                    summary.getDeviceAttrNames().add(attrName);
                }
                continue;
            }


            switch (attrName) {
                /* 1.运动检测-总开关*/
                case pirSwitch: {
                    appSetting.setNeedMotion(((Boolean) value) ? 1 : 0);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 2.运动检测-灵敏度*/
                case pirSensitivity: {
                    if (src.getSupport().getPirSensitivityOptions().contains(value)) {
                        appSetting.setMotionSensitivity(getEnumValue(enumMapping.getPirSensitivityOptions(), (String) value));
                        appSetting.setPirSensitivity((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 3.运动检测-录制时长*/
                case pirRecordTime: {
                    if (src.getSupport().getPirRecordTimeOptions().contains(value)) {
                        appSetting.setVideoSeconds(getEnumValue(enumMapping.getPirRecordTimeOptions(), (String) value));
                        appSetting.setPirRecordTime((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 4.运动检测-触发间隔开关*/
                case pirCooldownSwitch: {
                    appSetting.getCooldown().setUserEnable((Boolean) value);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 5.运动检测-触发间隔时间*/
                case pirCooldownTime: {
                    if (src.getSupport().getPirCooldownTimeOptions().contains(value)) {
                        appSetting.getCooldown().setValue(getEnumValue(enumMapping.getPirCooldownTimeOptions(), (String) value));
                        appSetting.setPirCooldownTime((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 6.定时休眠-总开关*/
                case timedDormancySwitch: {
                    device.setDormancyPlanSwitch((Boolean) value ? 1 : 0);
                    summary.getDeviceAttrNames().add(attrName);
                    break;
                }
                /* 7.运动追踪-总开关*/
                case motionTrackingSwitch: {
                    appSetting.setMotionTrack((Boolean) value ? 1 : 0);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 8.视频-分辨率*/
                case videoResolution: {
                    if (src.getSupport().getVideoResolutionOptions().contains(value)) { // eg: ["mid"] contans "mid"
                        final List<String> supportResolutionValue = src.getSupport().getDeviceSupportResolution().stream()
                                .map(it -> oldEnumName2Value.getOrDefault(it, it)).collect(Collectors.toList());
                        device.setRecResolution(LocalDeviceSupport.getRecResolution(supportResolutionValue, (String) value));
                        summary.getDeviceAttrNames().add(attrName);
                        appSetting.setVideoResolution((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 9.视频-翻转开关*/
                case videoFlipSwitch: {
                    appSetting.setMirrorFlip((Boolean) value ? 1 : 0);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 10.视频-抗频闪开关*/
                case videoAntiFlickerSwitch: {
                    appSetting.setAntiflickerSwitch((Boolean) value ? 1 : 0);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 11.视频-抗频闪频率*/
                case videoAntiFlickerFrequency: {
                    if (src.getSupport().getVideoAntiFlickerFrequencyOptions().contains(value)) {
                        appSetting.setAntiflicker(getEnumValue(enumMapping.getVideoAntiFlickerFrequencyOptions(), (String) value));
                        appSetting.setVideoAntiFlickerFrequency((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 12.音视频流联动(直播流编码)*/
                case liveStreamCodec: {
                    if (src.getSupport().getLiveStreamCodecOptions().contains(value)) {
                        appSetting.setDefaultCodec((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 13.设备所在位置*/
                case location: {
                    DeviceObjectEnumAttributes.Item item = FuncUtil.findFirst(src.getLocationOptions(), it -> it.getId(), (String) value);
                    if (item != null && src.isAdmin(input.getUserId())) {
                        device.setLocationId(Integer.valueOf(item.getId()));
                        device.setLocationName(item.getString("text"));
                        summary.getDeviceAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 14.设备名称*/
                case deviceName: {
                    if (src.isAdmin(input.getUserId())) {
                        device.setDeviceName((String) value);
                        summary.getDeviceAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 15.时区*/
                case timeZone: {
                    if (src.isAdmin(input.getUserId())) {
                        appSetting.setTimeZone((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 16.按铃通知开关*/
                case doorbellPressNotifySwitch: {
                    appSetting.setDoorbellPressNotifySwitch((Boolean) value);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 17.按铃通知方式*/
                case doorbellPressNotifyType: {
                    if (src.getDoorbellPressNotifyTypeOptions().contains(value)) {
                        appSetting.setDoorbellPressNotifyType((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 18.一键呼叫-一键呼叫开关*/
                case deviceCallNotifySwitch: {
                    appSetting.setDeviceCallToggleOn((Boolean) value);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 19.响铃设置-机械响铃开关*/
                case mechanicalDingDongSwitch: {
                    appSetting.setMechanicalDingDongSwitch((Boolean) value ? 1 : 0);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 20.响铃设置-响铃时长*/
                case mechanicalDingDongDuration: {
                    final DeviceAttributeIntRange range = src.getSupport().getMechanicalDingDongDurationRange();
                    if (range.contains((Integer) value)) {
                        appSetting.setMechanicalDingDongDuration((Integer) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 21.报警设置-运动触发报警*/
                case motionAlertSwitch: {
                    appSetting.setNeedAlarm((Boolean) value ? 1 : 0);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 22.报警设置-防拆报警开关*/
                case antiDisassemblyAlarmSwitch: {
                    appSetting.setAlarmWhenRemoveToggleOn((Boolean) value);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 23.报警设置-报警闪光灯开关*/
                case alarmFlashLightSwitch: {
                    appSetting.setWhiteLightScintillation((Boolean) value ? 1 : 0);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 24.灯光设置-指示灯开关*/
                case recLampSwitch: {
                    appSetting.setRecLamp((Boolean) value ? 1 : 0);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 25.灯光设置-夜视开关控制*/
                case nightVisionSwitch: {
                    appSetting.setNeedNightVision((Boolean) value ? 1 : 0);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 26.灯光设置-夜视灵敏度*/
                case nightVisionSensitivity: {
                    final List<String> options = src.getSupport().getNightVisionSensitivityOptions();
                    if (options != null && options.contains((String) value)) {
                        appSetting.setNightVisionSensitivityEnum((String) value);
                        appSetting.setNightVisionSensitivity(getEnumValue(enumMapping.getNightVisionSensitivityOptions(), (String) value));
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    // todo
                    break;
                }
                /* 27.灯光设置-夜视模式调节*/
                case nightVisionMode: {
                    final List<String> options = src.getSupport().getNightVisionModeOptions();
                    if (options != null && options.contains((String) value)) {
                        appSetting.setNightVisionModeEnum((String) value);
                        appSetting.setNightVisionMode(getEnumValue(enumMapping.getNightVisionModeOptions(), (String) value));
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
//                /* 28.充电自动开机-自动开机开关*/
//                case chargeAutoPowerOnSwitch: {
//                    appSetting.setChargeAutoPowerOnSwitch((Boolean) value ? 1 : 0);
//                    summary.getAppSettingAttrNames().add(attrName);
//                    break;
//                }
                /* 28.声音设置-报警时长*/
                case alarmDuration: {
                    final List<String> options = src.getSupport().getAlarmDurationOptions();
                    if (options != null && options.contains((String) value)) {
                        appSetting.setAlarmDurationEnum((String) value);
                        appSetting.setAlarmSeconds(getEnumValue(enumMapping.getAlarmDurationOptions(), (String) value));
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 29.声音设置-警铃音量*/
                case alarmVolume: {
                    final DeviceAttributeIntRange range = src.getSupport().getAlarmVolumeRange();
                    if (range.contains((Integer) value)) {
                        appSetting.setAlarmVolume((Integer) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 30.声音设置-设备语音语种*/
                case voiceLanguage: {
                    final List<String> options = src.getSupport().getDeviceSupportLanguage();
                    if (options != null && options.contains((String) value)) {
                        appSetting.setDeviceLanguage((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 31.声音设置-门铃铃音选择*/
                case doorBellRing: {
                    final List<Integer> options = src.getSupport().getSupportDoorBellRingKey();
                    if (options != null && options.contains(Integer.valueOf((String) value))) {
                        appSetting.setDoorBellRingKey(Integer.valueOf((String) value));
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 32.声音设置-提示音音量*/
                case voiceVolume: {
                    final DeviceAttributeIntRange range = src.getSupport().getVoiceVolumeRange();
                    if (range.contains((Integer) value)) {
                        appSetting.setVoiceVolume((Integer) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /* 33.声音设置-直播收音开关*/
                case liveAudioSwitch: {
                    appSetting.setLiveAudioToggleOn((Boolean) value);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 34.声音设置-录像收音开关*/
                case recordingAudioSwitch: {
                    appSetting.setRecordingAudioToggleOn((Boolean) value);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 35.供电方式*/
                case powerSource: {
                    if (getEnumNames(enumMapping.getPowerSourceOptions()).contains(value)) {
                        appSetting.setPowerSource((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /*  36.充电自动开机开关*/
                case chargeAutoPowerOnSwitch: {
                    appSetting.setChargeAutoPowerOnSwitch((Boolean) value ? 1 : 0);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /*  37.充电自动开机电量*/
                case chargeAutoPowerOnCapacity: {
                    // 目前不支持设置，固定10%
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                /* 38.声音设置-直播对讲音量*/
                case liveSpeakerVolume: {
                    final DeviceAttributeIntRange range = src.getSupport().getLiveSpeakerVolumeRange();
                    if (range.contains((Integer) value)) {
                        appSetting.setLiveSpeakerVolume((Integer) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                /** 38. pir检测结果偏好 */
                case pirAi: {
                    List<String> checkedPirAIList = (List<String>)value;
                    boolean detectPersonAi = false;
                    if(CollectionUtils.isNotEmpty(checkedPirAIList)) {
                        for(String checkedPirAI : checkedPirAIList) {
                            detectPersonAi = detectPersonAi || StringUtils.equalsIgnoreCase(checkedPirAI, "detectPersonAi");
                        }
                    }
                    appSetting.setDetectPersonAi(detectPersonAi);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }

                case otaAutoUpgrade: {
                    appSetting.setOtaAutoUpgrade((Boolean) value );
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }

                case logUpload: {
                    appSetting.setLogUpload((Boolean) value );
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }

                case wifiPowerMode: {
                    if (src.getSupport().getWifiPowerModeOptions().contains(value)) {
                        appSetting.setWifiPowerMode((String) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }
                case overallLightSwitch: {
                    appSetting.setOverallLightSwitch((Boolean) value);
                    summary.getAppSettingAttrNames().add(attrName);
                    break;
                }
                case overallLightIntensity: {
                    if (src.getSupport().getOverallLightRange().contains((Integer) value)) {
                        appSetting.setOverallLightIntensity((Integer) value);
                        summary.getAppSettingAttrNames().add(attrName);
                    } else {
                        summary.getErrorAttrs().add(attr);
                    }
                    break;
                }

                default: {
                    summary.getErrorAttrs().add(attr);
                }
            }
        }
        log.info("modifyDeviceAttributes summary={}", JSON.toJSONString(summary, true));
        return summary;
    }


    public static void addThingModelAttributeToSummary(JSONObject supportJson , ModifySummary summary, DeviceAppSettingsDO appSetting, DeviceModifiableAttribute attr, Optional<ThingModel.ThingEntity> thingEntity, Object value) {
        try {
            if ("SWITCH".equals(thingEntity.get().getType())) {
                appSetting.addToPropertyJson(attr.getName(), Boolean.TRUE.equals(attr.getValue()) ? 1 : 0);
            } else if ("ENUM".equals(thingEntity.get().getType())) {
                if (((List<String>) (supportJson.get(thingEntity.get().getOptionName()))).contains((String) value)) {
                    appSetting.addToPropertyJson(attr.getName(), attr.getValue());
                } else {
                    summary.getErrorAttrs().add(attr);
                }
            } else if ("BITENUM".equals(thingEntity.get().getType())) {
                List<String> options = getBitEnumOptions(supportJson, thingEntity.get());
                if (options.contains((String) value)) {
                    appSetting.addToPropertyJson(attr.getName(), attr.getValue());
                } else {
                    summary.getErrorAttrs().add(attr);
                }
            } else if ("RANGE".equals(thingEntity.get().getType())) {
                DeviceIntRangeAttributes intRangeAttributes = new DeviceIntRangeAttributes();
                intRangeAttributes.setIntRangeByLinkedTreeMap(supportJson.get(thingEntity.get().getOptionName()));

                if (intRangeAttributes.getIntRange().contains((Integer) value)) {
                    appSetting.addToPropertyJson(attr.getName(), value);
                } else {
                    summary.getErrorAttrs().add(attr);
                }
            } else {
                appSetting.addToPropertyJson(attr.getName(), attr.getValue());
            }
            summary.getAppSettingAttrNames().add(propertyJson);
        }catch (Exception e){
            log.error("thingModel modify error {}", attr , e);
        }
    }

    public Result modifyDeviceAttributes(DeviceAttributesModify input) {
        return modifyDeviceAttributes(input, false);
    }

    public Result modifyDeviceAttributes(DeviceAttributesModify input, boolean wait) {
        final long startTime = System.currentTimeMillis();
        final String sn = input.getSerialNumber();
        final Integer userId = input.getUserId();
        final DeviceAttributeSource src = getAttributeSource(sn);
        ModifySummary summary = createModifySummary(input, src);
        if (summary.getErrorAttrs().size() > 0) {
            return new Result(ResultCollection.INVALID_PARAMS.getCode(), "请求参数错误!", summary.getErrorAttrs());
        }
        // 先更新device
        if (!summary.getDeviceAttrNames().isEmpty()) {
            deviceService.updateDeviceInfo(summary.getDevice());
            // 通知alexa设备更名
            if (!summary.getDeviceAttrNames().isEmpty() &&
                    summary.getDevice() != null &&
                    summary.getDevice().getDeviceName() != null) {
                alexaSafemoRequestService.reportDeviceName(sn, summary.getDevice().getDeviceName());
            }
        }
        // 更新appSettings
        if (!summary.getAppSettingAttrNames().isEmpty()) {
            final DeviceAppSettingsDO appSettingsDO = summary.getAppSetting();
            final String lockKey = userConfigPre.replace("{serialNumber}", appSettingsDO.getSerialNumber());
            final Lock lock = lockRegistry.obtain(lockKey);
            boolean lockStatus = false;
            try {
                lockStatus = lock.tryLock(redisLockTimeout, TimeUnit.SECONDS);
                if (!lockStatus) {
                    com.addx.iotcamera.util.LogUtil.error(log, "modifyDeviceAttributes deviceAppSettings lock fail!");
                    return Result.Failure("获取修改设备属性锁失败! ");
                }
                return deviceSettingService.updateUserConfig(userId, appSettingsDO, startTime, wait);
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "modifyDeviceAttributes deviceAppSettings run error!" + e.getMessage(), e);
                return Result.Failure("修改设备属性异常! " + e.getMessage());
            } finally {
                if (lockStatus) {
                    try {
                        lock.unlock();
                    } catch (Exception ex) {
                        com.addx.iotcamera.util.LogUtil.error(log, "modifyDeviceAttributes deviceAppSettings unlock error!" + ex.getMessage(), ex);
                    }
                }
            }
        }
        // 先更新数据库，再执行后续操作
        if (summary.getDeviceAttrNames().contains(timedDormancySwitch)) {
            deviceDormancyPlanService.sendDeviceDormancyPlanMqtt(sn);
        }
        return Result.Success();
    }


    public Result modifyBstationAttributes(DeviceAttributesModify input) {
        final long startTime = System.currentTimeMillis();
        final String sn = input.getSerialNumber();
        final Integer userId = input.getUserId();
        final DeviceAttributeSource src = getAttributeSource(sn);
        ModifySummary summary = createModifySummary(input, src);
        if (summary.getErrorAttrs().size() > 0) {
            return new Result(ResultCollection.INVALID_PARAMS.getCode(), "请求参数错误!", summary.getErrorAttrs());
        }
        // 先更新device
        if (!summary.getDeviceAttrNames().isEmpty()) {
            deviceService.updateDeviceInfo(summary.getDevice());
        }
        // 更新appSettings
        if (!summary.getAppSettingAttrNames().isEmpty()) {
            final DeviceAppSettingsDO appSettingsDO = summary.getAppSetting();
            final String lockKey = userConfigPre.replace("{serialNumber}", appSettingsDO.getSerialNumber());
            final Lock lock = lockRegistry.obtain(lockKey);
            boolean lockStatus = false;
            try {
                lockStatus = lock.tryLock(redisLockTimeout, TimeUnit.SECONDS);
                if (!lockStatus) {
                    com.addx.iotcamera.util.LogUtil.error(log, "modifyDeviceAttributes deviceAppSettings lock fail!");
                    return Result.Failure("获取修改设备属性锁失败! ");
                }
                return deviceSettingService.updateUserBxConfig(userId, appSettingsDO, startTime);
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "modifyDeviceAttributes deviceAppSettings run error!" + e.getMessage(), e);
                return Result.Failure("修改设备属性异常! " + e.getMessage());
            } finally {
                if (lockStatus) {
                    try {
                        lock.unlock();
                    } catch (Exception ex) {
                        com.addx.iotcamera.util.LogUtil.error(log, "modifyDeviceAttributes deviceAppSettings unlock error!" + ex.getMessage(), ex);
                    }
                }
            }
        }
        // 先更新数据库，再执行后续操作
        if (summary.getDeviceAttrNames().contains(timedDormancySwitch)) {
            deviceDormancyPlanService.sendDeviceDormancyPlanMqtt(sn);
        }
        return Result.Success();
    }


    /**
     * 收到deviceSupport上报时，初始化属性值
     */
    public void initDeviceAttributes(String sn) {
        final long startTime = System.currentTimeMillis();
        log.info("initDeviceAttributes begin! sn={}", sn);
        try {
            final DeviceAttributeSource src = getAttributeSource(sn);
            deviceInfoService.setDeviceSettings(src.getSettings());
            initResolutionOnDeviceSupport(src);
            ReportInfo reportInfo = ReportInfo.builder().serialNumber(sn).startTime(startTime)
                    .operationId(CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID()).build();
            deviceSettingService.pushVermemqMessage(reportInfo, src.getSettings(), null);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "initDeviceAttributes updateUserConfig error! sn={}", sn, e);
        }

    }

    public void initResolutionOnDeviceSupport(DeviceAttributeSource src) {
        try {
            final DeviceEnumAttributes attr = getVideoResolution(src);
            if (attr.getValue() == null) {
                log.debug("initResolutionOnDeviceSupport value is null! sn={},attr={}", src.getSn(), attr);
                return;
            }
            ModifySummary summary = createModifySummary(new DeviceAttributesModify().setUserId(src.getAdminId())
                    .setSerialNumber(src.getSn()).setModifiableAttributes(Arrays.asList(attr)), src);
            if (summary.getDevice() == null) {
                log.debug("initResolutionOnDeviceSupport not modify! sn={},attr={},summary={}", src.getSn(), attr, summary);
            }
            Integer updateNum = deviceService.updateDeviceInfo(summary.getDevice());
            log.debug("initResolutionOnDeviceSupport end! sn={},updateNum={},attr={},summary={}", src.getSn(), updateNum, attr, summary);
        } catch (Exception e) {
            log.info("initResolutionOnDeviceSupport error! sn={}", src.getSn(), e);
        }
    }
    /**
     * 修复value不在options范围内的属性
     * 有些属性(pirRecordTime,pirCooldownTime)选项范围会随着套餐而变化，默认值的更新时机不好把握，而且还有有问题的存量数据
     * 因此在app查询的时候，对选项和值做一次校验，有问题的属性重发setting
     */
    private List<DeviceModifiableAttribute> prefixAttrs(DeviceAttributeSource src, List<DeviceModifiableAttribute> attrs) {
        try {
            List<String> errDescList = new LinkedList<>();
            List<DeviceModifiableAttribute> modifyAttrs = new LinkedList<>();
            for (final DeviceModifiableAttribute attr : attrs) {
                try {
                    if (attr.containsValue(attr.getValue())) continue;
                    final Object value = attr.defaultValue();
                    errDescList.add(new StringBuilder().append(attr.getName()).append(":").append(attr.getValue()).append("->").append(value).toString());
                    if (value != null) {
                        modifyAttrs.add(attr.setValue(value));
                    }
                } catch (Throwable e) {
                    com.addx.iotcamera.util.LogUtil.error(log, "prefixAttrs error! attr={}", attr, e);
                }
            }
            if (!modifyAttrs.isEmpty()) {
                log.info("prefixAttrs sn={},size={},desc={},errAttrs={}", src.getSn(), modifyAttrs.size()
                        , StringUtils.join(";", errDescList), JSON.toJSONString(modifyAttrs));
                // 不修改数据库，先给app端返回一个符合规则的值，避免app端报错
            }
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "prefixAttrs error!", e);
        }
        return attrs;
    }

    public List<DeviceModifiableAttribute> getPirCooldownSwitchAndTime(DeviceAttributeSource src) {
        final List<DeviceModifiableAttribute> attrList = new LinkedList<>();
        if (src.getSupport().getSupportPirCooldown() > 0) {
            final DeviceSwitchAttributes switchAttr = new DeviceSwitchAttributes().setName(pirCooldownSwitch.name())
                    .setValue(src.getSettings().getCooldownUserEnable());
            /* 5.运动检测-触发间隔时间*/
            DeviceEnumAttributes attr = new DeviceEnumAttributes().setName(pirCooldownTime.name())
                    .setValue(src.getSettings().getPirCooldownTime())
                    .setOptions(src.getSupport().getPirCooldownTimeOptions());
            if (src.getIsNoVipOrFreeTier2()) {
                List<Integer> coolDownValueList = FreeUserVipTier2Config.getCooldownOptionValueListBySerialNumber(src.getSn());
                List<AppFormOptionsDO.CooldownOptionValue> options = Arrays.stream(AppFormOptionsDO.CooldownOptionValue.values())
                        .filter(it -> src.getSupport().getPirCooldownTimeOptions().contains(it.getEnumName())).collect(Collectors.toList());
                attr.setOptions(options.stream().filter(pirCooldownTimeOption -> coolDownValueList.contains(pirCooldownTimeOption.getValue())).map(it -> it.getEnumName()).collect(Collectors.toList()));
                attr.setDisabledOptions(options.stream().filter(pirCooldownTimeOption -> !coolDownValueList.contains(pirCooldownTimeOption.getValue())).map(it -> it.getEnumName()).collect(Collectors.toList()));
                switchAttr.setDisabled(CollectionUtils.isNotEmpty(attr.getDisabledOptions()));
            }
            attrList.add(switchAttr);
            attrList.add(attr);
        }
        return attrList;
    }

    public List<DeviceModifiableAttribute> getPirCooldownSwitchAndTime(String sn) {
        return getPirCooldownSwitchAndTime(getAttributeSource(sn));
    }

    public DeviceEnumAttributes getVideoResolution(DeviceAttributeSource src) {
        return new DeviceEnumAttributes().setName(videoResolution.name())
                .setValue(src.getSettings().getVideoResolution())
                .setOptions(src.getSupport().getVideoResolutionOptions());
    }

}
