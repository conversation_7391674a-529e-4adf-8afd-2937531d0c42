package com.addx.iotcamera.service.videofile;

import com.addx.iotcamera.bean.video.VideoClearInfo;
import com.addx.iotcamera.bean.videofile.BxStorageInfo;
import com.addx.iotcamera.bean.videofile.StorageInfo;
import com.addx.iotcamera.bean.videofile.VideoClearParams;
import com.addx.iotcamera.bean.videofile.VideoDbCount;
import com.addx.iotcamera.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.dao.library.IShardingLibraryStatusDAO;
import com.addx.iotcamera.dao.library.IShardingVideoSliceDAO;
import com.addx.iotcamera.helper.DataSourceHelper;
import com.addx.iotcamera.service.VideoConfig;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.apache.commons.io.FileUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
@Component
@Lazy
public class VideoClearService {

    @Setter
    @Autowired
    @Lazy
    private StorageClearService storageClearService;

    @Setter
    @Autowired
    @Lazy
    private VideoConfig config;

    @Setter
    @Autowired
    @Lazy
    private DataSourceHelper dataSourceHelper;

    @Autowired
    @Lazy
    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;

    @PostConstruct
    public void init() {
        initClearVideoTask();
    }

    private void initClearVideoTask() {
        scheduledThreadPoolExecutor.scheduleWithFixedDelay(() -> {
            MDC.put("requestId", OpenApiUtil.shortUUID());
            Result<Map<String, Result<VideoClearInfo>>> result = this.clearVideo();
            log.info("clearVideoTask end! result={}", JSON.toJSONString(result));
        }, config.getClearVideo().getDelay(), config.getClearVideo().getInterval(), config.getClearVideo().getTimeUnit());
        scheduledThreadPoolExecutor.scheduleWithFixedDelay(() -> {
            MDC.put("requestId", OpenApiUtil.shortUUID());
            Map<String, DeleteVideoResult> result = clearFile();
            log.info("clearFileTask end! result={}", JSON.toJSONString(result));
        }, config.getClearFile().getDelay(), config.getClearFile().getInterval(), config.getClearFile().getTimeUnit());
    }

    public Map<String, DeleteVideoResult> clearFile() {
        final List<StorageInfo> storageList = storageClearService.getBxStorageInfo().getStorageList();
        Map<String, DeleteVideoResult> map = new LinkedHashMap<>();
        for (StorageInfo storage : storageList) {
            map.put(storage.getStorageId(), storageClearService.deleteByFs(storage));
        }
        return map;
    }

    /**
     * 清理存储空间
     */
    public Result<Map<String, Result<VideoClearInfo>>> clearVideo() {
        final BxStorageInfo bxStorageInfo = storageClearService.getBxStorageInfo();
        if (bxStorageInfo.getStorage() == null) {
            return Result.Failure("基站尚未绑定!");
        }
        limitLibraryDbFile(bxStorageInfo.getStorageList(), config.getClearParams()); // 控制libraryDb文件大小
        return clearStorageList(bxStorageInfo.getStorageList(), config.getClearParams());
    }

    private AtomicBoolean clearStorageListLock = new AtomicBoolean(false);

    public Result<Map<String, Result<VideoClearInfo>>> clearStorageList(List<StorageInfo> storageList, VideoClearParams params) {
        final Map<String, Result<VideoClearInfo>> storageId2Result = new LinkedHashMap<>();
        if (CollectionUtils.isEmpty(storageList)) return new Result<>(storageId2Result);
        boolean isLock = false;
        try {
            if (!(isLock = clearStorageListLock.compareAndSet(false, true))) {
                log.info("clearStorageList not get lock!");
                return new Result<>(201, "视频清理逻辑正在执行!", storageId2Result);
            }
            // 按存储优先顺序的逆序进行清理
            // [0]: [0,null]
            // [0,1] : [0,1]
            // [0,1,2] : [1,2] -> [0,1]
            for (int i = Math.max(storageList.size() - 2, 0); i >= 0; i--) {
                final StorageInfo storage = storageList.get(i);
                final StorageInfo extStorage = i + 1 < storageList.size() ? storageList.get(i + 1) : null;
                final Result<VideoClearInfo> result = clearStoragePair(storage, extStorage, params);
                storageId2Result.put(storage.getStorageId(), result);
                // 重新设置usableBytes的值，下一个循环中会用到
                storage.setUsableBytes(result.getData().getUsableBytes());
            }
            dataSourceHelper.vacuum("library"); // 释放library的空间
            return new Result<>(storageId2Result);
        } catch (Throwable e) {
            log.error("clearStorageList error! storageList={},params={},info={}", storageList, params, e);
            return new Result<>(Result.operationFailedFlag, "视频清理逻辑执行异常!", storageId2Result);
        } finally {
            if (isLock) {
                clearStorageListLock.set(false);
            }
        }
    }

    /**
     * 清理存储器对
     *
     * @param storage    当前要清理的存储器，不能为null
     * @param extStorage 当前存储器的扩展存储器，可能为null
     * @param params     视频清理的参数
     * @return 例如，emmc需要清理500M空间，ssd 只有300M可用
     * 1、先在ssd 删200M，结果只删了100M（400M可用）
     * 2、就清空ssd视频根目录 （表里不存在的情况）
     * 3、如果此时ssd只有450M 可用，就从emmc里删除50M，转移450M最老的数据到ssd，成为ssd里最新的数据
     * <p>
     * ReplyCreate taskCreate Jira issueLike41 mins ago
     */
    public Result<VideoClearInfo> clearStoragePair(final StorageInfo storage, final StorageInfo extStorage, final VideoClearParams params) {
        Objects.requireNonNull(storage);
        Objects.requireNonNull(params);
        final VideoClearInfo info = new VideoClearInfo(storage.getMinUsableBytes()
                , storage.getUsableBytes(), extStorage != null ? extStorage.getUsableBytes() : 0L);
        final long beginTime = System.currentTimeMillis();
        try {
            log.info("clearStoragePair begin! storage={},extStorage={},params={}", storage, extStorage, params);
            if (info.getNeedClearBytes() <= 0) {
                return new Result<>(Result.successFlag, "storage不需要清理! storageId=" + storage.getStorageId(), info);
            }
            if (extStorage != null) {
                // extStorage中可用空间不足，删除extStorage部分数据，补足缺少的空间
                if (info.extUsableBytes < info.getNeedClearBytes()) {
                    final long destDeleteExtBytes = info.getNeedClearBytes() - info.extUsableBytes;
                    info.deleteExtBytes = storageClearService.delete(extStorage, destDeleteExtBytes, params.epochQueryNum).getDeleteVideoFileSize();
                    info.extUsableBytes += info.deleteExtBytes;
                }
                // extStorage中可用空间不足，删除extStorage视频根目录的全部数据
                if (info.extUsableBytes < info.getNeedClearBytes()) {
                    info.deleteAllExtBytes = storageClearService.deleteAll(extStorage).getDeleteVideoFileSize();
                    info.extUsableBytes += info.deleteAllExtBytes;
                }
                // extStorage中可用空间不足，先删除storage部分数据，补足缺少的空间
                if (info.extUsableBytes < info.getNeedClearBytes()) {
                    final long destDeleteBytes = info.getNeedClearBytes() - info.extUsableBytes;
                    info.deleteBytes = storageClearService.delete(storage, destDeleteBytes, params.epochQueryNum).getDeleteVideoFileSize();
                    info.usableBytes += info.deleteBytes;
                }
                // 向extStorage可用空间转移数据，能转移多少转移多少
                final long destTransferBytes = Math.min(info.getNeedClearBytes(), info.extUsableBytes);
                info.transferBytes = storageClearService.transfer(storage, extStorage, destTransferBytes, params.epochQueryNum).getDeleteVideoFileSize();
                info.extUsableBytes -= info.transferBytes;
                info.usableBytes += info.transferBytes;
            }
            if (info.getNeedClearBytes() > 0) {
                // 无法向extStorage转移，删除storage自身的数据
                info.deleteBytes = storageClearService.delete(storage, info.getNeedClearBytes(), params.epochQueryNum).getDeleteVideoFileSize();
                info.usableBytes += info.deleteBytes;
            }
            info.costTime = System.currentTimeMillis() - beginTime;
            log.info("clearStoragePair end! storage={},extStorage={},params={},info={}", storage, extStorage, params, info);
            return new Result<>(info);
        } catch (Throwable e) {
            info.costTime = System.currentTimeMillis() - beginTime;
            log.error("clearStoragePair error! storage={},extStorage={},params={},info={}", storage, extStorage, params, info, e);
            return new Result<>(Result.failureFlag, "清理视频发生异常! storageId=" + storage.getStorageId(), info);
        }
    }

    @Async("commonPool")
    public void deleteAllOnSsdFormat(Integer userId, String serialNumber) {
        try {
            /** 发起ssd格式化时，先清空对应的视频表数据。不用管格式化是否成功 */
            final StorageInfo ssdStorage = storageClearService.getBxStorageInfo().getSsdStorage();
            if (ssdStorage != null) {
                storageClearService.deleteAll(ssdStorage);
            }
            log.info("deleteAllOnSsdFormat end! userId={},serialNumber={}", userId, serialNumber);
        } catch (Throwable e) {
            log.error("deleteAllOnSsdFormat error! userId={},serialNumber={}", userId, serialNumber, e);
        }
    }

    @Autowired
    @Lazy
    private IShardingLibraryDAO libraryDAO;
    @Autowired
    @Lazy
    private IShardingLibraryStatusDAO libraryStatusDAO;
    @Autowired
    @Lazy
    private IShardingVideoSliceDAO shardingVideoSliceDAO;

    @Value("${video-file.storage.rootPath}")
    private String rootPath;
    @Value("${video-file.limit-library-db.singleVideoBytes}")
    private long limitLibraryDbSingleVideoBytes; // 单个视频在library.db中约占1KB大小
    @Value("${video-file.limit-library-db.deleteVideoNum}")
    private long limitLibraryDbDeleteVideoNum; // 每次删除128个视频
    @Value("${video-file.limit-library-db.maxBytes}")
    private long limitLibraryDbMaxBytes; // 单个视频约占1MB空间。2T空间能存2048*1024个视频，需要libraryDB空间2G

    public DeleteVideoResult limitLibraryDbFile(List<StorageInfo> storageList, final VideoClearParams params) {
        Objects.requireNonNull(params);
        DeleteVideoResult totalResult = new DeleteVideoResult();
        try {
            final File libraryDbFile = new File(rootPath, "sqlite/library.db");
            final long rawLibraryDbSize = FileUtils.sizeOf(libraryDbFile);
            final long needDeleteBytes = rawLibraryDbSize - limitLibraryDbMaxBytes;
            final long needDeleteVideoNum = needDeleteBytes / limitLibraryDbSingleVideoBytes; // 根据单个视频索引大小，估算应该删除的视频数量
            log.info("limitLibraryDbFile total begin! libraryDbFile={},rawLibraryDbSize={},needDeleteBytes={},needDeleteVideoNum={}", libraryDbFile, rawLibraryDbSize, needDeleteBytes, needDeleteVideoNum);
            if (needDeleteVideoNum < 0L) {
                return totalResult;
            }
            final Set<String> storageIdSet = storageList.stream().map(it -> it.getStorageId()).collect(Collectors.toSet());
            // 优先删除被拔出的ssd中的视频，优先删除其中时间较早的
            final List<VideoDbCount> videoDbCounts = libraryDAO.countByStorageId();
            Collections.sort(videoDbCounts, Comparator.comparingInt(it -> it.getMinTimestamp()));
            for (VideoDbCount videoDbCount : videoDbCounts) {
                final String storageId = videoDbCount.getStorageId();
                if (storageIdSet.contains(storageId)) continue;
                final DeleteVideoResult result = new DeleteVideoResult();
                result.deleteVideoNum += libraryDAO.deleteByStorageId(storageId);
                result.deleteLibraryStatusNum += libraryStatusDAO.deleteByStorageId(storageId);
                result.deleteSliceNum += shardingVideoSliceDAO.deleteSliceByStorageId(storageId);
                result.deleteVideoObjectNum += storageClearService.deleteVideoObjectByStorageId(storageId);
                totalResult.merge(result);
                final boolean vacuumed = dataSourceHelper.vacuum("library");
                final long libraryDbSize = FileUtils.sizeOf(libraryDbFile);
                log.info("limitLibraryDbFile delete removed storage end! storageId={},vacuumed={},libraryDbSize={},result={},totalResult={}", storageId, vacuumed, libraryDbSize, result, totalResult);
                if (totalResult.getDeleteVideoNum() >= needDeleteVideoNum || libraryDbSize <= limitLibraryDbMaxBytes) { // 同时判断删除的视频数和libraryDb大小，避免vacuum不成功导致多删视频
                    return totalResult;
                }
            }
            // 按storage的逆序删除视频，直到删除视频数 或 视频库文件大小满足要求
            for (int i = storageList.size() - 1; i >= 0; i--) { // 倒序
                final StorageInfo storage = storageList.get(i);
                for (int j = 0; true; j++) {
                    final long destDeleteVideoNum = Math.min(needDeleteVideoNum - totalResult.getDeleteVideoNum(), limitLibraryDbDeleteVideoNum);
                    DeleteVideoResult result = storageClearService.delete(storage, res -> res.deleteVideoNum >= destDeleteVideoNum, "destDeleteVideoNum=" + destDeleteVideoNum, params.getEpochQueryNum());
                    totalResult.merge(result);
                    boolean vacuumed = dataSourceHelper.vacuum("library");
                    final long libraryDbSize = FileUtils.sizeOf(libraryDbFile);
                    log.info("limitLibraryDbFile delete existed storage end! storageId={},j={},vacuumed={},libraryDbSize={},result={},totalResult={}", storage.getStorageId(), j, vacuumed, libraryDbSize, result, totalResult);
                    if (totalResult.getDeleteVideoNum() >= needDeleteVideoNum || libraryDbSize <= limitLibraryDbMaxBytes) { // 同时判断删除的视频数和libraryDb大小，避免vacuum不成功导致多删视频
                        return totalResult;
                    }
                    if (result.getDeleteVideoNum() < limitLibraryDbDeleteVideoNum) {
                        break; // 删除的视频数量未到达预期，就调到下一个storage
                    }
                }
            }
            log.info("limitLibraryDbFile total end! totalResult={}", totalResult);
        } catch (Throwable e) {
            log.error("limitLibraryDbFile total error! totalResult={}", totalResult, e);
        }
        return totalResult;
    }

}
