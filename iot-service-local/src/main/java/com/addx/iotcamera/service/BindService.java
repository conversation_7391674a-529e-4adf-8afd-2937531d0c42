package com.addx.iotcamera.service;

import com.addx.biz.SecurityModeBiz;
import com.addx.iotcamera.bean.app.BindByApOperationRequest;
import com.addx.iotcamera.bean.app.BindDeviceResult;
import com.addx.iotcamera.bean.app.BindErrorRequest;
import com.addx.iotcamera.bean.app.BindOperationRequest;
import com.addx.iotcamera.bean.app.user.BindDeviceCloudUserInfo;
import com.addx.iotcamera.bean.app.user.LoginRequest;
import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.db.FirmwareTableDO;
import com.addx.iotcamera.bean.db.device.DeviceBindCode;
import com.addx.iotcamera.bean.device.DeviceConnectReport;
import com.addx.iotcamera.bean.device.DeviceModelSettingDO;
import com.addx.iotcamera.bean.device.attributes.OptionEnumMapping;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.DeviceBindStep;
import com.addx.iotcamera.bean.mqtt.Request.MqttDeviceBindRequest;
import com.addx.iotcamera.bean.videofile.BxStorageInfo;
import com.addx.iotcamera.config.apollo.DeviceLanguageConfig;
import com.addx.iotcamera.config.device.DeviceCameraNameConfig;
import com.addx.iotcamera.config.device.DeviceSettingConfig;
import com.addx.iotcamera.constants.DeviceReportKeyConstants;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.dao.LocalSettingDAO;
import com.addx.iotcamera.dao.device.IDeviceBindErrorDAO;
import com.addx.iotcamera.enums.*;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.helper.IpHelper;
import com.addx.iotcamera.helper.MyLockRegistry;
import com.addx.iotcamera.helper.TraceIdHelper;
import com.addx.iotcamera.helper.VideoSearchCacheHelper;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.publishers.vernemq.requests.SetParameterRequest;
import com.addx.iotcamera.publishers.vernemq.requests.SetRetainParamRequest;
import com.addx.iotcamera.publishers.vernemq.responses.MqttBstationDeviceBindResponse;
import com.addx.iotcamera.publishers.vernemq.responses.MqttDeviceBindResponse;
import com.addx.iotcamera.publishers.vernemq.responses.MqttResponseCode;
import com.addx.iotcamera.service.device.*;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.openapi.NodeMatcherAgency;
import com.addx.iotcamera.service.user.UserAppScoreService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.video.VideoAIService;
import com.addx.iotcamera.service.video.VideoGenerateService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.service.videofile.LocalSpaceService;
import com.addx.iotcamera.service.videofile.StorageClearService;
import com.addx.iotcamera.service.videofile.VideoFileService;
import com.addx.iotcamera.util.MapUtil;
import com.addx.iotcamera.util.QRcodeUtil;
import com.addx.kiss.service.impl.WssReportEventServiceImpl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import io.grpc.Grpc;
import io.grpc.InsecureChannelCredentials;
import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.exception.BaseException;
import org.addx.iot.common.proto.SendResult;
import org.addx.iot.common.proto.bstationd.BStationDeactivateProcessorGrpc;
import org.addx.iot.common.proto.bstationd.DeviceDeActiveMessage;
import org.addx.iot.common.thingmodel.ThingModel;
import org.addx.iot.common.thingmodel.ThingModelConfig;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.service.ICopywriteLanguageService;
import org.addx.iot.domain.extension.core.IExtensionService;
import org.addx.iot.domain.user.service.IUserService;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.common.enums.ResultCollection.DEVICE_BSTATIOND_UNACTIVATED;
import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;
import static com.addx.iotcamera.constants.DeviceInfoConstants.DEVICE_CLOUD_ADDRESS_KEY;
import static com.addx.iotcamera.constants.DeviceModelSettingConstants.*;
import static com.addx.iotcamera.constants.ReportLogConstants.*;
import static com.addx.iotcamera.service.device.DeviceAbilityService.NOT_INIT_MODE;
import static org.addx.iot.common.constant.AppConstants.APP_LANGUAGE_EN;
import static org.addx.iot.common.enums.ResultCollection.DEVICE_UNACTIVATED;
import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;

@Component @Lazy
@Slf4j
public class BindService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BindService.class);

    private static final int DEFAULT_ANTIFLICKER = 60;
    public static final int DEVICE_CONNECT_TIMEOUT = 3600;

    private static final int defaultBindType = 0;

    //同一个wifi，连接本地kiss时发消息需要
    public static final String WEBSOCKET_LINK_TOKEN_KEY = "websocket::linkToken";


    @Autowired @Lazy
    private ActivityZoneService activityZoneService;

    @Autowired @Lazy
    private IDeviceDAO deviceDAO;

    @Autowired @Lazy
    private UserRoleService userRoleService;

    @Autowired @Lazy
    private BxBindService bxBindService;

    @Autowired @Lazy
    private VideoSearchService videoSearchService;

    @Autowired @Lazy
    private DeviceInfoService deviceInfoService;

    @Autowired @Lazy
    private DeviceAttributeService deviceAttributeService;

    @Autowired @Lazy
    private FirmwareService firmwareService;

    @Autowired @Lazy
    private FactoryDataQueryService factoryDataQueryService;

    @Autowired @Lazy
    private NotificationService notificationService;

    @Autowired @Lazy
    private LocalSpaceService localSpaceService;

    @Autowired @Lazy
    private StorageClearService storageClearService;

    @Autowired @Lazy
    private UserVipService userVipService;
    @Autowired @Lazy
    private VipService vipService;

    @Autowired @Lazy
    private IDeviceBindErrorDAO iDeviceBindErrorDAO;

    @Autowired @Lazy
    private ReportLogService reportLogService;

    @Autowired @Lazy
    private LocationInfoService locationInfoService;

    @Autowired @Lazy
    private DeviceSettingService deviceSettingService;

    @Autowired @Lazy
    private DeviceLanguageConfig deviceLanguageConfig;
    @Autowired @Lazy
    private UserService userService;

    @Autowired @Lazy
    private ICopywriteLanguageService copywriteLanguageService;

    @Autowired @Lazy
    private RotationPointService rotationPointService;

    @Resource @Lazy
    private DeviceManualService deviceManualService;

    @Autowired @Lazy
    private NodeMatcherAgency nodeMatcherAgency;

    @Autowired @Lazy
    private RedisService redisService;

    @Autowired @Lazy
    private DeviceBindCodeService deviceBindCodeService;

    @Autowired @Lazy
    private MessageNotificationSettingsService messageNotificationSettingsService;

    @Autowired @Lazy
    private DeviceAiSettingsService deviceAiSettingsService;

    @Autowired @Lazy
    private DeviceDormancyPlanService deviceDormancyPlanService;

    @Autowired @Lazy
    private DeviceService deviceService;
    @Autowired @Lazy
    private DeviceStatusService deviceStatusService;
    @Autowired @Lazy
    private TokenService tokenService;

    @Resource @Lazy
    private DeviceModelConfigService deviceModelConfigService;

    @Autowired @Lazy
    private DeviceCameraNameConfig deviceCameraNameConfig;

    @Resource @Lazy
    private DeviceSettingConfig deviceSettingConfig;
    @Autowired @Lazy
    private LocalSettingDAO localSettingDAO;
    @Autowired @Lazy
    private VideoFileService videoFileService;
    @Autowired @Lazy
    private IpHelper ipHelper;
    @Autowired @Lazy
    private MyLockRegistry myLockRegistry;
    @Autowired @Lazy
    private VideoGenerateService videoGenerateService;

    private static final String deviceZhLanguage = "zh";
    private static final String deviceCnLanguage = "cn";
    private static final String deviceEnLanguage = "en";

    @Resource @Lazy
    private DevicePreBindService devicePreBindService;

    @Resource @Lazy
    private UserAppScoreService userAppScoreService;

//    @Autowired @Lazy
//    private UserTierDeviceService userTierDeviceService;

    @Resource @Lazy
    private LoginService loginService;

    @Value("${grpc.bstationd-service}")
    private String grpcBstationdService;

    @Resource
    @Lazy
    private DeviceOTAService deviceOTAService;

    @Resource
    @Lazy
    private UserSettingService userSettingService;

    @Resource
    @Lazy
    private DeviceRelationshipService deviceRelationshipService;

    @Resource
    @Lazy
    IExtensionService extensionService;

    @Resource
    @Lazy
    SecurityModeBiz securityModeBiz;
    @Autowired
    @Lazy
    private DeviceSupportService deviceSupportService;
    @Autowired
    private VideoStoreService videoStoreService;
    @Autowired
    @Lazy
    private VideoAIService videoAIService;

    @Autowired
    @Lazy
    IUserService iUserService;

    @Autowired @Lazy
    private ThingModelConfig thingModelConfig;

    public Result<UserRoleDO> deactivateDeviceBySerialNumber(String serialNumber) {
        Result<UserRoleDO> result = this.deactivateDeviceBySerialNumber(serialNumber, true);
        Integer adminUserId = iUserService.queryAdminUserId();
        if (Result.successFlag.equals(result.getResult())) {
            // 用户主动解绑bx设备成功时清空所有视频数据和文件
            if (result.getData().getBaseStation().equals(1)) {
                // 清理视频数据前先把存储路径查出来
                final BxStorageInfo bxStorageInfo = storageClearService.getBxStorageInfoByBxSn(serialNumber);
                extensionService.uninstallAllExtension(adminUserId);
                // 直接清空extension表数据
                extensionService.clearAllExtension();

                securityModeBiz.cleanAllModeSetting();
                //场景模式变成未初始化状态
                userSettingService.changeMode(result.getData().getUserId(), NOT_INIT_MODE);
                // 清除bx设备数据，放到倒数第二
                deviceReset(serialNumber);
                // 最后清理视频数据
                localSpaceService.videoReset(bxStorageInfo);
            } else {
                securityModeBiz.cleanModeSetting(serialNumber);
            }
        }
        return result;
    }

    // active: 是否是用户主动发起的解绑
    public Result<UserRoleDO> deactivateDeviceBySerialNumber(String serialNumber,boolean active) {
        UserRoleDO userRoleDO = userRoleService.getDeviceAdminUserRole(serialNumber);

        if (userRoleDO == null) {
            LOGGER.info("设备未绑定,{}", serialNumber);
            return Result.Error(DEVICE_UNACTIVATED, "DEVICE_UNACTIVATED");
        }
        Integer adminId = userRoleDO.getAdminId();

        List<Integer> userList = userRoleService.findAllUsersForDevice(serialNumber).stream()
                .filter(uid -> !uid.equals(adminId))
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(userList)){
            videoSearchService.clearSearchOptionCache(userList);
        }

        // 清除用户角色表
        this.deleteDeviceUserRole(serialNumber,adminId,active);

        //解绑Bx 清理admin登录token
        if(userRoleDO.getBaseStation().equals(1)){
            tokenService.deleteUserToken(String.valueOf(adminId));
        }

        String key = DEVICE_CLOUD_ADDRESS_KEY.replace("serialNumber",serialNumber);
        redisService.delete(key);

        //删除分享记录
        deviceRelationshipService.deleteDeviceShare(serialNumber);

        // 清空Activity Zone
        activityZoneService.deleteActivityZoneBySerialNumber(serialNumber);

        // 重置设备绑定信息
        Integer res = deviceInfoService.deactivateDevice(serialNumber);

        try {
            //删除该设备人型检测设置
            notificationService.deleteMessageNotificationSettings(serialNumber, adminId);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "设备删除设置,serialNumber:{}", serialNumber);
        }

        if (res < 1) {
            return Result.Error(-101, "Failed to deactivate device");
        }

        //cx解绑，解除预绑定关系
        boolean isBstationd = userRoleDO.getBaseStation().equals(1);
        devicePreBindService.deletePreBindRelation(isBstationd ? serialNumber : null, isBstationd ? null : serialNumber);

        //通知bstationd 设备解绑
        if(active && isBstationd){
            this.sendDeActiveByGrpc(serialNumber);
        }

        sendReportToMatterForCxDevices(userRoleDO); // 通知matter:bx或cx解绑

        if(isBstationd){
            // 通知ai-staion BX被解绑了
            videoAIService.sendBxUnBindEvent();
        }
        
        return new Result(userRoleDO);
    }

    /**
     * 删除设备关系
     * @param serialNumber
     * @param adminId
     * @param active
     */
    private void deleteDeviceUserRole(String serialNumber,Integer adminId,Boolean active){
        userRoleService.cleanUserRole(serialNumber,adminId);

        List<UserRoleDO> userRoleDOList = userRoleService.queryUserRoleByAssociationSn(serialNumber);
        if(CollectionUtils.isEmpty(userRoleDOList)){
            return;
        }
        //Bx解绑，需要将bx下的cx 也都解绑
        for(UserRoleDO userRoleDO : userRoleDOList){
            this.deactivateDeviceBySerialNumber(userRoleDO.getSerialNumber(),active);
        }
    }


    public void sendDeActiveByGrpc(String serialNumber) {
        if (org.apache.commons.lang3.StringUtils.isBlank(grpcBstationdService)) {
            return;
        }

        final ManagedChannel channel = Grpc.newChannelBuilder(grpcBstationdService, InsecureChannelCredentials.create()).build();
        final BStationDeactivateProcessorGrpc.BStationDeactivateProcessorBlockingStub blockingStub = BStationDeactivateProcessorGrpc.newBlockingStub(channel);
        try {
            final SendResult result = blockingStub.send(DeviceDeActiveMessage.newBuilder().setSerialNumber(serialNumber).build());
            log.info("sendDeActiveByGrpc end! grpc input={},result={}", serialNumber, result);
        } catch (Throwable e) {
            log.error("sendDeActiveByGrpc fail! grpc input={}", serialNumber, e);
        } finally {
            channel.shutdown();
        }
    }


    public Result bindDeviceFromApp(Integer userId, BindOperationRequest request, IpInfo ipInfo) {
        String operationId = this.bindOperation(userId, request, ipInfo);
        return Result.KVResult("operationId", operationId);
    }

    private static String createBindOperationId(Integer userId) {
        final String zeros = "000000";
        String prefix = TraceIdHelper.toAlphanumeric(userId); // max: '1BCkl2'(<=6位)
        String postfix = RandomStringUtils.randomAlphanumeric(10); // =16位
        return zeros.substring(6 - prefix.length()) + postfix; // =16位
    }

    private BindOperationTb queryBindOperation(Integer userId, BindOperationRequest request) {
        Integer locationId = request.getLocationId();
        // 绑定流程重构后，绑定请求中的locationId设置为-1来表示不合法的情况。没有新增接口的原因，是我实在不想再和App浪费口舌在和这种争论上了
        if (locationId == null || locationId == -1) {
            List<LocationDO> locations = locationInfoService.listUserLocations(userId);
            // 用户没有创建过任何地点时，根据App语言创建默认地址
            if (CollectionUtils.isEmpty(locations)) {
                LocationDO defaultLocation = locationInfoService.insertLocationReturnId(
                        locationInfoService.createDefaultLocationByLanguageAndAdminId(request.getLanguage(), userId, request.getApp().getTenantId()));
                locationId = defaultLocation.getId();
            } else {
                locationId = locations.get(0).getId(); // 如果用户有创建过地点的话，则选择第一个地点。后面的绑定中仍然会进行编辑，只是为了防止绑定过程中App crash等情况
            }
        }

        //获取绑定码生成时间
        BindOperationTb operationTb = BindOperationTb.builder()
                .operationId(request.getOperationId())
                .userId(userId)
                .locationId(locationId)
                .deviceLanguage(getAppDeviceLanguage(request.getLanguage()))
                .timeZone(request.getTimeZone())
                .ip("")
                .bindCode(request.getBindCode())
                .tenantId(request.getApp() == null ? "" : request.getApp().getTenantId())
                .appType(request.getApp() == null ? "" : request.getApp().getAppType())
                .version(request.getApp() == null ? "" : request.getApp().getVersionName())
                .bindType(request.getType())
                .bindContentSrc(request.getBindContentSrc())
                .deviceNetType(request.getDeviceNetType())
                .bindCodeTimestamp(0L)
                .build();

        LOGGER.info(String.format("Binding device for user %d to location: %d", userId, locationId));

        // 更新绑定操作信息表
        deviceDAO.initBindOperation(operationTb);

        reportLogService.sysReportBind(REPORT_TYPE_GENERATE_BIND_OPERATION,
                MapUtil.builder()
                        .put("bindOperationId", operationTb.getOperationId())
                        .put("userId", userId)
                        .put("bindCode", request.getBindCode())
                        .put("tenantId", request.getApp() == null ? "" : request.getApp().getTenantId())
                        .build()
        );
        return operationTb;
    }

    private String bindOperation(Integer userId, BindOperationRequest request, IpInfo ipInfo) {
        Integer locationId = request.getLocationId();

        // 绑定流程重构后，绑定请求中的locationId设置为-1来表示不合法的情况。没有新增接口的原因，是我实在不想再和App浪费口舌在和这种争论上了
        if (locationId == null || locationId == -1) {
            List<LocationDO> locations = locationInfoService.listUserLocations(userId);
            // 用户没有创建过任何地点时，根据App语言创建默认地址
            if (CollectionUtils.isEmpty(locations)) {
                LocationDO defaultLocation = locationInfoService.insertLocationReturnId(
                        locationInfoService.createDefaultLocationByLanguageAndAdminId(request.getLanguage(), userId, request.getApp().getTenantId()));
                locationId = defaultLocation.getId();
            } else {
                locationId = locations.get(0).getId(); // 如果用户有创建过地点的话，则选择第一个地点。后面的绑定中仍然会进行编辑，只是为了防止绑定过程中App crash等情况
            }
        }

        //获取绑定码生成时间
        String bindCodeTimestamp = StringUtils.isEmpty(request.getBindCode()) ? "" : redisService.get(DeviceReportKeyConstants.DEVICE_BIND_CODE_KEY.replace("{bindCode}", request.getBindCode()));
        BindOperationTb operationTb = BindOperationTb.builder()
                .userId(userId)
                .locationId(locationId)
                .deviceLanguage(getAppDeviceLanguage(request.getLanguage()))
                .timeZone(request.getTimeZone())
                .ip(ipInfo.getIp())
                .bindCode(request.getBindCode())
                .tenantId(request.getApp().getTenantId())
                .appType(request.getApp().getAppType())
                .version(request.getApp().getVersion().toString())
                .bindType(request.getType())
                .bindContentSrc(request.getBindContentSrc())
                .deviceNetType(request.getDeviceNetType())
                .bindCodeTimestamp(StringUtils.isEmpty(bindCodeTimestamp) ? 0 : Long.valueOf(bindCodeTimestamp))
                .build();

        LOGGER.info(String.format("Binding device for user %d to location: %d", userId, locationId));

        // 更新绑定操作信息表
        for (int i = 0; true; i++) {
            try {
                operationTb.setOperationId(createBindOperationId(userId));
                deviceDAO.initBindOperation(operationTb);
                break;
            } catch (DuplicateKeyException e) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "bindOperation operationId重复! traceId={},i={}", operationTb.getOperationId(), i, e);
                if (i >= 2) throw new RuntimeException("绑定操作保存失败!", e);
            }
        }
        reportLogService.sysReportBind(REPORT_TYPE_GENERATE_BIND_OPERATION,
                MapUtil.builder()
                        .put("bindOperationId", operationTb.getOperationId())
                        .put("userId", userId)
                        .put("deviceIp", ipInfo.getIp())
                        .put("bindCode", request.getBindCode())
                        .put("tenantId", request.getApp().getTenantId())
                        .build()
        );
        return operationTb.getOperationId();
    }

    private String getAppDeviceLanguage(String language) {
        //设备不支持的默认显示英语
        String deviceLanguage = !deviceLanguageConfig.getConfig().containsKey(language) ? deviceEnLanguage : language;

        //设备语言 zh 转 cn
        return deviceZhLanguage.equals(deviceLanguage) ? deviceCnLanguage : deviceLanguage;
    }


    public Result checkBindOperation(String bindOperationId, int userId, IpInfo ipInfo) {

        BindOperationTb operationTb = deviceDAO.selectOperation(bindOperationId);

        reportLogService.sysReportBind(REPORT_TYPE_CHECK_BIND_OPERATION,
                MapUtil.builder()
                        .put("bindOperationId", bindOperationId)
                        .put("userId", userId)
                        .put("deviceIp", ipInfo.getIp())
                        .put("answered", operationTb.getAnswered())
                        .put("statusCode", operationTb.getStatusCode())
                        .put("tenantId", operationTb.getTenantId())
                        .build()
        );

        if (operationTb.getAnswered() == 0) {
            return ResultCollection.REQUEST_NOT_HANDLED.getResult();
        } else if (operationTb.getStatusCode() != 0) {
            return ResultCollection.getResult(operationTb.getStatusCode());
        } else {
            return Result.KVResult("serialNumber", operationTb.getSerialNumber());
        }
    }

    /**
     * 绑定check v1
     *
     * @param bindOperationIds
     * @param userId
     * @param ipInfo
     * @return
     */
    public Result checkBindOperationStep(String bindOperationIds, int userId, IpInfo ipInfo) {
        if (StringUtils.isEmpty(bindOperationIds)) {
            throw new BaseException(INVALID_PARAMS, "bindOperationIds 不能为空");
        }
        String[] operationArray = bindOperationIds.split(",");

        DeviceBindStep deviceBindStepTemp = null;
        for (String bindOperationId : operationArray) {
            DeviceBindStep deviceBindStep = this.getDeviceBindSep(bindOperationId, userId, ipInfo);

            deviceBindStepTemp = deviceBindStepTemp == null ? deviceBindStep :
                    deviceBindStepTemp.getDeviceBindStep() < deviceBindStep.getDeviceBindStep() ? deviceBindStep : deviceBindStepTemp;
        }

        return new Result(deviceBindStepTemp);
    }

    private DeviceBindStep getDeviceBindSep(String bindOperationId, int userId, IpInfo ipInfo) {
        String key = DeviceReportKeyConstants.DEVICE_CONNECT_KEY.replace("{operationId}", bindOperationId);
        String step = redisService.get(key);
        reportLogService.sysReportBind(REPORT_TYPE_CHECK_BIND_OPERATION_STEP,
                MapUtil.builder()
                        .put("bindOperationId", bindOperationId)
                        .put("userId", userId)
                        .put("deviceIp", ipInfo.getIp())
                        .put("step", step)
                        .build()
        );
        Integer deviceBindStep = StringUtils.isEmpty(step) ? DeviceBindStatusEnums.PREPARE.getCode() : Integer.valueOf(step);
        String serialNumber = "";
        if (deviceBindStep == DeviceBindStatusEnums.INIT.getCode()) {
            BindOperationTb operationTb = deviceDAO.selectOperation(bindOperationId);
            if (StringUtils.isEmpty(operationTb.getSerialNumber())) {
                //未查询到设备序列号，有可能设备绑定操作还未完成（事务）,需要等到整个操作完成才算绑定完成
                deviceBindStep = DeviceBindStatusEnums.BIND.getCode();
                LOGGER.info("设备序列号为空:{}", bindOperationId);
            } else {
                serialNumber = operationTb.getSerialNumber();
                BindOperationTb operationTbOriginal = BindOperationTb.builder()
                        .operationId(bindOperationId)
                        .appRequestBindComplete(1)
                        .appRequestBindCompleteTimestamp(Instant.now().getEpochSecond())
                        .build();
                deviceDAO.updateBindOperationBindInfo(operationTbOriginal);
            }
        }
        return DeviceBindStep.builder()
                .serialNumber(serialNumber)
                .deviceBindStep(deviceBindStep)
                .opretionId(bindOperationId)
                .build();
    }

    private void sendBindResponse(MqttDeviceBindResponse mqttResponse, String serialNumber, int statusCode) {
        mqttResponse.setCode(statusCode);
        if (statusCode == MqttResponseCode.SUCCESS) {
            // value.pirResponseType: pir响应的数据结构类型。0或null：当前数据结构；1：对接第三方平台的数据结构
            Optional.ofNullable(mqttResponse.getValue()).map(it -> it.getUserId())
                    .map(userService::queryUserById).map(it -> it.getTenantId())
                    .ifPresent(mqttResponse.getValue()::setTenantId);

        }
        try {
            VernemqPublisher.bindOperationResponse(serialNumber, mqttResponse);
        } catch (Exception ex) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "Failed to reply bind request from camera " + serialNumber, ex);
        }
    }


    public String buildCxDevices(String bxSn) {
        try {
            final List<JSONObject> cxDeviceInfos = new LinkedList<>();
            for (String cxSn : userRoleService.getCxSnsByBx(bxSn)) {
                final DeviceManualDO deviceManualDO = deviceManualService.getDeviceManualBySerialNumber(cxSn);
                final JSONObject cxDeviceInfo = new JSONObject().fluentPut("sn", deviceManualDO.getSerialNumber())
                        .fluentPut("macAddress", deviceManualDO.getMacAddress()).fluentPut("online", 0);
                try {
                    final DeviceAttributeService.DeviceAttributeSource attrSrc = deviceAttributeService.getAttributeSource(cxSn);
                    cxDeviceInfo.put("online", attrSrc.getOnlineInfo().getOnline());
                } catch (Throwable e) {
                    log.error("buildCxDevices getOnlineInfo error! bxSn={},cxSn={}", bxSn, cxSn, e);
//                    cxDeviceInfo.put("online", ClientManager.getInstance().getGroupMaster(cxSn) != null); // 兜底
                }
                cxDeviceInfos.add(cxDeviceInfo);
            }
            log.info("buildCxDevices end! bxSn={}", bxSn);
            final JSONObject result = new JSONObject(new LinkedHashMap<>()).fluentPut("name", "cx_devices")
                    .fluentPut("time", System.currentTimeMillis()).fluentPut("id", 0).fluentPut("list", cxDeviceInfos);
            return result.toJSONString();
        } catch (Throwable e) {
            log.error("buildCxDevices error! bxSn={}", bxSn, e);
            return null;
        }
    }

    public void sendReportToMatterForCxDevices(UserRoleDO userRole) {
        final String bxSn = userRole.getBaseStation() == 1 ? userRole.getSerialNumber() : userRole.getAssociationSn();
        final String cxSn = userRole.getBaseStation() == 0 ? userRole.getSerialNumber() : null;
        try {
            final String data = buildCxDevices(bxSn); // iot-local主动通知bstationd
            if (data == null) return;
            final boolean isSuccess = WssReportEventServiceImpl.getInstance().sendReportToMatter(cxSn, data);
            log.info("sendReportToMatter for cx_devices end! bxSn={},cxSn={},isSuccess={},data={}", bxSn, cxSn, isSuccess, data);
        } catch (Throwable e) {
            log.info("sendReportToMatter for cx_devices error! bxSn={},bindCxSn={},data={}", bxSn, cxSn);
        }
    }

    public Result<BindDeviceResult> baseStationBindDeviceFromCamera(MqttDeviceBindRequest request) throws IdNotSetException {
        Result<BindDeviceResult> verifyParam = this.verifyBindDeviceParam(request);
        if(!verifyParam.getResult().equals(Result.successFlag)){
            return verifyParam;
        }
        MqttBstationDeviceBindResponse mqttResponse = new MqttBstationDeviceBindResponse();
        log.info("绑定消息类型{}",mqttResponse.getName());
        mqttResponse.setId(request.getId());
        mqttResponse.setTime(PhosUtils.getUTCStamp());

        String serialNumber = request.getSerialNumber();
        // 是否基站设备
        boolean baseStation = request.getValue().getCategoryId() != null && DeviceModelCategoryEnums.BASE_STATION.getCode() == request.getValue().getCategoryId();

        Result userResult = queryAdminUser(baseStation,request);
        User user = userResult.getResult().equals(Result.successFlag) ? (User)userResult.getData() : null;
        if(user == null){
            LOGGER.error("init user error {}",  serialNumber);
            sendBindResponse(mqttResponse, serialNumber, userResult.getResult());

            return new Result(userResult.getResult(),userResult.getMsg(),new BindDeviceResult());
        }
        BindDeviceCloudUserInfo cloudUserInfo = userService.initCloudUserInfo(user);

        BindOperationRequest bindOperationRequest = this.initBindOperationRequest(request,user.getId());
        BindOperationTb operationTb = this.queryBindOperation(user.getId(), bindOperationRequest);
        if(!baseStation){
            // cx绑定copy bx 的timezone

            DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(request.getValue().getAssociationSn());
            if(deviceSettingsDO == null || !StringUtils.hasLength(deviceSettingsDO.getTimeZone())){
                LOGGER.error("bx 还未绑定完成 {}",  serialNumber);
                sendBindResponse(mqttResponse, serialNumber, ResultCollection.DEVICE_BSTATIOND_UNACTIVATED.getCode());
                return new Result(ResultCollection.DEVICE_BSTATIOND_UNACTIVATED.getCode(), ResultCollection.DEVICE_BSTATIOND_UNACTIVATED.getMsg(),new BindDeviceResult());
            }
            operationTb.setTimeZone(deviceSettingsDO.getTimeZone());
        }

        // 记录绑定步骤
        this.updateDeviceBindStep(operationTb.getOperationId(), DeviceBindStatusEnums.BIND.getCode());
        LOGGER.info(String.format("Activating device %s for bind operation: %s", serialNumber, operationTb.getOperationId()));

        reportLogService.sysReportBind(REPORT_TYPE_RECEIVE_BIND_REQUEST_FROM_DEVICE,
                MapUtil.builder()
                        .put("bindOperationId", operationTb.getOperationId())
                        .put("serialNumber", serialNumber)
                        .build()
        );

        mqttResponse.getValue().setUserId(operationTb.getUserId());
        mqttResponse.getValue().setCloudUserId(cloudUserInfo.getCloudUserId());
        mqttResponse.getValue().setCloudUserEvn(cloudUserInfo.getCloudUserEvn());


        // 补全写回数据库信息
        operationTb.setSerialNumber(serialNumber);
        operationTb.setAnswered(1);
        // 缺省操作结果为成功
        operationTb.setStatusCode(ResultCollection.SUCCESS.getCode());

        // 如果bindContent是通过nodeMatcher传递过去的，则删除nodeMather上的绑定信息
        if (BindContentSrc.NODE_MATCHER.getCode() == operationTb.getBindContentSrc()) {
            nodeMatcherAgency.deleteBindContent(null, serialNumber);
        }

        //更新设备apInfo,只bx 有apInfo
        deviceInfoService.updateDeviceApInfoFromSync(serialNumber,request.getValue().getUserSn(),request.getValue().getApInfo());

        UserRoleDO userRoleDO = userRoleService.getDeviceAdminUserRole(serialNumber);

        // 如果设备已被激活
        if ((null != userRoleDO)) {
            if (operationTb.getUserId().equals(userRoleDO.getAdminId())) {
                // 该用户已拥有此设备，直接返回成功
                LOGGER.info(String.format("User %d try to re-activate device %s", operationTb.getUserId(), serialNumber));
                // 完成绑定初始化
                this.updateDeviceBindStep(operationTb.getOperationId(), DeviceBindStatusEnums.INIT.getCode());

                this.deviceBound(operationTb, serialNumber, mqttResponse);

                // 更新设备绑定时间
                userRoleService.updateDeviceBindTime(userRoleDO);
                // 重复绑定也发事件通知
                // openApiWebhookService.callWebhookForDeviceBind(operationTb.getUserId(), serialNumber, operationTb.getTimeZone());

                sendReportToMatterForCxDevices(userRoleDO); // 通知matter:bx或cx重复绑定

                return new Result<>(
                         BindDeviceResult.builder()
                                 .oldAdminId(userRoleDO.getAdminId())
                                 .adminId(userRoleDO.getAdminId())
                                 .build()
                );
            } else{

                if(baseStation){
                    sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.MANAGE_INVALID_REQUEST);
                    return new Result(ResultCollection.DEVICE_BIND_OTHER.getCode(), ResultCollection.DEVICE_BIND_OTHER.getMsg(),new BindDeviceResult());
                }
                // 设备属于同一节点其他用户，调用解绑函数
                deactivateDeviceBySerialNumber(serialNumber,false);
            }
        }

        // 拷贝硬件信息到对应节点的库
        DeviceManualDO deviceManual = new DeviceManualDO()
                .setUserSn(request.getValue().getUserSn())
                .setSerialNumber(request.getValue().getSerialNumber())
                .setModelNo(request.getValue().getModelNo())
                .setOriginalModelNo(request.getValue().getOriginModelNo())
                .setDisplayModelNo(request.getValue().getDisplayModelNo())
                .setMacAddress(request.getValue().getMacAddress())
                .setMcuNumber(request.getValue().getMcuNumber());
        deviceManualService.addDeviceManual(deviceManual);
        LOGGER.info(String.format("Copy factory hardware info for device", serialNumber));

        // 经过上面一系列检查，现在可以正式进行绑定操作了
        //重新绑定时删除之前的收藏点
        rotationPointService.deleteRotationPointBySerialNumber(serialNumber);
        initDeviceBinding(operationTb.getUserId(), serialNumber,request);
        
        // 更新数据库，确认绑定操作
        deviceDAO.updateBindOperation(operationTb);
        LOGGER.info(String.format("Updated bind operation info for: %s", operationTb.getOperationId()));

        // 初始化设备状态表
        deviceStatusService.initDeviceStatusForBind(serialNumber);
        LOGGER.info(String.format("Updated Device status for device: %s", serialNumber));

        // 更新用户设备关系表
        UserRoleDO userRole = new UserRoleDO();
        userRole.setUserId(operationTb.getUserId());
        userRole.setAdminId(operationTb.getUserId());
        userRole.setSerialNumber(serialNumber);
        userRole.setRoleId("1");
        userRole.setBaseStation(baseStation ? 1 : 0);
        userRole.setAssociationSn(baseStation ? "" : request.getValue().getAssociationSn());
        userRoleService.saveUserRole(userRole);
        LOGGER.info(String.format("Init admin relationship for device: %s with user : %d", serialNumber, operationTb.getUserId()));


        if(!baseStation){
            //cx 绑定时判断bx是否有分享
            deviceRelationshipService.verifyDeviceShare(userRole.getAssociationSn(), serialNumber, userRole.getAdminId());
        }

        // 在当前节点的最后才去修改device表
        DeviceDO device = new DeviceDO();
        device.setSerialNumber(serialNumber);
        device.setUserId(operationTb.getUserId());
        device.setDeviceName(initDeviceName(operationTb.getUserId(),request.getValue().getModelNo(),request.getValue().getCategoryId()));
        device.setLocationId(operationTb.getLocationId());
        device.setFirmwareId(request.getValue().getFirmwareId());
        device.setDisplayModelNo(request.getValue().getDisplayModelNo());
        device.setDormancyPlanSwitch(1);
        device.setModelCategory(Optional.ofNullable(request.getValue().getCategoryId()).orElse(DeviceModelCategoryEnums.CAMERA.getCode()));
        deviceService.activateDevice(device);
        LOGGER.info(String.format("Updated Device info after activating device: %s", serialNumber));

        // 更新OTA状态
        FirmwareViewDO firmwareViewDO = new FirmwareViewDO();
        firmwareViewDO.setSerialNumber(serialNumber);
        firmwareViewDO.setTargetFirmware(queryDeviceFirmware(serialNumber));
        firmwareService.insertDeviceOTA(firmwareViewDO);
        LOGGER.info(String.format("Init OTA info for device", serialNumber));

        // 初始化设备维度AI分析设置
        deviceAiSettingsService.initDeviceAiSettings(operationTb.getUserId(), serialNumber);
        //更新设备人型检测
        initMessageNotificationSettings(operationTb.getUserId(), serialNumber);
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        LOGGER.info("query deviceInfo {}", deviceDO);

        if(baseStation){
            Integer adminUserId = iUserService.queryAdminUserId();
            // BX绑定成功，更新设备的默认扩展
            extensionService.clearAllExtension();
            extensionService.installDefaultExtensionWhenBinding(adminUserId);
            // 通知 AI-station BX消息
            videoAIService.sendBxBindEvent();
        }

        // 计算设备所属套餐
//        userTierDeviceService.onAddUserDevice(operationTb.getUserId(), serialNumber);

        this.operationAfterDeviceBind(operationTb,baseStation,request.getValue().getAssociationSn(),serialNumber);

        // 通知摄像头初始化设置。cooldown、deviceAudio、doorBell都在initDeviceConfig里面有，不用单独去初始化。
        DeviceSettingsDO deviceSetting = initDeviceConfig(operationTb, request.getValue().getModelNo());

        sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.SUCCESS);

        // 完成绑定初始化
        this.updateDeviceBindStep(operationTb.getOperationId(), DeviceBindStatusEnums.INIT.getCode());

        // 绑定发送事件通知
        // openApiWebhookService.callWebhookForDeviceBind(operationTb.getUserId(), serialNumber, operationTb.getTimeZone());

        sendReportToMatterForCxDevices(userRole); // 通知matter:bx或cx首次绑定

        // 清除设备缓存信息---暂时
        deviceService.clearDeviceCache(serialNumber);
        return new Result<>(
                BindDeviceResult.builder()
                        .oldAdminId(userRoleDO != null ? userRoleDO.getAdminId() : null)
                        .adminId(userRole.getAdminId())
                        .build()
        );
    }

    /**
     * 初始化绑定设备admin
     * @param baseStation
     * @param request
     * @return
     */
    private Result queryAdminUser(boolean baseStation,MqttDeviceBindRequest request){
        User user;
        Result result ;
        if(baseStation){
            //bx 设备绑定
            result = loginService.register(request.getValue().getBindUser());
        }else {
            //cx ，已存在admin,跟随bx设备
            int adminId = userRoleService.getDeviceAdminUser(request.getValue().getAssociationSn());
            result = adminId == 0 ? new Result(DEVICE_BSTATIOND_UNACTIVATED.getCode(),DEVICE_BSTATIOND_UNACTIVATED.getMsg(),null) :
                    new Result(Result.successFlag,"",userService.queryUserById(adminId)) ;
        }

        return result;
    }

    /**
     * 设备绑定之后做的操作
     * @param operationTb
     */
    public void operationAfterDeviceBind(BindOperationTb operationTb,Boolean isBstationd,String bxSn,String cxSn){
        // 计算绑定后评分引导
        userAppScoreService.userAppScoreMomentDeviceBind(operationTb.getUserId(),operationTb.getSerialNumber());

        if(isBstationd!= null && !isBstationd){
            devicePreBindService.updateDevicePreBindStatus(bxSn,cxSn);
        }
    }

    /**
     * 设备绑定时做的初始化操作
     */
    private void initDeviceBinding(Integer userId, String serialNumber) {
        this.initDeviceBinding(userId,serialNumber,null);
    }

    private void initDeviceBinding(Integer userId, String serialNumber,MqttDeviceBindRequest request) {
        try {
            userVipService.deleteUserReminder(userId);

            // 设备新绑定去除所有休眠计划
            deviceDormancyPlanService.deleteDeviceDormancyPlan(serialNumber);

            //暂时放在redis，等提测代码都合并再改db
            if(request != null){
                String cloudAddrress = request.getValue().getCloudAddr();
                if(!StringUtils.isEmpty(cloudAddrress)){
                    String key = DEVICE_CLOUD_ADDRESS_KEY.replace("serialNumber",serialNumber);
                    redisService.set(key,cloudAddrress);
                }
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "用户绑定初始化失败" + userId, e);
        }
    }


    public String initDeviceName(Integer userId,String modelNo) {
        User user = userService.queryUserById(userId);
        if (user == null) {
            return "";
        }

        if(deviceCameraNameConfig.getConfig().containsKey(modelNo)){
            //指定型号设备 cameraName
            return deviceCameraNameConfig.getConfig().get(modelNo);
        }

        Integer deviceModelCategory = 1;
        return copywriteLanguageService.queryDeviceName(user.getLanguage(),deviceModelCategory);
    }

    public String initDeviceName(Integer userId,String modelNo,Integer deviceModelCategory) {
        User user = userService.queryUserById(userId);
        if (user == null) {
            return "";
        }

        if(deviceCameraNameConfig.getConfig().containsKey(modelNo)){
            //指定型号设备 cameraName
            return deviceCameraNameConfig.getConfig().get(modelNo);
        }

        return copywriteLanguageService.queryDeviceName(user.getLanguage(),deviceModelCategory);
    }

    /**
     * 设备重新绑定情况时回复
     *
     * @param operationTb
     * @param serialNumber
     * @param mqttResponse
     */
    private void deviceBound(BindOperationTb operationTb, String serialNumber, MqttDeviceBindResponse mqttResponse) throws IdNotSetException {
        //更新绑定操作记录表状态
        operationTb.setStatusCode(ResultCollection.SUCCESS.getCode());
        deviceDAO.updateBindOperation(operationTb);

        //更新设备语言
        DeviceSettingsDO deviceSettingsDO = DeviceSettingsDO.builder()
                .serialNumber(serialNumber)
                .language(operationTb.getDeviceLanguage())
                .deviceSupportLanguage(null)
                .build();
        deviceSettingService.updateDeviceSetting(deviceSettingsDO);

        //更新设备设置
        DeviceSettingsDO storeDeviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(serialNumber);
        LOGGER.info("设备重新绑定将数据库配置下发,serialNumber:{},deviceSettingsDO:{}", serialNumber, storeDeviceSettingsDO);
        deviceSettingService.resetVernemqSettingMessage(serialNumber, storeDeviceSettingsDO);

        //回复设备绑定结果
        sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.SUCCESS);
    }

    private DeviceSettingsDO initDeviceConfig(BindOperationTb operationTb, String modelNo) throws IdNotSetException {

            operationTb.setBindRequestApproved(1);
            operationTb.setBindRequestApprovedTimestamp(Instant.now().getEpochSecond());
            log.info("Bind device setting {}",operationTb);
            DeviceModelSettingDO deviceModelSettingDO = DeviceModelSettingDO.builder()
                    .deviceModelVoiceDO(null)
                    .mirrorFlip(MIRROR_FLIP_VALUE)
                    .motionSensitivity(deviceSettingConfig.queryMotionSensitivity(modelNo))
                    .chargeAutoPowerOnSwitch(deviceSettingConfig.queryModelChargeAutoPowerOnSwitch(modelNo))
                    .build();

            DeviceAppSettingsDO appSettingsDO = DeviceAppSettingsDO.defaultSettings(deviceModelSettingDO);
            appSettingsDO.setTimeZone(operationTb.getTimeZone());
            appSettingsDO.setDeviceLanguage(operationTb.getDeviceLanguage());
            appSettingsDO.setSerialNumber(operationTb.getSerialNumber());
            appSettingsDO.setAntiflickerSwitch(deviceSettingConfig.queryModelAntiflickerSwitch(modelNo));
            appSettingsDO.setAntiflicker(DEFAULT_ANTIFLICKER);
            appSettingsDO.setRecLamp(deviceSettingConfig.queryRecLamp(modelNo));
            appSettingsDO.setEnableOtherMotionAi(deviceSettingConfig.queryEnableOtherMotionAi(modelNo));

            /*
            // 根据套餐判断是否打开拍摄间隔开关
            try {
                final boolean isNoVipOrFreeTier2 = userTierDeviceService.getIsNoVipOrFreeTier2(operationTb.getUserId(), operationTb.getSerialNumber());
                if (isNoVipOrFreeTier2) {
                    User user = userService.queryUserById(operationTb.getUserId());
                    Integer currentTierId = userTierDeviceService.getDeviceCurrentTier(operationTb.getUserId(), operationTb.getSerialNumber());
                    appSettingsDO.setCooldown(new CoolDownDO().setValue(FreeUserVipTier2Config.getCooldownOptionValueList(currentTierId, user.getRegistTime(), modelNo).get(0).getValue()).setUserEnable(true));
                }
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "initDeviceConfig failed set cooldown", e);
            }
            */

            DeviceSettingsDO settingsDO = DeviceSettingsDO.ParseFrom(appSettingsDO, null);
            String rawSettingsDOJSON = null;
            try {
                rawSettingsDOJSON = JSON.toJSONString(settingsDO);
                final DeviceAttributeService.DeviceAttributeSource attrSrc = deviceAttributeService.getAttributeSource(operationTb.getSerialNumber());
                attrSrc.setSettingDefaultEnumValue(settingsDO); // 初始化枚举值字段
                LOGGER.info("initDeviceConfig setSettingDefaultEnumValue end! sn={},rawSettingsDO={},settingsDO={}", operationTb.getSerialNumber(), rawSettingsDOJSON, JSON.toJSONString(settingsDO));
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "initDeviceConfig setSettingDefaultEnumValue error! sn={},rawSettingsDO={},settingsDO={}", operationTb.getSerialNumber(), rawSettingsDOJSON, JSON.toJSONString(settingsDO), e);
            }

            this.pushMqttMessage(settingsDO); // 发送 setting mqtt 消息
            // 不需要设置设备支持语言
            settingsDO.setDeviceSupportLanguage(null);
            // 设备支持门铃铃音需要设备上报
            settingsDO.setSupportDoorBellRingKey("");
            appSettingsDO.setDoorbellPressNotifySwitch(DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_SWITCH);
            appSettingsDO.setDoorbellPressNotifyType(DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_TYPE);
            appSettingsDO.setPowerSource(OptionEnumMapping.OPTION_ENUM_UNSELECTED);
            appSettingsDO.setOverallLightSwitch(DEFAULT_VALUE_OVERALL_LIGHT_SWITCH);
            appSettingsDO.setOverallLightIntensity(DEFAULT_VALUE_OVERALL_LIGHT_INTENSITY);
            appSettingsDO.setWifiPowerLevel(DEFAULT_VALUE_WIFI_POWER_LEVEL);

            //物模型里的默认值赋值
            for (ThingModel.ThingEntity entity : thingModelConfig.getThingModelByModelNo(modelNo).getProperties()) {
                // thing_model.yml 的默认值
                if(entity.getDefaultValue()!=null) {
                    settingsDO.addToPropertyJson(entity.getIdentifier(), entity.getDefaultValue());
                }
            }

            deviceInfoService.setDeviceSettings(settingsDO);
            deviceInfoService.updateDeviceTimeZone(appSettingsDO);
            return settingsDO;
    }

    private void pushMqttMessage(DeviceSettingsDO settingsDO) throws IdNotSetException {
        //发送普通设置set消息
        SetParameterRequest setParameterRequest = settingsDO.ToRequest(CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID());
        VernemqPublisher.setParameter(settingsDO.getSerialNumber(), setParameterRequest);

        //发送retain message消息
        SetRetainParamRequest retainParamter = deviceSettingService.initSetParameterRequest(setParameterRequest.getId(), settingsDO, null);
        VernemqPublisher.setParameterRetain(settingsDO.getSerialNumber(), retainParamter);
    }

    private void initMessageNotificationSettings(Integer userId, String serialNumber) {
        try {
            // 初始化AI消息设置表。无论当前设备是否是VIP
            notificationService.initMessageNotificationSettings(serialNumber, userId);
            if (vipService.isVipDevice(userId, serialNumber)) {
                LOGGER.info("绑定AI人型识别:{}", serialNumber);
                // 开启人型检测
                deviceInfoService.updatePersonDetect(serialNumber, PersonDetectEnum.OPENPERSONDETECT.getCode());
            } else {
                //防止解绑时未关闭的情况
                LOGGER.info("关闭AI人型检测:{}", serialNumber);
                deviceInfoService.updatePersonDetect(serialNumber, PersonDetectEnum.CLOSEPERSONDETECT.getCode());
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "绑定AI人型识别error:serialNumber:{},{}", serialNumber, e);
        }
    }

    /**
     * 获取同型号最新的固件id
     *
     * @param serialNumber
     * @return
     */
    private String queryDeviceFirmware(String serialNumber) {
        String firewareId = "";
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if (modelNo != null) {
            FirmwareTableDO firmwareTableDO = firmwareService.getLatestActivatedFirmwareBySerialNumber(serialNumber);
            firewareId = firmwareTableDO == null ? firewareId : firmwareTableDO.getFirmwareId();
        }
        return firewareId;
    }

    /**
     * 上传设备绑定错误原因
     *
     * @param request
     * @return
     */
    public boolean bindErrorReason(BindErrorRequest request) {
        int time = (int) (System.currentTimeMillis() / 1000);
        request.setCdate(time);
        return iDeviceBindErrorDAO.insertOrUpdate(request) > 0;
    }

    public String queryModelBySerialNumber(String userSn) {
        //现在型号逻辑待确认，等确认后再补逻辑，已跟产品沟通

        return StringUtils.isEmpty(userSn) ? "" : "G";
    }

    public Result queryDeviceBindCode(Integer userId, BindOperationRequest request, IpInfo ipInfo) throws Throwable {
        String operation = bindOperation(userId, request, ipInfo);
        String contents;
        String image;
        if (DeviceBindTypeEnums.JSON.getCode().equals(request.getCodeType())) {
            contents = this.getCodeValueJson(operation, request);
            image = QRcodeUtil.genBarcode(contents, request.getWidth(), request.getHeight());
        } else {
            contents = this.getCodeValue(operation, request);
            image = QRcodeUtil.genBarcode(contents, request.getWidth(), request.getHeight());
        }
        if (StringUtils.isEmpty(image)) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "queryDeviceBindCode error,{}", request.toString());
            throw new BaseException(INVALID_PARAMS, "INVALID_PARAMS");
        }

        Map<String, String> map = Maps.newHashMap();
        map.put("operationId", operation);
        map.put("contents", contents);
        map.put("image", image);
        return new Result(map);
    }

    public Result bindCableDevice(Integer userId, BindOperationRequest request, IpInfo ipInfo) throws Exception {
        DeviceManufactureTableDO deviceManu = factoryDataQueryService.queryDeviceManufactureByUserSn(request.getUserSn());
        if (deviceManu == null) {
            return Result.Error(INVALID_PARAMS, "userSn无效");
        }
        request.setBindContentSrc(BindContentSrc.NODE_MATCHER.getCode());
        request.setDeviceNetType(DeviceNetType.CABLE.getCode());
        String operation = bindOperation(userId, request, ipInfo);
        String contents = this.getCodeValue(operation, request);
        // 设置绑定内容到node-matcher
        if (!nodeMatcherAgency.setBindContent(request.getUserSn(), deviceManu.getSerialNumber(), contents, DEVICE_CONNECT_TIMEOUT)) {
            return Result.Failure("发送绑定信息失败");
        }
        Map<String, String> map = Maps.newHashMap();
        map.put("operationId", operation);
        map.put("contents", contents);
        map.put("image", QRcodeUtil.genBarcode(contents, request.getWidth(), request.getHeight()));
        return new Result(map);
    }

    public Result queryDeviceBindByApTextResult(Integer userId, BindByApOperationRequest request, IpInfo ipInfo) throws Throwable {
        BindOperationRequest bindOperationRequest = new BindOperationRequest();
        BeanUtils.copyProperties(request, bindOperationRequest);
        bindOperationRequest.setBindContentSrc(BindContentSrc.QRCODE.getCode());
        String operation = bindOperation(userId, bindOperationRequest, ipInfo);

        String bindText = getCodeValue(operation, bindOperationRequest);
        String bindJson = getCodeValueJson(operation, bindOperationRequest);

        Map<String, String> map = Maps.newHashMap();
        map.put("operationId", operation);
        map.put("bindText", bindText);
        map.put("bindJson", bindJson);
        return new Result(map);
    }

    private String getCodeValueJson(String operation, BindOperationRequest request) {
        JSONObject codeValue = new JSONObject();
        codeValue.put("r", operation);
        codeValue.put("l", request.getDeviceLanguage());
        codeValue.put("n", request.getNetworkName());
        codeValue.put("e", IpHelper.getInstance().getNodeName());
        codeValue.put("p", request.getPassword());
        // 目前前入职只支持 0 （绑定）
        codeValue.put("c", defaultBindType);
        return codeValue.toString();
    }

    private String getCodeValue(String operation, BindOperationRequest request) throws UnsupportedEncodingException {
        StringBuilder codeStringBuilder = new StringBuilder();
        Integer language = deviceLanguageConfig.getConfig().containsKey(request.getDeviceLanguage()) ?
                deviceLanguageConfig.getConfig().get(request.getDeviceLanguage()) : deviceLanguageConfig.getConfig().get(request.getLanguage());
        if (language == null) {
            //默认英语
            language = 0;
        }
        final DeviceNetType deviceNetType = DeviceNetType.codeOf(request.getDeviceNetType());
        LOGGER.info("getCodeValueByte language:{},deviceNetType={}", language, deviceNetType);
        appendValue(CodeKeyEnums.QR_ID_REQUEST_ID.getCode(), operation, codeStringBuilder);
        appendValueInt(CodeKeyEnums.QR_ID_LANGUAGE.getCode(), language, codeStringBuilder);
        if (deviceNetType == DeviceNetType.WIFI) { // 防止影响老设备，不改变键值对顺序
            appendValue(CodeKeyEnums.QR_ID_WIFI_SSID.getCode(), request.getNetworkName(), codeStringBuilder);
        }
        appendValue(CodeKeyEnums.QR_ID_SERVER_ENV.getCode(), IpHelper.getInstance().getNodeName(), codeStringBuilder);
        if (deviceNetType == DeviceNetType.WIFI) { // 防止影响老设备，不改变键值对顺序
            appendValue(CodeKeyEnums.QR_ID_WIFI_PASSWORD.getCode(), request.getPassword(), codeStringBuilder);
        }
        // 防止影响老设备，新的键值对放在最后
        appendValueInt(CodeKeyEnums.QR_ID_DEVICE_NET_TYPE.getCode(), deviceNetType.getCode(), codeStringBuilder);

        return codeStringBuilder.toString();
    }

    /**
     * 拼接文本,key+value.length+value
     *
     * @param key
     * @param value
     */
    private void appendValue(int key, String value, StringBuilder codeStringBuilder) throws UnsupportedEncodingException {
        codeStringBuilder.append((char) key);
        codeStringBuilder.append((char) (StringUtils.isEmpty(value) ? 0 : value.getBytes().length));
        if (!StringUtils.isEmpty(value)) {
            codeStringBuilder.append(value);
        }
    }


    /**
     * 拼接文本,key+value.length+value
     *
     * @param key
     * @param value
     */
    private void appendValueInt(int key, Integer value, StringBuilder codeStringBuilder) {
        codeStringBuilder.append((char) key);
        codeStringBuilder.append((char) 1);
        codeStringBuilder.append((char) value.intValue());
    }

    /**
     * 用户进入绑定页面获取绑定码，会随着后续bind_operation 传入
     *
     * @param userId
     * @return
     */
    public String getBindCode(int userId) {
        StringBuilder code = new StringBuilder();
        code.append(userId);
        code.append("-");
        code.append(System.currentTimeMillis());
        String result = code.toString();
        reportLogService.sysReportBind(REPORT_TYPE_GENERATE_BIND_CODE,
                MapUtil.builder()
                        .put("userId", userId)
                        .put("code", result)
                        .build()
        );

        Long time = Instant.now().getEpochSecond();
        // 记录绑定码生成时间
        redisService.set(DeviceReportKeyConstants.DEVICE_BIND_CODE_KEY.replace("{bindCode}", result),
                String.valueOf(Instant.now().getEpochSecond()),
                DEVICE_CONNECT_TIMEOUT);

        // 记录绑定码
        DeviceBindCode deviceBindCode = DeviceBindCode.builder()
                .userId(userId)
                .bindCode(result)
                .cdate(time.intValue())
                .build();
        deviceBindCodeService.insertDeviceBindCode(deviceBindCode);
        return result;
    }

    /**
     * 设备上报连接http状态
     *
     * @param deviceConnectReport
     */
    public void deviceReportConnectStatus(DeviceConnectReport deviceConnectReport) {
        // 固件未保证顺讯性,暂时不更新步骤
//        String key = DeviceReportKeyConstants.DEVICE_CONNECT_KEY.replace("{operationId}", deviceConnectReport.getOperationId());
//        // 连接http成功
//        redisService.set(key, String.valueOf(DeviceBindStatusEnums.CONNECT.getCode()), DEVICE_CONNECT_TIMEOUT);
        reportLogService.deviceEventReport(REPORT_GROUP_BIND, REPORT_TYPE_DEVICE_HTTP_CONNECT, JSON.parseObject(JSON.toJSONString(deviceConnectReport)));

        LOGGER.info("deviceReportConnectStatus mark redis,operationId:{}", deviceConnectReport.getOperationId());
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .operationId(deviceConnectReport.getOperationId())
                .deviceHttpRequest(1)
                .deviceHttpRequestTimestamp(Instant.now().getEpochSecond())
                .build();

        deviceDAO.updateBindOperationBindInfo(bindOperationTb);
    }

    /**
     * 更新设备绑定步骤
     *
     * @param operationId
     * @param step
     */
    private void updateDeviceBindStep(String operationId, int step) {
        LOGGER.info("设备绑定步骤更新,operationId:{},step:{}", operationId, step);
        String key = DeviceReportKeyConstants.DEVICE_CONNECT_KEY.replace("{operationId}", operationId);
        // 更新 operationId 对应绑定步骤
        redisService.set(key, String.valueOf(step), DEVICE_CONNECT_TIMEOUT);

        if (DeviceBindStatusEnums.INIT.getCode() == step) {
            BindOperationTb bindOperationTb = BindOperationTb.builder()
                    .operationId(operationId)
                    .bindRequestComplete(1)
                    .bindRequestCompleteTimestamp(Instant.now().getEpochSecond())
                    .build();
            deviceDAO.updateBindOperationBindInfo(bindOperationTb);
        }
    }

    /**
     * 设备绑定-接收到设备mqtt消息
     *
     * @param operationId
     */
    public void receiveDeviceBindMqtt(String operationId) {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .operationId(operationId)
                .deviceMqttRequest(1)
                .deviceMqttRequestTimestamp(Instant.now().getEpochSecond())
                .build();

        deviceDAO.updateBindOperationBindInfo(bindOperationTb);
    }

    /**
     * APP上报绑定步骤
     *
     * @param operationId
     * @return
     */
    public boolean deviceBindComplete(String operationId, int bindStep, String appName) {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .operationId(operationId)
                .build();
        Long time = Instant.now().getEpochSecond();
        DeviceBindStatusEnums bindStatusEnums = DeviceBindStatusEnums.queryByCode(bindStep);
        switch (bindStatusEnums) {
            case WIFI:
                bindOperationTb.setAppBindStepWifi(1);
                bindOperationTb.setAppBindStepWifiTimestamp(time);
                break;
            case CONNECT:
                bindOperationTb.setAppBindStepHttp(1);
                bindOperationTb.setAppBindStepHttpTimestamp(time);
                break;
            case BIND:
                bindOperationTb.setAppBindStepApproved(1);
                bindOperationTb.setAppBindStepApprovedTimestamp(time);
                break;
            case INIT:
                bindOperationTb.setAppBindStepComplete(1);
                bindOperationTb.setAppBindStepCompleteTimestamp(time);
                break;
        }

        int result = deviceDAO.updateBindOperationBindInfo(bindOperationTb);
        return result > 0;
    }


    /**
     * 校验设备号是否正确
     * @param userSn
     * @param serialNumber
     * @return
     */
    public Result verifyDeviceSnExist(String userSn,String serialNumber){
        DeviceManufactureTableDO deviceManufactureTableDO = factoryDataQueryService.queryDeviceManufactureByUserSn(userSn);
        if (deviceManufactureTableDO == null) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "设备绑定时校验,设备序列号无效，deviceManufactureTableDO null");
            // 无效的序列号
            return Result.Failure("Invalid SN");
        }

        if(deviceManufactureTableDO.getSerialNumber() == null ||
                !deviceManufactureTableDO.getSerialNumber().equals(serialNumber)){
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "设备绑定时校验,设备序列号无效，deviceManufactureTableDO serialNumber {},device serialNumber {}",deviceManufactureTableDO.getSerialNumber(),serialNumber);
            return Result.Failure("UUID not exist");
        }
        return Result.Success();
    }

    /**
     * 获取绑定id
     * @param bindRequest
     * @return
     */
    private BindOperationRequest initBindOperationRequest(MqttDeviceBindRequest bindRequest,Integer userId){
        BindOperationRequest request = BindOperationRequest.builder()
                .operationId(bindRequest.getValue().getRid())
                .timeZone(bindRequest.getValue().getTimeZone())
                .build();

        LoginRequest re = Optional.ofNullable(bindRequest.getValue().getBindUser()).orElse(null);
        request.setApp(re == null ? null : re.getApp());
        request.setLanguage(re == null ? APP_LANGUAGE_EN : re.getLanguage());
        return request;
    }


    private Result verifyBindDeviceParam(MqttDeviceBindRequest request) {
        if (!StringUtils.hasLength(request.getValue().getRid())) {
            LOGGER.error("设备绑定，rid 为空");
            return new Result(INVALID_PARAMS.getCode(), "rid 为空", new BindDeviceResult());
        }
        BindOperationTb operationTb = deviceDAO.selectOperation(request.getValue().getRid());
        if (operationTb != null) {
            LOGGER.error("设备绑定，rid 重复");
            return new Result(INVALID_PARAMS.getCode(), "rid 重复", new BindDeviceResult());
        }
        if (!StringUtils.hasLength(request.getValue().getModelNo()) ||
                !StringUtils.hasLength(request.getValue().getOriginModelNo()) ||
                !StringUtils.hasLength(request.getValue().getDisplayModelNo())) {

            LOGGER.error("设备绑定，型号信息为空");
            return new Result(INVALID_PARAMS.getCode(), "型号信息为空", new BindDeviceResult());
        }

        return Result.Success();
    }

    /**
     * 设备reset 操作
     * @param serialNumber
     */
    public void deviceReset(String serialNumber){
        LOGGER.info("DeviceResetProcessor {}",serialNumber);
        this.deactivateDeviceBySerialNumber(serialNumber,false);

        userService.deleteAllUser();
        userRoleService.deleteAllRecord();
        //清空所有 device info
        deviceInfoService.deleteAllDevice();
        //清空所有 device 型号信息
        deviceManualService.deleteAllDeviceManual();

        deviceOTAService.deleteAllRecord();
        //删除已绑定所有记录
        devicePreBindService.deleteAllRecord();

        userSettingService.deleteAllRecord();
        //删除绑定Log表
        deviceDAO.deleteAllDeviceBindOperation();

        deviceRelationshipService.deleteDeviceShareAll();
        deviceSettingService.deleteAllRecord();
        deviceAiSettingsService.deleteAllRecord();

        securityModeBiz.cleanAllModeSetting();

        deviceSupportService.deleteAllRecord();
        deviceModelConfigService.deleteAllRecord();

        // 清除本地缓存
        ipHelper.getLocalSettingCache().cleanUp();
        myLockRegistry.cleanUp();
        VideoSearchCacheHelper.getInstance().cleanUp();
        videoGenerateService.cleanUp();

        // 清空数据库后清空所有redis缓存
        redisService.clearAllCache();

        // 清空blowfish的秘钥和加解密对象
        localSettingDAO.deleteAll();
        videoFileService.deleteAllCache();
    }

    public void setLinkToken(String linkToken) {
        redisService.set(WEBSOCKET_LINK_TOKEN_KEY,linkToken);
    }

    public String getLinkToken() {
        return redisService.get(WEBSOCKET_LINK_TOKEN_KEY);
    }
}
