package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.domain.report.ReportInfo;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelChangedV2;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelConfigV2;
import com.addx.iotcamera.dao.DynamicModelConfigV2DAO;
import com.addx.iotcamera.helper.ProcessLogHelper;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.requests.SetRetainParamRequest;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.util.FuncUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.LocalDeviceSupport;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;

import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;

@Slf4j
@Service @Lazy
public class DynamicModelConfigV2Service {

    @Autowired @Lazy
    private DynamicModelConfigV2DAO dynamicModelConfigV2DAO;
    @Autowired @Lazy
    private DeviceManualService deviceManualService;
    @Autowired @Lazy
    private UserRoleService userRoleService;
    @Autowired @Lazy
    private DeviceSettingService deviceSettingService;
    @Autowired @Lazy
    private DeviceInfoService deviceInfoService;

    public static Result parseDynamicModelConfigV2(String name, String content, Map<String, Map<String, DynamicModelConfigV2>> rootKey2ModelMap) {
        Yaml yaml = new Yaml();
        JSONObject root;
        try {
            root = new JSONObject(yaml.loadAs(content, Map.class));
        } catch (Throwable e) {
            String partContent = content != null ? content.substring(Math.min(200, content.length())) : "null";
            com.addx.iotcamera.util.LogUtil.error(log, "updateDynamicModelConfigV2 配置文件不是合法的yml格式! name={},content={}", name, partContent, e);
            return new Result(10, "配置文件不是合法的yml格式! name=" + name + ",content=" + partContent, null);
        }
        for (String rootKey : root.keySet()) {
            // 即使rootKey中没有任何内容，也要创建一个modelMap，确保能删除
            Map<String, DynamicModelConfigV2> modelMap = rootKey2ModelMap.computeIfAbsent(rootKey, k -> new LinkedHashMap<>());
            JSONObject models = root.getJSONObject(rootKey);
            if (models == null) continue;
            for (String modelNo : models.keySet()) {
                Object value = models.get(modelNo);
                if (value == null) continue;
                String normalizedContent = FuncUtil.toNormalizingJsonString(JSON.toJSON(value));
                DynamicModelConfigV2 model = new DynamicModelConfigV2().setRootKey(rootKey)
                        .setModelNo(modelNo).setContent(normalizedContent);
                modelMap.put(modelNo, model);
            }
        }
        return Result.Success();
    }

    public Result<DynamicModelChangedV2> updateDynamicModelConfigV2(Map<String, Map<String, DynamicModelConfigV2>> rootKey2ModelMap) {
        String rootKey2Size = rootKey2ModelMap.entrySet().stream()
                .map(it -> "\"" + it.getKey() + "\":" + (it.getValue() == null ? null : it.getValue().size()))
                .collect(Collectors.joining(",", "{", "}"));
        log.info("updateDynamicModelConfigV2 total begin! rootKey2Size={}", rootKey2Size);
        DynamicModelChangedV2 totalChanged = new DynamicModelChangedV2();
        for (Map.Entry<String, Map<String, DynamicModelConfigV2>> entry : rootKey2ModelMap.entrySet()) {
            final String rootKey = entry.getKey();
            final Map<String, DynamicModelConfigV2> modelMap = entry.getValue();
            final List<DynamicModelConfigV2> oldModelList = dynamicModelConfigV2DAO.queryByRootKey(rootKey);
            DynamicModelChangedV2 changed = new DynamicModelChangedV2();
            List<String> deleteModelNos = new LinkedList<>();
            List<DynamicModelConfigV2> updateModels = new LinkedList<>();
            for (DynamicModelConfigV2 oldModel : oldModelList) {
                final String modelNo = oldModel.getModelNo();
                DynamicModelConfigV2 newModel = modelMap.get(modelNo);
                // 处理删除的逻辑
                if (newModel == null) {
                    deleteModelNos.add(modelNo);
                    changed.getModelNos().add(modelNo);
                    continue;
                }
                // 移除掉已存在的,留下新增的
                modelMap.remove(modelNo);
                // 处理更新的
                if (oldModel.contentIsUpdate(newModel.getContent())) {
                    updateModels.add(newModel);
                    changed.getModelNos().add(modelNo);
                    continue;
                }
                log.info("updateDynamicModelConfigV2 型号配置不变！该型号下设备settings不重发 rootKey={},modelNo={}", rootKey, modelNo);
            }
            changed.getModelNos().addAll(modelMap.keySet());
            List<DynamicModelConfigV2> saveModels = new ArrayList<>(modelMap.values());
            changed.setSaveNum(saveModels.size() > 0 ? dynamicModelConfigV2DAO.save(saveModels) : 0);
            changed.setDeleteNum(deleteModelNos.size() > 0 ? dynamicModelConfigV2DAO.deleteByRootKeyAndModelNos(rootKey, deleteModelNos) : 0);
            changed.setUpdateNum(updateModels.stream().map(dynamicModelConfigV2DAO::updateContentByRootKeyAndModelNo).reduce(0, Integer::sum));
            log.info("updateDynamicModelConfigV2 型号配置更新成功 rootKey={},changed={}", rootKey, changed);
            totalChanged.merge(changed);
        }
        log.info("dynamicModelConfig total end! totalChanged={}", totalChanged);
        return new Result(totalChanged);
    }

    public Map<String, Object> queryDynamicSettingFields(String sn) {
        if (StringUtils.isBlank(sn)) return Collections.emptyMap();
        try {
            String modelNo = deviceManualService.getModelNoBySerialNumber(sn);
            return queryByModelNo(modelNo);
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "queryDynamicSettingFields error! sn={}", sn, e);
            return Collections.emptyMap(); // 避免影响settings消息发送
        }
    }

    private Map<String, Object> queryByModelNo(String modelNo) {
        if (StringUtils.isBlank(modelNo)) return Collections.emptyMap();
        List<DynamicModelConfigV2> list = dynamicModelConfigV2DAO.queryByModelNo(modelNo);
        return list.stream().collect(Collectors.toMap(it -> it.getRootKey(), it -> JSON.parse(it.getContent())));
    }

    @Async("commonPool")
    public void publishDynamicSetting(DynamicModelChangedV2 changed, int batchSize) {
        Set<String> modelNos = changed.getModelNos();
        ProcessLogHelper processHelper = new ProcessLogHelper("publishDynamicSetting", 2, RoundingMode.DOWN);
        processHelper.setTotalNum(userRoleService.queryAllAdminUserRoleNum());
        Iterator<UserRoleDO> iterator = userRoleService.queryAllAdminUserRoleIterator("publishDynamicSetting", batchSize);
        while (iterator.hasNext()) {
            UserRoleDO userRole = iterator.next();
            try {
                String modelNo = deviceManualService.getModelNoBySerialNumber(userRole.getSerialNumber());
                if (!modelNos.contains(modelNo)) {
                    processHelper.onSkip();
                    continue;
                }
                DeviceSettingsDO storeDeviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(userRole.getSerialNumber());
                if (storeDeviceSettingsDO == null) {
                    processHelper.onSkip();
                    continue;
                }
                long startTime = System.currentTimeMillis();
                String operationId = CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID();
                ReportInfo reportInfo = ReportInfo.builder()
                        .serialNumber(userRole.getSerialNumber())
                        .startTime(startTime)
                        .userId(userRole.getUserId())
                        .operationId(operationId)
                        .build();
                LocalDeviceSupport localDeviceSupport = deviceInfoService.getDeviceSupport(userRole.getSerialNumber());
                DeviceAppSettingsDO appSettings = DeviceAppSettingsDO.ParseFrom(storeDeviceSettingsDO, localDeviceSupport);
                DeviceSettingsDO settings = DeviceSettingsDO.ParseFrom(appSettings, storeDeviceSettingsDO);
                Map<String, Object> dynamicSettingFields = queryByModelNo(modelNo);
                SetRetainParamRequest setParameterRequestRetain = deviceSettingService.initSetParameterRequest(
                        reportInfo.getOperationId(), settings, storeDeviceSettingsDO, dynamicSettingFields);
                VernemqPublisher.setParameterRetain(reportInfo.getSerialNumber(), setParameterRequestRetain);
                processHelper.onProcess(Result.Success());
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "publishDynamicSetting error!", e);
                processHelper.onProcess(Result.Failure(""));
            }
        }
        processHelper.onEnd();
    }

}