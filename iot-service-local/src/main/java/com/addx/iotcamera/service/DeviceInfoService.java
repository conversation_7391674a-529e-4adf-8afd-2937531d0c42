package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.DeviceRequest;
import com.addx.iotcamera.bean.app.additional_tier.AdditionalUserTierInfo;
import com.addx.iotcamera.bean.app.device.DeviceConfigRequest;
import com.addx.iotcamera.bean.app.device.DeviceLocationRequest;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.db.SwitchWifiOperation;
import com.addx.iotcamera.bean.device.DeviceBind;
import com.addx.iotcamera.bean.device.DeviceDormancyStatus;
import com.addx.iotcamera.bean.device.FoundDeviceInfoQuery;
import com.addx.iotcamera.bean.device.FoundDeviceInfoResult;
import com.addx.iotcamera.bean.device.attributes.DeviceDormancyInfo;
import com.addx.iotcamera.bean.device.attributes.DeviceOnlineInfo;
import com.addx.iotcamera.bean.device.attributes.OptionEnumMapping;
import com.addx.iotcamera.bean.device.model.DeviceModelAISupportDo;
import com.addx.iotcamera.bean.device.model.DeviceModelIconDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.*;
import com.addx.iotcamera.bean.domain.report.ReportInfo;
import com.addx.iotcamera.bean.openapi.OpenApiDeviceConfig;
import com.addx.iotcamera.bean.response.device.DevicePushImage;
import com.addx.iotcamera.bean.video.UploadVideoBeginRequest;
import com.addx.iotcamera.config.PowerStatConfig;
import com.addx.iotcamera.config.device.DeviceBindFirmwareConfig;
import com.addx.iotcamera.config.device.SupportMagicPixConfig;
import com.addx.iotcamera.constants.CopyWriteConstans;
import com.addx.iotcamera.constants.DeviceInfoConstants;
import com.addx.iotcamera.constants.DeviceReportKeyConstants;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.dao.SwitchWifiOperationDAO;
import com.addx.iotcamera.dao.device.IDeviceApInfoDAO;
import com.addx.iotcamera.dao.device.IDeviceSettingDAO;
import com.addx.iotcamera.enums.*;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.helper.DeviceDetectHelper;
import com.addx.iotcamera.helper.ProcessLogHelper;
import com.addx.iotcamera.helper.TraceIdHelper;
import com.addx.iotcamera.kiss.helper.DeviceSleepHelper;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.mqtt.MqttPayload;
import com.addx.iotcamera.publishers.vernemq.MqttResponsePackage;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.publishers.vernemq.responses.WakeupResponse;
import com.addx.iotcamera.service.alexa.AlexaSafemoRequestService;
import com.addx.iotcamera.service.device.*;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.statemachine.StateMachineService;
import com.addx.iotcamera.service.video.VideoCacheService;
import com.addx.iotcamera.service.video.VideoGenerateService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.util.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Predicates;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.enums.DeviceDisconnectedReasonEnums;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.exception.BaseException;
import org.addx.iot.common.proto.HandleEventAnalyticsSwitchRequest;
import org.addx.iot.common.proto.SendResult;
import org.addx.iot.common.proto.bstationd.BstationdSettingProcessorGrpc;
import org.addx.iot.common.proto.bstationd.CmdInfo;
import org.addx.iot.common.proto.bstationd.CmdRequest;
import org.addx.iot.common.proto.deviceMsg.PbSyncTimeZoneOffset;
import org.addx.iot.common.proto.deviceMsg.PbSyncTimeZoneOffsetResponse;
import org.addx.iot.common.utils.EnumFindUtil;
import org.addx.iot.common.utils.IDUtil;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.LocalDeviceSupport;
import org.addx.iot.domain.config.model.DeviceAttributeIntRange;
import org.addx.iot.domain.config.service.ICopywriteLanguageService;
import org.addx.iot.domain.config.service.IDeviceInfoService;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.addx.iotcamera.bean.device.attributes.OptionEnumMapping.getEnumNames;
import static com.addx.iotcamera.constants.DeviceModelSettingConstants.DEFAULT_VALUE_WIFI_POWER_MODE;
import static com.addx.iotcamera.constants.ReportLogConstants.*;
import static com.addx.iotcamera.constants.VideoConstants.*;
import static com.addx.iotcamera.util.DTIMUtil.calculateDTIMFatigue;
import static org.addx.iot.common.enums.ResultCollection.ACCOUNT_NEED_REGISTERED_AGAIN;
import static org.addx.iot.common.enums.ResultCollection.DEVICE_NO_ACCESS;

@Component @Lazy
public class DeviceInfoService implements IDeviceInfoService {

    private static Logger LOGGER = LoggerFactory.getLogger(DeviceInfoService.class);
    public final static String DEVICE_SUPPORT_KEY_PRE = "deviceSupport:{serialNumber}";
    private final static String DEVICE_SUPPORT_PIR_SLICE_REPORT_KEY_PRE = "deviceSupportPirSliceReport:{serialNumber}";
    public final static String EVENT_TRACKING_SWITCH_KEY_PRE = "eventTrackingSwitch:{serialNumber}";

    private final static double BASE_HALF_LIFE_VALUE = 1.0;
    /**
     * 设备固件构建号-过期时间
     */
    private final static int DEVICE_FIRMWARE_BUILDER_EXPIRE = 2 * 12 * 30 * 24 * 60 * 60;

    private final static List<String> DNS_SERVERS = Arrays.asList("8.8.8.8","8.8.4.4"); // prod-us apollo的配置

    @Value("${oldDeviceSSlTimeOffset:0}")
    private long oldDeviceSSlTimeOffset;

    @Value("${oldDeviceResponseTimestamp:0}")
    private long oldDeviceResponseTimestamp;

    @Autowired @Lazy
    private IDeviceDAO deviceDAO;

    @Autowired @Lazy
    private IDeviceSettingDAO deviceSettingDAO;

    @Autowired @Lazy
    private WowzaService wowzaService;


    @Autowired @Lazy
    private VideoService videoService;

    @Autowired @Lazy
    private PushService pushService;
    @Autowired @Lazy
    private UserRoleService userRoleService;

    @Autowired @Lazy
    private UserService userService;

    @Autowired @Lazy
    private DeviceStatusService deviceStatusService;

    @Autowired @Lazy
    private LocationInfoService locationInfoService;

    @Autowired @Lazy
    private DeviceOTAService deviceOTAService;

    @Autowired @Lazy
    private FirmwareService firmwareService;

    @Autowired @Lazy
    private RoleDefinitionService roleDefinitionService;

    @Autowired @Lazy
    private DeviceSdCardStatusService deviceSdCardStatusService;

    @Autowired @Lazy
    private SwitchWifiOperationDAO switchWifiOperationDAO;

    @Autowired @Lazy
    private MessageNotificationSettingsService messageNotificationSettingsService;

    @Autowired @Lazy
    private DeviceModelEventService deviceModelEventService;

    @Autowired @Lazy
    private AdditionalUserTierService additionalUserTierService;

    @Value("${packagePushSwitch:false}")
    private boolean packagePushSwitch;

    @Autowired @Lazy
    private ReportLogService reportLogService;

    @Autowired @Lazy
    private LibraryService libraryService;

    @Autowired @Lazy
    private OpenApiConfigService openApiConfigService;

    @Autowired @Lazy
    private DeviceSettingService deviceSettingService;

    @Autowired @Lazy
    private DeviceAiSettingsService deviceAiSettingsService;

    @Autowired @Lazy
    private StateMachineService stateMachineService;

    @Autowired @Lazy
    private RedisService redisService;
    @Autowired @Lazy
    private VideoCacheService videoCacheService;

    @Autowired @Lazy
    private DeviceSupportService deviceSupportService;

    @Autowired @Lazy
    private VernemqPublisher vernemqPublisher;

    @Autowired @Lazy
    private DeviceModelIconService deviceModelIconService;

    @Resource @Lazy
    private DeviceManualService deviceManualService;

    @Autowired @Lazy
    private S3Service s3Service;

    @Autowired @Lazy
    private DeviceService deviceService;
    @Autowired @Lazy
    private DeviceDetectHelper deviceDetectHelper;

    @Autowired @Lazy
    private IKissService kissService;

    @Autowired @Lazy

    private KissWsService kissWsService;

    @Autowired @Lazy
    private DeviceDormancyPlanService deviceDormancyPlanService;

    @Autowired @Lazy
    private ICopywriteLanguageService copywriteLanguageService;

    @Autowired @Lazy
    private TraceIdHelper traceIdHelper;
    @Resource @Lazy
    private DeviceModelConfigService deviceModelConfigService;
    @Autowired @Lazy
    private VipService vipService;

    @Autowired @Lazy
    private DeviceConfigService deviceConfigService;

    @Autowired @Lazy
    private FactoryDataQueryService factoryDataQueryService;

    @Resource @Lazy
    private DeviceBindFirmwareConfig deviceBindFirmwareConfig;

    @Autowired @Lazy
    private IDeviceApInfoDAO deviceApInfoDAO;

    @Autowired @Lazy
    private DeviceEnumMappingService deviceEnumMappingService;

    @Autowired @Lazy
    private PowerStatConfig powerStatConfig;

    @Autowired @Lazy
    private UserVipService userVipService;

    @Autowired @Lazy
    private VideoGenerateService videoGenerateService;

    @Autowired @Lazy
    private DevicePreBindService devicePreBindService;

    @Autowired @Lazy
    private AlexaSafemoRequestService alexaSafemoRequestService;

    @Autowired @Lazy
    private IUserDAO userDAO;
    @Autowired
    @Lazy
    private VideoStoreService videoStoreService;

    public static ThreadLocal<EReportEventSource> reportEventSourceThreadLocal = new ThreadLocal<>();
    public static ThreadLocal<String> reportEventSourceIpThreadLocal = new ThreadLocal<>();

    @GrpcClient(value = "grpc-client", interceptorNames = {"openTelemetryGrpcClientInterceptor"})
    BstationdSettingProcessorGrpc.BstationdSettingProcessorBlockingStub bstationdSettingProcessorBlockingStub;

    public DeviceDO getSingleDevice(DeviceDO queryDeviceDO, Integer userId) {
        DeviceDO device = deviceService.getAllDeviceInfo(queryDeviceDO.getSerialNumber());
        if (device == null) {
            return null;
        }
        List<DeviceDO> deviceDOListParam = Lists.newArrayList(device);
        List<DeviceDO> deviceDOListResult = listDevicesByUserId(queryDeviceDO.getUserId(), deviceDOListParam);
        if (!CollectionUtils.isEmpty(deviceDOListResult)) {
            //带有序列号的情况，最多只会返回一条
            device = deviceDOListResult.get(0);
            device.setDisplayGitSha(this.getDeviceFirmwareBuilder(device.getSerialNumber()));
            device.setDeviceInVip(vipService.isVipDevice(userId, device.getSerialNumber()));
            getDeviceInfoFromSetting(device);

            if (StringUtils.isEmpty(device.getDefaultCodec()) && !StringUtils.isEmpty(device.getCodec())) {
                device.setDefaultCodec(device.getCodec().contains(DeviceCodecUtil.H265) ? DeviceCodecUtil.H265 : DeviceCodecUtil.H264);
            } else {
                // do nothing
            }
            if ((device.getShowCodecChange() != null && device.getShowCodecChange().booleanValue() == true) && StringUtils.isEmpty(device.getDefaultCodec())) {
                device.setDefaultCodec(DeviceCodecUtil.H265);
            } else {
                // do nothing
            }
        } else {
            return null;
        }

        device.setDeviceModel(processDeviceModelForWebrtc(device.getSerialNumber(), device.getDeviceSupport()));
        device.setSdCard(deviceSdCardStatusService.querySdCard(queryDeviceDO.getSerialNumber()));

        device.setShowCodecChange(getSupportChangeCodec(device));
        return device;
    }

    public boolean getSupportChangeCodec(DeviceDO device) {
        return true;
    }

    public void setDeviceSupport(DeviceDO deviceDO) {
        LocalDeviceSupport localDeviceSupport = getDeviceSupport(deviceDO.getSerialNumber());
        if (localDeviceSupport != null) {
            deviceDO.setDeviceSupport(localDeviceSupport);
        }
    }

    // 获取原始的设备支持项,不包含其他表的信息
    // (deviceModel会填充deviceSupport的信息，有可能死循环)
    public LocalDeviceSupport getRowDeviceSupport(String serialNumber) {
        String key = DEVICE_SUPPORT_KEY_PRE.replace("{serialNumber}", serialNumber);
        String value = redisService.get(key);
        final LocalDeviceSupport localDeviceSupport;
        if (value == null || !JSON.isValidObject(value)) {
            localDeviceSupport = deviceSupportService.queryDeviceSupportBySn(serialNumber);
            if (localDeviceSupport != null) {
                // 把mysql数据写入redis时，有小概率会覆盖mqtt上传的新值。因此设置一个缓存时间，下次查mysql查到的就是最新值。
                Gson gson = new Gson();
                redisService.set(key, gson.toJson(localDeviceSupport), 60 * 10);
            }
        } else {
            //防止gson把整数表示成浮点数
            Gson gson = new GsonBuilder()
                    .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
                    .create();

            localDeviceSupport = gson.fromJson(value, LocalDeviceSupport.class);
        }
        return localDeviceSupport;
    }

    // 获取设备支持项，填充了其他表的信息
    public LocalDeviceSupport getDeviceSupport(String serialNumber) {
        LocalDeviceSupport localDeviceSupport = getRowDeviceSupport(serialNumber);
        if (localDeviceSupport == null) {
            localDeviceSupport = new LocalDeviceSupport(); // 创建一个空的deviceSupport对象
        }
        inflateDeviceSupport(serialNumber, localDeviceSupport); // 填充其它对象中的信息
        return localDeviceSupport;
    }

    /**
     * 给deviceSupport填充其它对象中的信息（deviceStatus,deviceModel)
     */
    public void inflateDeviceSupport(final String serialNumber, final LocalDeviceSupport localDeviceSupport) {
        if (localDeviceSupport.getSupportPirSliceReport() == null) {
            localDeviceSupport.setSupportPirSliceReport(getDeviceSupportPirSliceReport(serialNumber));
        }
        /* todo deviceStatus中部分字段转移到deviceSupport中，老的deviceSupport中没有，需要兼容，下次上线去掉 */
        final Supplier<Optional<DeviceStatusDO>> deviceStatusOnce = FuncUtil.getOnce(() -> deviceStatusService.queryDeviceStatusBySerialNumber(serialNumber));
        if (localDeviceSupport.getQuantityCharge() == null) {
            localDeviceSupport.setQuantityCharge(deviceStatusOnce.get().map(it -> it.getQuantityCharge()).orElse(false) ? 1 : 0);
        }
        if (localDeviceSupport.getAntiflickerSupport() == null) {
            // deviceStatus.antiflicker的命名有问题，其实就是来自于remoteDO.antiflickerSupport
            localDeviceSupport.setAntiflickerSupport(deviceStatusOnce.get().map(it -> it.getAntiflicker()).orElse(0));
        }
        /*** 如果固件没有上报，填充deviceModel中的数据  开始*/
        final Supplier<Optional<String>> modelNoOnce = FuncUtil.getOnce(() -> {
            return deviceManualService.getModelNoBySerialNumber(serialNumber);
        });
        final Supplier<Optional<DeviceModel>> deviceModelOnce = FuncUtil.getOnce(() -> {
            return deviceModelConfigService.queryDeviceModelConfig(serialNumber);
        });
        final Supplier<Optional<DeviceDO>> deviceOnce = FuncUtil.getOnce(() -> {
            return modelNoOnce.get().map(deviceService::getAllDeviceInfo).orElse(null);
        });
        final Supplier<Optional<OptionEnumMapping>> enumMappingOnce = FuncUtil.getOnce(() -> {
            return modelNoOnce.get().map(deviceEnumMappingService::getEnumMappingByModelNo).orElse(null);
        });
        if (org.apache.commons.lang3.StringUtils.isBlank(localDeviceSupport.getSupportStreamProtocol())) {
            localDeviceSupport.setSupportStreamProtocol(deviceModelOnce.get().map(DeviceModel::getStreamProtocol).orElse(""));
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(localDeviceSupport.getSupportAudioCodectype())) {
            localDeviceSupport.setSupportAudioCodectype(deviceModelOnce.get().map(DeviceModel::getAudioCodectype).orElse(""));
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(localDeviceSupport.getSupportKeepAliveProtocol())) {
            localDeviceSupport.setSupportKeepAliveProtocol(deviceModelOnce.get().map(DeviceModel::getKeepAliveProtocol).orElse(""));
        }
        if (localDeviceSupport.getSupportCanStandby() == null) {
            localDeviceSupport.setSupportCanStandby(deviceModelOnce.get().map(DeviceModel::isCanStandby).orElse(false) ? 1 : 0);
        }
        if (localDeviceSupport.getSupportWhiteLight() == null) {
            localDeviceSupport.setSupportWhiteLight(deviceModelOnce.get().map(DeviceModel::isWhiteLight).orElse(false));
        }
        if (localDeviceSupport.getSupportDevicePersonDetect() == null) {
            localDeviceSupport.setSupportDevicePersonDetect(deviceModelOnce.get().map(DeviceModel::isDevicePersonDetect).orElse(false) ? 1 : 0);
        }
        if (localDeviceSupport.getCanRotate() == null) {
            localDeviceSupport.setCanRotate(deviceModelOnce.get().map(DeviceModel::isCanRotate).orElse(false) ? 1 : 0);
        }
        if (localDeviceSupport.getSupportMotionTrack() == null) {
            localDeviceSupport.setSupportMotionTrack(deviceModelOnce.get().map(DeviceModel::isSupportMotionTrack).orElse(false) ? 1 : 0);
        }
        if (localDeviceSupport.getSupportFrequency() == null) {
            localDeviceSupport.setSupportFrequency(deviceModelOnce.get().map(DeviceModel::isSupportFrequency).orElse(false) ? 1 : 0);
        }
        if (localDeviceSupport.getAntiDisassemblyAlarm() == null) {
            localDeviceSupport.setAntiDisassemblyAlarm(deviceModelOnce.get().map(DeviceModel::isAntiDisassemblyAlarm).orElse(false) ? 1 : 0);
        }
        /*** 以下是设备设置通用化一期 */
        if (CollectionUtils.isEmpty(localDeviceSupport.getPirSensitivityOptions())) {
            localDeviceSupport.setPirSensitivityOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getPirSensitivityOptions())).orElseGet(Collections::emptyList));
        }
        if (CollectionUtils.isEmpty(localDeviceSupport.getPirRecordTimeOptions())) {
            localDeviceSupport.setPirRecordTimeOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getPirRecordTimeOptions())).orElseGet(Collections::emptyList));
        }
        if (CollectionUtils.isEmpty(localDeviceSupport.getPirCooldownTimeOptions())) {
            localDeviceSupport.setPirCooldownTimeOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getPirCooldownTimeOptions())).orElseGet(Collections::emptyList));
        }
        if (CollectionUtils.isEmpty(localDeviceSupport.getVideoResolutionOptions())) {
            localDeviceSupport.setVideoResolutionOptions(LocalDeviceSupport.getVideoResolutionOptions(localDeviceSupport.getDeviceSupportResolution()));
        }
        if (CollectionUtils.isEmpty(localDeviceSupport.getVideoAntiFlickerFrequencyOptions())) {
            localDeviceSupport.setVideoAntiFlickerFrequencyOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getVideoAntiFlickerFrequencyOptions())).orElseGet(Collections::emptyList));
        }
        if (CollectionUtils.isEmpty(localDeviceSupport.getLiveStreamCodecOptions())) {
            localDeviceSupport.setLiveStreamCodecOptions(DeviceCodecUtil.getCodecOptions(localDeviceSupport.getSupportChangeCodec()));
        }
        if (CollectionUtils.isEmpty(localDeviceSupport.getNightVisionSensitivityOptions())) {
            localDeviceSupport.setNightVisionSensitivityOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getNightVisionSensitivityOptions())).orElseGet(Collections::emptyList));
        }
        if (localDeviceSupport.getSupportDoorbellPressNotifySwitch() == null) {
            localDeviceSupport.setSupportDoorbellPressNotifySwitch(deviceOnce.get().map(it -> it.getModelCategory() == DeviceModelCategoryEnums.DOORBELL.getCode()).orElse(false));
        }
        /*** 如果固件没有上报，填充deviceModel中的数据 结束*/
        if (localDeviceSupport.getSupportLocalVideoLookBack() == null) {
            localDeviceSupport.setSupportLocalVideoLookBack(false); // 默认值不写入数据库
        }
        if (localDeviceSupport.getLocalVideoStorageType() == null) {
            localDeviceSupport.setLocalVideoStorageType(0);  // 默认值不写入数据库
        }
        if (DeviceAttributeIntRange.isEmpty(localDeviceSupport.getMechanicalDingDongDurationRange())) {
            localDeviceSupport.setMechanicalDingDongDurationRange(new DeviceAttributeIntRange(200, 1000, 100));
        }
        if (localDeviceSupport.getSupportSdCardFormat() == null) {
            localDeviceSupport.setSupportSdCardFormat(false);  // 默认值不写入数据库
        }
        if (localDeviceSupport.getSupportNightVisionSwitch() == null) {
            localDeviceSupport.setSupportNightVisionSwitch(true); // 必然有红外灯，不一定有白光灯
        }
        if (CollectionUtils.isEmpty(localDeviceSupport.getNightVisionModeOptions())) {
            localDeviceSupport.setNightVisionModeOptions(localDeviceSupport.getSupportWhiteLight() ? Arrays.asList("infrared", "white") : Arrays.asList("infrared"));
        }
        if (CollectionUtils.isEmpty(localDeviceSupport.getAlarmDurationOptions())) {
            localDeviceSupport.setAlarmDurationOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getAlarmDurationOptions())).orElseGet(Collections::emptyList));
        }
        if (DeviceAttributeIntRange.isEmpty(localDeviceSupport.getAlarmVolumeRange())) {
            localDeviceSupport.setAlarmVolumeRange(new DeviceAttributeIntRange(10, 100, 1));
        }
        if (DeviceAttributeIntRange.isEmpty(localDeviceSupport.getVoiceVolumeRange())) {
            localDeviceSupport.setVoiceVolumeRange(new DeviceAttributeIntRange(0, 100, 1));
        }
        if (DeviceAttributeIntRange.isEmpty(localDeviceSupport.getLiveSpeakerVolumeRange())) {
            localDeviceSupport.setLiveSpeakerVolumeRange(new DeviceAttributeIntRange(0, 100, 1));
        }
        if (localDeviceSupport.getSupportAlarmFlashLight() == null) {
            localDeviceSupport.setSupportAlarmFlashLight(localDeviceSupport.getSupportWhiteLight()); // 支持白光灯就支持报警闪光灯
        }
        if (localDeviceSupport.getSupportIndoor() == null) {
            localDeviceSupport.setSupportIndoor(deviceOnce.get().map(it -> it.getModelCategory() == DeviceModelCategoryEnums.DOORBELL.getCode()).orElse(false));
        }
        if (localDeviceSupport.getSupportStarlightSensor() == null) {
            localDeviceSupport.setSupportStarlightSensor(false);  // 默认值不写入数据库
        }
        if (localDeviceSupport.getSupportUnlimitedWebsocket() == null) {
            localDeviceSupport.setSupportUnlimitedWebsocket(false);  // 默认值不写入数据库
        }
        if (localDeviceSupport.getSupportMagicPix() == null) {
            localDeviceSupport.setSupportMagicPix(BooleanUtils.isTrue(SupportMagicPixConfig.supportMagicPix(DeviceModelNoUtil.getDeviceModelNo(serialNumber))));
        }
        if (localDeviceSupport.getWifiPowerModeOptions() == null) {
            localDeviceSupport.setWifiPowerModeOptions(Collections.EMPTY_LIST);
        }
        if (localDeviceSupport.getSupportOverallLightSwitch() == null) {
            localDeviceSupport.setSupportOverallLightSwitch(false);
        }
    }

    /**
     * 用户设备列表-不限定序列号
     *
     * @param userId
     * @return
     */
    public List<DeviceDO> listDevicesByUserId(Integer userId) {
        return listDevicesByUserId(userId, null, null);
    }

    public List<DeviceDO> listDevicesByUserId(Integer userId, List<DeviceDO> deviceDOListParam) {
        return listDevicesByUserId(userId, deviceDOListParam, null);
    }
    public List<DeviceDO> listDevicesByUserId(Integer userId, List<DeviceDO> deviceDOListParam, Integer roleId) {
        return this.listDevicesByUserId(userId, deviceDOListParam, roleId, null);
    }

    /**
    * simple method to get online and dormancy status
    * @return deviceDoList with serialNumber,online and  deviceStatus
    * */
    public List<DeviceDO>  batchGetCameraOnlineAndDormancyStatus(List<String> serialNumberList){
        List<DeviceDO> deviceDOList = new ArrayList<>();
        Map<String, DeviceStateDO> deviceStateDOMap = stateMachineService.batchGetDeviceState(serialNumberList);
        for(String serialNumber: serialNumberList){
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setSerialNumber(serialNumber);

            if(deviceStateDOMap.containsKey(serialNumber)){
                DeviceOnlineInfo deviceOnlineInfo = DeviceStatusService.getDeviceOnlineInfoByState(deviceStateDOMap.get(serialNumber));
                deviceDO.setOnline(deviceOnlineInfo.getOnline());
            }else{
                deviceDO.setOnline(DeviceOnlineStatusEnums.OFFLINE.getCode());
            }

            final DeviceDormancyInfo dormancyInfo = getDeviceDormancyInfo(deviceDO.getSerialNumber(), 0, deviceDO.getOnline());
            deviceDO.setDeviceStatus(dormancyInfo.getDeviceStatus());

            deviceDOList.add(deviceDO);
        }
        return deviceDOList;
    }

    public List<DeviceDO> listDevicesByUserId(Integer userId, List<DeviceDO> deviceDOListParam, Integer roleId, String serialNumber) {
        User user = userService.queryUserById(userId);
        if (user == null) {
           throw new BaseException(ACCOUNT_NEED_REGISTERED_AGAIN);
        }
        List<DeviceDO> deviceDOList = Lists.newArrayList();
        List<UserRoleDO> userRoleDOList = userRoleService.getUserRoleByUserId(userId, roleId);
        if (StringUtils.hasLength(serialNumber)) {
            userRoleDOList = userRoleDOList.stream()
                    .filter(r -> serialNumber.equals(r.getSerialNumber()) || serialNumber.equals(r.getAssociationSn()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(userRoleDOList)) {
            return deviceDOList;
        }

        //获取设备序列号列表
        List<String> serialNumberList = Lists.newArrayList();
        //设备相关用户
        Set<Integer> userIdSet = Sets.newHashSet();
        //设备与用户关系map
        Map<String, UserRoleDO> mapSerialNumberToUserRole = Maps.newHashMap();
        //初始化需要关联其他表的参数
        userRoleDOList.forEach(userRoleDO -> {
            serialNumberList.add(userRoleDO.getSerialNumber());

            if (!userIdSet.contains(userRoleDO.getAdminId())) {
                userIdSet.add(userRoleDO.getAdminId());
            }
            if (!userIdSet.contains(userRoleDO.getUserId())) {
                userIdSet.add(userRoleDO.getUserId());
            }

            mapSerialNumberToUserRole.put(userRoleDO.getSerialNumber(), userRoleDO);
        });
        //设备记录,
        deviceDOList = CollectionUtils.isEmpty(deviceDOListParam) ? queDeviceBySerialNumbers(serialNumberList) : deviceDOListParam;
        if (CollectionUtils.isEmpty(deviceDOList)) {
            return deviceDOList;
        }
        // 对设备按绑定时间排序
        deviceDOList = this.deviceDOListSort(deviceDOList, userRoleDOList);

        //设备关联位置
        Set<Integer> locationSet = Sets.newHashSet();
        deviceDOList.forEach(deviceDO -> {
            if (!locationSet.contains(deviceDO.getLocationId())) {
                locationSet.add(deviceDO.getLocationId());
            }
        });
        Map<Integer, LocationDO> locationDOMap = locationInfoService.queryLocationDOs(locationSet);
        //获取用户相关信息
        Map<Integer, User> mapUser = userService.queryUserMap(new ArrayList<>(userIdSet));
        //设备状态
        Map<String, DeviceStatusDO> mapDeviceStatusDO = deviceStatusService.queryDeviceStatus(serialNumberList);
        Map<String, DeviceStateDO> deviceStateDOMap = Collections.emptyMap();
        try {
            deviceStateDOMap = stateMachineService.batchGetDeviceState(serialNumberList);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "failed batchGetDeviceState serialNumberList:{}", serialNumberList);
        }

        Map<String, DeviceManualDO> maoDeviceManufactureDO = deviceManualService.getDeviceManualMap(serialNumberList);
        //device_ota
        Map<String, DeviceOTADO> mapDeviceOta = deviceOTAService.queryDeviceOtaBySerialNumber(serialNumberList);

        //组装各部分信息
        int time = (int) (System.currentTimeMillis() / 1000);
        Iterator<DeviceDO> iterator = deviceDOList.iterator();
        while (iterator.hasNext()) {
            DeviceDO deviceDO = iterator.next();

            Integer modelCategory = Optional.ofNullable(deviceDO.getModelCategory()).orElse(DeviceModelCategoryEnums.CAMERA.getCode());
            if (modelCategory.equals(DeviceModelCategoryEnums.BASE_STATION.getCode())) {
                //基站绑定的CX列表
                deviceDO.setAssociationNum((int) deviceDOList.stream().filter(d -> d.getModelCategory() == null || !d.getModelCategory().equals(DeviceModelCategoryEnums.BASE_STATION.getCode())).count());
            } else {
                deviceDO.setAssociationNum(0);
            }


            UserRoleDO userRoleDO = mapSerialNumberToUserRole.get(deviceDO.getSerialNumber());
            if (userRoleDO == null) {
                // 当 deviceDOListParam 不为空时，deviceDOList有可能和mapSerialNumberToUserRole不一致
                iterator.remove();
                continue;
            }
            //设备所处位置
            fillDeviceLocation(deviceDO, locationDOMap);
            //用户相关信息
            fillDeviceUserInfo(userId, deviceDO, userRoleDO, mapUser);
            //设备状态相关
            fillDeviceStatus(deviceDO, mapDeviceStatusDO, deviceStateDOMap, time, user);

            fillDeviceManufacture(deviceDO, maoDeviceManufactureDO);
            //设备ota
            dillDeviceOta(deviceDO, mapDeviceOta, maoDeviceManufactureDO, modelCategory);

            //初始化设备图标
            fillDeviceInfo(deviceDO);
            //设置设备维度vip等级
            deviceDO.setDeviceVipLevel(vipService.queryLastDeviceVip(user.getTenantId(), deviceDO.getSerialNumber()).getVipLevel());
            //填充设备设置
            fillDeviceSetting(deviceDO);

            // 查询设备音频设置
            if (deviceDO.getLiveAudioToggleOn() == null) {
                DeviceConfigRequest.DeviceAudio deviceAudio = deviceConfigService.queryDeviceAudio(userId, deviceDO.getSerialNumber());
                if (deviceAudio != null) {
                    deviceDO.setLiveAudioToggleOn(deviceAudio.getLiveAudioToggleOn());
                    deviceDO.setRecordingAudioToggleOn(deviceAudio.getRecordingAudioToggleOn());
                    deviceDO.setLiveSpeakerVolume(deviceAudio.getLiveSpeakerVolume());
                }
            }

            deviceDO.setDeviceModel(processDeviceModelForWebrtc(deviceDO.getSerialNumber(), deviceDO.getDeviceSupport()));
            if (StringUtils.hasLength(deviceDO.getModelNo())) {
                final Set<String> supportEvents = deviceModelEventService.queryRowDeviceModelEvent(deviceDO.getModelNo());
                deviceDO.setSupportBirdVip(supportEvents.contains(AiObjectEnum.BIRD.getObjectName()));
            }

            // 设备截图image
            deviceDO.setDevicePushImage(this.initDevicePushImage(deviceDO.getSerialNumber(), deviceDO.getAdminId()));
        }

        return deviceDOList;
    }

    private void fillDeviceSetting(DeviceDO deviceDO) {
        if (deviceDO == null || deviceDO.getSerialNumber() == null) return;
        try {
            DeviceSettingsDO settingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(deviceDO.getSerialNumber());
            deviceDO.setWifiPowerMode(Optional.ofNullable(settingsDO).map(it -> it.getWifiPowerMode()).orElse(DEFAULT_VALUE_WIFI_POWER_MODE));
        } catch (Throwable e) {
            LOGGER.info("fillDeviceSetting error! sn={}", deviceDO.getSerialNumber(), e);
        }
    }

    /**
     * 设备列表按绑定时间排序
     *
     * @param sourceDeviceDOList
     * @param userRoleDOList
     * @return
     */
    private List<DeviceDO> deviceDOListSort(List<DeviceDO> sourceDeviceDOList, List<UserRoleDO> userRoleDOList) {
        Map<String, DeviceDO> deviceDOMap = sourceDeviceDOList.stream().collect(
                Collectors.toMap(DeviceDO::getSerialNumber, DeviceDO -> DeviceDO)
        );
        List<DeviceDO> newDeviceDOList = Lists.newArrayList();
        for (UserRoleDO userRoleDO : userRoleDOList) {
            if (!deviceDOMap.containsKey(userRoleDO.getSerialNumber())) {
                continue;
            }
            newDeviceDOList.add(deviceDOMap.get(userRoleDO.getSerialNumber()));
        }
        return newDeviceDOList;
    }

    /**
     * 获取设备图标
     *
     * @param deviceDO
     */
    private void fillDeviceInfo(DeviceDO deviceDO) {
        if (!StringUtils.hasLength(deviceDO.getModelNo())) {
            return;
        }
        DeviceModelIconDO deviceModelIconDO = deviceModelIconService.queryDeviceModelIcon(deviceDO.getModelNo());
        deviceDO.setIcon(deviceModelIconDO == null ?
                "" : deviceModelIconDO.getIconUrl());
        deviceDO.setSmallIcon(deviceModelIconDO == null ?
                "" : deviceModelIconDO.getSmallIconUrl());

        DeviceDBApInfo deviceDBApInfo = deviceApInfoDAO.queryBySn(deviceDO.getSerialNumber());
        deviceDO.setApInfo(deviceDBApInfo == null ? "" : deviceDBApInfo.getApInfo());
    }

    /**
     * 设备ota相关信息组装
     *
     * @param deviceDO
     * @param mapDeviceOta
     * @param maoDeviceManufactureDO
     * @param modelCategory
     */
    private void dillDeviceOta(DeviceDO deviceDO, Map<String, DeviceOTADO> mapDeviceOta, Map<String, DeviceManualDO> maoDeviceManufactureDO, Integer modelCategory) {
        if (!mapDeviceOta.containsKey(deviceDO.getSerialNumber())) {
            LOGGER.info("listDevicesByUserId mapDeviceOta not contains SerialNumber:{}", deviceDO.getSerialNumber());
            return;
        }
        if (!maoDeviceManufactureDO.containsKey(deviceDO.getSerialNumber())) {
            LOGGER.info("listDevicesByUserId maoDeviceManufactureDO not contains SerialNumber:{}", deviceDO.getSerialNumber());
            return;
        }

        DeviceOTADO deviceOTADO = mapDeviceOta.get(deviceDO.getSerialNumber());
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView(deviceDO, deviceOTADO);
        if (firmwareViewDO != null) {
            deviceDO.setFirmwareStatus(firmwareViewDO.getFirmwareStatus());
            deviceDO.setNewestFirmwareId(firmwareViewDO.getTargetFirmware());
        }

        //设置今晚升级属性，如果不展示，则检查下是否开始时间已经超时，如果超时则改为可展示
        //ota 成功后，改为 可展示 开始时间清0
        //ota 失败后，如果失败时间-开始时间> 重试时长，则改为 可展示 开始时间清0 ；否则等待明天继续重试
        deviceDO.setCanUpdateTonight(true);
        if(deviceDO.getUpdateTonight() == 0){
            deviceDO.setCanUpdateTonight(false);
            if((System.currentTimeMillis()/1000 - deviceDO.getUpdateTonightStarttime()) > 86400L * deviceDO.getUpdateTonightRetryCount()){
                deviceDO.setCanUpdateTonight(true);
            }

        }
    }

    private void fillDeviceManufacture(DeviceDO deviceDO, Map<String, DeviceManualDO> maoDeviceManufactureDO) {
        if (maoDeviceManufactureDO.containsKey(deviceDO.getSerialNumber())) {
            DeviceManualDO deviceManufactureDO = maoDeviceManufactureDO.get(deviceDO.getSerialNumber());
            deviceDO.setUserSn(deviceManufactureDO.getUserSn());
            // 这个hack是因为最开始App组没有做model no和display model no的区分，为了兼容不得不做
            deviceDO.setModelNo("SP10".equals(deviceManufactureDO.getDisplayModelNo()) ? "SP10" : deviceManufactureDO.getModelNo());
            deviceDO.setMacAddress(deviceManufactureDO.getMacAddress());
            deviceDO.setMcuNumber(deviceManufactureDO.getMcuNumber());
            deviceDO.setDisplayModelNo(StringUtils.isEmpty(deviceManufactureDO.getDisplayModelNo()) ? deviceDO.getModelNo() : deviceManufactureDO.getDisplayModelNo());
        }
    }


    /**
     * 填充设备状态相关信息
     *
     * @param deviceDO
     * @param mapDeviceStatusDO
     * @param time
     */
    public void fillDeviceStatus(DeviceDO deviceDO, Map<String, DeviceStatusDO> mapDeviceStatusDO, Map<String, DeviceStateDO> deviceStateDOMap, int time, User user) {

        // bx 设备默认在线
        deviceDO.setOnline(deviceDO.getModelCategory().equals(DeviceModelCategoryEnums.BASE_STATION.getCode()) ? 1 : 0);
        deviceDO.setAwake(deviceDO.getModelCategory().equals(DeviceModelCategoryEnums.BASE_STATION.getCode()) ? 1 : 0);

        if (mapDeviceStatusDO.containsKey(deviceDO.getSerialNumber())) {
            DeviceStatusDO deviceStatusDO = mapDeviceStatusDO.get(deviceDO.getSerialNumber());
            final DeviceOnlineInfo onlineInfo = deviceStatusService.getDeviceOnlineInfo(deviceDO.getSerialNumber(), time, deviceStatusDO, !MapUtils.isEmpty(deviceStateDOMap) && deviceStateDOMap.containsKey(deviceDO.getSerialNumber()) ? deviceStateDOMap.get(deviceDO.getSerialNumber()) : null);

            deviceDO.setOnline(deviceDO.getModelCategory().equals(DeviceModelCategoryEnums.BASE_STATION.getCode()) ? 1 : onlineInfo.getOnline());
            deviceDO.setAwake(deviceDO.getModelCategory().equals(DeviceModelCategoryEnums.BASE_STATION.getCode()) ? 1 : onlineInfo.getAwake());
            deviceDO.setOfflineTime(onlineInfo.getOfflineTime());
            deviceDO.setBatteryLevel(deviceStatusDO.getBatteryLevel());
            deviceDO.setIsCharging(deviceStatusDO.getIsCharging());
            deviceDO.setSignalStrength(deviceStatusDO.getSignalStrength());
            deviceDO.setWifiPowerLevel(deviceStatusDO.getWifiPowerLevel());
            deviceDO.setNetworkName(deviceStatusDO.getNetworkName());
            deviceDO.setIp(deviceStatusDO.getIp());
            deviceDO.setWifiChannel(deviceStatusDO.getWifiChannel());
            final LocalDeviceSupport localDeviceSupport = getDeviceSupport(deviceDO.getSerialNumber());
            deviceDO.setQuantityCharge(Optional.ofNullable(localDeviceSupport).map(it -> it.getQuantityCharge()).orElse(0) > 0);
            deviceDO.setAntiflickerSupport(Optional.ofNullable(localDeviceSupport).map(it -> it.getAntiflickerSupport()).orElse(0) > 0);
            deviceDO.setCodec(deviceStatusDO.getCodec());
            deviceDO.setLinkedPlatforms(deviceStatusDO.getLinkedPlatforms());
            deviceDO.setLiveAudioToggleOn(deviceStatusDO.getLiveAudioToggleOn());
            deviceDO.setRecordingAudioToggleOn(deviceStatusDO.getRecordingAudioToggleOn());
            deviceDO.setLiveSpeakerVolume(deviceStatusDO.getLiveSpeakerVolume());
            deviceDO.setAlarmWhenRemoveToggleOn(deviceStatusDO.getAlarmWhenRemoveToggleOn());
            deviceDO.setChargingMode(deviceStatusDO.getChargingMode());
            //默认改为hub
            deviceDO.setDeviceNetType(Optional.ofNullable(deviceStatusDO.getDeviceNetType()).orElse(DeviceNetType.HUB.getCode()));
            final DeviceDormancyInfo dormancyInfo = getDeviceDormancyInfo(deviceDO.getSerialNumber(), deviceDO.getUserId(), deviceDO.getOnline());
            deviceDO.setDeviceStatus(dormancyInfo.getDeviceStatus());
            deviceDO.setDeviceDormancyMessage(dormancyInfo.getDeviceDormancyMessage());
            deviceDO.setDeviceDormancyWakeTime(dormancyInfo.getDeviceDormancyWakeTime());

            deviceDO.setSolarModelNo(deviceStatusDO.getSolarModelNo());
            deviceDO.setSolarSn(deviceStatusDO.getSolarSn());
            deviceDO.setSolarOriginalModelNo(deviceStatusDO.getSolarOriginalModelNo());
            deviceDO.setBatterySn(deviceStatusDO.getBatterySn());
            deviceDO.setBatteryModelNo(deviceStatusDO.getBatteryModelNo());
            deviceDO.setBatteryRemoval(deviceStatusDO.getBatteryRemoval());
            deviceDO.setVideoHasCodecBeginTime(videoStoreService.getVideoHasCodecBeginTime(deviceDO.getAdminId(), deviceDO.getSerialNumber()));

            LOGGER.debug("fillDeviceStatus debug deviceDo:{}, onlineInfo:{}", deviceDO, onlineInfo);
            fillDeviceStatusReason(onlineInfo, deviceDO);
        }

        setDeviceSupport(deviceDO);
    }

    public void fillDeviceStatusReason(DeviceOnlineInfo onlineInfo, DeviceDO deviceDO) {
        try {
            if (deviceDO == null || onlineInfo == null) {
                return;
            }
            if (!DeviceOnlineStatusEnums.OFFLINE.getCode().equals(deviceDO.getOnline())) {
                return;
            }
            final Integer reason = onlineInfo.getReasonFromData();
            if (reason == null) {
                return;
            }
            final DeviceDisconnectedReasonEnums disconnectedReason = DeviceDisconnectedReasonEnums.codeOf(reason);
            if (disconnectedReason != null && disconnectedReason.getAppEnum() != null) {
                deviceDO.setDeviceStatus(disconnectedReason.getAppEnum().getCode());
            }
        } catch (Throwable e) {
            LOGGER.error("fillDeviceStatusReason error! sn={}", deviceDO.getSerialNumber(), e);
        }
    }

    public DeviceDormancyInfo getDeviceDormancyInfo(String sn, Integer userId, Integer online) {
        // 此状态后续会扩展，3表示在线且休眠，11表示低电量关机，12表示按键关机，0是默认值无特殊含义
        final DeviceDormancyInfo deviceDO = new DeviceDormancyInfo().setDeviceStatus(0);
        if (DeviceOnlineStatusEnums.ONLINE.getCode().equals(online)) {
            DeviceDormancyStatus deviceDormancyStatus = deviceDormancyPlanService.queryDeviceDormancyStatus(sn);
            if (deviceDormancyStatus != null && (deviceDormancyStatus.getStatus().equals(DeviceDormancyStatusEnums.DORMANCY_PLAN_DORMANCY.getCode()) ||
                    deviceDormancyStatus.getStatus().equals(DeviceDormancyStatusEnums.APP_DORMANCY.getCode()))) {
                // 表示处于休眠状态
                deviceDO.setDeviceStatus(DeviceDormancyPlanService.SLEEP);
                // 这段文案后续由APP维护
                deviceDO.setDeviceDormancyMessage(this.getDeviceDormancyMessage(deviceDormancyStatus.getWakeupTime(),
                        deviceDormancyStatus.getStatus(), userId));
                //设备退出休眠计划时间戳
                deviceDO.setDeviceDormancyWakeTime(deviceDormancyStatus.getWakeupTime());
            }
        }
        return deviceDO;
    }

    /**
     * 返回唤醒时间
     *
     * @param expireTime
     * @param deviceDormancyStatusType
     * @param userId
     * @return
     */
    private String getDeviceDormancyMessage(Long expireTime, Integer deviceDormancyStatusType, Integer userId) {
        LOGGER.info("device dormancy status expireTime:{},deviceDormancyStatusType:{}, userId:{}", expireTime, deviceDormancyStatusType, userId);
        long currentTime = Instant.now().getEpochSecond();
        User user = userService.queryUserById(userId);
        if (DeviceDormancyStatusEnums.APP_DORMANCY.getCode().equals(deviceDormancyStatusType) || expireTime < currentTime) {
            return copywriteLanguageService.queryCopywrite(user.getLanguage(), CopyWriteConstans.DEVICE_DORMANCY);
        }

        Long tomorrowTime = DateUtils.getDateAfter(new Date(), 1).getTime() / 1000;
        if (expireTime < tomorrowTime) {
            // 当天唤醒
            String timeFormatter = DateTimeFormatter.ofPattern("HH:mm").format(
                    LocalDateTime.ofEpochSecond(expireTime, 0, ZoneOffset.ofHours(8)));
            String message = copywriteLanguageService.queryCopywrite(user.getLanguage(), CopyWriteConstans.DEVICE_DORMANCY_TODAY);
            if (StringUtils.isEmpty(message)) {
                com.addx.iotcamera.util.LogUtil.warn(LOGGER, "getDeviceDormancyMessage deviceDormancyToday empty");
                return "";
            }
            return message.replace("{wakeTime}", timeFormatter);
        }
        Long afterOfTomorrowTime = DateUtils.getDateAfter(new Date(), 2).getTime() / 1000;
        if (expireTime < afterOfTomorrowTime) {
            //第二天唤醒
            String timeFormatter = DateTimeFormatter.ofPattern("HH:mm").format(
                    LocalDateTime.ofEpochSecond(expireTime, 0, ZoneOffset.ofHours(8)));
            String message = copywriteLanguageService.queryCopywrite(user.getLanguage(), CopyWriteConstans.DEVICE_DORMANCY_TOMORROW);
            if (StringUtils.isEmpty(message)) {
                com.addx.iotcamera.util.LogUtil.warn(LOGGER, "getDeviceDormancyMessage deviceDormancyTomorrow empty");
                return "";
            }
            return message.replace("{wakeTime}", timeFormatter);
        }
        //两天后唤醒
        String weekDay = Instant.ofEpochSecond(expireTime).atZone(ZoneId.systemDefault()).getDayOfWeek().toString();
        String message = copywriteLanguageService.queryCopywrite(user.getLanguage(), CopyWriteConstans.DEVICE_DORMANCY_MONDAY);
        if (StringUtils.isEmpty(message)) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "getDeviceDormancyMessage deviceDormancyWeekday empty");
            return "";
        }
        return message.replace("{weekday}", weekDay);
    }

    /**
     * 填充设备用户相关信息
     *
     * @param deviceDO
     * @param mapUser
     */
    private void fillDeviceUserInfo(Integer userId, DeviceDO deviceDO, UserRoleDO userRoleDO, Map<Integer, User> mapUser) {
        /* 不一定
    private void fillDeviceUserInfo(Integer userId, DeviceDO deviceDO, Map<String, UserRoleDO> mapSerialNumberToUserRole, Map<Integer, User> mapUser) {
        //值一定会存在
        UserRoleDO userRoleDO = mapSerialNumberToUserRole.get(deviceDO.getSerialNumber());
        */
        deviceDO.setUserId(userRoleDO.getUserId());
        deviceDO.setAssociationSn(userRoleDO.getAssociationSn());
        deviceDO.setAdminId(userRoleDO.getAdminId());
        if (mapUser.containsKey(userRoleDO.getAdminId())) {
            if (userId.equals(userRoleDO.getAdminId())) {
                deviceDO.setAdminEmail(mapUser.get(userRoleDO.getAdminId()).getEmail());
                deviceDO.setAdminPhone(mapUser.get(userRoleDO.getAdminId()).getPhone());
            } else {
                deviceDO.setAdminEmail(userService.userEmailProtect(mapUser.get(userRoleDO.getAdminId()).getEmail()));
                deviceDO.setAdminPhone(userService.userPhoneProtect(mapUser.get(userRoleDO.getAdminId()).getPhone()));
            }

            deviceDO.setAdminName(mapUser.get(userRoleDO.getAdminId()).getName());

            RoleDefinitionDO roleDefinitionDO = roleDefinitionService.quRoleDefinitionDO(Integer.valueOf(userRoleDO.getRoleId()));
            deviceDO.setRole(roleDefinitionDO.getRole());
            deviceDO.setRoleName(roleDefinitionDO.getRoleName());
        }
    }


    /**
     * 填充位置名称
     *
     * @param deviceDO
     * @param locationDOMap
     */
    private void fillDeviceLocation(DeviceDO deviceDO, Map<Integer, LocationDO> locationDOMap) {
        if (locationDOMap.containsKey(deviceDO.getLocationId())) {
            deviceDO.setLocationName(locationDOMap.get(deviceDO.getLocationId()).getLocationName());
        }
    }

    /**
     * 根据序列号查询设备列表
     *
     * @param serialNumbers
     * @return
     */
    public List<DeviceDO> queDeviceBySerialNumbers(List<String> serialNumbers) {
        //获取设备列表
        return deviceDAO.queryDeviceBynSerialNumbers(serialNumbers);
    }

    public List<MsgResponseDO> getAllPushDetailBySerialNumber(String serialNumber) {
        // 如果已经忽略推送，则直接在此处查询是就不带出推送信息
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        if (deviceDO.getPushIgnored()) {
            return new ArrayList<>();
        }

        // 不需要忽略推送，则正常查询所有应该收到推送的用户
        return deviceDAO.getPushDetailBySerialNumber(serialNumber);
    }

    /**
     * 查询应该受到推送的用户
     *
     * @param serialNumber
     * @return
     */
    public List<MsgResponseDO> getAllPushDetailBySerialNumberNoFollowPushIgnored(String serialNumber) {
        // 不需要忽略推送，正常查询所有应该收到推送的用户
        return deviceDAO.getPushDetailBySerialNumber(serialNumber);
    }

    /**
     * 更新设备firmware
     *
     * @param serialNumber
     * @param firmwareId
     */
    public void updateFirmwareVersionBySerialNumber(String serialNumber, String firmwareId) {
        String originalFirmwareId = deviceService.queryDeviceFirmware(serialNumber);
        if (StringUtils.isEmpty(firmwareId)) {
            return;
        }
        if (firmwareId.equals(originalFirmwareId)) {
            // 要更新的值与数据库中的值相同则不更新
            return;
        }

        deviceService.updateFirmwareVersionBySerialNumber(serialNumber, firmwareId);
    }

    /**
     * 更新设备mcuVersion
     */
    public void updateDeviceMcuVersion(String serialNumber, String mcuNumber) {
        if (StringUtils.isEmpty(mcuNumber)) {
            return;
        }
        String originalMcuVersion = deviceManualService.queryMcuVersionBySerialNumber(serialNumber);
        if (StringUtils.isEmpty(originalMcuVersion)) {
            // 未查询出结果表示数据表中无此记录，绑定还未完成，status 就已经上报了
            return;
        }
        if (originalMcuVersion.equals(mcuNumber)) {
            return;
        }
        deviceManualService.updateMcuVersionBySerialNumber(serialNumber, mcuNumber);
    }

    /**
     * 判断是否需要记录电量变化Log
     *
     * @param key
     * @param batteryLevel
     * @return
     */
    private boolean needDeviceStatusBatteryLog(String key, Integer batteryLevel) {
        if (!redisService.hasDeviceOperationDo(key)) {
            return true;
        }

        String batteryValue = redisService.get(key);
        if (StringUtils.isEmpty(batteryValue)) {
            return true;
        }

        Integer storeBattery = Integer.valueOf(batteryValue);
        if (storeBattery.equals(batteryLevel)) {
            return false;
        }
        return true;
    }


    public void updateHardwareStatus(DeviceDO device) {
        if (device.getWhiteLight() == null) {
            device.setWhiteLight(WhiteLightEnums.NOSUPPORT.getCode());
        }
        if (device.getDeviceSupport() != null) {
            this.initDeviceSupport(device.getSerialNumber(), device.getDeviceSupport());
        }

        this.deviceFirmwareBuilder(device);

        // 设备status上报 status信息缓存 手动解决保留一些原有字段
        DeviceStatusDO newDeviceStatusDO = DeviceStatusDO.parseFrom(device);
        try {
            DeviceStatusDO existDeviceStatusDO = deviceStatusService.queryDeviceStatusBySerialNumber(device.getSerialNumber());
            if (existDeviceStatusDO != null) {
                newDeviceStatusDO.setLinkedPlatforms(existDeviceStatusDO.getLinkedPlatforms());
            }
        } catch (Exception e) {
            LOGGER.info("setLinkedPlatforms error ", e);
        }
        deviceStatusService.saveDeviceStatus(newDeviceStatusDO);
    }


    /**
     * 固件构建号
     *
     * @param device
     */
    public void deviceFirmwareBuilder(DeviceDO device) {
        if (StringUtils.isEmpty(device.getDisplayGitSha())) {
            return;
        }
        String key = DeviceInfoConstants.DEVICE_FIRMWARE_BUILDER.replace("{serialNumber}", device.getSerialNumber());
        redisService.set(key, device.getDisplayGitSha(), DEVICE_FIRMWARE_BUILDER_EXPIRE);
    }

    /**
     * 获取设备构建号
     *
     * @param serialNumber
     * @return
     */
    public String getDeviceFirmwareBuilder(String serialNumber) {
        String key = DeviceInfoConstants.DEVICE_FIRMWARE_BUILDER.replace("{serialNumber}", serialNumber);
        String deviceFirmwareBuilder = redisService.get(key);
        return StringUtils.isEmpty(deviceFirmwareBuilder) ? "" : deviceFirmwareBuilder;
    }

    /**
     * redis存储固件支持的属性
     *
     * @param serialNumber
     * @param localDeviceSupport
     */
    public void initDeviceSupport(String serialNumber, LocalDeviceSupport localDeviceSupport) {
        String key = DEVICE_SUPPORT_KEY_PRE.replace("{serialNumber}", serialNumber);
        Gson gson = new Gson();
        redisService.set(key, gson.toJson(localDeviceSupport));
    }

    public void setDeviceSupportPirSliceReport(String serialNumber, Integer value) {
        String key = DEVICE_SUPPORT_PIR_SLICE_REPORT_KEY_PRE.replace("{serialNumber}", serialNumber);
        redisService.set(key, value != null ? (value + "") : "0");
    }

    private Integer getDeviceSupportPirSliceReport(String serialNumber) {
        String key = DEVICE_SUPPORT_PIR_SLICE_REPORT_KEY_PRE.replace("{serialNumber}", serialNumber);
        return "1".equals(redisService.get(key)) ? 1 : 0;
    }

    /**
     * 查询设备是否支持 killKeepAlive
     *
     * @param serialNumber
     * @return
     */
    public boolean deviceKillKeepAliveSupport(String serialNumber) {
        LocalDeviceSupport localDeviceSupport = getDeviceSupport(serialNumber);
        return localDeviceSupport == null ? false : localDeviceSupport.isKillKeepAlive();
    }

    // 影响较大，需要仔细测试后再添加
    @CacheEvict(value = "deviceSetting", key = "#deviceSettingsDO.serialNumber")
    public Integer setDeviceSettings(DeviceSettingsDO deviceSettingsDO) {
        processSettingNullValue(deviceSettingsDO);
        LOGGER.info("device setting param:{}", deviceSettingsDO);
        if (null == deviceSettingDAO.getDeviceSettingsBySerialNumber(deviceSettingsDO.getSerialNumber())) {
            return deviceSettingDAO.initDeviceSettings(deviceSettingsDO);
        } else {
            return deviceSettingDAO.updateDeviceSettings(deviceSettingsDO);
        }
    }

    @CacheEvict(value = "deviceSetting", key = "#appSettingsDO.serialNumber")
    public Integer updateDeviceTimeZone(DeviceAppSettingsDO appSettingsDO) {
        LOGGER.info("Update timezone to {} for device {}", appSettingsDO.getTimeZone(), appSettingsDO.getSerialNumber());
        if (StringUtils.isEmpty(appSettingsDO.getTimeZone())) {
            return 0;
        }
        return deviceSettingDAO.updateDeviceTimeZone(appSettingsDO.getSerialNumber(), appSettingsDO.getTimeZone());
    }

    /**
     * 校验是否通知ai
     */
    public List<String> pushAiList(String serialNumber, Integer userId, Collection<String> vipSupportEventObjects) {
        if (CollectionUtils.isEmpty(vipSupportEventObjects)) return Collections.emptyList();
        // 用户设置的需要ai分析的事件对象
        Set<String> userEventObjects = deviceAiSettingsService.queryEnableEventObjects(userId, serialNumber)
                .stream().map(AiObjectEnum::getObjectName).collect(Collectors.toSet());
        // 设备型号的需要ai分析的事件对象
//        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
//        Set<String> modelEventObjects = deviceModelEventService.queryRowDeviceModelEvent(modelNo);
        Set<String> modelEventObjects = new LinkedHashSet<>(vipSupportEventObjects);
        // 取交集
        List<String> retainEventObjects = FuncUtil.retainToList(modelEventObjects, userEventObjects, vipSupportEventObjects);
        LOGGER.info("pushAiList sn={},userId={},userEventObjects={},modelEventObjects={},vipSupportEventObjects={},retainEventObjects={}", serialNumber, userId, userEventObjects, modelEventObjects, vipSupportEventObjects, retainEventObjects);
        return retainEventObjects;
    }

    // 当前是否是鸟类vip
    @Deprecated // replace by com.addx.iotcamera.service.openapi.PaasVipService.isBirdVipDevice
    public boolean isBirdVip(Integer adminId) {
        try {
            User user = userService.queryUserById(adminId);
            List<AdditionalUserTierInfo> list = additionalUserTierService.getActiveAdditionalUserTierInfo(adminId, user.getTenantId(), user.getLanguage());
            return list.stream().anyMatch(it -> it.getType() == 1);
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "isBirdVip error! adminId={}", adminId, e);
            return false;
        }
    }

    public Result handleDeviceAwake(DeviceRequestDO awakeDO) throws IdNotSetException {
        WakeupResponse response = new WakeupResponse();
        response.setId(awakeDO.getRequestId());
        response.setTime(PhosUtils.getUTCStamp());
        String serialNumber = awakeDO.getSerialNumber();
        DeviceDO storedDevice = deviceService.getAllDeviceInfo(serialNumber);

        //判断是否需要手动设置设备的对时, 避免因为mqtt ssl证书过期导致设备连不上
        if (oldDeviceSSlTimeOffset != 0 || oldDeviceResponseTimestamp != 0) {
            //修改response time
            try {
                String firmwareId = deviceService.queryDeviceFirmware(serialNumber);
                String modelNo = StringUtils.isEmpty(storedDevice.getModelNo()) ? deviceManualService.getModelNoBySerialNumber(serialNumber) : storedDevice.getModelNo();
                response.setTime(OldDeviceWakeupResponseTimeUtil.getResponseTime(modelNo, firmwareId, oldDeviceSSlTimeOffset, oldDeviceResponseTimestamp));
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.warn(LOGGER, "set oldDeviceSSlTimeOffset failed, use current time", e);
            }
        }

        WakeupResponse.WakeupResponseValue wakeupResponseValue = response.new WakeupResponseValue();

        if (null == storedDevice || 0 == storedDevice.getActivated()) {
            // 2019.11.25 重做绑定逻辑后，不再向G0摄像头返回的MQTT消息中设置错误的状态码
            // response.setCode(MqttResponseCode.NOT_ACTIVATED);
            wakeupResponseValue.setDeviceStatus(ResultCollection.DEVICE_UNACTIVATED.getCode());
            response.setValue(wakeupResponseValue);
            VernemqPublisher.wakeupResponse(serialNumber, response);
            return Result.Error(-2002, "DEVICE_NO_ACCESS");
        }

        if (shouldPushForLowBattery(awakeDO)) {
            pushService.pushMessageForLowBatteryEvent(serialNumber);
        }

        // 设备还没有升级到最新版本，需要进一步升级
        // 不向摄像头回复response，如果摄像头由于种种原因没有收到OTA指令，还可以通过重发的awake消息来更新
        if (storedDevice.getOtaOnAwake()) {
            LOGGER.info("Device {} should OTA to a further version.", serialNumber);
            FirmwareViewDO otaTargetFirmware = firmwareService.buildFirmwareView(serialNumber, "");
            firmwareService.startOtaFromApp(serialNumber, otaTargetFirmware, false);
        }

        deviceStatusService.updateLastActBySerialNumber(serialNumber);

        int adminId = userRoleService.getDeviceAdminUser(serialNumber);
        if (!userVipService.isNoUserTier(adminId) || vipService.isVipDevice(adminId, serialNumber)) {
            StreamServerDO streamServerDO = wowzaService.getRecWowzaServerWithLeastLoad(serialNumber);
            wakeupResponseValue.setSize(streamServerDO.getSize());
            wakeupResponseValue.setDuration(streamServerDO.getDuration());
            wakeupResponseValue.setProtocol(streamServerDO.getProtocol());
            wakeupResponseValue.setHost(streamServerDO.getIp());
            wakeupResponseValue.setApp(streamServerDO.getAppName());
        }

        // traceId 必须具有唯一性
        String traceId = traceIdHelper.genTraceIdForRecordVideoV2(serialNumber, ERecordingTrigger.PIR);
        wakeupResponseValue.setTraceId(traceId);

        wakeupResponseValue.setDnsServers(DNS_SERVERS);
        // 新增timezone offset信息为了让设备自动更新夏令时状态

        DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(serialNumber);
        if (deviceSettingsDO != null) {
            this.initCxDeviceSetting(deviceSettingsDO);
            String timezone = deviceSettingsDO.getTimeZone();
            wakeupResponseValue.setStandardOffset(DateUtils.getStandardOffset(timezone));
            wakeupResponseValue.setDstOffset(DateUtils.getOffset(timezone));
        }
        // 耗电统计参数
        wakeupResponseValue.setPowerStatConfig(powerStatConfig);

        response.setValue(wakeupResponseValue);

        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);

        // 设备唤醒后根据唤醒原因分类操作
        this.deviceAwakeReport(awakeDO, traceId, modelNo);

        boolean res = VernemqPublisher.wakeupResponse(serialNumber, response);

        return Result.OperationResult(res);
    }


    private void initCxDeviceSetting(DeviceSettingsDO deviceSettingsDO){
        if(StringUtils.hasLength(deviceSettingsDO.getTimeZone())){
            return;
        }
        String associationSn = userRoleService.getBxSnByCx(deviceSettingsDO.getSerialNumber());
        DeviceSettingsDO associationDeviceSetting = deviceSettingService.getDeviceSettingsBySerialNumber(associationSn);
        if(associationDeviceSetting == null){
            LOGGER.info("设备上报awake {} bx timezone 为空",deviceSettingsDO.getSerialNumber());
            return;
        }
        deviceSettingsDO.setTimeZone(associationDeviceSetting.getTimeZone());
        deviceSettingService.updateDeviceSetting(deviceSettingsDO);

        try {
            deviceSettingService.resetVernemqSettingMessage(deviceSettingsDO.getSerialNumber(), deviceSettingsDO);
        }catch (Exception e){
            LOGGER.error("重新下发cx setting 错误");
        }
    }

    public PbSyncTimeZoneOffsetResponse syncTimeZoneOffsetByProto(String sn, PbSyncTimeZoneOffset req) {
        DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
        final String timezone = Optional.ofNullable(deviceSettingsDO).map(DeviceSettingsDO::getTimeZone).orElse("");

        PbSyncTimeZoneOffsetResponse.PbData.PbValue.Builder valueBuilder = PbSyncTimeZoneOffsetResponse.PbData.PbValue.newBuilder()
                .setStandardOffset(DateUtils.getStandardOffset(timezone))
                .setDstOffset(DateUtils.getOffset(timezone));
        if (req.hasValue() && req.getValue().hasTriggerDeviceMsgId()) {
            valueBuilder.setTriggerDeviceMsgId(req.getValue().getTriggerDeviceMsgId());
        }
        return PbSyncTimeZoneOffsetResponse.newBuilder().setResult(0).setMsg("Success")
                .setData(PbSyncTimeZoneOffsetResponse.PbData.newBuilder()
                        .setCode(0).setTime(PhosUtils.getUTCStamp()).setName(req.getName()).setId(req.getId())
                        .setValue(valueBuilder.build())
                        .build())
                .build();
    }

    /**
     * @param awakeDO
     * @param traceId
     */
    public void deviceAwakeReport(DeviceRequestDO awakeDO, String traceId, String modelNo) {
        boolean needComputeDtiM = !CollectionUtils.isEmpty(awakeDO.getReason().getWifiWakeInfo());
        if (needComputeDtiM) {
            //使用新计算逻辑
            this.deviceComputeDtimByWifiWakeInfo(awakeDO, modelNo);
        }

        Integer por = awakeDO.getReason().getPor();
        String serialNumber = awakeDO.getSerialNumber();

        PorEnums porEnums = PorEnums.queryEnums(por);
        if (porEnums != null) {
            switch (porEnums) {
                case PIR:
                    this.deviceAwakePir(serialNumber, traceId);
                    break;
                case TCP_UDP_KEEP_ALIVE:
                case WIFI_KEEP_ALIVE_DISCONNECTION:
                    if (!needComputeDtiM) {
                        this.deviceAwakeTcpOrUdpKeepalive(serialNumber, modelNo);
                    }
                    break;
                default:
                    break;
            }
        }

    }


    private void deviceAwakeTcpOrUdpKeepalive(String serialNumber, String modelNo) {
        DeviceDTIMFatigue deviceDTIMFatigue;
        Object storedDeviceDTIMFatigue = redisService.getHashFieldValue(DeviceReportKeyConstants.DEVICE_AWAKE_109_KEY, serialNumber);

        Gson gson = new Gson();
        if (storedDeviceDTIMFatigue == null) {
            deviceDTIMFatigue = DeviceDTIMFatigue.builder()
                    .timestamp(0)
                    .value(0.0)
                    .build();
        } else {
            deviceDTIMFatigue = gson.fromJson(storedDeviceDTIMFatigue.toString(), DeviceDTIMFatigue.class);
        }
        deviceDTIMFatigue.setModelNo(modelNo);
        long currentTime = Instant.now().getEpochSecond();
        if (deviceDTIMFatigue.getTimestamp() == 0) {
            deviceDTIMFatigue.setValue(1.0);
        } else {
            deviceDTIMFatigue.setValue(calculateDTIMFatigue(deviceDTIMFatigue) + BASE_HALF_LIFE_VALUE);
        }
        deviceDTIMFatigue.setTimestamp(currentTime);
        String deviceDtimStr = gson.toJson(deviceDTIMFatigue);
        redisService.setHashFieldValue(DeviceReportKeyConstants.DEVICE_AWAKE_109_KEY, serialNumber, deviceDtimStr);
        LOGGER.info("deviceAwakeTcpOrUdpKeepalive {} 新值:{}", serialNumber, deviceDtimStr);
    }

    /**
     * 计算dtim
     *
     * @param awakeDo
     * @param modelNo
     */
    public Double deviceComputeDtimByWifiWakeInfo(DeviceRequestDO awakeDo, String modelNo) {
        if (CollectionUtils.isEmpty(awakeDo.getReason().getWifiWakeInfo())) {
            // 未上报wifiWakeInfo (老固件)
            return 0D;
        }

        int cnt = 0;
        for (DeviceRequestDO.WifiWakeInfo wifiWakeInfo : awakeDo.getReason().getWifiWakeInfo()) {
            cnt += wifiWakeInfo.getCnt();
        }

        if (cnt < 1) {
            // wifi错误次数为0
            return 0D;
        }
        Gson gson = new Gson();
        //上一次存储的记录
        Object storedDeviceDTIMFatigue = redisService.getHashFieldValue(DeviceReportKeyConstants.DEVICE_AWAKE_109_KEY, awakeDo.getSerialNumber());

        DeviceDTIMFatigue deviceDTIMFatigue = storedDeviceDTIMFatigue == null ? DeviceDTIMFatigue.builder().timestamp(0).modelNo(modelNo).value(Double.valueOf(cnt)).build()
                : gson.fromJson(storedDeviceDTIMFatigue.toString(), DeviceDTIMFatigue.class);

        if (deviceDTIMFatigue.getTimestamp() > 0) {
            deviceDTIMFatigue.setValue(calculateDTIMFatigue(deviceDTIMFatigue) + cnt);
        }
        deviceDTIMFatigue.setTimestamp(Instant.now().getEpochSecond());

        String deviceDtimStr = JSON.toJSONString(deviceDTIMFatigue);
        redisService.setHashFieldValue(DeviceReportKeyConstants.DEVICE_AWAKE_109_KEY, awakeDo.getSerialNumber(), deviceDtimStr);
        LOGGER.info("deviceComputeDtimByWifiWakeInfo {} 新值:{}", awakeDo.getSerialNumber(), deviceDtimStr);
        return deviceDTIMFatigue.getValue();
    }

    /**
     * 设备pir唤醒后操作
     *
     * @param serialNumber
     * @param traceId
     */
    private void deviceAwakePir(String serialNumber, String traceId) {
        LOGGER.debug("Send awake response to device with serial number: {} awoke by PIR with trace id {}",
                serialNumber,
                traceId);

        //设备唤醒时同步设备配置信息
        try {
            deviceSettingService.syncDeviceSetting(serialNumber);
        } catch (Exception e) {
            LOGGER.info("wakeup serialNumber device setting error: {}", serialNumber, e);
        }
    }

    /**
     * 处理设备上报的事件信息
     *
     * @param eventDO
     * @return
     * @throws
     * @throws IdNotSetException
     */
    public Result handleEventReport(DeviceReportEventDO eventDO) {
        EReportEvent reportEvent = EReportEvent.findByEventId(eventDO.getValue().getEvent());
        if (reportEvent == null) return Result.Success(); // 遇到不认识的event，直接return，避免空指针异常
        switch (reportEvent) {
            case PIR:
                return respondStreamServerParams(eventDO, ERecordingTrigger.PIR);
            case LOW_BATTERY:
                return respondLowBatteryEvent(eventDO);
            case BIND_STATUS:
                return respondBindStatusEvent(eventDO);
            case POWER_PLUG_IN:
                break;
            case POWER_OFF:
                break;
            case LIVE_BEGIN:
                //直播开始
                break;
            case LIVE_END:
                // 直播结束
                break;
            case INFRARED_LIGHT_ON:
                //红外开始
                break;
            case INFRARED_LIGHT_OFF:
                // 红外灯结束
                break;
            case WHITE_LIGHT_ON:
                //白光灯开始
                break;
            case WHITE_LIGHT_OFF:
                //白光灯结束
                break;
            case BIDIRECTIONAL_SPEECH_ON:
                //双向语音开始
                break;
            case BIDIRECTIONAL_SPEECH_OFF:
                //双向语音结束
                break;
            case CRY: // 设备上报哭声检测
                return respondStreamServerParams(eventDO, ERecordingTrigger.CRY);
            case DETECT_RESULT: // 设备识别
                return respondDetectResult(eventDO);
            case DEVICE_CALL:
//                videoGenerateService.sendVideoMsg(VideoMsgType.REPORT_EVENT, eventDO.getValue().getTraceId(), eventDO);
                videoGenerateService.handleReportEvent(eventDO);
                break;
            case DOORBELL_PRESS:
                // 按压门铃
                // 优先返回，避免设备端阻塞
                // 发送alexa事件
                eventDO.setAttachment(new JSONObject().fluentPut(S3_VIDEO_SLICE_INFO_NOTIFY_ALEXA, true));
//                videoGenerateService.sendVideoMsg(VideoMsgType.REPORT_EVENT, eventDO.getValue().getTraceId(), eventDO);
                videoGenerateService.handleReportEvent(eventDO);
                break;
            case DOORBELL_REMOVE:
                // 拆除门铃
                // 优先返回，避免设备端阻塞
//                videoGenerateService.sendVideoMsg(VideoMsgType.REPORT_EVENT, eventDO.getValue().getTraceId(), eventDO);
                videoGenerateService.handleReportEvent(eventDO);
                break;
            case POWER_STATISTICS:
                // 耗电量统计
                // 优先返回，避免设备端阻塞
                kissService.sendReportEventResponse(eventDO.getSerialNumber(), JsonUtil.toJson(MqttResponsePackage.of("reportEvent", new HashMap<>(), eventDO.getId())));
                break;
            case ROTATE_CALIBRATION:
                boolean calibrationFinished = BooleanUtils.isTrue(eventDO.getValue().getCalibrationFinished());
                redisService.set(RotateService.getRotateCalibrationKey(eventDO.getSerialNumber()), String.valueOf(calibrationFinished), 10);
                break;
            case AI_EDGE_EVENT:
                vernemqPublisher.reportEventResponse(eventDO.getSerialNumber(), new HashMap<>(), eventDO.getId());
//                videoGenerateService.sendVideoMsg(VideoMsgType.REPORT_EVENT, eventDO.getValue().getTraceId(), eventDO);
                videoGenerateService.handleReportEvent(eventDO);
                break;
            default:
//                throw new IllegalArgumentException("Illegal event!");
        }
        return Result.Success();
    }

    /**
     * 更新今夜更新开关和开始时间
     *
     * @param serialNumber
     */
    public void updateCanUpdateTonight(String serialNumber, Integer canUpdateTonight) {
        LOGGER.info("今夜更新开关状态变更:serialNumber:【{}】,canUpdateTonight:【{}】", serialNumber, canUpdateTonight);
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setSerialNumber(serialNumber);
        deviceDO.setUpdateTonight(canUpdateTonight);
        deviceDO.setUpdateTonightStarttime((int) (System.currentTimeMillis()/1000));
        deviceService.updateDeviceInfo(deviceDO);
    }


    public void updatePersonDetect(String serialNumber, Integer personDetect) {
        LOGGER.info("人型检测状态变更:serialNumber:【{}】,personDetect:【{}】", serialNumber, personDetect);
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setSerialNumber(serialNumber);
        deviceDO.setPersonDetect(personDetect);
        deviceService.updateDeviceInfo(deviceDO);
    }

    private boolean shouldPushForLowBattery(DeviceRequestDO awakeDO) {
        if (awakeDO.getReason() == null || awakeDO.getReason().getBatteryEvent() == null) {
            return false;
        }
        return awakeDO.getReason().getBatteryEvent() == 1;
    }

    /**
     * 查询指定位置绑定的设备列表
     *
     * @return
     */
    public List<DeviceDO> queryDeviceByLocationId(int userId, int locationId) {
        List<DeviceDO> deviceDOList = deviceDAO.queryDeviceByLocation(locationId);
        if (!CollectionUtils.isEmpty(deviceDOList)) {
            deviceDOList = listDevicesByUserId(userId, deviceDOList, UserRoleEnums.ADMIN.getCode());
        }
        return deviceDOList;
    }


    private void processSettingNullValue(DeviceSettingsDO deviceSettingsDO) {
        if (deviceSettingsDO.getNightVisionSensitivity() != null && deviceSettingsDO.getNightVisionSensitivity() < 0) {
            deviceSettingsDO.setNightVisionSensitivity(NightVisionSensitivityEnums.LOW.getCode());
        }
        if (deviceSettingsDO.getNightThresholdLevel() != null && deviceSettingsDO.getNightThresholdLevel() < 0) {
            deviceSettingsDO.setNightThresholdLevel(NightThresholdLevelEnums.LOW.getCode());
        }
        if (deviceSettingsDO.getMotionSensitivity() != null && deviceSettingsDO.getMotionSensitivity() < 0) {
            throw new BaseException(ResultCollection.INVALID_PARAMS, "运动检测灵敏度参数不正确");
        }
        if (deviceSettingsDO.getAlarmDuration() != null && deviceSettingsDO.getAlarmDuration() < 0) {
            throw new BaseException(ResultCollection.INVALID_PARAMS, "警报时长参数不正确");
        }
        if (deviceSettingsDO.getIrThreshold() == null && deviceSettingsDO.getNightThresholdLevel() != null) {
            NightThresholdLevelEnums level = NightThresholdLevelEnums.queryByCode(deviceSettingsDO.getNightThresholdLevel());
            switch (level) {
                case LOW:
                    deviceSettingsDO.setIrThreshold(NightVisionSensitivityEnums.LOW.getCode());
                    break;
                case MIDDLE:
                    deviceSettingsDO.setIrThreshold(NightVisionSensitivityEnums.MIDDLE.getCode());
                    break;
                case HIGHT:
                    deviceSettingsDO.setIrThreshold(NightVisionSensitivityEnums.HIGHT.getCode());
                    break;
                default:
                    deviceSettingsDO.setIrThreshold(NightVisionSensitivityEnums.CLOSE.getCode());
                    break;
            }
        }

        //新版本APP-设置夜视灵敏度
        if (deviceSettingsDO.getNightThresholdLevel() != null && deviceSettingsDO.getNightVisionSensitivity() == null) {
            NightThresholdLevelEnums nightVisionLevel = NightThresholdLevelEnums.queryByCode(deviceSettingsDO.getNightThresholdLevel());
            switch (nightVisionLevel) {
                case LOW:
                    deviceSettingsDO.setNightVisionSensitivity(NightVisionSensitivityEnums.LOW.getCode());
                    break;
                case MIDDLE:
                    deviceSettingsDO.setNightVisionSensitivity(NightVisionSensitivityEnums.MIDDLE.getCode());
                    break;
                case HIGHT:
                    deviceSettingsDO.setNightVisionSensitivity(NightVisionSensitivityEnums.HIGHT.getCode());
                    break;
                default:
                    deviceSettingsDO.setNightVisionSensitivity(NightVisionSensitivityEnums.CLOSE.getCode());
                    break;
            }
        }

        //设备上报的时候-警报
        if (deviceSettingsDO.getPirSirenDuration() != null
                && deviceSettingsDO.getNeedAlarm() == null) {
            deviceSettingsDO.setNeedAlarm(deviceSettingsDO.getPirSirenDuration() > 0 ? 1 : 0);

            //上报的警报为关闭时，只更新警报开关，不更新警报时长
            if (deviceSettingsDO.getPirSirenDuration().intValue() == 0) {
                deviceSettingsDO.setPirSirenDuration(null);
            }
        }
        //设备上报的时候-视频
        if (deviceSettingsDO.getRecLen() != null
                && deviceSettingsDO.getNeedVideo() == null) {
            deviceSettingsDO.setNeedVideo(deviceSettingsDO.getRecLen().intValue() != 0 ? 1 : 0);

            //上报的录制视频为关闭时，只更录制视频开关开关，不更新视频时长
            if (deviceSettingsDO.getRecLen().intValue() == 0) {
                deviceSettingsDO.setRecLen(null);
            }
        }
        //设备上报的时候-夜视
        if (deviceSettingsDO.getIrThreshold() != null && (deviceSettingsDO.getNightVisionSensitivity() == null || deviceSettingsDO.getNightThresholdLevel() == null)) {
            NightVisionSensitivityEnums irThreshold = NightVisionSensitivityEnums.queryByCode(deviceSettingsDO.getIrThreshold());
            switch (irThreshold) {
                case LOW:
                    deviceSettingsDO.setNightVisionSensitivity(NightThresholdLevelEnums.LOW.getCode());
                    deviceSettingsDO.setNightThresholdLevel(deviceSettingsDO.getIrThreshold());
                    break;
                case MIDDLE:
                    deviceSettingsDO.setNightVisionSensitivity(NightThresholdLevelEnums.MIDDLE.getCode());
                    deviceSettingsDO.setNightThresholdLevel(deviceSettingsDO.getIrThreshold());
                    break;
                case HIGHT:
                    deviceSettingsDO.setNightVisionSensitivity(NightThresholdLevelEnums.HIGHT.getCode());
                    deviceSettingsDO.setNightThresholdLevel(deviceSettingsDO.getIrThreshold());
                    break;
                default:
                    break;
            }
        }
        //设备上报的时候-pir
        if (deviceSettingsDO.getPir() != null && deviceSettingsDO.getMotionSensitivity() == null) {
            deviceSettingsDO.setMotionSensitivity(deviceSettingsDO.getPir() > 0 ? deviceSettingsDO.getPir() : null);
        }
    }

    public String generateSwitchWifiOperation(DeviceRequest request, int userId) {
        String switchWifiOperationId = IDUtil.uuid().substring(0, 8);

        SwitchWifiOperation switchWifiOperation = SwitchWifiOperation.builder()
                .serialNumber(request.getSerialNumber())
                .operationId(switchWifiOperationId)
                .userId(userId)
                .build();

        switchWifiOperationDAO.insertSwitchWifiOperation(switchWifiOperation);

        return switchWifiOperationId;
    }

    /**
     * 设备包裹检测开关
     *
     * @param serialNumber
     * @return
     */
//    public void updateDevicePackagePush(Integer userId, String serialNumber, Integer packagePush) {
//
//        //开启包裹检测，需要验证用户当前是否购买套餐
//        boolean isVip = userVipService.isVipUser(userId);
//        if (!isVip) {
//            throw new BaseException(DEVICE_NO_ACCESS, DEVICE_NO_ACCESS.getResult().getMsg());
//        }
//
//        deviceDAO.updateDevicePackagePush(serialNumber, DevicePackagePushEnums.PACKAGEPUSHON.getCode().equals(packagePush) ? DevicePackagePushEnums.PACKAGEPUSHON.getCode() :
//                DevicePackagePushEnums.PACKAGEPUSHOFF.getCode());
//    }

    /**
     * 关闭用户包裹检测
     *
     * @param serialNumber
     */
//    public void closePackagePush(String serialNumber) {
//        deviceDAO.updateDevicePackagePush(serialNumber, DevicePackagePushEnums.PACKAGEPUSHOFF.getCode());
//    }

    /**
     * 更新用户配置
     *
     * @param serialNumber
     * @param userId
     * @param startTime
     */
    public void sysReportUpdateUserConfigStart(String serialNumber, Integer userId, long startTime) {
        reportLogService.sysReportUpdateUserConfig(REPORT_TYPE_UPDATE_USER_CONFIG_START, MapUtil.builder()
                .put("serialNumber", serialNumber)
                .put("userId", userId)
                .put("startTime", startTime)
                .put("modelNo", deviceManualService.getModelNoBySerialNumber(serialNumber))
                .build());
    }

    /**
     * 更新用户配置结果
     *
     * @param reportInfo
     * @param updateStatus
     */
    public void sysReportUpdateUserConfigEnd(ReportInfo reportInfo,
                                             boolean updateStatus, String reason) {
        long time = System.currentTimeMillis();
        final String modelNo = deviceManualService.getModelNoBySerialNumber(reportInfo.getSerialNumber());
        reportLogService.sysReportUpdateUserConfig(REPORT_TYPE_UPDATE_USER_CONFIG_END, MapUtil.builder()
                .put("serialNumber", reportInfo.getSerialNumber())
                .put("userId", reportInfo.getUserId())
                .put("endTime", time)
                .put("waitTime", time - reportInfo.getStartTime())
                .put("updateStatus", updateStatus)
                .put("reason", reason)
                .put("modelNo", modelNo)
                .build());
    }

    public void bindDeviceLocation(DeviceLocationRequest request, Integer userId) {
        if (request.getLocationId() == -1 && StringUtils.isEmpty(request.getLocationName())) {
            LOGGER.info("bindDeviceLocation 参数异常,serialNumber:{},locationId:{},locationName:{}", request.getSerialNumber(), request.getLocationId(), request.getLocationName());
            throw new BaseException(ResultCollection.INVALID_PARAMS, "INVALID_PARAMS");
        }
        DeviceDO deviceDO = DeviceDO.builder()
                .serialNumber(request.getSerialNumber())
                .deviceName(request.getDeviceName())
                .build();
        LOGGER.info("update device name success,serialNumber:{}", request.getSerialNumber());
        if (request.getLocationId() == -1 && !StringUtils.isEmpty(request.getLocationName())) {
            LocationDO location = new LocationDO();
            location.setAdminId(userId);
            location.setCity(request.getCity());
            location.setCountry(request.getCountry());
            location.setDistrict(request.getDistrict());
            location.setPostalCode(request.getPostalCode());
            location.setInsertTime(PhosUtils.getUTCStamp());
            location.setStreetAddress1(request.getStreetAddress1());
            location.setStreetAddress2(request.getStreetAddress2());
            location.setState(request.getState());
            location.setLocationName(request.getLocationName());
            location = locationInfoService.insertLocationReturnId(location);
            deviceDO.setLocationId(location.getId());
        } else {
            deviceDO.setLocationId(request.getLocationId());
        }

        deviceService.updateDeviceInfo(deviceDO);
        LOGGER.info("update device location success,serialNumber:{},locationId:{}", request.getSerialNumber(), deviceDO.getLocationId());
    }

    public DeviceModel processDeviceModelForWebrtc(String serialNumber, LocalDeviceSupport localDeviceSupport) {

        DeviceModel model = new DeviceModel();
        DeviceModel modelConfig = deviceModelConfigService.queryDeviceModelConfig(serialNumber);
        if (modelConfig == null) {
            // pm 还没发布配置
            return model;
        }
        BeanUtils.copyProperties(modelConfig, model);
        if (localDeviceSupport != null && Objects.equals(localDeviceSupport.getSupportWebrtc(), 1)) {
            model.setStreamProtocol("webrtc");
            Optional.ofNullable(localDeviceSupport.getSupportWhiteLight()).ifPresent(model::setWhiteLight);
        }
        return model;
    }

    private Result respondLowBatteryEvent(DeviceReportEventDO eventDO) {
        String serialNumber = eventDO.getSerialNumber();
        pushService.pushMessageForLowBatteryEvent(serialNumber);
        // openApiWebhookService.callWebhookForDeviceLowBattery(serialNumber);
        kissService.sendReportEventResponse(serialNumber, JsonUtil.toJson(MqttResponsePackage.of("reportEvent", new HashMap<>(), eventDO.getId())));
        // 通知alexa设备电量低
        alexaSafemoRequestService.reportDeviceBatteryLow(serialNumber, 10);
        return Result.OperationResult(true);
    }

    private Result respondStreamServerParams(DeviceReportEventDO eventDO, ERecordingTrigger trigger) {
        long pirStartTimestamp = System.currentTimeMillis();
        String serialNumber = eventDO.getSerialNumber();
        // 设备端生成的traceId就用设备端的，否则由服务端生成
        String traceId = Optional.ofNullable(eventDO.getValue().getTraceId())
                .orElseGet(() -> traceIdHelper.genTraceIdForRecordVideoV2(serialNumber, trigger));
        String motionType = Optional.ofNullable(eventDO.getValue().getMotionType())
                .orElse(OpenApiDeviceConfig.MotionType.video.name());
        VideoUploadType videoUploadType = VideoUploadType.codeOf(eventDO.getValue().getVideoUploadType());
        final Map<String, Object> pirEventResponse;
        if (videoUploadType == VideoUploadType.WOWZA) {
            StreamServerDO streamServerDO = wowzaService.getRecWowzaServerWithLeastLoad(serialNumber);
            pirEventResponse = (JSONObject) JSON.toJSON(streamServerDO);
        } else if (videoUploadType == VideoUploadType.S3) {
            UploadVideoBeginRequest uploadVideoBeginRequest = UploadVideoBeginRequest.builder().serialNumber(eventDO.getSerialNumber())
                    .traceId(traceId).mqttEventTime(eventDO.getTime()).motionType(motionType).build();
            Result<JSONObject> result = videoService.videoUploadBegin(uploadVideoBeginRequest);
            if (result.getResult() != Result.successFlag) return result;
            pirEventResponse = result.getData();
        } else {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "respondStreamServerParams 参数错误:{}", JSON.toJSONString(eventDO));
            return ResultCollection.INVALID_PARAMS.getResult();
        }
        pirEventResponse.put("traceId", traceId);

        // 如果是哭声, 一会儿还要通知
        if (trigger == ERecordingTrigger.CRY) {
            deviceDetectHelper.add(serialNumber, traceId, AiObjectEnum.CRY.getObjectName(), AiObjectActionEnum.CRY.getEventTypeName());
        }
        // 先返回pir上传视频参数,再做后续操作,减小耗时
        Result result = Result.OperationResult(vernemqPublisher.reportEventResponse(serialNumber, pirEventResponse, eventDO.getId()));
        // 在mqtt响应发送后统计pir触发时间
        long pirEndTimestamp = System.currentTimeMillis();
        Boolean saveSuccess = null;
        if (videoUploadType == VideoUploadType.S3) {
            eventDO.setAttachment(new JSONObject()
//                        .fluentPut(S3_VIDEO_SLICE_INFO_MQTT_EVENT_TIME, eventDO.getTime())
                    .fluentPut(S3_VIDEO_SLICE_INFO_MQTT_EVENT_TIME, System.currentTimeMillis()) // 以后端时间为准
                    .fluentPut(S3_VIDEO_SLICE_INFO_PIR_START_TIME, pirStartTimestamp)
                    .fluentPut(S3_VIDEO_SLICE_INFO_PIR_END_TIME, pirEndTimestamp));
//            videoGenerateService.sendVideoMsg(VideoMsgType.REPORT_EVENT, traceId, eventDO);
            videoGenerateService.handleReportEvent(eventDO);
            saveSuccess = true;
        }
        reportLogService.sysReportEvent(REPORT_TYPE_DEVICE_REPORT_EVENT,
                MapUtil.builder()
                        .put("event", eventDO.value.getEvent())
                        .put("serialNumber", serialNumber)
                        .put("time", eventDO.getTime())
                        .put("traceId", traceId)
                        .put("modelNo", deviceManualService.getModelNoBySerialNumber(serialNumber))
                        .put(VideoUploadType.KEY_NAME, videoUploadType.getCode())
                        .put("pirStartTimestamp", pirStartTimestamp)
                        .put("pirEndTimestamp", pirEndTimestamp)
                        .put("time2PirStart", DateUtils.diffMillis(eventDO.getTime(), pirStartTimestamp))
                        .put("pirStart2PirEnd", DateUtils.diffMillis(pirStartTimestamp, pirEndTimestamp))
                        .put("saveSuccess", saveSuccess)
                        .build());

        // 刷新s3存储天数设置
        deviceConfigService.refreshS3VideoStorageDaysByDay(serialNumber);

        // 通知alexa发生pir
        alexaSafemoRequestService.reportDeviceMotionDetected(serialNumber);

        //基站告警
        triggerHubWarn(serialNumber, "pir");

        return result;
    }


    private void triggerHubWarn(String serialNumber, String method) {
        CmdInfo cmdInfo = CmdInfo.newBuilder()
                .setName("action")
                .setValue("pirNotice")
                .build();
        CmdRequest cmdRequest = CmdRequest.newBuilder()
                .addCmdInfos(cmdInfo)
                .setSerialNumber(serialNumber)
                .build();
        try {
            SendResult result = bstationdSettingProcessorBlockingStub.handleCmd(cmdRequest);
            LOGGER.info("{} ,bstationd handleCmd , {} ,{}", method, cmdRequest, result);
            if (result.getCode() != ResultCollection.SUCCESS.getCode()) {
                LOGGER.error("{}}, bstationd handleCmd  failed", method);
            }
        }catch (Exception e){
            LOGGER.error("{}}, bstationdSettingProcessorBlockingStub error, ", method, e);
        }
    }

    private Result respondDetectResult(DeviceReportEventDO eventDO) {
        JSONObject data = JSON.parseObject(eventDO.value.data);
        String traceId = data.getString("traceId");
        String eventObject = data.getString("eventObject");

        // 哭声上报结果
        if (Objects.requireNonNull(EnumFindUtil.findByName(eventObject, AiObjectEnum.class)) == AiObjectEnum.CRY) {
            deviceDetectHelper.add(eventDO.getSerialNumber(), traceId, eventObject, AiObjectActionEnum.CRY.getEventTypeName());
        }

        return Result.Success();
    }

    private Result respondBindStatusEvent(DeviceReportEventDO eventDO) {
        String serialNumber = eventDO.getSerialNumber();
        UserRoleDO userRoleDO = userRoleService.getDeviceAdminUserRole(serialNumber);
        Map<String, Object> value = MapUtil.builder().put("bindStatus", userRoleDO == null ? 0 : 1).build();

        // 新增timezone offset信息为了让设备自动更新夏令时状态
        DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(serialNumber);
        if (deviceSettingsDO != null) {
            String timezone = deviceSettingsDO.getTimeZone();
            //标准时间
            value.put("standardOffset", DateUtils.getStandardOffset(timezone));
            //夏令时
            value.put("dstOffset", DateUtils.getOffset(timezone));
        }

        return Result.OperationResult(vernemqPublisher.reportEventResponse(serialNumber, value, eventDO.getId()));
    }

    /**
     * 将设备信息恢复到未绑定状态
     *
     * @param serialNumber
     */
    public int deactivateDevice(String serialNumber) {
        DeviceDO deviceDO = DeviceDO.builder()
                .serialNumber(serialNumber)
                .locationId(0)
                .activated(0)
                .activatedTime(0)
                .build();
        return deviceService.updateDeviceInfo(deviceDO);
    }


    /**
     * 返回设备绑定后初始化信息
     *
     * @param serialNumber
     * @return
     */
    public DeviceBind deviceBindInit(String serialNumber, Integer userId) {
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        if (deviceDO == null) {
            LOGGER.info("此序列号设备不存在");
            throw new BaseException(DEVICE_NO_ACCESS, "DEVICE_NO_ACCESS");
        }

        this.initDeviceFirmwareAfterBind(deviceDO);

        List<LocationDO> locationDOList = locationInfoService.listUserLocations(userId);

        DeviceBind deviceBind = DeviceBind.builder()
                .serialNumber(serialNumber)
                .deviceName(deviceDO.getDeviceName())
                .firmwareStatus(deviceDO.getFirmwareStatus())
                .newestFirmwareId(deviceDO.getNewestFirmwareId())
                .locationDOList(locationDOList)
                .locationId(deviceDO.getLocationId() == null ? 0 : deviceDO.getLocationId())
                .build();

        return deviceBind;
    }

    /**
     * 初始化设备绑定后固件升级信息
     *
     * @param deviceDO
     */
    private void initDeviceFirmwareAfterBind(DeviceDO deviceDO) {
        String modelNo = deviceManualService.getModelNoBySerialNumber(deviceDO.getSerialNumber());
        if (deviceBindFirmwareConfig.deviceNeedFilterOta(modelNo, deviceDO.getFirmwareId())) {
            LOGGER.info("设备{}生产型号{}固件版本号{}绑定后不需要升级", deviceDO.getSerialNumber(), modelNo, deviceDO.getFirmwareId());
            deviceDO.setFirmwareStatus(0);
            return;
        }

        DeviceOTADO deviceOTADO = deviceOTAService.queryDeviceOtaBySerialNumber(deviceDO.getSerialNumber());
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView(deviceDO, deviceOTADO);
        if (firmwareViewDO != null) {
            deviceDO.setFirmwareStatus(firmwareViewDO.getFirmwareStatus());
            deviceDO.setNewestFirmwareId(firmwareViewDO.getTargetFirmware());
        }
    }

    /**
     * 获取当前用户下设备推送image
     *
     * @param userId
     * @return
     */
    public List<DevicePushImage> queryUserDevicePushImage(Integer userId) {
        List<DevicePushImage> devicePushImageList = Lists.newArrayList();
        List<UserRoleDO> serialNumUserRoleList = userRoleService.getUserRoleByUserId(userId);

        for (UserRoleDO userRoleDO : serialNumUserRoleList) {
            DevicePushImage devicePushImage = this.initDevicePushImage(userRoleDO.getSerialNumber(), userRoleDO.getAdminId());
            if (devicePushImage == null) {
                continue;
            }
            devicePushImageList.add(devicePushImage);
        }
        return devicePushImageList;
    }

    public DevicePushImage initDevicePushImage(String serialNumber,Integer adminId){
        final DevicePushImage pushImage = videoCacheService.queryDevicePushImage(VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW, serialNumber, adminId);
        if (pushImage == null) {
            LOGGER.info("当前设备无message推送记录,serialNumber:{},adminId:{}", serialNumber, adminId);
            return null;
        }
        // 重新计算image url(有过期时间)
        pushImage.setLastPushImageUrl(s3Service.preSignUrl(pushImage.getLastPushImageUrl()));
        pushImage.setSerialNumber(serialNumber);
        final DevicePushImage subImage = videoCacheService.queryDevicePushImage(VideoTypeEnum.EVNET_RECORDING_SUB_VIEW, serialNumber, adminId);
        if (subImage != null) {
            subImage.setLastPushImageUrl(s3Service.preSignUrl(subImage.getLastPushImageUrl()));
            subImage.setSerialNumber(serialNumber);
            pushImage.setSubVideoImage(subImage);
        }
        return pushImage;
    }

    /**
     * 唤醒设备
     *
     * @param serialNumber
     * @param traceId
     */
    public void wakeUpDevice(String serialNumber, String traceId) {
        // 唤醒设备
        kissWsService.wakeup(serialNumber, traceId, DeviceSleepHelper.genWakeup(serialNumber));
    }

    /**
     * 设备是否已经唤醒
     *
     * @param serialNumber
     * @return
     */
    public boolean deviceHasWakeUp(String serialNumber, boolean supportUnlimitedWebsocket) {
        LOGGER.debug("设备deviceHasWakeUp {} begin! supportUnlimitedWebsocket={}", serialNumber, supportUnlimitedWebsocket);
        /* 先注释掉，设备上电了不代表连上mqtt了
        if (supportUnlimitedWebsocket) { // 判断设备是否唤醒时，如果是支持wss长链接的设备，则查询kiss中设备的状态
            final IKissService.DeviceStatus deviceStatus = kissService.getDeviceStatus(serialNumber);
            if (IKissService.DeviceStatus.normal.equals(deviceStatus)) return true;
            else if (IKissService.DeviceStatus.dormant.equals(deviceStatus)) return false;
        }
        */
        return deviceStatusService.deviceConnectionStatus(serialNumber);
    }

    /**
     * 等待设备唤醒
     *
     * @param serialNumber
     * @return
     */
    public boolean waitDeviceWakeUp(String serialNumber) {
        LOGGER.info("waitDeviceWakeUp for {}", serialNumber);
        boolean supportKillKeepAlive = this.deviceKillKeepAliveSupport(serialNumber);
        if (!supportKillKeepAlive) {
            LOGGER.info("设备 {} 不支持killKeepAlive");
            // 老固件不支持killKeepAlive
            return true;
        }
        Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
                .retryIfResult(Predicates.equalTo(false))
                // 设备唤醒平均2-3秒，没必要100 毫秒就一次判断，太频繁
                .withWaitStrategy(WaitStrategies.fixedWait(200, TimeUnit.MILLISECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(50))
                .build();
        final LocalDeviceSupport localDeviceSupport = getDeviceSupport(serialNumber);
        try {
            retryer.call(() -> this.deviceHasWakeUp(serialNumber, localDeviceSupport.getSupportUnlimitedWebsocket()));
            LOGGER.info("waitDeviceWakeUp {} succeeded! supportUnlimitedWebsocket={}", serialNumber, localDeviceSupport.getSupportUnlimitedWebsocket());
            return true;
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "waitDeviceWakeUp {} failed! supportUnlimitedWebsocket={}", serialNumber, localDeviceSupport.getSupportUnlimitedWebsocket());
            return false;
        }
    }

    public void triggerDeviceUploadSupport(String serialNumber){
        JSONObject textData = new JSONObject().fluentPut("name", "triggerSupport")
                .fluentPut("time", System.currentTimeMillis() / 1000)
                .fluentPut("id", String.format("cmd:%s",UUID.randomUUID().toString()))
                .fluentPut("value", null);
        kissWsService.sendCmdToDevice(serialNumber, new MqttPayload(textData.toString(), null));
    }

    public DeviceApInfoDO queryDeviceApInfo(Integer userId, String serialNumber, String userSn, String apInfoVersion) {
        DeviceApInfoDO deviceApInfoDO = new DeviceApInfoDO();

        // 优先从厂测数据库获取设备信息
        DeviceManufactureTableDO deviceManufactureTableDO = factoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(serialNumber, userSn);
        if (deviceManufactureTableDO == null) {
            throw new BaseException(ResultCollection.DEVICE_NO_EXIT, "device not exist");
        }

        BeanUtils.copyProperties(deviceManufactureTableDO, deviceApInfoDO);

        // 其次再从数据库查询设备信息
        DeviceDO device = deviceService.getAllDeviceInfo(deviceManufactureTableDO.getSerialNumber());
        if (device == null) {
            device = new DeviceDO();
        }
        device.setModelNo(org.apache.commons.lang3.StringUtils.defaultString(deviceManufactureTableDO.getRegisterModelNo(), deviceManufactureTableDO.getModelNo()));
        fillDeviceInfo(device);
        deviceApInfoDO.setIcon(device.getIcon());
        deviceApInfoDO.setSmallIcon(device.getSmallIcon());

        DeviceDBApInfo deviceDBApInfo = deviceApInfoDAO.queryBySn(deviceManufactureTableDO.getSerialNumber());

        // 尝试从数据库获取
        String apInfo = null;
        if (deviceDBApInfo != null && !StringUtils.isEmpty(deviceDBApInfo.getApInfo())) {
            String deviceDBApInfoVersion = deviceDBApInfo.getApInfoVersion();
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(deviceDBApInfoVersion, apInfoVersion)) {
                apInfo = deviceDBApInfo.getApInfo();
            }
        }

        // 尝试从厂测数据获取
        if (StringUtils.isEmpty(apInfo) && !StringUtils.isEmpty(deviceManufactureTableDO.getApInfo())) {
            apInfo = deviceManufactureTableDO.getApInfo();
        }

        // 都没获取到，则手动生成
        if (StringUtils.isEmpty(apInfo) && !"01".equals(apInfoVersion)) {
            String tenantId = null;
            try {
                tenantId = userService.queryTenantIdById(userId);
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.warn(LOGGER, "queryTenantIdById failed,  userId {}", userId, e);
            }
            apInfo = DeviceApInfoGenerateUtil.generateApInfo(tenantId, deviceManufactureTableDO.getUserSn(), apInfoVersion);
        }

        deviceApInfoDO.setApInfo(apInfo);
        return deviceApInfoDO;
    }

    /**
     * 从设备同步apInfo更新数据库里的apInfo
     *
     * @param serialNumber
     * @return
     */
    public void updateDeviceApInfoFromSync(String serialNumber, String userSn, String syncApInfo) {
        if (!StringUtils.hasLength(syncApInfo)) {
            return;
        }

        DeviceDBApInfo deviceDBApInfo = new DeviceDBApInfo();
        deviceDBApInfo.setSerialNumber(serialNumber);
        deviceDBApInfo.setUserSn(userSn);
        deviceDBApInfo.setApInfoVersion(DeviceApInfoGenerateUtil.DEFAULT_AP_INFO_VERSION);
        deviceDBApInfo.setApInfo(syncApInfo);

        deviceApInfoDAO.upsert(deviceDBApInfo);
    }

    public Result<FoundDeviceInfoResult> queryFoundDeviceInfo(FoundDeviceInfoQuery input) {
        Result<FoundDeviceInfoResult> result = new Result<>(new FoundDeviceInfoResult());
        List<DeviceManufactureTableDO> deviceManus = factoryDataQueryService.queryDeviceManufactureByUserSns(input.getUserSns());
        Map<String, DeviceManufactureTableDO> userSn2Manu = deviceManus.stream().collect(Collectors.toMap(it -> it.getUserSn(), it -> it));

        Map<String, DeviceModelIconDO> modelNo2Icon = new LinkedHashMap<>();
        for (String userSn : input.getUserSns()) {
            FoundDeviceInfoResult.DeviceItem deviceItem = new FoundDeviceInfoResult.DeviceItem().setUserSn(userSn);
            result.getData().getDevices().add(deviceItem);
            DeviceManufactureTableDO manu = userSn2Manu.get(userSn);
            if (manu == null) {
                deviceItem.setQueryStatus(-1);
                continue;
            }
            String modelNo = Optional.ofNullable(manu.getRegisterModelNo()).orElse(manu.getModelNo());
            if (modelNo == null) {
                deviceItem.setQueryStatus(-2);
                continue;
            }
            DeviceModelIconDO modelIcon = modelNo2Icon.computeIfAbsent(modelNo, deviceModelIconService::queryDeviceModelIcon);
            if (modelIcon == null) {
                deviceItem.setQueryStatus(-3);
                continue;
            }
            deviceItem.setQueryStatus(0).setIcon(modelIcon.getIconUrl()).setSmallIcon(modelIcon.getSmallIconUrl());
        }
        return result;
    }

    void getDeviceInfoFromSetting(DeviceDO device) {
        DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(device.getSerialNumber());
        if (deviceSettingsDO != null) {
            device.setDefaultCodec(deviceSettingsDO.getDefaultCodec());
            if (deviceSettingsDO.getLiveAudioToggleOn() != null) {
                device.setLiveAudioToggleOn(deviceSettingsDO.getLiveAudioToggleOn());
            } else {
                // do nothing
            }
            if (deviceSettingsDO.getRecordingAudioToggleOn() != null) {
                device.setRecordingAudioToggleOn(deviceSettingsDO.getRecordingAudioToggleOn());
            } else {
                // do nothing
            }
            if (deviceSettingsDO.getLiveSpeakerVolume() != null) {
                device.setLiveSpeakerVolume(deviceSettingsDO.getLiveSpeakerVolume());
            } else {
                // do nothing
            }
            if (deviceSettingsDO.getAlarmWhenRemoveToggleOn() != null) {
                device.setAlarmWhenRemoveToggleOn(deviceSettingsDO.getAlarmWhenRemoveToggleOn());
            } else {
                // do nothing
            }

            device.setTimeZone(deviceSettingsDO.getTimeZone());
        } else {
            // do nothing
        }
    }

    public Result initDeviceSupport() {
        ProcessLogHelper processHelper = new ProcessLogHelper("initDeviceSupport", 2, RoundingMode.DOWN);
        processHelper.setTotalNum(userRoleService.queryAllAdminUserRoleNum());
        Iterator<UserRoleDO> iterator = userRoleService.queryAllAdminUserRoleIterator("initDeviceSupport", 1000);
        while (iterator.hasNext()) {
            UserRoleDO userRole = iterator.next();
            final String sn = userRole.getSerialNumber();
            try {
                final LocalDeviceSupport localDeviceSupport = this.getRowDeviceSupport(sn);
                if (localDeviceSupport == null) {
                    processHelper.onSkip();
                    continue;
                }
                if (!Optional.ofNullable(localDeviceSupport.getSupportLocalVideoLookBack()).orElse(false)) {
                    localDeviceSupport.setSupportLocalVideoLookBack(null);
                }
                if (Optional.ofNullable(localDeviceSupport.getLocalVideoStorageType()).orElse(0) != 1) {
                    localDeviceSupport.setLocalVideoStorageType(null);
                }
                if (!Optional.ofNullable(localDeviceSupport.getSupportSdCardFormat()).orElse(false)) {
                    localDeviceSupport.setSupportSdCardFormat(null);
                }
                if (!Optional.ofNullable(localDeviceSupport.getSupportNightVisionSwitch()).orElse(false)) {
                    localDeviceSupport.setSupportNightVisionSwitch(null);
                }
                if (!Optional.ofNullable(localDeviceSupport.getSupportWhiteLight()).orElse(false)) {
                    localDeviceSupport.setSupportWhiteLight(null);
                }
                if (!Optional.ofNullable(localDeviceSupport.getSupportAlarmFlashLight()).orElse(false)) {
                    localDeviceSupport.setSupportAlarmFlashLight(null);
                }
                if (!Optional.ofNullable(localDeviceSupport.getSupportStarlightSensor()).orElse(false)) {
                    localDeviceSupport.setSupportStarlightSensor(null);
                }
                this.initDeviceSupport(sn, localDeviceSupport);
                deviceSupportService.insertDeviceSupport(sn, localDeviceSupport);
                processHelper.onProcess(Result.Success());
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "initDeviceSupport error!", e);
                processHelper.onProcess(Result.Failure(e.getMessage()));
            }
        }
        processHelper.onEnd();
        return new Result(0, processHelper.toString(), null);
    }


    public void initDeviceModelEvent(String serialNumber, String event) {
        if (!StringUtils.hasLength(event)) {
            return;
        }

        DeviceModelAISupportDo deviceModelAISupportDo = new DeviceModelAISupportDo();
        deviceModelAISupportDo.setModelNo(deviceManualService.getModelNoBySerialNumber(serialNumber));
        deviceModelAISupportDo.setEvent(event);
        deviceModelEventService.saveDeviceModelEvent(deviceModelAISupportDo);
    }

    /**
     * 删除所有设备
     */
    public void deleteAllDevice() {
        deviceDAO.deleteAllDeviceInfo();
    }

    public boolean getEventTrackingSwitch(String serialNumber) {
        String key = EVENT_TRACKING_SWITCH_KEY_PRE.replace("{serialNumber}", serialNumber);
        return "false".equals(redisService.get(key)) ? false : true;
    }


    public void updateEventTrackingSwitch(String serialNumber, boolean switchOn) {
        this.setEventTrackingSwitch(serialNumber, switchOn);

        this.handleEventAnalyticsSwitch(serialNumber, switchOn);
        this.sendConfig(serialNumber);
    }


    private void sendConfig(String serialNumber) {
        try {
            Integer userId = userDAO.selectAdminUserId();
            User user = userService.queryUserById(userId);
            LOGGER.info("sendConfig start, serialNumber:{}. userId:{} ", serialNumber, userId);
            if (user != null) {
                String tenantId = OpenApiConfigService.getTenantIdForDeviceConfig(user);
                openApiConfigService.publishConfig(serialNumber, tenantId);
            }
        } catch (Exception e) {
            LOGGER.error("sendConfig error, serialNumber:{} ", serialNumber, e);
        }

    }

    private void handleEventAnalyticsSwitch(String serialNumber, boolean switchOn) {

        HandleEventAnalyticsSwitchRequest request = HandleEventAnalyticsSwitchRequest.newBuilder()
                .setSerialNumber(serialNumber)
                .setType(1) // 目前只有bx
                .setSwitch(switchOn)
                .build();
        try {
            SendResult result = bstationdSettingProcessorBlockingStub.handleEventAnalyticsSwitch(request);
            LOGGER.info("bstationdSettingProcessorBlockingStub.handleEventAnalyticsSwitch , req:{} ,resp:{}",  request, result);
            if (result.getCode() != ResultCollection.SUCCESS.getCode()) {
                LOGGER.error("bstationdSettingProcessorBlockingStub.handleEventAnalyticsSwitch  failed, req:{}", request);
            }
        }catch (Exception e){
            LOGGER.error(" bstationdSettingProcessorBlockingStub.handleEventAnalyticsSwitch error, req:{} ",request,  e);
        }
    }

    public void setEventTrackingSwitch(String serialNumber, boolean switchOn) {
        String key = EVENT_TRACKING_SWITCH_KEY_PRE.replace("{serialNumber}", serialNumber);
        redisService.set(key, String.valueOf(switchOn));
        ReqToIotLocalService.getInstance().onEventTrackingSwitchChange(serialNumber, switchOn);
    }
}
