package com.addx.iotcamera.service.ops;

import com.addx.iotcamera.bean.app.AliveRequest;
import com.addx.iotcamera.bean.app.AskForDeviceLogRequest;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.ops.BriefLibraryQuery;
import com.addx.iotcamera.bean.ops.BriefRegistInfo;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.helper.DeviceOperationHelper;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.requests.MqttDeviceAskLogRequest;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceManualService;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.LocalDeviceSupport;
import org.addx.iot.domain.extension.video.param.LibraryRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;

@Component @Lazy
public class DeviceOpsService {

    @Autowired @Lazy
    private IDeviceDAO deviceDAO;

    @Autowired @Lazy
    private LibraryStatusService libraryStatusService;

    @Autowired @Lazy
    private UserService userService;

    @Autowired @Lazy
    private UserRoleService userRoleService;

    @Autowired @Lazy
    private UserVipService userVipService;

    @Autowired @Lazy
    private UdpService udpService;

    @Autowired @Lazy
    private VernemqPublisher vernemqPublisher;

    @Autowired @Lazy
    private RedisService redisService;

    @Autowired @Lazy
    private DeviceOperationHelper deviceOperationHelper;

    @Resource @Lazy
    private DeviceManualService deviceManualService;

    @Resource @Lazy
    private DeviceInfoService deviceInfoService;

    private static Logger LOGGER = LoggerFactory.getLogger(DeviceOpsService.class);

    public BriefRegistInfo getDeviceBriefInfo(String userSn) {

        String serialNumber = deviceManualService.getSerialNumberByUserSn(userSn);
        if (null == serialNumber) {
            return new BriefRegistInfo();
        }
        DeviceDO deviceDO = deviceDAO.getAllDeviceInfo(serialNumber);
        if (null == deviceDO || 0 == deviceDO.getActivated()) {
            return new BriefRegistInfo();
        }
        int adminId = userRoleService.getDeviceAdminUser(deviceDO.getSerialNumber());
        User user = userService.queryUserById(adminId);
        if (user == null) {
            return new BriefRegistInfo();
        }

        LocalDeviceSupport localDeviceSupport = deviceInfoService.getDeviceSupport(serialNumber);
        Boolean support = localDeviceSupport == null ? false : localDeviceSupport.getSupportResetVoltameter();

        BriefRegistInfo res = BriefRegistInfo.builder()
                .activatedTime(deviceDO.getActivatedTime())
                .adminEmail(user.getEmail())
                .adminName(user.getName())
                .uid(serialNumber)
                .userSn(userSn)
                .supportResetVoltameter(support)
                .build();
        return res;
    }

    public Result countDeviceLibraries(BriefLibraryQuery libraryQuery) {
        Integer count = 0;
        String serialNumber = deviceManualService.getSerialNumberByUserSn(libraryQuery.getUserSn());
        if (null == serialNumber) {
            return Result.KVResult("count", count);
        }
        DeviceDO deviceDO = deviceDAO.getAllDeviceInfo(serialNumber);
        if (null == deviceDO || 0 == deviceDO.getActivated()) {
            return Result.KVResult("count", count);
        }

        int adminId = userRoleService.getDeviceAdminUser(deviceDO.getSerialNumber());
        LibraryRequest libraryRequest = new LibraryRequest();
        libraryRequest.setSerialNumber(new ArrayList<>());
        libraryRequest.getSerialNumber().add(deviceDO.getSerialNumber());
        libraryRequest.setUserId(adminId);
        if (null != libraryQuery.getStartTimestamp() && null != libraryQuery.getEndTimestamp()) {
            libraryRequest.setStartTimestamp(libraryQuery.getStartTimestamp().longValue());
            libraryRequest.setEndTimestamp(libraryQuery.getEndTimestamp().longValue());
        } else {
            libraryRequest.setStartTimestamp(0L);
            libraryRequest.setEndTimestamp(PhosUtils.getUTCStamp().longValue());
        }
        count = libraryStatusService.queryLibraryCount(libraryRequest);

        return Result.KVResult("count", count);
    }

    public Result askLogFromDevice(AskForDeviceLogRequest askForDeviceLogRequest) {
        String serialNumber = askForDeviceLogRequest.getSerialNumber();

        MqttDeviceAskLogRequest mqttDeviceAskLogRequest = new MqttDeviceAskLogRequest(askForDeviceLogRequest);

        // 发送keepAlive唤醒设备
        Result result = udpService.keepAlive(-1, AliveRequest.builder() //TODO: 增加白名单进行操作校验
                .serialNumber(serialNumber)
                .seconds(30)
                .build());

        if (result.getResult() != 0) {
            LOGGER.info("Failed to wake {} up to upload log", serialNumber);
        } else {
            redisService.setDeviceOperationDOWithEmpty(mqttDeviceAskLogRequest.getId());

            vernemqPublisher.askLogFromDevice(mqttDeviceAskLogRequest);

            result = deviceOperationHelper.waitOperation(mqttDeviceAskLogRequest.getId());
        }
        return result;
    }
}
