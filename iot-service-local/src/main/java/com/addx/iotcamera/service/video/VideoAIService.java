package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.domain.AITask;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.AiTaskParamsConfig;
import com.addx.iotcamera.enums.VipAiAnalyzeType;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.JsonUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.protobuf.ByteString;
import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.proto.*;
import org.addx.iot.common.proto.ai_station.*;
import org.addx.iot.common.proto.iot_local.GetVideoEventTaskSettingsResult;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.extension.ai.model.AiFeature;
import org.addx.iot.domain.extension.ai.param.AIEdgeRequest;
import org.addx.iot.domain.extension.core.IExtensionManager;
import org.addx.iot.domain.user.service.IUserService;
import org.addx.iot.domain.video.entity.VideoSliceDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.net.URL;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;


@Slf4j(topic = "videoGenerate")
@Component
@Lazy
public class VideoAIService {

    @Autowired
    @Lazy
    private AiTaskParamsConfig aiTaskParamsConfig;
    @Autowired
    @Lazy
    private ActivityZoneService activityZoneService;
    @Autowired
    @Lazy
    private DeviceInfoService deviceInfoService;
    @Autowired
    @Lazy
    private VipService vipService;
    @Autowired
    @Lazy
    private UserRoleService userRoleService;
    @Autowired
    @Lazy
    private TimeTicker timeTicker;
    @Autowired
    @Lazy
    @Qualifier("commonPool")
    private Executor executor;

    @GrpcClient("ai-station-grpc-server")
    @Lazy
    private AIStationSettingProcessorGrpc.AIStationSettingProcessorFutureStub aiStationSettingProcessorFeatureStub;

    @GrpcClient("ai-station-grpc-server")
    @Lazy
    private AIStationTaskProcessorGrpc.AIStationTaskProcessorFutureStub aiStationTaskProcessorFeatureStub;
    @GrpcClient("ai-station-grpc-server")
    @Lazy
    CompressImageServiceGrpc.CompressImageServiceBlockingStub compressImageServiceBlockingStub;
    @Autowired
    private UserService userService;
    @Autowired @Lazy
    private IExtensionManager extensionManager;
    @Autowired @Lazy
    private IUserService iUserService;


    /**
     * 构建ai任务的通用参数
     * 这个方法要在video的admin和vip查询完毕后调用
     */
    public InputEdgeData createDefaultSaasAITask(VideoCommonCache video, Collection<String> extraAiDetectEvents) {
        final InputEdgeData.Builder saasAiTaskBuilder = InputEdgeData.newBuilder();
        if (!video.getIsVip()) {
            return saasAiTaskBuilder.build(); // 不是vip，则不查后续信息
        }
        final VipAiAnalyzeType vipTierType = VipAiAnalyzeType.getVipTierType(video.getIsVip(), video.getIsBirdVip());
        List<String> aiDetectNotifyEvents = deviceInfoService.pushAiList(video.getSerialNumber(), video.getAdminId(), vipTierType.getSupportEventObjectNames());
        if (CollectionUtils.isNotEmpty(extraAiDetectEvents)) {
            aiDetectNotifyEvents = ImmutableList.<String>builder().addAll(aiDetectNotifyEvents).addAll(extraAiDetectEvents).build();
        }
        if (CollectionUtils.isEmpty(aiDetectNotifyEvents)) {
            return saasAiTaskBuilder.build(); // AI分析开关为空，则不查后续信息
        }
        final AITask.IdentificationBox identificationBox = aiTaskParamsConfig.getIdentificationBoxByTenantId(video.getTenantId());
        final Map<String, String> event2Color = Optional.ofNullable(identificationBox).map(AITask.IdentificationBox::getColors)
                .orElseGet(Collections::emptyList).stream().collect(Collectors.toMap(AITask.Color::getName, AITask.Color::getColor));
        List<AiFeature> aiFeatures = extensionManager.getInstalledAiExtensions(video.getSerialNumber()).stream()
                .flatMap(it->it.getSupportAiFeatures().stream())
                .distinct()
                .collect(Collectors.toList());
        for (String event : FuncUtil.iterable(aiDetectNotifyEvents)) {
            if (event2Color.get(event) != null) {
                Color color = Color.newBuilder().setName(event).setColor(event2Color.get(event)).build();
                saasAiTaskBuilder.addColors(color);
            }
        }
        aiFeatures.stream().distinct().forEach((aiFeature)->{
            AIFeature.Builder aiFeatureBuilder = AIFeature.newBuilder()
                    .setId(aiFeature.getId())
                    .setName(aiFeature.getName());
            saasAiTaskBuilder.addFeatures(aiFeatureBuilder.build());
        });

        activityZoneService.queryActivityZone(video.getSerialNumber()).forEach(az -> {
            saasAiTaskBuilder.addActivityZoneList(ActivityZone.newBuilder().setId(az.getId()).setVertices(az.getVertices()));
        });
        return saasAiTaskBuilder.build();
    }

    /* eg:
    {
        "detectedFrames": [
            {
                "boxes": [
                    {
                        "bottom": 0.9982021,
                        "left": 0.37112865,
                        "name": "person",
                        "right": 0.58176684,
                        "score": 0.2896165,
                        "top": 0.353037
                    }
                ],
                "image": "file0",
                "imageOrder": 1,
                "timestamp": 7359241
            }
        ],
        "deviceSn": "c3f3b51962e88e970daab72353a5786c",
        "isLast": 0,
        "modelNo": "SS1131W1",
        "order": 1,
        "traceId": "04081709813887xJdVOYC3etXqCSQ"
    }
    {
        "detectedFrames": [
            {
                "boxes": [],
                "image": "file0",
                "imageOrder": 1,
                "timestamp": 0
            }
        ],
        "deviceSn": "c3f3b51962e88e970daab72353a5786c",
        "isLast": 1,
        "modelNo": "SS1131W1",
        "order": 1,
        "traceId": "04081709813991Hirbz16BNniI0t3"
    }
     */
    public Result<InputEdgeData> createAIStationTask(VideoCache video, AIEdgeRequest request
            , Map<String, FileItem> filename2Item) {
        long beginTime = System.currentTimeMillis();
        log.info("createAIStationTask begin! request={},filenames={}", JSON.toJSONString(request), JSON.toJSONString(filename2Item.keySet()));
        final Map<String, String> field2ErrMsg = new LinkedHashMap<>();
        if (StringUtils.isBlank(request.getTraceId())) {
            field2ErrMsg.put("$.order", "视频traceId不能为空!");
        }
        if (StringUtils.isBlank(request.getDeviceSn())) {
            field2ErrMsg.put("$.order", "设备deviceSn不能为空!");
        }
        if (StringUtils.isBlank(request.getModelNo())) {
            field2ErrMsg.put("$.order", "设备modelNo不能为空!");
        }
        if (Optional.ofNullable(request.getOrder()).orElse(-1) < 0) {
            field2ErrMsg.put("$.order", "请求顺序不合法!");
        }
        if (request.getIsLast() == null || request.getIsLast() < 0 || request.getIsLast() > 1) {
            field2ErrMsg.put("$.isLast", "是否最后一个请求不合法!");
        }
        final List<AIEdgeRequest.Frame> detectedFrames = Optional.ofNullable(request.getDetectedFrames()).orElseGet(Collections::emptyList);
        if (detectedFrames.isEmpty()) {
            field2ErrMsg.put("$.detectedFrames", "视频检测帧为空!");
        }
        final List<DetectedFrame> taskFrames = new LinkedList<>();
        int frameIndex = -1;
        for (final AIEdgeRequest.Frame detectedFrame : detectedFrames) {
            frameIndex++;
            if (Optional.ofNullable(detectedFrame.getImageOrder()).orElse(-1) < 0) {
                field2ErrMsg.put("$.detectedFrames[" + frameIndex + "].imageOrder", "图片顺序不合法!");
                continue;
            }
            if (detectedFrame.getTimestamp() == null) {
                field2ErrMsg.put("$.detectedFrames[" + frameIndex + "].timestamp", "时间戳不合法!");
                continue;
            }
            if (StringUtils.isBlank(detectedFrame.getImage())) {
                field2ErrMsg.put("$.detectedFrames[" + frameIndex + "].image", "图片名称不能为空!");
                continue;
            }
            final FileItem fileItem = filename2Item.get(detectedFrame.getImage());
            if (fileItem == null) {
                field2ErrMsg.put("$.detectedFrames[" + frameIndex + "].image", "图片内容不存在!");
                continue;
            }
            final ByteString imageData;
            try {
                imageData = ByteString.copyFrom(fileItem.get());
                log.info("createAIStationTask read inputStream end! sn={},traceId={},imageName={},imageSize={}", request.getDeviceSn(), request.getTraceId(), detectedFrame.getImage(), imageData.size());
            } catch (Throwable e) {
                field2ErrMsg.put("$.detectedFrames[" + frameIndex + "].image", "图片内容读取失败!");
                continue;
            }
            if (imageData.isEmpty()) {
                if (request.getIsLast() != 1) {
                    field2ErrMsg.put("$.detectedFrames[" + frameIndex + "].image", "图片内容为空! isLast=" + request.getIsLast()); // isLast=1时属于正常情况
                }
                continue;
            }
            DetectedFrame.Builder taskFrameBuilder = DetectedFrame.newBuilder()
                    .setImageOrder(detectedFrame.getImageOrder())
                    .setTimestamp(detectedFrame.getTimestamp())
                    // aiFeature
                    .setImage(JpegImageFile.newBuilder().setData(imageData));
            if (CollectionUtils.isNotEmpty(detectedFrame.getBoxes())) {
                for (AIEdgeRequest.Box it : detectedFrame.getBoxes()) {
                    final BoundingBox.Builder boxBuilder = BoundingBox.newBuilder().setScore(it.getScore())
                            .setLeft(it.getLeft()).setTop(it.getTop()).setRight(it.getRight()).setBottom(it.getBottom());
                    Optional.ofNullable(it.getClassId()).ifPresent(boxBuilder::setClassId);
                    Optional.ofNullable(it.getName()).ifPresent(boxBuilder::setName);
                    taskFrameBuilder.addBoxes(boxBuilder);
                }
            }
            if (StringUtils.isNotBlank(detectedFrame.getMd5())) {
                taskFrameBuilder.setMd5(detectedFrame.getMd5());
            }
            // 给算法传递关键帧时间信息
            taskFrameBuilder.setTimestamp(detectedFrame.getUtcTimestampMillis());  // utc 时间  毫秒ß
            taskFrameBuilder.setPts(detectedFrame.getTimestamp()/1000);  // pts 时间  后续全部使用毫秒
            taskFrames.add(taskFrameBuilder.build());
        }
        if (field2ErrMsg.size() > 0) {
            final String errMsg = JSON.toJSONString(field2ErrMsg, true);
            log.warn("createAIStationTask fail! costTime={},errMsg={},request={},filenames={}", (System.currentTimeMillis() - beginTime), errMsg, JSON.toJSONString(request), JSON.toJSONString(filename2Item.keySet()));
            return Result.Error(ResultCollection.INVALID_PARAMS, errMsg);
        }
        final InputEdgeData.Builder aiStationTaskBuilder = InputEdgeData.newBuilder()
                /* 以下为视频公共参数 */
                .setTraceId(request.getTraceId())
                .setDeviceSn(request.getDeviceSn())
                .setModelNo(request.getModelNo())
                .setOwnerId(video.getAdminId() + "")
                .setTenantId(video.getTenantId())
                .setTaskId(OpenApiUtil.shortUUID())
                .setTaskSendTime(timeTicker.readSeconds())
                /* 以下为AI分析请求的顺序 */
                .setOrder(request.getOrder())
                .setIsLast(request.getIsLast())
                /* 以下为aiStation生成图片的根路径 */
//                .setArtifactPrefix(artifactPrefix)
                /* 以下为后端自定义透传参数 */
                .setOutParams("{}");
        for (DetectedFrame taskFrame : taskFrames) {
            aiStationTaskBuilder.addFrames(taskFrame);
        }
        /* 以下为AI分析的用户配置 */
        for (ActivityZone activityZone : video.getDefaultSaasAITask().getActivityZoneListList()) {
            aiStationTaskBuilder.addActivityZoneList(activityZone);
        }
        // aiFeature
        for (AIFeature aiFeature : video.getDefaultSaasAITask().getFeaturesList()) {
            aiStationTaskBuilder.addFeatures(aiFeature);
        }

        // 允许分析的生物特征
        Biometrics faceBiometrics = Biometrics.FACE;
        Biometrics bodyBiometrics = Biometrics.BODY;
        aiStationTaskBuilder.addAllowed(faceBiometrics);
        aiStationTaskBuilder.addAllowed(bodyBiometrics);

        final InputEdgeData aiStationTask = aiStationTaskBuilder.build();
        if (aiStationTask.getFeaturesCount() == 0) {
            log.info("createAIStationTask end! costTime={},aiStationTask={}", (System.currentTimeMillis() - beginTime), null);
        } else {
            log.info("createAIStationTask end! costTime={},aiStationTask={}",
                    (System.currentTimeMillis() - beginTime), aiStationTask.getFeaturesList().stream().map(AIFeature::getName).collect(Collectors.toList()));
        }
        return new Result<>(aiStationTask);
    }

    private VideoCommonCache getVideoCommonCache(String serialNumber){
        VideoCommonCache video= new VideoCommonCache();
        int adminUserId= userRoleService.getDeviceAdminUser(serialNumber);
        User user = userService.queryUserById(adminUserId);
        video.setTenantId(user.getTenantId());
        video.setAdminId(adminUserId);
        video.setSerialNumber(serialNumber);
        video.setIsVip(vipService.isVipDevice(video.getAdminId(), video.getSerialNumber()));
        video.setIsBirdVip(vipService.isBirdVipDevice(video.getAdminId(), video.getSerialNumber()));
        video.setDefaultSaasAITask(createDefaultSaasAITask(video, new ArrayList<>()));
        return video;
    }

    public GetVideoEventTaskSettingsResult getVideoEventTaskSettings(String serialNumber) {
        GetVideoEventTaskSettingsResult.Builder aiStationTaskSettingsBuilder = GetVideoEventTaskSettingsResult.newBuilder();
        VideoCommonCache video= getVideoCommonCache(serialNumber);
        for (ActivityZone activityZone : video.getDefaultSaasAITask().getActivityZoneListList()) {
            aiStationTaskSettingsBuilder.addActivityZoneList(activityZone);
        }
        // aiFeature
        for (AIFeature aiFeature : video.getDefaultSaasAITask().getFeaturesList()) {
            aiStationTaskSettingsBuilder.addFeatures(aiFeature);
        }

        // 允许分析的生物特征
        Biometrics faceBiometrics = Biometrics.FACE;
        Biometrics bodyBiometrics = Biometrics.BODY;
        aiStationTaskSettingsBuilder.addAllowed(faceBiometrics);
        aiStationTaskSettingsBuilder.addAllowed(bodyBiometrics);
        return aiStationTaskSettingsBuilder.build();
    }

    public AiCloudParam getAiCloudParam(String serialNumber) {
        VideoCommonCache video= getVideoCommonCache(serialNumber);

        log.info("getAiCloudParam start, video:{}", JSON.toJSONString(video));
        AiCloudParam aiCloudParam = AiCloudParam.newBuilder()
                .setOwnerId(video.getAdminId())
                .setTenantId(video.getTenantId())
                .addAllActivityZoneList(video.getDefaultSaasAITask().getActivityZoneListList().stream().map((it-> Activityzonelist.newBuilder()
                        .setId(it.getId())
                        .addAllVertices(Arrays.stream(it.getVertices().split(",")).map((Double::valueOf)).collect(Collectors.toList()))
                        .addAllPose(it.getPoseList())
                        .build())).collect(Collectors.toList()))
                .addAllColors(video.getDefaultSaasAITask().getColorsList())
                .addAllFeatures(video.getDefaultSaasAITask().getFeaturesList())
                .addAllAllowed(new ArrayList<Biometrics>(){{add(Biometrics.FACE); add(Biometrics.BODY); }})
                .build();
        return aiCloudParam;
    }

    public boolean sendSaasAiTask(VideoCache video, VideoSliceDO slice) {
        return true;
    }

    public void sendLastSaasAiTask(VideoCache video) {
    }

    public void sendBxBindEvent() {
        try{
            aiStationSettingProcessorFeatureStub.notifyDeviceBound(BindRequest.newBuilder().build());
        }catch (Exception e){
            log.error("sendBxBindEvent to AI service error!",e);
        }
    }

    public void sendBxUnBindEvent() {
        try{
            aiStationSettingProcessorFeatureStub.notifyDeviceUnbound(UnbindRequest.newBuilder().build());
        }catch (Exception e){
            log.error("sendBxUnBindEvent to AI service error!",e);
        }
    }

    public void sendByGrpc(InputEdgeData input) {

        try {
            final ListenableFuture<SendResult> future = aiStationTaskProcessorFeatureStub.send(input);
            long beginTime = System.currentTimeMillis();
            final Object requestId = MDC.get("requestId");
            future.addListener(() -> {
                MDC.put("requestId", requestId);
                try {
                    final SendResult result = future.get();
                    log.info("sendSaasAiTask async end! grpc costTime={},input={},result={}", (System.currentTimeMillis() - beginTime), JsonUtil.grpcMsgToStr(input), result);
                } catch (Throwable e) {
                    log.error("sendSaasAiTask async fail! grpc costTime={},input={}", (System.currentTimeMillis() - beginTime), JsonUtil.grpcMsgToStr(input), e);
                }
            }, executor);
        } catch (Throwable e) {
            log.error("sendSaasAiTask fail! grpc input={}", JsonUtil.grpcMsgToStr(input), e);
        }
    }

    private static void shutdown(ManagedChannel channel) {
        try {
            channel.shutdown();
        } catch (Throwable e) {
            log.error("ManagedChannel shutdown error!", e);
        }
    }

    // 压缩图片
    public String compressImage(String imageUrl) {

        final long beginTime = System.currentTimeMillis();
        try (final InputStream fis = new URL(imageUrl).openStream()) {
            final JpegImageFile req = JpegImageFile.newBuilder().setData(ByteString.readFrom(fis)).build();
            final CompressedData resp = compressImageServiceBlockingStub.compress(req);
            if (resp.getCode() == 0) {
                final ByteString imageData = resp.getFile().getData();
                final byte[] imageBytes = imageData.toByteArray();
                final String imageBase64 = Base64.getEncoder().encodeToString(imageBytes);
                log.info("compressImage end! grpc imageUrl={},costTime={},imageBytesLength={},imageBase64Length={}", imageUrl, (System.currentTimeMillis() - beginTime), imageBytes.length, imageBase64.length());
                return imageBase64;
            }
            log.info("compressImage end! grpc imageUrl={},costTime={},resp={}", imageUrl, (System.currentTimeMillis() - beginTime), resp);
        } catch (Throwable e) {
            log.error("compressImage fail! grpc imageUrl={},costTime={}", imageUrl, (System.currentTimeMillis() - beginTime), e);
        }
        return ""; // 空字符串
    }

}
