package com.addx.iotcamera.service.device;

import com.addx.biz.SecurityModeBiz;
import com.addx.biz.dto.SecurityModeInfo;
import com.addx.biz.dto.SecurityModeSettingsDetail;
import com.addx.biz.dto.SimpleDeviceAttribute;
import com.addx.domain.settings.device.model.DeviceAttribute;
import com.addx.domain.settings.securitymode.model.SecurityMode;
import com.addx.iotcamera.bean.device.attributes.DeviceAttributesModify;
import com.addx.iotcamera.bean.device.attributes.DeviceModifiableAttribute;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.bean.response.device.DevicePushImage;
import com.addx.iotcamera.config.AbilityConfig;
import com.addx.iotcamera.enums.DeviceOnlineStatusEnums;
import com.addx.iotcamera.service.BxBindService;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.message.MessageToAppService;
import com.addx.iotcamera.service.statemachine.StateMachineService;
import com.addx.iotcamera.servlet.CustomHttpServletRequest;
import com.addx.iotcamera.servlet.LoggableDispatcherServlet;
import com.addx.iotcamera.thread.TaskExecutePool;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.domain.extension.core.IExtension;
import org.addx.iot.domain.extension.core.IExtensionManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.DeviceInfoConstants.BASE_STATION_TYPE;

/**
 * 能力集和首页状态查询相关功能:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2024/5/21 16:53
 */

@Slf4j
@Component
@Lazy
public class DeviceAbilityService {
    public static final int NOT_INIT_MODE = 0;
    @Resource
    @Lazy
    private AbilityConfig abilityConfig;
    @Autowired
    @Lazy
    private UserRoleService userRoleService;

    @Autowired
    @Lazy
    private DeviceInfoService deviceInfoService;

    @Autowired
    @Lazy
    private BxBindService bxBindService;

    @Autowired
    @Lazy
    private DeviceStatusService deviceStatusService;

    @Autowired
    @Lazy
    private StateMachineService stateMachineService;

    @Autowired
    @Lazy
    private SecurityModeBiz securityModeBiz;

    @Autowired
    @Lazy
    TaskExecutePool taskExecutePooll;

    @Autowired
    @Lazy
    private DeviceSettingService deviceSettingService;

    @Autowired
    @Lazy
    private DeviceAttributeService deviceAttributeService;
    @Autowired
    @Lazy
    private MessageToAppService messageToAppService;

    @Autowired
    @Lazy
    private IExtensionManager extensionManager;
    @Autowired
    private UserService userService;


    @PostConstruct
    public void init() {
        //检查是否有场景模式需要变更
        taskExecutePooll.commonPool().execute(() -> checkAndModifyMode());

    }

    public String queryAbility(String serialNumber) {
        int uid = userRoleService.getDeviceAdminUser(serialNumber);
        Map<String, Object> map = getStatusMap(serialNumber, uid, false);

        if (uid > 0) {
            map.put("stationSupportList", abilityConfig.getStationSupports());
        }

        return JSON.toJSONString(map);
    }


    public String queryStatus(String serialNumber) {

        int uid = userRoleService.getDeviceAdminUser(serialNumber);
        Map<String, Object> map = getStatusMap(serialNumber, uid, false);
        return JSON.toJSONString(map);
    }

    // 主动推送时，有敏感信息限制，不能推送封面图
    public String queryPushStatus(String serialNumber) {

        int uid = userRoleService.getDeviceAdminUser(serialNumber);
        Map<String, Object> map = getStatusMap(serialNumber, uid, true);
        // TODO face 推送extension状态
        // TODO face 主动查询
        return JSON.toJSONString(map);
    }

    private Map<String, Object> getStatusMap(String serialNumber, int uid, boolean sensitive) {
        Map<String, Object> map = new HashMap<>();
        if (uid > 0) {
            List<IExtension> installedExtensions = extensionManager.getInstalledExtensions(uid);
            List<String> installedExtensionNames = installedExtensions.stream().map(IExtension::getName).collect(Collectors.toList());
            SecurityModeInfo securityModeInfo = securityModeBiz.querySecurityModesByUid(uid);
            map.put("securityModes", securityModeInfo.getSecurityModes());
            map.put("selectedModeId", securityModeInfo.getSelectedModeId());
            map.put("securityModeSettings", securityModeInfo.getSecurityModeSettings());
            map.put("extensionInstalledStatus", installedExtensionNames);

            UserRoleDO userRole = userRoleService.getUserRoleDOByUserIdAndSerialNumber(uid, serialNumber);
            if (Objects.equals(userRole.getBaseStation(), BASE_STATION_TYPE)) {
                Set<String> serialNumberList = bxBindService.getCxSnsByBx(serialNumber);
                if (CollectionUtils.isNotEmpty(serialNumberList)) {
                    //获取封面图
                    HttpServletRequest request = new CustomHttpServletRequest();
                    LoggableDispatcherServlet.setRequest(request);

                    List<DevicePushImage> imgs = deviceInfoService.queryUserDevicePushImage(uid);
                    Map<String, DevicePushImage> imgMap = imgs.stream().collect(Collectors.toMap(item -> item.getSerialNumber(), Function.identity()));

                    List<DeviceDO> deviceDOList = getDeviceStatus(serialNumberList, userRole.getUserId());


                    List<SimpleDeviceInfo> deviceInfos = new ArrayList<>();
                    for (DeviceDO deviceDo : deviceDOList) {
                        SimpleDeviceInfo simpleDeviceInfo = new SimpleDeviceInfo();
                        simpleDeviceInfo.setDeviceName(deviceDo.getDeviceName());
                        simpleDeviceInfo.setUserSn(deviceDo.getUserSn());
                        simpleDeviceInfo.setSerialNumber(deviceDo.getSerialNumber());
                        simpleDeviceInfo.setOnline(deviceDo.getOnline());
                        simpleDeviceInfo.setModelNo(deviceDo.getModelNo());
                        simpleDeviceInfo.setBatteryLevel(deviceDo.getBatteryLevel());
                        simpleDeviceInfo.setDeviceNetType(deviceDo.getDeviceNetType());
                        simpleDeviceInfo.setSignalStrength(deviceDo.getSignalStrength());

                        if (!sensitive && imgMap.containsKey(simpleDeviceInfo.getSerialNumber())) {
                            DevicePushImage devicePushImage = imgMap.get(simpleDeviceInfo.getSerialNumber());
                            simpleDeviceInfo.setLastPushTime(devicePushImage.getLastPushTime());
                            simpleDeviceInfo.setLastPushImageUrl(devicePushImage.getLastPushImageUrl());
                        }

                        //未读数
                        // 本期先不提供，下期再提供，可能需要优化，循环取性能不好
//                        LibraryRequest libraryRequest = new LibraryRequest();
//                        libraryRequest.setUserId(uid);
//                        long currentTime = System.currentTimeMillis()/1000;
//                        libraryRequest.setStartTimestamp(currentTime -  30 * 24 * 3600);
//                        libraryRequest.setEndTimestamp(currentTime);
//                        libraryRequest.setFrom(0);
//                        libraryRequest.setMissing(1);
//                        libraryRequest.setSerialNumber(Collections.singletonList(deviceDo.getSerialNumber()));
//                        List<LibraryCountDay>  libraryCountDays = libraryStatusService.queryLibraryCountDay(libraryRequest);
//                        simpleDeviceInfo.setLibraryCountDays(libraryCountDays);

                        deviceInfos.add(simpleDeviceInfo);
                    }

                    map.put("cameraList", deviceInfos);
                } else {
                    map.put("cameraList", new ArrayList<>());
                }

            }
        }
        map.put("eventAnalyticsSwitch", deviceInfoService.getEventTrackingSwitch(serialNumber));

        map.put("bindStatus", uid == 0 ? 0 : 1);
        map.put("stationSerialNumber", serialNumber);
        return map;
    }

    private List<DeviceDO> getDeviceStatus(Set<String> serialNumberList, Integer userId) {
        List<String> serialNumberListist = new ArrayList<>(serialNumberList);
        //get online status
        //设备状态相关
        List<DeviceDO> deviceDOList = deviceInfoService.queDeviceBySerialNumbers(serialNumberListist);
        //设备状态
        Map<String, DeviceStatusDO> mapDeviceStatusDO = deviceStatusService.queryDeviceStatus(serialNumberListist);
        Map<String, DeviceStateDO> deviceStateDOMap = Collections.emptyMap();
        try {
            deviceStateDOMap = stateMachineService.batchGetDeviceState(serialNumberListist);
        } catch (Exception e) {
            log.warn("failed batchGetDeviceState serialNumberList:{}", serialNumberList);
        }
        //组装各部分信息
        int time = (int) (System.currentTimeMillis() / 1000);

        final User user = userId != null ? userService.queryUserById(userId) : null;
        for (DeviceDO deviceDO : deviceDOList) {
            //不设置会有空指针问题
            deviceDO.setUserId(userId);
            deviceInfoService.fillDeviceStatus(deviceDO, mapDeviceStatusDO, deviceStateDOMap, time, user);
        }
        return deviceDOList;
    }

    private void checkAndModifyMode() {

        while (true) {
            try {
                String bxSn = userRoleService.getBxSn();
                if (StringUtils.isNotEmpty(bxSn)) {
                    UserRoleDO userRoleDO = userRoleService.getDeviceAdminUserRole(bxSn);
                    SecurityModeInfo securityModeInfo = securityModeBiz.querySecurityModesByUid(userRoleDO.getUserId());
                    Integer selectedModeId = securityModeInfo.getSelectedModeId();
                    if (selectedModeId != null && selectedModeId == NOT_INIT_MODE) {
                        setDefaultModeAfterBind(userRoleDO, securityModeInfo);
                    }

                    if (selectedModeId != null && selectedModeId > 0) {
                        SecurityModeSettingsDetail securityModeSettingsDetail = securityModeBiz.getSecurityModeSettings(userRoleDO.getUserId(), selectedModeId);
                        //比较哪些相机的设置跟当前模式不同，修复之
                        //相机在线 ，且开启了pir
                        if (CollectionUtils.isNotEmpty(securityModeSettingsDetail.getCameraList())) {
                            Set<String> sns = securityModeSettingsDetail.getCameraList().stream().map(SimpleDeviceAttribute::getSerialNumber).collect(Collectors.toSet());
                            List<DeviceDO> deviceDOList = getDeviceStatus(sns, userRoleDO.getUserId());
                            for (DeviceDO deviceDO : deviceDOList) {
                                if (deviceDO.getOnline().equals(DeviceOnlineStatusEnums.ONLINE.getCode())) {
                                    DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(deviceDO.getSerialNumber());
                                    if (deviceSettingsDO.getPir() > 0) {
                                        deviceDO.setUserId(userRoleDO.getUserId());
                                        changeDeviceAttributeinDeamon(selectedModeId, securityModeSettingsDetail, deviceDO);
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("checkAndModifyMode error, ", e);
            }
            try {
                Thread.sleep(30 * 1000L);
            } catch (InterruptedException e) {
                log.error("checkAndModifyMode sleep error, ", e);
            }
        }
    }

    private void setDefaultModeAfterBind(UserRoleDO userRoleDO, SecurityModeInfo securityModeInfo) {
        Optional<SecurityMode> defaultMode = securityModeInfo.getSecurityModes().stream().filter(item -> item.getKey().equals(DeviceSettingService.DEFAULT_MODE_WHEN_NOT_INIT)).findFirst();
        //初始化一下
        if (defaultMode.isPresent()) {
            log.info("init user security mode ,{},{}", userRoleDO.getUserId(), defaultMode.get().getId());
            securityModeBiz.switchMode(userRoleDO.getUserId(), defaultMode.get().getId());

            messageToAppService.sendStatusMsg(userRoleDO.getUserId());

        }
    }

    private void changeDeviceAttributeinDeamon(Integer selectedModeId, SecurityModeSettingsDetail securityModeSettingsDetail, DeviceDO deviceDO) {
        DeviceAttributeService.DeviceAttributeSource src = deviceAttributeService.getAttributeSource(deviceDO.getSerialNumber());
        List<DeviceModifiableAttribute> attrs = deviceAttributeService.createDeviceModifiableAttributes(src);

        Optional<SimpleDeviceAttribute> modeAttrs = securityModeSettingsDetail.getCameraList().stream().filter(item -> item.getSerialNumber().equals(deviceDO.getSerialNumber())).findFirst();
        if (modeAttrs.isPresent()) {
            Boolean diffFlag = isAttrsDiff(deviceDO.getSerialNumber(), attrs, modeAttrs.get());
            if (diffFlag) {
                log.info("fix diff, modifySecurityModeSettings {}, {}", deviceDO.getSerialNumber(), JSON.toJSONString(modeAttrs));
                DeviceAttributesModify input = new DeviceAttributesModify();
                input.setUserId(deviceDO.getUserId());
                input.setSerialNumber(deviceDO.getSerialNumber());
                input.setModifiableAttributes(deviceAttributeService.transfer(deviceDO.getSerialNumber(), modeAttrs.get().getAttributes()));
                deviceAttributeService.modifyDeviceAttributes(input);
            }
        }
    }

    public static Boolean isAttrsDiff(String serialNumber, List<DeviceModifiableAttribute> attrs, SimpleDeviceAttribute simpleDeviceAttribute) {
        for (DeviceAttribute deviceAttribute : simpleDeviceAttribute.getAttributes()) {
            Optional<DeviceModifiableAttribute> modifyAttribute = attrs.stream().filter(item -> item.getName().equals(deviceAttribute.getName())).findFirst();
            if (modifyAttribute.isPresent()) {
                Boolean flag = modifyAttribute.get().getValue().equals(deviceAttribute.getValue());
                if (!flag) {
                    log.info("there is diff, {}, key {}, mode value={}, camera value={} ", serialNumber, deviceAttribute.getName(), deviceAttribute.getValue(), modifyAttribute.get().getValue());
                    return true;
                }
            }
        }
        return false;
    }
}
