package com.addx.iotcamera.service.videofile;

import com.addx.iotcamera.util.StorageUtil;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TransferVideoResult {
    public long beginTime = System.currentTimeMillis();

    public int num = 0;
    public int copyVideoFileNum = 0;
    public int copyVideoFileErrNum = 0;
    public int updateVideoNum = 0;
    public int updateLibraryStatusNum = 0;
    public int updateSliceNum = 0;
    public int updateVideoObjectNum = 0;
    public int deleteVideoFileNum = 0;
    public long deleteVideoFileSize = 0;

    public long getCostTime() {
        return System.currentTimeMillis() - beginTime;
    }

    @Override
    public String toString() {
        return StorageUtil.bytesBeanToStr(this);
    }
}
