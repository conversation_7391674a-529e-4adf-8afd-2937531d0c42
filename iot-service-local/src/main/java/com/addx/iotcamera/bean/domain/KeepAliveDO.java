package com.addx.iotcamera.bean.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KeepAliveDO {
    private String serialNumber;
    private String ip;
    private Integer port;
    private String heartBeat;
    private String awake;
    private Integer lastTime;
    private Integer interval;
    private Integer timeout;
    private Integer requestId;
    private Integer requestTime;
    private String podIp;
}
