package com.addx.iotcamera.bean.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServerNode {
    /**
     * 节点API host
     */
    private String nodeUrl;
    /**
     * 节点zendeskHost host
     */
    private String zendeskHost;
    /**
     * zendesk appId
     */
    private String appId;
    /**
     * zendesk clientId
     */
    private String clientId;

    private String countlyKey;

    private String serverNode;
}
