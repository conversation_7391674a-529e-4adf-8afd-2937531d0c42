package com.addx.iotcamera.bean.db;

import com.addx.iotcamera.constants.VideoConstants;
import com.addx.iotcamera.enums.VideoType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * CREATE TABLE `video_library` (
 * `id` int(11) NOT NULL AUTO_INCREMENT,
 * `timestamp` int(11) NOT NULL,
 * `serial_number` varchar(45) CHARACTER SET latin1 NOT NULL,
 * `image_url` varchar(300) CHARACTER SET latin1 DEFAULT NULL,
 * `video_url` varchar(300) CHARACTER SET latin1 DEFAULT NULL,
 * `period` float DEFAULT '10',
 * `deleted` tinyint(1) NOT NULL DEFAULT '0',
 * `expired` tinyint(1) NOT NULL DEFAULT '0',
 * `file_size` int(11) NOT NULL DEFAULT '3072000',
 * `image_only` tinyint(1) NOT NULL DEFAULT '0',
 * PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LibraryTb {
    private Integer id;
    private Integer timestamp;
    private Integer endTimestamp;
    private String serialNumber;
    private String imageUrl;
    private String videoUrl;
    @Builder.Default
    private BigDecimal period = BigDecimal.ZERO;
    @Builder.Default
    private Integer deleted = 0;
    @Builder.Default
    private Integer expired = 0;
    @Builder.Default
    private Long fileSize = 0L;
    @Builder.Default
    private Integer imageOnly = 0;
    @Builder.Default
    private Integer adminId = 0;
    private String shareUserIds;
    @Builder.Default
    private String deviceName = "";
    private String tags;
    private String aiEdgeTags;
    private String doorbellTags;
    private String deviceCallEventTag;

    private String summaryDescription; // AI生成的视频摘要描述

    private String eventInfo;
    private String aiEdgeEventInfo;
    private String doorbellEventInfo;
    private int segment; // 是否是切片

    // 设备端切片，新增加字段
    private String traceId;
    private Integer type; // 0-完整视频;1-设备端切片
    private Boolean receivedAllSlice; // 0-切片不完整，1-切片完整
//    private Boolean receivedComplete; // 是否收到上传完成通知
//    private Long completeTime; // 上传完成时间
//    private Long s3AddressReceivedTimestamp;
//    private Long totalStartRecordingTimestamp;
//    private Long totalEndRecordingTimestamp;

//    private String activityZoneId;

    private String storageId; // 存储器id

    // eventInfo内容较长，实际需要使用时，才去视频文件目录里加载
    public String getEventInfo() {
        return eventInfo;
    }

    /* ss121 begin */
    private Integer videoType; // 视频类型。0-快照录像;1-事件录像主画面 ;1-事件录像子画面。VideoTypeEnum
    private String mainTraceId; // 主画面traceId，event recording的子画面才需要传。主画面和子画面视频切片独立上报
    private String serviceName; // 存储服务。云存、bxs、cxs
    private String codec; // 视频编码。取值:h254,h265
    /* ss121 end */

    public static boolean getIsShowPeriod(LibraryTb view) {
        if (view != null && view.getTimestamp() != null && view.getType() != null
                && System.currentTimeMillis() - view.getTimestamp() < VideoConstants.MAX_TOTAL_PERIOD
                && VideoType.DEVICE_SLICE.getCode() == view.getType()
                && !Optional.ofNullable(view.getReceivedAllSlice()).orElse(false)) {
            return false;
        }
        return true;
    }
}
