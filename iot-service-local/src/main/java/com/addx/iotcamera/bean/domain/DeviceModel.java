package com.addx.iotcamera.bean.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.addx.iot.domain.config.entity.LocalDeviceRemoteDO;

import java.util.Optional;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceModel {
    String serialNumber;
    String streamProtocol;
    String liveApp;
    String recApp;
    boolean canStandby;
    String keepAliveProtocol;
    boolean canRotate;
    String audioCodectype;
    boolean whiteLight;
    boolean devicePersonDetect;
    /**
     * 是否支持运动追踪
     */
    boolean supportMotionTrack;
    /**
     * 频率915
     */
    boolean supportFrequency;
    /**
     * 防拆除警报
     */
    boolean antiDisassemblyAlarm;
    /**
     * 是否是外置天线
     */
    private Boolean isExternalAntenna;
    /**
     * 视频翻转开关
     */
    private Integer mirrorFlip;
    private Integer motion_auto_xMin;
    private Integer motion_auto_x_max;
    private Integer motion_auto_n;


    public static DeviceModel from(LocalDeviceRemoteDO remoteDO) {
        return DeviceModel.builder()
                .serialNumber(remoteDO.getSerialNumber())
                .streamProtocol(Optional.ofNullable(remoteDO.getStreamProtocol()).orElse(""))
                .liveApp("live")
                .recApp("rec")
                .canStandby(Optional.ofNullable(remoteDO.getCanStandby()).orElse(0).equals(1))
                .keepAliveProtocol(Optional.ofNullable(remoteDO.getKeepAliveProtocol()).orElse(""))
                .canRotate(Optional.ofNullable(remoteDO.getCanRotate()).orElse(0).equals(1))
                .audioCodectype(Optional.ofNullable(remoteDO.getAudioCodectype()).orElse(""))
                .whiteLight(Optional.ofNullable(remoteDO.getWhiteLight()).orElse(0).equals(1))
                .devicePersonDetect(Optional.ofNullable(remoteDO.getDevicePersonDetect()).orElse(0).equals(1))
                .supportMotionTrack(Optional.ofNullable(remoteDO.getSupportMotionTrack()).orElse(1).equals(1))
                .supportFrequency(Optional.ofNullable(remoteDO.getSupportFrequency()).orElse(1).equals(1))
                .antiDisassemblyAlarm(Optional.ofNullable(remoteDO.getAntiDisassemblyAlarm()).orElse(1).equals(1))
                .isExternalAntenna(Optional.ofNullable(remoteDO.getIsExternalAntenna()).orElse(0).equals(1))
                .mirrorFlip(Optional.ofNullable(remoteDO.getMirrorFlip()).orElse(0))
                .build();
    }
}
