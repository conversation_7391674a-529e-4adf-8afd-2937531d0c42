package com.addx.iotcamera.helper;

import com.addx.iotcamera.bean.openapi.OpenApiResult;
import com.addx.iotcamera.config.apollo.DeviceDebugConfig;
import com.addx.iotcamera.config.apollo.DeviceLanguageConfig;
import com.addx.iotcamera.config.apollo.GlobalConfig;
import com.addx.iotcamera.config.app.TenantFreeTierConfig;
import com.addx.iotcamera.config.device.*;
import com.addx.iotcamera.controller.auth.AuthController;
import com.addx.iotcamera.dao.*;
import com.addx.iotcamera.dao.device.IDeviceDormancyPlanDAO;
import com.addx.iotcamera.dao.device.IDeviceManualDAO;
import com.addx.iotcamera.dao.device.IDeviceSettingDAO;
import com.addx.iotcamera.dao.model.IDeviceConfigDAO;
import com.addx.iotcamera.dao.model.IDeviceModelEventDAO;
import com.addx.iotcamera.dao.user.IUserSettingsDAO;
import com.addx.iotcamera.event.SnowPlowManager;
import com.addx.iotcamera.mqtt.MqttSender;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.*;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.user.UserAppScoreService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.videofile.BashService;
import com.addx.iotcamera.service.videofile.VideoClearService;
import com.addx.iotcamera.statemachine.StatemachineEventSender;
import com.addx.iotcamera.util.DeviceModelNoUtil;
import com.addx.kiss.service.impl.WssReportEventServiceImpl;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.SpringContextUtil;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.service.ICopywriteLanguageService;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
@Lazy(false)
public class StartupTimes implements BeanPostProcessor {

    public static final long sysUpTime = getSysUpMillis();
    public static final long iotLocalBeginTime = System.currentTimeMillis();
    public static StartupTime HTTP = new StartupTime("HTTP"); // http服务启动
    public static StartupTime GRPC = new StartupTime("GRPC"); // grpc服务启动
    public static StartupTime KISS_WS = new StartupTime("KISS_WS"); // kiss-ws服务启动

    public static Map<String, StartupTime> STARTUP_TIME_MAP = new ConcurrentHashMap<>();

    @RequiredArgsConstructor
    @Data
    @Accessors(chain = true)
    public static class StartupTime {
        private final String name;
        private long beginTime;
        private long endTime;

        public void recordBeginTime() {
            STARTUP_TIME_MAP.put(this.name, this);
            beginTime = System.currentTimeMillis();
        }

        public void recordEndTime() {
            endTime = System.currentTimeMillis();
        }

        public long getStartupTime() {
            return endTime - beginTime;
        }

        public long getTotalStartupTime() {
            return endTime - iotLocalBeginTime;
        }

        public long getWaitStartupTime() {
            return beginTime - iotLocalBeginTime;
        }

        public long getSysStartupTime() {
            return sysUpTime != -1 ? (getTotalStartupTime() + sysUpTime) : -1;
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    private static final Map<String, Long> mapBeanTime = new HashMap<>();
    public static AtomicInteger count = new AtomicInteger(0);

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        mapBeanTime.put(beanName, System.currentTimeMillis());
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Long begin = mapBeanTime.get(beanName);

        final StartupTime startupTime = new StartupTime("init bean").setBeginTime(begin).setEndTime(System.currentTimeMillis());
        int index = count.getAndIncrement();
        if (begin != null) {
            log.info("startup init bean! {} {} 耗时: startupTime={}", index, beanName, startupTime);
        } else {
            log.info("startup init bean! {} {} not found beginTime 耗时: startupTime={}", index, beanName, startupTime);
        }
        return bean;
    }

    public static long getSysUpMillis() {
        try {
            String osName = System.getProperty("os.name").toLowerCase();
            if(!osName.contains("linux")) return 0L;
            OpenApiResult<String> result = BashService.getInstance().executeCmd("awk '{print $1}' /proc/uptime");
            if (!Result.successFlag.equals(result.getCode())) return -1L;
            return new BigDecimal(result.getData().trim()).multiply(new BigDecimal(1000)).longValue();
        } catch (Throwable e) {
            log.error("getSysUpTime error!", e);
            return -1L;
        }
    }

    public static Map<String, Long> initBeans(List<Class> beanClasses) {
        Map<String, Long> bean2CostTime = new LinkedHashMap<>();
        for (Class beanCls : beanClasses) {
            long beginTime = System.currentTimeMillis();
            SpringContextUtil.getBean(beanCls);
            bean2CostTime.put(beanCls.getSimpleName(), System.currentTimeMillis() - beginTime);
        }
        return bean2CostTime;
    }

    public static final List<Class> appLoginBeanClasses = Arrays.asList(
            TokenService.class,
            UserService.class,
            JwtHelper.class,
            GlobalConfig.class,
            LoginService.class,
            AuthController.class,
            ReportLogService.class,
            VideoClearService.class // clearVideoFile
    );

    // 需要提前初始化的bean
    public static final List<Class> bindDeviceBeanClasses = Arrays.asList(
            IDeviceDAO.class,
            IUserSettingsDAO.class,
            ILocationDAO.class,
            IDeviceManualDAO.class,
            IDeviceDormancyPlanDAO.class,
            IFirmwareDAO.class,
            DeviceAiSettingsDAO.class,
            IDeviceModelEventDAO.class,
            IUserVipDAO.class,
            DeviceModelEnumMappingDAO.class,
            IDeviceConfigDAO.class,
            DynamicModelConfigV2DAO.class,
            DynamicModelConfigDAO.class,
            IDeviceSettingDAO.class,

            TenantFreeTierConfig.class,
            UserVipActivateService.class,
            UserSettingService.class,
            UserAppScoreService.class,
            LocationInfoService.class,
            ICopywriteLanguageService.class,
            DeviceLanguageConfig.class,
            DeviceInfoService.class,
            DeviceManualService.class,
            RotationPointService.class,
            RotationPointDao.class,
            UserVipService.class,
            DeviceDormancyPlanService.class,
            DeviceService.class,
            MqttSender.class,
            KissWsService.class,
            DeviceStatusService.class,
            VideoSearchService.class,
            DeviceCameraNameConfig.class,
            FirmwareService.class,
            DeviceOTADebugConfig.class,
            VipService.class,
            DeviceAiSettingsService.class,
            NotificationService.class,
            MessageNotificationSettingsDao.class,
            MessageNotificationSettingsService.class,
            DeviceModelEventService.class,
            DeviceSettingConfig.class,
            DoorBellRingConfig.DoorBellRingKeyConfig.class,
            DeviceAttributeService.class,
            DeviceEnumMappingService.class,
            DeviceModelConfigService.class,
            DeviceModelNoUtil.class,
            SupportMagicPixConfig.class,
            DeviceSettingService.class,
            DeviceApplicationConfig.class,
            DeviceDebugConfig.class,
            DynamicModelConfigV2Service.class,
            DynamicModelConfigService.class,
            WssReportEventServiceImpl.class,
            SnowPlowManager.class,
            StatemachineEventSender.class,
            ReqToIotLocalService.class
    );

}
