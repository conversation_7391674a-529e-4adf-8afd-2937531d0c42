package com.addx.iotcamera.controller.device.operation;

import com.addx.iotcamera.bean.domain.device.DeviceReportEventDO;
import com.addx.iotcamera.bean.domain.device.http.DeviceCallRequest;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.service.video.VideoGenerateService;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController @Lazy
@RequestMapping("/device")
public class DeviceCallController {

    @Autowired @Lazy
    private VideoGenerateService videoGenerateService;

    /**
     * 设备主动发起打电话
     */
    @LogRequestAndResponse
    // @Timed
    @PostMapping(value = "/call", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result deviceCall(@RequestBody DeviceCallRequest deviceCallRequest, HttpServletRequest httpServletRequest) {
        DeviceReportEventDO eventDO = new DeviceReportEventDO();
        eventDO.setValue(eventDO.new ReportEventRequestValue());
        eventDO.getValue().setEvent(EReportEvent.DEVICE_CALL.getEventId());
        eventDO.setSerialNumber(deviceCallRequest.getSerialNumber());
        eventDO.setTime(System.currentTimeMillis());
        eventDO.setName("reportEvent");
        eventDO.setId(0);
//        videoGenerateService.sendVideoMsg(VideoMsgType.REPORT_EVENT, deviceCallRequest.getTraceId(), eventDO);
        videoGenerateService.handleReportEvent(eventDO);
        return Result.Success();
    }
}
