package com.addx.iotcamera.controller.user;

import com.addx.iotcamera.annotation.LoginUserToken;
import com.addx.iotcamera.bean.app.AppFormOptionsRequest;
import com.addx.iotcamera.bean.app.UpdatePasswordRequest;
import com.addx.iotcamera.bean.app.UserRequest;
import com.addx.iotcamera.bean.app.user.LoginRequest;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.dao.IPushDAO;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.util.LogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.param.AppRequestBase;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.addx.iotcamera.constants.ReportLogConstants.REPORT_TYPE_APP_RECEIVE_MSG;

@RestController @Lazy
@RequestMapping("/user")
public class UserController {
    private static Logger LOGGER = LoggerFactory.getLogger(UserController.class);

    @SuppressWarnings("all")
    @Autowired @Lazy
    private IUserDAO userDAO;

    @SuppressWarnings("all")
    @Autowired @Lazy
    private JwtHelper jwtHelper;

    @SuppressWarnings("all")
    @Autowired @Lazy
    private IPushDAO pushDAO;

    @Autowired @Lazy
    private UserService userService;
    @Autowired @Lazy
    private AppFormOptionsService appFormOptionsService;

    @Autowired @Lazy
    private UserRoleService userRoleService;

    @Autowired @Lazy
    private PushService pushService;

    @Autowired @Lazy
    private TenantTierConfig tenantTierConfig;

    @Resource @Lazy
    private ReportLogService reportLogService;

    @LogRequestAndResponse
    @PostMapping(value = "/updatename", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateName(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody UserRequest request,
                             HttpServletRequest httpServletRequest) {
        User user = JSON.parseObject(JSON.toJSONString(request), User.class);


        LOGGER.info(String.format("Updating user name to %s for user: %d", request.getName(), userId));

        user.setId(userId);
        return userService.updateUserName(user);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/syncUserInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result syncUserInfo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                               @RequestBody UserRequest request,
                                  HttpServletRequest httpServletRequest) {

        User user = new User();
        user.setId(userId);
        user.setName(request.getName());
        user.setEmail(request.getEmail());
        userService.updateUserById(user);
        return Result.Success();
    }


    @LogRequestAndResponse
    @PostMapping(value = "/getname", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getUserNamePost(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase request,
                                  HttpServletRequest httpServletRequest) {


        return Result.KVResult("name", userService.getUserName(userId));
    }

    @LogRequestAndResponse
    @PostMapping(value = "/setusertoken", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result setUserToken(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LoginRequest request,
                               HttpServletRequest httpServletRequest) {

        request.setId(userId);

        LOGGER.info(String.format("Setting user token for user: %d", userId));

        PushInfo pushInfo = new PushInfo();
        pushInfo.setAppType(request.getApp().getAppType());
        pushInfo.setBundleName(request.getApp().getBundle());
        pushInfo.setUserId(request.getId());
//        if (request.getMsgType() != null && request.getMsgToken() != null) {
//            pushInfo.setMsgType(request.getMsgType());
//            pushInfo.setMsgToken(request.getMsgToken());
//        }
//        pushInfo.setIosVoipToken(request.getIosVoipToken());

        userService.updateUserAppInfo(userId,request.getApp().getAppType(),request.getApp().getVersion(),request.getApp().getVersionName());
        return Result.SqlOperationResult(pushService.setPushInfo(pushInfo));
    }

    @LogRequestAndResponse
    @PostMapping(value = "/updatepassword", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updatePassword(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody UpdatePasswordRequest request,
                                 HttpServletRequest httpServletRequest) {


        LOGGER.info("Update password for user with ID: " + userId);

        return userService.updatePassword(userId, request);
    }


    @LogRequestAndResponse
    @PostMapping(value = "/updateLanguage", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateLanguage(@LoginUserToken UserToken userToken, @RequestBody UserRequest request,
                                 HttpServletRequest httpServletRequest) {
        User user = new User();
        user.setId(userToken.getUserId());
        user.setLanguage(request.getTargetLanguage());
        user.setTenantId(request.getApp().getTenantId());
        return userService.updateUserLanguage(user);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/getaccountinfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getAccountInfo(@RequestHeader(value = "Authorization") String authStr,
                                 @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                 HttpServletRequest httpServletRequest) {
        LOGGER.info("Get account info for user : {}", userId);

        LoginResponseDO responseDO = userService.getUserBasicInfo(userId);
        responseDO.setToken(new HttpTokenDO());
        responseDO.getToken().setTokenType(authStr.split(" ")[0]);
        responseDO.getToken().setToken(authStr);

        return new Result(responseDO);
    }

    /**
     * 用户注销
     *
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/cancellation", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result cancellation(@LoginUserToken UserToken userToken,
                               HttpServletRequest httpServletRequest) {
        userService.cancellationUser(userToken.getUserId());
        return Result.Success();
    }

    /**
     * app上报接受到Push
     * @param userToken
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/reportReceivePush", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getAccountInfo(@LoginUserToken UserToken userToken, @RequestBody JSONObject request,
                                 HttpServletRequest httpServletRequest) {

        long pushTime = request.getLong("timestamp");
        long receiveTime = System.currentTimeMillis() / 1000;

        request.put("pushTime", pushTime);
        request.put("msgPushToMsgReceiveTimeTook", receiveTime - pushTime);
        request.put("userId", userToken.getUserId());
        request.put("apiReceiveTime", receiveTime);
        reportLogService.appReportMsgReceive(REPORT_TYPE_APP_RECEIVE_MSG, request);
        return Result.Success();
    }

    /**
     * 查询 alexa account link 结果
     *
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/accountlinkedplatforms", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result queryAccountLinkResult(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase request) {
        List<String> accountLinkedPlatformList = new LinkedList<>();

        return Result.ListResult(accountLinkedPlatformList);
    }

    /**
     * 查询user的tenantName
     *
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/querytenantnamebyuserId", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result queryTenantNameByUserId(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId) {
        String tenantId = userService.queryTenantIdById(userId);
        String tenantName = tenantTierConfig.getConfig().containsKey(tenantId) ? tenantTierConfig.getConfig().get(tenantId) : tenantId;
        Map resultMap = new HashMap();
        resultMap.put("tenantId", tenantId);
        resultMap.put("tenantName", tenantName);
        return new Result(resultMap);
    }

    /**
     * 获取app填写表单选型
     *
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/getFormOptions", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getFormOptions(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppFormOptionsRequest appFormOptionsRequest) {
        AppFormOptionsDO appFormOptionsDO = null;
        try {
            appFormOptionsDO = appFormOptionsService.getAppFormOptions(userId, appFormOptionsRequest.getSerialNumber());
        } catch (Throwable e) {
            LogUtil.error(LOGGER, "getAppFormOptions error! userId={},sn={}", userId, appFormOptionsRequest.getSerialNumber(), e);
        }
        return new Result(appFormOptionsDO);
    }
}
