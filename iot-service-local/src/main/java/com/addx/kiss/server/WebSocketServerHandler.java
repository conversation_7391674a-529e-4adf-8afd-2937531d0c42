package com.addx.kiss.server;

import com.addx.iotcamera.event.SnowPlowManager;
import com.addx.kiss.peer.longconn.*;
import com.addx.kiss.service.IotService;
import com.addx.kiss.utils.NettyUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.handler.codec.http.*;
import io.netty.handler.codec.http.websocketx.*;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.util.CharsetUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.SpringContextUtil;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.addx.kiss.peer.longconn.ClientManager.buildChannelId;
import static com.addx.kiss.peer.longconn.ClientMsgMethod.AUTH;
import static io.netty.handler.codec.http.HttpUtil.isKeepAlive;
import static io.netty.handler.codec.http.HttpUtil.setContentLength;

@Slf4j
public class WebSocketServerHandler extends SimpleChannelInboundHandler<Object> {

    private static final long READ_IDLE_TIMEOUT = 30_000L; // 毫秒

    public String getRequestId() {
        return channelId;
    }

    private WebSocketServerHandshaker handshaker;
    private final IotService iotService;

    private String channelId = "";
    @Getter
    private static volatile ServerStatus serverStatus;
    @Getter
    private ClientMsgHandlerInterface clientMsgHandler;
    @Getter
    private Client client; // 当前websocket连接的客户端

    public static void setServerStatus(ServerStatus serverStatus) {
        log.info("setServerStatus oldVal={},newVal={}", WebSocketServerHandler.serverStatus, serverStatus);
        WebSocketServerHandler.serverStatus = serverStatus;
    }

    public WebSocketServerHandler(IotService iotService) {
        this.iotService = iotService;
        this.clientMsgHandler = new ClientMsgHandler(ClientManager.getInstance(), iotService::getWebrtcConfig, SnowPlowManager.getInstance());
    }

    private static final Cache<String, Long> clientId2LastUpdateTime = CacheBuilder.newBuilder()
            .expireAfterWrite(2, TimeUnit.HOURS)
            .build();

    public static long getLastUpdateTime(String clientId) {
        return Optional.ofNullable(clientId2LastUpdateTime.getIfPresent(clientId)).orElse(-1L);
    }

    public static void putLastUpdateTime(String clientId, long time) {
        try {
            clientId2LastUpdateTime.put(clientId, time);
        } catch (Throwable e) {
            log.error("putLastUpdateTime error! clientId={},time={}", clientId, time, e);
        }
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, Object msg) throws Exception {
        WebsocketServer.channelGroup.add(ctx.channel());
        putToMDC();
        try {
            clientMsgHandler.updateLastReadTime();
            if (client != null && client.isAuth()) {
                putLastUpdateTime(client.getId(), clientMsgHandler.getLastReadTime());
            }
            // 每个ws连接分配一个延时任务，可能会有性能问题。改为在 空闲超时 和 接收消息时 判断
            try {
                if (client != null && !client.isAuth()) {
                    final long timeout = clientMsgHandler.getLastReadTime() - client.getConnectTime();
                    if (timeout >= ClientMsgHandler.AUTH_TIMEOUT) {
                        log.info("client auth timeout! client={},timeout={}", client, timeout);
                        client.sender().sendResponseAndClose(AUTH, 402, "鉴权超时，断开连接!");
                        com.addx.iotcamera.event.SnowPlowManager.catchSnowPlowError(() -> com.addx.iotcamera.event.SnowPlowManager.catchSnowPlowError(() -> iotService.reportDeviceExceptionTracking( "auth_timeout", client.getId())));
                        return;
                    }
                }
            } catch (Throwable e) {
                com.addx.iotcamera.event.SnowPlowManager.catchSnowPlowError(() -> iotService.reportDeviceExceptionTracking( "auth_error", client.getId()));
                log.error("client auth timout check error! cause={}", ExceptionUtils.getStackTrace(e));
            }
//        log.info("1.读取到消息 msg={},msgCls={},channel={}",  msg, msg.getClass(), ctx.channel());
            // 以http请求形式接入，但是走的是websocket
            if (msg instanceof FullHttpRequest) {
                handleHttpRequest(ctx, (FullHttpRequest) msg);
            }
            // 处理websocket客户端的消息
            else if (msg instanceof WebSocketFrame) {
                handleWebsocketFrame(ctx, (WebSocketFrame) msg);
            }
        } catch (Throwable e) {
            log.error("channelRead0 error!", e);
        } finally {
            MDC.clear();
        }
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        putToMDC();
        try {
            channelId = buildChannelId(ctx);
            if (!SpringContextUtil.prepared()) {
                log.info("0.客户端加入连接 springContext未准备好 拒绝新连接 channel={}",  ctx.channel());
            } else if (serverStatus == ServerStatus.CLOSING) { // 服务器关闭中，拒绝所有新连接
                log.info("0.客户端加入连接 服务器拒绝新连接 channel={}",  ctx.channel());
                new ClientSenderImpl(ctx).close(ConnCloseReason.redeploy);
            } else {
                log.info("0.客户端加入连接 channel={}",  ctx.channel());
                com.addx.iotcamera.event.SnowPlowManager.catchSnowPlowError(() -> iotService.reportDeviceExceptionTracking( "client_connect", ctx.channel().id().toString()));
            }
        } catch (Throwable e) {
            log.error("channelActive error!", e);
        } finally {
            MDC.clear();
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        putToMDC();
        try {
            log.info("2.客户端断开连接 channel={}",  ctx.channel());
            com.addx.iotcamera.event.SnowPlowManager.catchSnowPlowError(() -> iotService.reportDeviceExceptionTracking( "client_disconnect ", ctx.channel().id().toString()));

            if (client != null) {
                clientMsgHandler.onDisConnect(client);
            }
        } catch (Throwable e) {
            log.error("channelInactive error!", e);
        } finally {
            MDC.clear();
        }
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
        ctx.flush();
    }

    /**
     * 唯一的一次http请求，用于创建websocket
     */
    private void handleHttpRequest(ChannelHandlerContext ctx, FullHttpRequest req) {
        if (!req.decoderResult().isSuccess()){
            sendHttpResponse(ctx, req, HttpResponseStatus.BAD_REQUEST, null);
            return;
        }

        //要求Upgrade为websocket，过滤掉get/Post
        if ((!"websocket".equals(req.headers().get("Upgrade")))) {
            //若不是websocket方式，则创建BAD_REQUEST的req，返回给客户端
            sendHttpResponse(ctx, req, HttpResponseStatus.BAD_REQUEST, null);
            return;
        }
        String websocketURL = "ws://" + req.headers().get(HttpHeaderNames.HOST) + req.uri();
        WebSocketServerHandshakerFactory wsFactory = new WebSocketServerHandshakerFactory(
                websocketURL, null, false);
        handshaker = wsFactory.newHandshaker(req);
        if (handshaker == null) {
            log.info("handleHttpRequest 握手失败 uri={}",  req.uri());
            WebSocketServerHandshakerFactory.sendUnsupportedVersionResponse(ctx.channel());
            return;
        }

        if ("/iot-channel/connect".equals(req.uri())) {
            log.info("handleHttpRequest wss长链接接入 uri={}",  req.uri());
            // 新的websocket handshake逻辑
            handshake(ctx.channel(), req, () -> {
                client = ClientManager.getInstance().createClient(ctx);
                clientMsgHandler.onConnect(client);
            });

        } else if ("/bx-channel/connect".equals(req.uri())) {
            log.info("handleHttpRequest wss长链接接入 bx uri={}",  req.uri());
            // 新的websocket handshake逻辑
            handshake(ctx.channel(), req, () -> {
                final ClientManager bxClientManager = ClientManager.getBxInstance();
                clientMsgHandler = new BxMsgHandler(bxClientManager, iotService::getWebrtcConfig);
                client = bxClientManager.createBxClient(ctx);
                clientMsgHandler.onConnect(client);
            });

        } else {
            sendHttpResponse(ctx, req, HttpResponseStatus.BAD_REQUEST, "不支持的uri");
            log.info("handleHttpRequest 不支持的uri uri={}",  req.uri());
        }
    }

    private void handshake(Channel channel, FullHttpRequest req, Runnable afterHandshake) {
        handshaker.handshake(channel, req).addListener(Void -> {
            log.info("handleHttpRequest handshake end! uri={}",  req.uri());
            try {
                afterHandshake.run();
            } catch (Throwable e) {
                log.error("clientMsgHandler after handshake error! channelId={},error={}",  ExceptionUtils.getStackTrace(e));
            }
        });
    }

    private void handleWebsocketFrame(ChannelHandlerContext ctx, WebSocketFrame frame) {
        // 判断是否关闭链路的指令
        if (frame instanceof CloseWebSocketFrame) {
            handshaker.close(ctx.channel(), (CloseWebSocketFrame) frame.retain());
            return;
        }
        // 判断是否ping消息
        if (frame instanceof PingWebSocketFrame) {
            ctx.channel().write(new PongWebSocketFrame(frame.content().retain()));
            return;
        }
        // 本例程仅支持文本消息，不支持二进制消息
        if (frame instanceof TextWebSocketFrame) {
            if (client != null) {
                // 新的websocket 消息处理逻辑
                final String payload = ((TextWebSocketFrame) frame).text();
                clientMsgHandler.onMessage(client, payload);
            }
        }
    }

    public static void sendHttpResponse(ChannelHandlerContext ctx, FullHttpRequest req, HttpResponseStatus respStatus, String body) {
        DefaultFullHttpResponse res = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, respStatus);
        boolean isOk = !HttpResponseStatus.OK.equals(res.status());
        if (body != null) {
            ByteBuf buf = Unpooled.copiedBuffer(body, CharsetUtil.UTF_8);
            res.content().writeBytes(buf);
            buf.release();
        }
        setContentLength(res, res.content().readableBytes());
        ChannelFuture f = ctx.channel().writeAndFlush(res);
        // 如果是非Keep-Alive，关闭连接
        if (!isKeepAlive(req) || !isOk) {
            f.addListener(ChannelFutureListener.CLOSE);
        }
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        putToMDC();
        try {
//        super.userEventTriggered(ctx, evt);
            if (evt instanceof IdleStateEvent) {
                IdleStateEvent event = (IdleStateEvent) evt;
                if (event.state() == IdleState.READER_IDLE) {
                    if (clientMsgHandler != null) {
                        long lastReadTime = clientMsgHandler.getLastReadTime();
                        long idleTime = System.currentTimeMillis() - lastReadTime;
                        log.info("readIdle isFirst={},lastReadTime={},idleTime={}",  event.isFirst(), lastReadTime, idleTime);
                        if (idleTime >= clientMsgHandler.getReadIdleTimeout(client)) {
                            log.info("2.服务端断开连接 channel={},idleTime={}",  ctx.channel(), idleTime);
                            com.addx.iotcamera.event.SnowPlowManager.catchSnowPlowError(() -> iotService.reportDeviceExceptionTracking( "server_disconnect ", ctx.channel().id().toString()));

                            NettyUtil.close(ctx, "client idle timeout");
                        }
                    }
                } else {
                    NettyUtil.close(ctx, "tcp idle timeout");
                }
            } else if (evt instanceof WebSocketServerProtocolHandler.HandshakeComplete) {
                // 配置了WebSocketServerProtocolHandler后，对应路径的HttpRequest不会传递到当前Handler中，需要在userEventTriggered中处理wss握手成功的消息
                WebSocketServerProtocolHandler.HandshakeComplete event = (WebSocketServerProtocolHandler.HandshakeComplete) evt;
                log.info("handleHttpRequest handshake end! uri={}", event.requestUri());
                if ("/iot-channel/connect".equals(event.requestUri())) {
                    client = ClientManager.getInstance().createClient(ctx);
                    clientMsgHandler.onConnect(client);
                } else if ("/bx-channel/connect".equals(event.requestUri())) {
                    final ClientManager bxClientManager = ClientManager.getBxInstance();
                    clientMsgHandler = new BxMsgHandler(bxClientManager, iotService::getWebrtcConfig);
                    client = bxClientManager.createBxClient(ctx);
                    clientMsgHandler.onConnect(client);
                } else {
                    log.info("handleHttpRequest handshake 不支持的uri uri={}", event.requestUri());
                    NettyUtil.close(ctx, "uri not supported");
                }
            }
        } catch (Throwable e) {
            log.error("userEventTriggered error! evt={}", JSON.toJSONString(evt), e);
        } finally {
            MDC.clear();
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        putToMDC();
        log.error("exceptionCaught channel={},client={}",  ctx.channel(), client, cause);
        MDC.clear();
    }

    public void putToMDC() {
        MDC.put("requestId", getRequestId());
        if (client != null && client.getId() != null) {
            MDC.put("clientId", client.getId());
        }
    }
    
}
