package com.addx.kiss.service.impl;

import com.addx.kiss.bean.Pair;
import com.addx.kiss.bean.domain.DeviceCache;
import com.addx.kiss.bean.response.ResponseResult;
import com.addx.kiss.enums.EProtocol;
import com.addx.kiss.peer.longconn.Client;
import com.addx.kiss.peer.longconn.ClientManager;
import com.addx.kiss.peer.longconn.ClientMsgMethod;
import com.addx.kiss.server.DeviceCacheManager;
import com.addx.kiss.service.IKissService;
import com.addx.iotcamera.statemachine.StatemachineEventSender;
import com.addx.kiss.utils.LiveLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.socket.DatagramPacket;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import java.net.InetSocketAddress;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.addx.kiss.peer.longconn.ClientMsgMethod.WAKE_UP;

@Slf4j
//@Service @Lazy // todo 合并到iot-service的kissService中
public class KissServiceImpl implements IKissService {
    @Resource @Lazy
    private ScheduledThreadPoolExecutor wakeupScheduledExecutor;

    private static final int[] delayMils = {0, 500, 1000, 2000};
    public static final Map<String, Pair<Long, Thread>> wakeupThreadMap = new HashMap<>();

    @Override
    public void preSleep(String sn, String secret) {
        DeviceCache newCache = new DeviceCache().setSecret(secret);
        DeviceCacheManager.putCache(sn, newCache);
    }

    @Override
    public void wakeup(String sn, String wakeup, String traceId) {
        final Client client = ClientManager.getInstance().getGroupMaster(sn);
        if (client != null && !client.isOldVersion()) { // wss的唤醒
            doWakeupByWss(client, sn, wakeup, traceId);
            return;
        }
        DeviceCache cache = DeviceCacheManager.getCache(sn);
        if (cache.getChannel() != null && cache.getChannel().isActive()) {
            doWakeup(cache, sn, wakeup, traceId);
        } else {
            log.warn("服务器上无设备有效的heartbeat连接 {}", sn);
        }
    }
    @Override
    public void sendReportEventResponse(String sn, String responseBody) {
        DeviceCache cache = DeviceCacheManager.getCache(sn);
        if (EProtocol.WSS.getValue() == cache.getProtocol()) {
            final Client client = ClientManager.getInstance().getClient(cache.getChannelId());
            boolean hasClient = false, isAuth = false, isActive = false;
            if (cache.getChannelId() != null && (hasClient = client != null)
                    && (isAuth = client.isAuth()) && (isActive = client.getSender().isActive())) {
                client.getSender().sendResponse(ClientMsgMethod.REPORT_EVENT, 0, responseBody);
            } else {
                log.warn("服务器上无设备有效的wss连接 sn={},channelId={},hasClient={},isAuth={},isActive={}", sn, cache.getChannelId(), hasClient, isAuth, isActive);
            }
        } else if (cache.getChannel() != null && cache.getChannel().isActive()) {
           cache.getChannel().writeAndFlush(new TextWebSocketFrame(responseBody));
        } else {
            log.warn("服务器上无设备有效的websocket连接 {}", sn);
        }
    }

    private void doWakeupByWss(Client client, String sn, String wakeup, String traceId) {
        LiveLogUtil.setLocalLiveInfo(traceId, sn);

        if (client != null && client.isAuth() && client.getSender().isActive()) {
            LiveLogUtil.log(traceId, "sendWssWakeupToDevice");
            wakeupScheduledExecutor.schedule(() -> {
                client.getSender().sendCommand(WAKE_UP, traceId, "iot-service", wakeup);
            }, 0, TimeUnit.MILLISECONDS);
        } else {
            log.warn("服务器上无设备有效的wss连接 sn={},client={}", sn, JSON.toJSONString(client));
        }
        LiveLogUtil.cleanLocalInfo();
    }

    public void doWakeup(DeviceCache cache, String sn, String wakeup, String traceId) {
        LiveLogUtil.setLocalLiveInfo(traceId, sn);

        // tcp的唤醒
        if (EProtocol.TCP.getValue() == cache.getProtocol()) {
            LiveLogUtil.log(traceId, "sendTcpWakeupToDevice");
            wakeupScheduledExecutor.schedule(() -> sendTcpWakeupToDevice(cache, sn, wakeup, traceId), 0, TimeUnit.MILLISECONDS);
            return;
        }
        // udp的唤醒
        // 如果是直播要求的wakeup，那么发送一个带有直播信息的udp
        if (!StringUtils.isEmpty(traceId)) {
            String udpLiveIdMessage = "udpLiveId:" + traceId + " serialNumber:" + sn;
            LiveLogUtil.log(traceId, "sendUdpWakeupToDevice udpLiveIdMessage:{}", LiveLogUtil.getNoQuotationStr(udpLiveIdMessage));
            wakeupScheduledExecutor.schedule(() -> sendUdpWakeupToDevice(cache, sn, udpLiveIdMessage, traceId), 0, TimeUnit.MILLISECONDS);
        }

        // 将发送任务添加到delayQueue中sendPeerResponse
        for (int delay : delayMils) {
            LiveLogUtil.log(traceId, "sendUdpWakeupToDevice delay:{}", String.valueOf(delay));
            wakeupScheduledExecutor.schedule(() -> sendUdpWakeupToDevice(cache, sn, wakeup, traceId), delay, TimeUnit.MILLISECONDS);
        }

        LiveLogUtil.cleanLocalInfo();
    }

    /**
     * 发送udp wakeup到设备s
     * @param sn
     * @param message
     */
    private static void sendUdpWakeupToDevice(DeviceCache cache, String sn, String message, String traceId) {
        Channel channel = cache.getChannel();
        InetSocketAddress addr = cache.getUdpSocketAddr();

        // 记录下线程开始工作的时间点, key为SN + threadID
        String key = "udp:" + sn + ":" + Thread.currentThread().getId();
        wakeupThreadMap.put(key, new Pair<>(Instant.now().toEpochMilli(), Thread.currentThread()));
        try {
            if (channel != null && addr != null) {
                channel.writeAndFlush(new DatagramPacket(Unpooled.copiedBuffer(message, CharsetUtil.UTF_8), addr));
                log.info("deviceTrack {} send kiss udp {}", sn, message);
                StatemachineEventSender.send(sn, "kissSendWakeup", "");

            } else {
                log.warn("sending udp can't get context from sn: {}", sn);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            wakeupThreadMap.remove(key);
        }
    }

    /**
     * 发送tcp wakeup到设备
     * @param sn
     * @param message
     */
    private static void sendTcpWakeupToDevice(DeviceCache cache, String sn, String message, String traceId) {
        Channel channel = cache.getChannel();

        // 记录下线程开始工作的时间点, key为SN + threadID
        String key = "tcp:" + sn + ":" + Thread.currentThread().getId();
        wakeupThreadMap.put(key, new Pair<>(Instant.now().toEpochMilli(), Thread.currentThread()));
        try {
            if (channel != null) {
                channel.writeAndFlush(message);
                log.info("deviceTrack {} send kiss tcp {}", sn, message);
                StatemachineEventSender.send(sn, "kissSendWakeup", "");

            } else {
                log.warn("sending tcp can't get context from sn: {}", sn);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            wakeupThreadMap.remove(key);
        }
    }

    @Override
    public String getDeviceStatus(String sn) {
        final Client client = ClientManager.getInstance().getGroupMaster(sn);
        if (client != null && !client.isOldVersion()) { // wss的唤醒
            return client.getStatus().name();
        }
        return Client.Status.offline.name();
    }

    @Override
    public ResponseResult sendMessageToDevice(String sn, JSONObject payload) {
        final Client client = ClientManager.getInstance().getGroupMaster(sn);
        if (client != null && !client.isOldVersion()) {
            client.sender().sendMessage(payload.toJSONString());
            return ResponseResult.success();
        } else {
            return ResponseResult.builder().code(404).msg("device offline").build();
        }
    }
}
