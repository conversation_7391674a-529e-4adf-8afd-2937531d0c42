package org.addx.iot.domain.extension.core;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.exception.BaseException;
import org.addx.iot.domain.extension.dao.IExtensionDAO;
import org.addx.iot.domain.extension.dao.IExtensionEnabledCameraDAO;
import org.addx.iot.domain.extension.entity.ExtensionBean;
import org.addx.iot.domain.extension.entity.ExtensionEnabledCamera;
import org.addx.iot.domain.extension.param.ExtensionInstallRequest;
import org.addx.iot.domain.extension.param.ExtensionNameRequest;
import org.addx.iot.domain.extension.vo.CameraInfoVO;
import org.addx.iot.domain.extension.vo.DeviceInfoVO;
import org.addx.iot.domain.extension.vo.ExtensionStatusVO;
import org.addx.iot.domain.extension.vo.HubAndCameraInfoVO;
import org.addx.iot.domain.user.service.IUserService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static org.addx.iot.common.enums.ResultCollection.*;

@Slf4j
@Data
public abstract class ExtensionService implements IExtensionService {

    private Consumer<Integer> messageToAppConsumer;
    private Consumer<String> deviceSupportUploadConsumer;
    private IExtensionManager extensionManager;
    private IUserService userService;
    private IExtensionDAO extensionDAO;
    private IExtensionEnabledCameraDAO extensionEnabledCameraDAO;

    private final Executor executor = Executors.newFixedThreadPool(15);

    public ExtensionService(IExtensionManager extensionManager,
                            Consumer<Integer> messageToAppConsumer,
                            Consumer<String> deviceSupportUploadConsumer,
                            IUserService userService,
                            IExtensionDAO extensionDAO,
                            IExtensionEnabledCameraDAO extensionEnabledCameraDAO) {
        this.messageToAppConsumer = messageToAppConsumer;
        this.deviceSupportUploadConsumer = deviceSupportUploadConsumer;
        this.extensionManager = extensionManager;
        this.userService = userService;
        this.extensionDAO = extensionDAO;
        this.extensionEnabledCameraDAO = extensionEnabledCameraDAO;
    }

    /**
     * 安装/修改插件
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer installOrUpdateExtension(ExtensionInstallRequest extensionInstallRequest) {
        IExtension extension = extensionManager.getExtensions().get(extensionInstallRequest.getExtensionName());
        if (extension == null) {
            log.warn("ExtensionManager extension {} can not be null", extensionInstallRequest.getExtensionName());
            throw new BaseException(EXTENSION_NOT_SUPPORT, "Unknown extension");
        }

        // 判断相机是否属于该用户
        List<CameraInfoVO> cameraInfoVOS = extensionEnabledCameraDAO.selectAllCamera(extensionInstallRequest.getAdminUserId());
        Set<String> serialNumbers = cameraInfoVOS.stream().map(CameraInfoVO::getSerialNumber).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(extensionInstallRequest.getSerialNumbers())) {
            List<String> toAddSerialNumbers = extensionInstallRequest.getSerialNumbers();
            if (!serialNumbers.containsAll(toAddSerialNumbers)) {
                throw new BaseException(EXTENSION_INSTALL_FAILED, "camera:" + toAddSerialNumbers + " not belong to user:" + extensionInstallRequest.getAdminUserId());
            }
        }

        // 判断用户的相机是否支持插件
        extensionInstallRequest.getSerialNumbers().forEach((serialNumber) -> {
            if (!extension.cameraIsSupport(serialNumber)) {
                throw new BaseException(EXTENSION_INSTALL_FAILED, "camera:" + serialNumber + " not support this extension");
            }
        });
        if (extensionInstallRequest.getExtensionId() == null) {
            return installExtension(extension, extensionInstallRequest);
        } else {
            return updateExtensionCamera(extension, extensionInstallRequest);
        }
    }

    /**
     * 安装绑定时默认需要安装的插件
     */
    @Transactional(rollbackFor = Exception.class)
    public void installDefaultExtensionWhenBinding(Integer adminUserId) {
        if (adminUserId == null)
            throw new BaseException(EXTENSION_INSTALL_FAILED, "Unknown user");
        for (IExtension extension : extensionManager.getExtensions().values()) {
            if (extension.isDefaultInstallWhenBinding()) {
                ExtensionBean extensionBean = extensionDAO.selectExtension(adminUserId, extension.getName());
                // 用户手动卸载的，无需再次默认安装
                if (extensionBean != null) continue;
                ExtensionInstallRequest extensionInstallRequest = new ExtensionInstallRequest();
                extensionInstallRequest.setExtensionName(extension.getName());
                extensionInstallRequest.setAdminUserId(adminUserId).setUserId(adminUserId);
                installOrUpdateExtension(extensionInstallRequest);
            }
        }
    }

    /**
     * 删除插件启用的相机，注意，该方法调用的时机一定要在installDefaultExtensionWhenBinding之前
     * 如果A用户在绑定完相机后启用插件，后不经过手动解绑直接reset，接着B用户直接绑定该相机
     * 则需要清理extension_enabled_camera表中该相机的记录
     */
    @Override
    public void clearExtensionEnabledCameraWhenBinding(String serialNumber) {
        extensionEnabledCameraDAO.deleteBySerialNumber(serialNumber);
    }

    /**
     * 安装插件
     */
    private Integer installExtension(IExtension extension, ExtensionInstallRequest extensionInstallRequest) {
        if (extensionInstallRequest == null || extensionInstallRequest.getExtensionId() != null) {
            throw new BaseException("param error");
        }
        Integer installedExtensionId = extensionDAO.isInstalled(extensionInstallRequest.getAdminUserId(), extensionInstallRequest.getExtensionName());
        if (installedExtensionId != null && installedExtensionId == 1) return installedExtensionId;
        ExtensionBean extensionBean = new ExtensionBean()
                .setCreateTime(System.currentTimeMillis())
                .setUpdateTime(System.currentTimeMillis())
                .setAdminUserId(extensionInstallRequest.getAdminUserId())
                .setName(extensionInstallRequest.getExtensionName())
                .setInstalled(1);
        extensionDAO.deleteByAdminUserIdAndExtensionName(extensionInstallRequest.getAdminUserId(), extensionInstallRequest.getExtensionName());
        extensionDAO.install(extensionBean);
        Integer extensionId = extensionBean.getId();
        if (extensionId == null) {
            throw new BaseException(EXTENSION_INSTALL_FAILED, "Extension install failed");
        }
        batchAddExtensionCamera(extensionInstallRequest.getAdminUserId(),
                extensionInstallRequest.getExtensionName(), extensionInstallRequest.getSerialNumbers());
        // setup和afterInit
        extension.setup(extensionInstallRequest.getSerialNumbers());
        if (messageToAppConsumer == null) {
            log.info("ExtensionManager installExtension messageToAppConsumer is null");
            return extensionId;
        }
        messageToAppConsumer.accept(extensionInstallRequest.getAdminUserId());
        return extensionId;
    }

    /**
     * 卸载插件
     */
    @DSTransactional
    public void uninstallExtension(ExtensionNameRequest extensionNameRequest) {
        if (extensionNameRequest == null) {
            throw new BaseException(EXTENSION_NOT_SUPPORT, "Unknown extension");
        }
        IExtension extension = extensionManager.getExtensions().get(extensionNameRequest.getExtensionName());
        if (extension == null) {
            log.warn("ExtensionManager extension {} is null", extensionNameRequest.getExtensionName());
            throw new BaseException(EXTENSION_NOT_SUPPORT, "Unknown extension");
        }
        Integer extensionId = extensionDAO.selectInstalledExtensionId(extensionNameRequest.getAdminUserId(), extensionNameRequest.getExtensionName());
        if (extensionId == null) {
            throw new BaseException(EXTENSION_NOT_INSTALL, "Extension not installed");
        }
        List<CameraInfoVO> cameraInfoVOS = extensionEnabledCameraDAO.selectCameraWithExtension(extensionId);
        List<String> serialNumbers = new ArrayList<>();
        if (!CollectionUtils.isEmpty(cameraInfoVOS)) {
            serialNumbers = cameraInfoVOS.stream().map(DeviceInfoVO::getSerialNumber).collect(Collectors.toList());
        }
        extension.tearDown(serialNumbers);
        extensionEnabledCameraDAO.deleteByExtensionId(extensionId);
        extensionDAO.uninstall(extensionNameRequest.getAdminUserId(), extensionNameRequest.getExtensionName());
        if (messageToAppConsumer == null) {
            log.info("ExtensionManager uninstallExtension messageToAppConsumer is null");
            return;
        }
        messageToAppConsumer.accept(extensionNameRequest.getAdminUserId());
    }

    /**
     * 卸载所有插件
     */
    @DSTransactional
    public void uninstallAllExtension(Integer adminUserId) {
        extensionManager.getInstalledExtensions(adminUserId).forEach((extension) -> {
            ExtensionNameRequest extensionNameRequest = new ExtensionNameRequest();
            extensionNameRequest.setAdminUserId(adminUserId).setUserId(adminUserId);
            extensionNameRequest.setExtensionName(extension.getName());
            uninstallExtension(extensionNameRequest);
        });
    }

    /**
     * 针对单个相机的setup
     */
    @Transactional(rollbackFor = Exception.class)
    public void setupAllExtension(Integer adminUserId, String serialNumber) {
        extensionManager.getInstalledExtensions(adminUserId).forEach((extension) -> {
            if (!extension.cameraIsSupport(serialNumber)) {
                log.error("Extension setupOne camera is not support={}", serialNumber);
                return;
            }
            if (extension.getEnabledCameras(adminUserId).contains(serialNumber)) return;
            batchAddExtensionCamera(adminUserId, extension.getName(), Collections.singletonList(serialNumber));
            extension.setup(serialNumber);
        });
    }

    /**
     * 针对单个相机的teardown
     */
    @DSTransactional
    public void tearDownAllExtension(Integer adminUserId, String serialNumber) {
        extensionManager.getInstalledExtensions(adminUserId).forEach((extension) -> {
            if (!extension.getEnabledCameras(adminUserId).contains(serialNumber)) {
                log.info("Extension tearDownOne camera is not exist={}", serialNumber);
                return;
            }
            Integer extensionId = extensionDAO.selectInstalledExtensionId(adminUserId, extension.getName());
            if (extensionId == null) {
                log.error("Extension setupOne extension is not exist={}", extension.getName());
                return;
            }
            extensionEnabledCameraDAO.deleteByExtensionIdAndSerialNumber(extensionId, serialNumber);
            extension.tearDown(serialNumber);
        });
    }

    /**
     * 触发相机的deviceSupport更新
     */
    public void triggerDeviceUploadSupport(String serialNumber) {
        if (serialNumber == null) {
            log.error("ExtensionManager triggerDeviceUploadSupport serialNumber is null");
            return;
        }
        if (deviceSupportUploadConsumer == null) {
            log.error("ExtensionManager triggerDeviceUploadSupport deviceSupportUploadConsumer is null");
            return;
        }
        deviceSupportUploadConsumer.accept(serialNumber);
    }

    /**
     * 更新插件已启用的相机
     */
    private Integer updateExtensionCamera(IExtension extension, ExtensionInstallRequest extensionInstallRequest) {
        if (extensionInstallRequest == null || extensionInstallRequest.getExtensionId() == null) {
            throw new BaseException("param error");
        }
        ExtensionBean extensionBean = extensionDAO.selectExtension(extensionInstallRequest.getAdminUserId(), extensionInstallRequest.getExtensionName());
        if (extensionBean != null && !Objects.equals(extensionBean.getId(), extensionInstallRequest.getExtensionId()))
            throw new BaseException(EXTENSION_CANNOT_IDENTIFY_USER, "Extension not belong to user");
        extensionDAO.enableExtension(extensionInstallRequest.getExtensionId(), extensionInstallRequest.getAdminUserId());
        List<CameraInfoVO> cameraInfoVOS = extensionEnabledCameraDAO.selectCameraWithExtension(extensionInstallRequest.getExtensionId());
        if (!CollectionUtils.isEmpty(cameraInfoVOS)) {
            extension.tearDown(cameraInfoVOS.stream().map(CameraInfoVO::getSerialNumber).collect(Collectors.toList()));
        }
        extensionEnabledCameraDAO.deleteByExtensionId(extensionInstallRequest.getExtensionId());
        batchAddExtensionCamera(extensionInstallRequest.getAdminUserId(), extensionInstallRequest.getExtensionName(), extensionInstallRequest.getSerialNumbers());
        extension.setup(extensionInstallRequest.getSerialNumbers());
        return extensionInstallRequest.getExtensionId();
    }

    /**
     * 批量添加相机
     */
    private void batchAddExtensionCamera(Integer adminUserId, String extensionName, List<String> serialNumberList) {
        if (CollectionUtils.isEmpty(serialNumberList)) {
            log.warn("ExtensionManagerService batchAddExtensionCamera is empty");
            return;
        }
        Integer extensionId = extensionDAO.selectInstalledExtensionId(adminUserId, extensionName);
        if (extensionId == null) {
            log.error("ExtensionManagerService batchAddExtensionCamera extensionId is null");
            return;
        }
        List<ExtensionEnabledCamera> cameraList = serialNumberList.stream()
                // 排除不属于自己的设备
                .filter((e) -> checkUserSerialNumberPermission(adminUserId, e))
                .map((e) -> new ExtensionEnabledCamera()
                        .setExtensionId(extensionId).setSerialNumber(e)
                        .setCreateTime(System.currentTimeMillis())
                        .setUpdateTime(System.currentTimeMillis())
                )
                .collect(Collectors.toList());
        extensionEnabledCameraDAO.insertBatchExtensionEnabledCamera(cameraList);
    }


    /**
     * 查询插件状态
     */
    public List<ExtensionStatusVO> queryExtensionStatus(ExtensionNameRequest extensionNameRequest) {
        if (extensionNameRequest == null) {
            throw new BaseException(EXTENSION_NOT_SUPPORT, "Unknown extension");
        }
        List<Integer> adminUserIds = userService.queryAdminUserIdsByUserId(extensionNameRequest.getAdminUserId());
        if (!adminUserIds.contains(extensionNameRequest.getUserId())) {
            adminUserIds.add(extensionNameRequest.getUserId());
        }
        return adminUserIds.stream().map((adminUserId) -> {

            IExtension extension = extensionManager.getExtensions().get(extensionNameRequest.getExtensionName());
            if (extension == null) {
                return new ExtensionStatusVO()
                        .setUserId(extensionNameRequest.getUserId())
                        .setAdminUserId(adminUserId)
                        .setSupport(false)
                        .setEnabled(false)
                        .setExtensionId(null)
                        .setCameraInfoList(Collections.emptyList());
            }

            List<CameraInfoVO> cameraInfoVOS = extensionEnabledCameraDAO.selectAllCamera(adminUserId);
            cameraInfoVOS.forEach((e) -> e.setSupport(extension.cameraIsSupport(e.getSerialNumber())));
            // 防止第一次上报，服务端不支持，增加上报机会
            cameraInfoVOS.forEach((e) -> executor.execute(() -> triggerDeviceUploadSupport(e.getSerialNumber())));

            Integer extensionId = extensionDAO.selectInstalledExtensionId(adminUserId, extensionNameRequest.getExtensionName());
            return new ExtensionStatusVO()
                    .setUserId(extensionNameRequest.getUserId())
                    .setAdminUserId(adminUserId)
                    .setSupport(true)
                    .setEnabled(extensionId != null)
                    .setExtensionId(extensionId)
                    .setCameraInfoList(cameraInfoVOS);
        }).collect(Collectors.toList());
    }

    /**
     * 查询基站勾选的插件的相机列表（未勾选,勾选的都查询）
     */
    public List<HubAndCameraInfoVO> queryExtensionEnabledCamera(ExtensionNameRequest extensionNameRequest) {
        if (extensionNameRequest == null) {
            throw new BaseException(EXTENSION_NOT_SUPPORT, "Unknown extension");
        }
        IExtension extension = extensionManager.getExtensions().get(extensionNameRequest.getExtensionName());
        if (extension == null) throw new BaseException(EXTENSION_NOT_SUPPORT, "Unknown extension");
        List<Integer> adminUserIds = userService.queryAdminUserIdsByUserId(extensionNameRequest.getAdminUserId());
        adminUserIds.add(extensionNameRequest.getUserId());
        return adminUserIds.stream().map((adminUserId) -> {
            Integer extensionId = extensionDAO.selectInstalledExtensionId(adminUserId, extensionNameRequest.getExtensionName());
            if (extensionId == null) return null;
            List<CameraInfoVO> cameraInfoList = extensionEnabledCameraDAO.selectCameraWithExtension(extensionId);
            HubAndCameraInfoVO hubAndCameraInfo = new HubAndCameraInfoVO();
            hubAndCameraInfo
                    .setUserId(extensionNameRequest.getUserId())
                    .setAdminUserId(adminUserId)
                    .setExtensionId(extensionId)
                    .setHubInfo(extensionEnabledCameraDAO.selectHubInfo())
                    .setCameraInfoList(cameraInfoList);
            return hubAndCameraInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 检查用户是否有权限操作该相机
     */
    private boolean checkUserSerialNumberPermission(Integer adminUserId, String serialNumber) {
        return extensionEnabledCameraDAO.selectByAdminUserIdAndSerialNumber(adminUserId, serialNumber) != null;
    }

}
