#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

echo "Checking domain dependencies..."

# Variable to track errors
has_error=0

# Function to check dependencies in pom.xml files
function check_dependencies() {
    local module_type=$1
    local module_path=$2
    local found_error=0
    
    # Find all pom.xml files in the module
    find "$module_path" -name "pom.xml" | while read -r pom_file; do
        echo "Checking dependencies in $pom_file"
        
        # Check for dependencies on other domain-service modules
        if grep -A 3 "<dependency>" "$pom_file"|grep -A 2 "org.addx.camera"|grep  -B 1 -e ".*-service-implement" > /dev/null; then
            # For site-controller, allow dependency on service module with same name
            if [[ "$module_type" == "domain-service" ]]; then
                echo -e "${RED}ERROR: Invalid dependency found in $pom_file${NC}"
                echo -e "${RED}domain-service modules cannot depend on other domain-service modules${NC}"
                found_error=1
                return 1
            elif [[ "$module_type" == "domain-interface" ]]; then
                echo -e "${RED}ERROR: Invalid dependency found in $pom_file${NC}"
                echo -e "${RED}domain-interface modules cannot depend on domain-service-implement modules${NC}"
                found_error=1
                return 1
            elif [[ "$module_type" == "site-controller" ]]; then
                # Extract the site-controller module name
                local controller_name=$(basename $(dirname "$pom_file") | sed 's/-site$//')
                # Extract the service module name from dependency
                local service_name=$(grep -A 3 "<dependency>" "$pom_file"|grep -A 2 "org.addx.camera"|grep -B 1 -e ".*-service-implement" | grep "artifactId" | sed 's/.*<artifactId>\(.*\)<\/artifactId>.*/\1/')
                # Check if the names match (excluding -service-implement suffix)
                if [[ ! "$service_name" =~ ^"$controller_name"-service-implement$ ]]; then
                    echo -e "${RED}ERROR: Invalid dependency found in $pom_file${NC}"
                    echo -e "${RED}site-controller modules can only depend on their corresponding service modules${NC}"
                    found_error=1
                    return 1
                fi
                # Check if dependency is runtime scope
                if grep -A 5 "<dependency>" "$pom_file" | grep -B 1 -A 4 "org.addx.camera" | grep -B 2 -A 3 -e ".*-service-implement" > /dev/null; then
                    if ! grep -A 5 "<dependency>" "$pom_file" | grep -B 1 -A 4 "org.addx.camera" | grep -B 2 -A 3 -e ".*-service-implement" | grep -q "<scope>runtime</scope>" > /dev/null; then
                        echo -e "${RED}ERROR: Invalid dependency scope in $pom_file${NC}"
                        echo -e "${RED}Dependencies on service-implement modules must have runtime scope${NC}"
                        found_error=1
                        return 1
                    fi
                fi
            elif [[ "$module_type" == "iot-service" ]]; then
                echo -e "${RED}ERROR: Invalid dependency found in $pom_file${NC}"
                echo -e "${RED}iot-service modules (local/cloud) cannot depend on domain-service-implement modules${NC}"
                found_error=1
                return 1
            fi
        fi
    done
}

# Check domain-service modules
if [ -d "domain-service" ]; then
    check_dependencies "domain-service" "domain-service"
    if [ $? -eq 1 ]; then
        has_error=1
    fi
else
    echo "domain-service directory not found"
fi

# Check domain-interface modules
if [ -d "domain-interface" ]; then
    check_dependencies "domain-interface" "domain-interface"
    if [ $? -eq 1 ]; then
        has_error=1
    fi
else
    echo "domain-interface directory not found"
fi

# Check site-controller modules
if [ -d "site-controller" ]; then 
    check_dependencies "site-controller" "site-controller"
    if [ $? -eq 1 ]; then
        has_error=1
    fi
else
    echo "site-controller directory not found"
fi

# Check iot-service modules
if [ -d "iot-service-local" ]; then
    check_dependencies "iot-service" "iot-service-local"
    if [ $? -eq 1 ]; then
        has_error=1
    fi
else
    echo "iot-service-local directory not found"
fi

if [ -d "iot-service-cloud" ]; then
    check_dependencies "iot-service" "iot-service-cloud" 
    if [ $? -eq 1 ]; then
        has_error=1
    fi
else
    echo "iot-service-cloud directory not found"
fi

if [ $has_error -eq 0 ]; then
    echo -e "${GREEN}Dependency check completed successfully${NC}"
    exit 0
else
    echo -e "${RED}Dependency check failed${NC}"
    exit 1
fi
