package org.addx.iot.domain.config.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
public class OptionEnumMapping {

    public static final String OPTION_ENUM_UNSELECTED = "unselected";

    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Entry<T> {
        private String enumName;
        private T value;
    }

    private List<Entry<Integer>> pirSensitivityOptions = new LinkedList<>();
    private List<Entry<Integer>> pirRecordTimeOptions = new LinkedList<>();
    private List<Entry<Integer>> sdCardPirRecordTimeOptions = new LinkedList<>();
    private List<Entry<Integer>> pirCooldownTimeOptions = new LinkedList<>();
    private List<Entry<String>> videoResolutionOptions = new LinkedList<>();
    private List<Entry<Integer>> videoAntiFlickerFrequencyOptions = new LinkedList<>();
    /*** 设备设置通用化改造二期 开始 */
    private List<Entry<Integer>> nightVisionSensitivityOptions = new LinkedList<>();
    private List<Entry<Integer>> nightVisionModeOptions = new LinkedList<>();
    private List<Entry<String>> voiceLanguageOptions = new LinkedList<>();
    private List<Entry<String>> doorBellRingOptions = new LinkedList<>();
    /*** 设备设置通用化改造二期 结束 */
    private List<Entry<String>> powerSourceOptions = new LinkedList<>();
    private List<Entry<String>> chargeAutoPowerOnCapacityOptions = new LinkedList<>();
    private List<Entry<Integer>> alarmDurationOptions = new LinkedList<>();

    public static <T> T getEnumValue(List<Entry<T>> options, String enumName) {
        if (StringUtils.isBlank(enumName)) return null;
        return options.stream().filter(it -> it.getEnumName().equals(enumName)).findFirst()
                .map(Entry::getValue).orElse(null);
    }

    public static <T> String getEnumName(List<Entry<T>> options, T value, Supplier<String> defaultEnumName) {
        if (value == null) return null;
        return options.stream().filter(it -> it.getValue().equals(value)).findFirst()
                .map(Entry::getEnumName).orElseGet(defaultEnumName != null ? defaultEnumName : () -> null);
    }

    public static <T> List<String> getEnumNames(List<Entry<T>> options) {
        return options.stream().map(Entry::getEnumName).collect(Collectors.toList());
    }

    public static <T> List<String> getEnumNames(List<Entry<T>> options, Collection<T> values) {
        if (CollectionUtils.isEmpty(values)) return getEnumNames(options);
        return options.stream().filter(it -> values.contains(it.getValue()))
                .map(Entry::getEnumName).collect(Collectors.toList());
    }

}
